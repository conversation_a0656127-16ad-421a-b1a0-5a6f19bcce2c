﻿using CP.Common.Base;
using CP.Common.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseOceanstorMssql", Namespace = "http://www.ContinuityPlatform.com/types")]
   public class DatabaseOceanstorMssql :BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId { get; set; }

        [DataMember]
        public string DatabaseName
        {
            get;
            set;
        }

        //[DataMember]
        //public string DatabaseID
        //{
        //    get;
        //    set;
        //}

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int DbMethod { get; set; }

        [DataMember]
        public string SqlServerType { get; set; }

        [DataMember]
        public string IPAddress { get; set; }

        [DataMember]
        public string InstanceName { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public SqlAuthenticateType AuthenticationMode { get; set; }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }
         [DataMember]
        public string ExcludeDatabaseList
        {
            get;
            set;
        }
         [DataMember]
        public string ClusterResourceStatus
        {
            get;
            set;
        }
         [DataMember]
        public string WindowsServiceStat
        {
            get;
            set;
        }
        #endregion
        
        #region Constructor

        public DatabaseOceanstorMssql()
            : base()
        {

        }

        #endregion

    }
}
