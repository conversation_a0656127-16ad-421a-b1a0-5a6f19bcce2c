(function($){
	var initLayout = function() {
		var hash = window.location.hash.replace('#', '');
		var currentTab = $('ul.navigationTabs a').bind('click', showTab).filter('a[rel=' + hash + ']');
		if (currentTab.size() == 0)
			currentTab = $('ul.navigationTabs a:first');

		showTab.apply(currentTab.get(0));

		$('#startDate').live("click",function(){
		        $('#startDate').DatePicker({
			        format:'Y-m-d',
			        date: $('#startDate').val(),
			        current: $('#startDate').val(),
			        starts: 1,
			        position: 'right',
			        onBeforeShow: function(){
				        //$('#startDate').DatePickerSetDate($('#startDate').val(), true);
			        },
			        onChange: function(formated, dates){
				        $('#startDate').val(formated);
				        if ($(".datepickerDays").is(":visible")) {
					        $('#startDate').DatePickerHide();
				        }
			        }
		        });
		});
		$('#endDate').live("click",function(){
		        $('#endDate').DatePicker({
			        format:'Y-m-d',
			        date: $('#endDate').val(),
			        current: $('#endDate').val(),
			        starts: 1,
			        position: 'right',
			        onBeforeShow: function(){
				        //$('#endDate').DatePickerSetDate($('#endDate').val(), true);
			        },
			        onChange: function(formated, dates){
				        $('#endDate').val(formated);
				        if ($(".datepickerDays").is(":visible")) {
					        $('#endDate').DatePickerHide();
				        }
			        }
		        });
		});

		var state = false;
		$('#widgetField>a').bind('click', function(){
			$('#widgetCalendar').stop().animate({height: state ? 0 : $('#widgetCalendar div.datepicker').get(0).offsetHeight}, 10000);
			state = !state;
			return false;
		});
		$('#widgetCalendar div.datepicker').css('position', 'absolute');
	};

	var showTab = function(e) {
		var tabIndex = $('ul.navigationTabs a')
							.removeClass('active')
							.index(this);
		$(this).addClass('active').blur();
		$('div.tab').hide().eq(tabIndex).show();
	};

	EYE.register(initLayout, 'init');
})(jQuery)