﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ViewApplicationDiscovery.aspx.cs" Inherits="CP.UI.SNMP.ViewApplicationDiscovery" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style>
        .appleft
        {
            margin-left: 4px;
        }

        .appright
        {
            margin-right: 4px;
        }

        .apptop
        {
            float: right;
            display: inline;
            margin-top: 0px;
        }

        .appiconright
        {
            margin-right: 5px;
        }

        .bootstrap-select:not([class*="col"]) .btn, .bootstrap-select:not([class*="col"])
        {
            width: 207px;
        }

        .div-discover-btn
        {
            bottom: 10px;
            right: 0;
            position: absolute;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
      <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
            <img src="../Images/business-process-icon.png" alt="View Application Discovery ">
            Application Discovery</h3>
      
        <asp:UpdatePanel ID="updPnlScan" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <label class="appright">Host From</label>

                       
                        <input type="text" id="inIPAddressFrom" class="form-control" placeholder="IP Address" runat="server"  onkeypress="return isNumberKey(event)" style="width: 9%" />

                        

                        <label class="appleft appright">To</label>

                       
                        <input type="text" id="inIPAddressTo" class="form-control" placeholder="IP Address" runat="server" onkeypress="return isNumberKey(event)" style="width: 9%" />

                        
                        <label class="appleft appright">OS</label>

                      
                        <input type="text" id="inOsName" runat="server" style="width: 6%" class="form-control" />

                        <label class="appleft appright">Applications</label>

                       
                        <input type="text" id="inApp" runat="server" style="width: 6%" class="form-control appright" />

                        <asp:Button ID="btnscan" runat="server" CssClass="btn btn-primary appright" Text="Discover" OnClick="btnScanNetwork_Click" />
                        <asp:DropDownList ID="ddlScannedHost" CssClass="selectpicker" data-style="btn-default" runat="server" Width="5%" AutoPostBack="true" OnSelectedIndexChanged="ddlScannedHost_SelectedIndexChanged">
                        </asp:DropDownList>
                        <div class="apptop">
                            <span class="count-icon appiconright"></span>Hosts Found : 
                               <asp:Label ID="lblCount" runat="server" CssClass="text-danger appleft appright" Text=""></asp:Label>
                            <span class="icon-Time appiconright"></span>
                            Discovery Time :  
                               <asp:Label ID="lblTime" runat="server" Text="" CssClass="text-danger appleft"></asp:Label>
                            <br />
                            <span id="spnNewhost" runat="server" visible="false">
                                <span class="count-icon appiconright"></span>New Hosts Found : 
                            <asp:Label ID="lblNewHostsFound" runat="server" Text="0" CssClass="text-danger appleft"></asp:Label>
                            </span>
                        </div>

                        <hr class="margin-bottom-none" style="margin: 7px 0;" />

                        <div class="text-right">
                            <asp:Label ID="lblNoResultFound" CssClass="text-left" runat="server" Text="No applications found" ForeColor="Red" Visible="false"></asp:Label>
                        </div>
                        <div id="body">
                        </div>
                        <div class="margin-right div-discover-btn">
                            <asp:Label ID="lblSaveMsg" CssClass="text-right padding" runat="server" Text="Application discovery has been saved successfully..." ForeColor="Green" Visible="false"></asp:Label>
                            <asp:Button ID="btnSaveDiscovery" runat="server" CssClass="btn btn-primary appright" Text="Save Discovery" Visible="false" OnClick="btnSaveDiscovery_Click" />
                            <asp:Button ID="btnRediscover" runat="server" CssClass="btn btn-primary appright" Text="Re-Discover" Visible="false" OnClick="btnRediscover_click" />
                        </div>
                    </div>
                </div>
                <asp:UpdateProgress ID="UpdateProgress1" AssociatedUpdatePanelID="updPnlScan" runat="server">
                    <ProgressTemplate>
                        <div id="imgLoading" class="loading-mask">
                            <span>Discovering...</span>
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    <script src="../script/d3.min.js"></script>
    <script src="../Script/appDiscovery.js" type="text/javascript"></script>
</asp:Content>
