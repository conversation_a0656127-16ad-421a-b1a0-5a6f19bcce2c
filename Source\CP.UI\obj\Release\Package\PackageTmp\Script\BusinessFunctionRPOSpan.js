﻿function pageLoad() {
    $("[id$=BVolume]").val('00:00');
    $('[id$=BVolume]').timepicker({ 'timeFormat': 'H:i' });
    $('[id$=TextBox2],[id$=TextBox4]').appendDtpicker();

    $('[id$=TextBox1],[id$=TextBox3],[id$=TextBox2],[id$=TextBox4]').hide();

    $("[id$=DropDownList1]").live("change", function (e) {
        var typeval = $("[id$=DropDownList1] option:selected").val();
        $('[id$=AVolume],[id$=BVolume]').val("");

        if (typeval == "Hours") {
            var listrange = $("table#tbltimespan > tbody > tr").length;
            if (listrange - 1 == 0) {
                $("[id$=AVolume]").val('00:00');
            }
            else {
                var preval = "00:00";
                var table = $("table#tbltimespan > tbody");
                table.find('tr').each(function (i) {
                    var $tds = $(this).find('td');
                    var type = $tds.eq(0).text();
                    var endtime = $tds.eq(2).text();
                    //                           alert(type);
                    if ($.trim(type) == "Hours") {
                        //                             alert($.trim(endtime));
                        preval = $.trim(endtime);
                    }
                });
                $("[id$=AVolume]").val(preval);
                //                   alert("Last Value " + preval);
            }
            $("[id$=BVolume]").val('00:00');
            $('[id$=TextBox1],[id$=TextBox3]').hide();
            $('[id$=TextBox2],[id$=TextBox4]').hide();
            $('[id$=AVolume],[id$=BVolume]').show();
        }

        if (typeval == "Weekends") {
            $('[id$=TextBox1],[id$=TextBox3]').show();
            $('[id$=TextBox2],[id$=TextBox4]').hide();
            $('[id$=AVolume],[id$=BVolume]').hide();
        }

        if (typeval == "Static Days") {
            $('[id$=TextBox1],[id$=TextBox3]').hide();
            $('[id$=TextBox2],[id$=TextBox4]').show();
            $('[id$=AVolume],[id$=BVolume]').hide();
        }
    });
};

$(document).ready(function () {
    $('[id$=btnpanel]').live("click", function () {
        var listrange = $("table#tbltimespan > tbody > tr").length;
        if (listrange - 1 == 0) {
            $('[id$=rpospan]').show();
            $('[id$=txtDataLag]').val("");
        }
        else {
            $('[id$=rpospan]').hide();

            $('[id$=txtDataLag]').val("RPO Time Selected");
        }
        $find("mpe").hide();
        return false;
    });
    $("[id$=BVolume]").val('00:00');
    $('[id$=BVolume]').timepicker({ 'timeFormat': 'H:i' });
    $('[id$=TextBox2],[id$=TextBox4]').appendDtpicker();
    $('[id$=TextBox1],[id$=TextBox3],[id$=TextBox2],[id$=TextBox4]').hide();

    $("[id$=DropDownList1]").live("change", function (e) {
        var typeval = $("[id$=DropDownList1] option:selected").val();
        $('[id$=AVolume],[id$=BVolume]').val("");

        if (typeval == "Hours") {
            var listrange = $("table#tbltimespan > tbody > tr").length;
            if (listrange - 1 == 0) {
                $("[id$=AVolume]").val('00:00');
            }
            else {
                var preval = "00:00";
                var table = $("table#tbltimespan > tbody");
                table.find('tr').each(function (i) {
                    var $tds = $(this).find('td');
                    var type = $tds.eq(0).text();
                    var endtime = $tds.eq(2).text();
                    //                           alert(type);
                    if ($.trim(type) == "Hours") {
                        //                             alert($.trim(endtime));
                        preval = $.trim(endtime);
                    }
                });
                $("[id$=AVolume]").val(preval);
                //                   alert("Last Value " + preval);
            }
            $("[id$=BVolume]").val('00:00');
            $('[id$=TextBox1],[id$=TextBox3]').hide();
            $('[id$=TextBox2],[id$=TextBox4]').hide();
            $('[id$=AVolume],[id$=BVolume]').show();
        }

        if (typeval == "Weekends") {
            $('[id$=TextBox1],[id$=TextBox3]').show();
            $('[id$=TextBox2],[id$=TextBox4]').hide();
            $('[id$=AVolume],[id$=BVolume]').hide();
        }

        if (typeval == "Static Days") {
            $('[id$=TextBox1],[id$=TextBox3]').hide();
            $('[id$=TextBox2],[id$=TextBox4]').show();
            $('[id$=AVolume],[id$=BVolume]').hide();
        }
    });
    });