﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DiffModuleMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DiffModuleMonitor : BaseEntity
    {
        #region Properties
        [DataMember]
        public int id { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public String PRServerIP { get; set; }

        [DataMember]
        public String DRServerIP { get; set; }

        [DataMember]
        public string PRRAMDetails { get; set; }

        [DataMember]
        public string DRRAMDetails { get; set; }

        [DataMember]
        public string CheckPRDRRAM { get; set; }

        [DataMember]
        public string PRCPUCountDetails { get; set; }

        [DataMember]
        public string DRCPUCountDetails { get; set; }

        [DataMember]
        public string CheckPRDRCPUCount { get; set; }

        [DataMember]
        public string PROSDetails { get; set; }

        [DataMember]
        public string DROSDetails { get; set; }

        [DataMember]
        public string CheckPRDROSDetails { get; set; }

        [DataMember]
        public string PRFileSystemSizeDetails { get; set; }

        [DataMember]
        public string DRFileSystemSizeDetails { get; set; }

        [DataMember]
        public string CheckPRDRFileSystemSize { get; set; }

        [DataMember]
        public string PRUsersDetails { get; set; }

        [DataMember]
        public string DRUsersDetails { get; set; }

        [DataMember]
        public string CheckPRDRUsers { get; set; }


        [DataMember]
        public string CheckPRLastPatching { get; set; }

        [DataMember]
        public string PRLastPatchingDetails { get; set; }

        [DataMember]
        public string DRLastPatchingDetails { get; set; }

        [DataMember]
        public string Name { get; set; }


        [DataMember]
        public int BusinessServiceId { get; set; }

        #endregion Properties
    }
}
