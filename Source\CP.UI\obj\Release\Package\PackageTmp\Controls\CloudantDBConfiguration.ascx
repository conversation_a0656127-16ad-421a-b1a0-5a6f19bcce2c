﻿<%@Control Language="C#" AutoEventWireup="true" CodeBehind="CloudantDBConfiguration.aspx.cs" Inherits="CP.UI.Controls.CloudantDBConfiguration" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<div id="Div1" runat="server" class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">Cloudant DB Configuration</h4>
        </div>
        <div class="widget-body">

            <div class="form-group">
                <label class="col-md-3">
                    Relationship <span class="inactive">*</span>
                </label>
                <div class="col-md-9">
                    <asp:Panel ID="PanelInterval" Visible="true" runat="server">
                        <asp:RadioButtonList ID="rdbRelationShip" runat="server" RepeatDirection="Horizontal"
                            CssClass="dropbox text-indent"
                            AutoPostBack="True" OnSelectedIndexChanged="RadioButtonList1_SelectedIndexChanged">
                            <asp:ListItem>UniDirectional</asp:ListItem>
                            <asp:ListItem>BiDirectional</asp:ListItem>
                        </asp:RadioButtonList>
                    </asp:Panel>
                </div>
            </div>


            <%--  <asp:Panel ID="Panel_1" runat="server" Visible="true">

                <table class="table table-bordered" style="table-layout: fixed; margin-bottom: 0;" runat="server" id="tblPrtoDr">
                    <thead>
                        <tr>
                            <th style="width: 3%;">No.
                            </th>
                            <th style="width: 90%">PR -> DR
                            </th>
                            <th style="width: 7%;">Action
                            </th>
                        </tr>
                    </thead>
                </table>
                <table class="table table-bordered" width="100%" style="border: 1px solid #3b3b3b">
                    <tbody>
                        <tr>
                            <td style="width: 3%;"></td>
                            <td style="width: 90%;">
                                <asp:TextBox ID="txtUnidirectionPRToDR" CssClass="form-control" Text='<%# Eval("PRToDRDocId") %>' Width="100%" runat="server"></asp:TextBox>
                                <asp:Label ID="lblUnidirectionPRToDR" runat="server" Text="" Visible="false"></asp:Label>
                            </td>

                            <td style="width: 7%; vertical-align: middle;">
                                <center> <asp:ImageButton ID="imgUpdate" runat="server" align="center" CommandName="Insert" AlternateText="Insert"
                                            CausesValidation="false" ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" /></center>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </asp:Panel>--%>

            <asp:Panel ID="pnlUnidirection" Visible="false" runat="server">

                <asp:ListView ID="lvUnidirection" runat="server" InsertItemPosition="LastItem" Visible="true" OnItemInserting="lvUnidirection_ItemInserting" OnItemEditing="lvUnidirection_ItemEditing"
                    OnItemUpdating="lvUnidirection_ItemUpdating" OnItemDeleting="lvUnidirection_ItemDeleting" >
                    <LayoutTemplate>
                        <table class="table table-bordered" style="table-layout: fixed; margin-bottom: 0;">
                            <thead>
                                <tr>
                                    <th style="width: 3%;">No.
                                    </th>
                                    <th style="width: 90%">PR -> DR Doc Id
                                    </th>
                                    <th style="width: 7%;">Action
                                    </th>
                                </tr>
                            </thead>
                        </table>
                        <div class="Scrollbar">
                            <table class="table table-bordered" width="100%" style="border: 1px solid #3b3b3b">
                                <tbody>
                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                </tbody>
                            </table>
                        </div>
                    </LayoutTemplate>
                    <ItemTemplate>
                        <tr>
                            <td style="width: 3%;">
                                <%#Container.DataItemIndex+1 %>
                            </td>
                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                            <td style="width: 30%;">
                                <asp:Label ID="lblPrtoDr" Text='<%# Eval("PRToDRDocId") %>' runat="server"> </asp:Label>
                            </td>
                            <td style="width: 7%; vertical-align: middle;">
                                <center> <asp:ImageButton ID="ImgEdit" runat="server" align="center" CommandName="Edit" AlternateText="Edit"
                                            CausesValidation="false" ToolTip="Edit"  ImageUrl="../Images/icons/pencil.png" />
                                        <asp:ImageButton ID="ImgDelete" runat="server" align="center" CommandName="Delete" AlternateText="Delete" CausesValidation="false"
                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" /></center>
                                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                    ConfirmText='<%# "Are you sure want to delete ? " %>' OnClientCancel="CancelClick">
                                </TK1:ConfirmButtonExtender>
                            </td>
                        </tr>
                    </ItemTemplate>
                    <EditItemTemplate>
                        <tr>
                            <td style="width: 3%;">
                                <%#Container.DataItemIndex+1 %>
                            </td>
                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtUnidirectionPRToDR" CssClass="form-control" Text='<%# Eval("PRToDRDocId") %>'
                                    runat="server"> </asp:TextBox>

                                <asp:Label ID="lblUnidirectionPRToDR" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                            </td>
                            <td style="width: 7%; vertical-align: middle;">
                                <center>  <asp:ImageButton ID="ImgUpdate" runat="server" align="center" CommandName="Update" AlternateText="Update" CausesValidation="false"
                                            ToolTip="Update" ImageUrl="../Images/icons/navigation-090.png" />
                                        <asp:ImageButton ID="ImgCancel" runat="server" align="center" CommandName="Cancel" AlternateText="Cancel" CausesValidation="false"
                                            ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" /></center>
                            </td>
                        </tr>
                    </EditItemTemplate>
                    <InsertItemTemplate>
                        <tr>
                            <td style="width: 3%;"></td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtInsertUniPrDocId" CssClass="form-control"
                                    runat="server"></asp:TextBox>
                                 <asp:Label ID="lblInsertUniPrDOCId" runat="server" Text="" Visible="false" AssociatedControlID="txtInsertUniPrDocId"
                                            ForeColor="red"></asp:Label>

                            </td>

                            <td style="width: 7%; vertical-align: middle;">
                                <center> <asp:ImageButton ID="imgUpdate" runat="server" align="center" CommandName="Insert" AlternateText="Insert"
                                            CausesValidation="false" ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" /></center>
                            </td>
                        </tr>
                    </InsertItemTemplate>
                </asp:ListView>
                <asp:Label ID="labelSDErrormessage" runat="server" CssClass="error" Font-Size="Small"></asp:Label>
            </asp:Panel>




            <%-- <asp:Panel ID="Panel_2" runat="server" Visible="false">

                <table class="table table-bordered" style="table-layout: fixed; margin-bottom: 0;" runat="server" id="tblDrtoPr">
                    <thead>
                        <tr>
                            <th style="width: 3%;">No.
                            </th>
                            <th style="width: 45%">PR -> DR
                            </th>
                            <th style="width: 45%">DR -> PR
                            </th>
                            <th style="width: 7%;">Action
                            </th>
                        </tr>
                    </thead>
                </table>
                <table class="table table-bordered" width="100%" style="border: 1px solid #3b3b3b">
                    <tbody>
                        <tr>
                            <td style="width: 3%;"></td>
                            <td style="width: 45%;">
                                <asp:TextBox ID="TextBox1BidirectionPRToDR" CssClass="form-control" Text='<%# Eval("PRToDRDocId") %>' runat="server" Width="100%"></asp:TextBox>
                                <asp:Label ID="lblBidirectionPRToDR" runat="server" Text="" Visible="false"></asp:Label>
                            </td>
                            <td style="width: 45%;">
                                <asp:TextBox ID="txtBidirectionDRToPR" CssClass="form-control" Text='<%# Eval("DRToPRDocId") %>' runat="server" Width="100%"></asp:TextBox>
                                <asp:Label ID="lblBidirectionDRToPR" runat="server" Text="" Visible="false"></asp:Label>
                            </td>
                            <td style="width: 7%; vertical-align: middle;">
                                <center> <asp:ImageButton ID="ImageButton1" runat="server" align="center" CommandName="Insert" AlternateText="Insert"
                                            CausesValidation="false" ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" /></center>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </asp:Panel>--%>

            <asp:Panel ID="PnlBidirection" Visible="false" runat="server">

                <asp:ListView ID="lvBidirection" runat="server" InsertItemPosition="LastItem" Visible="true" OnItemInserting="lvBidirection_ItemInserting"
                    OnItemEditing="lvBidirection_ItemEditing" OnItemUpdating="lvBidirection_ItemUpdating" OnItemDeleting="lvBidirection_ItemDeleting">
                    <LayoutTemplate>
                        <table class="table table-bordered" style="table-layout: fixed; margin-bottom: 0;">
                            <thead>
                                <tr>
                                    <th style="width: 3%;">No.
                                    </th>
                                    <th style="width: 45%">PR -> DR Doc Id
                                    </th>
                                    <th style="width: 45%">DR -> PR Doc Id
                                    </th>
                                    <th style="width: 7%;">Action
                                    </th>
                                </tr>
                            </thead>
                        </table>
                        <div class="Scrollbar">
                            <table class="table table-bordered" width="100%" style="border: 1px solid #3b3b3b">
                                <tbody>
                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                </tbody>
                            </table>
                        </div>
                    </LayoutTemplate>
                    <ItemTemplate>
                        <tr>
                            <td style="width: 3%;">
                                <%#Container.DataItemIndex+1 %>
                            </td>
                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                            <td style="width: 30%;">
                                <asp:Label ID="lblPrtoDr" Text='<%# Eval("PRToDRDocId") %>' runat="server"> </asp:Label>
                            </td>
                            <td style="width: 30%;">
                                <asp:Label ID="Label2" Text='<%# Eval("DRToPRDocId") %>' runat="server"> </asp:Label>
                            </td>
                            <td style="width: 7%; vertical-align: middle;">
                                <center> <asp:ImageButton ID="ImgEdit" runat="server" align="center" CommandName="Edit" AlternateText="Edit"
                                            CausesValidation="false" ToolTip="Edit"  ImageUrl="../Images/icons/pencil.png" />
                                        <asp:ImageButton ID="ImgDelete" runat="server" align="center" CommandName="Delete" AlternateText="Delete" CausesValidation="false"
                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" /></center>
                                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                    ConfirmText='<%# "Are you sure want to delete ? " %>' OnClientCancel="CancelClick">
                                </TK1:ConfirmButtonExtender>
                            </td>
                        </tr>
                    </ItemTemplate>
                    <EditItemTemplate>
                        <tr>
                            <td style="width: 3%;">
                                <%#Container.DataItemIndex+1 %>
                            </td>
                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtBidirectionPRToDR" CssClass="form-control" Text='<%# Eval("PRToDRDocId") %>'
                                    runat="server"> </asp:TextBox>

                                <asp:Label ID="lblBidirectionPRToDR" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                            </td>

                            <td style="width: 30%;">
                                <asp:TextBox ID="txtBidirectionDRToPR" CssClass="form-control" Text='<%# Eval("DRToPRDocId") %>'
                                    runat="server"> </asp:TextBox>

                                <asp:Label ID="lblBidirectionDRToPR" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                            </td>
                            <td style="width: 7%; vertical-align: middle;">
                                <center>  <asp:ImageButton ID="ImgUpdate" runat="server" align="center" CommandName="Update" AlternateText="Update" CausesValidation="false"
                                            ToolTip="Update" ImageUrl="../Images/icons/navigation-090.png" />
                                        <asp:ImageButton ID="ImgCancel" runat="server" align="center" CommandName="Cancel" AlternateText="Cancel" CausesValidation="false"
                                            ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" /></center>
                            </td>
                        </tr>
                    </EditItemTemplate>
                    <InsertItemTemplate>
                        <tr>
                            <td style="width: 3%;"></td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtInsertBiPrDocId" CssClass="form-control"
                                    runat="server"></asp:TextBox>
                                 <asp:Label ID="lblInsertBiPrDOCId" runat="server" Text="" Visible="false" AssociatedControlID="txtInsertBiPrDocId"
                                            ForeColor="red"></asp:Label>
                            </td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtInsertBiDrDocId" CssClass="form-control"
                                    runat="server"></asp:TextBox>
                                 <asp:Label ID="lblInsertBiDrDocId" runat="server" Text="" Visible="false" AssociatedControlID="txtInsertBiDrDocId"
                                            ForeColor="red"></asp:Label>
                                
                            </td>

                            <td style="width: 7%; vertical-align: middle;">
                                <center> <asp:ImageButton ID="imgUpdate" runat="server" align="center" CommandName="Insert" AlternateText="Insert"
                                            CausesValidation="false" ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" /></center>
                            </td>
                        </tr>
                    </InsertItemTemplate>
                </asp:ListView>
                <asp:Label ID="label1" runat="server" CssClass="error" Font-Size="Small"></asp:Label>
            </asp:Panel>

            <div class="form-actions row">
                <div class="col-md-3">
                    <asp:Label ID="label17" runat="server" Text=""></asp:Label>
                    <asp:Label ID="lblError" runat="server" ForeColor="Red" Visible="false"></asp:Label>
                </div>
                <div class="col-lg-9 text-right">
                    <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save" OnClick="btnSave_Click" />
                    <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server" Text="Cancel" CausesValidation="false" OnClick="btnCancel_Click" />
                </div>
            </div>
        </div>
    </div>
</div>


