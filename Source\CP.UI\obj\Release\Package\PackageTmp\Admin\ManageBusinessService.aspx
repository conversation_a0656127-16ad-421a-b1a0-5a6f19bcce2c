﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ManageBusinessService.aspx.cs"
    Inherits="CP.UI.Admin.ManageBusinessService" Title="Continuity Patrol :: Manage Business Service" %>

<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="BStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3><span class="business-setting-icon vertical-sub"></span>
            Manage Business Service</h3>



        <div class="widget widget-heading-simple widget-body-white">

            <div class="widget-body">

                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                    <ContentTemplate>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="cblstGroup">
                                Select Business Service <span class="inactive">*</span></label>
                         <div class="col-md-9">
                         <asp:Panel ID="Panel2" runat="server" ScrollBars="Vertical" Height="101px" class="padding pull-left"
                                    Width="48%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px" TabIndex="7">
                                    <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                                        <ContentTemplate>
                                            <asp:CheckBoxList ID="ServiceCheckBoxList1" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ServiceCheckBoxList1_SelectedIndexChanged">
                                            </asp:CheckBoxList>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </asp:Panel>
                              <asp:Label ID="lblserv" runat="server" ForeColor="red"></asp:Label>
                                  </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="cblstGroup">
                              Select InfraObject<span class="inactive">*</span></label>
                            <div class="col-md-9">

                                <asp:Panel ID="Panel1" runat="server" ScrollBars="Vertical" Height="101px" class="padding pull-left"
                                    Width="48%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px" TabIndex="7">
                                    <asp:UpdatePanel ID="uplcbsltGroup" runat="server" UpdateMode="Conditional">
                                        <ContentTemplate>
                                            <asp:CheckBoxList ID="cblstGroup" runat="server" AutoPostBack="True" OnSelectedIndexChanged="cblstGroup_SelectedIndexChanged">
                                            </asp:CheckBoxList>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </asp:Panel>
                                <asp:Label ID="lblchklst" runat="server" ForeColor="red"></asp:Label>
                                <asp:Label ID="labelSDErrormessage" runat="server" CssClass="error" Visible="false"></asp:Label>
                                <asp:Label ID="lblInfraUserErrMsg" runat="server" CssClass="error" Visible="false"></asp:Label>
                            </div>
                            <div class="clearfix"></div>
                        </div>



                        <div class="form-group">
                            <label class="col-md-3 control-label" for="txtDescription">
                                Operations <span class="inactive">*</span></label>

                            <div class="col-md-9">
                                <asp:DropDownList ID="ddloperationlst" runat="server" CssClass="selectpicker col-md-6"
                                    data-style="btn-default" AutoPostBack="True"
                                    OnSelectedIndexChanged="ddloperationlst_SelectedIndexChanged">

                                    <asp:ListItem Selected="True" Value="0">Select Operation</asp:ListItem>
                                    <asp:ListItem Value="1">Active</asp:ListItem>
                                    <asp:ListItem Value="2">Maintenance</asp:ListItem>
                                </asp:DropDownList>
                                <%-- <asp:Label ID="lblvalidation" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>--%>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" InitialValue="000" ControlToValidate="ddloperationlst"
                                    ErrorMessage="Select Operation" Display="Dynamic" CssClass="error"></asp:RequiredFieldValidator>
                                <asp:Label ID="Label1" runat="server" ForeColor="red"></asp:Label>

                            </div>
                            <div class="clearfix"></div>
                        </div>

                            <div class="form-group" id="txtrsn" runat="server" visible="false">
                              <label class="col-md-3 control-label">
                                     Reason <span class="inactive">*</span></label>
                                <div class="col-md-9">
                                    
                                      <asp:TextBox  ID="txtresan" runat="server" TextMode="MultiLine" CssClass="form-control"></asp:TextBox>
                                     <asp:Label ID="Label2" runat="server" ForeColor="red"></asp:Label>
                                </div>
                                 <div class="clearfix"></div>
                         </div> 
                        <hr class="separator" />
                        <div class="clearfix"></div>
                       

                        <div class="form-actions row">
                            <div class="col-xs-5">
                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required Fields</span>
                                <asp:Label ID="lblupdatestatus" runat="server" Text=""></asp:Label>
                            </div>
                            <div class="col-xs-7">
                                <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="15%" runat="server" Text="Active/Maintenance" Style="width:21%;margin-left:9px;"
                                    OnClick="btnSave_Click" TabIndex="18" />
                                <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server"
                                    Text="Cancel" OnClick="btnCancel_Click" CausesValidation="False" TabIndex="19" />
                            </div>
                        </div>



                    </ContentTemplate>
                </asp:UpdatePanel>

            </div>


        </div>

    </div>




</asp:Content>

