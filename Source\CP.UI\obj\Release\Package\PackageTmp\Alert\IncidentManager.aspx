﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="IncidentManager.aspx.cs" Inherits="CP.UI.IncidentManager"
    MasterPageFile="~/Master/BcmsDefault.Master" Title="Continuity Patrol :: Alert-IncidentManager" %>

<%@ Register TagPrefix="asp" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <asp:UpdatePanel ID="upAlert" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
            <ContentTemplate>

                <div class="grid_5 grid-15">
                    <div id="ulmessage" class="message error" runat="server" visible="false">

                        <span class="close-bt"></span>
                    </div>

                   
                    <h3>
                        <img src="../Images/incident-icon.png">
                        Incident Management</h3>
                  

                    <div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="row">

                                <div class="form-group">
                                    <div class="col-md-6">

                                        <label class="col-md-4">Select InfraObject: <span class="inactive">*</span></label>

                                        <asp:DropDownList ID="ddlInfra" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" AutoPostBack="false" OnSelectedIndexChanged="ddlInfra_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:Label ID="lblddlGroupValidation" runat="server" Text="*" ForeColor="Red" Visible="false"></asp:Label>
                                        <asp:RequiredFieldValidator ID="rfvgroup" runat="server" ErrorMessage="Select InfraObject" InitialValue="0"
                                            ControlToValidate="ddlInfra" ValidationGroup="valalert"></asp:RequiredFieldValidator>
                                    </div>

                                    <div class="col-md-6 text-right">

                                        <div class="col-md-12 text-right">

                                            <span class="input-type-text">
                                                <asp:TextBox ID="txtToDate" runat="server" class="form-control" MaxLength="10" Width="30%" Style="background: none;"></asp:TextBox>
                                               
                                                <img
                                                    src="../images/icons/calendar-month.png" width="16" id="imgToDate" style="margin-left: 4px;" />
                                            </span>

                                            <asp:TextBoxWatermarkExtender ID="txtWatermarkExtender1" runat="server" TargetControlID="txtToDate"
                                                WatermarkText="Start Date" />
                                            <asp:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtToDate"
                                                PopupButtonID="imgToDate" Format="yyyy-MM-dd" />
                                           

                                            <span class="input-type-text ">
                                                <asp:TextBox ID="txtFromDate" runat="server" class="form-control" MaxLength="10" Width="30%" Style="background: none;"></asp:TextBox>
                                              
                                                 <img
                                                    src="../images/icons/calendar-month.png" width="16" id="imgEndDate" style="margin-left: 4px;" />
                                            </span>

                                            <span>
                                                <asp:TextBoxWatermarkExtender ID="txtWatermarkExtender2" runat="server" TargetControlID="txtFromDate"
                                                    WatermarkText="End Date">
                                                </asp:TextBoxWatermarkExtender>
                                                <asp:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtFromDate"
                                                    CssClass="" PopupButtonID="imgEndDate" Format="yyyy-MM-dd">
                                                </asp:CalendarExtender>

                                                <asp:Button ID="btnDate" runat="server" Text="Search" CssClass="btn btn-primary" Width="25%"
                                                    CausesValidation="true" ValidationGroup="valalert" OnClick="BtnDateClick" />
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-action row">
                                <div class="col-lg-3"></div>
                                <div class="col-lg-9 text-right">
                                    <asp:Label ID="lblDatemsg" runat="server" CssClass="error"></asp:Label>
                                </div>
                            </div>
                            <asp:ListView ID="lvIncident" runat="server" OnPreRender="lvIncident_PreRender">
                              
                                <LayoutTemplate>
                                    <hr />
                                    <table id="tblUser" class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="margin-bottom: 0;">
                                        <thead>
                                            <tr>
                                                <th style="width: 4%;">
                                                    <span>No.
                                                </th>
                                                <th style="width: 12%;">IncidentNumber
                                                </th>
                                                <th style="width: 6%;">Severity
                                                </th>
                                                <th style="width: 30%;">Message
                                                </th>
                                                <th style="width: 25%;">InfraObject Name
                                                </th>
                                                <th>Create Date
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                        </tbody>
                                    </table>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td id="IdTD" runat="server">
                                            <asp:Label ID="Label1" runat="server" Text='<%#Eval("Id") %>' Visible="false"></asp:Label>
                                            <%#Container.DataItemIndex+1 %>
                                        </td>
                                        <td id="TypeTD" runat="server" style="word-wrap: break-word ! important;">
                                            <asp:Label ID="lblType" runat="server" Text='<%# Eval("IncidentNumber") %>' />
                                        </td>
                                        <td id="severityTD" runat="server">

                                            <asp:ImageButton ID="imgSeverity" ImageUrl='<%# GetSeverityTypeCss(Eval("Severity")) %>'
                                                ToolTip='<%# GetRowStyle(Eval("Severity")) %>' runat="server" />

                                           
                                        </td>
                                        <td id="descriptionTD" runat="server">

                                            <asp:Label ID="lblDescription" runat="server" Text='<%# Eval("Message") %>' />
                                        </td>
                                        <td id="GroupNameTd" runat="server">
                                            <asp:Label ID="lblGroupName" runat="server" Text='<%# Eval("InfraObjectName") %>' />
                                        </td>
                                        <td id="CreateDateTD" runat="server">
                                            <asp:Label ID="lblCreateDate" runat="server" Text='<%# Eval("CreateDate") %>'></asp:Label>
                                        </td>
                                    </tr>
                                </ItemTemplate>

                                <EmptyDataTemplate>

                                    <div class="message warning align-center bold no-bottom-margin">
                                        
                                        <asp:Label ID="lblError" Text="No Record Found" runat="server" CssClass="error" Visible="true"></asp:Label>
                                    </div>
                                </EmptyDataTemplate>
                            </asp:ListView>
                            <div class="row innerT">
                                <div class="col-md-6">

                                    <asp:Label ID="lblmsg" runat="server" Text=""></asp:Label>
                                    <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvIncident" PageSize="100">
                                        <Fields>
                                            <asp:TemplatePagerField>
                                                <PagerTemplate>
                                                    <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                    Records
                                        <%# (Container.StartRowIndex +1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                    <br />
                                                </PagerTemplate>
                                            </asp:TemplatePagerField>
                                        </Fields>
                                    </asp:DataPager>
                                </div>
                                <div class="col-md-6 text-right">
                                    <asp:DataPager ID="DataPager1" runat="server" PagedControlID="lvIncident" PageSize="10     ">
                                        <Fields>
                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                            <asp:NumericPagerField NextPageText="..." PreviousPageText="..." ButtonCount="10"
                                                NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                NumericButtonCssClass="btn-pagination" />
                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ShowFirstPageButton="false"
                                                ButtonType="Button" ShowLastPageButton="false" ShowPreviousPageButton="false"
                                                ShowNextPageButton="true" NextPageText="Next → " />
                                        </Fields>
                                    </asp:DataPager>
                                </div>
                            </div>
                            <hr class="separator" />
                            <div class="row">
                                <div class="col-md-6">
                                    <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span class="black">Required Fields</span>
                                </div>
                            </div>
                        </div>
                    </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>