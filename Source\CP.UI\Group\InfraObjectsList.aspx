﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="InfraObjectsList.aspx.cs" Inherits="CP.UI.InfraObjectsList" Title="Continuity Patrol :: InfraObject-InfraObject List" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
     <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
    <%--   <asp:UpdatePanel ID="updmain" runat="server" UpdateMode="Always">
                    <Triggers>
                       
                        <asp:AsyncPostBackTrigger ControlID="btnSearch" EventName="Click" />
                    </Triggers>
      </asp:UpdatePanel>--%>
        <h3>
            <img src="../Images/bus-func-icon.png">
            InfraObject List</h3>
      
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-md-5 col-md-push-7 text-right">
                        <asp:TextBox ID="txtsearchvalue"  placeholder="InfraObject Name" runat="server" CssClass="form-control"></asp:TextBox>
                        <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" OnClick="btnSearch_Click" />
                    </div>
                </div>
                <hr />

                <asp:ListView ID="lvInfraObjectsList" runat="server" OnItemDeleting="lvInfraObjectsList_ItemDeleting"
                    OnItemEditing="lvInfraObjectsList_ItemEditing" OnPreRender="lvInfraObjectsList_PreRender"
                    OnItemDataBound="lvInfraObjectsList_ItemDataBound">
                    <LayoutTemplate>
                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th style="width: 4%;" class="text-center">
                                        <span class="glyphicons cogwheel">
                                            <i></i>
                                        </span>
                                    </th>
                                    <th style="width: 26%;">Name
                                    </th>
                                    <th style="width: 15%;">Business Service Name
                                    </th>
                                    <th style="width: 15%;">Business Function Name
                                    </th>
                                    <th style="width: 10%;">Type
                                    </th>
                                    <th style="width: 16%;">Recovery Type
                                    </th>
                                    <th class="text-center" style="width: 6%;">Status
                                    </th>
                                    <th class="text-center" style="width: 8%;">Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                </tr>
                            </tbody>
                        </table>
                    </LayoutTemplate>
                    <ItemTemplate>

                        <asp:Label ID="state" runat="server" Text='<%# Eval("State") %>' Visible="false"></asp:Label>
                        <tr>
                            <td class="th table-check-cell sorting_1 text-center" style="width: 4%;">
                                <%#Container.DataItemIndex+1 %>
                            </td>
                            <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                            <td style="width: 26%;" class="tdword-wrap">
                                <asp:Label ID="NAME" runat="server" Text='<%# Eval("Name") %>' />
                            </td>
                            <td style="width: 15%;" class="tdword-wrap">
                                <asp:Label ID="lblBsServiceName" runat="server" Text='<%# Eval("BusinessServiceId") %>' />
                            </td>
                            <td style="width: 15%;" class="tdword-wrap">
                                <asp:Label ID="lblBsFunctionName" runat="server" Text='<%# Eval("BusinessFunctionId") %>' />
                            </td>
                            <td style="width: 10%;" class="tdword-wrap">
                                <asp:Label ID="TYPE" runat="server" Text='<%# GetType(Eval("Type")) %>' />
                            </td>
                            <td style="width: 16%;" class="tdword-wrap">
                                <asp:Label ID="RECOVERY_TYPE" runat="server" Text='<%# GetRecoveryType(Eval("RecoveryType"),Eval("SubType")) %>' ToolTip='<%# GetRecoveryType(Eval("RecoveryType"),Eval("SubType"))%>'/>
                            </td>
                            <td class="text-center" style="width: 6%;">
                                <asp:Image ID="imgtype" ImageUrl='<%# GetStatusType(Eval("State")) %>' ToolTip='<%# GetRowStyle(Eval("State")) %>'
                                    runat="server" />
                            </td>
                            <td class="text-center" style="width: 8%;">
                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit" Enabled='<%# DisableEdit(Eval("State"),Eval("Id")) %>'
                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                <asp:ImageButton ID="ImgDelete" runat="server" Enabled='<%# DisableEdit(Eval("State"),Eval("Id")) %>' CommandName="Delete"
                                    AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText='<%# "Are you sure want to delete " + Eval("Name") + " ? " %>'
                                    TargetControlID="ImgDelete" OnClientCancel="CancelClick">
                                </cc1:ConfirmButtonExtender>
                               
                            </td>
                        </tr>

                        <asp:Panel ID="panelDeleteMessage" runat="server" Width="250px" Visible="False">
                            <div class="modal" style="display: block;">
                                <div class="modal-dialog" style="width: 475px;">
                                    <div class="modal-content  widget-body-white">
                                        <div class="modal-header">
                                            <h3 class="modal-title">Confirmation</h3>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-12 form-horizontal uniformjs">
                                                    <img src="../images/icons/Warning.png" alt="Warning" width="32" height="32" />
                                                    Are you sure want to Delete InfraObject
                                                    <asp:Label ID="tt" runat="server" Text='<%# Eval("Name") %>' />?
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <asp:Button ID="ButtonOk" runat="server" Text="OK" CssClass="btn btn-primary" Width="15%" />
                                            <asp:Button ID="ButtonCancel" runat="server" Text="Cancel" CssClass="btn btn-default" Width="15%" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>
                    </ItemTemplate>
                </asp:ListView>

                <div class="row">
                    <div class="col-md-6">
                        <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvInfraObjectsList">
                            <Fields>
                                <asp:TemplatePagerField>
                                    <PagerTemplate>
                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                        Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                        <br />
                                    </PagerTemplate>
                                </asp:TemplatePagerField>
                            </Fields>
                        </asp:DataPager>
                    </div>
                    <div class="col-md-6 text-right">
                        <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvInfraObjectsList" PageSize="10">
                            <Fields>
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                    ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                    NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                    NumericButtonCssClass="btn-pagination" />
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                    ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                            </Fields>
                        </asp:DataPager>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
</asp:Content>