﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "MariaDB", Namespace = "http://www.ContinuityPlatform.com/types")]
   public class MariaDB :BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public string DatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        [DataMember]
        public string AttachedServers
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }

        [DataMember]
        public int Port
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public MariaDB()
            : base()
        {

        }

        #endregion
    }
}
