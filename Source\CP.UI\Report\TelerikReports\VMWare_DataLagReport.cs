namespace CP.UI.Report.TelerikReports
{
    using CP.Common.DatabaseEntity;
    using log4net;
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Data;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;

    /// <summary>
    /// Summary description for MssqlDatalagReport.
    /// </summary>
    public partial class VMWare_DataLagReport : Telerik.Reporting.Report
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(VMWareSPDatalag));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();

        public VMWare_DataLagReport()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }
        private void showtable()
        {
            try
            {
                var dataTable = new DataTable();

                string strDate = Convert.ToDateTime(this.ReportParameters["iStartDate"].Value).ToString("dd-MM-yyyy");
                string endDate = Convert.ToDateTime(this.ReportParameters["iEndDate"].Value).ToString("dd-MM-yyyy");
                int infraObjectId = Convert.ToInt32(this.ReportParameters["iInfraId"].Value);

                IList<VmwareVsphereMonitor> VMwaresphereList = new List<VmwareVsphereMonitor>();
                VMwaresphereList = Facade.GetVmwareVsphereGetByDate(infraObjectId, strDate, endDate);

                if (VMwaresphereList != null && VMwaresphereList.Count > 0)
                {
                    dataTable.Columns.Add("Sr.No.");
                    dataTable.Columns.Add("PREsxiServerIP");
                    dataTable.Columns.Add("DREsxiServerIP");
                    dataTable.Columns.Add("PRReplicationStatus");
                    dataTable.Columns.Add("DRReplicationStatus");
                    dataTable.Columns.Add("PRLastSyncSize");
                    dataTable.Columns.Add("DRLastSyncSize");
                    dataTable.Columns.Add("DataLag");
                    _logger.Info("Data Mapping Start For Report.");
                    int i = 1;
                    foreach (VmwareVsphereMonitor vsmoni in VMwaresphereList)
                    {
                        DataRow dr = dataTable.NewRow();
                        dr["Sr.No."] = i.ToString();
                        dr["PREsxiServerIP"] = vsmoni.PREsxiServerIP != null ? vsmoni.PREsxiServerIP : "N/A";
                        dr["DREsxiServerIP"] = vsmoni.DREsxiServerIP != null ? vsmoni.DREsxiServerIP : "N/A";

                        dr["PRReplicationStatus"] = vsmoni.PRReplicationStatus != null ? vsmoni.PRReplicationStatus : "N/A";
                        dr["DRReplicationStatus"] = vsmoni.DRReplicationStatus != null ? vsmoni.DRReplicationStatus : "N/A";
                        dr["PRLastSyncSize"] = vsmoni.PRLastSyncSize != null ? vsmoni.PRLastSyncSize : "N/A";
                        dr["DRLastSyncSize"] = vsmoni.DRLastSyncSize != null ? vsmoni.DRLastSyncSize : "N/A";

                        // InfraObject InfraObj = Facade.GetInfraObjectById(Convert.ToInt32(ddlGroup.SelectedValue));
                        InfraObject InfraObj = Facade.GetInfraObjectById(infraObjectId);
                        BusinessFunction businessFtn = Facade.GetBusinessFunctionById(InfraObj != null ? InfraObj.BusinessFunctionId : 0);
                        TimeSpan conDatalag = TimeSpan.FromSeconds(Convert.ToDouble(businessFtn.ConfiguredRPO));


                        dr["DataLag"] = vsmoni.DataLag != null ? vsmoni.DataLag : "N/A";
                        i++;
                        dataTable.Rows.Add(dr);

                    }
                }
                this.DataSource = dataTable;
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method, InnerException Message " + ex.InnerException.Message);
            }
        }

       

        private void VMWare_DataLagReport_NeedDataSource(object sender, EventArgs e)
        {
            showtable();

        }

        
    }
}