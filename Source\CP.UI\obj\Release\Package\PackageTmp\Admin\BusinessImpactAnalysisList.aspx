﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="BusinessImpactAnalysisList.aspx.cs" Inherits="CP.UI.Admin.BusinessImpactAnalysisList"
    Title="Continuity Patrol :: BIA-BusinessImpactanalysisList" %>

<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <div class="grid_5 grid-15">
            <div id="ulMessage" runat="server" visible="false">
                <asp:Label ID="lblMessage" runat="server" Text="ddddd"></asp:Label>
                <span class="close-bt"></span>
            </div>
            <asp:UpdatePanel ID="upnlBIAList" runat="server" UpdateMode="Conditional">
                <ContentTemplate>

                  
                    <h3>
                        <img src="../Images/bia-icon.png">
                        BIA List</h3>
                  
                    <div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="row">
                                <div class="col-md-5 col-md-push-7 text-right">
                                    <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Company Name"></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" />
                                </div>
                            </div>
                            <hr />

                            <asp:ListView ID="lvBiaList" runat="server" OnItemDeleting="LvBiaDeleteing" OnItemEditing="LvBiaItemEditing" OnItemDataBound="LvBiaItemDataBound">
                                <LayoutTemplate>
                                    <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                                        <thead>
                                            <tr>
                                                <th style="width: 4%;">
                                                    <span>
                                                        <img src="../Images/icons/database.png" /></span>
                                                </th>
                                                <th>Application Group Name
                                                </th>
                                                <th>Critical Times
                                                </th>
                                                <th>Business Information
                                                </th>
                                                <th>Financial Loss/Profit
                                                </th>
                                                <th>Action
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                            </tr>
                                        </tbody>
                                    </table>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td class="th table-check-cell">
                                            <asp:Label ID="ID" runat="server" Text='<%# Eval("ApplicationGroupId") %>' Visible="false" />
                                            <%#Container.DataItemIndex+1 %>
                                        </td>
                                        <td>
                                            <asp:Label ID="application_NAME" runat="server" Text='<%# Eval("Name") %>' />
                                        </td>
                                        <td>
                                            <asp:Label ID="CriticalTimes" runat="server" Text='<%# Eval("CriticalTimes") %>' />
                                        </td>
                                        <td>
                                            <asp:Label ID="Type" runat="server" Text='<%#GetBusinessOverview(Eval("BriefOverview")) %>' />
                                        </td>
                                        <td>
                                            <asp:Label ID="Label1" runat="server" Text='<%# GetFinanceImact(Eval("FinancialLossProfit")) %>' />
                                        </td>
                                        <td>
                                            <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                            <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                        </td>
                                        <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                            ConfirmText='<%# "Are you sure want to delete " + Eval("Name") + " ? " %>' OnClientCancel="CancelClick">
                                        </TK1:ConfirmButtonExtender>
                                    </tr>
                                </ItemTemplate>
                                <EditItemTemplate>
                                    <tr>
                                        <td>
                                            <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                        </td>
                                        <td>
                                            <asp:ImageButton ID="ImageButton4" runat="server" CommandName="Update" AlternateText="Update"
                                                ToolTip="Update" ImageUrl="../Images/icons/arrow-090.png" />
                                            <asp:ImageButton ID="ImageButton3" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                        </td>
                                    </tr>
                                </EditItemTemplate>
                                <EmptyDataTemplate>
                                    <tr>
                                        <td>
                                            <asp:Label ID="pageResult" runat="server" Text="No Records found"></asp:Label>
                                        </td>
                                    </tr>
                                </EmptyDataTemplate>
                            </asp:ListView>
                            <div class="row">
                                <div class="col-md-6">
                                    <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvBiaList" PageSize="4">
                                        <Fields>
                                            <asp:TemplatePagerField>
                                                <PagerTemplate>
                                                    <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                    Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                    <br />
                                                </PagerTemplate>
                                            </asp:TemplatePagerField>
                                        </Fields>
                                    </asp:DataPager>
                                </div>
                                <div class="col-md-6 text-right">
                                    <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvBiaList" PageSize="10">
                                        <Fields>
                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                            <asp:NumericPagerField PreviousPageText="..." NextPageText="..." ButtonCount="10"
                                                NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                NumericButtonCssClass="btn-pagination" />
                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                        </Fields>
                                    </asp:DataPager>
                                </div>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>
    </div>
</asp:Content>