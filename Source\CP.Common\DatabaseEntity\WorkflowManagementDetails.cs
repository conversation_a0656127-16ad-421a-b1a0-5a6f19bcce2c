﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "WorkflowManagementDetails", Namespace = "www.ContinuityPlatform.com")]
    public class WorkflowManagementDetails : BaseEntity
    {
        #region Properties
        [DataMember]
        public int id { get; set; }
        [DataMember]
        public int ActionType { get; set; }
        [DataMember]
        public string WorkflowName { get; set; }
        [DataMember]
        public string profilename { get; set; }
        [DataMember]
        public string Infraobjectname { get; set; }
        [DataMember]
        public string Username { get; set; }
        [DataMember]
        public string IsLock { get; set; }
        [DataMember]
        public string Lockname
        {
            get
            {
                if (IsLock == "0")
                {
                    return "Lock";
                }
                else
                {
                    return "UnLock";
                }
            }
        }

        [DataMember]
        public string Action_type { get; set; }
       
        [DataMember]
        public string BusinessService { get; set; }
        [DataMember]
        public string BusinessFunction { get; set; }
       
        #endregion Properties
    }

}
