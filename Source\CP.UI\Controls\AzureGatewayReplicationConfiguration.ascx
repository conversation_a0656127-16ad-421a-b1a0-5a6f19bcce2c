<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="AzureGatewayReplicationConfiguration.ascx.cs" Inherits="CP.UI.Controls.AzureGatewayReplicationConfiguration" %>

<div id="Div1" runat="server" class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">AzureGatewayReplication Configuration</h4>
        </div>
        <div class="widget-body">
               
			 <div class="form-group">
                <label class="col-md-3 control-label" for="txtSubscriptionName" id="SubscriptionName" runat="server">
                   SubscriptionName<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtSubscriptionName" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvSubscriptionName" CssClass="error" runat="server" ControlToValidate="txtSubscriptionName"
                        ErrorMessage="Enter SubscriptionName" Display="Dynamic"></asp:RequiredFieldValidator>                 
                </div>
            </div>

 <div class="form-group">
                <label class="col-md-3 control-label" for="txtGatewayName" id="GatewayName" runat="server">
                   GatewayName<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtGatewayName" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvGatewayName" CssClass="error" runat="server" ControlToValidate="txtGatewayName"
                        ErrorMessage="Enter GatewayName" Display="Dynamic"></asp:RequiredFieldValidator>                 
                </div>
            </div>

 <div class="form-group">
                <label class="col-md-3 control-label" for="txtResourceGroupName" id="ResourceGroupName" runat="server">
                   ResourceGroupName<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtResourceGroupName" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvResourceGroupName" CssClass="error" runat="server" ControlToValidate="txtResourceGroupName"
                        ErrorMessage="Enter ResourceGroupName" Display="Dynamic"></asp:RequiredFieldValidator>                 
                </div>
            </div>

 <div class="form-group">
                <label class="col-md-3 control-label" for="txtLocation" id="Location" runat="server">
                   Location<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtLocation" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvLocation" CssClass="error" runat="server" ControlToValidate="txtLocation"
                        ErrorMessage="Enter Location" Display="Dynamic"></asp:RequiredFieldValidator>                 
                </div>
            </div>

 <div class="form-group">
                <label class="col-md-3 control-label" for="txtPublicIPName" id="PublicIPName" runat="server">
                   PublicIPName<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtPublicIPName" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvPublicIPName" CssClass="error" runat="server" ControlToValidate="txtPublicIPName"
                        ErrorMessage="Enter PublicIPName" Display="Dynamic"></asp:RequiredFieldValidator>                 
                </div>
            </div>

 <div class="form-group">
                <label class="col-md-3 control-label" for="txtPublicIPRGName" id="PublicIPRGName" runat="server">
                   PublicIPRGName<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtPublicIPRGName" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvPublicIPRGName" CssClass="error" runat="server" ControlToValidate="txtPublicIPRGName"
                        ErrorMessage="Enter PublicIPRGName" Display="Dynamic"></asp:RequiredFieldValidator>                 
                </div>
            </div>

 <div class="form-group">
                <label class="col-md-3 control-label" for="txtPublicIPLocation" id="PublicIPLocation" runat="server">
                   PublicIPLocation<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtPublicIPLocation" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvPublicIPLocation" CssClass="error" runat="server" ControlToValidate="txtPublicIPLocation"
                        ErrorMessage="Enter PublicIPLocation" Display="Dynamic"></asp:RequiredFieldValidator>                 
                </div>
            </div>

	

            <div class="form-actions row">
                <div class="col-md-3">                    
                 </div>
                <div class="col-md-6" style="margin-left: 40.3%;">
                    <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save" OnClick="btnSave_Click" />
                    <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server" Text="Cancel" CausesValidation="false" OnClick="btnCancel_Click"/>
                </div>
            </div>
        </div>
    </div>
</div>
