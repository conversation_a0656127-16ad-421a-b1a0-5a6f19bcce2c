namespace CP.UI.Report.TelerikReports
{
    using System;
    using System.ComponentModel;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;

    /// <summary>
    /// Summary description for MSSQLAlwaysOnDataLagRpts.
    /// </summary>
    public partial class NutanixLeapReplOnDataLagRpt : Telerik.Reporting.Report
    {
        public NutanixLeapReplOnDataLagRpt()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }
    }
}