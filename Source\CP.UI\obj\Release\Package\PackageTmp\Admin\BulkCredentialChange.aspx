﻿<%@ Page Title="Bulk Credential" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="BulkCredentialChange.aspx.cs" Inherits="CP.UI.Admin.BulkCredentialChange" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script src="../Script/EncryptDecrypt.js"></script>
    <script type="text/javascript">

        function clearText(control) {

            if (control.value != "") {
                control.value = getOrignalData(control, $('#ctl00_cphBody_hdfStaticGuid').val());
            }
        }
        function getHashData(control) {
            if (control.value.length == 0)
                $("#ctl00_cphBody_lblPsswordErrorMsg").css("display", "inline-block");
            else {
                $("#ctl00_cphBody_lblPsswordErrorMsg").css("display", "none");

                control.value = genrateUserNameHash(control, $('#ctl00_cphBody_hdfStaticGuid').val());
            }
            //control.value = getOrignalData(hash, $('#ctl00_cphBody_hdfStaticGuid').val());
        }



        function ValidateCheckBoxList(sender, args) {
            var checkBoxList = document.getElementById("<%=cblstGroup.ClientID %>");
            var checkboxes = checkBoxList.getElementsByTagName("input");
            var isValid = false;
            for (var i = 0; i < checkboxes.length; i++) {
                if (checkboxes[i].checked) {
                    isValid = true;
                    break;
                }
            }
            args.IsValid = isValid;
        }
    </script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <h3>Bulk Credential Changes
                </h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <%-- <asp:UpdatePanel ID="uplcbsltGroup" runat="server" UpdateMode="Always">
                            <ContentTemplate>--%>
                                <div class="form-group" id="dvusername" runat="server">
                                    <%--  <asp:Label ID="lblUsername11" Text="User Name" runat="server" class="col-md-3 control-label">
                                    </asp:Label>--%>
                                    <label class="col-md-3 control-label">
                                        User Name<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtUserName" CssClass="form-control" runat="server" autocomplete="OFF" AutoPostBack="false"></asp:TextBox>
                                        <%--   <asp:RequiredFieldValidator ID="rqdUsrID" CssClass="error" runat="server" ControlToValidate="txtUserName"
                                            ErrorMessage="Enter User Name" Display="Dynamic"></asp:RequiredFieldValidator>--%>
                                        <asp:Label ID="lblUserNameMsg" runat="server" Visible="false" ForeColor="Red"></asp:Label>
                                        <asp:Button ID="btnSearch" runat="server" Text="Search" OnClick="btnSearch_Click" Font-Bold="true" AutoPostBack="True" />

                                    </div>
                                </div>
                                <%--      </ContentTemplate>
                        </asp:UpdatePanel>--%>
                                <%-- <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>--%>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Server<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:Panel ID="Panel1" runat="server" ScrollBars="Vertical" Height="101px" class="padding pull-left"
                                            Width="48%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px" TabIndex="7">
                                            <asp:CheckBoxList ID="cblstGroup" runat="server" AutoPostBack="True" OnSelectedIndexChanged="cblstGroup_SelectedIndexChanged">
                                                <asp:ListItem Value="0">ALL</asp:ListItem>
                                            </asp:CheckBoxList>
                                        </asp:Panel>
                                        <asp:Label ID="lblservermsg" runat="server" Visible="false" ForeColor="Red"></asp:Label>
                                        <%--     <asp:CustomValidator ID="CustomValidator1" ErrorMessage="Please select at least one IPAddress."
                                            ForeColor="Red" ClientValidationFunction="ValidateCheckBoxList" runat="server" />--%>
                                    </div>
                                </div>

                                <div class="form-group" id="divOSType" runat="server">
                                    <label class="col-md-3 control-label" for="ddlOS">
                                        Operating System<span class="inactive">*</span>
                                    </label>
                                    <%--<span class="inactive">*</span>--%>
                                    <div class="col-md-9">
                                        <asp:Panel ID="Panel2" runat="server" ScrollBars="Vertical" Height="101px" class="padding pull-left"
                                            Width="48%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px" TabIndex="7">
                                            <asp:CheckBoxList ID="cbkos" runat="server" AutoPostBack="True" OnSelectedIndexChanged="cbkos_SelectedIndexChanged">
                                                <asp:ListItem Text="Select All"></asp:ListItem>
                                            </asp:CheckBoxList>
                                        </asp:Panel>
                                        <%-- <asp:DropDownList ID="ddlOS" runat="server" TabIndex="9" AutoPostBack="true"
                                            CssClass="multiselectDrop col-md-6" data-style="btn-default" multiple="multiple">
                                             <asp:ListItem>1</asp:ListItem>
                                             <asp:ListItem>1</asp:ListItem>
                                             <asp:ListItem>1</asp:ListItem>
                                        </asp:DropDownList>--%>
                                        <%--    <asp:RequiredFieldValidator ID="rfvddlOS" runat="server" CssClass="error" ControlToValidate="ddlOS"
                                            Display="Dynamic" InitialValue="0" ErrorMessage="Select Operating System"></asp:RequiredFieldValidator>--%>
                                    </div>
                                </div>



                                
                                <div class="form-group" id="dvpassword" runat="server">
                                    <label id="lblPassword11" runat="server" class="col-md-3 control-label">
                                        Password<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtPassword" CssClass="form-control" runat="server" TextMode="Password" autocomplete="OFF" AutoPostBack="true" onblur="getHashData(this)" onfocus="clearText(this)" OnTextChanged="txtPassword_TextChanged"></asp:TextBox>
                                        <asp:Label ID="lblPsswordErrorMsg" runat="server" Style="display: none" ForeColor="Red" Text="Enter Password"></asp:Label>
                                        <asp:Label ID="lblPassword" runat="server" ForeColor="Red" Text="Invalid password" Visible="false"></asp:Label>
                                        <%-- <asp:RequiredFieldValidator ID="rfvPwd" CssClass="error" runat="server" ControlToValidate="txtPassword"
                                            ErrorMessage="Enter Password" Display="Dynamic"></asp:RequiredFieldValidator>--%>
                                    </div>
                                </div>


                                <div class="form-actions row">
                                    <div class="col-md-5">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span>
                                        <span>Required Fields</span>
                                    </div>
                                    <div class="col-md-7">
                                        <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="15%" runat="server" Text="Update" Style="margin-left: 10px;" OnClick="btnSave_Click"
                                            TabIndex="6" />
                                        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server"
                                            Text="Cancel" OnClick="btnCancel_Click" />
                                        <asp:Label ID="lblupdatemsg" runat="server" ForeColor="Green" Style="margin-left: 10px;"></asp:Label>
                                    </div>
                                </div>

                                <%-- </ContentTemplate>
                        </asp:UpdatePanel>--%>
                            </div>
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>



</asp:Content>
