﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
//using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    
    [Serializable]
    [DataContract(Name = "VmwareVsphereRepli", Namespace = "http://www.ContinuityPlatform.com/types")]

   public class VmwareVsphereRepli : BaseEntity
    {
         #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string ClusterName { get; set; }

        [DataMember]
        public string VMName { get; set; }

        [DataMember]
        public string ESXiUserName { get; set; }

        [DataMember]
        public string ESXiPassword { get; set; }

        [DataMember]
        public string CREATEDATE { get; set; }


        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }



        #endregion Properties
    
    }
}
