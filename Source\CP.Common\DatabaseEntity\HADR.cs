﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "HADR", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class HADR : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string PRIp { get; set; }

        [DataMember]
        public string DRIp { get; set; }

        [DataMember]
        public string PRDatabaseInstance { get; set; }

        [DataMember]
        public string DRDatabaseInstance { get; set; }

        [DataMember]
        public string PRDatabaseStatus { get; set; }

        [DataMember]
        public string DRDatabaseStatus { get; set; }

        [DataMember]
        public string PRLogFile { get; set; }

        [DataMember]
        public string DRLogFile { get; set; }

        [DataMember]
        public string PRCurrentLSN { get; set; }

        [DataMember]
        public string DRCurrentLSN { get; set; }

        [DataMember]
        public string PRLSN { get; set; }

        [DataMember]
        public string DRLSN { get; set; }

        [DataMember]
        public string PRTimestamp { get; set; }

        [DataMember]
        public string DRTimestamp { get; set; }

        [DataMember]
        public string Datalag { get; set; }

        [DataMember]
        public string LogGap { get; set; }

        [DataMember]
        public string DatabaseVersion { get; set; }

        [DataMember]
        public string DatabaseDRVersion { get; set; }

        public string InfraObjectName
        {
            get;
            set;
        }

        public string BusinessServiceName
        {
            get;
            set;
        }

        [DataMember]
        public string PRServerInstance
        {
            get;
            set;
        }

        [DataMember]
        public string DRServerInstance
        {
            get;
            set;
        }

        [DataMember]
        public string PRInstanceStatus
        {
            get;
            set;
        }

        [DataMember]
        public string DRInstanceStatus
        {
            get;
            set;
        }

        #endregion Properties
    }
}