﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsLogin.Master" AutoEventWireup="true" CodeBehind="ChangePassword.aspx.cs" Inherits="CP.UI.ChangePassword" Title="Continuity Patrol :: Change Password" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/ChangePassword.js"></script>
    <script src="../Script/EncryptDecrypt.js"></script>


</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div id="login" class="innerLR">
        <div class="row margin-none center">
            <div class="col-xs-12 innerB margin-top90 margin-bottom">
                <img src="../Images/ContinuityPatrol_logo_black1.png" class="center" alt="Continuity Patrol" /><asp:Label ID="lblVersion" runat="server" Text=""></asp:Label>
            </div>
            <div class="clearfix">&nbsp;</div>
            <div class="col-md-6 col-md-push-3 glyphicons keys margin-top">
                <i></i>
                <div class="widget box-shadow-none">
                    <div class="widget-head">
                        <h3 class="heading">
                            <a class="glyphicons keys">
                                <i></i></a>
                            <asp:Label ID="lblheader" CssClass="text-bold padding " runat="server" Text="Change Your Password"></asp:Label>
                        </h3>
                    </div>
                    <div class="widget-body text-left">
                        <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <input type="hidden" id="hdfStaticGuid" runat="server" />
                                <asp:HiddenField ID="HiddenField1" runat="server" />
                                <asp:Panel runat="server" Visible="true" ID="oldPassPanel">
                                    <label>Old Password <span class="inactive">*</span></label>
                                    <div>
                                        <asp:TextBox ID="txtPwd" runat="server" AutoPostBack="True" onfocus="cleartext(this)" TextMode="Password"
                                            OnTextChanged="TxtPwdTextChanged" autocomplete="off" CssClass="form-control chk-caps" onblur="getHashData(this)" />
                                        <asp:RequiredFieldValidator ID="rfvOldPassword" runat="server" ValidationGroup="vlGroupSite" ControlToValidate="txtPwd"
                                            ErrorMessage="Enter Old Password" Display="dynamic" CssClass="error"></asp:RequiredFieldValidator>

                                        <%--<span class="caps-error">Caps Lock is ON.</span>--%>
                                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" style="display: inline">
                                            <ContentTemplate>
                                                <asp:Label ID="lblError" runat="server" ForeColor="Red"
                                                    Text="Incorrect Password" Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                                            </ContentTemplate>
                                            <Triggers>
                                                <asp:AsyncPostBackTrigger ControlID="txtPwd" EventName="TextChanged" />
                                            </Triggers>
                                        </asp:UpdatePanel>
                                    </div>
                                </asp:Panel>
                                <div>
                                    <label>New Password <span class="inactive">*</span></label>
                                    <div>
                                        <asp:TextBox ID="txtNewPwd" runat="server" AutoPostBack="true" OnTextChanged="TxtNewPwdTextChanged" onblur="getHashData(this)" onfocus="cleartext(this)" TextMode="Password" autocomplete="off" placeholder="Ex:Admin@123" CssClass="form-control chk-capsp" />
                                        <asp:RequiredFieldValidator ID="rfvPwd" runat="server" Display="Dynamic" CssClass="error" ValidationGroup="vlGroupSite" ControlToValidate="txtNewPwd" ErrorMessage="Enter New Password"></asp:RequiredFieldValidator>

                                        <span class="caps-error">Caps Lock is ON.</span>
                                        <asp:UpdatePanel ID="UpdatePanel3" runat="server" style="display: inline">
                                            <ContentTemplate>
                                                <label style="color: red" id="lblalpha"></label>
                                                <asp:Label ID="lblErr" runat="server" Visible="false" CssClass="error"></asp:Label>
                                            </ContentTemplate>
                                            <Triggers>
                                                <asp:AsyncPostBackTrigger ControlID="txtNewPwd" EventName="TextChanged" />
                                            </Triggers>
                                        </asp:UpdatePanel>

                                    </div>
                                </div>
                                <div>
                                    <label>Confirm New Password <span class="inactive">*</span></label>
                                    <div>
                                        <asp:TextBox ID="txtConPwd" runat="server" TextMode="Password" OnTextChanged="TxtConfPwdTextChanged" onblur="getHashData(this)" onfocus="cleartext(this)" AutoPostBack="true" autocomplete="off" CssClass="form-control chk-capspc" />
                                        <asp:RequiredFieldValidator ID="rfvConPwd" runat="server" ValidationGroup="vlGroupSite" ControlToValidate="txtConPwd" Display="Dynamic" CssClass="error"
                                            ErrorMessage="Enter Confirm Password"></asp:RequiredFieldValidator>
                                        <%--<asp:CompareValidator ID="cvNewconfirm" runat="server" ErrorMessage="Passwords are not match" CssClass="error" ControlToCompare="txtNewPwd" Display="Dynamic" ControlToValidate="txtConPwd"></asp:CompareValidator>--%>

                                        <span class="caps-error">Caps Lock is ON.</span>

                                        <asp:UpdatePanel ID="UpdatePanel2" runat="server" style="display: inline">
                                            <ContentTemplate>
                                                <asp:Label ID="lblMessage" runat="server" Text="" ForeColor="Red" Visible="false" CssClass="error"></asp:Label>
                                            </ContentTemplate>
                                            <Triggers>
                                                <asp:AsyncPostBackTrigger ControlID="txtConPwd" EventName="TextChanged" />
                                            </Triggers>
                                        </asp:UpdatePanel>
                                    </div>
                                </div>

                                <hr class="separator" />
                                <span class="strong">Note:</span>&nbsp;<span class="inactive">*</span>
                                <asp:Label ID="lblpwdpolicy" Visible="false" runat="server"></asp:Label>
                                <asp:Label ID="lblDefaultPwdpolicy" runat="server" Visible="false" Text="Password must have minimum (8,16) characters. At least 1 upper-case alphabetic, 4 lower-case, 3 numeric character and 1 special characters."></asp:Label>

                                <hr class="separator" />
                                <div class="row">
                                    <div class="col-xs-4">
                                        <asp:Label ID="lblProfileResult" runat="server" Text="&nbsp;"></asp:Label>
                                    </div>
                                    <div class="col-xs-8 text-right ">
                                        <asp:Button ID="btnSave" CssClass="btn btn-primary" runat="server" Text="Change Password" ValidationGroup="vlGroupSite" OnClick="BtnSaveClick" Width="35%" />
                                        <asp:Button ID="btnCancel" CssClass="btn btn-default" runat="server" Text="Cancel" CausesValidation="False" ValidationGroup="vlGroupSite" Width="35%" EnableViewState="False" OnClick="BtnCancelClick" />
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <asp:HiddenField ID="hdtokenKey" runat="server" />

    <script src="../Script/jquery.capslockstate.js"></script>
    <script>
        function pageLoad() {
            $(window).bind("capsOn", function (event) {
                if ($(".chk-caps").length > 0) {
                    $(".chk-caps:focus").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-caps:focus").nextAll(".caps-error").hide();
            });
            $(".chk-caps").bind("focusout", function (event) {
                $(".chk-caps:focus").nextAll(".caps-error").hide();
            });
            $(".chk-caps").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-caps:focus").nextAll(".caps-error").show();
                }
            });
            $(window).capslockstate();



            $(window).bind("capsOn", function (event) {
                if ($(".chk-capspc:focus").length > 0) {
                    $(".chk-capspc").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-capspc").nextAll(".caps-error").hide();
            });
            $(".chk-capspc").bind("focusout", function (event) {
                $(".chk-capspc").nextAll(".caps-error").hide();
            });
            $(".chk-capspc").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-capspc").nextAll(".caps-error").show();
                }
            });
            $(window).capslockstate();

            /*For Password and confirm password field chk-capsp and chk-capspc */


            $(window).bind("capsOn", function (event) {
                if ($(".chk-capsp:focus").length > 0) {
                    $(".chk-capsp").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-capsp").nextAll(".caps-error").hide();
            });
            $(".chk-capsp").bind("focusout", function (event) {
                $(".chk-capsp").nextAll(".caps-error").hide();
            });
            $(".chk-capsp").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-capsp").nextAll(".caps-error").show();
                }
            });
            $(window).capslockstate();
        }
    </script>
</asp:Content>
