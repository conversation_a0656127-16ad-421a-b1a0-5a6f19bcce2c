﻿using CP.Common.Base;
using CP.Common.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;


namespace CP.Common
{
    public class eBDRProfileReplication : BaseEntity
    {
        [DataMember]
        public string ProfileName { get; set; }

        [DataMember]
        public string SourceHost { get; set; }

        [DataMember]
        public string TargetHost { get; set; }

        [DataMember]
        public string OsType { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public int Helth { get; set; }

        [DataMember]
        public string RPO { get; set; }

        [DataMember]
        public string MachineName { get; set; }

        [DataMember]
        public string isActive { get; set; }

        [DataMember]
        public string DataLag { get; set; }

        [DataMember]
        public int InfraobjectID { get; set; }

        [DataMember]
        public int currentReplicationCount { get; set; }


    }
}
