﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="MSSQLMonitoring.aspx.cs" Inherits="CP.UI.MSSQLMonitoring" Title="Continuity Patrol :: MSSQL Monitoring" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style type="text/css">
       
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
       
        <h3>
            <img src="../Images/monitor.png">
            MSSQL Log Shipping Monitoring</h3>
       
        <div class="widget">
            <div class="widget-head" id="exhealth">
                <asp:label id="lblHead" runat="server" CssClass="heading" style="padding-left:5px !important;">SQL Log Shipping Health</asp:label>
            </div>
            <div class="widget-body" id="exhealth-content">
                <div class="widget-body">
                   
                    <table class="table table-bordered table-white">
                      
                        <thead>
                            <tr>
                                <th class="col-md-4">MSSQL Health
                                </th>
                                <th class="col-md-4">Production
                                <asp:Label ID="lblprserverhead" runat="server" Text="NA" />
                                </th>
                                <th>DR
                                <asp:Label ID="lbldrserverhead" runat="server" Text="NA" />
                                </th>
                            </tr>
                        </thead>
                      
                        <tbody>
                            <tr>
                                <td>MSSQL Server Instance
                                </td>
                                <td>
                                    <span class="icon-storagePR float-left"></span>
                                    <asp:Label ID="lblPRServer" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span class="icon-storageDR float-left"></span>
                                    <asp:Label ID="lblDRServer" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Database
                                </td>
                                <td>
                                    <span class="icon-database float-left"></span>
                                    <asp:Label ID="lblprdatabase" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span class="icon-database float-left"></span>
                                    <asp:Label ID="lbldrdatabase" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Database State
                                </td>
                                <td>
                                    <span class="Replicating float-left"></span>
                                    <asp:Label ID="lblprDBState" CssClass="text-success" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span class="Replicating float-left"></span>
                                    <asp:Label ID="lbldrDBState" runat="server" CssClass="text-success" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Database Recovery Model
                                </td>
                                <td>
                                    <asp:Label ID="Label24" CssClass="icon-dbrecover" runat="server"></asp:Label>
                                    <asp:Label ID="lblprDBRecovery" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label2" CssClass="icon-dbrecover" runat="server"></asp:Label>
                                    <asp:Label ID="lbldrDBRecovery" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Transaction Log Shipping Status
                                </td>
                                <td>
                                    <asp:Label ID="lblprtranslogflag" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblprtranslog" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lbldrtranslogflag" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lbldrtranslog" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Access Restrict Status
                                </td>
                                <td>
                                    <asp:Label ID="Label29" CssClass="icon-dbrestrict" runat="server"></asp:Label>
                                    <asp:Label ID="lblAccessrestrictPR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label1" CssClass="icon-dbrestrict" runat="server"></asp:Label>
                                    <asp:Label ID="lblAccessrestrictDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Backup Job Status
                                </td>
                                <td>
                                    <asp:Label ID="lblbackupstatusflag" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblbackupstatus" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>--
                                </td>
                            </tr>
                            <tr>
                                <td>Backup Job Execution Status
                                </td>
                                <td>
                                    <asp:Label ID="lblbackupjobexejob" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblbackupjobexe" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>--
                                </td>
                            </tr>
                            <tr>
                                <td>Copy Job Status
                                </td>
                                <td>--
                                </td>
                                <td>
                                    <asp:Label ID="lblcopyjobstatusflag" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblcopyjobstatus" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Copy Job Execution Status
                                </td>
                                <td>--
                                </td>
                                <td>
                                    <asp:Label ID="lblcopyjobexejob" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblcopyjobexe" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Restore Job Status
                                </td>
                                <td>--
                                </td>
                                <td>
                                    <asp:Label ID="lblrestorejobstatflag" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblrestorejobstat" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Restore Job Execution Status
                                </td>
                                <td>--
                                </td>
                                <td>
                                    <asp:Label ID="lblrestorejobexecjob" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblrestorejobexec" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Updatability
                                </td>
                                <td>
                                    <asp:Label ID="Label25" CssClass="icon-dbupdate" runat="server"></asp:Label>
                                    <asp:Label ID="lblUpdateabilitypPR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label27" CssClass="icon-dbupdate" runat="server"></asp:Label>
                                    <asp:Label ID="lblUpdateabilitypDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Server Edition
                                </td>
                                <td>
                                    <asp:Label ID="Label28" CssClass="icon-edition" runat="server"></asp:Label>
                                    <asp:Label ID="lblVersionPR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label10" CssClass="icon-edition" runat="server"></asp:Label>
                                    <asp:Label ID="lblVersionDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Server Release
                                </td>
                                <td>
                                    <asp:Label ID="Label22" CssClass="icon-release" runat="server"></asp:Label>
                                    <asp:Label ID="lblReleasePR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label23" CssClass="icon-release" runat="server"></asp:Label>
                                    <asp:Label ID="lblReleaseDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Size (in MB)
                                </td>
                                <td>
                                    <asp:Label ID="Label30" CssClass="icon-size" runat="server"></asp:Label>
                                    <asp:Label ID="lblSizePR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label31" CssClass="icon-size" runat="server"></asp:Label>
                                    <asp:Label ID="lblSizeDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                       
                    </table>
                  
                </div>
            </div>
        </div>
        
        <div class="widget" data-toggle="collapse-widget" data-collapse-closed="true">
            <div class="widget-head" id="monitor">
               
                    <asp:Label ID="lblAppName" runat="server" CssClass="heading" style="padding-left:5px !important;">SQL Log Shipping Status</asp:Label>
                    
            </div>
            <div class="widget-body">
                <div id="monitor-content">
                    <table class="table table-bordered table-white">
                        <thead>
                            <tr>
                                <th style="border: 0px !important;">SQL Log shipping Name
                                </th>
                                <th style="border: 0px !important;">Status
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Last backup file Name
                                </td>
                                <td>
                                    <asp:Label ID="Label12" CssClass="icon-backup" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lbllastbackup" runat="server" Text=" NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>LSN of last backup file
                                </td>
                                <td>
                                    <asp:Label ID="Label13" CssClass="icon-currentlsn" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lbllastbackuplsn" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last Backup Date & Time
                                </td>
                                <td>
                                    <asp:Label ID="Label16" CssClass="icon-Time" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lablbackupdate" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last copied file Name
                                </td>
                                <td>
                                    <asp:Label ID="Label11" CssClass="icon-backup" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lbllastcopy" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>LSN of last copied file
                                </td>
                                <td>
                                    <asp:Label ID="Label14" CssClass="icon-currentlsn" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lbllastcopylsn" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last Copied Date & Time
                                </td>
                                <td>
                                    <asp:Label ID="Label18" CssClass="icon-Time" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lbllastcopydate" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last restored file Name
                                </td>
                                <td>
                                    <asp:Label ID="Label21" CssClass="icon-backup" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lbllastrestore" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>LSN of last restored file
                                </td>
                                <td>
                                    <asp:Label ID="Label20" CssClass="icon-currentlsn" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lbllastrestorelsn" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last Restored Date & Time
                                </td>
                                <td>
                                    <asp:Label ID="Label19" CssClass="icon-Time" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lbllastrestoredate" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
       
        <div class="widget" data-toggle="collapse-widget">
            <div class="widget-head" id="srstatus">
                
                    <asp:Label ID="Label17" CssClass="heading" runat="server" style="padding-left:5px !important;"> MSSQL Log Shipping Services Status</asp:Label>
            </div>
            <div class="widget-body">
                <div id="Div1" class="tabs-content ">
                    <table class="table table-bordered table-white" id="tblservice" runat="server">
                        <thead>
                            <tr>
                                <th class="col-md-4 text-left">Server
                                </th>
                                <th colspan="2" class="text-left" style="border: 0px !important;">
                                    <asp:Label ID="lblprimIPAddress" runat="server"
                                        Text="***************"></asp:Label>
                                </th>
                                <th colspan="2" class="text-left" style="border: 0px !important;">

                                    <asp:Label ID="lblsecIPAddress" runat="server" Text="***************"></asp:Label>
                                </th>
                            </tr>
                            <tr>
                                <td><strong>Service Name</strong>
                                </td>
                                <td><strong>Primary Status</strong>
                                </td>
                                <td><strong>Start Mode</strong>
                                </td>
                                <td><strong>DR Status</strong>
                                </td>
                                <td><strong>Start Mode</strong>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>SQL Server Service
                                </td>
                                <td>
                                    <asp:Label ID="lblPRsqlserstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRsqlserstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRsqlserMode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRsqlserstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRsqlserstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRsqlserMode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>SQL Server Agent
                                </td>
                                <td>
                                    <asp:Label ID="lblPRsqlseragentstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRsqlseragentstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRsqlseragentMode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRsqlseragentstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRsqlseragentstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRsqlseragentMode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>SQL Server Analysis Services
                                </td>
                                <td>
                                    <asp:Label ID="lblPRanalysisstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRanalysisstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRanalysismode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRanalysisstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRanalysisstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRanalysismode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>SQL Server Integration Services
                                </td>
                                <td>
                                    <asp:Label ID="lblPRIntergrationstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRIntergrationstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRIntergrationmode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRIntergrationstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRIntergrationstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRIntergrationmode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>SQL Server Reporting Services
                                </td>
                                <td>
                                    <asp:Label ID="lblPRreportingstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRreportingstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRreportingmode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRreportingstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRreportingstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRreportingmode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="style1">SQL Server VSS Writer
                                </td>
                                <td class="style1">
                                    <asp:Label ID="lblPRwriterstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRwriterstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td class="style1">
                                    <asp:Label ID="lblPRwritermode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td class="style1">
                                    <asp:Label ID="lblDRwriterstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRwriterstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td class="style1">
                                    <asp:Label ID="lblDRwritermode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>SQL Active Directory Helper Service
                                </td>
                                <td>
                                    <asp:Label ID="lblPRdirectorystatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRdirectorystatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRdirectorymode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRdirectorystatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRdirectorystatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRdirectorymode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>SQL Full-text Filter Daemon Launcher
                                </td>
                                <td>
                                    <asp:Label ID="lblPRdaemonstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRdaemonstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRdaemonmode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRdaemonstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRdaemonstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRdaemonmode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>SQL Server Browser
                                </td>
                                <td>
                                    <asp:Label ID="lblPRbrowserstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRbrowserstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRbrowsermode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRbrowserstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRbrowserstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRbrowsermode" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Windows Firewall
                                </td>
                                <td>
                                    <asp:Label ID="lblPRfirewallstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRfirewallstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblPRfirewallmode" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRfirewallstatusRun" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDRfirewallstatus" runat="server" Text="--"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRfirewallmode" runat="server" Text="--"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
    </div>
</asp:Content>