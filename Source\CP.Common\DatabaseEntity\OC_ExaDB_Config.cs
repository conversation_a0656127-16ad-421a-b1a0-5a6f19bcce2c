﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "OC_ExaDB_Config", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class OC_ExaDB_Config : BaseEntity
    {
        private ReplicationBase _replicationBase = new ReplicationBase();

       #region Properties


        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public string TenancyId
        {
            get;
            set;
        }

        [DataMember]
        public string UserId
        {
            get;
            set;
        }

        [DataMember]
        public string IsDBSystemExistUnderRoot
        {
            get;
            set;
        }

        [DataMember]
        public string CompartmentName
        {
            get;
            set;
        }

        [DataMember]
        public string CompartmentID
        {
            get;
            set;
        }

        [DataMember]
        public string DBSystemDisplayName
        {
            get;
            set;
        }

        [DataMember]
        public string DatabaseName

        {
            get;
            set;
        }

        [DataMember]
        public string HomeRegion
        {
            get;
            set;
        }


        [DataMember]
        public string ExadataVMClusterName
        {
            get;
            set;
        }


        [DataMember]
        public string DBHomeDisplayName
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public OC_ExaDB_Config()
            : base()
        {

        }

        #endregion
    }
}
