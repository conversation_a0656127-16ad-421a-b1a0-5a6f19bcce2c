﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ITInfraSummary.ascx.cs"
    Inherits="CP.UI.Admin.DashboardContols.ITInfraSummary" %>
<div class="widget widget-heading-simple widget-body-simple">
    <h3 class="innerL border-none">
      
        <img src="../Images/icons/infra-sum-icon.png" alt="IT Infra Summary" />IT Infra Summary
    </h3>
  
    <div class="widget-body">
        <div class="widget widget-tabs widget-tabs-icons-only-2 widget-activity margin-none">
          
            <div class="widget-head" style=" padding: 0 0px 0 0;">
                <ul>

                    <li class="active" style="padding-left: 7px;"><a data-toggle="tab" href="#tab-ostype">
                        <img src="../Images/icons/server.png" title="Server"></a></li>
                    <li><a class="glyphicons bell" data-toggle="tab"
                        href="#tab-dbtype">
                      
                        <img src="../Images/icons/db-line.png" title="DB"></a></li>
                    <li><a class="glyphicons sort" data-toggle="tab" href="#tab-repctype">
                        <img src="../Images/icons/storage.png" title="Replication"></a></li>
                </ul>
            </div>
           
            <div class="widget-body list">
                <div class="tab-content">
                  
                    <div class="tab-pane active" id="tab-ostype">

                      
                        <ul>
                            <asp:Repeater ID="rptServerCount" runat="server" OnItemCommand="rptServerCount_OnItemCommand" OnItemDataBound="rptServerCount_ItemDataBound">
                                <ItemTemplate>
                                    <li>
                                        <asp:Label runat="server" ID="lblIconOs"></asp:Label>
                                        <asp:LinkButton  ID="lblIcon"  runat="server"  Text=' <%# Eval("OSType")%>' CommandName="type"></asp:LinkButton>
                                       
                                       
                                        <asp:HiddenField ID="hdnOSType" runat="server" Value='<%# Eval("OSType")%>' Visible="false" />
                                        <span class="badge">
                                            <%# Eval("ServerOSTypeCount")%>
                                        </span>
                                        <div class="clearfix">
                                        </div>
                                    </li>
                                </ItemTemplate>
                            </asp:Repeater>
                        </ul>       
                      
                    </div>
                    <div class="tab-pane" id="tab-dbtype">
                      
                        <ul>
                            <asp:Repeater ID="rptDBServerCount" runat="server" OnItemDataBound="rptDBServerCount_ItemDataBound" OnItemCommand="rptDBServerCount_OnItemCommand">
                                <ItemTemplate>
                                    <li>
                                         <asp:Label ID="lblDatabaseType" runat="server"></asp:Label>
                                         <asp:LinkButton  ID="lblDatabaseText"  runat="server"  Text=' <%# Eval("DatabaseType")%>' CommandName="dbtype"></asp:LinkButton>
                                        <asp:HiddenField ID="hdnDatabaseType" runat="server" Value='<%# Eval("DatabaseType")%>' Visible="false" />
                                        <span class="badge">
                                            <%# Eval("DBServerTypeCount")%>
                                        </span>
                                        <div class="clearfix">
                                        </div>
                                    </li>
                                </ItemTemplate>
                            </asp:Repeater>
                        </ul>
                     
                    </div>
                    <div class="tab-pane" id="tab-repctype">
                      
                        <ul id="ulrepctype">
                            <asp:Repeater ID="rptRepDBServerCount" runat="server" OnItemDataBound="rptRepDBServerCount_ItemDataBound" OnItemCommand="rptRepDBServerCount_OnItemCommand">
                                <ItemTemplate>
                                    <li>
                                        <asp:Label ID="lblIcon" runat="server"></asp:Label>
                                          <asp:LinkButton  ID="lblrepltype"  runat="server"  ToolTip='<%# Eval("Type")%>'  Text='<%# Eval("Type")%>' CommandName="repltype"></asp:LinkButton>
                                      <asp:HiddenField ID="hdnreplType" runat="server" Value='<%# Eval("Type")%>' Visible="false" />
                                        <span class="badge">
                                            <%# Eval("RepliSrvTypeCount")%>
                                        </span>
                                        <div class="clearfix">
                                        </div>
                                    </li>
                                </ItemTemplate>
                            </asp:Repeater>
                        </ul>
                        
                    </div>
                   
                </div>
            </div>
        </div>
    </div>
</div>
