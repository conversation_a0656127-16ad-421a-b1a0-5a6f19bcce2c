﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    public class RegionDetails : BaseEntity
    {
        #region Properties

        [DataMember]
        public string RegionName { get; set; }

        [DataMember]
        public string RegionIdentifier { get; set; }

        [DataMember]
        public string RegionLocation { get; set; }

        [DataMember]
        public string RegionKey { get; set; }

        [DataMember]
        public string RealmKey { get; set; }

        [DataMember]
        public string AvailabilityDomains { get; set; }

        [DataMember]
        public string IdentityHostEndPoint { get; set; }

        [DataMember]
        public string CoreServicesAPIEndPoint { get; set; }


        [DataMember]
        public string DNSServiceAPIEndPoint { get; set; }

        [DataMember]
        public string DatabaseHostEndPoint { get; set; }
        #endregion
    }
}
