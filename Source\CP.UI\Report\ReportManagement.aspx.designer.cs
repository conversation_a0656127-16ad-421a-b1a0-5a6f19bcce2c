﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace CP.UI {
    
    
    public partial class ReportManagement {
        
        /// <summary>
        /// UpdatePanel1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.UpdatePanel UpdatePanel1;
        
        /// <summary>
        /// btnSchReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button btnSchReport;
        
        /// <summary>
        /// ddlReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList ddlReport;
        
        /// <summary>
        /// ucDataLagReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.DataLagReport ucDataLagReport;
        
        /// <summary>
        /// ucGroupConfiguration control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.GroupConfigurationReport ucGroupConfiguration;
        
        /// <summary>
        /// ucGlobalMirrorReplication control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.GlobalMirrorReplicationReport ucGlobalMirrorReplication;
        
        /// <summary>
        /// ucMediationGroup control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.GroupSummaryReport ucMediationGroup;
        
        /// <summary>
        /// ucApplicationGroup control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.ApplicationGroupReport ucApplicationGroup;
        
        /// <summary>
        /// ucGlobalMirrorCGFormation control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.GlobalMirrorCGFormation ucGlobalMirrorCGFormation;
        
        /// <summary>
        /// ucDatalagReport24Hrs control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatalagReport24Hrs ucDatalagReport24Hrs;
        
        /// <summary>
        /// ucNewDataLag24Hours control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.NewDataLag24Hours ucNewDataLag24Hours;
        
        /// <summary>
        /// ucStatus24 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.Status24HrsReport ucStatus24;
        
        /// <summary>
        /// ucApplicationDiscovery control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.ApplicationDiscovery ucApplicationDiscovery;
        
        /// <summary>
        /// ucDatalagstatus control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatalagStatus24HrsReport ucDatalagstatus;
        
        /// <summary>
        /// ucEmailReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.EmailReportlist ucEmailReport;
        
        /// <summary>
        /// ucSMSReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.SMSReportlist ucSMSReport;
        
        /// <summary>
        /// ucParallelDROperationReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.ParallelDrOperationReport ucParallelDROperationReport;
        
        /// <summary>
        /// ucDRDrillExcelReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DRDrillReportExcel ucDRDrillExcelReport;
        
        /// <summary>
        /// ucApplicationdependancyReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.ApplicationdependancyReport ucApplicationdependancyReport;
        
        /// <summary>
        /// FastcopyMonitorReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.FastcopyMonitorReport FastcopyMonitorReport;
        
        /// <summary>
        /// ucDRHealthReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DRHealthReport ucDRHealthReport;
        
        /// <summary>
        /// UserActivityDetailReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.UserActivityDetail UserActivityDetailReport;
        
        /// <summary>
        /// AuditReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.AuditReport AuditReport;
        
        /// <summary>
        /// SLAReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.SLAReport SLAReport;
        
        /// <summary>
        /// MontlySummaryReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.MontlySummaryReport MontlySummaryReport;
        
        /// <summary>
        /// ucVMCenterReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.VMCenterReport ucVMCenterReport;
        
        /// <summary>
        /// ucMultiserverprofilemoniReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.MultiserverprofilemoniReport ucMultiserverprofilemoniReport;
        
        /// <summary>
        /// ucImportCMDBReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.ImportCMDBReport ucImportCMDBReport;
        
        /// <summary>
        /// ucDRReddynesslog control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DRReddynesslog ucDRReddynesslog;
        
        /// <summary>
        /// ucDRReaddynesssummary control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DRReaddynesssummary ucDRReaddynesssummary;
        
        /// <summary>
        /// ucRPMonitor control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.RPMonitoringReport ucRPMonitor;
        
        /// <summary>
        /// ucGGReplicationReportHourly control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.GGReplicationReportHourly ucGGReplicationReportHourly;
        
        /// <summary>
        /// ucDRReadyRPT control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DRReadyRPT ucDRReadyRPT;
        
        /// <summary>
        /// RTOReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.RTOReport RTOReport;
        
        /// <summary>
        /// ucRPOSLADeviationReports control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.RPOSLADeviationReport ucRPOSLADeviationReports;
        
        /// <summary>
        /// AlertReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.AlertReport AlertReport;
        
        /// <summary>
        /// unLicenseUtilizationRPT control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.LicenseUtilizationRPT unLicenseUtilizationRPT;
        
        /// <summary>

        /// unPPDMRestoreExecution control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.PPDMReport unPPDMRestoreExecution;
        
        /// <summary>

        /// unCMDBCompareReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.CMDBCompareReport unCMDBCompareReport;
        
        /// <summary>

        /// lblProfileResult control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label lblProfileResult;
        
        /// <summary>
        /// panelCustom control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelCustom;
        
        /// <summary>
        /// Updatepanel3 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.UpdatePanel Updatepanel3;
        
        /// <summary>
        /// lnkbtnClose control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.LinkButton lnkbtnClose;
        
        /// <summary>
        /// UpdatePanel2 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.UpdatePanel UpdatePanel2;
        
        /// <summary>
        /// iframe1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlIframe iframe1;
        
        /// <summary>
        /// HiddenForModal control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button HiddenForModal;
        
        /// <summary>
        /// ModalPopupExtenderCustom control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::AjaxControlToolkit.ModalPopupExtender ModalPopupExtenderCustom;
    }
}
