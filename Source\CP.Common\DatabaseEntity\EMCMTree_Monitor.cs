﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "EMCMTree_Monitor", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class EMCMTree_Monitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraobjectId { get; set; }
                
        [DataMember]
        public string PRDataDomainServerIPAddress { get; set; }


        [DataMember]
        public string DRDataDomainServerIPAddress { get; set; }


        [DataMember]
        public string DataLag { get; set; }

        [DataMember]
        public string PRMtreeReplicationpath { get; set; }

        [DataMember]
        public string DRMtreeReplicationpath { get; set; }

        [DataMember]
        public string PRMtreeReplicationMode { get; set; }

        [DataMember]
        public string DRMtreeReplicationMode { get; set; }

        [DataMember]
        public string PRMtreePathQuotaHardSoftLimit { get; set; }

        [DataMember]
        public string DRMtreePathQuotaHardSoftLimit { get; set; }

        [DataMember]
        public string PRMTreeReplicationEnabledDisabled { get; set; }

        [DataMember]
        public string DRMTreeReplicationEnabledDisabled { get; set; }

        [DataMember]
        public string PRMTreeReplicationStatus { get; set; }

        [DataMember]
        public string DRMTreeReplicationStatus { get; set; }

        [DataMember]
        public string PRMTreeReplicationErrorStatus { get; set; }

        [DataMember]
        public string DRMTreeReplicationErrorStatus { get; set; }

        [DataMember]
        public string PRPrecompressedremaining { get; set; }

        [DataMember]
        public string DRPrecompressedremaining { get; set; }

        [DataMember]
        public string SyncedasofTime { get; set; }
        #endregion
    }

}
