using System;
using CP.Helper;

namespace CP.Common.Shared
{
    [Serializable]
    public enum CustomizedCacheDependencyType
    {
        [EnumDescription("Not Dependent")]
        Independent = 1,

        [EnumDescription("Cache Dependent")]
        CacheDependent = 2,

        [EnumDescription("External File Dependent")]
        ExternalFileDependent = 3,

        [EnumDescription("Database Table Dependent")]
        DatabaseDependent = 4
    }

    [Serializable]
    public enum AlertModeType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Email")]
        Email = 1,

        [EnumDescription("SMS")]
        SMS = 2,

        [EnumDescription("Email and SMS")]
        EmailandSMS = 3
    }


    [Serializable]
    public enum SingleSignOnType
    {
        [EnumDescription("TPAM")]
        TPAM = 1,
        [EnumDescription("CyberArk")]
        CyberArk = 2,
        [EnumDescription("Arcos")]
        Arcos = 3
    }

    [Serializable]
    public enum TransactionType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Add")]
        Add = 1,

        [EnumDescription("Update")]
        Update = 2,

        [EnumDescription("Delete")]
        Delete = 3,

        [EnumDescription("Save")]
        Save = 4,

        [EnumDescription("Execute")]
        Execute = 5,

        [EnumDescription("InActive")]
        InActive = 6,

        [EnumDescription("Deactive")]
        Deactive = 7,

        [EnumDescription("Active")]
        Active = 8,

        [EnumDescription("LoggedIn")]
        LoggedIn = 9,

        [EnumDescription("LoggedOut")]
        LoggedOut = 10,

        [EnumDescription("PasswordChanged")]
        PasswordChanged = 11,

        [EnumDescription("GenerateReport")]
        GenerateReport = 12,

        [EnumDescription("Maintenance")]
        Maintenance = 13,

        [EnumDescription("Lock")]
        Lock = 14,

        [EnumDescription("Active All")]
        ActiveAll = 15,

        [EnumDescription("Maintenance All")]
        MaintenanceAll = 16,

        [EnumDescription("Exists")]
        Exists = 17,

    }

    [Serializable]
    public enum ArchiveType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Week")]
        Week = 1,

        [EnumDescription("Month")]
        Month = 2,

        [EnumDescription("ThreeMonths")]
        ThreeMonths = 3,

        [EnumDescription("SixMonths")]
        SixMonths = 4,

        [EnumDescription("Year")]
        Year = 5
    }

    [Serializable]
    public enum InfraObjectActivityType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Active")]
        Active = 1,

        [EnumDescription("Maintenance")]
        Maintenance = 2,

        [EnumDescription("Lock")]
        Lock = 3
    }

    [Serializable]
    public enum UserRole
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("SuperAdmin")]
        SuperAdmin = 1,

        [EnumDescription("Administrator")]
        Administrator = 2,

        [EnumDescription("Operator")]
        Operator = 3,

        [EnumDescription("Manager")]
        Manager = 4,

        [EnumDescription("Hidden")]
        Hidden = 5,

        [EnumDescription("SuperMonitor")]
        SuperMonitor = 6,

        [EnumDescription("Custom")]
        Custom = 7
    }

    [Serializable]
    public enum SiteMapCategory
    {
        [EnumDescription("SITE MAP ROOT")]
        Root = 1,

        [EnumDescription("ADMIN MENU")]
        AdminMenu = 2,

        [EnumDescription("CONFIGURE MENU")]
        ConfigureMenu = 3,

        [EnumDescription("VIEW MENU")]
        ViewMenu = 4,

        [EnumDescription("GROUP MENU")]
        GroupMenu = 5,

        [EnumDescription("ALERT MENU")]
        AlertMenu = 6,

        [EnumDescription("WORKFLOW MENU")]
        WorkflowMenu = 7,

        [EnumDescription("REPORTS MENU")]
        ReportsMenu = 8,

        [EnumDescription("NETWORK MENU")]
        NetworkMenu = 9
    }

    [Serializable]
    public enum MessageType
    {
        [EnumDescription("Success")]
        Success,

        [EnumDescription("Info")]
        Primary,

        [EnumDescription("Warning")]
        Warning,

        [EnumDescription("Error")]
        Danger
    }

    public enum DataGuardMode
    {
        [EnumDescription("NA")]
        Undefined = 0,

        [EnumDescription("Sync")]
        Sync = 1,

        [EnumDescription("Async")]
        Async = 2,

        [EnumDescription("PARALLELSYNC")]
        PARALLELSYNC = 3
    }

    public enum FastCopyMode
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("DataSync")]
        DataSync = 1,

        [EnumDescription("VMWareDataSync")]
        VMWareDataSync = 2,

        [EnumDescription("OracleWithDataSync")]
        OracleWithDataSync = 3,

        [EnumDescription("DB2DataSync")]
        DB2DataSync = 4,

        [EnumDescription("SyBaseWithDataSync")]
        SyBaseWithDataSync = 5,

        [EnumDescription("MaxDBWithDataSync")]
        MaxDBWithDataSync = 6,

        [EnumDescription("MSSQLWithDataSync")]
        MSSQLWithDataSync = 7,

        [EnumDescription("OracleWithRSync")]
        OracleWithRSync = 8


    }

    public enum InfraObjectType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Application")]
        Application = 1,

        [EnumDescription("DB")]
        DB = 2,

        [EnumDescription("Virtual")]
        Virtual = 3,

        [EnumDescription("Database - Native Replication")]
        DatabaseNativeReplication = 4,

        [EnumDescription("Database - Storage Replication (Full DB)")]
        DatabaseStorageReplicationFullDB = 5,

        [EnumDescription("Database - Storage Replication (Logshipping)")]
        DatabaseStorageReplicationLogshipping = 6,

        [EnumDescription("Database - DataSync Replication")]
        DataBaseDataSyncReplication = 7,

        [EnumDescription("Application - Storage Replication")]
        ApplicationStorageReplication = 8,

        [EnumDescription("Application - DataSync Replication")]
        ApplicationDataSyncReplication = 9,

        [EnumDescription("VMware - Native Replication")]
        VMwareNativeReplication = 10,

        [EnumDescription("VMware - Storage Replication")]
        VMwareStorageReplication = 11,

        [EnumDescription("VMware - DataSync Replication")]
        VMwareDataSyncReplication = 12,

        [EnumDescription("Application - Custom Replication")]
        ApplicationCustomReplication = 13,

        [EnumDescription("Application - Native Replication")]
        ApplicationNativeReplication = 14,

        [EnumDescription("Application - No Replication")]
        ApplicationNoReplication = 15,

        [EnumDescription("Database - Third Party Replication")]
        DatabaseThirdPartyReplication = 16,

        [EnumDescription("Application - Third Party Replication")]
        ApplicationThirdPartyReplication = 17,

        [EnumDescription("Hyper-V-Native Replication")]
        HyperVNativeReplication = 18,

        [EnumDescription("Sybase-SRS")]
        SybaseSRS = 19,

        [EnumDescription("ESXI-HP3Par-Replication")]
        ESXIHP3ParReplication = 20,

        [EnumDescription("eBDR Replication-Perpetuuiti")]
        VMwareeBDRcReplication = 21,

        [EnumDescription("Database - RSync Replication")]
        DataBaseRSyncReplication = 22,

        [EnumDescription("Nutanix - Leap Replication")]
        NutanixLeapReplication = 23,

        [EnumDescription("Nutanix - Protection Domain Replication")]
        NutanixNativeReplication = 24,

        [EnumDescription("MariaDB Galera Cluster")]
        MariaDBGaleraCluster = 25,

        [EnumDescription("RPForVMReplication")]
        RPForVMReplication = 26,

        [EnumDescription("REDHAT_VirtulizationReplication")]
        REDHAT_VirtulizationReplication = 27,

        [EnumDescription("OpenShiftReplication")]
        OpenShiftReplication = 28,
    }

    public enum SubInfraObjectType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,
    }

    [Serializable]
    public enum GroupServerType
    {
        [EnumDescription("PRAppServer")]
        PRAppServer = 0,

        [EnumDescription("DRAppServer")]
        DRAppServer = 1,

        [EnumDescription("PRDBServer")]
        PRDBServer = 2,

        [EnumDescription("DRDBServer")]
        DRDBServer = 3,

        [EnumDescription("DSCLIServer")]
        DSCLIServer = 4,

        [EnumDescription("HMCServer")]
        HMCServer = 5,

        [EnumDescription("PRESXIServer")]
        PRESXIServer = 6,

        [EnumDescription("DRESXIServer")]
        DRESXIServer = 7,

        [EnumDescription("DNSServer")]
        DNSServer = 8,

        [EnumDescription("SymCLIServer")]
        SymCLIServer = 9,

        [EnumDescription("SybRepServer")]
        SybRepServer = 10,

        [EnumDescription("PRNutanixLeapServer")]
        PRNutanixLeapServer = 11,

        [EnumDescription("DRNutanixLeapServer")]
        DRNutanixLeapServer = 12,

        [EnumDescription("PRNutanixServer")]
        PRNutanixServer = 13,

        [EnumDescription("DRNutanixServer")]
        DRNutanixServer = 14,

        [EnumDescription("PRVsphereServer")]
        PRVsphereServer = 15,

        [EnumDescription("DRVsphereServer")]
        DRVsphereServer = 16,

        [EnumDescription("PROCIDGSERVER")]
        PROCIDGSERVER = 17,


        [EnumDescription("DROCIDGSERVER")]
        DROCIDGSERVER = 18,

        [EnumDescription("OPENSHIFTCLUSTERSERVER")]
        OPENSHIFTCLUSTERSERVER = 19,

        [EnumDescription("OPENSHIFTAPISERVER")]
        OPENSHIFTAPISERVER = 20
    }

    public enum AlertNotificationType
    {
        DataLag = 1,
        CGFormed = 2,
        NormalReplication = 3,
        PRDBCheck = 4,
        ApplyLogs = 5,
        PRServer = 6,
        ArchiveGap = 7,
        RecoveryMode = 8,
        PRDBListenerCheck = 9,
        DRServer = 10,
        DRDBCheck = 11,
        DRDBListenerCheck = 12
    }

    public enum HeatmapType
    {
        Server = 1,
        Database = 2,
        Os = 3,
        Storage = 4,
        Network = 5,
        Replication = 6,
        DBNode = 7
    }

    public enum ImpactType
    {
        ServerNotReachable = 1,
        ServerAuthenticationFailed = 2,
        DatabaseDown = 3,
        DatabaseListenerDown = 4,
        DatalagExceededConfiguredRPO = 5,
        ArchiveGapFound = 6,
        DataGuardNotInManagedState = 7
    }

    public enum AlertCategory
    {
        Group = 1,
        GlobalMirror = 2,
        RacGroup = 3,
        Application = 4
    }

    [Serializable]
    public enum UserActionType
    {
        [EnumDescription("Unknown")]
        Unknown = 0,

        //Login code 001
        [EnumDescription("Login User")]
        LoginUser = 001001,

        [EnumDescription("Logout User")]
        LogoutUser = 001002,

        //User code 002

        [EnumDescription("Delete User Account")]
        DeleteUserAccount = 002001,

        [EnumDescription("Create User Account")]
        CreateUserAccount = 002002,

        [EnumDescription("Update User Account")]
        UpdateUserAccount = 002003,

        [EnumDescription("Reset User Password")]
        ResetUserPassword = 002004,

        [EnumDescription("Change User Password")]
        ChangeUserPassword = 002005,

        //Site code 003

        [EnumDescription("Delete Site")]
        DeleteSite = 003001,

        [EnumDescription("Create Site")]
        CreateSite = 003002,

        [EnumDescription("Update Site")]
        UpdateSite = 003003,

        [EnumDescription("Update VeritasCluster")]
        UpdateVeritasCluster = 003004,

        //Company Profile code 004

        [EnumDescription("Delete Company Profile")]
        DeleteCompanyProfile = 004001,

        [EnumDescription("Create  Company Profile")]
        CreateCompanyProfile = 004002,

        [EnumDescription("Update  Company Profile")]
        UpdateCompanyProfile = 004003,

        //Database code 005

        [EnumDescription("Delete Database Component")]
        DeleteDatabaseComponent = 005001,

        [EnumDescription("Create  Database Component")]
        CreateDatabaseComponent = 005002,

        [EnumDescription("Update  Database Component")]
        UpdateDatabaseComponent = 005003,

        //Server code 006

        [EnumDescription("Delete Server Component")]
        DeleteServerComponent = 006001,

        [EnumDescription("Create  Server Component")]
        CreateServerComponent = 006002,

        [EnumDescription("Update  Server Component")]
        UpdateServerComponent = 006003,

        //Replication code 007

        [EnumDescription("Delete Replication Component")]
        DeleteReplicationComponent = 007001,

        [EnumDescription("Create  Replication Component")]
        CreateReplicationComponent = 007002,

        [EnumDescription("Update  Replication Component")]
        UpdateReplicationComponent = 007003,

        [EnumDescription("Discover Luns Information")]
        DiscoverLuns = 007004,

        //Group code 008

        [EnumDescription("Delete Group")]
        DeleteGroup = 008001,

        [EnumDescription("Create  Group")]
        CreateGroup = 008002,

        [EnumDescription("Update Group")]
        UpdateGroup = 008003,

        //Report code 009

        [EnumDescription("Generate ApplicationGroup summary report")]
        GenerateApplicationGroupReport = 009001,

        [EnumDescription("Generate Group summary report")]
        GenerateGroupReport = 009002,

        [EnumDescription("Generate DataLag report")]
        GenerateDataLagReport = 009003,

        [EnumDescription("Generate GlobalMirror replication report")]
        GenerateGMReplicationReport = 009004,

        [EnumDescription("Generate Group configuration report")]
        GenerateGroupConfigurationReport = 009005,

        //Alert code 010

        [EnumDescription("Generate Alerts report")]
        GenerateAlertsReport = 010001,

        //Admin code 011

        [EnumDescription("Active Group")]
        ActiveGroup = 011001,

        [EnumDescription("Active all Group")]
        ActiveAllGroup = 011002,

        [EnumDescription("Maintenance Group")]
        MaintenanceGroup = 011003,

        [EnumDescription("Maintenance all Group")]
        MaintenanceAllGroup = 011004,

        [EnumDescription("Monitor Group")]
        MonitorGroup = 011005,

        [EnumDescription("Generate DataLag report with graph image")]
        GenerateDataLagImageReport = 011006,

        //Workflow code 012

        [EnumDescription("Delete Action properties")]
        DeleteAction = 012001,

        [EnumDescription("Create  Action properties")]
        CreateAction = 012002,

        [EnumDescription("Update Action properties")]
        UpdateAction = 012003,

        [EnumDescription("Delete ActionSet properties")]
        DeleteActionSet = 012004,

        [EnumDescription("Create  ActionSet properties")]
        CreateActionSet = 012005,

        [EnumDescription("Update ActionSet properties")]
        UpdateActionSet = 012006,

        [EnumDescription("Create workflow")]
        CreateWorkflow = 012007,

        [EnumDescription("Update workflow")]
        UpdateWorkflow = 012008,

        [EnumDescription("Attach workflow with Group")]
        AttachWorkflowWithGroup = 012009,

        [EnumDescription("Load workflow")]
        LoadWorkflow = 012010,

        [EnumDescription("Delete workflow")]
        DeleteWorkflow = 012011,

        // Nodes 013
        [EnumDescription("Create Nodes")]
        CreateNodes = 013001,

        [EnumDescription("Update Nodes")]
        UpdateNodes = 013002,

        // Notification 014
        [EnumDescription("Create Notification Manager")]
        CreateNotificationManager = 014001,

        [EnumDescription("Update Notification Manager")]
        UpdateNotificationManager = 014002,

        [EnumDescription("Delete Notification Manager")]
        DeleteNotificationManager = 014003,

        // DNS Look up 015
        [EnumDescription("Create DNS LookUp")]
        CreateDNSLookUp = 015001,

        [EnumDescription("Update DNS LookUp")]
        UpdateDNSLookUp = 015002,

        // Business Process 016

        [EnumDescription("Create BusinessProcess")]
        CreateBusinessProcess = 016001,

        [EnumDescription("Update BusinessProcess")]
        UpdateBusinessProcess = 016002,

        [EnumDescription("Delete BusinessProcess")]
        DeleteBusinessProcess = 016003,

        // FastCopy Job 017

        [EnumDescription("Create FastCopyJob")]
        CreateFastCopyJob = 017001,

        [EnumDescription("Update FastCopyJob")]
        UpdateFastCopyJob = 017002,

        [EnumDescription("Delete FastCopyJob")]
        DeleteFastCopyJob = 017003,

        // Business Service 018

        [EnumDescription("Create Business Services")]
        CreateBusinessService = 018001,

        [EnumDescription("Update Business Services")]
        UpdateBusinessService = 018002,

        [EnumDescription("Delete Business Services")]
        DeleteBusinessService = 018003,

        // Business Function 020

        [EnumDescription("Create Business Function")]
        CreateBusinessFunction = 020001,

        [EnumDescription("Update Application List")]
        UpdateBusinessFunction = 020002,

        [EnumDescription("Delete Application List")]
        DeleteBusinessFunction = 020003,

        // Archive Bak Table 021

        [EnumDescription("Archive List")]
        ArchiveListConfiguration = 021001,

        // Application User Group 022

        [EnumDescription("Create Application User Group")]
        CreateApplicationUserGroup = 022001,

        [EnumDescription("Update Application User Group")]
        UpdateApplicationUserGroup = 022002,

        [EnumDescription("Delete Application User Group")]
        DeleteApplicationUserGroup = 022003,

        // DatabaseBack Management 023

        [EnumDescription("Create DatabaseBackup Management")]
        CreateDatabaseBackupManagement = 023001,

        [EnumDescription("Update DatabaseBackup Management")]
        UpdateDatabaseBackupManagement = 023002,

        [EnumDescription("Delete DatabaseBackup Management")]
        DeleteDatabaseBackupManagement = 023003,

        [EnumDescription("CP Window Service Stop State")]
        WindowServiceStopState = 023003,

        [EnumDescription("BackUp file is not present")]
        DatabaseBackupManagement = 023005,

        // JobManagement 024

        [EnumDescription("Create JobManagement")]
        CreateJobManagement = 024001,

        [EnumDescription("Update JobManagement")]
        UpdateJobManagement = 024002,

        [EnumDescription("Delete JobManagement")]
        DeleteJobManagement = 024003,

        // CustomExceptionManagement 025

        [EnumDescription("Create CustomException Management")]
        CreateCustomExceptionManagement = 025001,

        [EnumDescription("Update CustomException Management")]
        UpdateCustomExceptionManagement = 025002,

        [EnumDescription("Delete CustomException Management")]
        DeleteCustomExceptionManagement = 025003,

        // AlertManager 026

        [EnumDescription("Create Alert Manager")]
        CreateAlertManager = 026001,

        [EnumDescription("Update Alert Manager")]
        UpdateAlertManager = 026002,

        [EnumDescription("Delete Alert Manager")]
        DeleteAlertManager = 026003,

        // ParallelWorkFlow 027

        [EnumDescription("Create ParallelWorkFlow")]
        CreateParallelWorkFlow = 027001,

        [EnumDescription("Update ParallelWorkFlow")]
        UpdateParallelWorkFlow = 027002,

        [EnumDescription("Delete ParallelWorkFlow")]
        DeleteParallelWorkFlow = 027003,

        //Group code 028

        [EnumDescription("Delete InfraObjects")]
        DeleteInfraObjects = 028001,

        [EnumDescription("Create  Group")]
        CreateInfraObjects = 028002,

        [EnumDescription("Update Group")]
        UpdateInfraObjects = 028003,

        //DataSync Properties code 029

        [EnumDescription("Create DataSyncProperties")]
        CreateDataSyncProperties = 029001,

        [EnumDescription("Update DataSyncProperties")]
        UpdateDataSyncProperties = 029002,

        [EnumDescription("Delete DataSyncProperties")]
        DeleteDataSyncProperties = 029003,

        //InfraObjectworkflow code 030
        [EnumDescription("infraObject Attach")]
        AttachInfraObject = 030001,

        [EnumDescription("InfraObject De-Attach")]
        DeAttachInfraObject = 030002,

        // ParallelProfile 031
        [EnumDescription("Create ParallelProfile")]
        CreateParallelProfile = 031001,

        [EnumDescription("Update ParallelProfile")]
        UpdateParallelProfile = 031002,

        [EnumDescription("Delete ParallelProfile")]
        DeleteParallelProfile = 031003,

        // ParallelWorkflowProfile 032
        [EnumDescription("Create ParallelWorkflowProfile")]
        CreateParallelWorkFlowProfile = 032001,

        [EnumDescription("Update ParallelWorkflowProfile")]
        UpdateParallelWorkFlowProfile = 032002,

        [EnumDescription("Delete ParallelWorkflowProfile")]
        DeleteParallelWorkFlowProfile = 032003,

        //BusinessFunctionBIAActivity code 033
        [EnumDescription("Create BusinessFunctionBIAActivity")]
        CreateBusinessFunctionBIAActivity = 033001,
        [EnumDescription("Update BusinessFunctionBIAActivity")]
        UpdateBusinessFunctionBIAActivity = 033002,
        [EnumDescription("Delete BusinessFunctionBIAActivity")]
        DeleteBusinessFunctionBIAActivity = 033003,


        //BusinessFunctionBIADetails code 034
        [EnumDescription("Create BusinessFunctionBIADetails")]
        CreateBusinessFunctionBIADetails = 034001,
        [EnumDescription("Update BusinessFunctionBIADetails")]
        UpdateBusinessFunctionBIADetails = 034002,
        [EnumDescription("Delete BusinessFunctionBIADetails")]
        DeleteBusinessFunctionBIADetails = 034003,

        //BusinessFunctionBIARelation code 035
        [EnumDescription("Create BusinessFunctionBIARelation")]
        CreateBusinessFunctionBIARelation = 035001,
        [EnumDescription("Update BusinessFunctionBIARelation")]
        UpdateBusinessFunctionBIARelation = 035002,
        [EnumDescription("Delete BusinessFunctionBIARelation")]
        DeleteBusinessFunctionBIARelation = 035003,

        //BusinessFunctionBIA code 036
        [EnumDescription("Create BusinessFunctionBIA")]
        CreateBusinessFunctionBIA = 036001,
        [EnumDescription("Update BusinessFunctionBIA")]
        UpdateBusinessFunctionBIA = 036002,
        [EnumDescription("Delete BusinessFunctionBIA")]
        DeleteBusinessFunctionBIA = 036003,
        [EnumDescription("Delete ImpactTypeMaster")]
        DeleteImpactTypeMaster = 036004,
        [EnumDescription("Delete Impact Master")]
        DeleteImpactMaster = 036005,
        [EnumDescription("Update User Data")]
        UpdateUserData = 036006,
        [EnumDescription("Create Monitor Profile")]
        CreateMonitorProfile = 37001,
        [EnumDescription("Update Monitor Profile")]
        UpdateMonitorProfile = 37002,
        [EnumDescription("Delete Monitor Profile")]
        DeleteMonitorProfile = 37003,
        [EnumDescription("Delete Monitor Profile Details")]
        DeleteMonitorProfileServer = 37004,
        [EnumDescription("Update Monitor Profile Details")]
        UpdateMonitorProfileDetails = 37005,
        [EnumDescription("Import CMDB Data")]
        ImportCMDBData = 38001,
        [EnumDescription("Create Discovery Configuration")]
        CreateDiscoveryConfiguration = 38002,
        [EnumDescription("Create EventManagement Configuration")]
        CreateEventManagement = 38003,
        [EnumDescription("SNMPDiscovery")]
        SNMPDiscovery = 38004,
        [EnumDescription("NetworkDiscovery")]
        NetworkDiscovery = 38005,
        [EnumDescription("ImportCMDB")]
        ImportCMDB = 38006,
        [EnumDescription("Database List")]
        DatabaseList = 38007,
        [EnumDescription("Replication List")]
        ReplicationList = 38008,
        [EnumDescription("CreateSingleSignOn")]
        CreateSingleSignOn = 0360089,
        [EnumDescription("User Click on Menu")]
        UserClickonMenu = 38009,

        [EnumDescription("UpdateSingleSignOn")]
        UpdateSingleSignOn = 0360090,

        [EnumDescription("DeleteSingleSignOn")]
        DeleteSingleSignOn = 0360091,

        [EnumDescription("Create ADM Setting")]
        CreateADMSetting = 0370090,

        [EnumDescription("Create GridConfiguration")]
        CreateGridConfiguration = 0380090,

        [EnumDescription("Bulk Credential Change")]
        BulkCredentialChange = 0390090,

        [EnumDescription("View Application Discovery")]
        ViewApplicationDiscovery = 0400090,

        [EnumDescription("Create Monitoring Services")]
        CreateMonitoringServices = 0410090,

        [EnumDescription("Create Admin Setting")]
        CreateAdminSetting = 0420090,

        [EnumDescription("Create Default Monitoring Services")]
        CreateDefaultMonitoringServices = 0430090,

        [EnumDescription("Edit Default Monitoring Services")]
        EditDefaultMonitoringServices = 0430091,

        [EnumDescription("Delete Default Monitoring Services")]
        DeleteDefaultMonitoringServices = 0430092,

        [EnumDescription("View Application Dependency Mapping")]
        ViewApplicationDependencyMapping = 0440090,

        [EnumDescription("View Application Dependency")]
        ViewApplicationDependency = 0450090,

        [EnumDescription("Create Report Scheduler")]
        CreateReportScheduler = 0460090,

        [EnumDescription("Delete Report Scheduler")]
        DeleteReportScheduler = 0460090,

        [EnumDescription("Update Report Scheduler")]
        UpdateReportScheduler = 0460090,

        [EnumDescription("Create HACMPCluster")]
        CreateHACMPCluster = 039001,

        [EnumDescription("Update HACMPCluster")]
        UpdateHACMPCluster = 039002,

        [EnumDescription("Delete HACMPCluster")]
        DeleteHACMPCluster = 039003,

        [EnumDescription("Added Workflow In MonitoringService")]
        MonitorServiceAdd = 039004,

        [EnumDescription("Updated Workflow In MonitoringService")]
        MonitorServiceUpdate = 039005,

        [EnumDescription("Deleted Workflow In MonitoringService")]
        MonitorServiceDelete = 039006,

        [EnumDescription("Add Workflow In InfraObjectScheduledWorkflow")]
        InfraObjectScheduledWorkflowAdd = 039007,

        [EnumDescription("Updating Workflow In InfraObjectScheduledWorkflow")]
        InfraObjectScheduledWorkflowUpdate = 039008,

        [EnumDescription("Deleting Workflow In InfraObjectScheduledWorkflow")]
        InfraObjectScheduledWorkflowDelete = 039009,

        [EnumDescription("Save workflow")]
        SaveWorkflow = 039010,

        [EnumDescription("BulkCredentialDatabase")]
        BulkCredentialDatabase = 039011,

    }

    [Serializable]
    public enum ServerType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("PRServer")]
        PRServer = 1,

        [EnumDescription("DRServer")]
        DRServer = 2,

        [EnumDescription("NextDRServer")]
        NextDRServer = 3
    }

    [Serializable]
    public enum ReplicationType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Storage - IBM GlobalMirror")]
        IBMGlobalMirror = 1,

        [EnumDescription("Native Replication - Oracle-DataGuard")]
        OracleDataGuard = 2,

        [EnumDescription("Perpetuuiti - DataSync - App")]
        DataSync = 3,

        [EnumDescription("MSSQLServerNative")]
        MSSQLServerNative = 4,

        [EnumDescription("Database - MSExchange - Scr ")]
        MSSCR = 5,

        [EnumDescription("Storage - NetApp - SnapMirror")]
        NetAppSnapMirror = 6,

        [EnumDescription("Storage - App - Emc Srdf")]
        EMCSRDF = 7,

        [EnumDescription("Hitachi TrueCopy")]
        HITACHITrueCopy = 8,

        [EnumDescription("Storage - Hitachi-Ur")]
        HITACHIUROracleFullDB = 9,

        //[EnumDescription("HITACHI UR")]//HITACHIUR = 9,
        [EnumDescription("Perpetuuiti - DataSync - Vmware")]
        VMWareDataSync = 10,

        [EnumDescription("VMWare with Globalmirror")]
        VMWareGlobalMirror = 11,

        [EnumDescription("VMWare with - NetApp -Snapmirror")]
        VMWareSnapMirror = 12,

        [EnumDescription("Perpetuuiti - DataSync- MSSQL")]
        SqlServerDataSync = 13,

        [EnumDescription("Perpetuuiti - DataSync - Oracle")]
        OracleWithDataSync = 14,

        [EnumDescription("DB2HADR")]
        DB2HADR = 15,

        [EnumDescription("Storage - Oracle Log Shipping - Hitachi UR")]
        HITACHIUROracleLogShipping = 16,

        [EnumDescription("Storage - Oracle Full Db - Emc Srdf")]
        EMCSRDFOracleFullDB = 17,

        [EnumDescription("Storage - Oracle LOG Shipping - Emc Srdf")]
        EMCSRDFOracleLogShipping = 18,

        [EnumDescription("Storage HitachiUR -  Vmware ")]
        VMWareHitachiUR = 19,

        [EnumDescription("MS-Exchange DAG")]
        MSExchangeDAG = 20,

        [EnumDescription("DataBase - DB2Datasync")]
        DB2DataSync = 21,

        [EnumDescription("MySQL-GlobalMirrorFullDB")]
        MySQLGlobalMirrorFullDB = 22,

        [EnumDescription("MySQL-Native")]
        MySQLNative = 23,

        [EnumDescription("DB2HADR9.7")]
        DB2HADR9X = 24,

        [EnumDescription("EnterPrise DB")]
        EnterPriseDB = 25,

        [EnumDescription("Storage - MS-SQL FULL DB - EMC SRDF")]
        EMCSRDFMSSQLFullDB = 26,

        [EnumDescription("Postgres 9.X")]
        Postgres9X = 27,

        [EnumDescription("MS-SQL2kX")]
        SQLNative2008 = 28,

        [EnumDescription("EC2S3DataSync")]
        EC2S3DataSync = 29,

        [EnumDescription("MSSQL - NetAppSnapMirror")]
        MSSqlNetAppSnapMirror = 30,

        [EnumDescription("Storage - Oracle FULL DB - IBM GlobalMirror")]
        OracleFullDBIBMGlobalMirror = 31,

        [EnumDescription("MS-SQL - DoubleTake (Full DB)")]
        MSSQLDoubleTakeFullDB = 32,

        [EnumDescription("Oracle - DoubleTake (Full DB)")]
        OracleDoubleTakeFullDB = 33,

        [EnumDescription("Application - DoubleTake")]
        ApplicationDoubleTake = 34,

        [EnumDescription("Oracle Full DB - NetApp-SnapMirror")]
        OracleFullDBNetAppSnapMirror = 35,

        [EnumDescription("MSSQL-DataBase-Mirror")]
        MSSQLDataBaseMirror = 36,

        [EnumDescription("Storage - Oracle FULL DB - EMC SRDF VMAX")]
        EMCSRDFVMAXOracleFULLDB = 37,

        [EnumDescription("Storage - Oracle FULL DB - EMC SRDF DMX")]
        EMCSRDFDMXOracleFULLDB = 38,

        [EnumDescription("Storage - MSSQL FULL DB - EMC SRDF VMAX")]
        EMCSRDFVMAXMSSQLFULLDB = 39,

        [EnumDescription("Storage - MSSQL FULL DB - EMC SRDF DMX")]
        EMCSRDFDMXMSSQLFULLDB = 40,

        [EnumDescription("Perpetuuiti - DataSync - SyBase")]
        SyBaseWithDataSync = 41,

        [EnumDescription("MIMIX")]
        MIMIX = 42,

        [EnumDescription("Oracle FullDB SVC-GlobalMirror")]
        OracleFullDBSVCGlobalMirror = 43,

        [EnumDescription("SVC - GlobalMirror OR Metro Mirror")]
        SVCGlobalMirrorORMetroMirror = 44,

        [EnumDescription("Hitachi-OracleFullDB-Rac")]
        HitachiOracleFullDBRac = 45,

        [EnumDescription("EmcSrdf-OracleRac-FullDB")]
        EMCSRDFOracleRacFullDB = 46,

        [EnumDescription("EmcSrdf-SyBase")]
        EMCSRDFSyBase = 47,

        [EnumDescription("Hitachi-Syabse")]
        HitachiSyabse = 48,

        [EnumDescription("Hyper-V")]
        HyperV = 49,

        [EnumDescription("Storage - App - HitachiUr")]
        AppHitachiUr = 50,

        [EnumDescription("MySql - Native Log Shipping")]
        MySqlNativeLogShipping = 51,

        [EnumDescription("MSSQL-DB Mirroring")]
        MSSQLDBMirroring = 52,

        [EnumDescription("SVC-MSSQL FullDB")]
        SVCMSSQLFullDB = 53,

        [EnumDescription("DB2-IBMGLOBALMIRROR")]
        DB2IBMGLobalmirror = 54,

        [EnumDescription("DB2-NetApp SnapMirror")]
        DB2NetAppSnapMirror = 117,

        [EnumDescription("VMWare with SVC")]
        VMWareWithSVC = 55,

        [EnumDescription("Storage - MSSQLFullDB - HitachiUR")]
        HitachiURMSSQLFullDB = 56,

        [EnumDescription("Storage - MSSQLFullDB - GlobalMirror")]
        GlobalMirrorMSSQLFullDB = 57,

        [EnumDescription("DB2IBM-XIV MIRROR")]
        DB2IBMXIVMIRROR = 58,

        [EnumDescription("Application-XIV MIRROR")]
        ApplicationXIVMIRROR = 59,

        [EnumDescription("Storage - NetApp SnapMirror FullDB - Postgres9.X")]
        NetAppSnapMirrorPostgresFullDB = 60,

        [EnumDescription("SVCGlobalMirror-OracleFullDB-Rac")]
        SVCGlobalMirrorOracleFullDBRac = 61,

        [EnumDescription("SRM-VMware")]
        SRMVMware = 62,

        [EnumDescription("EmcSrdf-Mysql-FullDB")]
        EMCSRDFMysqlFullDB = 63,

        [EnumDescription("Perpetuuiti - DataSync - MaxDB")]
        MaxDBWithDataSync = 64,

        [EnumDescription("Sybase-SRS")]
        SybaseWithSRS = 65,

        [EnumDescription("Application -eBDR")]
        ApplicationeBDR = 66,

        [EnumDescription("DR-NET")]
        DRNET = 67,

        [EnumDescription("TPCR")]
        TPCR = 68,

        [EnumDescription("MSSQL - AlwaysOn")]
        MSSQLAlwaysOn = 69,

        [EnumDescription("Storage - App - Recover Point")]
        RecoverPoint = 70,

        [EnumDescription("Storage - Oracle FULL DB - Recover Point")]
        RecoverPointOracleFULLDB = 71,

        [EnumDescription("Storage - MSSQL FULL DB - Recover Point")]
        RecoverPointMSSQLFULLDB = 72,

        [EnumDescription("Storage - MYSQL FULL DB - Recover Point")]
        RecoverPointMYSQLFULLDB = 73,

        [EnumDescription("Storage - MSSQL FullDB - HP3PAR")]
        HP3PARMSSQLFULLDB = 74,

        [EnumDescription("HP3Par with Application")]
        HP3ParwithApplication = 75,

        [EnumDescription("HP3Par with Postgress FullDB")]
        HP3ParwithPostgressMSSQL = 76,

        //[EnumDescription("HP3Par with ESXI")]
        //HP3ParwithESXI = 84,

        //[EnumDescription("MSSQL FullDB-VVR Replication")]
        //MSSQLFullDBVVRReplication = 83,

        [EnumDescription("Storage - Oracle FULL DB - EMCSRDF - SG")]
        EMCSRDFSGORACLEFULLDB = 77,

        [EnumDescription("Storage - Oracle FULL DB - EMCSRDF - Star")]
        EMCSRDFSTARORACLEFULLDB = 78,

        [EnumDescription("Storage - App - EMCSRDF - SG")]
        EMCSRDFSGAPPLICATION = 79,

        [EnumDescription("Storage - App - EMCSRDF - Star")]
        EMCSRDFSTARAPPLICATION = 80,

        [EnumDescription("Storage - MSSQL - FullDB - EMCSRDF - SG")]
        EMCSRDFMSQLFULLDBSG = 81,

        [EnumDescription("Storage - MSSQL - FullDB - EMCSRDF - Star")]
        EMCSRDFMSQLFULLDBSTAR = 82,

        [EnumDescription("MSSQL FullDB-VVR Replication")]
        MSSQLFullDBVVRReplication = 83,

        [EnumDescription("HP3Par with ESXI")]
        HP3ParwithESXI = 84,


        [EnumDescription("Storage - Oracle FULL DB - ZFS")]
        ZFSOracleFULLDB = 85,

        [EnumDescription("Storage - App - ZFS")]
        ZFSApplication = 86,

        [EnumDescription("Storage - DB2 - ZFS")]
        ZFSWithDB2 = 87,

        [EnumDescription("Storage - Max FULL DB - ZFS")]
        ZFSMaxFULLDB = 88,

        [EnumDescription("EMC ISILON SYNCIQ Multi-Site")]
        EMCISilon = 89,

        [EnumDescription("EmcMirrorView Oracle FullDB")]
        EmcMirrorViewOracleFullDB = 90,

        [EnumDescription("EmcMirrorView Sybase FullDB")]
        EmcMirrorViewSybaseFullDB = 91,

        [EnumDescription("EmcMirrorView Application")]
        EmcMirrorViewApp = 92,

        [EnumDescription("RoboCopy")]
        RoboCopy = 93,

        [EnumDescription("Sybase-RS-HADR")]
        SybaseWithRSHADR = 94,

        [EnumDescription("Emc Unity(Unisphere) Application")]
        EmcUnityApp = 95,

        [EnumDescription("DB2 FullDB SVC")]
        DB2FullDBSVC = 96,

        [EnumDescription("Postgress FullDB SVC ")]
        PostgressFullDBSVC = 97,

        [EnumDescription("HP3Par with MongoFullDB")]
        HP3ParwithMongoFullDB = 98,

        [EnumDescription("EMCSRDF-DB2FullDB")]
        EMCSRDFDB2FullDB = 99,

        [EnumDescription("DB2 FullDB-EMC-Recovery Point")]
        DB2FullDBEMCRecoveryPoint = 100,

        [EnumDescription("EMC-STAR-DB2FullDB")]
        EMCSTARDB2FullDB = 101,

        [EnumDescription("EMC-STAR-MYSQLFullDB")]
        EMCSTARMYSQLFullDB = 102,

        [EnumDescription("Hitachi-Ur-DB2FullDB")]
        HitachiUrDB2FullDB = 103,

        [EnumDescription("Hitachi-Ur-MySqlFullDB")]
        HitachiUrMySqlFullDB = 104,

        [EnumDescription("SAP HANADB Replication")]
        SAPHANADBReplication = 105,

        [EnumDescription("eBDR Replication-Perpetuuiti")]
        VirtualeBDR = 106,

        [EnumDescription("Golden Gate Replication")]
        GoldenGateRepli = 107,

        [EnumDescription("RSync")]
        RSync = 108,

        [EnumDescription("Storage - Oracle FullDB - HP3PAR")]
        HP3PARORACLEFULLDB = 109,

        [EnumDescription("Postgres 10.X")]
        Postgres10 = 110,

        [EnumDescription("Application With No Replication")]
        ApplicationWithNoReplication = 111,

        [EnumDescription("Veeam Replication")]
        VeeamReplication = 112,

        [EnumDescription("VVRApplication Replication")]
        VVRApplicationReplication = 113,

        [EnumDescription("Perpetuuiti - RSync - Oracle")]
        OracleWithRSync = 114,

        [EnumDescription("Native Replication - Cloudant - DB")]
        CloudantDB = 115,

        [EnumDescription("Native Replication - Cloudant - Instance")]
        CloudantInstance = 116,

        [EnumDescription("HP3PAR MAX FULLDB")]
        HP3PARMAXFULLDB = 118,

        [EnumDescription("Storage - Huawei Application")]
        HuaweiApplication = 119,

        [EnumDescription("Storage - Postgress FullDB - Huawei")]
        // HuaweiPostgressFullDb = 121,
        HuaweiWithPostgresClusterFullDB = 121,

        [EnumDescription("Nutanix Leap Replication")]
        NutanixLeapReplication = 122,

        [EnumDescription("Mysql-FullDB-SVC")]
        MysqlFullDBSVC = 123,

        [EnumDescription("Application Solution - SVC")]
        ApplicationSolutionSVC = 124,

        //[EnumDescription("Storage - MYSQL FULL DB - EMCSRDF - SG")]
        //EMCSRDFSGMYSQLFULLDB = 124,

        [EnumDescription("Storage - DB2 - EMCSRDF - SG")]
        EMCSRDFSGDB2 = 125,

        [EnumDescription("MySQL - NetApp - SnapMirror")]
        MySQLNetAppSnapMirror = 126,

        [EnumDescription("Application - ActiveDirectory")]
        ActiveDirectory = 127,

        //[EnumDescription("Storage - MSSQL FullDB - Huawei")]
        //OceanstorMSSqlHuaweiDBReplication = 128,

        [EnumDescription("Oceanstor - MSSQL FullDB - Huawei")]
        OceanstorMSSqlHuaweiDBReplication = 128,

        [EnumDescription("Nutanix Protection Domain Replication")]
        NutanixProtectionDomainReplication = 129,

        [EnumDescription("MariaDB Galera Cluster")]
        MariaDBGaleraCluster = 130,

        [EnumDescription("Azure Site Recovery")]
        RecoveryAzureSite = 131,

        [EnumDescription("Zerto")]
        Zerto = 132,


        [EnumDescription("Storage - App - EMCSRDF - CG")]
        EMCSRDFCGAPPLICATION = 133,

        [EnumDescription("Storage -Oracle FULL DB- EMCSRDF -CG")]
        EMCSRDFCGORACLEFULLDB = 134,

        [EnumDescription("MongoDB")]
        MongoDB = 135,

        [EnumDescription("Vmware Vsphere Replication")]
        VmwareVsphereRepli = 136,

        [EnumDescription("Azure Application Gateway")]
        AzureGatewayReplication = 137,

        [EnumDescription("RPForVM Replication")]
        RPForVMReplication = 138,

        [EnumDescription("Oracle Cloud Replication")]
        OracleCloudReplication = 139,

        [EnumDescription("EMC Data Domain MTree")]
        EMCDataDomainMTree = 140,

        [EnumDescription("AvamarReplication")]
        AvamarReplication = 141,

        [EnumDescription("Storage - Oracle FULL DB - Zerto")]
        ZertoOracleFULLDB = 142,

        [EnumDescription("Storage - MSSQL FULLDB - Zerto")]
        ZertoMssqlFULLDB = 143,

        [EnumDescription("RPForVM With MSSQL FullDB")]
        RPForVMWithMSSQLFullDB = 144,

        [EnumDescription("RackWare Replication")]
        RackWareReplication = 145,

        [EnumDescription("MySQL Full DB - IBM-GlobalMirror")]
        MySQLFullDBIBMGlobalMirror = 146,

        [EnumDescription("DellEMCCyberRecoverVault Replication")]
        DellEMCCyberRecoverVaultReplication = 147,

        [EnumDescription("DB2-FullDB-MIMIX")]
        DB2FullDBMIMIX = 148,

        [EnumDescription("Oracle Cloud DataGuard")]
        OracleCloudDataGuard = 149,

        [EnumDescription("Azure Kubernetes")]
        AzureKubernetes = 150,

        [EnumDescription("Azure CosmosDB")]
        AzureCosmosDB = 151,

        [EnumDescription("REDHAT_Virtulization")]
        REDHAT_Virtulization = 152,

        [EnumDescription("OpenShift")]
        OpenShift = 153,

        [EnumDescription("vSphere Replication")]
        vSphereReplication = 154,

        [EnumDescription("OC_ExaDB")]
        OC_ExaDB = 155


    }

    [Serializable]
    public enum ReplicationMode
    {
        [EnumDescription("Concurrent")]
        Concurrent = 1,

        [EnumDescription("Cascaded")]
        Cascaded = 2,
    }


    [Serializable]
    public enum SqlAuthenticateType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Windows")]
        Windows = 1,

        [EnumDescription("Sql Server")]
        SqlServer = 2
    }

    [Serializable]
    public enum DatabaseType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Oracle")]
        Oracle = 1,

        [EnumDescription("Sql")]
        Sql = 2,

        [EnumDescription("Exchange")]
        Exchange = 3,

        [EnumDescription("OracleRac")]
        OracleRac = 4,

        [EnumDescription("DB2")]
        DB2 = 5,

        [EnumDescription("Exchange DAG")]
        ExchangeDAG = 6,

        [EnumDescription("MySQL")]
        MySQL = 9,

        [EnumDescription("PostgreSQL")]
        PostgreSQL = 8,

        [EnumDescription("Postgres9x")]
        Postgres9x = 7,

        [EnumDescription("SQLNative2008")]
        SQLNative2008 = 10,

        [EnumDescription("PostgresGreSQL")]
        PostgresGreSQL = 11,

        [EnumDescription("EC2S3DataSync")]
        EC2S3DataSync = 12,

        [EnumDescription("SyBase")]
        SyBase = 13,

        [EnumDescription("MaxDB")]
        MaxDB = 14,

        [EnumDescription("SyBaseWithSrs")]
        SyBaseWithSrs = 15,

        [EnumDescription("MongoDB")]
        MongoDB = 16,

        [EnumDescription("SyBaseWithRsHADR")]
        SyBaseWithRsHADR = 17,

        [EnumDescription("HANADB")]
        HANADB = 18,

        [EnumDescription("CloudantDB")]
        CloudantDB = 19,

        [EnumDescription("MSSQLFullDB")]
        MSSQLFullDB = 20,

        [EnumDescription("MariaDB")]
        MariaDB = 21,

        [EnumDescription("OracleCloudDataGuard")]
        OracleCloudDataGuard = 22,

        [EnumDescription("OC_ExaDB")]
        OC_ExaDB = 23,

    }

    //[Serializable]
    //public enum DataLagType : int
    //{
    //    [EnumDescription("Oracle")]
    //    Oracle = 1,
    //    [EnumDescription("VmWare")]
    //    VmWare = 2,
    //    [EnumDescription("MSExchange")]
    //    MsExchange = 3,
    //    [EnumDescription("MSSqlServer")]
    //    MSSqlServer = 4,
    //    [EnumDescription("MSSqlNative")]
    //    MSSqlNative = 5
    //}

    [Serializable]
    public enum InfraObjectState
    {
        [EnumDescription("Inactive")]
        InActive = 0,

        [EnumDescription("Active")]
        Active = 1,

        [EnumDescription("Maintenance")]
        Maintenance = 2,

        [EnumDescription("Locked")]
        Locked = 3,

        [EnumDescription("Replicating")]
        Replicating = 4
    }

    [Serializable]
    public enum InfraObjectMonitorStatus
    {
        [EnumDescription("Active")]
        Active = 0,

        [EnumDescription("Running")]
        IsRunning = 1,

        [EnumDescription("Completed")]
        IsCompleted = 2

    }
    [Serializable]
    public enum InfraObjectReplicationStatus
    {
        [EnumDescription("Running")]
        Running = 0,

        [EnumDescription("PausingGM")]
        PausingGM = 1,

        [EnumDescription("PerformingNormalReplication")]
        PerformingNormalReplication = 2,

        [EnumDescription("NormalReplicationCompleted")]
        NormalReplicationCompleted = 3,

        [EnumDescription("ResumingGM")]
        ResumingGM = 4,

        [EnumDescription("MountingVolume")]
        MountingVolume = 5,

        [EnumDescription("ApplyingIncrementalLogs")]
        ApplyingIncrementalLogs = 6,

        [EnumDescription("UnMountingVolume")]
        UnMountingVolume = 7,

        [EnumDescription("CompletedSuccessfully")]
        CompletedSuccessfully = 8,

        [EnumDescription("CompletedWithError")]
        CompletedWithError = 9,

        [EnumDescription("NormalReplicationFailed")]
        NormalReplicationFailed = 10,

        [EnumDescription("GlobalMirrorFatalState")]
        GlobalMirrorFatalState = 11,

        [EnumDescription("Maintenance")]
        Maintenance = 12,

        [EnumDescription("ExecutingReplicationCycle")]
        ExecutingReplicationCycle = 13,

        [EnumDescription("NoLogsAvailable")]
        NoLogsAvailable = 14,

        [EnumDescription("PerformingDataSyncReplication")]
        PerformingDataSyncReplication = 15,

        [EnumDescription("DataSyncReplicationCompleted")]
        DataSyncReplicationCompleted = 16,

        [EnumDescription("Locked")]
        Locked = 17,

        [EnumDescription("Active")]
        Active = 18,

        [EnumDescription("PerformingRSyncReplication")]
        PerformingRSyncReplication = 19,

        [EnumDescription("RSyncReplicationCompleted")]
        RSyncReplicationCompleted = 20,

        [EnumDescription("PerformingRoboCopyReplication")]
        PerformingRoboCopyReplication = 21,

        [EnumDescription("RoboCopyReplicationCompleted")]
        RoboCopyReplicationCompleted = 22
    }

    [Serializable]
    public enum SiteType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("PRSite")]
        PRSite = 1,

        [EnumDescription("DRSite")]
        DRSite = 2,

        [EnumDescription("NextDRSite")]
        NearDRSite = 3
    }

    [Serializable]
    public enum AuthenticationType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Default")]
        Default = 1,

        [EnumDescription("Basic")]
        Basic = 2,

        [EnumDescription("Kerberos")]
        Kerberos = 3,
    }

    [Serializable]
    public enum ProtocolType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Http")]
        Http = 1,

        [EnumDescription("Https")]
        Https = 2,
    }

    [Serializable]
    public enum SiteStatus
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Active")]
        Active = 1,

        [EnumDescription("Inactive")]
        Inactive = 2
    }

    [Serializable]
    public enum NodeStatus
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Up")]
        Up = 1,

        [EnumDescription("Down")]
        Down = 2
    }

    [Serializable]
    public enum UnlockMode
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Auto")]
        Auto = 1,

        [EnumDescription("Manual")]
        Manual = 2
    }

    [Serializable]
    public enum GroupType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("ApplicationGroup")]
        ApplicationGroup = 1,

        [EnumDescription("DatabaseGroup")]
        DatabaseGroup = 2
    }

    [Serializable]
    public enum LoginType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("LDAP")]
        LDAP = 1,

        [EnumDescription("AD")]
        AD = 2,

        [EnumDescription("SAML")]
        SAML = 3,

        [EnumDescription("FormAuthentication")]
        FormAuthentication = 4

    }

    [Serializable]
    public enum Modules
    {
        [EnumDescription("Admin")]
        Admin = 1,

        [EnumDescription("CompanyProfile")]
        CompanyProfile = 2,

        [EnumDescription("Site")]
        Site = 3,

        [EnumDescription("Group")]
        Group = 4,

        [EnumDescription("Component")]
        Component = 5,

        [EnumDescription("Alert")]
        Alert = 6,

        [EnumDescription("Reports")]
        Reports = 7,

        [EnumDescription("User")]
        UserManagement = 8,

        [EnumDescription("Workflow")]
        Workflow = 9,

        [EnumDescription("Network")]
        Network = 10
    }

    [Serializable]
    public enum OSTypes
    {
        [EnumDescription("AIX")]
        AIX = 1,

        [EnumDescription("HPUX")]
        HPUX = 2,

        [EnumDescription("Linux")]
        Linux = 3,

        [EnumDescription("Solaris")]
        Solaris = 4,

        [EnumDescription("Windows2003")]
        Windows2003 = 5,

        [EnumDescription("Windows2008")]
        Windows2008 = 6,

        [EnumDescription("Windows2012")]
        Windows2012 = 7,

        [EnumDescription("AS400")]
        AS400 = 8,

        [EnumDescription("Windows2016")]
        Windows2016 = 9,

        //[EnumDescription("Windows2014")]
        //Windows2014 = 10,

        [EnumDescription("Windows2019")]
        Windows2019 = 11,

        [EnumDescription("Windows2022")]
        Windows2022 = 12
    }

    [Serializable]
    public enum DatabaseMode
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Up")]
        Up = 1,

        [EnumDescription("Down")]
        Down = 2
    }

    [Serializable]
    public enum ServerStatus
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Up")]
        Up = 1,

        [EnumDescription("Down")]
        Down = 2,

        [EnumDescription("Pending")]
        Pending = 3
    }

    [Serializable]
    public enum InstantJob
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Ping")]
        Ping = 130,

        [EnumDescription("SwitchOver")]
        SwitchOver = 131,

        [EnumDescription("SwitchBack")]
        SwitchBack = 132,

        [EnumDescription("FailOver")]
        FailOver = 132,
    }

    [Serializable]
    public enum WorkflowActionType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("PauseGlobalMirror")]
        PauseGlobalMirror = 1,

        [EnumDescription("ResumeGlobalMirror")]
        ResumeGlobalMirror = 2,

        [EnumDescription("PerformNormalReplication")]
        PerformNormalReplication = 3,

        [EnumDescription("PausePPRC")]
        PausePPRC = 4,

        [EnumDescription("ResumePPRC")]
        ResumePPRC = 5,

        [EnumDescription("FailBackPPRC")]
        FailBackPPRC = 6,

        [EnumDescription("MKFlashBC")]
        MKFlashBC = 7,

        [EnumDescription("MKFlashBD")]
        MKFlashBD = 8,

        [EnumDescription("RMFlashBc")]
        RMFlashBc = 9,

        [EnumDescription("RMFlashBD")]
        RMFlashBD = 10,

        [EnumDescription("FailOverPPRC")]
        FailOverPPRC = 11,

        [EnumDescription("ApplyIncrementalLogs")]
        ApplyIncrementalLogs = 12,

        [EnumDescription("MountVG")]
        MountVG = 13,

        [EnumDescription("UnMountVG")]
        UnMountVG = 14,

        [EnumDescription("startDB")]
        StartDB = 15,

        [EnumDescription("shutDB")]
        ShutDB = 16,

        [EnumDescription("SwitchOver")]
        SwitchOver = 17,

        [EnumDescription("SwitchBack")]
        SwitchBack = 18,

        [EnumDescription("PerformNormalReflication")]
        PerformNormalReflication = 19,

        [EnumDescription("DGConnector")]
        DGConnector = 20,

        [EnumDescription("DGVerifyDBModeAndRole")]
        DGVerifyDBModeAndRole = 21,

        [EnumDescription("DGCheckUserStatus")]
        DGCheckUserStatus = 22,

        [EnumDescription("DGJobStatus")]
        DGJobStatus = 23,

        [EnumDescription("DGVerifyMaxSequenceNumber")]
        DGVerifyMaxSequenceNumber = 24,

        [EnumDescription("DGSwitchPrimaryToStandBy")]
        DGSwitchPrimaryToStandBy = 25,

        [EnumDescription("DGSwitchStandByToPrimary")]
        DGSwitchStandByToPrimary = 26,

        [EnumDescription("DGShutDownPrimary")]
        DGShutDownPrimary = 27,

        [EnumDescription("DGMountStandBy")]
        DGMountStandBy = 28,

        [EnumDescription("DGStartPrimary")]
        DGStartPrimary = 29,

        [EnumDescription("DGSwitchLogFile")]
        DGSwitchLogFile = 30,

        [EnumDescription("DGRecoverStandBy")]
        DGRecoverStandBy = 31,

        [EnumDescription("PowerOnMachine")]
        PowerOnMachine = 32,

        [EnumDescription("PowerOffMachine")]
        PowerOffMachine = 33,

        [EnumDescription("ExecuteOSCommand")]
        ExecuteOSCommand = 34,

        [EnumDescription("MakeGMIR")]
        MakeGMIR = 35,

        [EnumDescription("RemoveGMIR")]
        RemoveGMIR = 36,

        [EnumDescription("MakeSession")]
        MakeSession = 37,

        [EnumDescription("ChangeSession")]
        ChangeSession = 38,

        [EnumDescription("MountVGS")]
        MountVGS = 39,

        [EnumDescription("UnMountVGS")]
        UnMountVGS = 40,

        [EnumDescription("ExecuteDBCommand")]
        ExecuteDBCommand = 41,

        [EnumDescription("ExecuteStorageCommand")]
        ExecuteStorageCommand = 42,

        [EnumDescription("StartDatabseunMount")]
        StartDatabseunMount = 43,

        [EnumDescription("StartDatabseMount")]
        StartDatabseMount = 44,

        [EnumDescription("OpenDatabase")]
        OpenDatabase = 45,

        [EnumDescription("AlterDatabaseOpen")]
        AlterDatabaseOpen = 46,

        [EnumDescription("BackUpControlFile")]
        BackUpControlFile = 47,

        [EnumDescription("CreateStandByControlFile")]
        CreateStandByControlFile = 48,

        [EnumDescription("StartDownDataBase")]
        StartDownDataBase = 49,

        [EnumDescription("SCP")]
        SCP = 50,

        [EnumDescription("RestoreStandByControlFile")]
        RestoreStandByControlFile = 51,

        [EnumDescription("RecoverStandByDatabaseFile")]
        RecoverStandByDatabaseFile = 52,

        [EnumDescription("RecoverDatabase")]
        RecoverDatabase = 53,

        [EnumDescription("CreateControlFileByScript")]
        CreateControlFileByScript = 54,

        [EnumDescription("CreateControlFileScript")]
        CreateControlFileScript = 55,

        [EnumDescription("IsDBRunning")]
        IsDBRunning = 56,

        [EnumDescription("IsDBDown")]
        IsDBDown = 57,

        [EnumDescription("AlterDatabaseMount")]
        AlterDatabaseMount = 58,

        [EnumDescription("AlterSystemLog")]
        AlterSystemLog = 59,

        [EnumDescription("CreateTempFile")]
        CreateTempFile = 60,

        [EnumDescription("CreateTempFileTableSpace")]
        CreateTempFileTableSpace = 61,

        [EnumDescription("IsCheckPointCountOne")]
        IsCheckPointCountOne = 62,

        [EnumDescription("StartDBStandBy")]
        StartDBStandBy = 63,

        [EnumDescription("StartDBReadWrite")]
        StartDBReadWrite = 64,

        [EnumDescription("ReplicateStandByControlFile")]
        ReplicateStandByControlFile = 65,

        [EnumDescription("MSSqlGenerateLog")]
        MSSqlGenerateLog = 66,

        [EnumDescription("MSSqlGenerateLastLog")]
        MSSqlGenerateLastLog = 67,

        [EnumDescription("MSSqlRestoreLog")]
        MSSqlRestoreLog = 68,

        [EnumDescription("MSSqlRestoreLastLog")]
        MSSqlRestoreLastLog = 69,

        [EnumDescription("MSSqlKillProcessPR")]
        MSSqlKillProcessPR = 70,

        [EnumDescription("MSSqlDBOption")]
        MSSqlDBOption = 71,

        [EnumDescription("MSSqlRestoreDBWithRecovery")]
        MSSqlRestoreDBWithRecovery = 72,

        [EnumDescription("MSSqlVerifyLogSequence")]
        MSSqlVerifyLogSequence = 73,

        [EnumDescription("FastcopyRelication")]
        FastcopyRelication = 74,

        [EnumDescription("SwitchShutPrimaryDB")]
        SwitchShutPrimaryDB = 75,

        [EnumDescription("SetJobQueProcess")]
        SetJobQueProcess = 76,

        [EnumDescription("CheckJobQueProcess")]
        CheckJobQueProcess = 77,

        [EnumDescription("DeleteArchiveLogs")]
        DeleteArchiveLogs = 78,

        [EnumDescription("IsFileSystemMounted")]
        IsFileSystemMounted = 79,

        [EnumDescription("ExecuteRMANCommand")]
        ExecuteRMANCommand = 80,

        [EnumDescription("IsCGFormed")]
        IsCGFormed = 81,

        [EnumDescription("VerifyLogSequence")]
        VerifyLogSequence = 82,

        [EnumDescription("ActiveDatabaseReadWrite")]
        ActiveDatabaseReadWrite = 83,

        [EnumDescription("StartDatabase")]
        StartDatabase = 84,

        [EnumDescription("ImportVG")]
        ImportVG = 85,

        [EnumDescription("ReplicateStandByCtrFile")]
        ReplicateStandByCtrFile = 86,

        [EnumDescription("ReplicateStandByTraceFile")]
        ReplicateStandByTraceFile = 87,

        [EnumDescription("StartListner")]
        StartListner = 88,

        [EnumDescription("StopListner")]
        StopListner = 89,

        [EnumDescription("ReStartListner")]
        ReStartListner = 90,

        [EnumDescription("CheckGroupSyncStatus")]
        CheckGroupSyncStatus = 91,

        [EnumDescription("ChangeDNS")]
        ChangeDNS = 92,

        [EnumDescription("DisableBackupJobPrimary")]
        DisableBackupJobPrimary = 93,

        [EnumDescription("DisableCopyJobDr")]
        DisableCopyJobDr = 94,

        [EnumDescription("DisableRestoreJobDr")]
        DisableRestoreJobDr = 95,

        [EnumDescription("StartCopyJobDr")]
        StartCopyJobDr = 96,

        [EnumDescription("StartRestoreJobDr")]
        StartRestoreJobDr = 97,

        [EnumDescription("KillTransactionProcess")]
        KillTransactionProcess = 98,

        [EnumDescription("GenerateLastLog")]
        GenerateLastLog = 99,

        [EnumDescription("DisableSecondaryDbJob")]
        DisableSecondaryDbJob = 100,

        [EnumDescription("MakeSingleUser")]
        MakeSingleUser = 101,

        [EnumDescription("RestoreLastLogNative")]
        RestoreLastLogNative = 102,

        [EnumDescription("Native_RestoreLogShipping")]
        NativeRestoreLogShipping = 103,

        [EnumDescription("CheckNoLoggingOperation")]
        CheckNoLoggingOperation = 104,

        [EnumDescription("BackUpPrimaryDatabase")]
        BackUpPrimaryDatabase = 105,

        [EnumDescription("Native_RestoreSecondaryDatabase")]
        NativeRestoreSecondaryDatabase = 106,

        [EnumDescription("Native_RestoreLogShippingPrimary")]
        NativeRestoreLogShippingPrimary = 107,

        [EnumDescription("AddSecondaryLog")]
        AddSecondaryLog = 108,

        [EnumDescription("RestoreLogshipSecondary")]
        RestoreLogshipSecondary = 109,

        [EnumDescription("UpdateSecondaryServerCopyJob")]
        UpdateSecondaryServerCopyJob = 110,

        [EnumDescription("UpdateSecondaryServerRestoreJob")]
        UpdateSecondaryServerRestoreJob = 111,

        [EnumDescription("MigrateLoging")]
        SqlNativeMigrateLogingPR = 112,

        [EnumDescription("Native_MonitorLogShipping")]
        NativeMonitorLogShipping = 113,

        [EnumDescription("RunBackJob")]
        RunBackJob = 114,

        [EnumDescription("RunCopyJob")]
        RunCopyJob = 115,

        [EnumDescription("RunRestoreJob")]
        RunRestoreJob = 116,

        [EnumDescription("RestoreSecondaryWithRecovery")]
        RestoreSecondaryWithRecovery = 117,

        [EnumDescription("AddDNS")]
        AddDNS = 118,

        [EnumDescription("DeleteDNS")]
        DeleteDNS = 119,

        [EnumDescription("StartOracleListner")]
        StartOracleListner = 120,

        [EnumDescription("StopOracleListner")]
        StopOracleListner = 121,

        [EnumDescription("PreShutRedoCtrlScript")]
        PreShutRedoCtrlScript = 122,

        [EnumDescription("CopyRedoCtrFile")]
        CopyRedoCtrFile = 123,

        [EnumDescription("Status")]
        Status = 124,

        [EnumDescription("CheckStorageMailboxPath")]
        CheckStorageMailboxPath = 125,

        [EnumDescription("SetSCRPrerequisite")]
        SetSCRPrerequisite = 126,

        [EnumDescription("EnableSCR")]
        EnableSCR = 127,

        [EnumDescription("ExecuteSudoCommand")]
        ExecuteSudoCommand = 128,

        [EnumDescription("UnMountNFSVolume")]
        UnMountNFSVolume = 129,

        [EnumDescription("MountNFSVolume")]
        MountNFSVolume = 130,

        [EnumDescription("CheckFileExist")]
        CheckFileExist = 131,

        [EnumDescription("CheckDBStandBy")]
        CheckDBStandBy = 132,

        [EnumDescription("CheckDBReadWrite")]
        CheckDBReadWrite = 133,

        [EnumDescription("ExecuteCheckOSCommand")]
        ExecuteCheckOSCommand = 134,

        [EnumDescription("FastCopyReplicateFile")]
        FastCopyReplicateFile = 135,

        [EnumDescription("FastCopySyncFolders")]
        FastCopySyncFolders = 136,

        [EnumDescription("CompaireNewSGMailboxPath")]
        CompaireNewSGMailboxPath = 137,

        [EnumDescription("UpdateTargetStorageGroup")]
        UpdateTargetStorageGroup = 138,

        [EnumDescription("ResumeSCR")]
        ResumeSCR = 139,

        [EnumDescription("ChecktargetDBStatus")]
        ChecktargetDBStatus = 140,

        [EnumDescription("DismountMailboxdatabase")]
        DismountMailboxdatabase = 141,

        [EnumDescription("SCRStatusWithCopyQueueLength")]
        SCRStatusWithCopyQueueLength = 142,

        [EnumDescription("DisableSCRRestorelogs")]
        DisableSCRRestorelogs = 143,

        [EnumDescription("CheckTargetDBFileStatus")]
        CheckTargetDBFileStatus = 144,

        [EnumDescription("AllowFileRestoreToMailboxDB")]
        AllowFileRestoreToMailboxDB = 145,

        [EnumDescription("MountDatabase")]
        MountDatabase = 146,

        [EnumDescription("GetMaiboxListCountbeforeswitch")]
        GetMaiboxListCountbeforeswitch = 147,

        [EnumDescription("MoveMailboxConfiguration")]
        MoveMailboxConfiguration = 148,

        [EnumDescription("GetMaiboxListCount")]
        GetMaiboxListCount = 149,

        [EnumDescription("ChangeRedoArchivePermission")]
        ChangeRedoArchivePermission = 150,

        [EnumDescription("RevertRedoArchivePermission")]
        RevertRedoArchivePermission = 151,

        [EnumDescription("FastCopySyncArchiveFolder")]
        FastCopySyncArchiveFolder = 152,

        [EnumDescription("FastCopySyncRedoArchiveFolder")]
        FastCopySyncRedoArchiveFolder = 153,

        [EnumDescription("DGRecoverStandBy")]
        InitiateBCVCopy = 154,

        [EnumDescription("InitiateBCVCopy")]
        CreateASMDisks = 155,

        [EnumDescription("StartASMInstance")]
        StartASMInstance = 156,

        [EnumDescription("MountASMDiskGroups")]
        MountASMDiskGroups = 157,

        [EnumDescription("ActivatePhysicalStdbyDB")]
        ActivatePhysicalStdbyDB = 158,

        [EnumDescription("StopSchedulerJobs")]
        StopSchedulerJobs = 159,

        [EnumDescription("UnMountASMDiskGroups")]
        UnMountASMDiskGroups = 160,

        [EnumDescription("ShutASMInstance")]
        ShutASMInstance = 161,

        [EnumDescription("SqlDS_MsSqlSwitchOver")]
        SqlDSMsSqlSwitchOver = 162,

        [EnumDescription("SqlDS_MsSqlSwitchBack")]
        SqlDSMsSqlSwitchBack = 163,

        [EnumDescription("RSyncFileReplication")]
        RSyncFileReplication = 164,

        [EnumDescription("VMCreateSnapShot")]
        VMCreateSnapShot = 165,

        [EnumDescription("VMRemoveSnapShot")]
        VMRemoveSnapShot = 166,

        [EnumDescription("VMRevertToSnapShot")]
        VMRevertToSnapShot = 167,

        [EnumDescription("VMRegisterMachine")]
        VMRegisterMachine = 168,

        [EnumDescription("UpdateSecondaryServerBackUpJob")]
        UpdateSecondaryServerBackUpJob = 169,

        [EnumDescription("CreateStorageGroup")]
        CreateStorageGroup = 170,

        [EnumDescription("NativeSetdbOption")]
        NativeSetdbOption = 171,

        [EnumDescription("NativeDeleteLastLog")]
        NativeDeleteLastLog = 172,

        [EnumDescription("NativeRemovePRDRLS")]
        NativeRemovePRDRLS = 173,

        [EnumDescription("NativeRemoveLSPri")]
        NativeRemoveLSPri = 174,

        [EnumDescription("NativeRemoveLSSEC")]
        NativeRemoveLSSEC = 175,

        [EnumDescription("ASMActivatePhysicalStbyDB")]
        ASMActivatePhysicalStbyDB = 176,

        [EnumDescription("DisableSchedulerJobs")]
        DisableSchedulerJobs = 177,

        [EnumDescription("CheckDataFileErrorCountNull")]
        CheckDataFileErrorCountNull = 178,

        [EnumDescription("CheckCheckPointChange")]
        CheckCheckPointChange = 179,

        [EnumDescription("BackUpControlFileLocal")]
        BackUpControlFileLocal = 180,

        [EnumDescription("DeleteFile")]
        DeleteFile = 181,

        [EnumDescription("RenameFile")]
        RenameFile = 182,

        [EnumDescription("CopyFileLocal")]
        CopyFileLocal = 183,

        [EnumDescription("MoveFileLocal")]
        MoveFileLocal = 184,

        [EnumDescription("ExecuteCheckSqlCommand")]
        ExecuteCheckSqlCommand = 185,

        [EnumDescription("IsDBReadWrite")]
        IsDBReadWrite = 186,

        [EnumDescription("IsDBStandBy")]
        IsDBStandBy = 187,

        [EnumDescription("MigrateLogingDR")]
        SqlNativeMigrateLogingDR = 188,

        [EnumDescription("MSSqlKillProcessDR")]
        MSSqlKillProcessDR = 189,

        [EnumDescription("IsMachineRunning")]
        IsMachineRunning = 190,

        [EnumDescription("OnRegisterVirtualMachine")]
        OnRegisterVirtualMachine = 191,

        [EnumDescription("ReplicateSnapShot")]
        ReplicateSnapShot = 192,

        [EnumDescription("ReplicateSCP")]
        ReplicateSCP = 193,

        [EnumDescription("ReplicateSnapShotFile")]
        ReplicateSnapShotFile = 194,

        [EnumDescription("ReplicateVMConfigFile")]
        ReplicateVMConfigFile = 195,

        //for snapMirror
        [EnumDescription("SnapMirror_Update")]
        SnapMirrorUpdate = 196,

        [EnumDescription("SnapMirror_Break")]
        SnapMirrorBreak = 197,

        [EnumDescription("SnapMirror_Vloume_Online")]
        SnapMirrorVloumeOnline = 198,

        [EnumDescription("SnapMirror_Vloume_Restrict")]
        SnapMirrorVloumeRestrict = 199,

        [EnumDescription("SnapMirror_Resync")]
        SnapMirrorResync = 200,

        [EnumDescription("ApplyASMLog")]
        ApplyASMLog = 201,

        [EnumDescription("NativeSetFileNameFlag")]
        NativeSetFileNameFlag = 202,

        [EnumDescription("NativeChangeRolePri_Sec")]
        NativeChangeRolePriSec = 203,

        [EnumDescription("ReplicateVirtualMachine")]
        ReplicateVirtualMachine = 204,

        [EnumDescription("MSSqlKillProcess_Sec")]
        MSSqlKillProcessSec = 205,

        [EnumDescription("MSSqlKillProcess_Prim")]
        MSSqlKillProcessPrim = 206,

        [EnumDescription("MSSqlGenerateLastLog_FC")]
        MSSqlGenerateLastLogFc = 207,

        [EnumDescription("FileReplaceText")]
        FileReplaceText = 208,

        [EnumDescription("ExecuteCheckStroageCommand")]
        ExecuteCheckStroageCommand = 209,

        [EnumDescription("WriteToRouter")]
        WriteToRouter = 210,

        [EnumDescription("ApplicationStart")]
        ApplicationStart = 211,

        [EnumDescription("ApplicationStop")]
        ApplicationStop = 212,

        [EnumDescription("ApplicationMonitor")]
        ApplicationMonitor = 213,

        [EnumDescription("IsApplicationUp")]
        IsApplicationUp = 214,

        [EnumDescription("IsApplicationDown")]
        IsApplicationDown = 215,

        [EnumDescription("EMC_ISDGRESENT")]
        EmcisDGResent = 216,

        [EnumDescription("EMC_DISABLEDEVICEGROUP")]
        EMCDisableDeviceGroup = 217,

        [EnumDescription("EMC_ENABLEDEVICEGROUP")]
        EMCEnableDeviceGroup = 218,

        [EnumDescription("EMC_SETACPDEVICEMODE")]
        EMCSetACPDeviceMode = 219,

        [EnumDescription("EMC_FAILOVERDEVICEGROUP")]
        EMCFailOverDeviceGroup = 220,

        [EnumDescription("EMC_SWAPPERSONALITY")]
        EMCSwapPersonality = 221,

        [EnumDescription("EMC_RESUMEDEVICEGROUP")]
        EMCResumeDeviceGroup = 222,

        [EnumDescription("EMC_SETASYNCMODE")]
        EMCSetAsyncMode = 223,

        [EnumDescription("HPUX_MOUNT")]
        HPUXMount = 224,

        [EnumDescription("HPUX_UNMOUNT")]
        HPUXUnmount = 225,

        [EnumDescription("HPUX_ACTIVEVG")]
        HPUXActiveVG = 226,

        [EnumDescription("HPUX_DEACTIVEVG")]
        HPUXDeactiveVG = 227,

        [EnumDescription("ExecuteSymcliCommand")]
        ExecuteSymcliCommand = 228,

        ExecuteCheckDB2Command = 229, Wait = 230,

        [EnumDescription("HPUX_IsVGACTIVE")]
        HPUXIsVgActive = 231,

        [EnumDescription("HPUX_EXPORTVGTOMAPFILE")]
        HPUXExportVGtoMapfile = 232,

        [EnumDescription("HPUX_IMPORTVGTOMAPFILE")]
        HPUXImportVGtoMapfile = 233,

        ExecuteSSH = 234,

        [EnumDescription("HP_MOUNTVGS")]
        HpMountVGS = 235,

        [EnumDescription("HP_UNMOUNTVGS")]
        HpUnmountVGS = 236,

        [EnumDescription("EMC_SWITCHOVERDG")]
        EMCSwitchoverDG = 237,

        [EnumDescription("EMC_SWITCHBACKDG")]
        EMCSwitchbackDG = 238,

        [EnumDescription("EMC_ISDGCONSISTENT")]
        EMCisDGConsistent = 239,

        [EnumDescription("HP_MOUNTVGPARALLEL")]
        HPMountvgparallel = 240,

        [EnumDescription("HP_UNMOUNTVGPARALLEL")]
        HPUnmountVGParallel = 241,

        [EnumDescription("EMC_CHECKDGTRACKSZERO")]
        EMCCheckdgTracksZero = 242,

        [EnumDescription("WAITFORWORKFLOWACTION")]
        WaitforWorkflowAction = 243,

        [EnumDescription("DB2_Start_HADRPrimary")]
        DB2StartHADRPrimary = 244,

        [EnumDescription("DB2_Stop_HADR")]
        DB2StopHADR = 245,

        [EnumDescription("DB2_IsHADRAActive")]
        DB2_IsHADRAActive = 246,

        [EnumDescription("DB2_IsDBUp")]
        DB2IsDBUp = 247,

        [EnumDescription("DB2_SwitchOverDB")]
        DB2SwitchOverDB = 248,

        [EnumDescription("DB2_SwitchBackDB")]
        DB2SwitchBackDB = 249,

        [EnumDescription("DB2_ActivateDB")]
        DB2ActivateDB = 250,

        [EnumDescription("DB2_DeActivateDBStart")]
        DB2DeActivateDBStart = 251,

        [EnumDescription("DB2_IsHADRRolePrimary")]
        DB2IsHADRRolePrimary = 252,

        [EnumDescription("DB2_IsHADRRoleStandby")]
        DBIsHADRRoleStandby = 253,

        [EnumDescription("DB2_IsDatabasePrimary")]
        DB2IsDatabasePrimary = 254,

        [EnumDescription("DB2_IsDatabaseStandby")]
        DB2IsDatabaseStandby = 255,

        [EnumDescription("DB2_IsHADRStatePEER")]
        DB2IsHADRStatePEER = 256,

        [EnumDescription("DB2_QuiesceDatabase")]
        DB2QuiesceDatabase = 257,

        [EnumDescription("DB2_UnQuiesceDatabase")]
        DB2UnQuiesceDatabase = 258,

        [EnumDescription("DB2_IsDatabaseQuiesced")]
        DB2IsDatabaseQuiesced = 259,

        [EnumDescription("DB2_Terminate")]
        DB2Terminate = 260,

        [EnumDescription("DB2_Start")]
        DB2Start = 261,

        [EnumDescription("DB2_TakeOverHADR")]
        DB2TakeOverHADR = 262,

        [EnumDescription("DB2_DeActivateDBTerminate")]
        DB2DeActivateDBTerminate = 263,

        [EnumDescription("DB2_Start_HADRStandBy")]
        DB2StartHADRStandBy = 264,

        [EnumDescription("DGWinVerifyMaxSequenceNumber")]
        DGWinVerifyMaxSequenceNumber = 265,

        [EnumDescription("DGWinCheckUserStatus")]
        DGWinCheckUserStatus = 266,

        [EnumDescription("DGWinVerifyDBModeAndRole")]
        DGWinVerifyDBModeAndRole = 267,

        [EnumDescription("MigrateLoging_PR")]
        MigrateLogingPR = 268,

        [EnumDescription("MigrateLoging_DR")]
        MigrateLogingDR = 269,

        [EnumDescription("SqlDS_DatSyncSql2000")]
        SqlDSDataSyncSql2000 = 270,

        [EnumDescription("MigrateServer_roles_PR")]
        MigrateServerRolesPR = 271,

        [EnumDescription("MigrateServer_roles_DR")]
        MigrateServerRolesDR = 272,

        [EnumDescription("ReplicateFastCopyFileWin")]
        ReplicateFastCopyFileWin = 273,

        [EnumDescription("ReplicateFastCopyFilePosix")]
        ReplicateFastCopyFilePosix = 274,

        [EnumDescription("CancelRecoverMode")]
        CancelRecoverMode = 275,

        [EnumDescription("GenerateRedoCtrlBKPScriptWin")]
        GenerateRedoCtrlBKPScriptWin = 276,

        [EnumDescription("SwitchShutPRDBWin")]
        SwitchShutPRDBWin = 277,

        [EnumDescription("ExecuteRedoCtrlBKPWin")]
        ExecuteRedoCtrlBKPWin = 278,

        [EnumDescription("StartDBReadWriteWin")]
        StartDBReadWriteWin = 279,

        [EnumDescription("StartDBStandByWin")]
        StartDBStandByWin = 280,

        [EnumDescription("ReplicateFastCopyFoldersWin")]
        ReplicateFastCopyFoldersWin = 281,

        [EnumDescription("ReplicateFastCopyFoldersPosix")]
        ReplicateFastCopyFoldersPosix = 282,

        [EnumDescription("SyncArchives")]
        SyncArchives = 283,

        [EnumDescription("ReveseReplicateFastCopyFoldersPosix")]
        ReveseReplicateFastCopyFoldersPosix = 284,

        [EnumDescription("CheckProcessCount")]
        CheckProcessCount = 285,

        [EnumDescription("ExecuteWindowsProcess")]
        ExecuteWindowsProcess = 286,

        [EnumDescription("StartHypervisorMachine")]
        StartHypervisorMachine = 287,

        [EnumDescription("StopHypervisorMachine")]
        StopHypervisorMachine = 288,

        [EnumDescription("StartWindowsService")]
        StartWindowsService = 289,

        [EnumDescription("StopWindowsService")]
        StopWindowsService = 290,

        [EnumDescription("KillWindowsProcess")]
        KillWindowsProcess = 291,

        [EnumDescription("WaitForPing")]
        WaitForPing = 292,

        [EnumDescription("DS_failOver")]
        DsFailOver = 293,

        [EnumDescription("DS_RMFLASH")]
        DsRmFlash = 294,

        [EnumDescription("DS_CHECKCONSISTENCY")]
        DsCheckConsistency = 295,

        [EnumDescription("DS_REVERSEFLASH")]
        DsReverseFlash = 296,

        [EnumDescription("DS_MKFLASH")]
        DsMkFlash = 297,

        [EnumDescription("AIX_MountVG")]
        AIXMountVG = 298,

        [EnumDescription("AIX_UNMountVG")]
        AIXUnMountVG = 299,

        [EnumDescription("NativeMigrateServerRole_PR")]
        NativeMigrateServerRolePR = 300,

        [EnumDescription("NativeMigrateServerRole_DR")]
        NativeMigrateServerRoleDR = 301,

        [EnumDescription("NativeVerifyLastRestoreFileName")]
        NativeVerifyLastRestoreFileName = 302,

        [EnumDescription("NativeVerifyDBState")]
        NativeVerifyDBState = 303,

        [EnumDescription("AIX_Mount")]
        AIXMount = 304,

        [EnumDescription("AIX_DisMount")]
        AIXDisMount = 305,

        [EnumDescription("Hitachi_Horcctakeover")]
        HitachiHorccTakeOver = 306,

        [EnumDescription("Hitachi_Pairsplit")]
        HitachiPairsplit = 307,

        [EnumDescription("Hitachi_PairReSync")]
        HitachiPairReSync = 308,

        [EnumDescription("Hitachi_IsVolSuspended")]
        HitachiIsVolSuspended = 309,

        [EnumDescription("ReplicateFileParallel")]
        ReplicateFileParallel = 310,

        [EnumDescription("DGVerifySwitchoverStatusIsPrimary")]
        DGVerifySwitchoverStatusIsPrimary = 311,

        [EnumDescription("DGVerifySwitchoverStatusIsStandby")]
        DGVerifySwitchoverStatusIsStandby = 312,

        [EnumDescription("DGSetArchiveLogDestination")]
        DGSetArchiveLogDestination = 313,

        [EnumDescription("DGApplyJobQeueProcess")]
        DGApplyJobQeueProcess = 314,

        [EnumDescription("ExecuteCheckSymcliCommand")]
        ExecuteCheckSymcliCommand = 315,

        [EnumDescription("NativeSetDBOptionSql2012")]
        NativeSetDBOptionSql2012 = 316,

        [EnumDescription("AlterDatabaseFlashBackOn")]
        AlterDatabaseFlashBackOn = 317,

        [EnumDescription("CreateRestorePoint")]
        CreateRestorePoint = 318,

        [EnumDescription("AlterDatabaseActiveStby")]
        AlterDatabaseActiveStby = 319,

        [EnumDescription("FlashBackToRestorePoint")]
        FlashBackToRestorePoint = 320,

        [EnumDescription("AlterDatabaseConvertPhysicalStby")]
        AlterDatabaseConvertPhysicalStby = 321,

        [EnumDescription("StartUpMountForce")]
        StartUpMountForce = 322,

        [EnumDescription("DropRestorePoint")]
        DropRestorePoint = 323,

        [EnumDescription("AlterDatabaseFlashBackOff")]
        AlterDatabaseFlashBackOff = 324,

        [EnumDescription("AlterSystemSetLogArchiveDestDefer")]
        AlterSystemSetLogArchiveDestDefer = 325,

        [EnumDescription("AlterSystemSetLogArchiveDestEnable")]
        AlterSystemSetLogArchiveDestEnable = 326,

        [EnumDescription("AlterStbyDBtoMaxPerformance")]
        AlterStbyDBtoMaxPerformance = 327,

        [EnumDescription("AlterDBRecoverManagedStbyDBCancel")]
        AlterDBRecoverManagedStbyDBCancel = 328,

        [EnumDescription("CheckFlashBackOn")]
        CheckFlashBackOn = 329,

        [EnumDescription("EMC_ISSPLIT")]
        EMCIsSplit = 330,

        [EnumDescription("EMC_ISISSYNCINPROGRESS")]
        EMCIsSyncInProgress = 331,

        [EnumDescription("EMC_SPLITDEVICEGROUP")]
        EMCSplitDeviceGroup = 332,

        [EnumDescription("EMC_ESTABLISHDEVICEGROUP")]
        EMCEstablishDeviceGroup = 333,

        [EnumDescription("SendAlert")]
        SendAlert = 334,

        [EnumDescription("ExecuteShellProcess")]
        ExecuteShellProcess = 335,

        [EnumDescription("ExecuteSwiftProcess")]
        ExecuteSwiftProcess = 336,

        [EnumDescription("ExecuteT24Process")]
        ExecuteT24Process = 337,

        [EnumDescription("ChangeCIProperties")]
        ChangeCIProperties = 338,

        [EnumDescription("Win_MontVol")]
        WinMontVol = 339,

        [EnumDescription("Win_DeMontVol")]
        WinDeMontVol = 340,

        [EnumDescription("VM_UnRegister")]
        VMUnRegister = 341,

        [EnumDescription("VM_ShutDown")]
        VMShutDown = 342,

        [EnumDescription("VM_Mount")]
        VMMount = 343,

        [EnumDescription("VM_UnMount")]
        VMUnMount = 344,

        [EnumDescription("DetachLunFromESXI")]
        DetachLunFromESXI = 345,

        [EnumDescription("RemoveLunFromESXI")]
        RemoveLunFromESXI = 346,

        [EnumDescription("UpdateVMNetworkInfo")]
        UpdateVMNetworkInfo = 347,

        [EnumDescription("ExecutePowerShellScript")]
        ExecutePowerShellScript = 348,

        [EnumDescription("VMGuestStartService")]
        VMGuestStartService = 349,

        [EnumDescription("VMGuestStopService")]
        VMGuestStopService = 350,

        [EnumDescription("Hitachi_IsPairSync")]
        HitachiIsPairSync = 351,

        [EnumDescription("Hitachi_IsPairSuspended")]
        HitachiIsPairSuspended = 352,

        [EnumDescription("DB2_IsDBDown")]
        DB2IsDBDown = 353,

        [EnumDescription("ExecuteScript")]
        ExecuteScript = 354,

        [EnumDescription("ExecuteScriptWithPassword")]
        ExecuteScriptWithPassword = 355,

        [EnumDescription("Sleep")]
        Sleep = 356,

        [EnumDescription("ExecuteSqlScriptWithPassword")]
        ExecuteSqlScriptWithPassword = 357,

        [EnumDescription("ExecuteScriptwithEnvPassword")]
        ExecuteScriptwithEnvPassword = 358,

        [EnumDescription("ExecuteSqlscriptWithEnvPassword")]
        ExecuteSqlscriptWithEnvPassword = 359,

        [EnumDescription("ExecuteScriptalongPassword")]
        ExecuteScriptalongPassword = 360,

        [EnumDescription("StartDataBaseReadOnly")]
        StartDataBaseReadOnly = 361,

        [EnumDescription("KillUnixProcess")]
        KillUnixProcess = 362,

        [EnumDescription("Win_KillProcessById")]
        Win_KillProcessById = 363,

        [EnumDescription("win_killProcessByName")]
        win_killProcessByName = 364,

        [EnumDescription("win_IsServiceRunning")]
        win_IsServiceRunning = 365,

        [EnumDescription("win_IsServiceDown")]
        win_IsServiceDown = 366,

        [EnumDescription("win_IsProcessRunning")]
        win_IsProcessRunning = 367,

        [EnumDescription("win_IsProcessDown")]
        win_IsProcessDown = 368,

        [EnumDescription("win_ExecuteProcess")]
        win_ExecuteProcess = 369,

        [EnumDescription("win_IsFileExist")]
        win_IsFileExist = 370,

        [EnumDescription("win_FileDelete")]
        win_FileDelete = 371,

        [EnumDescription("SyncArchive")]
        SyncArchive = 372,

        [EnumDescription("IsFileExist")]
        IsFileExist = 373,

        [EnumDescription("IsFolderExist")]
        IsFolderExist = 374,

        [EnumDescription("IsProcessRunning")]
        IsProcessRunning = 375,

        [EnumDescription("KillProcess")]
        KillProcess = 376,

        [EnumDescription("ExecuteConsoleCommand")]
        ExecuteConsoleCommand = 377,

        [EnumDescription("ExecuteCheckConsoleCommand")]
        ExecuteCheckConsoleCommand = 378,

        [EnumDescription("ExecuteDS_MKFlash")]
        ExecuteDS_MKFlash = 379,

        [EnumDescription("ExecuteDS_ReSyncFlash")]
        ExecuteDS_ReSyncFlash = 380,

        [EnumDescription("ExecuteDS_CheckFlashZeroTracks")]
        ExecuteDS_CheckFlashZeroTracks = 381,

        [EnumDescription("ExecuteDSCommand")]
        ExecuteDSCommand = 382,

        [EnumDescription("ExecuteCheckDSCommand")]
        ExecuteCheckDSCommand = 383,

        [EnumDescription("ExecuteDS_WaitforflashTracksZero")]
        ExecuteDS_WaitforflashTracksZero = 384,

        [EnumDescription("ExecuteScriptWithUserPassword")]
        ExecuteScriptWithUserPassword = 385,

        [EnumDescription("ExecuteDS_PausePPRC")]
        ExecuteDS_PausePPRC = 386,

        [EnumDescription("ExecuteDS_ResumePPRC")]
        ExecuteDS_ResumePPRC = 387,

        [EnumDescription("ExecuteDS_FailOverPPRC")]
        ExecuteDS_FailOverPPRC = 388,

        [EnumDescription("ExecuteDS_RMFlash")]
        ExecuteDS_RMFlash = 389,

        [EnumDescription("ExecuteDS_ReverseFlash")]
        ExecuteDS_ReverseFlash = 390,

        [EnumDescription("ExecuteDS_FailBackPPRC")]
        ExecuteDS_FailBackPPRC = 391,

        [EnumDescription("ExecuteDS_RevertFlash")]
        ExecuteDS_RevertFlash = 392,

        [EnumDescription("ExecuteDS_CommitFlash")]
        ExecuteDS_CommitFlash = 393,

        [EnumDescription("ExecuteDS_ChangeSessionAdd")]
        ExecuteDS_ChangeSessionAdd = 394,

        [EnumDescription("ExecuteDS_ChangeSessionRemove")]
        ExecuteDS_ChangeSessionRemove = 395,

        [EnumDescription("ExecuteDS_MakeSession")]
        ExecuteDS_MakeSession = 396,

        [EnumDescription("ExecuteDS_MakeGMIR")]
        ExecuteDS_MakeGMIR = 397,

        [EnumDescription("ExecuteDS_RemoveGMIR")]
        ExecuteDS_RemoveGMIR = 398,

        [EnumDescription("ExecuteDS_PauseGMIR")]
        ExecuteDS_PauseGMIR = 399,

        [EnumDescription("ExecuteDS_ResumeGMIR")]
        ExecuteDS_ResumeGMIR = 400,

        [EnumDescription("Execute3270")]
        Execute3270 = 401,

        [EnumDescription("ExecuteAIXProcess")]
        ExecuteAIXProcess = 402,

        [EnumDescription("ExecuteVmWarePowerCLICommand")]
        ExecuteVmWarePowerCLICommand = 403,

        [EnumDescription("ExecutePowerCLITasks")]
        ExecutePowerCLITasks = 404,

        [EnumDescription("ExecuteNCheOpVmWarePowerCLICommand")]
        ExecuteNCheOpVmWarePowerCLICommand = 405,

        [EnumDescription("ExecuteSSHTasks")]
        ExecuteSSHTasks = 406,

        [EnumDescription("ExecuteSVCTask")]
        ExecuteSVCTask = 407,

        [EnumDescription("ExecuteCheckSVCTask")]
        ExecuteCheckSVCTask = 408,

        [EnumDescription("SVCWaitforRCState")]
        SVCWaitforRCState = 409,

        [EnumDescription("SVCStartReplication")]
        SVCStartReplication = 410,

        [EnumDescription("SVCStopReplication")]
        SVCStopReplication = 411,

        [EnumDescription("SVCStartReverseReplication")]
        SVCStartReverseReplication = 412,

        [EnumDescription("SRMInitiateRecoveryPlan")]
        SRMInitiateRecoveryPlan = 413,

        [EnumDescription("StartMSCluster")]
        StartMSCluster = 414,

        [EnumDescription("StopMSCluster")]
        StopMSCluster = 415,

        [EnumDescription("ChangeNodeIP")]
        ChangeNodeIP = 416,

        [EnumDescription("ExecuteCPSL")]
        ExecuteCPSL = 417,

        [EnumDescription("ExecuteNohup")]
        ExecuteNohup = 418,

        [EnumDescription("HitCheckHTTPUrl")]
        HitCheckHTTPUrl = 419,

        [EnumDescription("HitHTTPurl")]
        HitHTTPurl = 420,

        [EnumDescription("ExecuteOSCmd")]
        ExecuteOSCmd = 421,

        [EnumDescription("ExecuteLocalProcess")]
        ExecuteLocalProcess = 422,

        [EnumDescription("StartVxVMDG")]
        StartVxVMDG = 423,

        [EnumDescription("StopVxVMDG")]
        StopVxVMDG = 424,

        [EnumDescription("ImportVxVMDG")]
        ImportVxVMDG = 425,

        [EnumDescription("DeportVxVMDG")]
        DeportVxVMDG = 426,

        [EnumDescription("MountFS")]
        MountFS = 427,

        [EnumDescription("UnMountFS")]
        UnMountFS = 428,

        [EnumDescription("IsVxVMDGVolumeEnable")]
        IsVxVMDGVolumeEnable = 429,

        [EnumDescription("IsVxVMDGVolumeDisable")]
        IsVxVMDGVolumeDisable = 430,

        [EnumDescription("IsFSMounted")]
        IsFSMounted = 431,

        [EnumDescription("IsFSUnMounted")]
        IsFSUnMounted = 432,

        [EnumDescription("CheckDGAllVolEnabled")]
        CheckDGAllVolEnabled = 433,

        [EnumDescription("CheckDGAllVolDisabled")]
        CheckDGAllVolDisabled = 434,


        [EnumDescription("DisplayAlert")]
        DisplayAlert = 435,

        [EnumDescription("ShutDownZone")]
        ShutDownZone = 437,

        [EnumDescription("CheckZoneUp")]
        CheckZoneUp = 438,

        [EnumDescription("CheckZoneDown")]
        CheckZoneDown = 439,

        [EnumDescription("CheckZoneStatus")]
        CheckZoneStatus = 440,

        [EnumDescription("AttachZone")]
        AttachZone = 441,

        [EnumDescription("DetachZone")]
        DetachZone = 442,

        [EnumDescription("BootZone")]
        BootZone = 443,

        [EnumDescription("SendSMS")]
        SendSMS = 444,

        [EnumDescription("SendEmail")]
        SendEmail = 445,

        [EnumDescription("ApplicationServiceMonitor")]
        ApplicationServiceMonitor = 446,

        [EnumDescription("DB2_IsDatabaseActive")]
        DB2_IsDatabaseActive = 447,

        [EnumDescription("DB2_DeactivateDatabase")]
        DB2_DeactivateDatabase = 448,

        [EnumDescription("DB2_VerifyLogGap")]
        DB2_VerifyLogGap = 449,

        [EnumDescription("DB2_VerifyLogPosition")]
        DB2_VerifyLogPosition = 450,

        [EnumDescription("UpdateDROprationStatus")]
        UpdateDROprationStatus = 451,

        [EnumDescription("DataSyncSyncFolder")]
        DataSyncSyncFolder = 452,

        [EnumDescription("IsClusterOnline")]
        IsClusterOnline = 453,

        [EnumDescription("IsClusterOffline")]
        IsClusterOffline = 454,

        [EnumDescription("ReportTestPlan")]
        ReportTestPlan = 455,

        [EnumDescription("ExecuteMSSQLQuery")]
        ExecuteMSSQLQuery = 456,

        [EnumDescription("ExecuteMSSQLSP")]
        ExecuteMSSQLSP = 457,

        [EnumDescription("AttachSqlDatabase")]
        AttachSqlDatabase = 458,

        [EnumDescription("DetachSqlDatabase")]
        DetachSqlDatabase = 459,

        [EnumDescription("ExecuteMSSQLCommand")]
        ExecuteMSSQLCommand = 460,

        [EnumDescription("ExecuteCheckMSSQLCommand")]
        ExecuteCheckMSSQLCommand = 461,

        [EnumDescription("ReProtectRecoveryPlan")]
        ReProtectRecoveryPlan = 462,

        [EnumDescription("TestSSHConnectivity")]
        TestSSHConnectivity = 463,

        [EnumDescription("TestWMIConnectivity")]
        TestWMIConnectivity = 464,

        [EnumDescription("TestSRMConnectivity")]
        TestSRMConnectivity = 465,

        [EnumDescription("TestMSSQLConnectivity")]
        TestMSSQLConnectivity = 466,

        [EnumDescription("TestOracleSSHConnectivity")]
        TestOracleSSHConnectivity = 467,

        [EnumDescription("ChangeServiceStartMode")]
        ChangeServiceStartMode = 468,

        [EnumDescription("VerifyBackupJobStatus")]
        VerifyBackupJobStatus = 469,

        [EnumDescription("FixOrphanUser")]
        FixOrphanUser = 470,

        [EnumDescription("CheckRecoveryPlanState")]
        CheckRecoveryPlanState = 471,

        [EnumDescription("RebootRemoteServer")]
        RebootRemoteServer = 472,

        [EnumDescription("ShutdownRemoteServer")]
        ShutdownRemoteServer = 473,

        [EnumDescription("ExecuteWinSSHCommand")]
        ExecuteWinSSHCommand = 474,

        [EnumDescription("ExecuteCheckWinSSHCommand")]
        ExecuteCheckWinSSHCommand = 475,

        [EnumDescription("ClusterRemoveDependency")]
        ClusterRemoveDependency = 476,

        [EnumDescription("ClusterAddDependency")]
        ClusterAddDependency = 477,

        [EnumDescription("EMC_EXECUTENAVICLICOMMAND")]
        EMC_EXECUTENAVICLICOMMAND = 478,

        [EnumDescription("EMC_EXECUTECHECKNAVICLICOMMAND")]
        EMC_EXECUTECHECKNAVICLICOMMAND = 479,

        [EnumDescription("EMC_EXECUTECHECKREMOTENAVICLICOMMAND")]
        EMC_EXECUTECHECKREMOTENAVICLICOMMAND = 480,

        [EnumDescription("ExecuteSendOSCommand")]
        ExecuteSendOSCommand = 481,

        [EnumDescription("KillOracleSessions")]
        KillOracleSessions = 482,

        [EnumDescription("AIX_ChangeFileText")]
        AIX_ChangeFileText = 483,

        [EnumDescription("Win_ChangeFileText")]
        Win_ChangeFileText = 484,

        [EnumDescription("TIMEFINDER_CREATEDG")]
        TIMEFINDER_CREATEDG = 485,

        [EnumDescription("TIMEFINDER_ADDDEVICETODG")]
        TIMEFINDER_ADDDEVICETODG = 486,

        [EnumDescription("TIMEFINDER_LINK_BCV")]
        TIMEFINDER_LINK_BCV = 487,


        [EnumDescription("TIMEFINDER_ESTABLISHBCV")]
        TIMEFINDER_ESTABLISHBCV = 488,

        [EnumDescription("TIMEFINDER_SPLITBCV")]
        TIMEFINDER_SPLITBCV = 489,

        [EnumDescription("TIMEFINDER_SYNCRONISEDBCV")]
        TIMEFINDER_SYNCRONISEDBCV = 490,

        [EnumDescription("TIMEFINDER_RESTORECHANGETRACK")]
        TIMEFINDER_RESTORECHANGETRACK = 491,

        [EnumDescription("TIMEFINDER_FULLRESTORE")]
        TIMEFINDER_FULLRESTORE = 492,

        [EnumDescription("TIMEFINDER_CHECKBCVSTATUS")]
        TIMEFINDER_CHECKBCVSTATUS = 493,

        [EnumDescription("TIMEFINDER_CREATESNAPVALUME")]
        TIMEFINDER_CREATESNAPVALUME = 494,

        [EnumDescription("TIMEFINDER_STARTSNAPSESSIONFORDEVICE")]
        TIMEFINDER_STARTSNAPSESSIONFORDEVICE = 495,

        [EnumDescription("TIMEFINDER_STARTSNAPSESSIONFORGROUP")]
        TIMEFINDER_STARTSNAPSESSIONFORGROUP = 496,

        [EnumDescription("TIMEFINDER_CREATEVOLUMEINDG")]
        TIMEFINDER_CREATEVOLUMEINDG = 497,

        [EnumDescription("TIMEFINDER_ACTIVATECOPYONWRITE")]
        TIMEFINDER_ACTIVATECOPYONWRITE = 498,

        [EnumDescription("TIMEFINDER_TERMINATESESSION")]
        TIMEFINDER_TERMINATESESSION = 499,

        [EnumDescription("TIMEFINDER_REESTABLISHSNAP")]
        TIMEFINDER_REESTABLISHSNAP = 500,

        [EnumDescription("TIMEFINDER_STARTDIFFERENTIALUPDATE")]
        TIMEFINDER_STARTDIFFERENTIALUPDATE = 501,

        [EnumDescription("TIMEFINDER_RECOVERVOLUME")]
        TIMEFINDER_RECOVERVOLUME = 502,

        [EnumDescription("TIMEFINDER_CREATETARGERDEVICE")]
        TIMEFINDER_CREATETARGERDEVICE = 503,

        [EnumDescription("TIMEFINDER_STARTCLONEFORDEVICE")]
        TIMEFINDER_STARTCLONEFORDEVICE = 504,

        [EnumDescription("TIMEFINDER_BEGINGCOPYANDREFREASH")]
        TIMEFINDER_BEGINGCOPYANDREFREASH = 505,

        [EnumDescription("TIMEFINDER_ACTIVATEREADWRIGHT")]
        TIMEFINDER_ACTIVATEREADWRIGHT = 506,

        [EnumDescription("TIMEFINDER_ACTIVATECLONEPROCESS")]
        TIMEFINDER_ACTIVATECLONEPROCESS = 507,

        [EnumDescription("TIMEFINDER_CHECKCLONESTATUS")]
        TIMEFINDER_CHECKCLONESTATUS = 508,

        [EnumDescription("TIMEFINDER_REFREASHCLONE")]
        TIMEFINDER_REFREASHCLONE = 509,

        [EnumDescription("TIMEFINDER_APPLYDIFFRENTIAL")]
        TIMEFINDER_APPLYDIFFRENTIAL = 510,

        [EnumDescription("TIMEFINDER_RESTOREVOLUMEFORDG")]
        TIMEFINDER_RESTOREVOLUMEFORDG = 511,

        [EnumDescription("TIMEFINDER_RESTOREVOLUMEFORDEVICE")]
        TIMEFINDER_RESTOREVOLUMEFORDEVICE = 512,

        [EnumDescription("TIMEFINDER_CHECKSTATUS")]
        TIMEFINDER_CHECKSTATUS = 513,

        [EnumDescription("TIMEFINDER_SPLITCLONE")]
        TIMEFINDER_SPLITCLONE = 514,

        [EnumDescription("TIMEFINDER_TERMINATECLONE")]
        TIMEFINDER_TERMINATECLONE = 515,

        [EnumDescription("MatchTableCount")]
        MatchTableCount = 516,

        [EnumDescription("UploadDirectory")]
        UploadDirectory = 517,

        [EnumDescription("DownloadDirectory")]
        DownloadDirectory = 518,

        [EnumDescription("DownloadFile")]
        DownloadFile = 519,

        [EnumDescription("UploadFile")]
        UploadFile = 520,

        [EnumDescription("WaitForParallelAction")]
        WaitForParallelAction = 521,

        [EnumDescription("OnLine")]
        OnLine = 522,


        [EnumDescription("OffLine")]
        OffLine = 523,

        [EnumDescription("Import")]
        Import = 524,

        [EnumDescription("CheckDiskStatus")]
        CheckDiskStatus = 525,
        //Win_ChangeFileText = 484,
        //Win_ChangeFileText = 484,

        [EnumDescription("VerifyActiveMBXDBCopyContent")]
        VerifyActiveMBXDBCopyContent = 526,

        [EnumDescription("VerifyActiveMBXDBCopyErrorMessage")]
        VerifyActiveMBXDBCopyErrorMessage = 527,

        [EnumDescription("VerifyDAGStatusOnline")]
        VerifyDAGStatusOnline = 528,


        [EnumDescription("StartedMailBoxServers")]
        StartedMailBoxServers = 529,

        [EnumDescription("StoppedMailBoxServers")]
        StoppedMailBoxServers = 530,

        [EnumDescription("VerifyPassiveMBXCopyQueueLength")]
        VerifyPassiveMBXCopyQueueLength = 531,

        [EnumDescription("VerifyPassiveMBXReplayQueueLength")]
        VerifyPassiveMBXReplayQueueLength = 532,

        [EnumDescription("VerifyPassiveMBXDBCopyStatus")]
        VerifyPassiveMBXDBCopyStatus = 533,

        [EnumDescription("VerifyPassiveMBXDBCopyContent")]
        VerifyPassiveMBXDBCopyContent = 534,

        [EnumDescription("VerifyPassiveMBXDBCopyErrorMessage")]
        VerifyPassiveMBXDBCopyErrorMessage = 535,

        [EnumDescription("MoveActiveMailboxDatabase")]
        MoveActiveMailboxDatabase = 536,

        [EnumDescription("VerifyActiveMBXDBCopyStatus")]
        VerifyActiveMBXDBCopyStatus = 537,

        [EnumDescription("CheckPostgresServiceStatus")]
        CheckPostgresServiceStatus = 538,

        [EnumDescription("ClusterChangeOver")]
        ClusterChangeOver = 539,

        [EnumDescription("StartPostgresService")]
        StartPostgresService = 540,

        [EnumDescription("StopPostgresService")]
        StopPostgresService = 541,

        [EnumDescription("ExecutePostgresSQLCommand")]
        ExecutePostgresSQLCommand = 542,

        [EnumDescription("StopDatabaseAvailabilityGroup")]
        StopDatabaseAvailabilityGroup = 543,

        [EnumDescription("StopDBAvailablityOnProdAndMakeItKnownAtDR")]
        StopDBAvailablityOnProdAndMakeItKnownAtDR = 544,

        [EnumDescription("StopClusterServiceATDR")]
        StopClusterServiceATDR = 545,

        [EnumDescription("RestoreDAGOnDRSite")]
        RestoreDAGOnDRSite = 546,

        [EnumDescription("ActivateDBCopiesOnDRSite")]
        ActivateDBCopiesOnDRSite = 547,

        [EnumDescription("CheckDAGMembershipStatus")]
        CheckDAGMembershipStatus = 548,

        [EnumDescription("CheckHealthyMailboxDatabaseDetails")]
        CheckHealthyMailboxDatabaseDetails = 549,

        [EnumDescription("CheckReplicationHealthStatus")]
        CheckReplicationHealthStatus = 550,

        [EnumDescription("CheckMailboxServerRoleServicesStatus")]
        CheckMailboxServerRoleServicesStatus = 551,

        [EnumDescription("VerifyReplicationServiceonPAM")]
        VerifyReplicationServiceonPAM = 552,

        [EnumDescription("CheckDatabaseMountedStatus")]
        CheckDatabaseMountedStatus = 553,

        [EnumDescription("MoveMailboxDatabaseToDRServer")]
        MoveMailboxDatabaseToDRServer = 554,

        [EnumDescription("ExecutePawerCLIScript")]
        ExecutePawerCLIScript = 555,

        [EnumDescription("ExecutePowerShellCommand")]
        ExecutePowerShellCommand = 556,

        [EnumDescription("StopListnerWithPassword")]
        StopListnerWithPassword = 557,

        [EnumDescription("StartListnerWithPassword")]
        StartListnerWithPassword = 558,

        [EnumDescription("SetDatabaseCopyAutoActivationPolicy")]
        SetDatabaseCopyAutoActivationPolicy = 559,

        [EnumDescription("MoveDefaultofflineAddressBook")]
        MoveDefaultofflineAddressBook = 560,

        [EnumDescription("SetPublicFolderDatabase")]
        SetPublicFolderDatabase = 561,

        [EnumDescription("StopDatabaseAvailabilityGroupOnMailboxServer")]
        StopDatabaseAvailabilityGroupOnMailboxServer = 562,

        [EnumDescription("VerifyMailboxServerEntries")]
        VerifyMailboxServerEntries = 563,

        [EnumDescription("RestoreDatabaseAvailabilityGroup")]
        RestoreDatabaseAvailabilityGroup = 564,

        [EnumDescription("SetDatabaseCopyAutoActivationPolicyBlocked")]
        SetDatabaseCopyAutoActivationPolicyBlocked = 565,

        [EnumDescription("LSSQL_DisableJob")]
        LSSQL_DisableJob = 566,

        [EnumDescription("LSSQL_RunJob")]
        LSSQL_RunJob = 567,

        [EnumDescription("LSSQL_VerifyLogFileSequence")]
        LSSQL_VerifyLogFileSequence = 568,

        [EnumDescription("LSSQL_SetDBSingleUserAcessMode")]
        LSSQL_SetDBSingleUserAcessMode = 569,

        [EnumDescription("LSSQL_VerifyPRDBSingleUserAcessMode")]
        LSSQL_VerifyPRDBSingleUserAcessMode = 570,

        [EnumDescription("LSSQL_KillSessionOnServer")]
        LSSQL_KillSessionOnServer = 571,

        [EnumDescription("LSSQL_GenerateLastBackUpLog")]
        LSSQL_GenerateLastBackUpLog = 572,

        [EnumDescription("LSSQL_SetDBMultiUserAccessMode")]
        LSSQL_SetDBMultiUserAccessMode = 573,

        [EnumDescription("LSSQL_RestoreLastBackupLogwithNoRecovery")]
        LSSQL_RestoreLastBackupLogwithNoRecovery = 574,

        [EnumDescription("LSSQL_RestoreDatabaseWithRecovery")]
        LSSQL_RestoreDatabaseWithRecovery = 575,

        [EnumDescription("LSSQL_RemovePrimarySecondaryLogShipping")]
        LSSQL_RemovePrimarySecondaryLogShipping = 576,

        [EnumDescription("LSSQL_RemovePrimaryLogShipping")]
        LSSQL_RemovePrimaryLogShipping = 577,

        [EnumDescription("LSSQL_RemoveSecondaryLogShipping")]
        LSSQL_RemoveSecondaryLogShipping = 578,

        [EnumDescription("LSSQL_RemoveSecondaryJob")]
        LSSQL_RemoveSecondaryJob = 579,

        [EnumDescription("LSSQL_ExecutePrimaryLogShipping")]
        LSSQL_ExecutePrimaryLogShipping = 580,

        [EnumDescription("LSSQL_ExecuteSecondaryLogShipping")]
        LSSQL_ExecuteSecondaryLogShipping = 581,

        [EnumDescription("LSSQL_UpdatingBackupJobwithPRIPaddress")]
        LSSQL_UpdatingBackupJobwithPRIPaddress = 582,


        [EnumDescription("LSSQL_UpdatingcopyjobwithDRIpaddress")]
        LSSQL_UpdatingcopyjobwithDRIpaddress = 583,

        [EnumDescription("LSSQL_UpdatingRestoreJobwithDRIPAddress ")]
        LSSQL_UpdatingRestoreJobwithDRIPAddress = 584,

        [EnumDescription("LSSQL_fixorphanusers")]
        LSSQL_fixorphanusers = 585,

        [EnumDescription("SnapMirror_Check_Vol_Status")]
        SnapMirror_Check_Vol_Status = 586,

        [EnumDescription("SnapMirror_Check_Lun_Status")]
        SnapMirror_Check_Lun_Status = 587,

        [EnumDescription("CheckStaticRoute")]
        CheckStaticRoute = 588,

        [EnumDescription("CheckVlan")]
        CheckVlan = 589,

        [EnumDescription("CheckADForestFSMORole")]
        CheckADForestFSMORole = 590,

        [EnumDescription("CheckADDomainFSMORole")]
        CheckADDomainFSMORole = 591,

        [EnumDescription("DisableInActiveAccounts")]
        DisableInActiveAccounts = 592,

        [EnumDescription("HP_CheckPowerStatusON")]
        HP_CheckPowerStatusON = 593,

        [EnumDescription("HP_CheckPowerStatusOFF")]
        HP_CheckPowerStatusOFF = 594,

        [EnumDescription("HP_PowerOFFBladeServer")]
        HP_PowerOFFBladeServer = 595,

        [EnumDescription("HP_PowerONBladeServer")]
        HP_PowerONBladeServer = 596,

        [EnumDescription("Nexus_AddRoute")]
        Nexus_AddRoute = 597,

        [EnumDescription("INFOBLOX_AddDNS")]
        INFOBLOX_AddDNS = 598,

        [EnumDescription("INFOBLOX_ModifyDNS")]
        INFOBLOX_ModifyDNS = 599,

        [EnumDescription("INFOBLOX_DeleteDNS")]
        INFOBLOX_DeleteDNS = 600,

        [EnumDescription("INFOBLOX_QueryDNS")]
        INFOBLOX_QueryDNS = 601,

        [EnumDescription("ExecuteSSHSingleSession")]
        ExecuteSSHSingleSession = 602,

        [EnumDescription("LSSQL_MigrateLogins")]
        LSSQL_MigrateLogins = 603,

        [EnumDescription("LSSQL_MigrateRoles")]
        LSSQL_MigrateRoles = 604,

        [EnumDescription("LSSQL_SwitchOver")]
        LSSQL_SwitchOver = 605,

        [EnumDescription("LSSQL_SwitchBack")]
        LSSQL_SwitchBack = 606,

        [EnumDescription("UIAutomation")]
        UIAutomation = 607,

        [EnumDescription("INFOBLOX_DNSQueryStatisticsView")]
        INFOBLOX_DNSQueryStatisticsView = 608,

        [EnumDescription("ShutDown_Client_LPAR")]
        ShutDown_Client_LPAR = 609,

        [EnumDescription("Check_Client_LPAR_Status")]
        Check_Client_LPAR_Status = 610,

        [EnumDescription("Check_Client_BootDisk_Mapping")]
        Check_Client_BootDisk_Mapping = 611,

        [EnumDescription("Check_Ethernet_Status")]
        Check_Ethernet_Status = 612,

        [EnumDescription("Activate_Client_LPAR")]
        Activate_Client_LPAR = 613,

        [EnumDescription("WaitFor_LPAR_State")]
        WaitFor_LPAR_State = 614,

        [EnumDescription("CompareTableCountPR")]
        CompareTableCountPR = 615,

        [EnumDescription("CompareTableCountDR")]
        CompareTableCountDR = 616,

        [EnumDescription("CompareSqlServerTableCountPR")]
        CompareSqlServerTableCountPR = 617,

        [EnumDescription("CompareSqlServerTableCountDR")]
        CompareSqlServerTableCountDR = 618,

        [EnumDescription("BackUpFirewallConfiguration")]
        BackUpFirewallConfiguration = 619,

        [EnumDescription("Start_EC2_Instance")]
        Start_EC2_Instance = 620,

        [EnumDescription("Stop_EC2_Instance")]
        Stop_EC2_Instance = 621,

        [EnumDescription("UpdateFirewallPolicy")]
        UpdateFirewallPolicy = 622,

        [EnumDescription("Check_EC2_Instance_State")]
        Check_EC2_Instance_State = 623,

        [EnumDescription("Is_EC2_Instance_UP")]
        Is_EC2_Instance_UP = 624,

        [EnumDescription("Is_EC2_Instance_Down")]
        Is_EC2_Instance_Down = 625,

        [EnumDescription("Upload_To_S3_Bucket")]
        Upload_To_S3_Bucket = 626,

        [EnumDescription("Download_From_S3_Bucket")]
        Download_From_S3_Bucket = 627,

        [EnumDescription("Modify_EC2_Instance_Size")]
        Modify_EC2_Instance_Size = 628,

        [EnumDescription("Upload_Files_To_S3_Bucket")]
        Upload_Files_To_S3_Bucket = 629,

        [EnumDescription("Download_Files_To_S3_Bucket")]
        Download_Files_To_S3_Bucket = 630,

        [EnumDescription("Add_CName_Record")]
        Add_CName_Record = 631,

        [EnumDescription("Delete_CName_Record")]
        Delete_CName_Record = 632,

        [EnumDescription("Modify_CName_Record")]
        Modify_CName_Record = 633,

        [EnumDescription("Replicate_Posix_File")]
        Replicate_Posix_File = 634,

        [EnumDescription("Replicate_Posix_Folder")]
        Replicate_Posix_Folder = 635,

        [EnumDescription("Linux_Mount")]
        Linux_Mount = 636,

        [EnumDescription("Linux_DisMount")]
        Linux_DisMount = 637,

        [EnumDescription("Database_Switch_Log")]
        Database_Switch_Log = 638,

        [EnumDescription("Recover_Stand_By_Database")]
        Recover_Stand_By_Database = 639,

        [EnumDescription("ShutStandByDB")]
        ShutStandByDB = 640,

        [EnumDescription("GenerateRedoCtrlBKPScriptWin_DS")]
        GenerateRedoCtrlBKPScriptWin_DS = 641,

        [EnumDescription("SwitchShutPRDBWin_DS")]
        SwitchShutPRDBWin_DS = 642,

        [EnumDescription("ExecuteRedoCtrlBKPWin_DS")]
        ExecuteRedoCtrlBKPWin_DS = 643,

        [EnumDescription("StartDBReadWriteWin_DS")]
        StartDBReadWriteWin_DS = 644,

        [EnumDescription("StartDBStandByWin_DS")]
        StartDBStandByWin_DS = 645,

        [EnumDescription("ReplicateDataSyncFoldersDR")]
        ReplicateDataSyncFoldersDR = 646,

        [EnumDescription("ILSSQL_DisableBackUpJob")]
        ILSSQL_DisableBackUpJob = 647,

        [EnumDescription("ILSSQL_RunBackUpJob")]
        ILSSQL_RunBackUpJob = 648,

        [EnumDescription("ILSSQL_VerifyLogFileSequence")]
        ILSSQL_VerifyLogFileSequence = 649,

        [EnumDescription("ILSSQL_SetDBSingleUserAcessModePrimary")]
        ILSSQL_SetDBSingleUserAcessModePrimary = 650,

        [EnumDescription("ILSSQL_VerifyPRDBSingleUserAcessMode")]
        ILSSQL_VerifyPRDBSingleUserAcessMode = 651,

        [EnumDescription("ILSSQL_KillSessionPrimary")]
        ILSSQL_KillSessionPrimary = 652,

        [EnumDescription("ILSSQL_GenerateLastBackUpLog")]
        ILSSQL_GenerateLastBackUpLog = 653,

        [EnumDescription("ILSSQL_SetDBMultiUserAccessModePrimary")]
        ILSSQL_SetDBMultiUserAccessModePrimary = 654,

        [EnumDescription("ILSSQL_RestoreLastBackupLogwithNoRecovery")]
        ILSSQL_RestoreLastBackupLogwithNoRecovery = 655,

        [EnumDescription("ILSSQL_RestoreDatabaseWithRecovery")]
        ILSSQL_RestoreDatabaseWithRecovery = 656,

        [EnumDescription("ILSSQL_RemovePrimarySecondaryLogShipping")]
        ILSSQL_RemovePrimarySecondaryLogShipping = 657,

        [EnumDescription("ILSSQL_RemovePrimaryLogShipping")]
        ILSSQL_RemovePrimaryLogShipping = 658,

        [EnumDescription("ILSSQL_RemoveSecondaryLogShipping")]
        ILSSQL_RemoveSecondaryLogShipping = 659,

        [EnumDescription("ILSSQL_RemoveBackUpJob")]
        ILSSQL_RemoveBackUpJob = 660,

        [EnumDescription("ILSSQL_ExecutePrimaryLogShipping")]
        ILSSQL_ExecutePrimaryLogShipping = 661,

        [EnumDescription("ILSSQL_ExecuteSecondaryLogShipping")]
        ILSSQL_ExecuteSecondaryLogShipping = 662,

        [EnumDescription("ILSSQL_UpdatingBackupJobwithPRIPaddress")]
        ILSSQL_UpdatingBackupJobwithPRIPaddress = 663,

        [EnumDescription("ILSSQL_UpdatingcopyjobwithDRIpaddress")]
        ILSSQL_UpdatingcopyjobwithDRIpaddress = 664,

        [EnumDescription("ILSSQL_UpdatingRestoreJobwithDRIPAddress ")]
        ILSSQL_UpdatingRestoreJobwithDRIPAddress = 665,

        [EnumDescription("ILSSQL_fixorphanusers")]
        ILSSQL_fixorphanusers = 666,

        [EnumDescription("ILSSQL_MigrateLogins")]
        ILSSQL_MigrateLogins = 667,

        [EnumDescription("ILSSQL_MigrateRoles")]
        ILSSQL_MigrateRoles = 668,

        [EnumDescription("ILSSQL_SwitchOver")]
        ILSSQL_SwitchOver = 669,

        [EnumDescription("ILSSQL_SwitchBack")]
        ILSSQL_SwitchBack = 670,

        [EnumDescription("ILSSQL_DisableCopyJob")]
        ILSSQL_DisableCopyJob = 671,

        [EnumDescription("ILSSQL_DisableRestoreJob")]
        ILSSQL_DisableRestoreJob = 672,

        [EnumDescription("ILSSQL_RunCopyJob")]
        ILSSQL_RunCopyJob = 673,

        [EnumDescription("ILSSQL_RunRestoreJob")]
        ILSSQL_RunRestoreJob = 674,

        [EnumDescription("ILSSQL_killSessionDR")]
        ILSSQL_killSessionDR = 675,

        [EnumDescription("ILSSQL_SetDBSingleUserAcessModeDR")]
        ILSSQL_SetDBSingleUserAcessModeDR = 676,

        [EnumDescription("ILSSQL_SetDBMultiUserAccessModeDR")]
        ILSSQL_SetDBMultiUserAccessModeDR = 677,

        [EnumDescription("DT_Start_Job")]
        DT_Start_Job = 678,

        [EnumDescription("DT_Stop_Job")]
        DT_Stop_Job = 679,

        [EnumDescription("DT_Job_PerformFailover")]
        DT_Job_PerformFailover = 680,

        [EnumDescription("DT_Job_PerformFailBack")]
        DT_Job_PerformFailBack = 681,

        [EnumDescription("DT_Job_Restore")]
        DT_Job_Restore = 682,

        [EnumDescription("DTJobCheck_DiskQueueBytes")]
        DTJobCheck_DiskQueueBytes = 683,

        [EnumDescription("DTJobCheck_MirrorBytesRemaining")]
        DTJobCheck_MirrorBytesRemaining = 684,

        [EnumDescription("DTJobCheck_MirrorPermillage")]
        DTJobCheck_MirrorPermillage = 685,

        [EnumDescription("DTJobCheck_Health")]
        DTJobCheck_Health = 686,

        [EnumDescription("DTJobCheck_HighLevelState")]
        DTJobCheck_HighLevelState = 687,

        [EnumDescription("DTJobCheck_MirrorState")]
        DTJobCheck_MirrorState = 688,

        [EnumDescription("DTJobCheck_CanFailover")]
        DTJobCheck_CanFailover = 689,

        [EnumDescription("DTJobCheck_CanFailback")]
        DTJobCheck_CanFailback = 690,

        [EnumDescription("DTJobCheck_CanStart")]
        DTJobCheck_CanStart = 691,

        [EnumDescription("DTJobCheck_CanStop")]
        DTJobCheck_CanStop = 692,

        [EnumDescription("DTJobCheck_CanRestore")]
        DTJobCheck_CanRestore = 693,

        [EnumDescription("DTJobCheck_IsInError")]
        DTJobCheck_IsInError = 694,

        [EnumDescription("DTJobCheck_NotIsInError")]
        DTJobCheck_NotIsInError = 695,

        [EnumDescription("DBOffline")]
        DBOffline = 696,

        [EnumDescription("VerifyDatabaseStatus")]
        VerifyDatabaseStatus = 697,

        [EnumDescription("VerifyLunsStatus")]
        VerifyLunsStatus = 698,

        [EnumDescription("InitialiseSnapMirror")]
        InitialiseSnapMirror = 699,

        [EnumDescription("VerifyDisk")]
        VerifyDisk = 700,

        [EnumDescription("RescanAllAdaptors")]
        RescanAllAdaptors = 701,

        [EnumDescription("VerifyAttachStatus")]
        VerifyAttachStatus = 702,

        [EnumDescription("VerifyStorageLun")]
        VerifyStorageLun = 703,

        [EnumDescription("VerifyVolumeMount")]
        VerifyVolumeMount = 704,

        [EnumDescription("MountUsingVMFSUUID")]
        MountUsingVMFSUUID = 705,

        [EnumDescription("GetVMIDFromImage")]
        GetVMIDFromImage = 706,

        [EnumDescription("AddAdditionalDisk")]
        AddAdditionalDisk = 707,

        [EnumDescription("VerifyDeviceAssociatedToDisk")]
        VerifyDeviceAssociatedToDisk = 708,

        [EnumDescription("SetDBSingleUserAccessMode")]
        SetDBSingleUserAccessMode = 709,

        [EnumDescription("AttachLuns")]
        AttachLuns = 710,

        [EnumDescription("SetDBOptionSingleUser")]
        SetDBOptionSingleUser = 711,

        [EnumDescription("Check_Azure_VMstatus")]
        Check_Azure_VMstatus = 712,

        [EnumDescription("Check_Azure_VMPowerState")]
        Check_Azure_VMPowerState = 713,

        [EnumDescription("Start_AzureVM")]
        Start_AzureVM = 714,

        [EnumDescription("Stop_AzureVM")]
        Stop_AzureVM = 715,

        [EnumDescription("Change_AzureVMSize")]
        Change_AzureVMSize = 716,

        [EnumDescription("ExecuteDS_Make_Flash")]
        ExecuteDS_Make_Flash = 717,

        [EnumDescription("ExecuteDS_ChVolGroupAdd")]
        ExecuteDS_ChVolGroupAdd = 718,

        [EnumDescription("ExecuteDS_ChVolGroupRemove")]
        ExecuteDS_ChVolGroupRemove = 719,

        [EnumDescription("ExecuteDS_Check_NoFlashCopy")]
        ExecuteDS_Check_NoFlashCopy = 720,

        [EnumDescription("ExecuteDS_WaitForNoFlashCopy")]
        ExecuteDS_WaitForNoFlashCopy = 721,

        [EnumDescription("Mount_VolumeGroups")]
        Mount_VolumeGroups = 722,

        [EnumDescription("UnMount_VolumeGroups")]
        UnMount_VolumeGroups = 723,

        [EnumDescription("VaryOff_VolumeGroups")]
        VaryOff_VolumeGroups = 724,

        [EnumDescription("Export_VolumeGroups")]
        Export_VolumeGroups = 725,

        [EnumDescription("ExecuteDS_SetFlashRevertible")]
        ExecuteDS_SetFlashRevertible = 726,

        [EnumDescription("VaryOn_VolumeGroups")]
        VaryOn_VolumeGroups = 727,

        [EnumDescription("VerifyMountStatus")]
        VerifyMountStatus = 728,

        [EnumDescription("RemoveAdditionalDisk")]
        RemoveAdditionalDisk = 729,

        [EnumDescription("AttachDB")]
        AttachDB = 730,

        [EnumDescription("RMDEVS_VolumeGroups")]
        RMDEVS_VolumeGroups = 731,

        [EnumDescription("ILSSQL_RemoveCopyJob")]
        ILSSQL_RemoveCopyJob = 732,

        [EnumDescription("ILSSQL_RemoveRestoreJob")]
        ILSSQL_RemoveRestoreJob = 733,

        [EnumDescription("DTVerifyJobName")]
        DTVerifyJobName = 734,

        [EnumDescription("DTVerifyReplicationStatus")]
        DTVerifyReplicationStatus = 735,

        [EnumDescription("DTVerifyReplicationQueue")]
        DTVerifyReplicationQueue = 736,

        [EnumDescription("CheckDSTracks_LSPPRC")]
        CheckDSTracks_LSPPRC = 737,

        [EnumDescription("RemoveHDisk")]
        RemoveHDisk = 738,

        [EnumDescription("KillMssqlProcessByName")]
        KillMssqlProcessByName = 739,

        [EnumDescription("DatabaseMirroring-SwitchOver")]
        DatabaseMirroringSwitchOver = 740,

        [EnumDescription("DatabaseMirroring-CheckStatus")]
        DatabaseMirroringCheckStatus = 741,

        [EnumDescription("VerifyUnmountStatus")]
        VerifyUnmountStatus = 742,

        [EnumDescription("NepApp_VM_UnMount")]
        NepApp_VM_UnMount = 743,

        [EnumDescription("MirroringFailOver")]
        MirroringFailOver = 744,

        [EnumDescription("VerifyDatabaseMirroringStatus")]
        VerifyDatabaseMirroringStatus = 745,

        [EnumDescription("SQLNLS_CheckDatabaseState")]
        SQLNLS_CheckDatabaseState = 746,

        [EnumDescription("SQLNLS_CheckDatabaseMode")]
        SQLNLS_CheckDatabaseMode = 747,

        [EnumDescription("ExecuteCPL")]
        ExecuteCPL = 748,

        [EnumDescription("ExecuteCheckSybaseISqlCommand")]
        ExecuteCheckSybaseIsqlcommand = 749,

        [EnumDescription("ExecuteSybaseISqlCommand")]
        ExecuteSybaseIsqlCommand = 750,

        [EnumDescription("VerifySybaseDataServerStatus")]
        VerifySybaseDataServerStatus = 751,

        [EnumDescription("VerifyBackupServerStatus")]
        VerifyBackupServerStatus = 752,

        [EnumDescription("VerifyDBStatus")]
        VerifyDBStatus = 753,

        [EnumDescription("ExecuteSybaseBcpOutCommand")]
        ExecuteSybaseBcpOutCommand = 754,

        [EnumDescription("ExecuteSybaseBcpInCommand")]
        ExecuteSybaseBcpInCommand = 755,

        [EnumDescription("GenerateSybaselastTransactionLog")]
        GenerateSybaselastTransactionLog = 756,

        [EnumDescription("CopySybaselastTransactionLog")]
        CopySybaselastTransactionLog = 757,

        [EnumDescription("LoadTransactionlogFile")]
        LoadTransactionlogFile = 758,

        [EnumDescription("SybaseOnlineDB")]
        SybaseOnlineDB = 759,

        [EnumDescription("SybaseOnlineDBforStandbyaccess")]
        SybaseOnlineDBforStandbyaccess = 760,

        [EnumDescription("DumpTransactionLogfilewithStandbyaccess")]
        DumpTransactionLogfilewithStandbyaccess = 761,

        [EnumDescription("CompareFile")]
        CompareFile = 762,

        [EnumDescription("CompareFolder")]
        CompareFolder = 763,

        [EnumDescription("ReplicateSybaseBcpTable")]
        ReplicateSybaseBcpTable = 764,

        // DB2 windows

        [EnumDescription("Win_VerifyDB2DatabaseStatus")]
        Win_VerifyDB2DatabaseStatus = 766,


        [EnumDescription("Win_VerifyDB2HADRStatus")]
        Win_VerifyDB2HADRStatus = 767,

        [EnumDescription("Win_VerifyDB2HADRDBRole")]
        Win_VerifyDB2HADRDBRole = 768,

        [EnumDescription("Win_VerifyDB2HADRPeerStae")]
        Win_VerifyDB2HADRPeerState = 769,

        [EnumDescription("Win_DeactivateDB2HADRDatabase")]
        Win_DeactivateDB2HADRDatabase = 770,

        [EnumDescription("Win_VerifyDB2HADRDBStatus")]
        Win_VerifyDB2HADRDBStatus = 771,

        [EnumDescription("Win_VerifyDB2HADRActivateDB")]
        Win_VerifyDB2HADRActivateDB = 772,

        [EnumDescription("Win_VerifySimilarEntryOfLogFileNameOnPrimaryAndStandby")]
        Win_VerifySimilarEntryOfLogFileNameOnPrimaryAndStandby = 773,

        [EnumDescription("Win_VerifyDB2LogFileNameEntryDR")]
        Win_VerifyDB2LogFileNameEntryDR = 774,

        [EnumDescription("Win_VerifySimilarPageNumberEntryOfPrimaryAndStandby")]
        Win_VerifySimilarPageNumberEntryOfPrimaryAndStandby = 775,

        [EnumDescription("Win_VerifyDB2PageNumberEntryDR")]
        Win_VerifyDB2PageNumberEntryDR = 776,

        [EnumDescription("Win_VerifySimilarLogFileLSNEntry")]
        Win_VerifySimilarLogFileLSNEntry = 777,

        [EnumDescription("Win_VerifyDB2LogFileLSNEntryDR")]
        Win_VerifyDB2LogFileLSNEntryDR = 778,

        [EnumDescription("Win_VerifyDB2LogGAPEntry")]
        Win_VerifyDB2LogGAPEntry = 779,

        [EnumDescription("Win_DB2_QuieseDatabase")]
        Win_DB2_QuieseDatabase = 780,

        [EnumDescription("Win_DB2_UnQuieseDatabase")]
        Win_DB2_UnQuieseDatabase = 781,

        [EnumDescription("Win_TerminateDB2Session")]
        Win_TerminateDB2Session = 782,

        [EnumDescription("Win_DeactivateDB")]
        Win_DeactivateDB = 783,

        [EnumDescription("Win_DB2TakeOver")]
        Win_DB2TakeOver = 784,

        [EnumDescription("Win_HADRRoleChangeover")]
        Win_HADRRoleChangeover = 785,

        [EnumDescription("Verify_MySQLServicestatus")]
        MySQLServicestatus = 786,

        [EnumDescription("Verify_MySQLSlaveStatus")]
        MySQLSlaveStatus = 787,

        [EnumDescription("Verify_SlaveIOState")]
        SlaveIOState = 788,

        [EnumDescription("Verify_SlaveIORunningStatus")]
        SlaveIORunningStatus = 789,

        [EnumDescription("Verify_SlaveSQLRunningStatus")]
        SlaveSQLRunningStatus = 790,

        [EnumDescription("Verify_ConnectStatus")]
        ConnectStatus = 791,

        [EnumDescription("Verify_MasterlogFile")]
        MasterlogFile = 792,


        [EnumDescription("RelayMasterlogFile")]
        RelayMasterlogFile = 793,



        [EnumDescription("Verify_ExecMasterLogPosition")]
        ExecMasterLogPosition = 794,

        [EnumDescription("Verify_ReadMasterLogPosition")]
        ReadMasterLogPosition = 795,

        [EnumDescription("Stop_Slave")]
        Stop_Slave = 796,

        [EnumDescription("Master_RelayMasterlogFile")]
        Master_RelayMasterlogFile = 797,

        [EnumDescription("Master_ReadMasterlogPosition")]
        Master_ReadMasterlogPosition = 798,

        //Master_ReadMasterlogPosition
        #region Hyper_V


        [EnumDescription("PrepareVMFailover")]
        PrepareVMFailover = 799,

        [EnumDescription("StartVMFailover")]
        StartVMFailover = 800,

        [EnumDescription("CheckVMReplicationState")]
        CheckVMReplicationState = 801,

        [EnumDescription("CheckVMReplicationMode")]
        CheckVMReplicationMode = 802,

        [EnumDescription("SetVMReverseReplication")]
        SetVMReverseReplication = 803,

        [EnumDescription("hypervStartVM")]
        hypervStartVM = 804,

        [EnumDescription("hypervStopVM")]
        hypervStopVM = 805,

        [EnumDescription("CheckHyperVReplication")]
        CheckHyperVReplication = 806,

        [EnumDescription("CheckHyperVVM")]
        CheckHyperVVM = 807,

        [EnumDescription("CheckHyperExecuteCommand")]
        CheckHyperExecuteCommand = 808,

        [EnumDescription("VerifyForwardReplicationConnection")]
        VerifyForwardReplicationConnection = 809,


        [EnumDescription("VerifyReverseReplicationConnection")]
        VerifyReverseReplicationConnection = 810,

        [EnumDescription("CheckVMState")]
        CheckVMState = 811,

        [EnumDescription("VerifyPreparedForFailoverStatus")]
        VerifyPreparedForFailoverStatus = 812,

        [EnumDescription("VerifyFailedOverWaitingCompletionStatus")]
        VerifyFailedOverWaitingCompletionStatus = 813,

        #endregion

        [EnumDescription("SybaseCheckPointDB")]
        SybaseCheckPointDB = 814,

        [EnumDescription("SybaseShutdownDB")]
        SybaseShutdownDB = 815,

        [EnumDescription("VerifySybaseServerIsDown")]
        VerifySybaseServerIsDown = 816,

        [EnumDescription("VerifySybaseServerIsUp")]
        VerifySybaseServerIsUp = 817,

        [EnumDescription("StartSybaseServer")]
        StartSybaseServer = 818,

        [EnumDescription("Verify_Exist_Status_of_VirtualSwitch")]
        Verify_Exist_Status_of_VirtualSwitch = 819,

        [EnumDescription("Verify_VMNetwork_Adapter_Status")]
        Verify_VMNetwork_Adapter_Status = 820,

        [EnumDescription("Verify_VMNetwork_Adapter_VLANID_ConnectionStatus")]
        Verify_VMNetwork_Adapter_VLANID_ConnectionStatus = 821,

        [EnumDescription("Set_Network_Adapter_Connection")]
        Set_Network_Adapter_Connection = 822,

        [EnumDescription("Set_VLANID_To_VirtualSwitch")]
        Set_VLANID_To_VirtualSwitch = 823,

        [EnumDescription("DisconnectNetworkAdapterConnection")]
        DisconnectNetworkAdapterConnection = 824,

        [EnumDescription("LSSQL_ExecutePrimaryLogShipping_JobSchedule")]
        LSSQL_ExecutePrimaryLogShipping_JobSchedule = 825,

        [EnumDescription("LSSQL_ExecuteSecondaryLogShipping_JobSchedule")]
        LSSQL_ExecuteSecondaryLogShipping_JobSchedule = 826,

        [EnumDescription("CheckSqlServerRunningStates")]
        CheckSqlServerRunningStates = 827,

        [EnumDescription("CheckDatabaseRoleandMirrorState")]
        CheckDatabaseRoleandMirrorState = 828,

        [EnumDescription("ExecuteDBClusterChangeOver")]
        ExecuteDBClusterChangeOver = 829,

        [EnumDescription("VerifyDatabaseMirrorState")]
        VerifyDatabaseMirrorState = 830,

        [EnumDescription("SetDatabaseAvailablityGroup")]
        SetDatabaseAvailablityGroup = 832,


        [EnumDescription("VerifyDAGPrameterValue")]
        VerifyDAGPrameterValue = 833,

        [EnumDescription("StopDAGOnMailBoxServer")]
        StopDAGOnMailBoxServer = 834,

        [EnumDescription("StopDAGActiveDirSiteConfigOnly")]
        StopDAGActiveDirSiteConfigOnly = 835,

        [EnumDescription("SetPreferredADServer")]
        SetPreferredADServer = 836,

        [EnumDescription("RestoreDAGActiveDirSite")]
        RestoreDAGActiveDirSite = 837,

        [EnumDescription("CheckMailBoxDBStatusPrameterValue")]
        CheckMailBoxDBStatusPrameterValue = 838,


        [EnumDescription("StartDAGOnMailBoxServer")]
        StartDAGOnMailBoxServer = 839,

        [EnumDescription("MoveClusterGroup")]
        MoveClusterGroup = 840,

        [EnumDescription("Move_ActiveMailBoxDatabase")]
        Move_ActiveMailBoxDatabase = 841,

        [EnumDescription("ApplicationDataSync")]
        ApplicationDataSync = 842,

        [EnumDescription("ScanFileFromApplicationServer")]
        ScanFileFromApplicationServer = 843,

        [EnumDescription("DownloadResponseFileFromProducationServer")]
        DownloadResponseFileFromProducationServer = 844,

        [EnumDescription("CreateCSVFileFromRESP")]
        CreateCSVFileFromRESP = 845,

        [EnumDescription("MoveCSVFileToDBServer")]
        MoveCSVFileToDBServer = 846,

        [EnumDescription("FileReplicationApplicationToDBServer")]
        FileReplicationApplicationToDBServer = 847,

        [EnumDescription("ProvisionVM_Esxi")]
        ProvisionVM_Esxi = 848,

        [EnumDescription("CheckUserMailBoxExist")]
        CheckUserMailBoxExist = 849,

        [EnumDescription("CheckUserMoveReqCanBeCreated")]
        CheckUserMoveReqCanBeCreated = 850,

        [EnumDescription("GetMoveReqStatus")]
        GetMoveReqStatus = 851,

        [EnumDescription("RemoveUserMailBoxMoveReqIfExsit")]
        RemoveUserMailBoxMoveReqIfExsit = 852,

        [EnumDescription("SuspendWhenReadyToCompleteMode")]
        SuspendWhenReadyToCompleteMode = 853,

        [EnumDescription("SuspendWhenReadyToCompleteModeStatus")]
        SuspendWhenReadyToCompleteModeStatus = 854,

        [EnumDescription("GetMoveReqStatics")]
        GetMoveReqStatics = 855,

        [EnumDescription("ResumeMoveMailBoxReq")]
        ResumeMoveMailBoxReq = 856,

        [EnumDescription("LSSQL_EnableJob")]
        LSSQL_EnableJob = 857,

        [EnumDescription("LSSQL_VerifyLogFileSequenceforFailover")]
        LSSQL_VerifyLogFileSequenceforFailover = 858,

        [EnumDescription("LSSQL_Make_DBWritable_LastRestoredFile_Failover")]
        LSSQL_Make_DBWritable_LastRestoredFile_Failover = 859,

        [EnumDescription("LSSQL_VerifyDatabaseWriteStatus")]
        LSSQL_VerifyDatabaseWriteStatus = 860,

        [EnumDescription("LSSQL_Check_Primary_SecondaryLogshipping_Exist")]
        LSSQL_Check_Primary_SecondaryLogshipping_Exist = 861,

        [EnumDescription("LSSQL_Check_DBentry_OnSecondaryServer_Exist")]
        LSSQL_Check_DBEntry_OnSecondaryServer_Exist = 862,

        [EnumDescription("LSSQL_Check_PrimaryLogshipping_Exist")]
        LSSQL_Check_PrimaryLogshipping_Exist = 863,

        [EnumDescription("LSSQL_Check_DbEntry_OnPrimaryServer_Exist")]
        LSSQL_Check_DbEntry_OnPrimaryServer_Exist = 864,

        [EnumDescription("LSSQL_Verify_Primary_SecondarylogShipping_Exist")]
        LSSQL_Verify_Primary_SecondarylogShipping_Exist = 865,

        [EnumDescription("LSSQL_Verify_Primarylogshipping_Exist")]
        LSSQL_Verify_Primarylogshipping_Exist = 866,

        [EnumDescription("LSSQL_Verify_DBEntry_OnSecondaryServer_Exist")]
        LSSQL_Verify_DBEntry_OnSecondaryServer_Exist = 867,

        [EnumDescription("LSSQL_Verify_DBEntry_OnPrimaryServer_Exist")]
        LSSQL_Verify_DBEntry_OnPrimaryServer_Exist = 868,

        [EnumDescription("LSSQL_KillDBSessionWithTimeOut")]
        LSSQL_KillDBSessionWithTimeOut = 869,


        [EnumDescription("PAIX_LPAR_POWER_OFF")]
        PAIX_LPAR_POWER_OFF = 870,

        [EnumDescription("PAIX_LPAR_POWER_CHECK_ON")]
        PAIX_LPAR_POWER_CHECK_ON = 871,

        [EnumDescription("PAIX_LPAR_POWER_CHECK_OFF")]
        PAIX_LPAR_POWER_CHECK_OFF = 872,

        [EnumDescription("LDM_StartDomain")]
        LDM_StartDomain = 873,

        [EnumDescription("LDM_StopDomain")]
        LDM_StopDomain = 874,

        [EnumDescription("LDM_BindDomain")]
        LDM_BindDomain = 875,

        [EnumDescription("LDM_UnbindDomain")]
        LDM_UnbindDomain = 876,

        [EnumDescription("LDM_RemoveDomain")]
        LDM_RemoveDomain = 877,

        [EnumDescription("LDM_AddDomain")]
        LDM_AddDomain = 878,
        [EnumDescription("LDM_ADDCPU")]
        LDM_ADDCPU = 879,
        [EnumDescription("LDM_ADDMemory")]
        LDM_ADDMemory = 880,

        [EnumDescription("LDOM_Domain_IsAvailable")]
        LDOM_Domain_IsAvailable = 881,

        [EnumDescription("LDOM_Domain_IsNotAvailable")]
        LDOM_Domain_IsNotAvailable = 882,

        [EnumDescription("ExecuteOpsCenterCommand")]
        ExecuteOpsCenterCommand = 883,

        [EnumDescription("PowerOffAsset")]
        PowerOffAsset = 884,

        [EnumDescription("PowerOnAsset")]
        PowerOnAsset = 885,

        [EnumDescription("RebootAsset")]
        RebootAsset = 886,

        [EnumDescription("ILOM_PowerOff")]
        ILOM_PowerOff = 887,

        [EnumDescription("ILOM_SystemStatus")]
        ILOM_SystemStatus = 888,

        [EnumDescription("ILOM_PowerOn")]
        ILOM_PowerOn = 889,

        [EnumDescription("ExecuteCheckOpsCenterCommand")]
        ExecuteCheckOpsCenterCommand = 890,

        [EnumDescription("PutAssetInMaintenanceMode")]
        PutAssetInMaintenanceMode = 891,

        [EnumDescription("RemoveAssetInMaintenanceMode")]
        RemoveAssetInMaintenanceMode = 892,

        [EnumDescription("ManageAnAsset")]
        ManageAnAsset = 893,

        [EnumDescription("UnamageAnAsset")]
        UnamageAnAsset = 894,

        [EnumDescription("RefreshAnAsset")]
        RefreshAnAsset = 895,

        [EnumDescription("UpdateAnAsset")]
        UpdateAnAsset = 896,

        [EnumDescription("ExecutePlan")]
        ExecutePlan = 897,

        [EnumDescription("PAIX_LPAR_POWER_ON")]
        PAIX_LPAR_POWER_ON = 898,

        [EnumDescription("UpdateWindowsHostName")]

        UpdateWindowsHostName = 899,

        [EnumDescription("VerifySybasePrimaryDBStatus")]
        VerifySybasePrimaryDBStatus = 900,

        [EnumDescription("VerifySybaseStandbyDBStatus")]
        VerifySybaseStandbyDBStatus = 901,

        [EnumDescription("SybaseStopReplicationAgentOnPrimary")]
        SybaseStopReplicationAgentOnPrimary = 902,

        [EnumDescription("VerifySybaseReplicationAgentisDown")]
        VerifySybaseReplicationAgentisDown = 903,

        [EnumDescription("SybasePrimaryToStandby")]
        SybasePrimaryToStandby = 904,

        [EnumDescription("SybaseReplicationSwitchOverStatus")]
        SybaseReplicationSwitchOverStatus = 905,

        [EnumDescription("SybaseReplicationCheckRoleSwitchStatus")]
        SybaseReplicationCheckRoleSwitchStatus = 906,

        [EnumDescription("SybaseStartReplicationAgentOnPrimary")]
        SybaseStartReplicationAgentOnPrimary = 907,

        [EnumDescription("SybaseReplicationResumeConnection")]
        SybaseReplicationResumeConnection = 908,

        [EnumDescription("ExecuteCheckCommand")]
        ExecuteCheckCommand = 909,

        [EnumDescription("StartMAXDBDatabase")]
        StartMAXDBDatabase = 910,

        [EnumDescription("StopMAXDBDataBase")]
        StopMAXDBDataBase = 911,

        [EnumDescription("VerifyDataBaseState")]
        VerifyDataBaseState = 912,

        [EnumDescription("Vcenter_PowerOnVM")]
        Vcenter_PowerOnVM = 970,

        [EnumDescription("Vcenter_PowerOffVM")]
        Vcenter_PowerOffVM = 971,

        [EnumDescription("VMCheckRunning")]
        VMCheckRunning = 972,

        [EnumDescription("VMIsAvailable")]
        VMIsAvailable = 973,

        [EnumDescription("RemoveVM")]
        RemoveVM = 974,

        [EnumDescription("Execute5250TransactionCountCheckPopup")]
        Execute5250TransactionCountCheckPopup = 975,

        [EnumDescription("Execute5250Process")]
        Execute5250Process = 976,

        [EnumDescription("Start_eBDR")]
        Start_eBDR = 977,

        [EnumDescription("Stop_eBDR")]
        Stop_eBDR = 978,

        [EnumDescription("Resume_eBDR")]
        Resume_eBDR = 979,

        [EnumDescription("Pause_eBDR")]
        Pause_eBDR = 980,

        [EnumDescription("Cancle_eBDR")]
        Cancle_eBDR = 981,

        [EnumDescription("ExecuteBase24Command")]
        ExecuteBase24Command = 982,

        [EnumDescription("ExecuteBase24CheckCommand")]
        ExecuteBase24CheckCommand = 983,

        [EnumDescription("ExecuteDRNetCheckRecords")]
        ExecuteDRNetCheckRecords = 984,

        [EnumDescription("ExecuteDRNetCheckStatusState")]
        ExecuteDRNetCheckStatusState = 985,

        [EnumDescription("ExecuteDRNetCheckAuditModeStatus")]
        ExecuteDRNetCheckAuditModeStatus = 986,

        [EnumDescription("ExecuteDRNetCheckCollectorFileMapMode")]
        ExecuteDRNetCheckCollectorFileMapMode = 987,

        [EnumDescription("ExecuteDRNetCheckDistributorFileMapStatusQueue")]
        ExecuteDRNetCheckDistributorFileMapStatusQueue = 988,

        [EnumDescription("Execute_SAF_COUNT_CHECK")]
        Execute_SAF_COUNT_CHECK = 989,

        [EnumDescription("Execute_TXN_QUE_CHECK")]
        Execute_TXN_QUE_CHECK = 990,

        [EnumDescription("Execute_STATUS_NODE_CHECK")]
        Execute_STATUS_NODE_CHECK = 991,

        [EnumDescription("VerifyAppVersionCommand")]
        VerifyAppVersionCommand = 992,

        [EnumDescription("VerifyAppPasswordCommand")]
        VerifyAppPasswordCommand = 993,

        [EnumDescription("DRReady")]
        DRReady = 994,

        [EnumDescription("CreateLinkedClone")]
        CreateLinkedClone = 995,

        [EnumDescription("ExecuteCheckVMCommand")]
        ExecuteCheckVMCommand = 996,

        [EnumDescription("CheckVMExist")]
        CheckVMExist = 997,

        [EnumDescription("CheckVMToolStatus")]
        CheckVMToolStatus = 998,

        [EnumDescription("CheckSqlServerRunningStates1")]
        CheckSqlServerRunningStates1 = 999,

        [EnumDescription("CheckDatabaseRoleandMirrorState1")]
        CheckDatabaseRoleandMirrorState1 = 1000,

        [EnumDescription("ExecuteDBClusterChangeOver1")]
        ExecuteDBClusterChangeOver1 = 1001,

        [EnumDescription("VerifyDatabaseMirrorState1")]
        VerifyDatabaseMirrorState1 = 1002,

        //MSSQLAlwayson
        [EnumDescription("ExecuteAlwaysOnForceFailover")]
        ExecuteAlwaysOnForceFailover = 1003,

        [EnumDescription("ExecuteAlwaysonNormalFailover")]
        ExecuteAlwaysonNormalFailover = 1004,

        [EnumDescription("CheckRole")]
        CheckRole = 1005,

        [EnumDescription("CheckAvailabilityMode")]
        CheckAvailabilityMode = 1006,

        [EnumDescription("Verify3DRSiteLogFileSequence")]
        Verify3DRSiteLogFileSequence = 1007,

        [EnumDescription("RestoreLogWithStandby")]
        RestoreLogWithStandby = 1008,

        [EnumDescription("Execute3DRSitePrimaryLogShipping")]
        Execute3DRSitePrimaryLogShipping = 1009,

        [EnumDescription("Execute3SiteSecondaryLogShipping")]
        Execute3SiteSecondaryLogShipping = 1010,

        [EnumDescription("LSSQL_RestoreLastBackupLogWithRecovery")]
        LSSQL_RestoreLastBackupLogWithRecovery = 1011,

        [EnumDescription("Execute2DRSitePrimaryLogShipping")]
        Execute2DRSitePrimaryLogShipping = 1012,

        [EnumDescription("SoftLayer_PowerONVirtualGuest")]
        SoftLayer_PowerONVirtualGuest = 1013,

        [EnumDescription("SoftLayer_PowerOFFVirtualGuest")]
        SoftLayer_PowerOFFVirtualGuest = 1014,

        [EnumDescription("SoftLayer_CheckVirtualGuestPowerON")]
        SoftLayer_CheckVirtualGuestPowerON = 1015,

        [EnumDescription("SoftLayer_CheckVirtualGuestPowerOFF")]
        SoftLayer_CheckVirtualGuestPowerOFF = 1016,

        [EnumDescription("SoftLayer_UpgradeVirtualGuest")]
        SoftLayer_UpgradeVirtualGuest = 1017,

        [EnumDescription("SoftLayer_ProvisionVirtualGuest")]
        SoftLayer_ProvisionVirtualGuest = 1018,

        [EnumDescription("SoftLayer_UpgradeVirtualMachineByIds")]
        SoftLayer_UpgradeVirtualMachineByIds = 1019,

        [EnumDescription("SoftLayer_ProvisionVirtualMachineByIds")]
        SoftLayer_ProvisionVirtualMachineByIds = 1020,

        [EnumDescription("SoftLayer_UpgradeVirtualMachineCPU")]
        SoftLayer_UpgradeVirtualMachineCPU = 1021,

        [EnumDescription("SoftLayer_UpgradeVirtualMachineMemory")]
        SoftLayer_UpgradeVirtualMachineMemory = 1022,

        [EnumDescription("CompareFileORFolder")]
        CompareFileORFolder = 1023,

        [EnumDescription("VerifyGroupState")]
        VerifyGroupState = 1024,

        [EnumDescription("VerifyGroupStatistics")]
        VerifyGroupStatistics = 1025,

        [EnumDescription("PauseGroupDataTransfer")]
        PauseGroupDataTransfer = 1026,

        [EnumDescription("StartGroupDataTransfer")]
        StartGroupDataTransfer = 1027,

        [EnumDescription("EnableGroupImageAccess")]
        EnableGroupImageAccess = 1028,

        [EnumDescription("DisableGroupImageAccess")]
        DisableGroupImageAccess = 1029,

        [EnumDescription("SetProductionCopy")]
        SetProductionCopy = 1030,

        [EnumDescription("FailoverProtection")]
        FailoverProtection = 1031,

        [EnumDescription("ExecuteCheckXCLICommand")]
        ExecuteCheckXCLICommand = 1032,

        [EnumDescription("MSSQLlinkstatus")]
        MSSQLlinkstatus = 1033,



        #region VCenter
        [EnumDescription("Vcenter_VMPowerOnVM")]
        Vcenter_VMPowerOnVM = 1034,

        [EnumDescription("Vcenter_VMPowerOffVM")]
        Vcenter_VMPowerOffVM = 1035,

        [EnumDescription("Vcenter_VMCheckRunning")]
        Vcenter_VMCheckRunning = 1036,

        [EnumDescription("Vcenter_CheckVMPowerState")]
        Vcenter_CheckVMPowerState = 1037,

        [EnumDescription("Vcenter_RemoveVM")]
        Vcenter_RemoveVM = 1038,

        [EnumDescription("Vcenter_CreateLinkedClone")]
        Vcenter_CreateLinkedClone = 1039,

        [EnumDescription("Vcenter_CheckVMExist")]
        Vcenter_CheckVMExist = 1040,

        [EnumDescription("Vcenter_CheckVMToolStatus")]
        Vcenter_CheckVMToolStatus = 1041,

        [EnumDescription("Vcenter_RemoveSnapshot")]
        Vcenter_RemoveSnapshot = 1042,

        [EnumDescription("Vcenter_ExecuteCheckVMCommand")]
        Vcenter_ExecuteCheckVMCommand = 1043,

        #endregion

        #region SRM

        //SRM
        [EnumDescription("SRM_CheckRecoveryPlanState")]
        SRM_CheckRecoveryPlanState = 1044,

        [EnumDescription("SRM_CheckProtectionGroupState")]
        SRM_CheckProtectionGroupState = 1045,

        [EnumDescription("SRM_ExecuteCleanupRecoveryPlan")]
        SRM_ExecuteCleanupRecoveryPlan = 1046,

        [EnumDescription("SRM_PerformPlannedMigrationFailover")]
        SRM_PerformPlannedMigrationFailover = 1047,

        [EnumDescription("SRM_PerformDisasterRecoveryFailover")]
        SRM_PerformDisasterRecoveryFailover = 1048,

        [EnumDescription("SRM_ExecuteTestRecoveryPlan")]
        SRM_ExecuteTestRecoveryPlan = 1049,

        [EnumDescription("SRM_ReProtectRecoveryPlan")]
        SRM_ReProtectRecoveryPlan = 1050,
        #endregion

        [EnumDescription("ChangeGuestVMHostName")]
        ChangeGuestVMHostName = 1051,

        [EnumDescription("JoinGuestVMSystemToDomain")]
        JoinGuestVMSystemToDomain = 1052,

        [EnumDescription("RemoveGuestVMSystemFromDomain")]
        RemoveGuestVMSystemFromDomain = 1053,

        [EnumDescription("VerifyBackupDirectoryandSharePath")]
        VerifyBackupDirectoryandSharePath = 1054,

        [EnumDescription("VerifySourceAndDestbackupDirectory")]
        VerifySourceAndDestbackupDirectory = 1055,

        [EnumDescription("VerifyJobNameAssociatedwithLogshipping")]
        VerifyJobNameAssociatedwithLogshipping = 1056,

        //[EnumDescription("TestFailover")]
        //TestFailover = 1057,

        //[EnumDescription("UndoFailover")]
        //UndoFailover = 1058,

        #region PowerCLI

        [EnumDescription("RescanHBAToFindNewStorageLUN")]
        RescanHBAToFindNewStorageLUN = 1059,

        [EnumDescription("RegisterVM")]
        RegisterVM = 1060,

        [EnumDescription("UnRegisterVM")]
        UnRegisterVM = 1061,

        [EnumDescription("UnRegisterDATASTORE")]
        UnRegisterDATASTORE = 1062,

        [EnumDescription("PowerOnVM")]
        PowerOnVM = 1063,

        [EnumDescription("PowerOnDATASTORE")]
        PowerOnDATASTORE = 1064,

        [EnumDescription("PowerOffVM")]
        PowerOffVM = 1065,

        [EnumDescription("PowerOffDATASTORE")]
        PowerOffDATASTORE = 1066,

        [EnumDescription("AttachLUN")]
        AttachLUN = 1067,

        [EnumDescription("DeAttachLUN")]
        DeAttachLUN = 1068,

        [EnumDescription("StartVSphereHAAgentService")]
        StartVSphereHAAgentService = 1069,

        [EnumDescription("StopVSphereHAAgentService")]
        StopVSphereHAAgentService = 1070,

        [EnumDescription("MountDataStore")]
        MountDataStore = 1071,

        [EnumDescription("UnMountDataStore")]
        UnMountDataStore = 1072,

        [EnumDescription("CheckDataStoreCanBeUnmount")]
        CheckDataStoreCanBeUnmount = 1073,

        [EnumDescription("ReScanVMFSforStorageLUN")]
        ReScanVMFSforStorageLUN = 1074,


        #endregion

        [EnumDescription("DiskOnlinewithDriveLetter")]
        DiskOnlinewithDriveLetter = 1075,

        [EnumDescription("StartIISWebsite")]
        StartIISWebsite = 1076,

        [EnumDescription("StopIISWebsite")]
        StopIISWebsite = 1077,
        //hyper v

        [EnumDescription("VerifyClusterSharedVolumeDiskinFailoverCluster")]
        VerifyClusterSharedVolumeDiskinFailoverCluster = 1078,

        [EnumDescription("VerifyDiskStatusOnClusterSharedVolume")]
        VerifyDiskStatusOnClusterSharedVolume = 1079,

        [EnumDescription("CheckVirtualHDSharingOptionStatusNoDuplicateVMS")]
        CheckVirtualHDSharingOptionStatusNoDuplicateVMS = 1080,

        [EnumDescription("CheckVirtualHDSharingOptionStatusDuplicateVMS")]
        CheckVirtualHDSharingOptionStatusDuplicateVMS = 1081,

        [EnumDescription("AddNewVirtualHDSharingFixedLocationNumber")]
        AddNewVirtualHDSharingFixedLocationNumber = 1082,

        [EnumDescription("AddNewVirtualHardDiskSharingFixedLocationNumberDuplicateVMS")]
        AddNewVirtualHDSharingFLNDuplicateVMS = 1083,

        [EnumDescription("RemoveVirtualSharingHardDisk")]
        RemoveVirtualSharingHardDisk = 1084,

        [EnumDescription("RemoveVirtualSharingHardDiskDuplicateVMS")]
        RemoveVirtualSharingHardDiskDuplicateVMS = 1085,

        [EnumDescription("DisableVirtualHarddiskSharingOptiontoHyper-VVM")]
        DisableVirtualHDSharingOptiontoHyperVVM = 1086,

        [EnumDescription("DisableVirtualHDSharingOptiontoHyper-VVMDuplicateVMS")]
        DisableVirtualHDSharingOptiontoHyperVVMDuplicateVMS = 1087,

        [EnumDescription("EnableVirtualHarddiskSharingOptiontoHyper-VVM")]
        EnableVirtualHDSharingOptiontoHyperVVM = 1088,

        [EnumDescription("EnableVirtualHarddiskSharingOptiontoHyper-VVMDuplicateVMS")]
        EnableVirtualHarddiskSharingOptiontoHyperVVMDuplicateVMS = 1089,


        [EnumDescription("PreFlightCheckDatabaseSyncState")]
        PreFlightCheckDatabaseSyncState = 1090,

        //powerCLI new action
        [EnumDescription("RegisterVMWithoutESXiHost")]
        RegisterVMWithoutESXiHost = 1095,

        [EnumDescription("AtatchLunOnMultipleESXiHost")]
        AtatchLunOnMultipleESXiHost = 1096,

        [EnumDescription("MountDatastoreOnAllEsxiHost")]
        MountDatastoreOnAllEsxiHost = 1097,

        [EnumDescription("UnmounFromAllEsxiHost")]
        UnmounFromAllEsxiHost = 1098,

        #region ExecuteCheckOSCommandWithPassword

        [EnumDescription("ExecuteCheckOSCommandWithPassword")]
        ExecuteCheckOSCommandWithPassword = 1099,

        [EnumDescription("ExecuteOSCommandWithPassword")]
        ExecuteOSCommandWithPassword = 1100,

        [EnumDescription("ExecuteOSZFSCommand")]
        ExecuteOSZFSCommand = 1101,

        #endregion

        #region MssqlClusterAddRemoveDisk

        [EnumDescription("ADDClusterResourceToClusterOwnerGroup")]
        ADDClusterResourceToClusterOwnerGroup = 1102,

        [EnumDescription("RemoveClusterResourceFromClusterOwnerGroup")]
        RemoveClusterResourceFromClusterOwnerGroup = 1103,

        [EnumDescription("VerifyClusterResourceDependencyInDependentResource")]
        VerifyClusterResourceDependencyInDependentResource = 1104,

        [EnumDescription("ADDClusterResourceDependencyToDependentResource")]
        ADDClusterResourceDependencyToDependentResource = 1105,

        [EnumDescription("RemoveClusterResourceDependencyFromDependentResource")]
        RemoveClusterResourceDependencyFromDependentResource = 1106,

        #endregion

        //end 

        //hyperv start vm and stop vm 
        [EnumDescription("Start_VM")]
        Start_VM = 1107,

        [EnumDescription("Shutdown_VM")]
        Shutdown_VM = 1108,

        //ODGBroker Action
        [EnumDescription("DGBSwitchOver")]
        DGBSwitchOver = 1109,
        [EnumDescription("DGBFailOver")]
        DGBFailOver = 1110,
        [EnumDescription("DGBConvertSnapshotStandby")]
        DGBConvertSnapshotStandby = 1111,
        [EnumDescription("DGBConvertPhysicalStandby")]
        DGBConvertPhysicalStandby = 1112,
        [EnumDescription("DGBConfigurationIsOK")]
        DGBConfigurationIsOK = 1113,
        [EnumDescription("DGBValidateDB")]
        DGBValidateDB = 1114,

        #region RecoverPiont New Action
        [EnumDescription("IsGroupSync")]
        IsGroupSync = 1115,
        [EnumDescription("VerifyLoggedAccess")]
        VerifyLoggedAccess = 1116,
        [EnumDescription("VerifyDirectAccess")]
        VerifyDirectAccess = 1117,
        [EnumDescription("VerifyDataTransfer")]
        VerifyDataTransfer = 1118,
        [EnumDescription("FailoverProtectionGroup_set")]
        FailoverProtectionGroup_set = 1119,

        #endregion

        #region --EMCIsilon--
        [EnumDescription("ModifySyncPolicySchedule")]
        ModifySyncPolicySchedule = 1120,

        [EnumDescription("RunSyncJob")]
        RunSyncJob = 1121,

        [EnumDescription("SyncPolicyAllowWrites")]
        SyncPolicyAllowWrites = 1122,

        [EnumDescription("FailoverSyncPolicy")]
        FailoverSyncPolicy = 1123,

        #endregion

        #region EMC Star Actions
        [EnumDescription("VerifyProtectStatus")]
        VerifyProtectStatus = 1124,
        [EnumDescription("HaltCG")]
        HaltCG = 1125,
        [EnumDescription("SwitchSite")]
        SwitchSite = 1126,
        [EnumDescription("ConnectSite")]
        ConnectSite = 1127,
        [EnumDescription("ProtectSite")]
        ProtectSite = 1128,
        [EnumDescription("EnableStar")]
        EnableStar = 1129,
        [EnumDescription("DisableStar")]
        DisableStar = 1130,
        [EnumDescription("Isolate")]
        Isolate = 1131,
        [EnumDescription("SplitStatus")]
        SplitStatus = 1132,
        [EnumDescription("DisconnectSite")]
        DisconnectSite = 1133,
        [EnumDescription("UnprotectSite")]
        UnprotectSite = 1134,

        #endregion EMC Star Actions

        #region--ASM oracle datasyn action

        [EnumDescription("PRPreShutRedoCtrlScript")]
        PRPreShutRedoCtrlScript = 1135,

        [EnumDescription("PreShutDB")]
        PreShutDB = 1136,

        [EnumDescription("ShutDownPrimaryDB")]
        ShutDownPrimaryDB = 1137,

        [EnumDescription("BackUpRedoControlFile")]
        BackUpRedoControlFile = 1138,

        [EnumDescription("CreateControlScript")]
        CreateControlScript = 1139,

        [EnumDescription("RecoverStandByDB")]
        RecoverStandByDB = 1140,

        [EnumDescription("ShutDownStandByDB")]
        ShutDownStandByDB = 1141,

        [EnumDescription("CreateControlFileFromTraceFile")]
        CreateControlFileFromTraceFile = 1142,

        [EnumDescription("ASMRecoverDatabase")]
        ASMRecoverDatabase = 1143,

        [EnumDescription("ASMOpenDatabase")]
        ASMOpenDatabase = 1144,

        [EnumDescription("AddTempTableSpace")]
        AddTempTableSpace = 1145,

        [EnumDescription("StartUpMount")]
        StartUpMount = 1146,

        [EnumDescription("ShutDownPRDB")]
        ShutDownPRDB = 1147,

        [EnumDescription("ASMCreateStandByControlFile")]
        ASMCreateStandByControlFile = 1148,

        [EnumDescription("StartUpNoMount")]
        StartUpNoMount = 1149,

        [EnumDescription("ASMRestoreStandByControlFile")]
        ASMRestoreStandByControlFile = 1150,

        [EnumDescription("MountStandByDatabase")]
        MountStandByDatabase = 1151,

        #endregion

        #region OracleDG12C

        [EnumDescription("OracleDG12C_DGVerifySwitchover")]
        OracleDG12C_DGVerifySwitchover = 1152,

        [EnumDescription("OracleDG12C_DGSwitchover")]
        OracleDG12C_DGSwitchover = 1153,

        [EnumDescription("OracleDG12C_ShutdownDB")]
        OracleDG12C_ShutdownDB = 1154,

        [EnumDescription("OracleDG12C_StartDB")]
        OracleDG12C_StartDB = 1155,

        [EnumDescription("OracleDG12C_MountDB")]
        OracleDG12C_MountDB = 1156,

        [EnumDescription("OracleDG12C_StartRecovery")]
        OracleDG12C_StartRecovery = 1157,


        #endregion OracleDG12C

        [EnumDescription("ExecutePowerShellCommand1")]
        ExecutePowerShellCommand1 = 1158,

        [EnumDescription("ExecuteAndChkOutputPowerShellCmd")]
        ExecuteAndChkOutputPowerShellCmd = 1159,

        //Veeam Action 
        [EnumDescription("FailoverReplicationJob")]
        FailoverReplicationJob = 1160,

        [EnumDescription("FailBackReplicationJob")]
        FailBackReplicationJob = 1161,

        [EnumDescription("FailBackCommitReplicationJob")]
        FailBackCommitReplicationJob = 1162,

        [EnumDescription("CreateReplicationJobFromBackupFiles")]
        CreateReplicationJobFromBackupFiles = 1163,

        [EnumDescription("CreateReplicationJobUseProdVMState")]
        CreateReplicationJobUseProdVMState = 1164,

        //GoldenGate   
        [EnumDescription("CheckGoldenGateGroupStatus")]
        CheckGoldenGateGroupStatus = 1166,

        [EnumDescription("CheckGoldenGateGroupRBASync")]
        CheckGoldenGateGroupRBASync = 1167,

        [EnumDescription("StopGoldenGateGroup")]
        StopGoldenGateGroup = 1168,

        [EnumDescription("StartGoldenGateGroup")]
        StartGoldenGateGroup = 1169,

        [EnumDescription("LSSQL_ExecutePrimaryLogShipping_Reverse")]
        LSSQL_ExecutePrimaryLogShipping_Reverse = 1170,

        [EnumDescription("LSSQL_ExecuteSecondaryLogShipping_Reverse")]
        LSSQL_ExecuteSecondaryLogShipping_Reverse = 1171,

        //Rsync
        [EnumDescription("Verify_Connectivity")]
        Verify_Connectivity = 1172,

        [EnumDescription("Verify_Sync_Status")]
        Verify_Sync_Status = 1173,

        [EnumDescription("Execute_RsyncJob")]
        Execute_RsyncJob = 1174,

        [EnumDescription("Stop_Rsync_Replication")]
        Stop_Rsync_Replication = 1175,

        [EnumDescription("VerifyDatabaseClusterStatus")]
        VerifyDatabaseClusterStatus = 1176,

        [EnumDescription("VerifyPRReplicationStatus")]
        VerifyPRReplicationStatus = 1177,

        [EnumDescription("VerifyDRReplicationStatus")]
        VerifyDRReplicationStatus = 1178,

        [EnumDescription("VerifyPRAndDRWalLSNMatches")]
        VerifyPRAndDRWalLSNMatches = 1179,

        [EnumDescription("CreateRecoveryConfAtPR")]
        CreateRecoveryConfAtPR = 1180,

        [EnumDescription("StopPRPostgreSQLServer")]
        StopPRPostgreSQLServer = 1181,

        [EnumDescription("VerifyPRDBClusterShutdown")]
        VerifyPRDBClusterShutdown = 1182,

        [EnumDescription("VerifyDRTriggerFilePath")]
        VerifyDRTriggerFilePath = 1183,

        [EnumDescription("ExecuteDRFailingOverToStandbyServer")]
        ExecuteDRFailingOverToStandbyServer = 1184,

        [EnumDescription("RestartPRPostgreSQLServer")]
        RestartPRPostgreSQLServer = 1185,

        [EnumDescription("StartPRPostgreSQLServer")]
        StartPRPostgreSQLServer = 1186,

        [EnumDescription("VerifyPostGresSQL_XLogLSNMatching")]
        VerifyPostGresSQL_XLogLSNMatching = 1187,

        [EnumDescription("VerifyPostGresSQL_RecoveryRunningStatus")]
        VerifyPostGresSQL_RecoveryRunningStatus = 1188,

        [EnumDescription("ExecuteCheckMysqlDBCommand")]
        ExecuteCheckMysqlDBCommand = 1189,

        [EnumDescription("ExecuteMysqlDBCommand")]
        ExecuteMysqlDBCommand = 1190,

        [EnumDescription("VerifyMasterLogFileAndPositiononMasterSlaveServer")]
        VerifyMasterLogFileAndPositiononMasterSlaveServer = 1191,

        [EnumDescription("ChangeMASTERTOMasterHostAndLogFile")]
        ChangeMASTERToMasterHostAndLogFile = 1192,

        //VeemWF WF action
        [EnumDescription("CheckReplicationJob")]
        CheckReplicationJob = 1193,

        [EnumDescription("CheckReplicationJobLastState")]
        CheckReplicationJobLastState = 1194,

        [EnumDescription("CheckReplicationJobLastResult")]
        CheckReplicationJobLastResult = 1195,

        [EnumDescription("CheckReplicatMonitoringStatus")]
        CheckReplicatMonitoringStatus = 1196,

        [EnumDescription("VBRVMWareReplicaFailoverDROnly")]
        VBRVMWareReplicaFailoverDROnly = 1197,

        [EnumDescription("VBVMWareReplicaFailoverPlanned")]
        VBVMWareReplicaFailoverPlanned = 1198,

        [EnumDescription("VBRVMWareReplicaFailoverPermanent")]
        VBRVMWareReplicaFailoverPermanent = 1199,

        [EnumDescription("VBRVMWareReplicaFAILBACKToOriginalVM")]
        VBRVMWareReplicaFAILBACKToOriginalVM = 1200,

        [EnumDescription("VBRHyperVReplicaUndoFailover")]
        VBRVMwareReplicaUndoFailover = 1201,

        [EnumDescription("VBRHyperVReplicaUndoFailback")]
        VBRVMWareReplicaUndoFailback = 1202,

        [EnumDescription("EnableScheduledTask")]
        EnableScheduledTask = 1203,

        [EnumDescription("DisableScheduledTask")]
        DisableScheduledTask = 1204,

        [EnumDescription("StartScheduledTask")]
        StartScheduledTask = 1205,

        [EnumDescription("StopScheduledTask")]
        StopScheduledTask = 1206,

        [EnumDescription("CheckScheduledTaskStatus")]
        CheckScheduledTaskStatus = 1207,



        ////Rsync
        //[EnumDescription("Verify_Connectivity")]
        //Verify_Connectivity = 1203,

        //[EnumDescription("Verify_Sync_Status")]
        //Verify_Sync_Status = 1204,

        //[EnumDescription("Execute_RsyncJob")]
        //Execute_RsyncJob = 1205,

        //[EnumDescription("Stop_Rsync_Replication")]
        //Stop_Rsync_Replication = 1206,


        #region ActifioDBBackUp

        [EnumDescription("Actifio_DatabaseBackUp")]
        Actifio_DatabaseBackUp = 1207,

        #endregion

        #region WebLogic

        [EnumDescription("StartAdminServer")]
        StartAdminServer = 1208,

        [EnumDescription("StopAdminServer")]
        StopAdminServer = 1209,

        [EnumDescription("StartNodeManagerService")]
        StartNodeManagerService = 1210,

        [EnumDescription("StopNodeManagerService")]
        StopNodeManagerService = 1211,

        [EnumDescription("StartManagedServer")]
        StartManagedServer = 1212,

        [EnumDescription("StopManagedServer")]
        StopManagedServer = 1213,

        [EnumDescription("StartHTTPServer")]
        StartHTTPServer = 1214,

        [EnumDescription("StopHTTPServer")]
        StopHTTPServer = 1215,

        #endregion

        #region HP3PAR

        //[EnumDescription("VerifyrcopyGroupSyncStatus")]
        //VerifyrcopyGroupSyncStatus = 1216,

        //[EnumDescription("SyncRemoteCopyGroup")]
        //SyncRemoteCopyGroup = 1217,

        //[EnumDescription("StopRemoteCopyGroup")]
        //StopRemoteCopyGroup = 1218,

        //[EnumDescription("FailoverRemoteCopyGroup")]
        //FailoverRemoteCopyGroup = 1219,

        //[EnumDescription("RecoverRemoteCopyGroup")]
        //RecoverRemoteCopyGroup = 1220,

        //[EnumDescription("RestoreRemoteCopyGroup")]
        //RestoreRemoteCopyGroup = 1221,
        #endregion

        #region HPE_3PAR


        [EnumDescription("CheckRCopyGroupSyncStatus")]
        CheckRCopyGroupSyncStatus = 1216,

        [EnumDescription("ExecuteSyncRCopy")]
        ExecuteSyncRCopy = 1217,

        [EnumDescription("StopRCopyGroup")]
        StopRCopyGroup = 1218,

        [EnumDescription("SetRCopyGroupFailOver")]
        SetRCopyGroupFailOver = 1219,

        [EnumDescription("SetRCopyGroupRestore")]
        SetRCopyGroupRestore = 1220,



        #endregion HPE_3PAR

        [EnumDescription("TransferOrSeizureFSMORole")]
        TransferOrSeizureFSMORole = 1222,


        #region Nutanix

        [EnumDescription("Check_VM_State")]
        Check_VM_State = 1223,

        [EnumDescription("ExecuteVMPowerOn")]
        ExecuteVMPowerOn = 1224,

        [EnumDescription("ExecuteVMPowerOff")]
        ExecuteVMPowerOff = 1225,

        [EnumDescription("CheckProtectionDomainStatus")]
        CheckProtectionDomainStatus = 1226,

        [EnumDescription("CheckCGExistInPDForActiveSite")]
        CheckCGExistInPDForActiveSite = 1227,

        [EnumDescription("CheckCGExistInPDForInactiveSite")]
        CheckCGExistInPDForInactiveSite = 1228,

        [EnumDescription("CheckVMExistInCGUnderPDForActiveSite")]
        CheckVMExistInCGUnderPDForActiveSite = 1229,

        [EnumDescription("CheckVMExistInCGUnderPDForInactiveSite")]
        CheckVMExistInCGUnderPDForInactiveSite = 1230,

        [EnumDescription("VerifyIfNoReplIsPendingUnderPDForActiveSite")]
        VerifyIfNoReplIsPendingUnderPDForActiveSite = 1231,

        [EnumDescription("VerifyIfNoReplIsPendingUnderPDForInactiveSite")]
        VerifyIfNoReplIsPendingUnderPDForInactiveSite = 1232,

        [EnumDescription("MigrateProtectionDomain")]
        MigrateProtectionDomain = 1233,


        #endregion nutanix

        #region OracleSnapShot

        [EnumDescription("VerifyDBModeBeforRevertSnapshot")]
        VerifyDBModeBeforRevertSnapshot = 1234,

        [EnumDescription("VerifyDBModeBeforSnapshot")]
        VerifyDBModeBeforSnapshot = 1235,

        [EnumDescription("VerifyDataGuardStatus")]
        VerifyDataGuardStatus = 1236,

        [EnumDescription("VerifyDBRecoveryFileDest")]
        VerifyDBRecoveryFileDest = 1237,

        [EnumDescription("VerifyDBRecoveryFileDestSize")]
        VerifyDBRecoveryFileDestSize = 1238,

        [EnumDescription("VerifyDBFlashBackRetentionTarget")]
        VerifyDBFlashBackRetentionTarget = 1239,

        [EnumDescription("ConvertSnapshotStandby")]
        ConvertSnapshotStandby = 1240,

        [EnumDescription("VerifyCurrentScn")]
        VerifyCurrentScn = 1241,

        [EnumDescription("VerifyDBModeAfterSnapshot")]
        VerifyDBModeAfterSnapshot = 1242,

        [EnumDescription("VerifyDBRoleAfterSnapshot")]
        VerifyDBRoleAfterSnapshot = 1243,

        [EnumDescription("ConvertSnapToPhysicalStandby")]
        ConvertSnapToPhysicalStandby = 1244,

        [EnumDescription("CheckFlashBackOff")]
        CheckFlashBackOff = 1245,

        [EnumDescription("VerifyDBRoleBeforSnapshot")]
        VerifyDBRoleBeforSnapshot = 1246,

        [EnumDescription("VerifyDBRoleBeforReverteSnapshot")]
        VerifyDBRoleBeforReverteSnapshot = 1247,

        [EnumDescription("VerifyDBRoleAfterRevertSnapshot")]
        VerifyDBRoleAfterRevertSnapshot = 1248,

        [EnumDescription("VerifyDBModeAfterRevertSnapshot")]
        VerifyDBModeAfterRevertSnapshot = 1249,

        #endregion

        #region - hyper v
        //hyper v Action 
        [EnumDescription("ChangeClusterHyperVVMIPAddressWithMacAddress")]
        ChangeClusterHyperVVMIPAddressWithMacAddress = 1250,

        [EnumDescription("ChangeClusterHyperVVMIPAddressWithOutMacAddress")]
        ChangeClusterHyperVVMIPAddressWithOutMacAddress = 1251,


        [EnumDescription("ChangeClusterHyperVVMIPAddressWithMacAddressSingleNet")]
        ChangeClusterHyperVVMIPAddressWithMacAddressSingleNet = 1252,

        [EnumDescription("ChangeClusterHyperVVMIPAddressWithOutMacAddressSingleNet")]
        ChangeClusterHyperVVMIPAddressWithOutMacAddressSingleNet = 1253,

        [EnumDescription("ChangeDNSIP")]
        ChangeDNSIP = 1254,


        //HyperV Cluster

        [EnumDescription("Verify_State")]
        Verify_State = 1255,
        [EnumDescription("Verify_Replication_State")]
        Verify_Replication_State = 1256,
        [EnumDescription("Verify_Replication_Mode")]
        Verify_Replication_Mode = 1257,
        [EnumDescription("Verify_Primary_Replication_Mode")]
        Verify_Primary_Replication_Mode = 1258,
        [EnumDescription("Verify_Forward_Repli_Connection_config")]
        Verify_Forward_Repli_Connection_config = 1259,
        [EnumDescription("Verify_Reverse_Repli_Connection_config")]
        Verify_Reverse_Repli_Connection_config = 1260,
        //[EnumDescription("Shutdown_VM")]
        //Shutdown_VM = 1269,
        [EnumDescription("Verify_status_of_VM")]
        Verify_status_of_VM = 1261,
        [EnumDescription("Start_Failover")]
        Start_Failover = 1262,
        [EnumDescription("Verify_Prepared_For_Failover_status")]
        Verify_Prepared_For_Failover_status = 1263,
        [EnumDescription("Fails_Over_Replica_VM")]
        Fails_Over_Replica_VM = 1264,
        [EnumDescription("Verify_FailedOverWaitingCompletion_Status")]
        Verify_FailedOverWaitingCompletion_Status = 1265,
        [EnumDescription("Change_Replication_mode")]   //
        Change_Replication_mode = 1266,
        //[EnumDescription("Start_VM")]
        //Start_VM = 1049,
        [EnumDescription("VerifyHyperVVMDisConnectedStatus")]
        VerifyHyperVVMDisConnectedStatus = 1267,
        [EnumDescription("VerifyHyperVVMConnectedStatus")]
        VerifyHyperVVMConnectedStatus = 1268,
        [EnumDescription("VerifyHyperVVMVLANIDConnectedStatus")]
        VerifyHyperVVMVLANIDConnectedStatus = 1269,
        [EnumDescription("ConnectionToVirtualSwitchOnHyperVVM")]
        ConnectionToVirtualSwitchOnHyperVVM = 1270,
        [EnumDescription("SetVLANIDToVirtualSwitchOnHyperVVM")]
        SetVLANIDToVirtualSwitchOnHyperVVM = 1271,
        [EnumDescription("DisconnectionToHyperVVM")]
        DisconnectionToHyperVVM = 1272,
        [EnumDescription("Cancel_FailOver_Cluster_HyperVVM")]
        Cancel_FailOver_Cluster_HyperVVM = 1273,
        [EnumDescription("Cancel_FailOver_Standalone_HyperVVM")]
        Cancel_FailOver_Standalone_HyperVVM = 1274,
        //[EnumDescription("ChangeDNSIP")]
        //ChangeDNSIP = 1285,
        [EnumDescription("HyperVResumeReplication")]
        HyperVResumeReplication = 1275,

        #endregion

        #region ExchangeDAG_ActiveDirectory_DNS_Savola

        [EnumDescription("CheckActiveDirectoryReplicationSyncAllStatus")]
        CheckActiveDirectoryReplicationSyncAllStatus = 1276,

        [EnumDescription("CheckActiveDirectoryReplicationStatus")]
        CheckActiveDirectoryReplicationStatus = 1277,


        //DNS
        [EnumDescription("DNSVerify")]
        DNSVerify = 1278,
        [EnumDescription("DNSModify")]
        DNSModify = 1279,
        [EnumDescription("ModifyArecordwithTTL")]
        ModifyArecordwithTTL = 1280,
        [EnumDescription("ModifyCNAMEwithTTL")]
        ModifyCNAMEwithTTL = 1281,
        [EnumDescription("VerifyDNSCNAERecord")]
        VerifyDNSCNAERecord = 1282,

        [EnumDescription("DNSServerSetTTLValueforDNSSOA")]
        DNSServerSetTTLValueforDNSSOA = 1283,


        //ExchangeDAG_F/O Actions MaheshSingh
        [EnumDescription("VerifyTestReplicationHealthPassedStatusExceptDatabaseAvailability")]
        VerifyTestReplicationHealthPassedStatusExceptDatabaseAvailability = 1284,

        [EnumDescription("VerifyDatabaseCopyActivationPolicySettingforMailboxServer")]
        VerifyDatabaseCopyActivationPolicySettingforMailboxServer = 1285,

        [EnumDescription("StopDAGatPrimarySiteWithoutConfigurationOnly")]
        StopDAGatPrimarySiteWithoutConfigurationOnly = 1286,

        [EnumDescription("StopDAGatPrimarySiteWithConfigurationOnly")]
        StopDAGatPrimarySiteWithConfigurationOnly = 1287,

        [EnumDescription("CheckSiteLevelMailboxServerStatusunderDAG")]
        CheckSiteLevelMailboxServerStatusunderDAG = 1288,

        [EnumDescription("CheckMailboxServerMessageQueueCount")]
        CheckMailboxServerMessageQueueCount = 1289,

        [EnumDescription("RedirectMailboxServerMessageQueuetoOtherServer")]
        RedirectMailboxServerMessageQueuetoOtherServer = 1290,

        [EnumDescription("MoveActiveMailboxDatabasewithSkipOptions")]
        MoveActiveMailboxDatabasewithSkipOptions = 1291,

        [EnumDescription("VerifyServerComponentActiveStateforallComponent")]
        VerifyServerComponentActiveStateforallComponent = 1292,

        [EnumDescription("VerifyPrimaryActiveManagerStateatPR")]
        VerifyPrimaryActiveManagerStateatPR = 1293,

        [EnumDescription("MovePrimaryActiveManagertoTarget")]
        MovePrimaryActiveManagertoTarget = 1294,

        [EnumDescription("CheckTransportSeverQueueStatus")]
        CheckTransportSeverQueueStatus = 1295,

        [EnumDescription("VerifyMailboxDBMountedStatusatPR_SiteLevel")]
        VerifyMailboxDBMountedStatusatPR_SiteLevel = 1296,

        [EnumDescription("VerifyMailboxDBMountedStatusatPR_ServerLevel")]
        VerifyMailboxDBMountedStatusatPR_ServerLevel = 1297,

        [EnumDescription("VerifyMailboxDBHealthyStatusatDR_SiteLevel")]
        VerifyMailboxDBHealthyStatusatDR_SiteLevel = 1298,

        [EnumDescription("VerifyMailboxDBHealthyStatusatDR_ServerLevel")]
        VerifyMailboxDBHealthyStatusatDR_ServerLevel = 1299,

        [EnumDescription("VerifyReplayQueueStatusforAllMailboxDB")]
        VerifyReplayQueueStatusforAllMailboxDB = 1300,

        [EnumDescription("VerifyContentIndexStateasHealthyforallMailboxDB")]
        VerifyContentIndexStateasHealthyforallMailboxDB = 1301,


        #endregion ExchangeDAG_ActiveDirectory_DNS_Savola

        #region VeritasVR

        [EnumDescription("VVR_GetServiceGroupStateOnSpecifiedNode")]
        VVR_GetServiceGroupStateOnSpecifiedNode = 1302,

        [EnumDescription("VVR_CheckServiceGroupStateOfClusterNode")]
        VVR_CheckServiceGroupStateOfClusterNode = 1303,

        [EnumDescription("VVR_SwitchNodeStateinCluster")]
        VVR_SwitchNodeStateinCluster = 1304,

        [EnumDescription("VVR_CheckLinkStatsReplicationStatus")]
        VVR_CheckLinkStatsReplicationStatus = 1305,

        [EnumDescription("VVR_CheckLinkStatsUpToDateStatus")]
        VVR_CheckLinkStatsUpToDateStatus = 1306,

        #endregion VeritasVR

        #region RP4VM

        [EnumDescription("RP4VM_EnableLatestImage")]
        RP4VM_EnableLatestImage = 1307,

        [EnumDescription("RP4VM_FailoverUsingPredefinedNetwork")]
        RP4VM_FailoverUsingPredefinedNetwork = 1308,

        [EnumDescription("RP4VM_StartReplicaTransfer")]
        RP4VM_StartReplicaTransfer = 1309,

        #endregion RP4VM

        #region NLS WithNoRecovery

        [EnumDescription("LSSQL_GenerateLastLogBackupWithNoRecovery")]
        LSSQL_GenerateLastLogBackupWithNoRecovery = 1310,

        #endregion NLS WithNoRecovery

        #region Win2k8

        [EnumDescription("Win2k8_VerifyClusterResourceState")]
        Win2k8_VerifyClusterResourceState = 1311,

        [EnumDescription("Win2k8_StopClusterResource")]
        Win2k8_StopClusterResource = 1312,

        [EnumDescription("Win2k8_StartClusterResource")]
        Win2k8_StartClusterResource = 1313,

        #endregion Win2k8

        [EnumDescription("DiskRescan")]
        DiskRescan = 1314,

        #region DNS

        [EnumDescription("ChangeDNSGlueRecord")]
        ChangeDNSGlueRecord = 1315,

        #endregion DNS

        #region Filehandling

        [EnumDescription("RenameFile")]
        FRenameFile = 1316,

        [EnumDescription("CopyFile")]
        CopyFile = 1317,

        [EnumDescription("RenameFolder")]
        RenameFolder = 1318,

        [EnumDescription("CopyFolder")]
        CopyFolder = 1319,

        [EnumDescription("StartApplicationPool")]
        StartApplicationPool = 1320,

        [EnumDescription("StopApplicationPool")]
        StopApplicationPool = 1321,

        #endregion Filehandling

        #region MSSQL Alwayson

        [EnumDescription("ManualPlannedFailover")]
        ManualPlannedFailover = 1322,

        [EnumDescription("ForceFullyFailover")]
        ForceFullyFailover = 1323,

        [EnumDescription("SuspendReplicationAllDBinAvailabilityGrup")]
        SuspendReplicationAllDBinAvailabilityGrup = 1324,

        [EnumDescription("ResumeReplication")]
        ResumeReplication = 1325,

        [EnumDescription("CheckSuspendedState")]
        CheckSuspendedState = 1326,

        [EnumDescription("CheckResumed")]
        CheckResumed = 1327,

        [EnumDescription("CheckAvailabilityGroupStatusDR")]
        CheckAvailabilityGroupStatusDR = 1328,

        [EnumDescription("CheckAvailabilityGroupStatusPR")]
        CheckAvailabilityGroupStatusPR = 1329,

        [EnumDescription("CheckAvailabilityGroupStatusEnableDisable")]
        CheckAvailabilityGroupStatusEnableDisable = 1330,

        [EnumDescription("CheckAllowAllConnectionsPRDR")]
        CheckAllowAllConnectionsPRDR = 1331,

        [EnumDescription("TestallavailabilityreplicasHealthPR")]
        TestallavailabilityreplicasHealthPR = 1332,

        [EnumDescription("TestallavailabilityreplicasHealthDR")]
        TestallavailabilityreplicasHealthDR = 1333,

        [EnumDescription("CheckAllDatabasesstate")]
        CheckAllDatabasesstate = 1334,

        #endregion MSSQL Alwayson

        #region Mssql AlwaysOnFailover

        [EnumDescription("ModifyAGMode")]
        ModifyAGMode = 1335,

        [EnumDescription("RemovePrimaryDatabase")]
        RemovePrimaryDatabase = 1336,

        [EnumDescription("RestoreDatabasewithRecoveryDROnly")]
        RestoreDatabasewithRecoveryDROnly = 1337,

        [EnumDescription("AddPrimaryDatabasetoAvailabiityGroup")]
        AddPrimaryDatabasetoAvailabiityGroup = 1338,

        [EnumDescription("UnjoinedDBfromAvailabilityGroup")]
        UnjoinedDBfromAvailabilityGroup = 1339,

        [EnumDescription("JoinSecondaryDBtoAvailabilityGroup")]
        JoinSecondaryDBtoAvailabilityGroup = 1340,

        [EnumDescription("CheckDBisJoinedorUnjoined")]
        CheckDBisJoinedorUnjoined = 1341,
        //Here added New  

        [EnumDescription("Start_WorkflowRTO")]
        Start_WorkflowRTO = 1426,

        [EnumDescription("Stop_WorkflowRTO")]
        Stop_WorkflowRTO = 1427,

        #endregion Mssql AlwaysOnFailover

        #region winFileHandling

        [EnumDescription("CheckServiceStatus")]
        CheckServiceStatus = 1342,

        [EnumDescription("StartService")]
        StartService = 1343,

        [EnumDescription("StopService")]
        StopService = 1344,

        [EnumDescription("AddnewrecordTxtFile")]
        AddnewrecordTxtFile = 1345,

        [EnumDescription("ReplaceTxtInFile")]
        ReplaceTxtInFile = 1346,

        [EnumDescription("BatchFileExecution")]
        BatchFileExecution = 1347,

        [EnumDescription("RenameFile")]
        WINRenameFile = 1348,

        [EnumDescription("ReplaceFile")]
        ReplaceFile = 1349,

        #endregion winFileHandling

        #region --MSSQL Native-Log-Shipping COPY TAIL LOG Backup using Robocopy

        [EnumDescription("GenerateLastBackupLogwithStandbyPR")]
        GenerateLastBackupLogwithStandbyPR = 1350,
        [EnumDescription("VerifyPrimarybackuptransactionlog")]
        VerifyPrimarybackuptransactionlog = 1351,
        [EnumDescription("CopyTailLogBackupDR")]
        CopyTailLogBackupDR = 1352,
        [EnumDescription("validatebackupwithPrimarySecondary")]
        validatebackupwithPrimarySecondary = 1353,
        [EnumDescription("RestoreLastlogwithRecoveryDR")]
        RestoreLastlogwithRecoveryDR = 1354,
        [EnumDescription("RestoreLastlogwithStandbyDR")]
        RestoreLastlogwithStandbyDR = 1355,
        [EnumDescription("VerifyLastLogbackupSizeandLSNPRDR")]
        VerifyLastLogbackupSizeandLSNPRDR = 1356,

        #endregion

        #region-- Citrix NetScaler Load Balancer
        [EnumDescription("CheckNetScalerLoadBalancerStatus")]
        CheckNetScalerLoadBalancerStatus = 1357,
        [EnumDescription("DisableNetScalerLoadBalancerNode")]
        DisableNetScalerLoadBalancerNode = 1358,
        [EnumDescription("EnableNetScalerLoadBalancerNode")]
        EnableNetScalerLoadBalancerNode = 1359,

        #endregion

        #region Doubletake

        [EnumDescription("VerifyJobStatus")]
        VerifyJobStatus = 1360,

        [EnumDescription("VerifyFailOver")]
        VerifyFailOver = 1361,

        [EnumDescription("ExecuteFailOver")]
        ExecuteFailOver = 1362,

        [EnumDescription("VerifyJobReverse")]
        VerifyJobReverse = 1363,

        [EnumDescription("ExecuteReverse")]
        ExecuteReverse = 1364,

        [EnumDescription("CheckStatusExecuteReverse")]
        CheckStatusExecuteReverse = 1365,


        #endregion Doubletake

        #region VCloud

        [EnumDescription("VCDPowerOff")]
        VCDPowerOff = 1366,

        [EnumDescription("VCDVerifyStatus")]
        VCDVerifyStatus = 1367,

        [EnumDescription("VCDPowerOn")]
        VCDPowerOn = 1368,

        #endregion VCloud

        #region PostGresSQL9.6

        [EnumDescription("VerifyPostGresSQL_DatabaseClusterState")]
        VerifyPostGresSQL_DatabaseClusterState = 1369,

        [EnumDescription("VerifyPostGresSQL_PR_ReplicationStatus")]
        VerifyPostGresSQL_PR_ReplicationStatus = 1370,

        [EnumDescription("VerifyPostGresSQL_DR_ReplicationStatus")]
        VerifyPostGresSQL_DR_ReplicationStatus = 1371,

        [EnumDescription("VerifyPostGresSQL_TriggerFileNameAndPath")]
        VerifyPostGresSQL_TriggerFileNameAndPath = 1372,

        #endregion PostGresSQL9.6

        #region DAG_2016

        [EnumDescription("ExchDAGSetWitnessServerAndDirectory")]
        ExchDAGSetWitnessServerAndDirectory = 1373,

        [EnumDescription("ExchDAGVerifyWitnessServerAndPathConfigured")]
        ExchDAGVerifyWitnessServerAndPathConfigured = 1374,

        [EnumDescription("ExchDAGSetAlternateWitnessServerAndDirectory")]
        ExchDAGSetAlternateWitnessServerAndDirectory = 1375,

        [EnumDescription("ExchDAGVerifyAlternateWitnessServerAndDirectory")]
        ExchDAGVerifyAlternateWitnessServerAndDirectory = 1376,

        #endregion DAG_2016

        #region DFSReplication

        [EnumDescription("DisableFolderTargetReferralNamespace")]
        DisableFolderTargetReferralNamespace = 1377,

        [EnumDescription("EnableFolderTargetReferralNamespace")]
        EnableFolderTargetReferralNamespace = 1378,

        [EnumDescription("CheckFolderTargetReferralNamespaceStatus")]
        CheckFolderTargetReferralNamespaceStatus = 1379,

        #endregion DFSReplication

        #region ExchangeDAGNewSavolMaheshSingh

        [EnumDescription("EnableMailboxDatabaseCircularLogging")]
        EnableMailboxDatabaseCircularLogging = 1380,

        [EnumDescription("DisableMailboxDatabaseCircularLogging")]
        DisableMailboxDatabaseCircularLogging = 1381,

        [EnumDescription("EnableSendConnector")]
        EnableSendConnector = 1382,

        [EnumDescription("DisableSendConnector")]
        DisableSendConnector = 1383,

        [EnumDescription("AD_CheckReplicationSyncAllStatus")]
        AD_CheckReplicationSyncAllStatus = 1384,

        [EnumDescription("ShutDownServer")]
        ShutDownServer = 1385,

        [EnumDescription("CheckMailboxdatabaseBackupFull_IncrementalStatus")]
        CheckMailboxdatabaseBackupFull_IncrementalStatus = 1386,

        #endregion ExchangeDAGNewSavolMaheshSingh

        [EnumDescription("UIAutomationWithPassword")]
        UIAutomationWithPassword = 1387,

        #region Ora_Rsync

        [EnumDescription("ReplicateStandByTraceFile")]
        Ora_Rsync_ReplicateStandByTraceFile = 1388,

        [EnumDescription("ReplicateRSyncFilePosix")]
        ReplicateRSyncFilePosix = 1389,

        [EnumDescription("ReplicateRSyncFoldersPosix")]
        ReplicateRSyncFoldersPosix = 1390,

        [EnumDescription("ReplicateStandByControlFile")]
        Ora_Rsync_ReplicateStandByControlFile = 1391,

        #endregion

        #region VeritasVRSG

        [EnumDescription("VVRSG_CheckStatusofServiceGroupClusterNode")]
        VVRSG_CheckStatusofServiceGroupClusterNode = 1392,

        [EnumDescription("VVRSG_ExecuteOfflineOnAnyOneOfServiceGroupClusterNode")]
        VVRSG_ExecuteOfflineOnAnyOneOfServiceGroupClusterNode = 1393,

        [EnumDescription("VVRSG_ExecuteOnlineOnAnyOneOfServiceGroupClusterNode")]
        VVRSG_ExecuteOnlineOnAnyOneOfServiceGroupClusterNode = 1394,

        [EnumDescription("ExecuteBatchFile")]
        ExecuteBatchFile = 1395,

        [EnumDescription("CheckStringValueExistInFile")]
        CheckStringValueExistInFile = 1396,

        #endregion VeritasVRSG

        [EnumDescription("ExchDAG_VerifyCheckandStopclusterService")]
        ExchDAG_VerifyCheckandStopclusterService = 1397,

        [EnumDescription("ExchDAG_VerifyResumeMailBoxDatabaseCopy")]
        ExchDAG_VerifyResumeMailBoxDatabaseCopy = 1398,

        [EnumDescription("ExchDAG_SetDAGToSeedALLChanges")]
        ExchDAG_SetDAGToSeedALLChanges = 1399,

        [EnumDescription("ExchDAG_MountMailboxDatabaseActivePRMailboxServer")]
        ExchDAG_MountMailboxDatabaseActivePRMailboxServer = 1400,

        [EnumDescription("LSSQL_GenerateLastBackUpLogWithNoRecovery")]
        LSSQL_GenerateLastBackUpLogWithNoRecovery = 1401,

        [EnumDescription("LSSQL_RestoreLastBackupLogWithNoRecoveryWithPrecheck")]
        LSSQL_RestoreLastBackupLogWithNoRecoveryWithPrecheck = 1402,

        [EnumDescription("LSSQL_EnableLogshippingwithTargetDBRestoring")]
        LSSQL_EnableLogshippingwithTargetDBRestoring = 1403,

        [EnumDescription("LSSQL_RestoreLastBackupLogWithRecoveryWithPrecheck")]
        LSSQL_RestoreLastBackupLogWithRecoveryWithPrecheck = 1412,

        [EnumDescription("AddSPNToActiveDirectoryComputer")]
        AddSPNToActiveDirectoryComputer = 1404,

        [EnumDescription("RemoveSPNFromActiveDirectoryComputer")]
        RemoveSPNFromActiveDirectoryComputer = 1405,

        #region DNS_Allow_Deny_RemoveHost

        [EnumDescription("AddComputerAccountToDNSRecord_WithAllowPermission")]
        AddComputerAccountToDNSRecord_WithAllowPermission = 1406,

        [EnumDescription("AddComputerAccountToDNSRecord_WithDenyPermission")]
        AddComputerAccountToDNSRecord_WithDenyPermission = 1407,

        [EnumDescription("RemoveComputerAccountFromDNSRecord_WithAllowOrDenyPermission")]
        RemoveComputerAccountFromDNSRecord_WithAllowOrDenyPermission = 1408,

        [EnumDescription("AddNTAUTHORITYSystemAccountToDNSRecord_WithAllowPermission")]
        AddNTAUTHORITYSystemAccountToDNSRecord_WithAllowPermission = 1409,

        [EnumDescription("AddNTAUTHORITYSystemAccountToDNSRecord_WithDenyPermission")]
        AddNTAUTHORITYSystemAccountToDNSRecord_WithDenyPermission = 1410,

        [EnumDescription("RemoveNTAUTHORITYSystemAccountFromDNSRecord_WithAllowOrDenyPermission")]
        RemoveNTAUTHORITYSystemAccountFromDNSRecord_WithAllowOrDenyPermission = 1411,

        #endregion DNS_Allow_Deny_RemoveHost

        #region QIAExchangeDagActions

        //Some actions not added as enums were present but in actiontype they were not present

        [EnumDescription("ExchDAG_VerifyCurrentSiteNameAssociatedWithMailboxServer")]
        ExchDAG_VerifyCurrentSiteNameAssociatedWithMailboxServer = 1413,

        [EnumDescription("ExchDAG_CheckPrimaryAlternateFileShareWitnessInUse")]
        ExchDAG_CheckPrimaryAlternateFileShareWitnessInUse = 1414,

        [EnumDescription("ExchDAG_CheckReplicationHealthonMailboxServer")]
        ExchDAG_CheckReplicationHealthonMailboxServer = 1415,

        [EnumDescription("ExchDAG_StopDAGAtPRMailboxServer")]
        ExchDAG_StopDAGAtPRMailboxServer = 1416,

        [EnumDescription("ExchDAG_StartDAGAtPRMailboxServer")]
        ExchDAG_StartDAGAtPRMailboxServer = 1417,

        [EnumDescription("ExchDAG_VerifyStarteddMailBoxServer")]
        ExchDAG_VerifyStarteddMailBoxServer = 1418,


        #endregion QIAExchangeDagActions

        #region Nutanix_VMNetworkAutomation_MaheshSingh

        [EnumDescription("AddNetworkInterfaceCardToVM")]
        AddNetworkInterfaceCardToVM = 1419,

        [EnumDescription("CheckIfNetworkAdapterExistUsingNICIPAddress")]
        CheckIfNetworkAdapterExistUsingNICIPAddress = 1420,

        [EnumDescription("CheckIfNetworkAdapterExistUsingMACAddress")]
        CheckIfNetworkAdapterExistUsingMACAddress = 1421,

        [EnumDescription("AssignDNSIPAddressToWindowsServerUsingIPAddress")]
        AssignDNSIPAddressToWindowsServerUsingIPAddress = 1422,

        [EnumDescription("AssignDNSIPAddressToWindowsServerUsingMACAddress")]
        AssignDNSIPAddressToWindowsServerUsingMACAddress = 1423,

        [EnumDescription("AssignDNSIPAddressToWindowsServerrandomly")]
        AssignDNSIPAddressToWindowsServerrandomly = 1424,

        //ConsolidatedAction
        [EnumDescription("Nutanix_AssignIPAddressToNetworkInterfaceCard")]
        Nutanix_AssignIPAddressToNetworkInterfaceCard = 1425,

        [EnumDescription("ReplicateIncrementalArchiveLogs")]
        ReplicateIncrementalArchiveLogs = 1428,

        [EnumDescription("VerifyJobRestoreStatus")]
        VerifyJobRestoreStatus = 1429,

        [EnumDescription("ExecuteRestoreJob")]
        ExecuteRestoreJob = 1430,

        [EnumDescription("VerifyFailBack")]
        VerifyFailBack = 1431,

        [EnumDescription("ExecuteFailback")]
        ExecuteFailback = 1432,

        [EnumDescription("VerifyJobStart")]
        VerifyJobStart = 1433,

        [EnumDescription("CheckStatusExecuteStart")]
        CheckStatusExecuteStart = 1434,

        [EnumDescription("VerifyNutanixLeapRecoveryPlanName")]
        VerifyNutanixLeapRecoveryPlanName = 1435,

        [EnumDescription("VerifyTargetLocalAvailabilityZoneforLeapRecoveryPlan")]
        VerifyTargetLocalAvailabilityZoneforLeapRecoveryPlan = 1436,

        [EnumDescription("VerifySourceFailedAvailabilityZoneforLeapRecoveryPlan")]
        VerifySourceFailedAvailabilityZoneforLeapRecoveryPlan = 1437,

        [EnumDescription("CheckRecoveryPointEntityVMexistAtSourceFailedAZ")]
        CheckRecoveryPointEntityVMexistAtSourceFailedAZ = 1438,

        [EnumDescription("CheckRecoveryPointEntityVMsdoesnotexistAtTargetLocalAZ")]
        CheckRecoveryPointEntityVMsdoesnotexistAtTargetLocalAZ = 1439,

        [EnumDescription("NutanixLeapRecoveryPlanPlannedFailoverTargetSite")]
        NutanixLeapRecoveryPlanPlannedFailoverTargetSite = 1440,

        [EnumDescription("NutanixLeapRecoveryPlanUnplannedFailoverTargetSite")]
        NutanixLeapRecoveryPlanUnplannedFailoverTargetSite = 1441,

        [EnumDescription("NutanixLeapRecoveryPlanValidateRecoveryPlanTargetSite")]
        NutanixLeapRecoveryPlanValidateRecoveryPlanTargetSite = 1442,

        #endregion Nutanix_VMNetworkAutomation_MaheshSingh



        #region NutanixProtectionDomainFailOverActions_MaheshSingh


        [EnumDescription("CheckProtectionDomainStatusForSite")]
        CheckProtectionDomainStatusForSite = 1443,

        [EnumDescription("AcknowledgenResolveRecentInformativeAlertForPD")]
        AcknowledgenResolveRecentInformativeAlertForPD = 1444,

        [EnumDescription("CheckConsistencyGroupMemberStatusUnderPD_ActiveSite")]
        CheckConsistencyGroupMemberStatusUnderPD_ActiveSite = 1445,

        [EnumDescription("CheckVMExistInConsistencyGroupUnderPD_ActiveSite")]
        CheckVMExistInConsistencyGroupUnderPD_ActiveSite = 1446,

        [EnumDescription("VerifyIfNoReplicationPendingForPD_ActiveSite")]
        VerifyIfNoReplicationPendingForPD_ActiveSite = 1447,

        [EnumDescription("ProtectionDomainActivationFailoverFromDR")]
        ProtectionDomainActivationFailoverFromDR = 1448,



        [EnumDescription("CheckConsistencyGroupMemberStatusUnderPD_InActiveSite")]
        CheckConsistencyGroupMemberStatusUnderPD_InActiveSite = 1449,

        [EnumDescription("CheckVMExistInConsistencyGroupUnderPD_InActiveSite")]
        CheckVMExistInConsistencyGroupUnderPD_InActiveSite = 1450,

        [EnumDescription("VerifyIfNoReplicationPendingForPD_InActiveSite")]
        VerifyIfNoReplicationPendingForPD_InActiveSite = 1451,


        #endregion NutanixProtectionDomainFailOverActions_MaheshSingh

        #region DNS_Triveni
        [EnumDescription("CheckDNSRecordwithoutTTLValueAType")]
        CheckDNSRecordwithoutTTLValueAType = 1452,

        [EnumDescription("CheckDNSRecordwithTTLvalueAType")]
        CheckDNSRecordwithTTLvalueAType = 1453,

        [EnumDescription("ModifyDNSRecordValueATypewithoutTTL")]
        ModifyDNSRecordValueATypewithoutTTL = 1454,

        [EnumDescription("ModifyDNSRecordValueATypewithTTL")]
        ModifyDNSRecordValueATypewithTTL = 1455,

        #endregion

        [EnumDescription("DGVerifyDBSyncStatus")]
        DGVerifyDBSyncStatus = 1456,


        [EnumDescription("vAPP_ExecuteFailOver")]
        vAPP_ExecuteFailOver = 1457,
        //VMWare


        [EnumDescription("vAPP_SetReverseReplication")]
        vAPP_SetReverseReplication = 1458,

        [EnumDescription("vAPP_PowerOn")]
        vAPP_PowerOn = 1459,

        [EnumDescription("vAPP_PowerOff")]
        vAPP_PowerOff = 1460,

        [EnumDescription("vAPP_Delete")]
        vAPP_Delete = 1461,

        [EnumDescription("vAPP_CheckRecoveryState")]
        vAPP_CheckRecoveryState = 1462,


        //Exchange Auto Activation 

        [EnumDescription("CheckDatabaseCopyAutoActivationPolicy")]
        CheckDatabaseCopyAutoActivationPolicy = 1463,

        [EnumDescription("SetDataBaseCopyAutoActivationPolicy")]
        SetDataBaseCopyAutoActivationPolicy = 1464,

        [EnumDescription("CheckMailboxDatabaseCopyStatus")]
        CheckMailboxDatabaseCopyStatus = 1465,

        [EnumDescription("DismountDatabase")]
        DismountDatabase = 1466,

        [EnumDescription("MountDataBase")]
        MountDataBase = 1467,

        #region Azure_Cloud

        [EnumDescription("Azure_TestFailOver")]
        Azure_TestFailOver = 1468,

        [EnumDescription("Azure_CleanUpFailOver")]
        Azure_CleanUpFailOver = 1469,

        [EnumDescription("CheckAllowedOperation")]
        CheckAllowedOperation = 1470,

        [EnumDescription("ExecuteUnplannedFailover")]
        ExecuteUnplannedFailover = 1471,

        [EnumDescription("CheckUnplannedFailoverStatus")]
        CheckUnplannedFailoverCompletedStatus = 1472,

        [EnumDescription("CheckCommitFailOver")]
        CheckCommitFailOver = 1473,

        [EnumDescription("ExecuteCommitUnplannedFailover")]
        ExecuteCommitUnplannedFailover = 1474,

        [EnumDescription("CheckCommitUnplannedFailoverStatus")]
        CheckUnplannedFailoverCommittedStatus = 1475,

        [EnumDescription("CheckRe_Protect")]
        CheckRe_Protect = 1476,

        [EnumDescription("ExecuteRe_Protect")]
        ExecuteRe_Protect = 1477,

        [EnumDescription("CheckRe_ProtectProtectionState")]
        CheckRe_ProtectProtectionState = 1478,

        [EnumDescription("AssociatePublicIPAddress")]
        AssociatePublicIPAddress = 1479,

        [EnumDescription("CheckPublicIPAddress")]
        CheckPublicIPAddress = 1480,

        [EnumDescription("ExecutePlannedFailover")]
        ExecutePlannedFailover = 1481,

        [EnumDescription("CheckPlannedFailoverStatus")]
        CheckPlannedFailoverStatus = 1482,

        [EnumDescription("CheckNSGNameExist")]
        CheckNSGNameExist = 1494, //1483

        [EnumDescription("CheckNSGNameAssociatetoVM")]
        CheckNSGNameAssociatetoVM = 1495,

        [EnumDescription("AssociateNSGtoVM")]
        AssociateNSGtoVM = 1496,

        [EnumDescription("DissociateNSGtoVM")]
        DissociateNSGtoVM = 1497,

        [EnumDescription("AddSecurityRuletoNSG")]
        AddSecurityRuletoNSG = 1498,

        [EnumDescription("RemoveSecurityRuletoNSG")]
        RemoveSecurityRuletoNSG = 1499,

        [EnumDescription("AssociateNSGtoNetworkInterface")]
        AssociateNSGtoNetworkInterface = 1500,

        [EnumDescription("DissociateNSGtoNetworkInterface")]
        DissociateNSGtoNetworkInterface = 1501,

        [EnumDescription("AssignNSGtoVMWithReplaceExistingNSG")]
        AssignNSGtoVMWithReplaceExistingNSG = 1502,

        #endregion

        #region Zetro

        [EnumDescription("CheckVPGConnectedSite")]
        CheckVPGConnectedSite = 1483,

        [EnumDescription("CheckVPGProtectionsStatusAndVPGState  ")]
        CheckVPGProtectionsStatusAndVPGState = 1484,

        [EnumDescription("CheckVPGProtectionStatusFailingover")]
        CheckVPGProtectionStatusFailingover = 1485,

        [EnumDescription("CheckVPGProtectionStateFailingOverBeforeCommit")]
        CheckVPGProtectionStateFailingOverBeforeCommit = 1486,

        [EnumDescription("CheckVPGStateDeltaSyncing")]
        CheckVPGStateDeltaSyncing = 1487,

        [EnumDescription("CheckVPGProtectionStateFailingOverRollingBack")]
        CheckVPGProtectionStateFailingOverRollingBack = 1488,

        [EnumDescription("ExecuteVPGFailoverwithoutCommit")]
        ExecuteVPGFailoverwithoutCommit = 1489,

        [EnumDescription(" ExecuteVPGVPGFailoverCommitwithReverseProtection")]
        ExecuteVPGVPGFailoverCommitwithReverseProtection = 1490,

        [EnumDescription("ExecuteVPGRollbackBeforeFailoverCommit")]
        ExecuteVPGRollbackBeforeFailoverCommit = 1491,

        [EnumDescription("ExecuteVPGFAILOVERTEST")]
        ExecuteVPGFAILOVERTEST = 1492,

        [EnumDescription("ExecuteVPGSTOPFAILOVERTEST")]
        ExecuteVPGSTOPFAILOVERTEST = 1493,


        #endregion Zetro

        #region AzureLoadBalancer

        [EnumDescription("AddLoadBalancerToVM")]
        AddLoadBalancerToVM = 1503,

        [EnumDescription("RemoveLoadBalancerFromVM")]
        RemoveLoadBalancerFromVM = 1504,

        [EnumDescription("AddProbeHealthToLoadBalancer")]
        AddProbeHealthToLoadBalancer = 1505,

        [EnumDescription("RemoveProbeHealthfromLoadBalancer")]
        RemoveProbeHealthfromLoadBalancer = 1506,

        [EnumDescription("AddLoadBalancerRuleToLoadBalancer")]
        AddLoadBalancerRuleToLoadBalancer = 1507,

        [EnumDescription("RemoveLoadBalancerRuleFromLoadBalancer")]
        RemoveLoadBalancerRuleFromLoadBalancer = 1508,

        [EnumDescription("AddLoadBalancerInboundNATRuleToLoadBalancer")]
        AddLoadBalancerInboundNATRuleToLoadBalancer = 1509,

        [EnumDescription("RemoveLoadBalancerInboundNATRuleFromLoadBalancer")]
        RemoveLoadBalancerInboundNATRuleFromLoadBalancer = 1510,

        #endregion AzureLoadBalancer

        [EnumDescription("AssociateNSGtoVMDefaultNetworkCard")]
        AssociateNSGtoVMDefaultNetworkCard = 1511,

        //RubrikAPI
        [EnumDescription("RubrikMountVirtualMachine")]
        RubrikMountVirtualMachine = 1512,

        [EnumDescription("MigrateDataStoreAfterMount")]
        MigrateDataStoreAfterMount = 1513,



        #region Azure-OnPrim

        [EnumDescription("CheckAllowedOperationAzureToPrim")]
        CheckAllowedOperationAzureToPrim = 1514,

        [EnumDescription("CheckReplicatedVMProtectionState")]
        CheckReplicatedVMProtectionState = 1515,

        [EnumDescription("ExecutePlannedFailoverAzureToOnPrim")]
        ExecutePlannedFailoverAzureToOnPrim = 1516,

        [EnumDescription("ExecuteCommitFailback")]
        ExecuteCommitFailbackAzureToOnPrim = 1517,

        [EnumDescription("ExecuteReprotectOnPrimToAzure")]
        ExecuteReprotectOnPrimToAzure = 1518,

        [EnumDescription("ExecutePlannedFailoverCommitOnPrimToAzure")]
        ExecutePlannedFailoverCommitOnPrimToAzure = 1519,

        [EnumDescription("ExecuteReverseReplicationAzureToOnPrim")]
        ExecuteReverseReplicationAzureToOnPrim = 1520,

        [EnumDescription("ExecuteUnplannedFailOverOnPrimToAzure")]
        ExecuteUnplannedFailOverOnPrimToAzure = 1521,

        [EnumDescription("ExecuteReprotectAzureToOnPrim")]
        ExecuteReprotectAzureToOnPrim = 1522,
        [EnumDescription("ExecuteReprotectOnPrimToAzure")]
        ExecuteReprotectOnPremVMwareToAzure = 1523,


        #endregion

        #region MongoDB

        [EnumDescription("CheckHealthStatusUP")]
        CheckHealthStatusUP = 1524,

        [EnumDescription("CheckSecondaryState")]
        CheckSecondaryState = 1525,

        [EnumDescription("CheckPrimaryState")]
        CheckPrimaryState = 1526,

        [EnumDescription("SetPriorityoftheReplicaSet")]
        SetPriorityoftheReplicaSet = 1527,

        [EnumDescription("CheckReplicationLagStatus")]
        CheckReplicationLagStatus = 1528,


        #endregion MongoDB
        #region TCLCloud

        [EnumDescription("TCL_Cloud_VM_Instance_PowerON")]
        TCL_Cloud_VM_Instance_PowerON = 1529,


        [EnumDescription("TCL_Cloud_VM_Instance_PowerOFF")]
        TCL_Cloud_VM_Instance_PowerOFF = 1530,


        [EnumDescription("Check_TCL_Cloud_VM_InstanceStatus")]
        Check_TCL_Cloud_VM_InstanceStatus = 1531,



        #endregion TCLCloud

        //
        [EnumDescription("vAPP_CheckOverallHealth")]
        vAPP_CheckOverallHealth = 1532,

        [EnumDescription("vAPP_CheckReplicationState")]
        vAPP_CheckReplicationState = 1533,

        [EnumDescription("vAPP_CheckFailoverNetworkSettings")]
        vAPP_CheckFailoverNetworkSettings = 1534,

        [EnumDescription("vAPP_UpdateFailoverNetworkSettings")]
        vAPP_UpdateFailoverNetworkSettings = 1535,

        [EnumDescription("vAPP_CheckFailoverNetworkSettingsSrcTgt")]
        vAPP_CheckFailoverNetworkSettingsSrcTgt = 1536,

        [EnumDescription("VCAV_ExecutevAPPSync")]
        VCAV_ExecutevAPPSync = 1537,

        //Wind Postgres From NM

        [EnumDescription("VerifyServerRuuning")]
        VerifyServerRuuning = 1538,

        [EnumDescription("VerifyClusterStatus")]
        VerifyClusterStatus = 1539,

        [EnumDescription("VerifyReplicationStatus")]
        VerifyReplicationStatus = 1540,

        [EnumDescription("VerifyRecoveryStatus")]
        VerifyRecoveryStatus = 1541,

        [EnumDescription("Postgres_StopService")]
        Postgres_StopService = 1542,

        [EnumDescription("CheckRecoveryFile")]
        CheckRecoveryFile = 1543,

        [EnumDescription("CreateTriggerFile")]
        CreateTriggerFile = 1544,

        [EnumDescription("RestartService")]
        RestartService = 1545,

        [EnumDescription("VerifyRecoveryDone")]
        VerifyRecoveryDone = 1546,

        [EnumDescription("CheckRecoveryFilePrimary")]
        CheckRecoveryFilePrimary = 1547,

        [EnumDescription("AddContentRecovery")]
        AddContentRecovery = 1548,

        [EnumDescription("CheckContent")]
        CheckContent = 1549,

        [EnumDescription("Postgres_StartService")]
        Postgres_StartService = 1550,



        #region NS1DNS_MaheshSingh


        [EnumDescription("NS1DNS_CheckNS1DNSRecordATypeSingleIPAddress")]
        NS1DNS_CheckNS1DNSRecordATypeSingleIPAddress = 1551,

        [EnumDescription("NS1DNS_CheckNS1DNSRecordATypeMultipleIPAddress")]
        NS1DNS_CheckNS1DNSRecordATypeMultipleIPAddress = 1552,

        [EnumDescription("NS1DNS_CheckNS1DNSRecordCNAMEType")]
        NS1DNS_CheckNS1DNSRecordCNAMEType = 1553,

        [EnumDescription("NS1DNS_ModifyNS1DNSRecordATypeSingleIPAddress")]
        NS1DNS_ModifyNS1DNSRecordATypeSingleIPAddress = 1554,

        [EnumDescription("NS1DNS_ModifyNS1DNSRecordsATypeMultipleIPAddress")]
        NS1DNS_ModifyNS1DNSRecordsATypeMultipleIPAddress = 1555,

        [EnumDescription("NS1DNS_ModifyNS1DNSRecordCNAMEType")]
        NS1DNS_ModifyNS1DNSRecordCNAMEType = 1556,



        #endregion NS1DNS_MaheshSingh



        #region GenericRestAPI_MaheshSingh

        [EnumDescription("ExecuteCheckRestAPICommand")]
        ExecuteCheckRestAPICommand = 1557,


        #endregion GenericRestAPI_MaheshSingh

        #region ORACLEAPI_MaheshSingh

        [EnumDescription("OracleDataGuardAPI_SwitchDB")]
        OracleDataGuardAPI_SwitchDB = 1558,

        [EnumDescription("OracleDataGuardAPI_CheckDBSyncStatus")]
        OracleDataGuardAPI_CheckDBSyncStatus = 1559,




        #endregion ORACLEAPI_MaheshSingh

        #region Azure

        [EnumDescription("CheckAzureSQLDBRole")]
        CheckAzureSQLDBRole = 1560,

        [EnumDescription("CheckAzureReplicationState")]
        CheckAzureReplicationState = 1561,

        [EnumDescription("ExecutePlannedFailOver")]
        ExecutePlannedFailOverAzure = 1562,

        [EnumDescription("ExecuteForceFailOver")]
        ExecuteForceFailOverAzure = 1563,

        #endregion

        #region Azure Kubernetes

        [EnumDescription("CheckVirtualMachineScaleSet")]
        CheckVirtualMachineScaleSet = 1564,

        [EnumDescription("StartVirtualMachineScaleAllInstances")]
        StartVirtualMachineScaleAllInstances = 1565,

        [EnumDescription("StopVirtualMachineScaleAllInstances")]
        StopVirtualMachineScaleAllInstances = 1566,

        [EnumDescription("CheckSpecificVirtualMachineScaleSet")]
        CheckSpecificVirtualMachineScaleSet = 1567,

        [EnumDescription("StartSpecificVirtualMachineScaleSetInstance")]
        StartSpecificVirtualMachineScaleSetInstance = 1568,

        [EnumDescription("StopSpecificVirtualMachineScaleSetInstance")]
        StopSpecificVirtualMachineScaleSetInstance = 1569,

        #endregion

        #region Azure COSMOS
        [EnumDescription("CheckAzureWriteLocationCosmos")]
        CheckAzureWriteLocationCosmos = 1570,

        [EnumDescription("CheckAzureReadLocationCosmos")]
        CheckAzureReadLocationCosmos = 1571,

        [EnumDescription("CheckAzureDBAccountProvisioningStatusCosmos")]
        CheckAzureDBAccountProvisioningStatusCosmos = 1572,

        [EnumDescription("ExecuteFailoverAzureCosmosDB")]
        ExecuteFailoverAzureCosmosDB = 1573,

        #endregion

        #region Azure MySql Database

        [EnumDescription("CheckAZMySQLRole")]
        CheckAZMySQLRole = 1574,

        [EnumDescription("CheckAZMySQLServerAvailableStatus")]
        CheckAZMySQLServerAvailableStatus = 1575,

        [EnumDescription("DeleteReplicationSourceServer")]
        DeleteReplicationSourceServer = 1576,

        [EnumDescription("DeleteReplicationReplicaServer")]
        DeleteReplicationReplicaServer = 1577,

        [EnumDescription("CreateReplicationAZMySql")]
        CreateReplicationAZMySql = 1578,

        [EnumDescription("StopMySqlReplication")]
        StopMySqlReplication = 1579,

        #endregion Azure MySql Database

        #region AzureGatewayAction
        // vamsi azure gateway application
        [EnumDescription("CheckApplicationGatewayRulePathexist")]
        CheckApplicationGatewayRulePathexist = 1580,

        [EnumDescription("AddApplicationGatewayBackendPoolRulePath")]
        AddApplicationGatewayBackendPoolRulePath = 1581,

        [EnumDescription("RemovePathbasedRulefromBackendPoolRule")]
        RemovePathbasedRulefromBackendPoolRule = 1582,

        [EnumDescription("CheckListenerwithAssociatedRule")]
        CheckListenerwithAssociatedRule = 1583,

        [EnumDescription("AddListenerHTTPType")]
        AddListenerHTTPType = 1584,

        [EnumDescription("AddRoutingRule")]
        AddRoutingRule = 1585,

        [EnumDescription("RemoveRoutingRule")]
        RemoveRoutingRule = 1586,

        [EnumDescription("CheckApplicationGatewayOperationalState")]
        CheckApplicationGatewayOperationalState = 1587,

        //[EnumDescription("CheckApplicationGatewayRulePathexist1")]
        //CheckApplicationGatewayRulePathexist1 = 1588,
        #region new Action Zetro
        [EnumDescription("CheckVPGStateMovingBeforeCommit")]
        CheckVPGStateMovingBeforeCommit = 1588,
        [EnumDescription("ExecuteVPGMove_CommitPolicyNONE")]
        ExecuteVPGMove_CommitPolicyNONE = 1589,
        [EnumDescription("ExecuteVPGMoveCommitwithReverseProtection")]
        ExecuteVPGMoveCommitwithReverseProtection = 1590,
        [EnumDescription("ExecuteVPGRollbackBeforeMoveCommit")]
        ExecuteVPGRollbackBeforeMoveCommit = 1591,
        [EnumDescription("ExecuteVPGMoveCommitwithoutReverseProtection")]
        ExecuteVPGMoveCommitwithoutReverseProtection = 1592,
        [EnumDescription("CheckVPGStateVolumeInitialSync")]
        CheckVPGStateVolumeInitialSync = 1593,
        [EnumDescription("ExecuteVPGFailoverwithoutCommitAndShutdownSourceVM")]
        ExecuteVPGFailoverwithoutCommitAndShutdownSourceVM = 1594,

        #endregion Action Zetro


        #endregion


        #region OracleCloudReplication
        [EnumDescription("CheckOracleCloudVMInstanceStatus")]
        CheckOracleCloudVMInstanceStatus = 1595,

        [EnumDescription("OracleCloudVMInstanceAction")]
        OracleCloudVMInstanceAction = 1596,


        [EnumDescription("CheckifNSGAttachedtoInstanceVNIC")]
        CheckifNSGAttachedtoInstanceVNIC = 1597,


        [EnumDescription("AddNewNSGtoInstanceVNICReplaceExistingNSGs")]
        AddNewNSGtoInstanceVNICReplaceExistingNSGs = 1598,


        [EnumDescription("AddNewNSGtoInstanceVNICAppendtoExistingNSGs")]
        AddNewNSGtoInstanceVNICAppendtoExistingNSGs = 1599,

        [EnumDescription("DeattachALLNSGsfromInstanceVNIC")]
        DeattachALLNSGsfromInstanceVNIC = 1600,


        [EnumDescription("CreateNSGandAddSecurityRule")]
        CreateNSGandAddSecurityRule = 1601,



        #endregion Oraclecloudreplication


        #region NSXTAPI


        [EnumDescription("UpdateorModifyParticularSegment")]
        UpdateorModifyParticularSegment = 1602,

        [EnumDescription("RemoveSegmentConfiguration")]
        RemoveSegmentConfiguration = 1603,

        [EnumDescription("VerifySegmentDetailsWhenTypeDisconnected")]
        VerifySegmentDetailsWhenTypeDisconnected = 1604,

        [EnumDescription("VerifySegmentDetailsWhenTypeOtherThanDisconnected")]
        VerifySegmentDetailsWhenTypeOtherThanDisconnected = 1605,

        [EnumDescription("ModifyL2VPNConfiguration")]
        ModifyL2VPNConfiguration = 1606,

        //sarath
        [EnumDescription("Create_Mtree")]
        CreateMtree = 1607,

        [EnumDescription("Delete_Mtree")]
        DeleteMtree = 1608,

        [EnumDescription("CreateMtreeReplicationPair")]
        CreateMtreeReplicationPair = 1609,

        [EnumDescription("SyncReplicationandVerify")]
        SyncReplicationandVerify = 1610,

        [EnumDescription("SyncReplication")]
        SyncReplication = 1611,

        [EnumDescription("CheckmtreeReplicationPairStatus")]
        CheckmtreeReplicationPairStatus = 1612,

        //sarath

        #endregion NSXTAPI

        #region AVAMAR_AVAMAR REPLICATION ACTION  /*Yugan*/

        [EnumDescription("CheckIfDomainsNameexist")]
        CheckIfDomainsNameexist = 1613,

        [EnumDescription("CheckifClientexists")]
        CheckifClientexists = 1614,

        [EnumDescription("CheckVirtual_MachineLatestBackupImageexist")]
        CheckVirtual_MachineLatestBackupImageexist = 1615,

        [EnumDescription("ReplicateBackup")]
        ReplicateBackup = 1616,

        [EnumDescription("CheckReplicationActivityStatus")]
        CheckReplicationActivityStatus = 1617,

        [EnumDescription("CheckifDomainexistafterReplication")]
        CheckifDomainexistafterReplication = 1618,

        [EnumDescription("CheckifClientexistafterReplication")]
        CheckifClientexistafterReplication = 1619,

        [EnumDescription("CheckVirtualMachineLatestBackupexistAfterReplication")]
        CheckVirtualMachineLatestBackupexistAfterReplication = 1620,

        [EnumDescription("ReplicatingAllbackupsforspecificDomain")]
        ReplicatingAllbackupsforspecificDomain = 1621,

        [EnumDescription("ReplicatingAllbackupsforspecificClient")]
        ReplicatingAllbackupsforspecificClient = 1622,



        #endregion AVAMAR_AVAMARREPLICATIONACTION

        #region AVAMAR_AVAMAR RESTORE VMWARE

        [EnumDescription("CheckifTargetvCenterNameexistforRestore")]
        CheckifTargetvCenterNameexistforRestore = 1623,

        [EnumDescription("CheckifTargetVMexistforrestore")]
        CheckifTargetVMexistforrestore = 1624,

        [EnumDescription("CheckLatestBackupImageexistofTargetVMforRestoreoperation")]
        CheckLatestBackupImageexistofTargetVMforRestoreoperation = 1625,

        [EnumDescription("Checkiftargetexistforrestoreoperation")]
        Checkiftargetexistforrestoreoperation = 1626,

        [EnumDescription("RestoreVirtualMachinetoOriginallocation")]
        RestoreVirtualMachinetoOriginallocation = 1627,

        [EnumDescription("RestoreVirtualMachinetoNewlocation")]
        RestoreVirtualMachinetoNewlocation = 1628,

        [EnumDescription("RestoreVirtualMachinefromHistorytoOriginalLocation")]
        RestoreVirtualMachinefromHistorytoOriginalLocation = 1629, //yu

        [EnumDescription("RestoreVirtualMachinefromHistorytoNewLocation")]
        RestoreVirtualMachinefromHistorytoNewLocation = 1630,   //yu

        #endregion AVAMAR_AVAMARRESTOREVMWARE

        #region Hyper v networkAddapter

        [EnumDescription("ChangeHyperVVMIPAddressWithMacAddress")]
        ChangeHyperVVMIPAddressWithMacAddress = 1631,
        [EnumDescription("ChangeHyperVVMIPAddressWithOutMacAddress")]
        ChangeHyperVVMIPAddressWithOutMacAddress = 1632,
        [EnumDescription("ChangeHyperVVMIPAddressWithMacAddressSingleNet")]
        ChangeHyperVVMIPAddressWithMacAddressSingleNet = 1633,
        [EnumDescription("ChangeHyperVVMIPAddressWithOutMacAddressSingleNet")]
        ChangeHyperVVMIPAddressWithOutMacAddressSingleNet = 1634,
        #endregion

        #region Exchange DAG

        [EnumDescription("MoveDatabaseWithSkip")]
        MoveDatabaseWithSkip = 1635,
        [EnumDescription("MoveDatabaseWithoutSkip")]
        MoveDatabaseWithoutSkip = 1636,
        [EnumDescription("RestoreDAG")]
        RestoreDAG = 1637,

        [EnumDescription("StartDAG")]
        StartDAG = 1638,

        [EnumDescription("SendeReceiveConnector")]
        SendeReceiveConnector = 1639,

        [EnumDescription("ADReplicationStatus")]
        ADReplicationStatus = 1640,

        [EnumDescription("ActiveDirectoryUser")]
        ActiveDirectoryUser = 1641,

        #endregion Exchange DAG

        #region Rackware
        [EnumDescription("Rackware_CheckWaveStatus")]
        Rackware_CheckWaveStatus = 1642,

        [EnumDescription("Rackware_CheckDRPolicyStatus")]
        Rackware_CheckDRPolicyStatus = 1643,

        [EnumDescription("Rackware_ExecuteWaveFailOver")]
        Rackware_ExecuteWaveFailOver = 1644,

        [EnumDescription("Rackware_ExecuteWaveFailBack")]
        Rackware_ExecuteWaveFailBack = 1645,

        [EnumDescription("CreateHostSyncAutoTargetnotAvailDynamicIPWithoutWait")]
        CreateHostSyncAutoTargetnotAvailDynamicIPWithoutWait = 1646,

        [EnumDescription("CreateHostSyncAutoTargetnotAvailStaticIPWithWait")]
        CreateHostSyncAutoTargetnotAvailStaticIPWithWait = 1647,


        [EnumDescription("CreateHostSyncAutoTargetnotAvailStaticIPWithoutWait")]
        CreateHostSyncAutoTargetnotAvailStaticIPWithoutWait = 1648,

        [EnumDescription("HostSyncWhenTargetExistWithWait")]
        HostSyncWhenTargetExistWithWait = 1649,

        [EnumDescription("HostSyncWhenTargetExistWithoutWait")]
        HostSyncWhenTargetExistWithoutWait = 1650,

        [EnumDescription("CreateWaveWithHostSetOCIAutoprovisionparameters")]
        CreateWaveWithHostSetOCIAutoprovisionparameters = 1651,

        [EnumDescription("Rackware_StartWaveinRMMServer")]
        Rackware_StartWaveinRMMServer = 1652,






        #endregion

        #region arrayloadbalancer
        [EnumDescription("Enable_SLB_Real_Services")]
        Enable_SLB_Real_Services = 1653,

        [EnumDescription("Disable_SLB_Real_Services")]
        Disable_SLB_Real_Services = 1654,

        [EnumDescription("Check_SLB_Real_ServiceName_Status")]
        Check_SLB_Real_ServiceName_Status = 1655,

        #endregion arrayloadbalancer


        #region Dell EMC Power Protect VM Copy Restore Actions

        [EnumDescription("CheckVMExistAndProtect")]
        CheckVMExistAndProtect = 1656,
        [EnumDescription("RestoreVMToAlternateLocation")]
        RestoreVMToAlternateLocation = 1657,
        [EnumDescription("CheckVMExistAndProtect_New")]
        CheckVMExistAndProtect_New = 1658,
        [EnumDescription("RestoreVMToAlternateLocation_New")]
        RestoreVMToAlternateLocation_New = 1659,
        //[EnumDescription("R_Replication")]
        //R_Replication = 1659,
        #endregion


        #region VCenter Create new VM
        [EnumDescription("CreateNewVm")]
        CreateNewVm = 1660,
        #endregion

        [EnumDescription("MSSQLServerInstalled")]
        MSSQLServerInstalled = 1661,


        [EnumDescription("MSSQLServerInstalled_SSMS_Install")]
        MSSQLServerInstalled_SSMS_Install = 1662,

        [EnumDescription("R_Replication")]
        R_Replication = 1663,

        [EnumDescription("UACDisable")]
        UACDisable = 1664,

        [EnumDescription("InstallOpenSSH")]
        InstallOpenSSH = 1665,

        [EnumDescription("InstallOpenSSH_New")]
        InstallOpenSSH_New = 1666,


        [EnumDescription("RestoreVMToAlternateLocation_Proxy")]
        RestoreVMToAlternateLocation_Proxy = 1667,


        [EnumDescription("RestoreVMToAlternateLocation_Multiple")]
        RestoreVMToAlternateLocation_Multiple = 1668,

        [EnumDescription("VerifyInstalledSSH")]
        VerifyInstalledSSH = 1669,

        [EnumDescription("Start_ScheduledTask_Job")]
        Start_ScheduledTask_Job = 1676,

        [EnumDescription("Add_User_Administrator_Group")]
        Add_User_Administrator_Group = 1677,

        [EnumDescription("RestoreVMToAlternateLocation_Multiple_New")]
        RestoreVMToAlternateLocation_Multiple_New = 1678,

        #region HDID

        [EnumDescription("ChecktheReplicationState_OK")]
        ChecktheReplicationState_OK = 1670,

        [EnumDescription("ChecktheReplicationPausedState")]
        ChecktheReplicationPausedState = 1671,

        [EnumDescription("ChecktheReplicationSwappedState")]
        ChecktheReplicationSwappedState = 1672,

        [EnumDescription("PauseApplicationNodeReplication")]
        PauseApplicationNodeReplication = 1673,

        [EnumDescription("ResumeApplicationNodeReplication")]
        ResumeApplicationNodeReplication = 1674,

        [EnumDescription("SwapApplicationNodeReplication")]
        SwapApplicationNodeReplication = 1675,


        #endregion HDID






        #region CyberRecoveryVault


        [EnumDescription("VMInstantAccessRestore")]
        VMInstantAccessRestore = 1679,

        //[EnumDescription("PPCR_ExecutePolicyRecoveryCheck")]
        //PPCR_ExecutePolicyRecoveryCheck = 1680,

        [EnumDescription("Check_LastAnalysisStatus")]
        Check_LastAnalysisStatus = 1681,

        [EnumDescription("CheckRecovery_LatestSelected_CheckStatus")]
        CheckRecovery_LatestSelected_CheckStatus = 1682,

        [EnumDescription("Check_LatestCopyAvailableForPerticularPolicy")]
        Check_LatestCopyAvailableForPerticularPolicy = 1683,



        [EnumDescription("Execute_Policy_Application_Recovery")]
        Execute_Policy_Application_Recovery = 1684,


        [EnumDescription("Execute_Copy_Analyze")]
        Execute_Copy_Analyze = 1686,


        #endregion CyberRecoveryVault

        [EnumDescription("VMInstantAccessRestore_Multiple")]
        VMInstantAccessRestore_Multiple = 1685,


        [EnumDescription("RestoreVMToAlternateLocationMultiple_New")]
        RestoreVMToAlternateLocationMultiple_New = 1687,

        [EnumDescription("Execute_Recovery_Check")]
        Execute_Recovery_Check = 1688,



        [EnumDescription("Execute_SyncCopy")]
        Execute_SyncCopy = 1689,


        [EnumDescription("Execute_SecureCopy")]
        Execute_SecureCopy = 1690,

        [EnumDescription("Execute_SecureCopy_Analyze")]
        Execute_SecureCopy_Analyze = 1691,

        [EnumDescription("IBMSpectrumProtectPlus_ExecuteRestoreVM")]
        IBMSpectrumProtectPlus_ExecuteRestoreVM = 1692,

        [EnumDescription("VeritasNetbackupRestore_ExecuteRecovery")]
        VeritasNetbackupRestore_ExecuteRecovery = 1693,

        #region F5 Actions
        [EnumDescription("CheckLTMVirtualServerState")]
        CheckLTMVirtualServerState = 1694,

        [EnumDescription("ExecuteDisableLTMVirtualServer")]
        ExecuteDisableLTMVirtualServer = 1695,

        [EnumDescription("ExecuteEnableLTMVirtualServer")]
        ExecuteEnableLTMVirtualServer = 1696,

        [EnumDescription("ExecuteForceDisableNodestateofVirtualServer")]
        ExecuteForceDisableNodestateofVirtualServer = 1697,

        [EnumDescription("ExecuteForceEnableNodestateofVirtualServer")]
        ExecuteForceEnableNodestateofVirtualServer = 1698,

        #endregion


        #region PDELL vCenter

        [EnumDescription("AddCredentialsToPPDMTypeVCENTER")]
        AddCredentialsToPPDMTypeVCENTER = 1699,

        [EnumDescription("AddvCenterToPPDMAssetSource")]
        AddvCenterToPPDMAssetSource = 1700,

        [EnumDescription("CheckvCenterExistOnthePPDMServerAssetSource")]
        CheckvCenterExistOnthePPDMServerAssetSource = 1701,

        #endregion PDELL vCenter

        #region connector Enable/Disable


        [EnumDescription("AddIPAddresstoSmartHostInSendConnector")]
        AddIPAddressToSmartHostInSendConnector = 1702,

        [EnumDescription("CheckExistIPAddressToSmartHostInSendConnector")]
        CheckExistIPAddressToSmartHostInSendConnector = 1703,

        [EnumDescription("RemoveGivenIPAddressfromSmartHostInSendConnector")]
        RemoveGivenIPAddressfromSmartHostInSendConnector = 1704,

        [EnumDescription("AddIPAddressToSourceTransportServersInSendConnector")]
        AddIPAddresstoSourceTransportServersInSendConnector = 1705,

        [EnumDescription("RemoveSendConnectorScopingSourceServer")]
        RemoveSendConnectorScopingSourceServer = 1706,

        [EnumDescription("CheckExistHostAddressToSmartHostInSendConnector")]
        CheckExistHostAddressToSmartHostInSendConnector = 1707,

        [EnumDescription("EnableDisableConnectorInExchangeOnline")]
        EnableDisableConnectorInExchangeOnline = 1708,

        [EnumDescription("CheckEnableDisableConnectorInExchangeOnline")]
        CheckEnableDisableConnectorInExchangeOnline = 1709,

        #endregion connector Enable/Disable

        [EnumDescription("CheckAllMailboxDBMountedStatus")]
        CheckAllMailboxDBMountedStatus = 1710,

        //huawai
        [EnumDescription("CheckReplicationRunningStatus")]
        CheckReplicationRunningStatus = 1711,
        [EnumDescription("SyncReplication_Huawei")]
        SyncReplication_Huawei = 1712,
        [EnumDescription("SplitReplication")]
        SplitReplication = 1713,
        [EnumDescription("CheckLUNSplitState")]
        CheckLUNSplitState = 1714,
        [EnumDescription("EnableSecondaryResourceAccess_ReadWrite")]
        EnableSecondaryResourceAccess_ReadWrite = 1715,
        [EnumDescription("CheckSecondaryResourceAccess_ReadWrite")]
        CheckSecondaryResourceAccess_ReadWrite = 1716,
        [EnumDescription("EnableSecondaryResourceAccess_ReadOnly")]
        EnableSecondaryResourceAccess_ReadOnly = 1717,
        [EnumDescription("CheckSecondaryResourceAccess_ReadOnly")]
        CheckSecondaryResourceAccess_ReadOnly = 1718,
        [EnumDescription("CheckConsistencyGroupStatusSynchronizationBegins")]
        CheckConsistencyGroupStatusSynchronizationBegins = 1719,
        [EnumDescription("CheckConsistencyGroupReplicaRole")]
        CheckConsistencyGroupReplicaRole = 1720,
        [EnumDescription("SwapReplicaRole")]
        SwapReplicaRole = 1721,

        // Huawei storage

        [EnumDescription("ChangeConsistencyGroupTimeValue")]
        ChangeConsistencyGroupTimeValue = 1722,
        [EnumDescription("CheckConsistencyGroupTimeValue")]
        CheckConsistencyGroupTimeValue = 1723,

        #region OracleCloud_DNS
        [EnumDescription("OCI_CheckifDNSRecordExistAType")]
        OCI_CheckifDNSRecordExistAType = 1724,

        [EnumDescription("OCI_CheckifDNSRecordDoesNotExistAType")]
        OCI_CheckifDNSRecordDoesNotExistAType = 1725,

        [EnumDescription("OCI_AddDNSRecordAType")]
        OCI_AddDNSRecordAType = 1726,


        [EnumDescription("OCI_CheckExistingTTLValueDNSRecordAType")]
        OCI_CheckExistingTTLValueDNSRecordAType = 1727,

        [EnumDescription("OCI_DeleteDNSRecordAType")]
        OCI_DeleteDNSRecordAType = 1728,

        #endregion OracleCloud_DNS

        #region AZure ATM

        [EnumDescription("EnableDisableATMEndpoint")]
        EnableDisableATMEndpoint = 1729,

        [EnumDescription("preCheckEnableDisableATMEndpoint")]
        preCheckEnableDisableATMEndpoint = 1730,

        #endregion

        //broker action
        [EnumDescription("SetMSSQLBrokerServiceEnable")]
        SetMSSQLBrokerServiceEnable = 1731,

        [EnumDescription("SetMSSQLBrokerServiceDisable")]
        SetMSSQLBrokerServiceDisable = 1732,

        [EnumDescription("CheckMSSQLBrokerServiceStatusEnable")]
        CheckMSSQLBrokerServiceStatusEnable = 1733,

        [EnumDescription("CheckMSSQLBrokerServiceStatusDisable")]
        CheckMSSQLBrokerServiceStatusDisable = 1734,


        //UI automation
        [EnumDescription("UIPathAddQueueItem")]
        UIPathAddQueueItem = 1735,


        [EnumDescription("VerifyUIPathConnection")]
        TestUIPath = 1736,



        #region OracleDBTableCountMatch


        [EnumDescription("VerifySingleTableRowCount_PR")]
        VerifySingleTableRowCount_PR = 1741,

        [EnumDescription("VerifySingleTableRowCount_DR")]
        VerifySingleTableRowCount_DR = 1742,

        [EnumDescription("VerifySingleSchemaMultipleTablesRowCount_PR")]
        VerifySingleSchemaMultipleTablesRowCount_PR = 1743,

        [EnumDescription("VerifySingleSchemaMultipleTablesRowCount_DR")]
        VerifySingleSchemaMultipleTablesRowCount_DR = 1744,

        #endregion OracleDBTableCountMatch


        #region PostgresDBTableCountMatch

        [EnumDescription("VerifySingleTableCount_PR")]
        VerifySingleTableRow_Count_PR = 1745,

        [EnumDescription("VerifySingleTableCount_DR")]
        VerifySingleTableRow_Count_DR = 1746,

        [EnumDescription("VerifyMultipleTableCount_PR")]
        VerifyMultipleTableRow_Count_PR = 1747,

        [EnumDescription("VerifyMultipleTableCount_DR")]
        VerifyMultipleTableRow_Count_DR = 1748,

        #endregion PostgresDBTableCountMatch


        #region RedisDBTableCountMatch

        [EnumDescription("Redis_VerifySingleTableRowCount_PR")]
        Redis_VerifySingleTableRowCount_PR = 1749,

        [EnumDescription("Redis_VerifySingleTableRowCount_DR")]
        Redis_VerifySingleTableRowCount_DR = 1750,

        [EnumDescription("Redis_CheckEmptyArray_PR")]
        Redis_CheckEmptyArray_PR = 1751,

        [EnumDescription("Redis_CheckEmptyArray_DR")]
        Redis_CheckEmptyArray_DR = 1752,

        #endregion RedisDBTableCountMatch


        #region--mssql AlwaysOn Faliover Action

        [EnumDescription("SynAlwaysOnFailoverWithRandomSecondary")]
        SynAlwaysOnFailoverWithRandomSecondary = 1737,

        [EnumDescription("SynAlwaysOnFailoverWithSpecificSecondarysyn")]
        SynAlwaysOnFailoverWithSpecificSecondarysyn = 1738,

        [EnumDescription("ASynAlwaysOnFailoverWithRandomSecondary")]
        ASynAlwaysOnFailoverWithRandomSecondary = 1739,

        [EnumDescription("ASynAlwaysOnFailoverWithSpecificSecondary")]
        ASynAlwaysOnFailoverWithSpecificSecondary = 1740,

        #endregion


        #region Cluster Disk Using Powershell
        [EnumDescription("CheckDiskDetailsUsingLUNWWNNumber")]
        CheckDiskDetailsUsingLUNWWNNumber = 1753,

        [EnumDescription("MakeDiskOnline")]
        MakeDiskOnline = 1754,

        [EnumDescription("MakeDiskReadWrite")]
        MakeDiskReadWrite = 1755,

        [EnumDescription("CheckDriveLetterafterDiskOnline")]
        CheckDriveLetterafterDiskOnline = 1756,

        [EnumDescription("ChangeDiskDriveLetter")]
        ChangeDiskDriveLetter = 1757,

        [EnumDescription("MakeDiskOffline")]
        MakeDiskOffline = 1758,

        #endregion Cluster Disk Using Powershell

        #region  G42 API integration

        [EnumDescription("G42_CheckECSInstanceStatus")]
        G42_CheckECSInstanceStatus = 1759,

        [EnumDescription("G42_PowerOnECSInstance")]
        G42_PowerOnECSInstance = 1760,

        [EnumDescription("G42_PowerOffECSInstance")]
        G42_PowerOffECSInstance = 1761,


        #endregion  G42 API integration

        [EnumDescription("CrossvCenterVMMigration")]
        CrossvCenterVMMigration = 1762,


        [EnumDescription("PPDM_Backup")]
        PPDM_Backup = 1763,




        #region kubernet
        [EnumDescription("CheckPodStatefulSetsCountwithAllReadyStatus")]
        CheckPodStatefulSetsCountwithAllReadyStatus = 1764,

        [EnumDescription("ExecuteScaleUpDownPodsStatefulSetsCount")]
        ExecuteScaleUpDownPodsStatefulSetsCount = 1765,

        [EnumDescription("CheckPodReplicaSetsCountwithAllReadyStatus")]
        CheckPodReplicaSetsCountwithAllReadyStatus = 1766,

        [EnumDescription("OpenShiftExecuteScaleUpDownPodsReplicaSetsCount")]
        OpenShiftExecuteScaleUpDownPodsReplicaSetsCount = 1767,


        [EnumDescription("CheckPodDeploymentsCountwithAllReadyStatus")]
        CheckPodDeploymentsCountwithAllReadyStatus = 1768,

        [EnumDescription("ExecuteScaleUpDownPodsDeploymentsCount")]
        ExecuteScaleUpDownPodsDeploymentsCount = 1769,



        [EnumDescription("CheckSpecificVirtualMachineRunningStatus")]
        CheckSpecificVirtualMachineRunningStatus = 1770,
        [EnumDescription("ExecuteVirtualMachineStart")]
        ExecuteVirtualMachineStart = 1771,
        [EnumDescription("ExecuteVirtualMachineStop")]
        ExecuteVirtualMachineStop = 1772,
        [EnumDescription("CheckOpenShiftMachineSetMachineCount")]
        CheckOpenShiftMachineSetMachineCount = 1773,
        [EnumDescription("ExecuteScaleUpDownMachineSetsMachinesCount")]
        ExecuteScaleUpDownMachineSetsMachinesCount = 1774,


        #endregion

        #region Add RDM Disk (SCSI-Paravirtual, Physical Type), Existing Disk  to the VMware VM (Using PowerShell)

        [EnumDescription("CheckLUNPathExistAtVMsHost")]
        CheckLUNPathExistAtVMsHost = 1775,

        [EnumDescription("CheckRDMTypeDiskAttachedToVM")]
        CheckRDMTypeDiskAttachedToVM = 1776,

        [EnumDescription("CheckExistingNonRDMTypeDiskAttachedToVM")]
        CheckExistingNonRDMTypeDiskAttachedToVM = 1777,

        [EnumDescription("AddRDMDiskToVM")]
        AddRDMDiskToVM = 1778,

        [EnumDescription("AddExistingNonRDMTypeDiskToVM")]
        AddExistingNonRDMTypeDiskToVM = 1779,

        #endregion

        #region STCPay - Mango DB Cluster Replication Solution requirement.

        [EnumDescription("CheckPriorityStatus")]
        CheckPriorityStatus = 1780,

        [EnumDescription("CheckRolePrimaryORSecondary")]
        CheckRolePrimaryORSecondary = 1781,

        [EnumDescription("CheckPriorityStatusAtDRDC")]
        CheckPriorityStatusAtDRDC = 1782,

        [EnumDescription("CheckRoleSecondary")]
        CheckRoleSecondary = 1783,

        [EnumDescription("CheckRoleNotreachableOrHealthy")]
        CheckRoleNotreachableOrHealthy = 1784,

        [EnumDescription("PromoteDRDCSecondaryToPrimary")]
        PromoteDRDCSecondaryToPrimary = 1785,

        [EnumDescription("PromoteMainDCSecondarytoPrimary")]
        PromoteMainDCSecondarytoPrimary = 1786,

        [EnumDescription("ForceFailoverPromoteDRDCSecondaryToPrimary")]
        ForceFailoverPromoteDRDCSecondaryToPrimary = 1787,

        [EnumDescription("ForceFailbackPromoteMainDCToPrimary")]
        ForceFailbackPromoteMainDCToPrimary = 1788,


        #endregion




        #region VMware_RP4VM

        [EnumDescription("CheckConsistencyGroupProdReplicavRPAClusterDetails")]
        CheckConsistencyGroupProdReplicavRPAClusterDetails = 1789,

        [EnumDescription("CheckConsistencyGroupTransferStatusActive")]
        CheckConsistencyGroupTransferStatusActive = 1790,

        [EnumDescription("CheckConsistencyGroupRecoveryActivitiesStatus")]
        CheckConsistencyGroupRecoveryActivitiesStatus = 1791,

        [EnumDescription("RecoverPointForVMTestCopy")]
        RecoverPointForVMTestCopy = 1792,

        [EnumDescription("RecoverPointForVMTestCopy_StopActivity")]
        RecoverPointForVMTestCopy_StopActivity = 1793,

        [EnumDescription("RecoverPointForVMTestCopyForFailover")]
        RecoverPointForVMTestCopyForFailover = 1794,


        [EnumDescription("RecoverPointForVMTestCopyForFailover_StopActivity")]
        RecoverPointForVMTestCopyForFailover_StopActivity = 1795,

        [EnumDescription("RecoverPointForVMRecoveryActivitiesFailoverRoleChange")]
        RecoverPointForVMRecoveryActivitiesFailoverRoleChange = 1796,

        [EnumDescription("RecoverPointForVMExecuteFailoverRoleChange")]
        RecoverPointForVMExecuteFailoverRoleChange = 1797,

        [EnumDescription("RecoverPointForVMTestCopyForRecoverProduction")]
        RecoverPointForVMTestCopyForRecoverProduction = 1798,

        [EnumDescription("RecoverPointForVMTestCopyForRecoverProduction_StopActivity")]
        RecoverPointForVMTestCopyForRecoverProduction_StopActivity = 1799,


        [EnumDescription("RecoverPointForVMRecoveryActivitiesRecoverProduction")]
        RecoverPointForVMRecoveryActivitiesRecoverProduction = 1800,

        [EnumDescription("RecoverPointForVMExecuteRecoverProduction")]
        RecoverPointForVMExecuteRecoverProduction = 1801,





        #endregion
        #region ocicloudoracle

        [EnumDescription("OCI_DG_CheckDBSystemWithAssociatedDatabaseNameExist")]
        OCI_DG_CheckDBSystemWithAssociatedDatabaseNameExist = 1802,

        [EnumDescription("OCI_DG_CheckDatabaseSystemDataguardAssociationsRole")]
        OCI_DG_CheckDatabaseSystemDataguardAssociationsRole = 1803,

        [EnumDescription("OCI_DG_CheckApplyLag0SecondsDBSystemDataguardAssociationsatStandby")]
        OCI_DG_CheckApplyLag0SecondsDBSystemDataguardAssociationsatStandby = 1804,

        [EnumDescription("OCI_DG_ExecuteOracleDBDataguardAssociationsSwitchOver")]
        OCI_DG_ExecuteOracleDBDataguardAssociationsSwitchOver = 1805,

        #endregion  ocicloudoracle

        #region RedHatVirtulization


        [EnumDescription("CheckVMRunningStatusUpInStorageDomain")]
        CheckVMRunningStatusUpInStorageDomain = 1806,

        [EnumDescription("CheckAllVMsRunningStatusUpInStorageDomain")]
        CheckAllVMsRunningStatusUpInStorageDomain = 1807,

        [EnumDescription("CheckVMRunningStatusDownInStorageDomain")]
        CheckVMRunningStatusDownInStorageDomain = 1808,

        [EnumDescription("CheckAllVMsRunningStatusDownInStorageDomain")]
        CheckAllVMsRunningStatusDownInStorageDomain = 1809,

        [EnumDescription("ExecuteShutdownVMinStorageDomain")]
        ExecuteShutdownVMinStorageDomain = 1810,



        [EnumDescription("ExecuteShutDownForAllVMsInStorageDomain")]
        ExecuteShutDownForAllVMsInStorageDomain = 1811,

        [EnumDescription("ExecuteStartForVMInStorageDomain")]
        ExecuteStartForVMInStorageDomain = 1812,

        [EnumDescription("ExecuteStartForAllVMsInStorageDomain")]
        ExecuteStartForAllVMsInStorageDomain = 1813,

        [EnumDescription("CheckStorageDomainStatusInDatacenter")]
        CheckStorageDomainStatusInDatacenter = 1814,

        [EnumDescription("ExecuteDeactivateMaintenanceForStorageDomain")]
        ExecuteDeactivateMaintenanceForStorageDomain = 1815,

        [EnumDescription("ExecuteActivateForStorageDomain")]
        ExecuteActivateForStorageDomain = 1816,

        [EnumDescription("ExecuteDetachStorageDomainFromDatacenter")]
        ExecuteDetachStorageDomainFromDatacenter = 1817,

        [EnumDescription("ExecuteAttachDataStorageDomainToDatacenter")]
        ExecuteAttachDataStorageDomainToDatacenter = 1818,



        [EnumDescription("CheckVMExistForVMImportInStorageDomain")]
        CheckVMExistForVMImportInStorageDomain = 1819,

        [EnumDescription("ExecuteRegisterImportSingleVMFromStorageDomain")]
        ExecuteRegisterImportSingleVMFromStorageDomain = 1820,

        [EnumDescription("ExecuteRegisterImportAllVMsFromStorageDomain")]
        ExecuteRegisterImportAllVMsFromStorageDomain = 1821,

        [EnumDescription("CheckStorageDomainNamenotExistbeforeImport")]
        CheckStorageDomainNamenotExistbeforeImport = 1822,

        [EnumDescription("ImportDomanCheckifStorageDomainExistForImportToRHVHostFCType")]
        ImportDomanCheckifStorageDomainExistForImportToRHVHostFCType = 1823,

        [EnumDescription("ExecuteRemoveUnattachedStorageDomainFromHosts")]
        ExecuteRemoveUnattachedStorageDomainFromHosts = 1824,

        [EnumDescription("ImportDomainExecuteImportStorageDomainRHVHostFCType")]
        ImportDomainExecuteImportStorageDomainRHVHostFCType = 1825,

        #endregion RedHatVirtulization

        #region OCI_EXADATADB

        [EnumDescription("CheckExadataInfrastructureVMClusterStatus")]
        CheckExadataInfrastructureVMClusterStatus = 1826,

        [EnumDescription("CheckExadataVMClusterWithAssociatedDatabaseNameExist")]
        CheckExadataVMClusterWithAssociatedDatabaseNameExist = 1827,

        [EnumDescription("CheckExadataDatabase_DataGuardGroupMemberRole_Primary")]
        CheckExadataDatabase_DataGuardGroupMemberRole_Primary = 1828,

        [EnumDescription("CheckExadataDatabase_DataGuardGroupMemberRole_StandBy")]
        CheckExadataDatabase_DataGuardGroupMemberRole_StandBy = 1829,

        [EnumDescription("CheckApplyLag0forExadataDBDataGuardGroupStandby")]
        CheckApplyLag0forExadataDBDataGuardGroupStandby = 1830,

        [EnumDescription("ExecuteExadataDatabaseDataGuardGroup_SwitchOver_AtStandby")]
        ExecuteExadataDatabaseDataGuardGroup_SwitchOver_AtStandby = 1831,

        [EnumDescription("ExecuteExadataDatabaseDataGuardGroup_FailOver_AtStandby")]
        ExecuteExadataDatabaseDataGuardGroup_FailOver_AtStandby = 1832,

        #endregion OCI_EXADATADB

        
         #region Updated Mount Datastore as per CPROOT-40250 and solution team suggestion

         [EnumDescription("ModifiedMountDataStore")]
         ModifiedMountDataStore = 1833,

         #endregion

        #region AWSCloudEndure

         [EnumDescription("AWS_CheckCloudEndureMachineReplicationStatus")]
         AWS_CheckCloudEndureMachineReplicationStatus = 1834,

         [EnumDescription("AWS_Launch_Target_Machine_InRecoveryMode")]
         AWS_Launch_Target_Machine_InRecoveryMode = 1835,

         [EnumDescription("AWS_VerifyReplicaTargetRunningStatusAfterFailover")]
         AWS_VerifyReplicaTargetRunningStatusAfterFailover = 1836,

        #endregion AWSCloudEndure
    }

    [Serializable]
    public enum Workflow_DatabaseMongoClusterSubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("MongoDBCluster")]
        MongoDBCluster = 669900,
    }

    public enum Workflow_KubernetActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("OpenShift")]
        OpenShift = 1,

    }


    public enum OsTypeeBDR
    {
        FILE = 1,
        ESXI = 2,
        AWS = 3,
        PHY = 4,
        KUM = 5,
        VHM = 6,
        RAW = 7
    }

    public enum ActionTypeeBDR
    {
        MIGRATION_STARTED = 1,
        RESUMED_MIGRATION = 2,
        RESUMED_REPLICATION = 3,
        STARTED = 4,
        MAX_TRIES_OVER = 5, // = "Max Attempts to connect appliance is reached "

        MIGRATION_FAILED = 6,
        MIGRATION_IN_PROGRESS = 7,
        MIGRATION_COMPLETED = 8,
        MIGRATION_ABORTED = 9,

        //10-13
        LAUNCH_STARTED = 10,
        LAUNCH_FAILED = 11,
        LAUNCH_IN_PROGRESS = 12,
        LAUNCH_COMPLETED = 13,

        //14-17
        SIMULATION_STARTED = 14,
        SIMULATION_FAILED = 15,
        SIMULATION_IN_PROGRESS = 16,
        SIMULATION_COMPLETED = 17,

        //18-20

        REPLICATION_FAILED = 18,
        REPLICATION_IN_PROGRESS = 19,
        REPLICATION_COMPLETED = 20,

        //21-22
        FINAL_MIGRATION_COMPLETED = 21,
        WAITING_FOR_REPLICATION = 22,

        //23-24
        CANT_CREATE_MACHINE = 23,
        MACHINE_ALREADY_CREATED = 24, // = "Machine is already created in the target "
        MACHINE_CREATED_SUCCESSFULLY = 25,

        //26-28
        CANT_ADD_DISK = 26,
        DISK_ALREADY_ADDED = 27,//= "Disk is already added in the target "
        DISK_ADDED_SUCCESSFULLY = 28,
        //29-32
        MACHINE_POWERED_ON_SUCCESSFULLY = 29,
        CANT_POWER_ON_MACHINE = 30,
        MACHINE_CLONED_SUCCESSFULLY = 31,
        CANT_CLONE_MAHCINE = 32,

        //33-38
        CANT_CONNECT_TARGET = 33,
        CANCELED_MIGRATION = 34,
        ABORTED_ACTION = 36,
        IN_PROGRESS = 37,
        PAUSED_MIGRATION = 38,

        CANCELED_REPLICATION = 35,
        PAUSED = 39,
        SCHEDULED_MIGRATION = 40,
        Running = 41,
        RETRYING_TO_CONNECT = 42,
        YET_TO_START = 43,
    }

    [Serializable]
    public enum Workflow_HypervisorSubCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Common")]
        Common = 508,

        [EnumDescription("Hyper-V")]
        Hyperv = 509,

        [EnumDescription("Hyper-V Cluster")]
        HypervCluster = 515,

    }

    [Serializable]
    public enum MaintenanceMode
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Auto")]
        Auto = 1,

        [EnumDescription("Manual")]
        Manual = 2
    }

    [Serializable]
    public enum InfraWorkflowOperation
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("SwitchOverStart")]
        StartSwitchOver = 1,

        [EnumDescription("SwitchOverCompleted")]
        SwitchOverCompleted = 2,

        [EnumDescription("SwitchOverError")]
        SwitchOverError = 3,

        [EnumDescription("SwitchBackStart")]
        SwitchBackStart = 4,

        [EnumDescription("SwitchBackCompleted")]
        SwitchBackCompleted = 5,

        [EnumDescription("SwitchBackError")]
        SwitchBackError = 6,

        [EnumDescription("FailOverStart")]
        FailOverStart = 7,

        [EnumDescription("FailOverCompleted")]
        FailOverCompleted = 8,

        [EnumDescription("FailOverError")]
        FailOverError = 9,

        [EnumDescription("FailBackStart")]
        FailBackStart = 10,

        [EnumDescription("FailBackCompleted")]
        FailBackCompleted = 11,

        [EnumDescription("FailBackError")]
        FailBackError = 12,

        [EnumDescription("CustomStart")]
        CustomStart = 13,

        [EnumDescription("CustomCompleted")]
        CustomCompleted = 14,

        [EnumDescription("CustomError")]
        CustomError = 15,

        [EnumDescription("StartPowerOn")]
        StartPowerOn = 16,

        [EnumDescription("CustomError")]
        PowerOnCompleted = 17,

        [EnumDescription("PowerOnCompleted")]
        PowerOnError = 18,

        [EnumDescription("StartPowerOff")]
        StartPowerOff = 19,

        [EnumDescription("PowerOffCompleted")]
        PowerOffCompleted = 20,

        [EnumDescription("PowerOffError")]
        PowerOffError = 21
    }

    [Serializable]
    public enum WorkflowManagement
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("SwitchOver")]
        SwitchOver = 1,

        [EnumDescription("SwitchBack")]
        SwitchBack = 2,

        [EnumDescription("FailOver")]
        FailOver = 3,

        [EnumDescription("FailBack")]
        FailBack = 4,

        [EnumDescription("Custom")]
        Custom = 5,

        [EnumDescription("DataSynchronization")]
        DataSynchronization = 6,

        [EnumDescription("Monitor")]
        Monitor = 7,

        [EnumDescription("DR Ready")]
        DR_Ready = 8,
    }

    [Serializable]
    public enum AppDependency
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("InwardDataFlow")]
        InwardDataFlow = 1,

        [EnumDescription("OutwardDataFlow")]
        OutwardDataFlow = 2,

        [EnumDescription("Infrastructure")]
        Infrastructure = 3,

        [EnumDescription("ExternalInwardDataFlow")]
        ExternalInwardDataFlow = 4,

        [EnumDescription("ExternalOutwardDataFlow")]
        ExternalOutwardDataFlow = 5
    }

    [Serializable]
    public enum ApplicationDependencyType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("InwardDataFlow")]
        InwardDataFlow = 1,

        [EnumDescription("OutwardDataFlow")]
        OutwardDataFlow = 2,

        [EnumDescription("Infrastructure")]
        Infrastructure = 3,

        [EnumDescription("ExternalInwardDataFlow")]
        ExternalInwardDataFlow = 4,

        [EnumDescription("ExternalOutwardDataFlow")]
        ExternalOutwardDataFlow = 5
    }

    [Serializable]
    public enum ConditionalOperationType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Skip")]
        Skip = 1,

        [EnumDescription("Retry")]
        Retry = 2,

        [EnumDescription("Abort")]
        Abort = 3,

        [EnumDescription("Next")]
        Next = 4,

        [EnumDescription("FailedNext")]
        FailedNext = 5,

        [EnumDescription("Reload")]
        Reload = 6
    }

    [Serializable]
    public enum WorkflowExecutionType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Auto")]
        Auto = 1,

        [EnumDescription("Manual")]
        Manual = 2
    }

    [Serializable]
    public enum WorkflowActionStatus
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Pending")]
        Pending = 1,

        [EnumDescription("Running")]
        Running = 2,

        [EnumDescription("Success")]
        Success = 3,

        [EnumDescription("Completed")]
        Completed = 4,

        [EnumDescription("Abort")]
        Abort = 5,

        [EnumDescription("Retry")]
        Retry = 6,

        [EnumDescription("Skip")]
        Skip = 7,

        [EnumDescription("Error")]
        Error = 8,

        [EnumDescription("Aborted")]
        Aborted = 9,

        [EnumDescription("Wait")]
        Wait = 10,

        [EnumDescription("Start")]
        Start = 11,

        [EnumDescription("WaitForCredential")]
        WaitForCredential = 12,

        [EnumDescription("WaitForError")]
        WaitForError = 13,

        [EnumDescription("Pause")]
        Pause = 14

    }

    [Serializable]
    public enum Workflow_WindowsActionCategoryType
    {

        [EnumDescription("DiskPart")]
        DiskPart = 4,

        [EnumDescription("Common")]
        Common = 5,

        [EnumDescription("ActiveDirectory")]
        ActiveDirectory = 6,

        //[EnumDescription("TIMEFINDER")]
        //TIMEFINDER = 3,

        [EnumDescription("DFSServerAutomation")]
        DFSServerAutomation = 10,

        [EnumDescription("Win2k8")]
        Win2k8 = 11,

        [EnumDescription("CitrixNetScaler")]
        CitrixNetScaler = 12,

        [EnumDescription("PowerShellDiskPart")]
        PowerShellDiskPart = 13,



    }

    [Serializable]
    public enum Workflow_EMCActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("DMX SRDF")]
        DMXSRDF = 1,

        [EnumDescription("RECOVER POINT")]
        RECOVER_POINT = 2,

        [EnumDescription("Timefinder")]
        Timefinder = 3,

        [EnumDescription("EMCIsilon")]
        EMCISILON = 4,

        [EnumDescription("STAR")]
        STAR = 5,

        //sarath
        [EnumDescription("Data Domain")]
        DataDomain = 6,

        [EnumDescription("AVAMAR")]
        AVAMAR = 7,


        [EnumDescription("CyberVaultRecovery")]
        CyberVaultRecovery = 8,


    }

    [Serializable]
    public enum Workflow_StorageIBMActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("DISK Global Mirror")]
        DISKGlobalMirror = 6100,


    }

    [Serializable]
    public enum Workflow_StorageHDSActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("HUR")]
        HUR = 6300,


    }

    [Serializable]
    public enum Workflow_DatabaseOracleSubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Data Guard")]
        Data_Guard = 6400,

        [EnumDescription("DataSync")]
        DataSync = 6500,

        [EnumDescription("DGBroker")]
        DGBroker = 61600,

        [EnumDescription("ODG12C")]
        ODG12C = 61699,

        [EnumDescription("WebLogic")]
        WebLogic = 61700,

        [EnumDescription("DataGuardAPI")]
        DataGuardAPI = 61800,

        [EnumDescription("OracleDBTableCountMatch")]
        OracleDBTableCountMatch = 61801,

    }
    //added 01.12.2017
    [Serializable]
    public enum Workflow_DatabaseASMOracleSubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("DataSync")]
        DataSync = 70000,
    }
    [Serializable]
    public enum Workflow_DatabaseMSSQL2008SubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("NLS(Native Log Shipping)")]
        NLS = 7000,

        [EnumDescription("MSSQL Mirroring")]
        DatabaseMirroring = 10200,

        [EnumDescription("MSSQLCluster")]
        MSSQLCluster = 10900,

    }

    [Serializable]
    public enum Workflow_StorageNetAppActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("SnapMirror")]
        SnapMirror = 6200,


    }

    [Serializable]
    public enum Workflow_DatabasePostgres9SubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("NativeReplication")]
        NativeReplication = 6600,


        [EnumDescription("PostgresDBTableCountMatch")]
        PostgresDBTableCountMatch = 6601,



    }

    [Serializable]
    public enum Workflow_ExchangeConnectorSubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("ExchangeConnectorAbility")]
        ExchangeConnectorAbility = 9600,

    }

    [Serializable]
    public enum Workflow_Databasesybase9SubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("DataSync")]
        DataSync = 67,

        [EnumDescription("SRS")]
        SRS = 110,

    }

    [Serializable]
    public enum Workflow_DatabaseMSSQLSubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("MSSQL-AlwaysON")]
        MSSQLAlwaysON = 6700,

        [EnumDescription("MSSQL-AlwaysONFailover")]
        MSSQLAlwaysONFailOver = 68101,

        [EnumDescription("MSSQL-AlwaysONPauseResume")]
        MSSQLAlwaysONPauseResume = 68102,
    }

    [Serializable]
    public enum Workflow_DatabaseMYSQLSubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("NativeReplication")]
        NativeReplication = 6800,



    }

    [Serializable]
    public enum Workflow_DatabaseNLSSubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("NativeReplication")]
        NativeReplication = 6900,

        [EnumDescription("Database Mirroring")]
        DatabaseMirroring = 102090,
    }

    [Serializable]
    public enum Workflow_DatabaseMSSQLServer2000SubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("DataSync")]
        DataSync = 7300,



    }

    [Serializable]
    public enum Workflow_HPSubCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Common")]
        Common = 1,

        [EnumDescription("Base24")]
        Base24 = 2,

    }

    [Serializable]
    public enum WorkflowActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        //[EnumDescription("COMMON")]
        //Common = 1,
        [EnumDescription("Generic Database Actions")]
        GenericDatabaseActions = 1,

        [EnumDescription("IBM")]
        IBM = 2,

        [EnumDescription("SCR")]
        SCR = 3,

        [EnumDescription("EMC")]
        EMC = 4,

        [EnumDescription("HDS")]
        HDS = 5,

        [EnumDescription("MS-SQL")]
        MSSQLServer = 6,

        //Commented As Per QIA Client
        //[EnumDescription("MS-SQL Server 2012")]
        //MSSQLServer2012 = 6,

        //[EnumDescription("MS-SQL Server 2000")]
        //MSSQLServer2000 = 7,

        //[EnumDescription("DATA GUARD")]
        //Dataguard = 8,

        [EnumDescription("HPUX")]
        HPUX = 9,

        [EnumDescription("vCenter")]
        vCenter = 10,

        [EnumDescription("IBM AIX")]
        IBMAIX = 11,

        [EnumDescription("WINDOWS")]
        Windows = 12,

        [EnumDescription("NetApp")]
        Netapp = 13,

        [EnumDescription("IBM DB2")]
        IBMDB2 = 14,

        [EnumDescription("Hypervisor")]
        Hypervisor = 15,

        [EnumDescription("HADR")]
        HADR = 16,

        [EnumDescription("Routers")]
        Routers = 17,

        [EnumDescription("WORKFLOW")]
        Workflow = 18,

        [EnumDescription("Oracle")]
        Oracle = 19,

        [EnumDescription("COMMON")]
        Oscommon = 20,

        [EnumDescription("HTTP")]
        HTTP = 21,

        [EnumDescription("VxVM")]
        VxVM = 22,

        [EnumDescription("Oracle Solaris")]
        OracleSolaris = 23,

        //Commented As Per QIA Requirement
        //[EnumDescription("MS-SQL Server 2008")]
        //MSSQLServer2008 = 24,

        [EnumDescription("SRM ")]
        SRM = 25,

        [EnumDescription("SRDF ")]
        SRDF = 26,

        [EnumDescription("Amazon ")]
        Amanzons3 = 27,

        [EnumDescription("DAG")]
        DAG = 28,

        [EnumDescription("PostgreSQL")]
        PostgreSQL = 29,

        [EnumDescription("DAG_2010")]
        DAG_2010 = 30,

        [EnumDescription("Infoblox")]
        Infoblox = 31,

        [EnumDescription("Switches")]
        Switches = 32,

        [EnumDescription("HP")]
        HP = 33,

        [EnumDescription("Firewall")]
        Firewall = 34,

        [EnumDescription("DoubleTake")]
        DoubleTake = 35,

        [EnumDescription("Azure")]
        Azure = 36,

        [EnumDescription("Sybase")]
        Sybase = 37,

        [EnumDescription("MySQL")]
        MySQL = 38,

        [EnumDescription("MSSQLMirror")]
        MSSQLMirror = 39,

        [EnumDescription("DAG_FO")]
        DAG_FO = 40,

        //[EnumDescription("Generic Database Actions")]
        //GenericDatabaseActions = 41,

        [EnumDescription("Microsoft")]
        Microsoft = 41,

        [EnumDescription("Oracle Ops Center")]
        ORACLEOPSCENTER = 42,

        [EnumDescription("Sun ILOM")]
        Solaris = 43,

        [EnumDescription("AIX")]
        AIX = 44,

        [EnumDescription("MaxDB")]
        MaxDB = 45,

        [EnumDescription("MAINFRAME")]
        MAINFRAME = 46,

        [EnumDescription("eBDR")]
        eBDR = 47,

        [EnumDescription("CP")]
        CP = 48,

        //Commented As Per QIA Requirement
        //[EnumDescription("MS-SQL Server 2014")]
        //MSSQLServer2014 = 49,

        [EnumDescription("SoftLayer")]
        SoftLayer = 50,

        [EnumDescription("AIX/LINUX/UNIX")]
        Aixlinuxunix = 51,

        [EnumDescription("PowerCLI")]
        PowerCLI = 52,

        [EnumDescription("IIS")]
        IIS = 53,
        [EnumDescription("MSSQLCluster")]
        MSSQLCluster = 54,

        [EnumDescription("ASM Oracle")]
        ASMOracle = 55,

        [EnumDescription("Veeam")]
        Veeam = 56,

        [EnumDescription("HP3PAR")]
        HP3PAR = 57,

        [EnumDescription("Hadoop")]
        Hadoop = 58,

        [EnumDescription("eBDRPerpetuuiti")]
        eBDRPerpetuuiti = 59,

        [EnumDescription("GoldenGate")]
        GoldenGate = 60,

        [EnumDescription("RSync")]
        RSync = 61,

        [EnumDescription("ActifioDBBackUp")]
        ActifioDBBackUp = 62,

        [EnumDescription("Nutanix")]
        Nutanix = 63,

        [EnumDescription("DAG_2016")]
        DAG_2016 = 64,

        [EnumDescription("Cluster")]
        Cluster = 65,

        [EnumDescription("FileHandling")]
        FileHandling = 66,

        [EnumDescription("OracleRsync")]
        OracleRsync = 67,

        [EnumDescription("NutanixPDFailOver")]
        NutanixPDFailOver = 68,

        [EnumDescription("vmWare")]
        vmWare = 69,

        [EnumDescription("AzureLoadBalancer")]
        AzureLoadBalancer = 70,

        [EnumDescription("Rubrik")]
        Rubrik = 71,

        [EnumDescription("AzureToHyperV")]
        AzureToHyperV = 72,

        [EnumDescription("MongoDB")]
        MongoDB = 73,


        [EnumDescription("TCLCloud")]
        TCLCloud = 74,


        [EnumDescription("NS1_DNS")]
        NS1_DNS = 75,
        //vamsi azure gateway replication
        [EnumDescription("AzureApplicationGateway")]
        AzureApplicationGateway = 76,

        [EnumDescription("OracleCloud")]
        OracleCloud = 77,


        [EnumDescription("NSXT")]
        NSXT = 78,

        [EnumDescription("Rackware")]
        Rackware = 79,

        [EnumDescription("F5")]
        F5Actions = 80,

        [EnumDescription("HUAWEI")]
        HUAWEI = 81,

        [EnumDescription("OracleCloud_DNS")]
        OracleCloud_DNS = 82,

        [EnumDescription("MSSQLBroker")]
        MSSQLBroker = 83,


        [EnumDescription("Redis")]
        Redis = 84,

        [EnumDescription("G42")]
        G42 = 85,

        //riyadh
        [EnumDescription("Kubernet")]
        Kubernet = 86,

        [EnumDescription("RedHatVirtualization")]
        RedHatVirtualization = 87,

        [EnumDescription("OCI_EXADATADB")]
        OCI_EXADATADB = 88,


    }



    [Serializable]
    public enum DAGStatusResult
    {
        [EnumDescription("Started")]
        Started = 0,

        [EnumDescription("Starting")]
        Starting = 1,

        [EnumDescription("Stopping")]
        Stopping = 2,

        [EnumDescription("Not Running")]
        NotRunning = 3
    }
    //EnumId 2000
    [Serializable]
    public enum OracleDGGroup
    {
        [EnumDescription("Production(PR)")]
        PRServerMonitor = 2001,

        [EnumDescription("StandBy(DR)")]
        DRServerMonitor = 2002,

        [EnumDescription("Production(PR) Database")]
        PRDatabasecheck = 2003,

        [EnumDescription("StandBY(DR) Database")]
        DRDatabaseCheck = 2004,

        [EnumDescription("Production(PR) Database Listner")]
        PRDatabaseListner = 2005,

        [EnumDescription("StandBy(DR) Database Listner")]
        DRDatabaseListner = 2006,

        [EnumDescription("DataLag")]
        DataLagMonitor = 2007,

        [EnumDescription("DataGuard State")]
        DataGuardStateMonitor = 2008,

        [EnumDescription("Archive Log ")]
        ArchiveLogMonitor = 2009,

        [EnumDescription("Archive Log Deletion")]
        PRArchiveLogDelete = 2010,

        [EnumDescription("Archive Log Deletion")]
        DRArchiveLogDelete = 2011
    }

    //EnumId 20
    [Serializable]
    public enum ExchangeSCR
    {
        [EnumDescription("Production(PR)")]
        PRServerMonitor = 201,

        [EnumDescription("StandBy(DR)")]
        DRServerMonitor = 202,

        [EnumDescription("Production(PR) Database")]
        PRDatabasecheck = 203,

        [EnumDescription("StandBY(DR) Database")]
        DRDatabaseCheck = 204,

        [EnumDescription("Production(PR) Database Listner")]
        PRDatabaseListner = 205,

        [EnumDescription("StandBy(DR) Database Listner")]
        DRDatabaseListner = 206,

        [EnumDescription("DataLag")]
        DataLagMonitor = 207,

        [EnumDescription("DataGuard State")]
        DataGuardStateMonitor = 208,

        [EnumDescription("Archive Log ")]
        ArchiveLogMonitor = 209,

        [EnumDescription("Archive Log Deletion")]
        PRArchiveLogDelete = 210,

        [EnumDescription("Archive Log Deletion")]
        DRArchiveLogDelete = 211
    }

    [Serializable]
    public enum VMWareGroup
    {
        [EnumDescription("VMWare with DataSync")]
        PRMoniotrDatabase = 0,

        [EnumDescription("VMWare with Globalmirror")]
        NRMoniotrDatabase,

        [EnumDescription("VMWare with Snapmirror")]
        DRMoniotrDatabase
    }

    [Serializable]
    public enum GroupExchangeDAg2010
    {
        [EnumDescription("Group_Exchange PR Monitor Database")]
        PRMoniotrDatabase = 0,

        [EnumDescription("Group_Exchange NR Monitor Database")]
        NRMoniotrDatabase,

        [EnumDescription("Group_Exchange DR Monitor Database")]
        DRMoniotrDatabase
    }

    [Serializable]
    public enum GeoupExchangeDB
    {
        [EnumDescription("GeoupExchangeDB Monitor Database")]
        PRMoniotrDatabase = 0,

        [EnumDescription("GeoupExchangeDB Monitor Database")]
        NRMoniotrDatabase,

        [EnumDescription("GeoupExchangeDB Monitor Database")]
        DRMoniotrDatabase
    }

    [Serializable]
    public enum GroupExchnageDAG
    {
        [EnumDescription("GroupExchnageDAG PR Monitor Database")]
        PRMoniotrDatabase = 0,

        [EnumDescription("GroupExchnageDAG NR Monitor Database")]
        NRMoniotrDatabase,

        [EnumDescription("GroupExchnageDAG DR Monitor Database")]
        DRMoniotrDatabase
    }

    [Serializable]
    public enum OdbFcGroup
    {
        [EnumDescription("Production(PR)")]
        PRServerMonitor = 0,

        [EnumDescription("StandBy(DR)")]
        DRServerMonitor,

        [EnumDescription("Production(PR) Database")]
        PRDatabasecheck,

        [EnumDescription("StandBY(DR) Database")]
        DRDatabaseCheck,

        [EnumDescription("Production(PR) Database Listner")]
        PRDatabaseListner,

        [EnumDescription("StandBy(DR) Database Listner")]
        DRDatabaseListner,

        [EnumDescription("DataLag")]
        DataLagMonitor,

        [EnumDescription("Archive Log Deletion")]
        PRArchiveLogDelete,

        [EnumDescription("Archive Log Deletion")]
        DRArchiveLogDelete
    }

    [Serializable]
    public enum DB2Group
    {
        [EnumDescription("DB2 Server Instance State")]
        PRDB2ServerMonitor = 0,

        [EnumDescription("DB2 Server Instance State")]
        DRDB2ServerMonitor,

        [EnumDescription("DB2 Database Name")]
        DB2DatabaseMonitor,

        [EnumDescription("Production(PR) Database Status")]
        PRDatabaseMonitor,

        [EnumDescription("StandBy(DR) Database Status")]
        DRDatabaseMonitor,

        [EnumDescription("DB2 Instance Status")]
        DB2InstanceMonitor,

        [EnumDescription("DB2 Database Recovery Model State")]
        DB2DatabaseRecoveryModelMonitor,

        [EnumDescription("DB2 Database Recovery State")]
        DB2DatabaseRecoveryStateMonitor,

        [EnumDescription("HADR Status")]
        HADRMonitor,

        [EnumDescription("Database Backup Pending State")]
        DatabaseBackupPendingMonitor,

        [EnumDescription("Database Rollforward Pending State")]
        DatabaseRollforwardPendingMonitor,

        [EnumDescription("Database Restore Pending State Status")]
        DatabaseRestorePendingStateMonitor,

        [EnumDescription("HADR status-HADR ROLE Status")]
        HADRStatusHadrRoleMonitor,

        [EnumDescription("HADR status-HADR Connect Status")]
        HADRStatusConnectMonitor,

        [EnumDescription("HADR HADR Synchronization mode Status")]
        HadhadrSynchronizationModeMonitor,

        [EnumDescription("HADR status-Primary Member IP Address Status")]
        HADRStatusPrimaryMemberIPAddressMonitor,

        [EnumDescription("HADR status-Standby Member IP Address Status")]
        HADRStatusStandbyMemberIPAddressMonitor,

        [EnumDescription("HADR status-Primary Instance Name Status")]
        HADRStatusPrimaryInstanceNameMonitor,

        [EnumDescription("HADR status-Standby Instance Name")]
        HADRStatusStandbyInstanceNameMonitor,

        [EnumDescription("HADR status-HADR Local Service (Name / Port No.)")]
        HadrLocalServiceMonitor,

        [EnumDescription("HADR status-HADR Remote Service (Name / Port No.)")]
        HadrRemoteService,

        [EnumDescription("HADR status-Heartbeat Interval (seconds) Status)")]
        HADRHeartbeatIntervalMonitor,

        [EnumDescription("HADR status-HADR Primary Log File (Name) Status)")]
        HADRPrimaryLogFileMonitor,

        [EnumDescription("HADR status-HADR Standby Log File (Name) Status")]
        HADRStandbyLogFileMonitor,

        [EnumDescription("HADR status-STANDBY REPLAY LOG FILE Status)")]
        HadrStandByReplayLogfileMonitor,

        [EnumDescription("HADR status-HADR Primary Log LSN Status)")]
        PrHADRPrimaryLogLSNMonitor,

        [EnumDescription("HADR status-HADR Standby Log LSN Status)")]
        DrHADRStandbyLogLSNMonitor,

        [EnumDescription("HADR status-Database Log file Current_LSN Number Status)")]
        HADRDatabaseLogfileCurrentLSNNumberMonitor,

        [EnumDescription("HADR status-Database Log file Name for Current_LSN Status)")]
        HADRDatabaseLogfileNameforCurrentLSNMonitor,

        [EnumDescription("HADR status-HADR Primary Log Page Status)")]
        HADRPrimaryLogPageMonitor,

        [EnumDescription("HADR status-HADR Standby Log Page Status)")]
        HADRStandbyLogPageMonitor,

        [EnumDescription("HADR status-STANDBY REPLAY LOG PAGE Status)")]
        HadrStandbyReplayLogPage,

        [EnumDescription("HADR status-HADR Log gap (bytes) )")]
        HADRLogGapbytesMonitor,

        [EnumDescription("HADR status-STANDBY RECV REPLAY GAP)")]
        HADRStandbyRecvReplaygapMonitor,

        [EnumDescription("HADR status-Primary Log Time Status)")]
        HADRPrimaryLogTimeMonitor,

        [EnumDescription("HADR status-Standby Log Time Status)")]
        HADRStandbyLogTimeMonitor,

        [EnumDescription("HADR status-STANDBY REPLAY LOG TIME Status)")]
        HADRStandbyReplaylogTimeMonitor,

        [EnumDescription("Production(PR)Replication DataLag)")]
        PRDataLagMonitor,

        [EnumDescription("StandBy(DR) Replication DataLag)")]
        DRDataLagMonitor
    }

    [Serializable]
    public enum ExchangeDemo
    {
        [EnumDescription("Production(PR)")]
        PRServerMonitor = 0,

        [EnumDescription("StandBy(DR)")]
        DRServerMonitor,

        [EnumDescription("Production(PR) Database")]
        PRDatabasecheck,

        [EnumDescription("StandBY(DR) Database")]
        DRDatabaseCheck,

        [EnumDescription("Production(PR) Database Listner")]
        PRDatabaseListner,

        [EnumDescription("StandBy(DR) Database Listner")]
        DRDatabaseListner,

        [EnumDescription("DataLag")]
        DataLagMonitor,

        [EnumDescription("DataGuard State")]
        DataGuardStateMonitor,

        [EnumDescription("Archive Log ")]
        ArchiveLogMonitor,

        [EnumDescription("Archive Log Deletion")]
        PRArchiveLogDelete,

        [EnumDescription("Archive Log Deletion")]
        DRArchiveLogDelete
    }

    [Serializable]
    public enum RacdbQa
    {
        [EnumDescription("Production(PR)")]
        PRServerMonitor = 0,

        [EnumDescription("StandBy(DR)")]
        DRServerMonitor,

        [EnumDescription("Production(PR) Database")]
        PRDatabasecheck,

        [EnumDescription("StandBY(DR) Database")]
        DRDatabaseCheck,

        [EnumDescription("Production(PR) Database Listner")]
        PRDatabaseListner,

        [EnumDescription("StandBy(DR) Database Listner")]
        DRDatabaseListner,

        [EnumDescription("DataLag")]
        DataLagMonitor,

        [EnumDescription("DataGuard State")]
        DataGuardStateMonitor,

        [EnumDescription("Archive Log ")]
        ArchiveLogMonitor,

        [EnumDescription("Archive Log Deletion")]
        PRArchiveLogDelete,

        [EnumDescription("Archive Log Deletion")]
        DRArchiveLogDelete
    }

    [Serializable]
    public enum SqlSvr2012Nls
    {
        [EnumDescription("Production(PR)")]
        PRServerMonitor = 0,

        [EnumDescription("StandBy(DR)")]
        DRServerMonitor,

        [EnumDescription("Data Lag")]
        PRDatabasecheck,

        [EnumDescription("Data Lag")]
        DRDatabaseCheck,

        [EnumDescription("Data Lag Listner")]
        PRDatabaseListner,

        [EnumDescription("Data Lag Listner")]
        DRDatabaseListner,

        [EnumDescription("DataLagMonitor")]
        DataLagMonitor,

        [EnumDescription("DataGuardMonitor")]
        DataGuardMonitor,

        [EnumDescription("Archive Log ")]
        ArchiveLogGap,

        [EnumDescription("Archive Log Deletion Successful")]
        PRArchiveLogDelete,

        [EnumDescription("Archive Log Deletion Successful")]
        DRArchiveLogDelete
    }

    [Serializable]
    public enum SQL_2K8NativeLShipping
    {
        [EnumDescription("SQL Server Instance State")]
        PrsqlServerInstanceMonitor = 4001,

        [EnumDescription("SQL Server Instance State")]
        DrsqlServerInstanceMonitor = 4002,

        [EnumDescription("Transaction Log Shipping Status")]
        PRTransactionLogShippingMonitor = 4003,

        [EnumDescription("Transaction Log Shipping Status")]
        DRTransactionLogShippingMonitor = 4004,

        [EnumDescription("Backup Job Status")]
        PRBackupJobMonitor = 4005,

        [EnumDescription("Copy Job Status")]
        DRCopyJobMonitor = 4006,

        [EnumDescription("Restore Job Status")]
        DRRestoreJobMonitor = 4007,

        [EnumDescription("PR SQL Service")]
        PrsqlServerServiceMonitor = 4008,

        [EnumDescription("DR SQL Service")]
        DrsqlServerServiceMonitor = 4009,

        [EnumDescription("PRSQLServer Agent")]
        PrsqlServerAgentMonitor = 4010,

        [EnumDescription("DRSQLServer Agent")]
        DrsqlServerAgentMonitor = 4011,

        [EnumDescription("DataLag")]
        DataLagMonitor = 4012
    }

    [Serializable]
    public enum SQL_2K8NativeLShippingEvent
    {
        [EnumDescription("Failed Disable BackupJob Primary")]
        DisableBackupJobPrimary = 4001,

        [EnumDescription("Failed Disable Copy Job DR")]
        DisableCopyJobDR = 4002,

        [EnumDescription("Failed Disable Restore Job DR")]
        DisableRestoreJobDR = 4003,

        [EnumDescription("Failed MSSql Kill Process PR")]
        MSSqlKillProcessPR = 4004,

        [EnumDescription("Failed MSSql Verify LogSequence")]
        MSSqlVerifyLogSequence = 4005,

        [EnumDescription("Failed Restore Last LogNative")]
        RestoreLastLogNative = 4006,

        [EnumDescription("Failed Restore LogShipping Primary")]
        RestoreLogShippingPrimary = 4007,

        [EnumDescription("Failed Restore Logship Secondary")]
        RestoreLogshipSecondary = 4008,

        [EnumDescription("Failed Update Secondary ServerBackUp Job")]
        UpdateSecondaryServerBackUpJob = 4009,

        [EnumDescription("Failed Update Secondary ServerCopy Job")]
        UpdateSecondaryServerCopyJob = 4010,

        [EnumDescription("Failed Update Secondary ServerRestore Job")]
        UpdateSecondaryServerRestoreJob = 4011,

        [EnumDescription("Failed Migrate Loging PR")]
        MigrateLogingPR = 4012,

        [EnumDescription("Failed Migrate Loging DR")]
        MigrateLogingDR = 4013,

        [EnumDescription("Failed Restore Secondary With Recovery")]
        RestoreSecondaryWithRecovery = 4014,

        [EnumDescription("Failed Native Remove PRDRLS")]
        NativeRemovePRDRLS = 4015,

        [EnumDescription("Failed Native Remove LSPri")]
        NativeRemoveLSPri = 4016,

        [EnumDescription("Failed Native Remove LSSEC")]
        NativeRemoveLSSEC = 4017,

        [EnumDescription("Failed Monitoring Service")]
        MonitoringService = 4018,

        [EnumDescription("Failed Migrate ServerRole DR")]
        MigrateServerRoleDR = 4019,

        [EnumDescription("Failed MSSql Kill Process DR")]
        MSSqlKillProcessDR = 4020,

        [EnumDescription("Failed MSSql Set DB Option DR")]
        MSSqlSetDBOptionDR = 4021,

        [EnumDescription("Failed MSSql Set Last File Flag")]
        MSSqlSetLastFileFlag = 4022,

        [EnumDescription("Failed Migrate ServerRole PR")]
        MigrateServerRolePR = 4023,

        [EnumDescription("Failed Native Generate Last Log")]
        NativeGenerateLastLog = 4024
    }

    [Serializable]
    public enum OracleDGGroupPriority
    {
        [EnumDescription("High")]
        High = 0,

        Normal
    }

    [Serializable]
    public enum BusinessServicePriority
    {
        [EnumDescription("Low")]
        Low = 1,

        [EnumDescription("Medium")]
        Medium = 2,

        [EnumDescription("High")]
        High = 3
    }

    [Serializable]
    public enum InfraObjectPriority
    {
        [EnumDescription("Low")]
        Low = 3,

        [EnumDescription("Medium")]
        Medium = 2,

        [EnumDescription("High")]
        High = 1
    }

    [Serializable]
    public enum AlertManagerPriority
    {
        [EnumDescription("High")]
        High = 0,

        [EnumDescription("Low")]
        Low = 1,

        [EnumDescription("VeryHigh")]
        VeryHigh = 2,

        [EnumDescription("Normal")]
        Normal = 3
    }

    [Serializable]
    public enum Description
    {
        [EnumDescription("Dummy")]
        Test1 = 0,

        Test2,
        Test3
    }

    [Serializable]
    public enum VmWareDescription
    {
        [EnumDescription("Dummy")]
        Test1 = 0,

        Test2,
        Test3
    }

    [Serializable]
    public enum OracleDB
    {
        [EnumDescription("9i")]
        Oracle9iV = 9,

        [EnumDescription("10g")]
        Oracle10gV = 10,

        [EnumDescription("11g")]
        Oracle11gV = 11,

        [EnumDescription("12c")]
        Oracle12cV = 12,

        [EnumDescription("18c")]
        Oracle18cV = 13,

        [EnumDescription("19c")]
        Oracle19cV = 14,

        [EnumDescription("21c")]
        Oracle21cV = 15
    }

    [Serializable]
    public enum SqlDB
    {
        [EnumDescription("2000")]
        MSSQL2000 = 2000,
        [EnumDescription("2005")]
        MSSQL2005 = 2005,

        [EnumDescription("2008")]
        MSSQL2008 = 2008,

        [EnumDescription("2012")]
        MSSQL2012 = 2012,

        [EnumDescription("2014")]
        MSSQL2014 = 2014,

        [EnumDescription("2016")]
        MSSQL2016 = 2016,

        [EnumDescription("2019")]
        MSSQL2019 = 2019
    }

    [Serializable]
    public enum CloudantDBVersion
    {
        [EnumDescription("1.0.0")]
        CloudantDB100 = 100,

        [EnumDescription("1.1.0")]
        CloudantDB110 = 110

    }

    [Serializable]
    public enum MariaDBType
    {
        [EnumDescription("V10.0")]
        mariadb300 = 300
    }

    public enum OracleCloudDBType
    {
        [EnumDescription("*********.0")]
        OracleCloudDB = 400
    }


    [Serializable]
    public enum MsSqlFullDB
    {
        [EnumDescription("2005")]
        MsSqlFullDB2005 = 2005,

        [EnumDescription("2008")]
        MsSqlFullDB2008 = 2008,

        [EnumDescription("2012")]
        MsSqlFullDB2012 = 2012,

        [EnumDescription("2014")]
        MsSqlFullDB2014 = 2014,

        [EnumDescription("2016")]
        MsSqlFullDB2016 = 2016,

        [EnumDescription("2018")]
        MsSqlFullDB2018 = 2018
    }

    [Serializable]
    public enum SybaseDBType
    {
        [EnumDescription("12")]
        Sybase12 = 12,
    }

    [Serializable]
    public enum SybaseWithSRSDBType
    {
        [EnumDescription("15")]
        SybaseWithSRS15 = 15,
    }

    [Serializable]
    public enum SybaseWithRSHadrDBType
    {
        [EnumDescription("16")]
        SybaseWithRsHadr16 = 16,

        [EnumDescription("15.6")]
        SybaseWithRsHadr15 = 15,
    }

    [Serializable]
    public enum MaxDBType
    {
        [EnumDescription("7.6")]
        MaxDB76 = 6,
        [EnumDescription("7.7")]
        MaxDB77 = 7,
        [EnumDescription("7.8")]
        MaxDB78 = 8,
        [EnumDescription("7.9")]
        MaxDB79 = 9,
    }

    public enum MongoDBType
    {
        [EnumDescription("3.0")]
        MongoDB1 = 0,
        [EnumDescription("3.2")]
        MongoDB2 = 2,
        [EnumDescription("3.4")]
        MongoDB3 = 4,
        [EnumDescription("3.6")]
        MongoDB4 = 6,
        [EnumDescription("4.0")]
        MongoDB5 = 8,
        [EnumDescription("6.6")]
        MongoDB6 = 10,

    }

    [Serializable]
    public enum HANADBType
    {
        [EnumDescription("HANA 2 SP02")]
        HANA2SP02 = 202

    }

    [Serializable]
    public enum ExchangeDB
    {
        [EnumDescription("2007")]
        Exchange2007 = 2007,

        [EnumDescription("2010")]
        Exchange2010 = 2010,

        [EnumDescription("2012")]
        Exchange2012 = 2012
    }

    [Serializable]
    public enum ExchangeDAG
    {
        [EnumDescription("ExchangeDAG-2007")]
        Exchange2007 = 11,
        [EnumDescription("ExchangeDAG-2010")]
        Exchange2010 = 12,
        [EnumDescription("ExchangeDAG-2013")]
        Exchange2013 = 13,
        [EnumDescription("ExchangeDAG-2016")]
        Exchange2016 = 14,
        [EnumDescription("ExchangeDAG-2019")]
        Exchange2019 = 15

    }

    [Serializable]
    public enum DB2DB
    {
        [EnumDescription("9.1")]
        DB291 = 91,

        [EnumDescription("9.7")]
        DB297 = 97,

        [EnumDescription("10.1")]
        DB2101 = 101,

        [EnumDescription("10.5")]
        DB2105 = 105,

        [EnumDescription("11.5")]
        DB2102 = 102,

    }

    [Serializable]
    public enum MySQLDB
    {
        [EnumDescription("V3.51")]
        mysql351 = 351,
        [EnumDescription("V4.0")]
        mysql400 = 400,
        [EnumDescription("V4.1")]
        mysql410 = 410,
        [EnumDescription("V5.0")]
        mysql500 = 500,
        [EnumDescription("V5.1")]
        mysql510 = 510,
        [EnumDescription("V5.5")]
        mysql550 = 550,
        [EnumDescription("V5.6")]
        mysql560 = 560,
        [EnumDescription("V5.7")]
        mysql570 = 570,
        [EnumDescription("V8.0")]
        mysql80 = 80
    }

    [Serializable]
    public enum POSTGRESQLDB
    {
        [EnumDescription("7.0")]
        postgresql70 = 70,

        [EnumDescription("8.0")]
        postgresql80 = 80,

        [EnumDescription("9.0")]
        postgresql90 = 90
    }

    [Serializable]
    public enum POSTGRE9XDB
    {
        [EnumDescription("9.x")]
        postgre9x93 = 93,

        [EnumDescription("10.x")]
        postgre9x104 = 104,

        [EnumDescription("14.x")]
        postgres9x145 = 145
    }

    [Serializable]
    public enum DBSQLNATIVE2008
    {
        [EnumDescription("2005")]
        DBSQLNATIVE05 = 2005,

        [EnumDescription("2008")]
        DBSQLNATIVE08 = 2008,

        [EnumDescription("2012")]
        DBSQLNATIVE12 = 2012,

        [EnumDescription("2014")]
        DBSQLNATIVE14 = 2014,

        [EnumDescription("2016")]
        DBSQLNATIVE16 = 2016,

        [EnumDescription("2017")]
        DBSQLNATIVE17 = 2017,

        [EnumDescription("2019")]
        DBSQLNATIVE19 = 2019,

    }

    [Serializable]
    public enum PRDRDatabaseType
    {
        [EnumDescription("PRDatabase")]
        PRDatabase = 1,

        [EnumDescription("DRDatabase")]
        DRDatabase = 2
    }

    [Serializable]
    public enum ServiceAvailStatus
    {
        [EnumDescription("High")]
        High = 1,

        [EnumDescription("Medium")]
        Medium = 2,

        [EnumDescription("Low")]
        Low = 3
    }

    [Serializable]
    public enum AppInfraReplicationType
    {
        [EnumDescription("SelectReplication")]
        SelectReplication = 0,

        [EnumDescription("Global Mirror")]
        GlobalMirror = 1,

        [EnumDescription("DataSync")]
        DataSync = 2,

        [EnumDescription("EMCSRDF")]
        EMCSRDF = 3,

        [EnumDescription("DataGuard")]
        DataGuard = 4
    }

    [Serializable]
    /// <summary>
    /// Enumeration for Navigation for Infra List Repeater on Command Center
    /// </summary>
    /// <author> Kuntesh Thakker - 23-04-2014 </author>
    public enum Navigation
    {
        None,
        First,
        Next,
        Previous,
        Last,
        Pager
    }

    [Serializable]
    /// <summary>
    /// Enumeration for BIA 
    /// </summary>
    /// <author> Kuntesh Thakker - 23-04-2014 </author>   
    public enum RTO
    {
        Upto2Hrs = 2,
        Upto4Hrs = 4,
        Upto8Hrs = 8,
        Upto12Hrs = 12,
        Upto24Hrs = 24,
        Upto48Hrs = 48,
        Upto72Hrs = 72,
        Upto1Week = 168,
        Upto2Weeks = 336,
        Upto1Month = 720,
        MoreThan1Month = 1000
    }

    public enum BusinessFunctionRTO
    {
        Upto2Hrs = 2,
        Upto4Hrs = 4,
        Upto8Hrs = 8,
        Upto12Hrs = 12,
        Upto24Hrs = 24,
        Upto48Hrs = 48,
        Upto72Hrs = 72,
        Upto1Week = 168,
        Upto2Weeks = 336,
        Upto1Month = 720,
    }

    public enum ImpactTypeData
    {
        FinancialImpact = 1,
        OperationImpact = 2,
        CumulativeImpact = 3,
        LegalRegulatoryImpact = 4,
        OverallImpact = 5
    }

    public enum ImpactRating
    {
        Negligible = 1,
        Insignificant = 2,
        Minor = 3,
        Major = 4,
        Severe = 5
    }

    [Serializable]
    public enum ImpactSeverity
    {
        [EnumDescription("Partial Impact")]
        Partial = 1,
        [EnumDescription("Major Impact")]
        Major = 2,
        [EnumDescription("Total Impact")]
        Total = 3
    }

    [Serializable]
    public enum BusinessFunctionCriticalityLevel
    {
        [EnumDescription("Normal")]
        Normal = 1,

        [EnumDescription("High")]
        High = 2,

        [EnumDescription("Critical")]
        Critical = 3
    }

    [Serializable]
    public enum Workflow_DB2SubCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("DB2 HADR for LUW")]
        DB2HADRforLUW = 10800,

        [EnumDescription("WINDOWS")]
        Windows = 10900,

        [EnumDescription("Common")]
        Common = 11000,

    }

    [Serializable]
    public enum Workflow_MysqlNative
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("AIX/LINUX/UNIX")]
        Linux = 111,

        [EnumDescription("WINDOWS")]
        Windows = 112,

        [EnumDescription("Common")]
        Common = 113,

    }

    [Serializable]
    public enum Workflow_AIXOSActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("VIOS")]
        VIOS = 8,

        [EnumDescription("Common")]
        Common = 9,

        [EnumDescription("Replication")]
        Replication = 20,

        [EnumDescription("LPAR")]
        LPAR = 21,
    }

    [Serializable]
    public enum Workflow_VirtualizationCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("LDOM")]
        LDOM = 22221,

        [EnumDescription("ZONES")]
        ZONES = 22222,

        [EnumDescription("ILOM")]
        ILOM = 22223,
    }

    [Serializable]
    public enum Workflow_Amanzons3ActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("EC2")]
        EC2 = 1400,

        [EnumDescription("S3")]
        S3 = 1500,

        [EnumDescription("AWSCloudEndure")]
        AWSCloudEndure = 1600,

    }

    [Serializable]
    public enum Workflow_VMWareActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("vCAV")]
        vCAV = 2,

        [EnumDescription("Zerto")]
        Zerto = 3,


        [EnumDescription("RP4VM")]
        RP4VM = 4,
    }

    [Serializable]
    public enum BusinessImpactRelationType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("BFtoBF")]
        BFtoBF = 1,

        [EnumDescription("BFtoBS")]
        BFtoBS = 2,

        [EnumDescription("InfratoBF")]
        InfratoBF = 3,

        [EnumDescription("BStoBS")]
        BStoBS = 4,
    }

    public enum InfraobjectComponentType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Server")]
        Server = 1,

        [EnumDescription("Database")]
        Database = 2,

        [EnumDescription("OS")]
        OS = 3,

        [EnumDescription("Storage")]
        Storage = 4,

        [EnumDescription("Network")]
        Network = 5,

        [EnumDescription("Replication")]
        Replication = 6,

        [EnumDescription("Application")]
        Application = 7,

        [EnumDescription("DB")]
        DB = 8,

        [EnumDescription("Virtual")]
        Virtual = 9,

        [EnumDescription("InfraObject")]
        InfraObject = 10,

    }

    [Serializable]
    public enum BIImpactType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Partially Impacted")]
        Partially = 1,

        [EnumDescription("Majorly Impacted")]
        Majorly = 2,

        [EnumDescription("Totally Impacted")]
        Totally = 3


    }

    [Serializable]
    public enum OracleJobList
    {
        [EnumDescription("ApplyArchiveLogsJob")]
        ApplyArchiveLogsJob = 3,

        [EnumDescription("FastCopyOracleSyncJob")]
        FastCopyOracleSyncJob = 4,

        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 55,
    }

    [Serializable]
    public enum DataSync
    {
        [EnumDescription("ApplicationReplicationJob")]
        ApplicationReplicationJob = 5,

        [EnumDescription("MonitorApplicationJob")]
        MonitorApplicationJob = 6,

        [EnumDescription("ApplicationDeleteFilesJob")]
        ApplicationDeleteFilesJob = 56,

    }

    [Serializable]
    public enum EMCSRDFMSSQLFullDB
    {
        [EnumDescription("MSSqlEmcSrdfFullDbJob")]
        MSSqlEmcSrdfFullDbJob = 8,
        //[EnumDescription("MonitorSRDFDataLagJob")]
        //MonitorSRDFDataLagJob = 9,

    }

    [Serializable]
    public enum AppliNoRepli
    {
        [EnumDescription("MonitorsServiesJob")]
        MonitorsServiesJob = 62,

    }



    [Serializable]
    public enum EMCSRDFMysqlFullDB
    {
        [EnumDescription("MonitorSRDFDataLagJob")]
        MonitorSRDFDataLagJob = 8,
        [EnumDescription("MonitorMySqlDBJob")]
        MonitorMySqlDBJob = 9,

    }

    [Serializable]
    public enum EMCSRDF_OracleFullDB
    {
        // [EnumDescription("MonitorOracleDgJob")]
        // MonitorOracleDgJob = 383,
        // [EnumDescription("MonitorSRDFJob")]
        //MonitorSRDFJob = 14,
        [EnumDescription("MonitorSRDFDataLagJob")]
        MonitorSRDFDataLagJob = 15,
    }

    [Serializable]
    public enum SRMVMwareMonitorJob
    {
        [EnumDescription("SRMVMwareMonitorJob")]
        SRMVMwareMonitorJob = 16,
    }

    [Serializable]
    public enum SVCGlobalMirrorOracleFullDBRac
    {
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 17,
        [EnumDescription("MonitorOracleDgJob")]
        MonitorSVCGlobalMirrorJob = 18,
    }

    [Serializable]
    public enum NetAppSnapMirrorPostgresFullDB
    {
        [EnumDescription("SnapMirrorJob")]
        SnapMirrorJob = 19,
        [EnumDescription("Postgre9XJob")]
        Postgre9XJob = 20,
    }

    [Serializable]
    public enum HitachiURMSSQLFullDB
    {
        [EnumDescription("MssqlSVCFullDbJob")]
        MssqlSVCFullDbJob = 21,
        [EnumDescription("MonitorHitachiHURJob")]
        MonitorHitachiHURJob = 22,
    }

    [Serializable]
    public enum VMWareWithSVC
    {
        [EnumDescription("MonitorVmWareJob")]
        MonitorVmWareJob = 23,
        [EnumDescription("MonitorSVCGlobalMirrorJob")]
        MonitorSVCGlobalMirrorJob = 24,
    }

    [Serializable]
    public enum DB2IBMGlobalMirror
    {
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 25,
    }

    [Serializable]
    public enum SVCMSSQLFullDB
    {
        [EnumDescription("MssqlSVCFullDbJob")]
        MssqlSVCFullDbJob = 26,
        [EnumDescription("MonitorSVCGlobalMirrorJob")]
        MonitorSVCGlobalMirrorJob = 26,
    }

    [Serializable]
    public enum MSSQLDBMirroring
    {
        [EnumDescription("SqlDBMirroringJob")]
        SqlDBMirroringJob = 27,
    }

    [Serializable]
    public enum OracleFullDBSVCGlobalMirror
    {
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 28,
        [EnumDescription("MonitorSVCGlobalMirrorJob")]
        MonitorSVCGlobalMirrorJob = 29,
    }

    [Serializable]
    public enum MySqlNativeLogShipping
    {
        [EnumDescription("MySqlNativeJob")]
        MySqlNativeJob = 30,
    }

    [Serializable]
    public enum EmcsrfdOracleFullDBRac
    {
        [EnumDescription("MonitorSRDFDataLagJob")]
        MonitorSRDFDataLagJob = 31,
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 32,
    }

    [Serializable]
    public enum HitachiOracleFullDBRac
    {
        [EnumDescription("MonitorHitachiHURJob")]
        MonitorHitachiHURJob = 33,
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 34,
    }

    [Serializable]
    public enum SYBASEDataBase
    {
        [EnumDescription("SybaseMonitoringJob")]
        SybaseMonitoringJob = 35,
    }

    [Serializable]
    public enum MSSQLDoubleTakeFullDB
    {
        [EnumDescription("MssqldoubleTakeJob")]
        MssqldoubleTakeJob = 36,
    }

    [Serializable]
    public enum DB2HADR9X
    {
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 37,
    }

    [Serializable]
    public enum Enterprisedb
    {
        [EnumDescription("PostgreMonitoringJob")]
        PostgreMonitoringJob = 38,
    }

    [Serializable]
    public enum Sql2000FastCopy
    {
        [EnumDescription("MSSqlServer2000Job")]
        MSSqlServer2000Job = 39,
    }

    [Serializable]
    public enum OracleFullDbGlobalMirror
    {
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 40,
    }

    [Serializable]
    public enum NetAppSnapMirror
    {
        [EnumDescription("SnapMirrorJob")]
        SnapMirrorJob = 41,
    }

    [Serializable]
    public enum VMWareSnapmirror
    {
        [EnumDescription("MonitorVmWareJob")]
        MonitorVmWareJob = 42,
        [EnumDescription("SnapMirrorJob")]
        SnapMirrorJob = 43,
    }

    [Serializable]
    public enum MSExchangeDAG
    {
        [EnumDescription("MonitorExchangeDagJob")]
        MonitorExchangeDagJob = 44,
    }

    [Serializable]
    public enum OracleFullDB_NetAppSnapMirror
    {
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 45,
        [EnumDescription("SnapMirrorJob")]
        SnapMirrorJob = 46,
    }

    [Serializable]
    public enum MSSqlNetAppSnapMirror
    {
        [EnumDescription("MSSqlNetAppSnapMirror")]
        MSSqlNetAppSnapMirror = 47,
        [EnumDescription("SnapMirrorJob")]
        SnapMirrorJob = 48,
    }

    [Serializable]
    public enum MySqlNetAppSnapMirror
    {
        [EnumDescription("MYSqlNetAppSnapMirror")]
        MYSqlNetAppSnapMirror = 185,
        [EnumDescription("SnapMirrorJob")]
        SnapMirrorJob = 186,
    }

    [Serializable]
    public enum HITACHIUROracleFullDB
    {
        [EnumDescription("MonitorHitachiHURJob")]
        MonitorHitachiHURJob = 49,
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 50,
    }

    [Serializable]
    public enum VMWareHitachiUR
    {
        [EnumDescription("MonitorVmWareJob")]
        MonitorVmWareJob = 51,
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 52,
        [EnumDescription("MonitorHitachiHURJob")]
        MonitorHitachiHURJob = 53,
    }

    [Serializable]
    public enum MSSQLDataBaseMirror
    {
        [EnumDescription("MSSqlEmcSrdfFullDbJob")]
        MSSqlEmcSrdfFullDbJob = 51,
        [EnumDescription("MSSQLMirroringMonitoringJob")]
        MSSQLMirroringMonitoringJob = 52
    }

    [Serializable]
    public enum MySQLGlobalMirrorFullDBlist
    {

        [EnumDescription("MonitorMySqlDBJob")]
        MonitorMySqlDBJob = 101,
    }


    [Serializable]
    public enum EC2S3DataSynclist
    {

        [EnumDescription("EC2S3DataSyncJob")]
        EC2S3DataSyncJob = 102,


    }

    [Serializable]
    public enum ApplicationDoubleTakelist
    {
        [EnumDescription("ApplicationDTJob")]
        ApplicationDTJob = 103,

    }

    [Serializable]
    public enum MIMIXlist
    {
        [EnumDescription("ApplicationMimixJob")]
        ApplicationMimixJob = 104,

    }


    [Serializable]
    public enum HyperVlist
    {
        [EnumDescription("MonitorHyperVJob")]
        MonitorHyperVJob = 105,

    }

    [Serializable]
    public enum AppHitachiUrlist
    {
        [EnumDescription("MonitorHitachiHURJob")]
        MonitorHitachiHURJob = 106,
        [EnumDescription("MonitorApplicationServiceJob")]
        MonitorApplicationServiceJob = 107,

    }

    [Serializable]
    public enum GlobalMirrorMssqlFullDBlist
    {
        [EnumDescription("MssqlSVCFullDbJob")]
        MssqlSVCFullDbJob = 108,
        [EnumDescription("GlobalMirrorDataAccess")]
        GlobalMirrorDataAccess = 109,

    }

    [Serializable]
    public enum MaxDBWithDataSynclist
    {
        [EnumDescription("MonitorMAXDBJob")]
        MonitorMAXDBJob = 110,

    }

    [Serializable]
    public enum SybaseWithSRSlist
    {
        [EnumDescription("SybaseWithSRSMonitoringJob")]
        SybaseWithSRSMonitoringJob = 111,

    }

    [Serializable]
    public enum ApplicationeBDRlist
    {
        [EnumDescription("ApplicationeBDRJob")]
        ApplicationeBDRJob = 112,

    }

    [Serializable]
    public enum DRNETlist
    {
        [EnumDescription("DRNetMonitorJob")]
        DRNetMonitorJob = 113,

    }
    [Serializable]
    public enum TPCRlist
    {
        [EnumDescription("MonitorTPCRJob")]
        MonitorTPCRJob = 114,

    }

    [Serializable]
    public enum MSSQLAlwaysOnlist
    {
        [EnumDescription("MSSqlAlwaysONJob")]
        MSSqlAlwaysONJob = 115,

    }
    [Serializable]
    public enum RecoverPointlist
    {
        [EnumDescription("MonitorRecoverPointJob")]
        MonitorRecoverPointJob = 116,

    }
    [Serializable]
    public enum RecoverPointOracleFULLDBlist
    {
        [EnumDescription("MonitorRecoverPointJob")]
        MonitorRecoverPointJob = 117,
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 118,


    }


    [Serializable]
    public enum RecoverPointMSSQLFULLDBlist
    {
        [EnumDescription("MonitorRecoverPointJob")]
        MonitorRecoverPointJob = 119,
        [EnumDescription("MSSqlEmcSrdfFullDbJob")]
        MSSqlEmcSrdfFullDbJob = 120,


    }

    [Serializable]
    public enum HP3PARMSSQLFULLDBlist
    {
        [EnumDescription("MssqlSVCFullDbJob")]
        MssqlSVCFullDbJob = 121,
        [EnumDescription("MonitorHP3PARJob")]
        MonitorHP3PARJob = 122,


    }

    [Serializable]
    public enum HP3ParwithApplicationlist
    {
        [EnumDescription("MonitorHP3PARJob")]
        MonitorHP3PARJob = 123,

    }

    [Serializable]
    public enum HP3ParwithPostgressMSSQLlist
    {
        [EnumDescription("MonitorHP3PARJob")]
        MonitorHP3PARJob = 124,
        [EnumDescription("Postgre9XJob")]
        Postgre9XJob = 125,

    }

    [Serializable]
    public enum EMCSRDFSTARORACLEFULLDBlist
    {
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 126,
        [EnumDescription("MonitorSRDFDataLagJob")]
        MonitorSRDFDataLagJob = 127,
        [EnumDescription("MonitorVeritasClusterJob")]
        MonitorVeritasClusterJob = 128,

    }


    [Serializable]
    public enum EMCSRDFSTARAPPLICATIONlist
    {
        //[EnumDescription("MonitorSRDFDataLagJob")]
        //MonitorSRDFDataLagJob = 129,
        [EnumDescription("MonitorVeritasClusterJob")]
        MonitorVeritasClusterJob = 130,


    }

    [Serializable]
    public enum EMCSRDFAPPLICATIONlist
    {
        //[EnumDescription("MonitorSRDFDataLagJob")]
        //MonitorSRDFDataLagJob = 129,
        [EnumDescription("MonitorVeritasClusterJob")]
        MonitorVeritasClusterJob = 195,


    }

    [Serializable]
    public enum EMCSRDFMSQLFULLDBSTARlist
    {
        [EnumDescription("MSSqlEmcSrdfFullDbJob")]
        MSSqlEmcSrdfFullDbJob = 131,
        [EnumDescription("MonitorSRDFDataLagJob")]
        MonitorSRDFDataLagJob = 132,
        [EnumDescription("MonitorVeritasClusterJob")]
        MonitorVeritasClusterJob = 133,


    }


    [Serializable]
    public enum HP3ParwithESXIlist
    {
        [EnumDescription("MonitorVmWareJob")]
        MonitorVmWareJob = 134,
        [EnumDescription("MonitorHP3PARJob")]
        MonitorHP3PARJob = 135,
    }

    [Serializable]
    public enum ZFSOracleFULLDBlist
    {
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 136,
        [EnumDescription("MonitorZFSJob")]
        MonitorZFSJob = 137,

        [EnumDescription("MonitorHACMPCluster")]
        MonitorHACMPCluster = 138,
        [EnumDescription("MonitorGlobalCluster")]
        MonitorGlobalCluster = 139,
    }


    [Serializable]
    public enum ZFSApplicationlist
    {
        [EnumDescription("MonitorZFSJob")]
        MonitorZFSJob = 140,
        [EnumDescription("MonitorHACMPCluster")]
        MonitorHACMPCluster = 141,

        [EnumDescription("MonitorGlobalCluster")]
        MonitorGlobalCluster = 142,

    }

    [Serializable]
    public enum ZFSWithDB2list
    {
        [EnumDescription("MonitorZFSJob")]
        MonitorZFSJob = 143,
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 144,

        [EnumDescription("MonitorHACMPCluster")]
        MonitorHACMPCluster = 145,
        [EnumDescription("MonitorGlobalCluster")]
        MonitorGlobalCluster = 146,

    }


    [Serializable]
    public enum ZFSMaxFULLDBlist
    {
        [EnumDescription("MonitorMAXDBJob")]
        MonitorMAXDBJob = 147,
        [EnumDescription("MonitorZFSJob")]
        MonitorZFSJob = 148,

        [EnumDescription("MonitorHACMPCluster")]
        MonitorHACMPCluster = 149,
        [EnumDescription("MonitorGlobalCluster")]
        MonitorGlobalCluster = 150,

    }

    [Serializable]
    public enum EMCISilonlist
    {
        [EnumDescription("EMCIsilonReplicationMonitorJob")]
        EMCIsilonReplicationMonitorJob = 151,
    }

    [Serializable]
    public enum EmcMirrorViewOracleFullDBlist
    {
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 152,
        [EnumDescription("EMCMirrorViewMonitorJob")]
        EMCMirrorViewMonitorJob = 153,
    }


    [Serializable]
    public enum EmcMirrorViewSybaseFullDBlist
    {
        [EnumDescription("EMCMirrorViewMonitorJob")]
        EMCMirrorViewMonitorJob = 154,
        [EnumDescription("SybaseMonitoringJob")]
        SybaseMonitoringJob = 155,
    }

    [Serializable]
    public enum EmcMirrorViewApplist
    {
        [EnumDescription("EMCMirrorViewMonitorJob")]
        EMCMirrorViewMonitorJob = 156,

    }

    [Serializable]
    public enum RoboCopylist
    {
        [EnumDescription("RoboCopyReplicationJob")]
        RoboCopyReplicationJob = 157,

    }

    [Serializable]
    public enum DB2FullDBSVClist
    {
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 158,
        [EnumDescription("MonitorSVCGlobalMirrorJob")]
        MonitorSVCGlobalMirrorJob = 159,


    }
    [Serializable]
    public enum PostgressFullDBSVClist
    {
        [EnumDescription("Postgre9XJob")]
        Postgre9XJob = 160,
        [EnumDescription("MonitorSVCGlobalMirrorJob")]
        MonitorSVCGlobalMirrorJob = 161,


    }

    [Serializable]
    public enum HP3ParwithMongoFullDBlist
    {
        [EnumDescription("MonitorHP3PARJob")]
        MonitorHP3PARJob = 162,
        [EnumDescription("MonitorMongoDBJob")]
        MonitorMongoDBJob = 163,


    }
    [Serializable]
    public enum EMCSRDFDB2FullDBlist
    {
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 164,
        [EnumDescription("MSSqlEmcSrdfFullDbJob")]
        MSSqlEmcSrdfFullDbJob = 165,


    }


    [Serializable]
    public enum DB2FullDBEMCRecoveryPointlist
    {
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 166,
        [EnumDescription("MonitorRecoverPointJob")]
        MonitorRecoverPointJob = 167,


    }
    [Serializable]
    public enum EMCSTARDB2FullDBlist
    {
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 168,
        [EnumDescription("MonitorSRDFDataLagJob")]
        MonitorSRDFDataLagJob = 169,
        [EnumDescription("MonitorVeritasClusterJob")]
        MonitorVeritasClusterJob = 170,


    }

    [Serializable]
    public enum EMCSTARMYSQLFullDBlist
    {
        [EnumDescription("MonitorMySqlDBJob")]
        MonitorMySqlDBJob = 171,
        [EnumDescription("MonitorSRDFDataLagJob")]
        MonitorSRDFDataLagJob = 172,
        [EnumDescription("MonitorVeritasClusterJob")]
        MonitorVeritasClusterJob = 173,


    }


    [Serializable]
    public enum HitachiUrDB2FullDBlist
    {
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 174,
        [EnumDescription("MonitorHitachiHURJob")]
        MonitorHitachiHURJob = 175,
    }


    [Serializable]
    public enum HitachiUrMySqlFullDBlist
    {
        [EnumDescription("MonitorMySqlDBJob")]
        MonitorMySqlDBJob = 176,
        [EnumDescription("MonitorHitachiHURJob")]
        MonitorHitachiHURJob = 177,
    }
    [Serializable]
    public enum SAPHANADBReplicationlist
    {
        [EnumDescription("MonitorSAPHana")]
        MonitorSAPHana = 178,

    }

    [Serializable]
    public enum VirtualeBDRlist
    {
        [EnumDescription("VirtualeBDRJob")]
        VirtualeBDRJob = 179,

    }

    [Serializable]
    public enum GoldenGateReplilist
    {
        [EnumDescription("MonitorGoldenGateJob")]
        MonitorGoldenGateJob = 180,

    }

    [Serializable]
    public enum RSynclist
    {
        [EnumDescription("MonitorRSyncJob")]
        MonitorRSyncJob = 181,

    }

    [Serializable]
    public enum HP3PARORACLEFULLDBlist
    {
        [EnumDescription("MonitorHP3PARJob")]
        MonitorHP3PARJob = 182,
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 183,


    }
    //[Serializable]
    //public enum NetAppSnapMirrorPostgresFullDBlist
    // {
    //     [EnumDescription("SnapMirrorJob")]
    //     SnapMirrorJob = 110,
    //     [EnumDescription("Postgre9XJob")]
    //     Postgre9XJob = 111,

    // }



    [Serializable]
    public enum MonitorBusinessServiceAvailbility
    {
        [EnumDescription("MonitorBusinessServiceAvailbility")]
        MonitorBusinessServiceAvailbility = 16,
    }

    [Serializable]
    public enum EC2S3DataSyncReplicationJob
    {
        [EnumDescription("EC2S3DataSyncReplicationJob")]
        EC2S3DataSyncReplicationJob = 13,
        [EnumDescription("EC2S3DataSyncJob")]
        EC2S3DataSyncJob = 14,

    }

    [Serializable]
    public enum ApplicationDoubleTake
    {
        [EnumDescription("ApplicationDTJob")]
        ApplicationDTJob = 56,
    }

    [Serializable]
    public enum MonintorExchangeJob
    {
        [EnumDescription("MonintorExchangeJob")]
        MonintorExchangeJob = 11,
    }

    [Serializable]
    public enum MSSqlNativeLogjob
    {
        [EnumDescription("MSSqlNativeLogjob")]
        MSSqlNativeLogjob = 12,
    }

    [Serializable]
    public enum MonitorHADRJob
    {
        [EnumDescription("MonitorHADRJob")]
        MonitorHADRJob = 13,
    }

    [Serializable]
    public enum Postgre9XJob
    {
        [EnumDescription("Postgre9XJob")]
        Postgre9XJob = 10,
    }

    [Serializable]
    public enum MSSqlNative2008
    {
        [EnumDescription("MSSqlNative2008Job")]
        MSSqlNative2008Job = 7,

    }

    [Serializable]
    public enum MySqlNativeJob
    {
        [EnumDescription("MySqlNativeJob")]
        MySqlNativeJob = 51,

    }

    [Serializable]
    public enum JobList1
    {
        [EnumDescription("MonitorOracleDgJob")]
        MonitorOracleDgJob = 0,

        [EnumDescription("ArchivedLogDeleteJob")]
        ArchivedLogDeleteJob = 1,

        [EnumDescription("MonitorReplicateLogVolumeJob")]
        MonitorReplicateLogVolumeJob = 2,
    }

    [Serializable]
    public enum AzureSite
    {
        [EnumDescription("MonitorRecoveryAzureSiteJob")]
        MonitorRecoveryAzureSiteJob = 182,

    }

    [Serializable]
    public enum AccessManagerType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Dashboard")]
        Dashboard = 1,

        [EnumDescription("Configuration")]
        Configuration = 2,

        [EnumDescription("View")]
        View = 3,

        [EnumDescription("Manage")]
        Manage = 4,

        [EnumDescription("Alerts")]
        Alerts = 5,

        [EnumDescription("ITOrchestration")]
        ITOrchestration = 6,

        [EnumDescription("Reports")]
        Reports = 7,

        [EnumDescription("Admin")]
        Admin = 8
    }

    [Serializable]
    public enum SubDashboard
    {
        [EnumDescription("Monitor")]
        Monitor = 1,

        [EnumDescription("Management")]
        Management = 2,
    }

    [Serializable]
    public enum SubConfiguration
    {
        [EnumDescription("Add")]
        Add = 3,
    }

    [Serializable]
    public enum SubView
    {
        [EnumDescription("Edit")]
        Edit = 4,

        [EnumDescription("Delete")]
        Delete = 5,

    }

    [Serializable]
    public enum SubManage
    {
        [EnumDescription("Add")]
        Add = 3,

        [EnumDescription("Edit")]
        Edit = 4,

        [EnumDescription("Delete")]
        Delete = 5,

    }

    [Serializable]
    public enum SubAlerts
    {
        [EnumDescription("View")]
        Add = 3,
    }

    [Serializable]
    public enum SubITOrchestration
    {
        [EnumDescription("Add")]
        Add = 3,

        [EnumDescription("Edit")]
        Edit = 4,

        [EnumDescription("Delete")]
        Delete = 5,
        [EnumDescription("Execute")]
        Execute = 6,

        [EnumDescription("View")]
        View = 7,

    }

    [Serializable]
    public enum SubReports
    {
        [EnumDescription("View")]
        Add = 3,
    }

    [Serializable]
    public enum AccessSubMenuType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Monitor")]
        Monitor = 1,

        [EnumDescription("Management")]
        Management = 2,

        [EnumDescription("Add")]
        Add = 3,

        [EnumDescription("Edit")]
        Edit = 4,

        [EnumDescription("Delete")]
        Delete = 5,

        [EnumDescription("Execute")]
        Execute = 6,

        [EnumDescription("View")]
        View = 7,

    }


    [Serializable]
    public enum GlobalMirrorFiletype
    {
        [EnumDescription("Undefined")]
        Undefined = 0,
        [EnumDescription("Redo")]
        Redo = 1,
        [EnumDescription("Archive")]
        Archive = 2,
        [EnumDescription("Datafile")]
        Datafile = 3,
        [EnumDescription("Controlfile")]
        Controlfile = 4,
        [EnumDescription("Application")]
        Application = 5,
        [EnumDescription("Other")]
        Other = 6
    }

    [Serializable]
    public enum SoultionType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("Storage")]
        Storage = 1,
        [EnumDescription("Database")]
        Database = 2,
        [EnumDescription("OS")]
        OS = 3,
        [EnumDescription("VMWareAction")]
        VMWareAction = 4,
        [EnumDescription("Network")]
        Network = 5,
        [EnumDescription("WorkFlow")]
        WorkFlow = 6,
        [EnumDescription("Hypervisor")]
        Hypervisor = 7,
        [EnumDescription("Web")]
        Web = 8,
        [EnumDescription("FileSystem")]
        FileSystem = 9,
        [EnumDescription("Virtualization")]
        Virtualization = 10,
        [EnumDescription("MsExchange")]
        MsExchange = 11,
        [EnumDescription("Cloud")]
        Cloud = 12,
        [EnumDescription("Server Management")]
        ServerManagement = 13,
        [EnumDescription("Replication")]
        Replication = 14,

        [EnumDescription("Total")]
        Total = 10000,
    }

    [Serializable]
    public enum Workflow_MainFrameSubCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("AS400")]
        AS400 = 3300,

    }

    [Serializable]
    public enum SubsAuthnTypes
    {
        [EnumDescription("SudoSu")]
        SudoSu = 1,
        [EnumDescription("Su")]
        Su = 2,
        [EnumDescription("ASU")]
        ASU = 3,
        [EnumDescription("Sudo")]
        Sudo = 4,
        [EnumDescription("Privrun")]
        Privrun = 5,
        [EnumDescription("Other")]
        Other = 6,
    }

    [Serializable]
    public enum EventType
    {
        [EnumDescription("Migration Not Yet Started")]
        Undefined = 0,
        [EnumDescription("Migration Initiated")]
        MIGRATION_STARTED = 1,
        [EnumDescription("Migration Resumed")]
        RESUMED_MIGRATION = 2,
        [EnumDescription("Replication Started")]
        REPLICATION_STARTED = 3,
        [EnumDescription("Max attempts to connect appliance")]
        MAX_TRIES_OVER = 4,                      // = "Max Attempts to connect appliance is reached "
        [EnumDescription("Migration Failed")]
        MIGRATION_FAILED = 5,
        [EnumDescription("Migration In Progress")]
        MIGRATION_IN_PROGRESS = 6,
        [EnumDescription("Migration Completed")]
        MIGRATION_COMPLETED = 7,
        [EnumDescription("Migration Aborted")]
        MIGRATION_ABORTED = 8,

        //8-12
        [EnumDescription("Launch Started")]
        LAUNCH_STARTED = 9,
        [EnumDescription("Launch Failed")]
        LAUNCH_FAILED = 10,
        [EnumDescription("Launch In Progress")]
        LAUNCH_IN_PROGRESS = 11,
        [EnumDescription("Launch Completed")]
        LAUNCH_COMPLETED = 12,

        //13-16
        [EnumDescription("Simulation Started")]
        SIMULATION_STARTED = 13,
        [EnumDescription("Simulation Failed")]
        SIMULATION_FAILED = 14,
        [EnumDescription("Simulation In Progress")]
        SIMULATION_IN_PROGRESS = 15,
        [EnumDescription("Simulation Completed")]
        SIMULATION_COMPLETED = 16,

        //17-19
        [EnumDescription("Replication Failed")]
        REPLICATION_FAILED = 17,
        [EnumDescription("Replication is in Progress")]
        REPLICATION_IN_PROGRESS = 18,
        [EnumDescription("Replication Completed")]
        REPLICATION_COMPLETED = 19,

        //20-21
        [EnumDescription("Final Migration Completed")]
        FINAL_MIGRATION_COMPLETED = 20,
        [EnumDescription("Waiting for Replication")]
        WAITING_FOR_REPLICATION = 21,

        //22-24
        [EnumDescription("Target Machine Can Not Be Created")]
        CANT_CREATE_MACHINE = 22,
        [EnumDescription("Target Machine is Already Exists")]
        MACHINE_ALREADY_CREATED = 23, // = "Machine is already created in the target "
        [EnumDescription("Target Machine is Created Successfully")]
        MACHINE_CREATED_SUCCESSFULLY = 24,

        //25-27
        [EnumDescription("Disk Can Not Be Added At Target")]
        CANT_ADD_DISK = 25,
        [EnumDescription("Disk Is Already Added At Target")]
        DISK_ALREADY_ADDED = 26,//= "Disk is already added in the target "
        [EnumDescription("Disk Added At Target")]
        DISK_ADDED_SUCCESSFULLY = 27,

        //28-31
        [EnumDescription("Target Machine is Powered On Successfully")]
        MACHINE_POWERED_ON_SUCCESSFULLY = 28,
        [EnumDescription("Target Machine can not be powered on")]
        CANT_POWER_ON_MACHINE = 29,
        [EnumDescription("Target Machine Clone Created Successfully")]
        MACHINE_CLONED_SUCCESSFULLY = 30,
        [EnumDescription("Target Machine Clone Can not be Created")]
        CANT_CLONE_MAHCINE = 31,



        //32-38

        [EnumDescription("Target Connection Failed")]
        CANT_CONNECT_TARGET = 32,
        [EnumDescription("Migration Cancelled")]
        CANCELED_MIGRATION = 33,
        [EnumDescription("Aborted Action")]
        ABORTED_ACTION = 34,
        [EnumDescription("In Progress")]
        IN_PROGRESS = 35,
        [EnumDescription("Migration Paused")]
        PAUSED_MIGRATION = 36,
        [EnumDescription("Re-Trying to Connect")]
        RETRYING_TO_CONNECT = 37,


        [EnumDescription("Yet to Start Migration/Replication")]
        YET_TO_START = 38,
        [EnumDescription("Migration Accepted")]
        MIGRATION_ACCEPTED = 39,
        [EnumDescription("Target Machine Provisioned")]
        MACHINE_STARTED = 40,
        [EnumDescription("Disk Migration Started")]
        START_DISK = 41,
        [EnumDescription("Disk Migration Ended")]
        END_DISK = 42,
        [EnumDescription("Disk Migration Ended Successfully")]
        END_DISK_SUCCESSFULLY = 43,
        [EnumDescription("Machine Ended")]
        END_MACHINE = 44,
        [EnumDescription("Machine Ended Successfully")]
        END_MACHINE_SUCCESSFULLY = 45,
        [EnumDescription("Data Completed")]
        DATA_COMPLETED = 46,
        [EnumDescription("Data Completed Successfully")]
        DATA_COMPLETED_SUCCESSFULLY = 47,
        [EnumDescription("Migration Ended")]
        END_MIGRATION = 48,




        [EnumDescription("Migration Completed Successfully")]
        END_MIGRATION_SUCCESSFULLY = 49,
        [EnumDescription("Migration In Progress")]
        START_FULL_MIGRATION = 50,
        [EnumDescription("Migration Started Successfully")]
        START_FULL_MIGRATION_SUCCESSFULLY = 51,
        [EnumDescription("Migration Failed")]
        FULL_MIGRATION_FAILED = 52,
        [EnumDescription("Replication Completed Successfully")]
        REPLICATION_COMPLETED_SUCCESSFULLY = 53,
        [EnumDescription("Replication Resumed")]
        RESUMED_REPLICATION = 54,
        [EnumDescription("Replication Paused")]
        PAUSE_REPLICATION = 55,
        [EnumDescription("Replication Cancelled")]
        CANCEL_REPLICATION = 56,
        [EnumDescription("Replication Is Scheduled For Next Cycle")]
        COUNTDOWN = 57,
        [EnumDescription("Replication is Scheduled")]
        REPLICATION_SCHEDULED = 58,
        [EnumDescription("License Expired")]
        LICENCE_EXPIRED = 59,
        [EnumDescription("Snapshot Limit is Exceeded")]
        SNAPSHOT_LIMITE_EXCEED = 65,


        [EnumDescription("Migration is Auto Paused")]
        AUTO_PAUSED_MIGRATION = 66,
        [EnumDescription("Migration is Auto Resumed")]
        AUTO_RESUMED_MIGRATION = 67,
        [EnumDescription("Replication is Auto Paused")]
        AUTO_PAUSED_REPLICATION = 68,
        [EnumDescription("Replication is Auto Resumed")]
        AUTO_RESUMED_REPLICATION = 69,
        [EnumDescription("Discovery")]
        DISCOVERY = 70,
        [EnumDescription("Discovery Failed")]
        DISCOVERY_FAILED = 71,
        [EnumDescription("Discovery Completed Successfully")]
        DISCOVERY_COMPLETED_SUCCESSFULLY = 72,

        [EnumDescription("Taking Target Snapshot Started")]
        TARGET_SNAPSHOT_STARTED = 73,
        [EnumDescription("Target Snapshot Taken Successfully")]
        TARGET_SNAPSHOT_COMPLETED = 74,
        [EnumDescription("Taking Target Snapshot Failed")]
        TARGET_SNAPSHOT_FAILED = 75,
        [EnumDescription("Deleting Target Snapshot Stated")]
        DELETE_TARGET_SNAPSHOT_STARTED = 76,
        [EnumDescription("Target Snapshot Deleted Successfully")]
        DELETE_TARGET_SNAPSHOT_COMPLETED = 77,
        [EnumDescription("Deleting Target Snapshot Failed")]
        DELETE_TARGET_SNAPSHOT_FAILED = 78,

        [EnumDescription("Delete Snapshot Failed")]
        DELETE_SNAPSHOT_FAILED = 79,

        [EnumDescription("HyperV Server Connected Successfully")]
        HYPERVSERVERCONNECT_SUCCESSFULLY = 80,

        [EnumDescription("Connecting to HyperV Server Failed")]
        HYPERVSERVERCONNECT_FAILED = 81,

        [EnumDescription("HyperV Server")]
        HYPERV_SERVER_CONNECT = 82,

        [EnumDescription("Manual Replication Started")]
        MANUAL_REPLICATION_STARTED = 83,

        [EnumDescription("Manual Replication In Progress")]
        MANUAL_REPLICATION_IN_PROGRESS = 84,

        [EnumDescription("Manual Replication Failed")]
        MANUAL_REPLICATION_FAILED = 85,

        [EnumDescription("Manual Replication Completed Successfully")]
        MANUAL_REPLICATION_COMPLETED = 86,

        [EnumDescription("Migration Retry action executed successfully")]
        RETRY_MIGRATION_COMPLETED = 87,

        [EnumDescription("Replication Retry action executed successfully")]
        RETRY_REPLICATION_COMPLETED = 88,

        [EnumDescription("Replication Stopped Successfully")]
        REPLICATION_STOP = 89,

        [EnumDescription("APPLICATION REPLICATION ACCEPTED")]
        APPLICATION_REPLICATION_ACCEPTED = 90,

        [EnumDescription("Replication Aborted")]
        REPLICATION_ABORTED = 91,


        [EnumDescription("Replicating pending cycle")]
        REPLICATING_CYCLE = 92,


        [EnumDescription("Preparing For Reverse Replication")]
        PREPARING_FOR_REVERSE_REPLICATION = 93,

        [EnumDescription("Physical Server")]
        PHYSICAL_SERVER_CONNECT = 94,

        [EnumDescription("Connecting to Physical Server Failed")]
        PHYSICALSERVERCONNECT_FAILED = 95,

        [EnumDescription("Failback Initiated Successfully")]
        Failback_Initiated = 107,

        [EnumDescription("Failback Started Successfully")]
        Failback_Started = 108,

        [EnumDescription("Failback In Progress")]
        Failback_InProgress = 109,

        [EnumDescription("Failback Ended Successfully")]
        Failback_Ended = 110,


        //120-125 KVM AGENT
        [EnumDescription("Target Agent Running Successfully")]
        Agent_Running = 120,

        [EnumDescription("Target Agent Not Running")]
        Agent_NotRunning = 121,

        [EnumDescription("Start Target Agent")]
        Agent_Start = 122,

        [EnumDescription("Stop Target Agent ")]
        Agent_Stop = 123,

        [EnumDescription("Target Agent Status")]
        Agent_Status = 124,

        [EnumDescription("Run on Demand")]
        RunOn_Demand = 125,

        [EnumDescription("Run as Service")]
        RunAs_Service = 126,

        [EnumDescription("Command Executed")]
        Agent_CmdExecuted = 127,

        [EnumDescription("KvmDiscovery")]
        KVMDISCOVERY = 128,

        [EnumDescription("Checking Server Connection for OpenStack")]
        OpenStackCheckServerConnection = 129,

        [EnumDescription("AzureDiscovery")]
        Azure_Discovery = 130,

        [EnumDescription("AzureDiscovery Failed")]
        AzureServerConnect_Failed = 131,


        //database to database 
        [EnumDescription("File created successfully")]
        FILE_CREATED_SUCCESSFULLY = 132,

        [EnumDescription("Database replication accepted")]
        DATABASE_REPLICATION_ACCEPTED = 133,

        [EnumDescription("File cannot create")]
        FILE_CANNOT_CREATE = 134,

        [EnumDescription("DB already created")]
        DB_ALREADY_CREATED = 135,

        [EnumDescription("Database launch instance")]
        DATABASE_LAUNCH_INSTANCE = 136,

        [EnumDescription("Database launch instance Failed")]
        DATABASE_LAUNCH_INSTANCE_FAILED = 137,

        [EnumDescription("Database Target File Path")]
        DATABASE_FILEPATH_EXIST = 138,

        [EnumDescription("Database Tcp connection Failed")]
        DATABASE_TCPCONNECTION_FAILED = 139,

        [EnumDescription("Database File path not created")]
        DATABASE_FILEPATH_FAILED = 140,

        [EnumDescription("Database File path Exist")]
        DATABASE_FILEPATH_CREATE = 141,

        [EnumDescription("Database launch instance created ")]
        DATABASE_LAUNCH_INSTANCE_CREATED = 142,

        [EnumDescription("Database Target File Already Exist")]
        DATABASE_TARGETFILE_EXIST = 143,


        [EnumDescription("Database Target File Not Exist")]
        DATABASE_TARGETFILE_NOTEXIST = 144,

        [EnumDescription("Database Server connected Successfully")]
        DATABASE_SERVER_CONNECTED = 145,

        [EnumDescription("Database Server connection Failed ")]
        DATABASE_SERVER_NOTCONNECTED = 146,

        [EnumDescription("Database Target File Path Invalid")]
        DATABASE_INVALID_TARGETFILEPATH = 147,


        [EnumDescription("Filter Driver Service is in stop state")]
        DATABASE_FILTERDRIVER_NOTCONNECTED = 149,

        [EnumDescription("Filter Driver Service is in Running state")]
        DATABASE_FILTERDRIVER_CONNECTED = 150,

        [EnumDescription("Check Filter Driver Service Connection")]
        DATABASE_CHKFILTERDRIVER_CONNECTION = 151,

        [EnumDescription("PhysicalMigration Started")]
        PhysicalMigration_Started = 152,

        [EnumDescription("Physical Linux Disk Details")]
        LINUX_DISK_DETAILS = 153,

        [EnumDescription("Check Source Service Connection")]
        CHKSOURCESERVICE_CONNECTION = 154,

        [EnumDescription("Source Service is in Running state")]
        SOURCESERVICE_CONNECTED = 155,

        [EnumDescription("Source Service is in stop state")]
        SOURCESERVICE_NOTCONNECTED = 156,

        [EnumDescription("Cluster Hostname")]
        CLUSTER_HOSTNAME = 157,

        [EnumDescription("Started taking snapshot")]
        AZURE_SNAPSHOT_STARTED = 158,

        [EnumDescription("Completed taking sanpshot")]
        AZURE_SNAPSHOT_COMPLETED = 159,

        [EnumDescription("Migration Initiated")]
        COPYING_BLOB_STARTED = 160,
        //COPYING_BLOB_COMPLETED = 161,

        [EnumDescription("Deleting snapshot")]
        DELETING_SNAPSHOT_STARTED = 161,

        [EnumDescription("Snapshot deleted")]
        DELETING_SNAPSHOT_COMPLETED = 162,

        [EnumDescription("Source snapshot creation started")]
        SNAPSHOT_CREATION_STARTED = 163,

        [EnumDescription("Source snapshot created successfully")]

        SNAPSHOT_CREATED_SUCCESSFULLY = 164,

        [EnumDescription("Source snapshot deletion started")]
        SNAPSHOT_DELETION_STARTED = 165,

        [EnumDescription("Source snapshot deleted successfully")]
        SNAPSHOT_DELETED_SUCCESSFULLY = 166,

        [EnumDescription("Target snapshot creation started")]
        TARGET_SNAPSHOT_CREATION_STARTED = 167,

        [EnumDescription("Target snapshot created successfully")]
        TARGET_SNAPSHOT_CREATED_SUCCESSFULLY = 168,

        [EnumDescription("Target snapshot deletion started")]
        TARGET_SNAPSHOT_DELETION_STARTED = 169,

        [EnumDescription("Target snapshot deleted successfully")]
        TARGET_SNAPSHOT_DELETED_SUCCESSFULLY = 170,

        [EnumDescription("Target VM deleted")]
        TARGET_VM_DELETED = 171,


    }
    public enum UIStatus
    {
        //Migration
        [EnumDescription("MIGRATION NOT YET STARTED")]
        Undefined = 0,

        [EnumDescription("START MIGRATION")]
        StartMigration = 1,

        [EnumDescription("MIGRATION IN PROGRESS")]
        MigrationInProgress = 2,

        [EnumDescription("Migration stopped successfully")]
        StopMigration = 3,

        [EnumDescription("PAUSE MIGRATION")]
        PauseMigration = 4,

        [EnumDescription("RESUME MIGRATION")]
        ResumeMigration = 5,

        [EnumDescription("CANCEL MIGRATION")]
        CancelMigration = 6,

        [EnumDescription("MIGRATION COMPLETED")]
        MigrationCompleted = 7,

        [EnumDescription("MIGRATION FAILED")]
        MigrationFailed = 8,


        //Replication
        [EnumDescription("START REPLICATION")]
        StartReplication = 9,

        [EnumDescription("REPLICATION IN PROGRESS")]
        ReplicationInProgress = 10,

        [EnumDescription("Replication stopped successfully")]
        StopReplication = 11,

        [EnumDescription("PAUSE REPLICATION")]
        PauseReplication = 12,

        [EnumDescription("RESUME REPLICATION")]
        ResumeReplication = 13,

        [EnumDescription("CANCEL REPLICATION")]
        CancelReplication = 14,

        [EnumDescription("REPLICATION COMPLETED")]
        ReplicationCompleted = 15,

        [EnumDescription("REPLICATION FAILED")]
        ReplicationFailed = 16,

        [EnumDescription("OPRATION SUCCESSFULLY")]
        Opration_Successfully = 17,

        [EnumDescription("REPLICATION RESCHEDULE JOB")]
        Replication_Reschedule_Job = 18,

        [EnumDescription("SUCCESSFULLY START MIGRATION")]
        successfullyStartMigration = 19,

        [EnumDescription("SUCCESSFULLY MIGRATION IN PROGRESS")]
        successfullyMigrationInProgress = 20,

        [EnumDescription("Migration Stopped Successfully")]
        successfullyStopMigration = 21,

        [EnumDescription("SUCCESSFULLY PAUSE MIGRATION")]
        successfullyPauseMigration = 22,

        [EnumDescription("SUCCESSFULLY RESUME MIGRATION")]
        successfullyResumeMigration = 23,

        [EnumDescription("SUCCESSFULLY CANCEL MIGRATION")]
        successfullyCancelMigration = 24,

        [EnumDescription("SUCCESSFULLY MIGRATION COMPLETED")]
        successfullyMigrationCompleted = 25,

        [EnumDescription("SUCCESSFULLY  MIGRATION FAILED")]
        successfullyMigrationFailed = 26,

        [EnumDescription("SUCCESSFULLY START REPLICATION")]
        successfullyStartReplication = 27,

        [EnumDescription("SUCCESSFULLY REPLICATION IN PROGRESS")]
        successfullyReplicationInProgress = 28,

        [EnumDescription("Replication Stopped Successfully")]
        successfullyStopReplication = 29,

        [EnumDescription("SUCCESSFULLY PAUSE REPLICATION")]
        successfullyPauseReplication = 30,

        [EnumDescription("SUCCESSFULLY RESUME REPLICATION")]
        successfullyResumeReplication = 31,

        [EnumDescription("SUCCESSFULLY CANCEL REPLICATION")]
        successfullyCancelReplication = 32,

        [EnumDescription("SUCCESSFULLY REPLICATION COMPLETED")]
        successfullyReplicationCompleted = 33,

        [EnumDescription("SUCCESSFULLY REPLICATION FAILED")]
        successfullyReplicationFailed = 34,

        [EnumDescription("SUCCESSFULLY OPRATION SUCCESSFULLY")]
        successfullyOpration_Successfully = 35,

        [EnumDescription("SUCCESSFULLY REPLICATION RESCHEDULE JOB")]
        successfullyReplication_Reschedule_Job = 36,

        [EnumDescription("YET TO START")]
        Yet_to_start = 37,

        [EnumDescription("AUTO PAUSE MIGRATION")]
        AutoPauseMigration = 38,

        [EnumDescription("AUTO RESUME MIGRATION")]
        AutoResumeMigration = 39,

        [EnumDescription("AUTO PAUSE REPLICATION")]
        AutoPausReplication = 40,

        [EnumDescription("AUTO RESUME REPLICATION")]
        AutoResumeReplication = 41,

        //Manual Replication

        [EnumDescription("MANUAL REPLICATION STARTED")]
        ManualReplicationStarted = 42,

        [EnumDescription("MANUAL REPLICATION IN PROGRESS ")]
        ManualReplicationInProgress = 43,

        [EnumDescription("MANUAL REPLICATION FAILED")]
        ManualReplicationFailed = 44,

        [EnumDescription("MANUAL REPLICATION COMPLETED")]
        ManualReplicationCompleted = 45,

        [EnumDescription("Failback Completed Successfully")]
        FailBackCompletedSucessfully = 46,

        [EnumDescription("Instance Launched Successfully")]
        LaunchInstanceCompleted = 47,

        [EnumDescription("Instance Launched Failed")]
        LaunchInstanceFailed = 48,

        [EnumDescription("VM Migration Pending")]
        VMMigrationPending = 49,


        //CP Events

        [EnumDescription("PAUSE MIGRATION")]
        CPPauseMigration = 50,

        [EnumDescription("SUCESSFULLY PAUSE MIGRATION")]
        CPSucessfullyPauseMigration = 51,

        [EnumDescription("RESUME MIGRATION")]
        CPResumeMigration = 52,

        [EnumDescription("SUCESSFULLY RESUME MIGRATION")]
        CPSucessfullyResumeMigration = 53,

        [EnumDescription("STOP MIGRATION")]
        CPStopMigration = 54,

        [EnumDescription("SUCESSFULLY STOP MIGRATION")]
        CPSucessfullyStopMigration = 55,

        [EnumDescription("PAUSE REPLICATION")]
        CPPauseReplication = 56,

        [EnumDescription("SUCESSFULLY PAUSE REPLICATION")]
        CPSucessfullyPauseReplication = 57,

        [EnumDescription("RESUME REPLICATION")]
        CPResumeReplication = 58,

        [EnumDescription("SUCESSFULLY RESUME REPLICATION")]
        CPSucessfullyResumeReplication = 59,

        [EnumDescription("STOP REPLICATION")]
        CPStopReplication = 60,

        [EnumDescription("SUCESSFULLLY STOP REPLICATION")]
        CPSucessfullyStopReplication = 61,

        [EnumDescription("START MIGRATION")]
        CPStartMigration = 62,

        [EnumDescription("SUCESSFULLLY START MIGRATION")]
        CPSucessfullyStartMigration = 63,

        [EnumDescription("START REPLICATION")]
        CPStartReplication = 64,

        [EnumDescription("SUCESSFULLLY START REPLICATION")]
        CPSucessfullyStartReplication = 65,

    }

    [Serializable]
    public enum UserCrendential
    {
        [EnumDescription("Yes")]
        Yes = 1,
        [EnumDescription("No")]
        No = 0,

    }

    [Serializable]
    public enum RPORTOUnit
    {
        [EnumDescription("Days")]
        twoHours = 1,
        [EnumDescription("Hours")]
        Twoweek = 2,
        [EnumDescription("Minutes")]
        Minutes = 3,
        [EnumDescription("Second")]
        Second = 4,
    }


    [Serializable]
    public enum Workflow_RedisSubActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("RedisDBTableCountMatch")]
        RedisDBTableCountMatch = 1,

    }

    [Serializable]
    public enum Workflow_VirtualizationActionCategoryType
    {
        [EnumDescription("Undefined")]
        Undefined = 0,

        [EnumDescription("RedHatVirtualization")]
        RedHatVirtualization = 1,


    }
}
