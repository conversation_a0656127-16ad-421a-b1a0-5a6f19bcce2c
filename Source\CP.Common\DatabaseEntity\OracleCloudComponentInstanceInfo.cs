﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "OracleCloudComponentInstanceInfo", Namespace = "http://www.ContinuityPlatform.com/types")]
   public class OracleCloudComponentInstanceInfo :BaseEntity
    {
        #region Properties

        public int InfraObjectId { get; set; }
        public string CompartmentName { get; set; }
        public string CompartmentTotalInstances { get; set; }
        public string CompartmentRunningInstances { get; set; }
        public string CompartmentStoppedInstances { get; set; }
        public string CompartmentTerminatedInstances { get; set; }


        #endregion Properties

    }
}
