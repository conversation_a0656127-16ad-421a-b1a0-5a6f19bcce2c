﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.Serialization;
using CP.Common.Base;
namespace CP.Common.DatabaseEntity
{
    public class RPVMReplicationmonitor : BaseEntity
    {
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string ConsistencyGroupName { get; set; }

        [DataMember]
        public string PRPluginServer { get; set; }
        [DataMember]
        public string DRPluginServer { get; set; }
        [DataMember]
        public string PRvCenterName { get; set; }
        [DataMember]
        public string DRvCenterName { get; set; }
        [DataMember]
        public string PRvRPAClusterName { get; set; }

        [DataMember]
        public string DRvRPAClusterName { get; set; }
        [DataMember]
        public string PRRecoverPointVMsVersion { get; set; }
        [DataMember]
        public string DRRecoverPointVMsVersion { get; set; }
        [DataMember]
        public string ProtectedSize { get; set; }
        [DataMember]
        public string PRProtectedVMNames { get; set; }
        [DataMember]
        public string DRProtectedVMNames { get; set; }
        [DataMember]
        public string TransferStatus { get; set; }
        [DataMember]
        public string State { get; set; }

         [DataMember]
        public string LatestSnapTimestamp { get; set; }

        
        [DataMember]
        public string Datalag { get; set; }

        [DataMember]
        public string CreateDate { get; set; }

        [DataMember]
        public string UpdateDate { get; set; }
    }
}
