﻿

var base64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef" +
   "ghijklmnopqrstuvwxyz0123456789+/=";

function encode64(input) {
    var output = "";
    var ch1, ch2, ch3, enc1, enc2, enc3, enc4;
    var i = 0;

    do {
        ch1 = input.charCodeAt(i++);
        ch2 = input.charCodeAt(i++);
        ch3 = input.charCodeAt(i++);

        enc1 = ch1 >> 2;
        enc2 = ((ch1 & 3) << 4) | (ch2 >> 4);
        enc3 = ((ch2 & 15) << 2) | (ch3 >> 6);
        enc4 = ch3 & 63;

        if (isNaN(ch2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(ch3)) {
            enc4 = 64;
        }

        output = output + base64.charAt(enc1) + base64.charAt(enc2) +
          base64.charAt(enc3) + base64.charAt(enc4);
        ch1 = ch2 = ch3 = "";
        enc1 = enc2 = enc3 = enc4 = "";
    } while (i < input.length);
    return output;
}

function Swaping(UserCode) {
    var len = (UserCode.length) / 3;
    var code1 = "";
    var i = 0;
    for (i = 0; i < UserCode.length ; i++) {

        code1 = code1 + UserCode.charAt(i + 1) + UserCode.charAt(i)
        i++
    }
    return code1;
}


function shuffle(array) {
    var currentIndex = array.length, temporaryValue, randomIndex;
    while (0 !== currentIndex) {
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex -= 1;
        temporaryValue = array[currentIndex];
        array[currentIndex] = array[randomIndex];
        array[randomIndex] = temporaryValue;
    }
    return array;
}

function reverse(s) {
    var o = '';
    for (var i = s.length - 1; i >= 0; i--)
        o += s[i];
    return o;
}
function ClearVal(control) {

    control.value = '';
}


function genrateUserNameHash(control, guid) {
    var strResult = "";
    var strData = control.value;
    if (strData != "") {
        var strGuid = guid;
        var GuidArr = strGuid.split('-');
        var strEncryptData = Swaping(encode64(strData));
        var j = 0;
        for (var i = 0; i < strEncryptData.length; i++) {
            if (i % 5 == 0)
                j = 0;
            GuidArr[j] += strEncryptData[i];
            j++;
        }
        for (var k = 0; k < GuidArr.length; k++) {
            strResult += GuidArr[k] + "-";
        }
        strResult = strResult.substring(0, strResult.length - 1);
        var arr = shuffle(strResult.split('-'));
        var valforcontrol = "";
        for (var k = 0; k < arr.length; k++)
            if (arr) {
                arr[k] = reverse(arr[k]);
                valforcontrol += arr[k];
            }

        if (control.id != "ctl00_cphBody_UserName")
            control.value = valforcontrol;
    }
    return strResult;
}


function getOrignalData(control, guid) {
    var hashArr = [];
    var strHash = control.value;
    var guidArr = guid.split('-');
    var hashArr = strHash.split('-');
    var data = {};
    for (var i = 0; i < guidArr.length; i++) {
        if (hashArr[i] !== undefined) {
            var g = guidArr[i].length;
            var h = hashArr[i].length;
            data[i] = hashArr[i].substring(g, h);
        }
    }
    //var lgth = 0;
    //for (var i = 0; i < 5; i++) {
    //    if (data[i].length > lgth) {
    //        lgth = data[i].length;
    //    }
    //}
    var strResult = "";
    var t = 0;
    for (var a = 0; a < 5; ++a) {
        if (typeof data !== 'undefined') {
            if (data[0].length == 0 && data[1].length == 0 && data[2].length == 0 && data[3].length == 0 && data[4].length == 0) {
                return DecodeVal(strResult);
            }
            if (t == 5)
                a = t = 0;
            var l = data[a].length;
            if (l > 0) {
                strResult += data[a][0];
                data[a] = data[a].slice(1);
            }
            if (a == 4)
                a = 0;
            t++;
        }
    }
}

decodeBase64 = function (s) {
    var e = {}, i, b = 0, c, x, l = 0, a, r = '', w = String.fromCharCode, L = s.length;
    for (i = 0; i < 64; i++) { e[base64.charAt(i)] = i; }
    for (x = 0; x < L; x++) {
        c = e[s.charAt(x)]; b = (b << 6) + c; l += 6;
        while (l >= 8) { ((a = (b >>> (l -= 8)) & 0xff) || (x < (L - 2))) && (r += w(a)); }
    }
    return r;
};
function DecodeVal(strData) {
    var decode = "";
    var actual = "";
    var ln = strData.length;
    for (var i = 0; i < ln; i++) {
        actual = actual + strData[i + 1] + strData[i];
        i++;
    }

    return decodeBase64(actual);
}


function GetOriginalResponse(encText) {
    if (encText == '' ||  encText == null)
        return '';
    var key = CryptoJS.enc.Utf8.parse('8080808080808080');
    var iv = CryptoJS.enc.Utf8.parse('8080808080808080');

    //var encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(txtUserName), key,
    //{
    //    keySize: 128 / 8,
    //    iv: iv,
    //    mode: CryptoJS.mode.CBC,
    //    padding: CryptoJS.pad.Pkcs7
    //});
    var decrypted = CryptoJS.AES.decrypt(encText, key,
       {
           keySize: 128 / 8,
           iv: iv,
           mode: CryptoJS.mode.CBC,
           padding: CryptoJS.pad.Pkcs7
       });
    return decrypted.toString(CryptoJS.enc.Utf8);
}

