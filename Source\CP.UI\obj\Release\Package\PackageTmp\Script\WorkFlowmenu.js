﻿var formClose = "";
var totalRTO = "";
var deAttachClose = "";
var btnDeAttachGroupisOpen = "";
var selectedInfraCount = [];
var existingRTOInfras = [];

$("#groupAttech").click(function () {
    btnDeAttachGroupisOpen = "";
    //var mainContent = groupAttachAndDeAttachModalPopUp();
    var mainContent = groupAttachAndDeAttachModalPopUpNew();
    openModal("Attach InfraObject", mainContent, "Reset:Update:Cancel:", SaveRelation, "");
    $(".row").css({ "margin-left": "-10px", "margin-right": "-10px" });
    $(".modal-footer").css('padding', '5px');
    $(".modal-header .modal-title").css('line-height', '29px');
    $(".close").css({ "font-size": "28px", "color": "#fff" });
    $(".modal-footer button:last-child").css('margin-right', '16%');
    groupAttechClick();
});

$("#btnDeAttachGroup").click(function () {
    btnDeAttachGroupisOpen = "btnDeAttachGroupisOpen";
    var mainContent = groupAttachAndDeAttachModalPopUp();
    OpenDeAttachGroupWorkflow(mainContent, DeleteRelation);
    $(".row").css({ "margin-left": "-10px", "margin-right": "-10px" });
    $(".modal-footer").css('padding', '5px');
    $(".modal-header .modal-title").css('line-height', '29px');
    $(".close").css({ "font-size": "28px", "color": "#fff" });
    $(".modal-footer button:last-child").css('margin-right', '16%');
});

//added by hanumant 26042019
function groupAttachAndDeAttachModalPopUpNew() {

    var groups = "<div class='form-group'> <div  class='col-md-3'>Infraobject </div>" +
                "<div  class='col-md-9'><select id='groupAtech' class='chosen-select' style='width:80%'> Infraobjects </select><span></span></div> </div>";
    var workFlows = "<div class='form-group'> <div  class='col-md-3'>Workflow </div>" +
                    "<div  class='col-md-9'><select id='workFlows' class='chosen-select'  style='width:80%'> WorkFlows </select><span></span></div></div>";
    var typeAction = "<div class='form-group margin-bottom-none'> <div  class='col-md-3'>Action Type </div>" +
                    "<div  class='col-md-9'><select id='TypeAction' style='width:95%'> Action Type </select><span></span></div></div>";
    var isRTOUpdate = "<div class='form-group' id='RTODiv' style='display:none;margin-top: 12px;'> <div  class='col-md-3'>RTO Update </div>" +
                    "<div  class='col-md-9'><input type='checkbox' id='isRTOUpdate'/><span></span></div></div>";
    var infralist = "<div class='form-group' id='infralist' style='display:none'> <div  class='col-md-3'>InfraObject's </div>" +
                   "<div  class='col-md-9'><div id='divSelectInfraList'><input id='Button123' type='button' value='-- Select Infraobject--' class='select align-left 'style='width: 95%;' /><span></span></div>" +
                   " <div class='tree_scroll' style='display:none'> <ul id='tree'></ul></div><span></span></div></div>";

    var mainContent = "<div class='col-md-12 form-horizontal uniformjs'> <div class='row'> " + groups + workFlows + typeAction + isRTOUpdate + infralist + "</div>";
    var ajaxUrl = "WorkflowConfiguration.aspx/GetExistingWorkflow";
    var ajaxData = "{'args':'GetWorkFlow'}";
    AjaxFunction(ajaxUrl, ajaxData, GetAllWorkFlows, OnError);

    var ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllInfraObject";
    var ajaxDatagroup = "{}";
    AjaxFunction(ajaxUrlgroup, ajaxDatagroup, GetAllInfraObject, OnError);

    var ajaxUrltype = "WorkflowConfiguration.aspx/FetchTypeAction";
    var ajaxDatatype = "{}";
    AjaxFunction(ajaxUrltype, ajaxDatatype, GetTypeAction, OnError);
    return mainContent;
}

$(".io_p").live('click', function () {
    $(this).next('ul[id^=ulBS_]').toggle();
    $(this).toggleClass("minus");
});

//TypeAction
$('#TypeAction').live('change', function () {
    //$('#isRTOUpdate').prop('checked', false);

    $("#isRTOUpdate").attr('checked', false);
    $("#isRTOUpdate").next().children().children('span').css('display', 'block');
    $("#isRTOUpdate").next().children().children('span.cb-icon-check').css('display', 'none');
    $("#RTODiv").hide();
    $("#infralist").hide();

    var actiontype = $('#TypeAction').val();
    if (actiontype == 1 || actiontype == 3) {
        $("#RTODiv").show();
        //groupAtech 000 /workFlows 000 /TypeAction 000
        existingRTOInfras = [];
        var groupAtech = $('#groupAtech').val();
        var workFlows = $('#workFlows').val();
        var TypeAction = $('#TypeAction').val();
        if (groupAtech != "000" && workFlows != "000" && TypeAction != "000") {
            $.ajax({
                type: "POST",
                url: "WorkflowConfiguration.aspx/GetRTOUpdateInfraobjectsByGruopWFAndActionType",
                data: "{'groupAtech':'" + groupAtech + "','workFlows':'" + workFlows + "','TypeAction':'" + TypeAction + "'}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                success: function Success(data) {
                    data.d = GetOriginalResponse(data.d);
                    if (data.d != "") {
                        
                        $('input[type="checkbox"]').checkbox();

                        var res = data.d;
                        if (res != null) {
                            res = data.d.split(":");
                            if (res.length > 0) {
                                for (var i = 0; i < res.length; i++) {
                                    existingRTOInfras[i] = res[i];
                                }
                                //this.checked = true;
                                $("#isRTOUpdate").attr('checked', 'checked');
                                $("#isRTOUpdate").next("span.bootstrap-checkbox").find("span").hide();
                                $("#isRTOUpdate").next("span.bootstrap-checkbox").find("span:first-child").show();

                            }
                            //this.checked = true;k
                            $("ul#tree").empty();
                            $('#Button123').val('-- Select Infraobject--');
                            if ($("#isRTOUpdate").is(":checked")) {

                                var ajaxUrlgroup;
                                var ajaxDatagroup;
                                ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllApplicationGroup";
                                ajaxDatagroup = "{}";
                                AjaxFunction(ajaxUrlgroup, ajaxDatagroup, createTreeViewBS, OnError);

                                $("#infralist, .tree_scroll").show();

                                $(".tree_scroll").mCustomScrollbar({
                                    axis: "y",
                                });
                                // $('#tree :checkbox').live('click', function () {
                                //$('#tree :checkbox').trigger("click");
                                var s = "71";
                                $("#liBS_" + s).find("span.io_p").addClass("minus").siblings("ul").show();
                            }
                            else {
                                $("#infralist").hide();
                            }
                        }
                    }
                }
            });
        }
    }
    else {
        $("#RTODiv").hide();
        $("#infralist").hide();

    }


    // $('[id$=isRTOUpdate]').trigger("click");
});

$('#isRTOUpdate').live('click', function () {
    //groupAtech 000 /workFlows 000 /TypeAction 000
    //existingRTOInfras = [];
    //var groupAtech = $('#groupAtech').val();
    //var workFlows = $('#workFlows').val();
    //var TypeAction = $('#TypeAction').val();
    //if (groupAtech != "000" && workFlows != "000" && TypeAction != "000") {
    //    $.ajax({
    //        type: "POST",
    //        url: "WorkflowConfiguration.aspx/GetRTOUpdateInfraobjectsByGruopWFAndActionType",
    //        data: "{'groupAtech':'" + groupAtech + "','workFlows':'" + workFlows + "','TypeAction':'" + TypeAction + "'}",
    //        contentType: "application/json; charset=utf-8",
    //        dataType: "json",
    //        async: true,
    //        success: function Success(data) {
    //            var res = data.d;
    //            if (res != null) {
    //                res = data.d.split(":");
    //                if (res.length > 0) {
    //                    for (var i = 0; i < res.length; i++) {
    //                        existingRTOInfras[i] = res[i];
    //                    }
    //                    //this.checked = true;
    //                }
    //            }
    //        }
    //    });
    //}

    $("ul#tree").empty();
    $('#Button123').val('-- Select Infraobject--');
    if (this.checked) {
        $("#infralist").show();//on checked- show infralist
        var ajaxUrlgroup;
        var ajaxDatagroup;
        ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllApplicationGroup";
        ajaxDatagroup = "{}";
        AjaxFunction(ajaxUrlgroup, ajaxDatagroup, createTreeViewBS, OnError);

        $(".tree_scroll").mCustomScrollbar({
            axis: "y",
        });

    }
    else {
        $("#infralist").hide();//on unchecked- hide infralist
        $('.tree_scroll').hide();
    }
});

function createTreeViewBS(msg) {
    var result = msg.d.split(",");
    if (result.length > 0) {
        for (var i = 0; i < result.length; i++) {//for BS

            var selectedValue = result[i].split(":");
            var text = selectedValue[0];
            var value = selectedValue[1];
            var li_t = "<li id='liBS_" + value + "'> <span class='plus io_p'>&nbsp;</span> " + text + " </li>"
            $("#tree").append(li_t);
            var ul = document.createElement("ul");
            ul.setAttribute('id', "ulBS_" + value);
            ul.setAttribute('style', "display:none");
            $("#liBS_" + value).append(ul);
            //end bs
        }
        //for binding infraobject
        $.ajax({
            type: "POST",
            url: "WorkflowConfiguration.aspx/GetAllInfraobjectByUserId",
            data: "{}",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: true,
            success: function Success(data) {
                
                msg.d = GetOriginalResponse(msg.d);
                var res = data.d.split(",");
                if (res.length > 0) {
                    for (var i = 0; i < res.length; i++) {//for infra
                        var chkValue = res[i].split(":");
                        var itext = chkValue[0];
                        var ivalue = chkValue[1];
                        var bvalue = chkValue[2];
                        var liio = "<li id='liio_" + ivalue + "'> <input type='checkbox' name='chkinfra' value='" + ivalue + "' id='" + ivalue + "'> " + itext + " </li>"
                        $("#ulBS_" + bvalue).append(liio);
                    }
                    //groupAtech
                    var grpval = $("#groupAtech option:selected").val();
                    if (grpval != "000") {
                        $("#liio_" + grpval).remove();
                    }
                    if (existingRTOInfras.length > 0) {
                        for (var j = 0; j < existingRTOInfras.length; j++) {
                            $("#" + existingRTOInfras[j]).attr('checked', 'checked');

                            $($("#" + existingRTOInfras[j]).parent().parent().parent()[0].children[0]).addClass("minus");
                            $($("#" + existingRTOInfras[j]).parent().parent().parent()[0].children[1]).show();
                        }
                        if (existingRTOInfras.length == 1)
                            $('#Button123').val('(' + existingRTOInfras.length + ') InfraObject Selected.');
                        else if (existingRTOInfras.length > 1)
                            $('#Button123').val('(' + existingRTOInfras.length + ') InfraObject\'s Selected.');
                    }

                }
            }
        });
    }
}

function getIsRTOUpdateCheckedInfraList() {
    var checkedInfra = [];
    $.each($("input[name='chkinfra']:checked"), function () {
        checkedInfra.push($(this).val());
    });
    if (checkedInfra.length > 0)
        return checkedInfra.join(":");
    else
        return null;
}

$("#divSelectInfraList input[type='button']").live('click', function () {
    var sf_menu_sub = $('.tree_scroll');
    sf_menu_sub.toggle();
    $('input[type="checkbox"]').checkbox();
});

$('#tree :checkbox').live('click', function () {
    var checkedInfra = [];
    $.each($("input[name='chkinfra']:checked"), function () {
        checkedInfra.push($(this).val());
    });
    if (checkedInfra.length == 1)
        $('#Button123').val('(' + checkedInfra.length + ') InfraObject Selected.');
    else if (checkedInfra.length > 1)
        $('#Button123').val('(' + checkedInfra.length + ') InfraObject\'s Selected.');
    else
        $('#Button123').val('-- Select Infraobject--');


});

function DeleteRelation(win) {

    deAttachClose = win;
    $("#groupAtech,#workFlows,#TypeAction").trigger("blur");

    $("#groupAtech").next("div").show();
    // $("#groupAtech").chosen({ search_contains: true });
    //$("#groupAtech").trigger("chosen:updated");
    if ($("#groupAtech").siblings(".error").is(":visible")) {
        return false;
    }
    if ($("#workFlows").siblings(".error").is(":visible")) {
        return false;
    }
    if ($("#TypeAction").siblings(".error").is(":visible")) {
        return false;
    }
    var selectedItem = $("#groupAtech option:selected").val();
    var workFlowId = $("#workFlows option:selected").val();
    var actionTypeId = $("#TypeAction option:selected").val();

    var infraObjectName = $("#groupAtech option:selected").text();
    var workFlowName = $("#workFlows option:selected").text();
    var actionTypeName = $("#TypeAction option:selected").text();
    var values = workFlowId + "," + selectedItem + "," + actionTypeId + "," + totalRTO + "," + infraObjectName + "," + workFlowName + "," + actionTypeName;

    //var username = $('[id$=lblLoggedUser]').html();
    var username = $('[id$=lblUserName]').html();

    username = username.replace(/\\/g, '````');


    $.ajax({
        type: "POST",

        url: "WorkflowConfiguration.aspx/CheckProfileWithInfraobjectIdAndWorkflowId",
        data: "{'values':'" + values + "','username':'" + username + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (msg) {
            if (msg.d == "WorkFlowScheduled")
                OpenAlertModelAlert("<div class='margin-bottom8' style='line-height:1.5em;'>Workflow  is Scheduled for this InfraObject. !!!</div>");
            else if (msg.d != "NotExist") {
                OpenAlertModelAlert("<div class='margin-bottom8' style='line-height:1.5em;'>Profile <b class='active'> " + msg.d + "</b> is Created for this infraobject and workflow !!!</div>");
            } else {
                var ajaxUrl = "WorkflowConfiguration.aspx/DeAttachGroupWorkflow";
                var ajaxData = "{'values':'" + values + "','username':'" + username + "'}";// "{infraObject: " + selectedItem + ",workFlowId:" + workFlowId + ",actionTypeId:" + actionTypeId + ",infraObjectName:" + infraObjectName + ",workFlowName:" + workFlowName + ",actionTypeName:" + actionTypeName + "}";
                if (selectedItem != "000" && workFlowId != "000" && actionTypeId != "000") {
                    //AjaxFunction(ajaxUrl, ajaxData, DeAttachGroup, OnError);
                    if (actionTypeId == 7 || actionTypeId == 8) {

                        $("#ctl00_cphBody_chkActiveDirectory").attr('checked', false);
                        $("#ctl00_cphBody_chkActiveDirectory + span .btn-link span.icon").hide();
                        $("#ctl00_cphBody_chkActiveDirectory + span .btn-link span.icon.cb-icon-check-empty").show();
                        $("#labelSDErrormessage").hide();

                        $("#UserName").val('');
                        $("#Password").val('');
                        $("#pnluserauthon").show();
                        $("#loguserbtn").hide();
                        $("#loguserbtndetach").show();

                        $("#ctl00_cphBody_pnlDomain").hide();
                        $('#ctl00_cphBody_chkActiveDirectory').trigger("change");

                    } else {
                        $("#pnluserauthon").hide();
                        AjaxFunction(ajaxUrl, ajaxData, DeAttachGroup, OnError);
                    }
                }
            }
        },
        error: function (msg) {
            alert(msg.d);


        }

    });




}

function DeAttachGroup(msg) {
    if (msg.d == "success") {
        CloseModel(deAttachClose);
        OpenAlertModelAlert("InfraObject De-Attach Successfully !!!");
        deAttachClose = "";
        btnDeAttachGroupisOpen = "";
    } else if (msg.d == "Error") {
        CloseModel(deAttachClose);
        OpenAlertModelAlert("This InfraObject is not Attached !!!");
        deAttachClose = "";
        btnDeAttachGroupisOpen = "";
    }
}
//function groupAttachAndDeAttachModalPopUpDettach() { 
//    //var groupType = "<div class='form-group'> <div class='col-md-4'>App Type</div>" +
//    //            "<div  class='col-md-8'><select id='groupType'> <option value='0'> --- Select Type --- </option><option value='1'>InfraObject Type</option><option value='2'>Application Type</option> </select><span></span></div></div>";
//    var groups = "<div class='form-group'> <div  class='col-md-4'>Infraobject </div>" +
//	            "<div  class='col-md-8'><select id='groupAtech'> Infraobjects </select><span></span></div> </div>";
//    var workFlows = "<div class='form-group'> <div  class='col-md-4'>Workflow </div>" +
//                    "<div  class='col-md-8'><select id='workFlows'> WorkFlows </select><span></span></div></div>";
//    var typeAction = "<div class='form-group'> <div  class='col-md-4'>Action Type </div>" +
//                    "<div  class='col-md-8'><select id='TypeAction'> Action Type </select><span></span></div></div>";
//    var mainContent = "<div class='col-md-12 form-horizontal uniformjs'> <div class='row'> " + groups + workFlows + typeAction + "</div>";
//    var ajaxUrl = "WorkflowConfiguration.aspx/GetExistingWorkflow";
//    var ajaxData = "{'args':'GetWorkFlow'}";
//    AjaxFunction(ajaxUrl, ajaxData, GetAllWorkFlows, OnError);

//    var ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllInfraObject";
//    var ajaxDatagroup = "{}";
//    AjaxFunction(ajaxUrlgroup, ajaxDatagroup, GetAllInfraObject, OnError);

//    var ajaxUrltype = "WorkflowConfiguration.aspx/FetchTypeAction";
//    var ajaxDatatype = "{}";
//    AjaxFunction(ajaxUrltype, ajaxDatatype, GetTypeAction, OnError);
//    return mainContent;
//}

function groupAttechClick() {
    $("#workFlows").empty();
    var ajaxUrl = "WorkflowConfiguration.aspx/GetExistingWorkflow";
    var ajaxData = "{'args':'GetWorkFlow'}";
    AjaxFunction(ajaxUrl, ajaxData, GetAllWorkFlows, OnError);

}


function groupAttachAndDeAttachModalPopUp() {
    //var groupType = "<div class='form-group'> <div class='col-md-4'>App Type</div>" +
    //            "<div  class='col-md-8'><select id='groupType'> <option value='0'> --- Select Type --- </option><option value='1'>InfraObject Type</option><option value='2'>Application Type</option> </select><span></span></div></div>";
    var groups = "<div class='form-group'> <div  class='col-md-3'>Infraobject </div>" +
	            "<div  class='col-md-9'><select id='groupAtech' class='chosen-select' style='width:80%'> Infraobjects </select><span></span></div> </div>";
    var workFlows = "<div class='form-group'> <div  class='col-md-3'>Workflow </div>" +
                    "<div  class='col-md-9'><select id='workFlows' class='chosen-select'  style='width:80%'> WorkFlows </select><span></span></div></div>";
    var typeAction = "<div class='form-group margin-bottom-none'> <div  class='col-md-3'>Action Type </div>" +
                    "<div  class='col-md-9'><select id='TypeAction' style='width:95%'> Action Type </select><span></span></div></div>";
    var mainContent = "<div class='col-md-12 form-horizontal uniformjs'> <div class='row'> " + groups + workFlows + typeAction + "</div>";
    var ajaxUrl = "WorkflowConfiguration.aspx/GetExistingWorkflow";
    var ajaxData = "{'args':'GetWorkFlow'}";
    AjaxFunction(ajaxUrl, ajaxData, GetAllWorkFlows, OnError);

    var ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllInfraObject";
    var ajaxDatagroup = "{}";
    AjaxFunction(ajaxUrlgroup, ajaxDatagroup, GetAllInfraObject, OnError);

    var ajaxUrltype = "WorkflowConfiguration.aspx/FetchTypeAction";
    var ajaxDatatype = "{}";
    AjaxFunction(ajaxUrltype, ajaxDatatype, GetTypeAction, OnError);
    return mainContent;
}


$("#btnscheduler").click(function () {
    var workFlows = "<div class='grid-21 float-left'>Workflow </div>" +
                    "<div ><select id='workFlows'> WorkFlows </select><span></span></div><hr/>";
    var groups = "<div class='grid-21 float-left'>Group </div>" +
	            "<div ><select id='groupAtech'> Groups </select><span></span></div> <hr/>";
    var time = "<div class='grid-21 float-left'>Time </div>" +
                    "<div ><input type='text' readonly='true' name='txtTime' class='date' id='txtTimeScheduler'/><span></span></div><hr/>";
    var mainContent = groups + workFlows + time;
    var ajaxUrl = "WorkflowConfiguration.aspx/GetExistingWorkflow";
    var ajaxData = "{'args':'GetWorkFlow'}";
    AjaxFunction(ajaxUrl, ajaxData, GetAllWorkFlows, OnError);

    var ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllInfraObject";
    var ajaxDatagroup = "{}";
    AjaxFunction(ajaxUrlgroup, ajaxDatagroup, GetAllInfraObject, OnError);
    openModal("Schedule Workflow", mainContent, "Reset:Update:Cancel:", SaveWorkflowScheduler, "");
    $('#txtTimeScheduler').datetimepicker({ dateFormat: 'yy-mm-dd', minDate: 0 });
});

$("#txtTimeScheduler ,#workFlows ,#groupAtech,#TypeAction").live("blur", function () {
    RequireFieldForSchedule($(this).attr("id"));
});

function RequireFieldForSchedule(id) {
    var txt = $("#" + id).val();
    var span = $("#" + id).next();
    if ($("#" + id).hasClass("chosen-select"))
        span = $("#" + id).next().next();
    txt = jQuery.trim(txt);
    // var length = txt.length;
    var span;
    if (txt == "000" || txt == "") {
        $(span).html("*");
        $(span).show();
        $(span).attr("class", "error");
        //return true;
    }
    else {
        $(span).hide();
        $(span).removeClass('error');
        //return false;
    }
}

function SaveWorkflowScheduler(win) {
    $("#txtTimeScheduler,#groupAtech,#workFlows").trigger("blur");
    if ($("#groupAtech .error").is(":visible")) {
        return false;
    }
    if ($("#workFlows .error").is(":visible")) {
        return false;
    }
    if ($("#txtTimeScheduler .error").is(":visible")) {
        return false;
    }
    formClose = win;
    var groupName = $("#groupAtech option:selected").text();
    var workFlowName = $("#workFlows option:selected").text();
    var timeScheduler = $("#txtTimeScheduler").val();
    if (groupName == "-Select Infraobject-" || workFlowName == "-Select workFlow-" || timeScheduler == "") {
        return false;
    }
    OpenAlertModelActionGroup("<div style='line-height: 17px; text-indent:0;'> Are you sure you want to schedule <b class='text-success'>" + workFlowName + "</b> workflow for <b class='text-success'>" + groupName + "</b> group for time <b class='text-success'>" + timeScheduler + "</b>.</div>", closeScheduleWorkflowCoinfirmationModelPopup);
}

function closeScheduleWorkflowCoinfirmationModelPopup(win) {
    var grId = $("#groupAtech option:selected").val();
    var workFlowId = $("#workFlows option:selected").val();
    var timeScheduler = $("#txtTimeScheduler").val();
    var values = workFlowId + "," + grId + "," + timeScheduler;
    var ajaxUrl = "WorkflowConfiguration.aspx/SaveWorkflowSchedule";
    var ajaxData = "{'values':'" + values + "'}";
    AjaxFunction(ajaxUrl, ajaxData, WorkflowScheduler, OnError);
    CloseModel(win);
}

//    function CheckduplicateScheduler(msg)
//    {
//      if(msg=="success")
//      {
//           OpenAlertModelActionGroup("Are you sure you want to update Workflow Schedule? ", closeScheduleWorkflowModelPopup);
//      }
//      else if(msg=="Error")
//      {
//        var grId = $("#groupAtech option:selected").val();
//        var workFlowId=$("#workFlows option:selected").val();
//	    var timeScheduler=$("#txtTimeScheduler").val();
//	    var values =workFlowId +"," +grId+"," +timeScheduler;
//	    var ajaxUrl = "WorkflowConfiguration.aspx/SaveWorkflowSchedule";
//	    var ajaxData = "{'values':'"+ values +"'}";
//	    AjaxFunction(ajaxUrl, ajaxData,WorkflowScheduler , OnError);
//      }
//    }

function closeScheduleWorkflowModelPopup(win) {
    var grId = $("#groupAtech option:selected").val();
    var workFlowId = $("#workFlows option:selected").val();
    var timeScheduler = $("#txtTimeScheduler").val();
    var values = workFlowId + "," + grId + "," + timeScheduler;
    var ajaxUrl = "WorkflowConfiguration.aspx/SaveWorkflowSchedule";
    var ajaxData = "{'values':'" + values + "'}";
    AjaxFunction(ajaxUrl, ajaxData, WorkflowScheduler, OnError);
    CloseModel(win);
}

function WorkflowScheduler(msg) {
    if (msg.d == "Success") {
        $("#groupAtech").empty();
        $("#workFlows").empty();
        $("#txtTimeScheduler").val("");
        OpenAlertModelAlert("Workflow Schedule Successfully !!!");
        CloseModel(formClose);
        formClose = "";
    }
}


//$(function()
//    {
//      $("#btnHeader, #idworkflow").live("hover",function(){
//      $("#idworkflow").toggle();
//    });
//});

$("#btnManageGroupWorkflow").click(function () {
    var groups = "<div class='grid-21 float-left'>Group </div>" +
	            "<div ><select id='manageGroupAtech'> Groups </select><span></span></div> <hr/>";
    //var workFlowName = "<div class='grid-21 float-left'>Workflow </div>"+
    //                   "<div id='idOwnerHeader'><input id='btnHeader' type='button' value='-- Select workflow--' class='select align-left' style='width: 35%;' /><div id='idworkflow' class='dropdownli' style='margin-left: 39%; display: none; width: 32%;' ><table></table></div></div><div class='clear-both'></div><hr/>";
    var typeAction = "<div class='grid-21 float-left'>Action Type </div>" +
                    "<div ><select id='TypeActionForManageGrp'> Action Type </select><span></span></div><hr/>";
    var workFlowName = "<div class='grid-21 float-left'>Workflow(s)</div><div id='idworkflow' class='dropdownli' style='margin-left: 44%; display: block; width: 55.5%; position:relative;'></div> <div class='clear-both'></div><hr/>";
    var mainContent = groups + typeAction + workFlowName;
    var ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllInfraObject";
    var ajaxDatagroup = "{}";
    AjaxFunction(ajaxUrlgroup, ajaxDatagroup, GetAllManageGroup, OnError);
    var ajaxUrltype = "WorkflowConfiguration.aspx/FetchTypeAction";
    var ajaxDatatype = "{}";
    AjaxFunction(ajaxUrltype, ajaxDatatype, GetActionTypeForManageGroup, OnError);
    //openModal("Manage Group Workflow",mainContent,"Reset:Update:Cancel:",SaveManageGroupWorkflow,"");
    OpenManageGroupWorkflowPopUp(mainContent);
    $('[id$=idworkflow]').append("<div class='margin-bottom8' style='line-height:1.5em;'><b style='display:block'> First select Infraobject name and action type</b></div>");
});

function GetAllManageGroup(msg) {
    var result = msg.d.split(",");
    var text = "-Select Infraobject-";
    var value = "000";
    var chkValue = "";
    AppendOption('manageGroupAtech', value, text);
    for (var i = 0; i < result.length; i++) {
        chkValue = result[i].split(":");
        text = chkValue[0];
        value = chkValue[1];
        $("#manageGroupAtech").append("<option value='" + value + "'>" + text + "</option>");
    }
}
$("#TypeActionForManageGrp").live("change", function () {
    var groupName = $("#manageGroupAtech option:selected").val();
    var actionType = $("#TypeActionForManageGrp option:selected").val();
    if (groupName != "000") {
        var manageData = groupName + "," + actionType;
        var ajaxUrl = "WorkflowConfiguration.aspx/GetManageGroupWorkflowByGroupId";
        var ajaxData = "{'values':'" + manageData + "'}";
        AjaxFunction(ajaxUrl, ajaxData, GetManageGroupWorkflow, OnError);
    }
    else {
        $("[id$=idworkflow]>div").remove();
        $('[id$=idworkflow]').append("<div class='margin-bottom8' style='line-height:1.5em;'><b style='display:block'> First select Infraobject name and action type</b></div>");
    }
});

$("#manageGroupAtech").live("change", function () {
    $("#TypeActionForManageGrp").val(0);
    $("#TypeActionForManageGrp").trigger("change");
});


function GetManageGroupWorkflow(msg) {
    var workFlowResult = msg.d;
    var data = workFlowResult.split(",");
    var html = "";
    //		      var groupName = $("#manageGroupAtech option:selected").text();
    //	          var typeAction=$("#TypeActionForManageGrp option:selected").text();
    $("[id$=idworkflow] > div ").remove();
    //$("#idworkflow").show();
    if (data != "") {
        for (var i = 0; i < data.length; i++) {
            var volume = data[i].split(":");
            var text = volume[0];
            value = volume[1];
            html = html + "<div class='bold' style='border-bottom:1px solid grey; padding-bottom:5px; padding-top:5px;'>" + (i + 1) + ") " + "" + text + "</div>";
        }
        $('[id$=idworkflow]').append(html);
        // OpenAlertModelAlert("<div class='margin-bottom8' style='line-height:1.5em;'>Following workflow(s) as it is attached to <b class='active'> "+groupName+"</b> group for <b class='active'> "+typeAction+"</b> :-</div> <div class='text-indent scrollL active margin-bottom' style='width: 60%; height: 90px; margin: auto; clear: both;'>"+html+"</div>");
    } else {
        var emptyMessage = "<div class='margin-bottom8' style='line-height:1.5em;'>Workflow(s)are not attached to group.</div>";
        $('[id$=idworkflow]').append(emptyMessage);
        //OpenAlertModelAlert("<div class='margin-bottom8' style='line-height:1.5em;'>Workflow(s) are not attached to <b class='active'> "+groupName+"</b> group for <b class='active'> "+typeAction+"</b> :-</div>");
    }
}


function GetAllAttachedWorkFlows(msg) {
    var result = msg.d.split(",");
    if (result == "") {
        $("select[id$=workFlows] > option").remove();
        OpenAlertModelAlert("For this group workflow is not attached !!!");
        return false;
    }
    var text = "-Select workFlow-";
    var value = "000";
    var chkValue = "";
    $("select[id$=workFlows] > option").remove();
    AppendOption('workFlows', value, text);
    for (var i = 0; i < result.length; i++) {
        chkValue = result[i].split(":");
        text = chkValue[0];
        value = chkValue[1];
        $("#workFlows").append("<option value='" + value + "'>" + text + "</option>");
    }
}

$("#manageGroupAtech,#workFlows,#TypeActionForManageGrp").live("change", function () {
    RequireDropDown($(this).attr("id"));
});
function SaveManageGroupWorkflow(win) {
    $("#manageGroupAtech,#workFlows,#TypeActionForManageGrp").trigger("blur");
    if ($("#manageGroupAtech .error").is(":visible")) {
        return false;
    }
    if ($("#workFlows .error").is(":visible")) {
        return false;
    }
    if ($("#TypeActionForManageGrp .error").is(":visible")) {
        return false;
    }

    var groupName = $("#manageGroupAtech option:selected").text();
    var workFlowName = $("#workFlows option:selected").text();
    var typeAction = $("#TypeActionForManageGrp option:selected").text();
    OpenAlertModelActionGroup("<div style='line-height: 17px; text-indent:0;'> Are you sure you want to Manage <b class='text-success'>" + workFlowName + "</b> workflow for <b class='text-success'>" + groupName + "</b> group for action type <b class='text-success'>" + typeAction + "</b>.</div>", closeManageGroupWorkflowCoinfirmationModelPopup);
}

function closeManageGroupWorkflowCoinfirmationModelPopup(win) {
    CloseModel(win);
}


function Checkduplicate(msg) {
    if (msg.d == "success") {
        $("#groupAtech").next("div").show();
        OpenAlertModelActionGroup("Are you sure you want to update? ", closeModelPopupActionGroup);
    }
    else if (msg.d == "Error") {
        var grId = $("#groupAtech option:selected").val();
        var workFlowId = $("#workFlows option:selected").val();
        var actionTypeId = $("#TypeAction option:selected").val();

        var infraObjectName = $("#groupAtech option:selected").text();
        var workFlowName = $("#workFlows option:selected").text();
        var actionTypeName = $("#TypeAction option:selected").text();

        var selectedinfralist = getIsRTOUpdateCheckedInfraList();

        var values = workFlowId + "," + grId + "," + actionTypeId + "," + totalRTO + "," + infraObjectName + "," + workFlowName + "," + actionTypeName + "," + selectedinfralist;
        var ajaxUrl = "WorkflowConfiguration.aspx/SaveInfraObjectWorkflow";
        var ajaxData = "{'values':'" + values + "'}";
        AjaxFunction(ajaxUrl, ajaxData, WorkflowGroup, OnError);
    }
}

$("#groupAtech,#workFlows,#TypeAction").live("change", function () {
    RequireDropDown($(this).attr("id"));
});

$("#groupAtech").live("change", function () {
    if (btnDeAttachGroupisOpen == "btnDeAttachGroupisOpen") {
        $("#workFlows").empty();
        var groupAtechIdss = $("[id$=groupAtech] option:selected").val();
        var ajaxUrl = "WorkflowConfiguration.aspx/GetExistingWorkflowbyInfraobject12";
        var ajaxData = "{'args':'" + groupAtechIdss + "'}";
        AjaxFunction(ajaxUrl, ajaxData, GetAllWorkFlows, OnError);
        //if (deAttachClose = "") {
        //    btnDeAttachGroupisOpen = "";
        //}
    }
});

$("#workFlows").live("change", function () {
    var workflowId = $("[id$=workFlows] option:selected").val();
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/GetRtoOfSelectedWorkflow",
        data: "{'id':'" + workflowId + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (msg) {
            GetSelectedWorkFlowRTO(msg);
        }
    });
});
function GetSelectedWorkFlowRTO(msg) {
    totalRTO = msg.d;
    //	    var workflowId  = $("[id$=workFlows] option:selected").val();
    //	    var value= workflowId + "," + totalRto;
    //	      $.ajax({
    //                type: "POST",
    //                url: "WorkflowConfiguration.aspx/UpdateTotalRTO",
    //                data: "{'value':'" + value + "'}",
    //                contentType: "application/json; charset=utf-8",
    //                dataType: "json",
    //                success: function(data)
    //                {
    //                    updateTotalRTO(data);
    //                }
    //         });
}
//    function updateTotalRTO(data) {
//
//    }
function SaveRelation(win) {
    formClose = win;
    $("#groupAtech,#workFlows,#TypeAction").trigger("blur");
    if ($("#groupAtech .error").is(":visible")) {
        return false;
    }
    if ($("#workFlows .error").is(":visible")) {
        return false;
    }
    if ($("#TypeAction .error").is(":visible")) {
        return false;
    }

    var selectedItem = $("#groupAtech option:selected").val();
    var workFlowId = $("#workFlows option:selected").val();
    var actionTypeId = $("#TypeAction option:selected").val();
    var ajaxUrl = "WorkflowConfiguration.aspx/CheckDuplicate";
    var ajaxData = "{id: " + selectedItem + ",workFlowId:" + workFlowId + ",actionTypeId:" + actionTypeId + "}";
    var intRegex = /^\d+$/;
    if (intRegex.test(selectedItem) && intRegex.test(workFlowId) && intRegex.test(actionTypeId)) {
        if (selectedItem != "000" && workFlowId != "000" && actionTypeId != "000" && workFlowId != "0" && selectedItem != "0") {
            if (actionTypeId == 7 || actionTypeId == 8) {

                $("#ctl00_cphBody_chkActiveDirectory").attr('checked', false);
                $("#ctl00_cphBody_chkActiveDirectory + span .btn-link span.icon").hide();
                $("#ctl00_cphBody_chkActiveDirectory + span .btn-link span.icon.cb-icon-check-empty").show();
                $("#labelSDErrormessage").hide();

                $("#UserName").val('');
                $("#Password").val('');
                $("#pnluserauthon").show();
                $("#loguserbtn").show();
                $("#loguserbtndetach").hide();
                $("#ctl00_cphBody_pnlDomain").hide();
                $('#ctl00_cphBody_chkActiveDirectory').trigger("change");
            } else {
                $("#pnluserauthon").hide();
                AjaxFunction(ajaxUrl, ajaxData, Checkduplicate, OnError);
            }
        }
    }
}

function user_logger(msg) {
    if (msg.d == "success") {
        $("#pnluserauthon").hide();
        var selectedItem = $("#groupAtech option:selected").val();
        var workFlowId = $("#workFlows option:selected").val();
        var actionTypeId = $("#TypeAction option:selected").val();
        //var applicationType = $("#groupType option:selected").val();
        var ajaxUrl = "WorkflowConfiguration.aspx/CheckDuplicate";
        var ajaxData = "{id: " + selectedItem + ",workFlowId:" + workFlowId + ",actionTypeId:" + actionTypeId + "}";
        AjaxFunction(ajaxUrl, ajaxData, Checkduplicate, OnError);
        $("#labelSDErrormessage").hide();
    }
    else if (msg.d == "Error") {
        $("#pnluserauthon").show();
        $("#labelSDErrormessage").show();
        $("#labelSDErrormessage").text("Authentication Failed");
    }
}

function user_logger_detach(msg) {
    //if (msg.d == "success") {
    //    $("#pnluserauthon").hide();

    //    $("#labelSDErrormessage").hide();
    //}
    //else if (msg.d == "Error") {
    //    $("#pnluserauthon").show();
    //    $("#labelSDErrormessage").show();
    //    $("#labelSDErrormessage").val("Authentication Failed");
    //}
    if (msg.d == "success") {
        $("#pnluserauthon").hide();
        var selectedItem = $("#groupAtech option:selected").val();
        var workFlowId = $("#workFlows option:selected").val();
        var actionTypeId = $("#TypeAction option:selected").val();

        var infraObjectName = $("#groupAtech option:selected").text();
        var workFlowName = $("#workFlows option:selected").text();
        var actionTypeName = $("#TypeAction option:selected").text();
        var values = workFlowId + "," + selectedItem + "," + actionTypeId + "," + totalRTO + "," + infraObjectName + "," + workFlowName + "," + actionTypeName;

        //var username = $('[id$=lblLoggedUser]').html();
        var username = $('[id$=lblUserName]').html();

        username = username.replace(/\\/g, '````');
        var ajaxUrl = "WorkflowConfiguration.aspx/DeAttachGroupWorkflow";
        var ajaxData = "{'values':'" + values + "','username':'" + username + "'}";// "{infraObject: " + selectedItem + ",workFlowId:" + workFlowId + ",actionTypeId:" + actionTypeId + ",infraObjectName:" + infraObjectName + ",workFlowName:" + workFlowName + ",actionTypeName:" + actionTypeName + "}";
        if (selectedItem != "000" && workFlowId != "000" && actionTypeId != "000") {
            AjaxFunction(ajaxUrl, ajaxData, DeAttachGroup, OnError);
        }
    }
    else if (msg.d == "Error") {
        $("#pnluserauthon").show();
        $("#labelSDErrormessage").show();
        $("#labelSDErrormessage").text("Authentication Failed");
    }
}

$("#loguserbtn").click(function () {

    var usr_log = $("#UserName").val();
    var usr_pass = $("#Password").val();

    var isAD = $("#ctl00_cphBody_chkActiveDirectory").attr('checked');
    var adDomain = $("#ctl00_cphBody_combobox1").val();

    var ajaxUrl = "WorkflowConfiguration.aspx/CheckLoginCredentail";
    //var ajaxData = "{'username': '" + usr_log + "', 'password':'" + usr_pass + "'}";
    var ajaxData = "{'username': '" + usr_log + "', 'password':'" + usr_pass + "', 'isAD':'" + isAD + "', 'adDomain':'" + adDomain + "'}";
    AjaxFunction(ajaxUrl, ajaxData, user_logger, OnError);

});

$("#loguserbtndetach").click(function () {

    var usr_log = $("#UserName").val();
    var usr_pass = $("#Password").val();

    var isAD = $("#ctl00_cphBody_chkActiveDirectory").attr('checked');
    var adDomain = $("#ctl00_cphBody_combobox1").val();

    var ajaxUrl = "WorkflowConfiguration.aspx/CheckLoginCredentail";
    //var ajaxData = "{'username': '" + usr_log + "', 'password':'" + usr_pass + "'}";
    var ajaxData = "{'username': '" + usr_log + "', 'password':'" + usr_pass + "', 'isAD':'" + isAD + "', 'adDomain':'" + adDomain + "'}";
    AjaxFunction(ajaxUrl, ajaxData, user_logger_detach, OnError);


});

$("#logusercancel").click(function () {
    $("#pnluserauthon").hide();
});

function closeModelPopupActionGroup(win) {
    var grId = $("#groupAtech option:selected").val();
    var workFlowId = $("#workFlows option:selected").val();
    var actionTypeId = $("#TypeAction option:selected").val();

    var infraObjectName = $("#groupAtech option:selected").text();
    var workFlowName = $("#workFlows option:selected").text();
    var actionTypeName = $("#TypeAction option:selected").text();

    var selectedinfralist = getIsRTOUpdateCheckedInfraList();

    var values = workFlowId + "," + grId + "," + actionTypeId + "," + totalRTO + "," + infraObjectName + "," + workFlowName + "," + actionTypeName + "," + selectedinfralist;
    var ajaxUrl = "WorkflowConfiguration.aspx/SaveInfraObjectWorkflow";
    var ajaxData = "{'values':'" + values + "'}";
    AjaxFunction(ajaxUrl, ajaxData, WorkflowGroup, OnError);
    CloseModel(win);
}

function WorkflowGroup(msg) {
    if (msg.d == "Success") {
        $("#groupAtech").empty();
        $("#workFlows").empty();
        $("#TypeAction").empty();
        CloseModel(formClose);
        //  OpenAlertModelAlert("Infraobject Record Attached Successfully !!!");
        OpenAlertModelAlert("Workflow Attached To Infraobject Successfully !!!");
        formClose = "";
    }
    else if (msg.d == "Update") {
        $("#groupAtech").empty();
        $("#workFlows").empty();
        $("#TypeAction").empty();
        //OpenAlertModelAlert("Attached Infraobject Record Updated Successfully !!!");
        CloseModel(formClose);
        OpenAlertModelAlert("Workflow Attached To Infraobject Updated Successfully !!!");
        formClose = "";
    }
    else if (msg.d == "error") {
        OpenAlertModelAlert("Workflow Not Attached To Infraobject try again !!!");
    }
}

function MapWorkFlowGroup(msg) {
    var result = msg.d.split(":");
    var trValue = "";
    var inc = "";
    for (var i = 0 ; i < result.length ; i++) {
        inc = i + 1;
        trValue = "<tr> <td>" +
                  "<input type='checkbox'class='chk' name='chk" + result[inc] + "' id='" + result[inc] + "' /><label for='chk" + result[inc] + "'>" + result[i] + "</label> " +
                  " </td> </tr>";
        $("#tblwf tbody").append(trValue);
        i++;
    }
}

function GetAllInfraObject(msg) {
    msg.d = GetOriginalResponse(msg.d);
    $("#groupType").val(1);
    var result = msg.d.split(",");
    var text = "-Select Infraobject-";
    var value = "000";
    var chkValue = "";
    $("select[id$=groupAtech] > option").remove();
    AppendOption('groupAtech', value, text);
    for (var i = 0; i < result.length; i++) {
        chkValue = result[i].split(":");
        text = chkValue[0];
        value = chkValue[1];
        $("#groupAtech").append("<option value='" + value + "'>" + text + "</option>");


    }
    $("#groupAtech").chosen({ search_contains: true });
    $("#groupAtech").trigger("chosen:updated");
    $('input[type="checkbox"]').checkbox();
}

function GetAllWorkFlows(msg) {
    msg.d = GetOriginalResponse(msg.d);
    var result = msg.d.split(",");
    var text = "-Select workFlow-";
    var value = "000";
    var chkValue = "";
    $("select[id$=workFlows] > option").remove();
    if (result == "") {
        AppendOption('workFlows', value, text);
    }
    else {
        AppendOption('workFlows', value, text);
        for (var i = 0; i < result.length; i++) {
            chkValue = result[i].split(":");
            text = chkValue[0];
            value = chkValue[1];
            $("#workFlows").append("<option value='" + value + "'>" + text + "</option>");
        }
    }
    $("#workFlows").chosen({ search_contains: true });
    $("#workFlows").trigger("chosen:updated");

    $(".chosen-select").trigger("chosen:updated");
}

function GetTypeAction(msg) {
    var data = msg.d;
    var text = "-Select Action Type-";
    var value = "000";
    // var chkValue="";
    AppendOption('TypeAction', value, text);
    for (var i = 0; i < data.length; i++) {
        value = data[i].WorkflowTypeId;
        text = data[i].Action;
        $("#TypeAction").append("<option value='" + value + "'>" + text + "</option>");
    }
}

function GetActionTypeForManageGroup(msg) {
    var data = msg.d;
    var text = "-Select Action Type-";
    var value = "000";
    //var chkValue="";
    AppendOption('TypeActionForManageGrp', value, text);
    for (var i = 0; i < data.length; i++) {
        value = data[i].ID;
        text = data[i].Action;
        $("#TypeActionForManageGrp").append("<option value='" + value + "'>" + text + "</option>");
    }
}

//  $("#groupAtech").live("change",function(){
//	var value = $("#groupAtech option:selected").val();
//	var ajaxUrl = "WorkflowConfiguration.aspx/GetWorkflowByGroup";
//	var ajaxData ="{'values':'" + value + "'}";
//	AjaxFunction(ajaxUrl, ajaxData,GetWorkflowByGroup , OnError);
//   });
//
//    function GetWorkflowByGroup(msg){
//	var ids = msg.d.split(':');
//	var isPresent = "";
//	$("input:checkbox").each(function(){
//		isPresent = jQuery.inArray($(this).attr("id"), ids);
//		if (isPresent > -1)
//			$(this).attr("checked", true);
//		else
//			$(this).attr("checked", false);
//	});
//}

$("#xmlExport").click(function () {
    var ajaxUrl = "WorkflowConfiguration.aspx/SaveXml";
    var workflowId = GlobalWorkFlowID;
    var ajaxData = "{'workflowId':'" + workflowId + "'}";
    if (workflowId != "") {
        AjaxFunction(ajaxUrl, ajaxData, Exported, OnError);
    }
    else {
        alert("Choose Workflow");
    }
});

var Exported = function (msg) {
    alert(msg.d);
};

$("#runBook").click(function () {
    var ajaxUrl = "WorkflowConfiguration.aspx/GetAllActionsParameterOfSelectedWorkflow";
    var id = GlobalWorkFlowID;
    var ajaxData = "{'id':'" + id + "'}";
    if (id != "") {
        AjaxFunction(ajaxUrl, ajaxData, ExportReport, OnError);
    }
    else {
        alert("First Load Workflow");
    }
});

var ExportReport = function (data) {
    var result = data.d;
    if (result != "error") {
        var fullUrl = result.split("`");
        var serverPath = getpath();
        window.open(serverPath + "ExcelFiles/" + fullUrl[0], '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=DataLog Report');
    }
    else {
        alert("Error while generate runnbook");
    }
    //	var tableValue = result.split("^");
    //	var trValue = tableValue[1].split("#");
    //	var tdhtml = "";
    //	var trHtml = "";
    //	for (var i = 0; i < trValue.length; i++)
    //	{
    //		var tdValue = trValue[i].split("*");
    //		for(var j = 0; j < tdValue.length; j++)
    //		{
    //
    //			tdhtml =tdhtml+ "<td style= 'border:1px solid grey; padding-bottom:5px; padding-top:5px;'>" + tdValue[j] + "</td>";
    //		}
    //		trHtml=trHtml+"<tr style='border:1px solid grey; padding-bottom:5px; padding-top:5px;'>"+tdhtml+"</tr>";
    //		tdhtml = "";
    //	}
    //OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> Run Book Generated For Workflow: <b class='active'>"+tableValue[0]+"</b>.</div>");
    //OpenModelPopupForRunBook("<div class='margin-bottom8' style='line-height:1.5em;'></div> <div class='active' style='margin-top:-20px;'>"+tableValue[0]+"</div></br><div><table style='border:1px solid grey; padding-bottom:5px; padding-top:5px;'>"+trHtml+"</table></div>");
};
function getpath() {
    var loc = window.location;
    var str = loc.pathname;
    var pathName = str.substring(0, str.lastIndexOf('/'));
    var finalPath = pathName.substring(0, pathName.lastIndexOf('/') + 1);
    return finalPath;
}

//$("#groupType").live("change", function () {
//    var ajaxUrlgroup;
//    var ajaxDatagroup;
//    var groupType = $("#groupType option:selected").val();
//    if (groupType == 2) {
//        ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllApplicationGroup";
//        ajaxDatagroup = "{}";
//        AjaxFunction(ajaxUrlgroup, ajaxDatagroup, getAllApplicationGroup, OnError);
//    } else if (groupType == 1) {
//        ajaxUrlgroup = "WorkflowConfiguration.aspx/GetAllInfraObject";
//        ajaxDatagroup = "{}";
//        AjaxFunction(ajaxUrlgroup, ajaxDatagroup, GetAllInfraObject, OnError);
//    }
//});

function getAllApplicationGroup(msg) {
    var result = msg.d.split(",");
    var text = "-Select Application-";
    var value = "000";
    $("select[id$=groupAtech] > option").remove();
    AppendOption('groupAtech', value, text);
    for (var i = 0; i < result.length; i++) {
        var selectedValue = result[i].split(":");
        text = selectedValue[0];
        value = selectedValue[1];
        $("#groupAtech").append("<option value='" + value + "'>" + text + "</option>");
    }
}