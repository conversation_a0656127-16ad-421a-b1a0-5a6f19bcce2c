﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="DiscoveryConfiguration.aspx.cs" Inherits="CP.UI.SNMP.DiscoveryConfigurationForm" %>

<%@ Register Assembly="Telerik.Web.UI" Namespace="Telerik.Web.UI" TagPrefix="telerik" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
      <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
         
        Discovery Configuration</h3>
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">

                    <div class="col-md-6">
                        <label class="margin-right">IP Range From</label>
                        <span class="ip-icon"></span>
                        <asp:TextBox ID="txtIPAddressFrom" runat="server" class="form-control" Placeholder="IP Address From"></asp:TextBox>
                       
                    </div>
                    <div class="col-md-6">
                        <label class="margin-right">IP Range To</label>
                        <span class="ip-icon"></span>
                         <asp:TextBox ID="txtIPAddressTo" runat="server" class="form-control"></asp:TextBox>
                       
                     
                    </div>
                </div>

                <hr class="separator" />
                <div class="clearfix"></div>
                <div class="form-actions row">
                    <div class="col-xs-8">
                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                            Fields</span>
                        <asp:Label ID="lblupdatestatus" runat="server" Text=""></asp:Label>
                    </div>
                    <div class="col-xs-4">
                        <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="22.5%" runat="server"
                            Text="Save" TabIndex="11" OnClick="btnSave_Click"></asp:Button>
                        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="22.5%" runat="server"
                            Text="Cancel" CausesValidation="False" TabIndex="12" />
                    </div>

                </div>
            </div>
        </div>
    </div>
</asp:Content>
