﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Common.Shared;
using CP.UI.Controls;
using CP.Helper;

namespace CP.UI.SNMP
{
    public partial class APMSetting : BasePage
    {
        #region Variables

        static IFacade _facade = new Facade();

        public string ADMSettingText
        {
            get
            {
                return (string)ViewState["ADMSettingText"];
            }
            set
            {
                ViewState["ADMSettingText"] = value;
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Page load event
        /// </summary>
        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            if (IsUserCustom)
            {
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.FirstOrDefault(x => x.AccessMenuType == AccessManagerType.Configuration.ToString());
                    if (ObjAccess == null)
                    {
                        Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                        return;
                    }
                }
                else
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }

            }
            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 
        }

        #endregion

        /// <summary>
        /// ddlType SelectedIndexChanged event
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-28/01/2015</author>
        protected void ddlType_SelectedIndexChanged(object sender, EventArgs e)
        {

            AppDepMappingSettings appDepMapSettings = _facade.GetApplicationDependencyMappingSettings(ddlType.SelectedItem.Text.ToString());
            lblSaveMsg.Text = "";
            if (appDepMapSettings != null)
            {
                if (ddlType.SelectedIndex > 0)
                {
                    if (ddlType.SelectedIndex == 1)
                    {
                        pnlpath.Visible = true;
                        pnlsnmp.Visible = false;
                        lblSaveMsg.Visible = false;
                        txtLocation.Text = "";
                        rfvtxtSnmpCommunity.Enabled = false;
                        rfvtxtLocation.Enabled = true;
                        txtLocation.Text = appDepMapSettings.ThirdPartyPath;
                        ADMSettingText = appDepMapSettings.ThirdPartyPath;
                    }
                    else if (ddlType.SelectedIndex == 2)
                    {
                        pnlpath.Visible = false;
                        pnlsnmp.Visible = true;
                        lblSaveMsg.Visible = false;
                        txtSnmpCommunity.Text = "";
                        rfvtxtSnmpCommunity.Enabled = true;
                        rfvtxtLocation.Enabled = false;
                        txtSnmpCommunity.Text = appDepMapSettings.SNMPCommunity;
                        ADMSettingText = appDepMapSettings.SNMPCommunity;
                    }
                }
                else
                {

                    pnlpath.Visible = false;
                    pnlsnmp.Visible = false;
                    lblSaveMsg.Visible = false;
                    txtLocation.Text = "";
                    rfvtxtSnmpCommunity.Enabled = true;
                    rfvtxtLocation.Enabled = false;
                }
            }
            else
            {
                if (ddlType.SelectedIndex == 1)
                {
                    pnlpath.Visible = true;
                    txtLocation.Text = "";
                    rfvtxtLocation.Enabled = true;
                    pnlsnmp.Visible = false;
                    rfvtxtSnmpCommunity.Enabled = false;
                }
                else if (ddlType.SelectedIndex == 2)
                {
                    pnlsnmp.Visible = true;
                    txtSnmpCommunity.Text = "";
                    rfvtxtSnmpCommunity.Enabled = true;
                    pnlpath.Visible = false;
                    rfvtxtLocation.Enabled = false;
                }
                else
                {
                    pnlpath.Visible = false;
                    pnlsnmp.Visible = false;
                    lblSaveMsg.Visible = false;
                    txtLocation.Text = "";
                    txtSnmpCommunity.Text = "";
                    rfvtxtLocation.Enabled = false;
                    rfvtxtSnmpCommunity.Enabled = false;
                }
            }
        }

        /// <summary>
        /// Save AppDepMappingSettings data to database table "appdep_mappingSettings"
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-28/01/2015</author>
        protected void btnSave_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("ADMSetting", UserActionType.CreateADMSetting))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);
                }

                else
                {
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;

                    if (ddlType.SelectedIndex > 0)
                    {
                        try
                        {
                            if (ddlType.SelectedIndex == 1)
                            {
                                AppDepMappingSettings appDepMappingSettings = new AppDepMappingSettings();
                                appDepMappingSettings.ProviderName = ddlType.SelectedItem.Text;
                                appDepMappingSettings.ThirdPartyPath = txtLocation.Text;
                                appDepMappingSettings.SNMPCommunity = null;

                                if (appDepMappingSettings != null)
                                {
                                    var appDepMapSettings = _facade.AddOrUpdateApplicationDependencyMappingSettings(appDepMappingSettings);
                                    if (!string.IsNullOrEmpty(ADMSettingText))
                                    {
                                        lblSaveMsg.Text = "Updated successfully.";
                                       
                                    }
                                    if (appDepMapSettings != null)
                                        lblSaveMsg.Visible = true;
                                    else
                                        lblSaveMsg.Visible = false;
                                  
                                }
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Third-Party", TransactionType.Save));
                            }
                            else if (ddlType.SelectedIndex == 2)
                            {
                                AppDepMappingSettings appDepMappingSettings = new AppDepMappingSettings();
                                appDepMappingSettings.ProviderName = ddlType.SelectedItem.Text;
                                appDepMappingSettings.ThirdPartyPath = null;
                                appDepMappingSettings.SNMPCommunity = txtSnmpCommunity.Text;

                                if (appDepMappingSettings != null)
                                {
                                    var appDepMapSettings = _facade.AddOrUpdateApplicationDependencyMappingSettings(appDepMappingSettings);
                                    if (!string.IsNullOrEmpty(ADMSettingText))
                                    {
                                        lblSaveMsg.Text = "Updated successfully.";
                                    }
                                   
                                    if (appDepMapSettings != null)
                                        lblSaveMsg.Visible = true;
                                    else
                                        lblSaveMsg.Visible = false;
                                   
                                }
                               ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("SNMP", TransactionType.Save));
                            }
                        }
                        catch (CpException exc)
                        {
                            ExceptionManager.Manage(exc);
                        }
                        catch (Exception ex)
                        {
                            var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while saving application dependency mapping data", ex);
                            ExceptionManager.Manage(cpException);
                        }
                        Response.Redirect("~/SNMP/ADMSetting.aspx", false);
                    }
                }
            }
        }

        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request

        #region Methods
        #endregion

    }
}