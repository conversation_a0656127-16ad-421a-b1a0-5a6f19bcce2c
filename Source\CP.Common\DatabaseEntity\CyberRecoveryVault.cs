﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "CyberRecoveryVault", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class CyberRecoveryVault : BaseEntity
    {

        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables



        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string CyberRecoveryPolicyName { get; set; }
         
    
        [DataMember]
        public string CreateDate { get; set; }



        [DataMember]
        public string UpdateDate { get; set; }



        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }



        #endregion Properties



    }
}
