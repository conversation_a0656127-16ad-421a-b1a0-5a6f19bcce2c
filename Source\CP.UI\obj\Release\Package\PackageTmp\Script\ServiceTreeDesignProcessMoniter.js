﻿
// Starts =====================
var globalstring = null;
//var globalJSONObj = null;
var JsonForFullView = null;
var isbtnReloadHide = false;
var level = 0;
var lastRightClickNode = null;
var newJSON = "";
var apptype = "";
var isChange = false;
var isNewProfile = false;
var clickNewService = false;
var IsUpdateGroupService = false;
var GroupServiceID = "";
var GroupProfileID = "";
var BSIndexCount = 1;
var IsDeletedBsIndex = [];

//Render first full view tree
function getJSONByServiceId(businessid) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'businessid': businessid }),
        url: "../Admin/DesignProcessMoniter.aspx/LoadServiceById",
        success: function (msg) {
            var value = msg.d.split('#');
            globalstring = value[1];
            var JsonObj = eval('(' + value[0] + ')');
            JsonForFullView = JsonObj;
            treeShow(JsonObj);
        }
    });
}

//Renders second infraobject components tree
function renderAjaxDataForInfraObjects(infraObjectNodeRelation) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'infraObjectNodeRelation': infraObjectNodeRelation }),
        url: "../Admin/DesignProcessMoniter.aspx/NodeRelationConverterForInfraObjects",
        success: function (msg) {
            var JsonObj = eval('(' + msg.d + ')');
            treeShow(JsonObj);
        }
    });
}


function getNewNodeJSON(businessid, nodeName) {
    newJSON = "";
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        data: JSON.stringify({ 'businessid': businessid, 'nodeName': nodeName }),
        url: "../Admin/DesignProcessMoniter.aspx/CreateNewNodeJSON",
        success: function (msg) {
            var value = msg.d.split('#');
            newJSON = value[1];
        }
    });
}
//draw BIT daigram
function treeShow(root1) {
    d3.select("svg").remove();
    i = 0, root;
    var duration = 750;
    var viewerWidth = 1035;
    var viewerHeight = 800;
    //Define the zoom function for the zoomable tree

    function zoom() {
        vis.attr("transform", "translate(" + d3.event.translate + ")scale(" + d3.event.scale + ")");
    }

    // Function to reset Zoom level
    function resetZoom() {
        d3.select("#bsbody3").select("svg").select("g")
       .transition().duration(750)
       .attr("transform", root1.name.length < 6 ? "translate(30,30)scale(0.85)" : "translate(90,30)scale(0.85)");
    };
    d3.select("#btnReset").on("click", resetZoom);

    // Function to center node when clicked/dropped so node doesn't get lost when collapsing/moving with large amount of children.
    function centerNode(source) {
        scale = zoomListener.scale();
        x = -source.y0;
        y = -source.x0;
        x = 0;
        //y = y * scale + viewerHeight / 2; // Commented - Kuntesh Thakker - 7-04-2014 as it disturbs the RTO RPO graph design
        y = 0;
        d3.select('g').transition()
        .duration(duration)
        .attr("transform", "translate(" + x + "," + y + ")scale(" + scale + ")");
        zoomListener.scale(scale);
        zoomListener.translate([x, y]);
    }

    // define the zoomListener which calls the zoom function on the "zoom" event constrained within the scaleExtents
    var zoomListener = d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom);

    var tree = d3.layout.tree()
    .size([viewerHeight, viewerWidth]);

    var diagonal = d3.svg.diagonal()
    .projection(function (d) {
        return [d.y, d.x];
    });

    var vis = d3.select("#bsbody3").append("svg:svg")
    .attr("width", viewerWidth)
    .attr("height", viewerHeight)
    .call(d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom)).on("dblclick.zoom", null)
    .append("g")
    //zoomListener.translate([100, 50]).scale(0.85);
    .attr("transform", root1.name.length < 6 ? "translate(30,30)scale(0.85)" : "translate(90,30)scale(0.85)");

    var root = root1;
    root.x0 = viewerHeight / 2;
    root.y0 = 0;

    //toggle node
    function toggleAll(d) {
        if (d.children) {
            d.children.forEach(toggleAll);
            toggleOnLoad(d);
        }
    }
    //Initialize the display to show a few nodes.
    root.children.forEach(toggleAll);
    update(root);
    //centerNode(root);

    //Update node
    function update(source) {
        var duration = d3.event && d3.event.altKey ? 5000 : 500;

        // Compute the new tree layout.
        var nodes = tree.nodes(root).reverse();

        // Normalize for fixed-depth.
        nodes.forEach(function (d) { d.y = d.depth * 180; });

        //var y = d3.scale.sqrt().range([0, 15]);
        //nodes.forEach(function (d) { d.x = y(d.x); });

        // Update the nodes…
        var node = vis.selectAll("g.node")
        .data(nodes, function (d) { return d.id || (d.id = ++i); });




        // Enter any new nodes at the parent's previous position.
        var nodeEnter = node.enter().append("svg:g")
        //.attr("opacity", function (d) { return hidenodeonload1(d.name, d.ImpactType); })
        .attr("class", "node")
        .attr("transform", function (d) { return "translate(" + source.y0 + "," + source.x0 + ")"; })
        .on("click", function (d) { GetJsonFromNodeRelation(d); toggleOnClick(d); update(d); });


        nodeEnter.append("svg:circle")
        .attr("r", 1e-6)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        //if (level == 0) {
        nodeEnter.append("svg:text")
       .attr("x", function (d) { return returnx(d); })
       .attr("dy", function (d) { return returny(d); })
       .attr("text-anchor", function (d) { return returnTextAnchor(d); })
       .attr("class", function (d) { return returnTextAnchorClass(d.name, d.ImpactType); })
       .text(function (d) {
           if (d.name == undefined)
               return "";
           else {
               if (d.name.indexOf("$") != -1)
                   return d.name.split("$")[1];

               else
                   return d.name.substring(d.name.substring(d.name.lastIndexOf('@') + 1));

           }
       })
       .style("fill-opacity", 1e-6);

        nodeEnter.append("svg:image")
                .attr("xlink:href", function (d) {
                    //if (d.logo == "") {
                    if (d.name != undefined) {

                        var iscap = d.name.substring(d.name.lastIndexOf('@') + 1, d.name.lastIndexOf('$'))
                        if (iscap.indexOf("_") >= 0) {
                            apppre = iscap.split("_")[0] + "_";
                            if (apppre.indexOf("^") >= 0) {
                                apppre = apppre.split('^')[1];
                            }
                        }

                        if (apppre.indexOf("IS") >= 0)
                            return GetNodeImgPathByName("IS", d.ImpactType);
                        else if (apppre.indexOf("F_") >= 0)
                            return GetNodeImgPathByName("F", d.ImpactType);
                        else if (apppre.indexOf("I_") >= 0)
                            return GetNodeImgPathByName("I", d.ImpactType);
                        else if (apppre.indexOf("S_") >= 0)
                            return GetNodeImgPathByName("S", d.ImpactType);
                        else if (apppre.indexOf("ICSV_") >= 0)
                            return GetNodeImgPathByName("ICSV", d.ImpactType);
                        else if (apppre.indexOf("ICD_") >= 0)
                            return GetNodeImgPathByName("ICD", d.ImpactType);
                        else if (apppre.indexOf("ICR_") >= 0)
                            return GetNodeImgPathByName("ICR", d.ImpactType);
                        else if (apppre.indexOf("ICSM_") >= 0)
                            return GetNodeImgPathByName("ICSM", d.ImpactType);
                        else if (apppre.indexOf("ICSS_") >= 0)
                            return GetNodeImgPathByName("ICSS", d.ImpactType);
                        else if (apppre.indexOf("Q_") >= 0)
                            return GetNodeImgPathByName("Q", d.ImpactType);
                        else if (apppre.indexOf("NQ_") >= 0)
                            return GetNodeImgPathByName("NQ", d.ImpactType);
                        else
                            return GetNodeImgPathByName("");

                    }
                    //}
                    //else
                    //    return d.logo;

                })
                .attr("x", function (d) { return d.children || d._children ? -6 : -6; })
                //.attr("y", function (d) { return d.children || d._children ? "-0.9em" : "-0.9em"; })
                .attr("y", function (d) { return d.children || d._children ? GetRadius(d.name, d.ImpactType) : GetRadius(d.name, d.ImpactType); }) // d._children ? "-0.5em" : "-0.5em"; })
                .attr("height", function (d) {
                    return d.logoheight || GetNodeImgHightByName(d.name, d.ImpactType);
                })
                .attr("width", function (d) {
                    return d.logowidth || GetNodeImgWidthByName(d.name, d.ImpactType);
                })
                .on("contextmenu", function (d, nodes, links) {
                    apptype = "";

                    lastRightClickNode = d;
                    var linkToPage = "";
                    var Ismenu = false;
                    linkToPage = "../ImpactAnalysis/ImpactConfiguration.aspx";
                    if (d.name != "") {
                        if (d.name.indexOf('@') >= 0) {
                            var Apptype = d.name.substring(d.name.lastIndexOf('@') + 1)
                            if (Apptype.split('_')[0] == "S")
                                apptype = "CS";
                            else if (Apptype.split('_')[0] == "F")
                                apptype = "F";
                        }
                        else {
                            if (d.name.indexOf("^") >= 0) {
                                if (d.name.split('$')[0].split('_')[0].split('^')[1] == "S")
                                    apptype = "RS";
                                else if (d.name.split('$')[0].split('_')[0] == "F")
                                    apptype = "F";
                            }
                            else {
                                if (d.name.split('$')[0].split('_')[0] == "S")
                                    apptype = "RS";
                                else if (d.name.split('$')[0].split('_')[0] == "F")
                                    apptype = "F";
                            }

                        }
                    }


                    if (apptype == "RS" && apptype.lastIndexOf("RS") >= 0) {

                        context.attach(("svg image"),
                        [
                             {
                                 text: "Add Service", action: function (e) {
                                     clickNewService = false;
                                     openAddServicePopup(d.name);

                                 }
                             }

                        ]);


                        $('[id$=bsbody]').on("contextmenu", true);

                    }
                    else if (apptype == "CS" && apptype.lastIndexOf("CS") >= 0) {

                        context.attach(("svg image"),
                        [
                             {
                                 text: "Add Service", action: function (e) {
                                     clickNewService = false;
                                     openAddServicePopup(d.name);

                                 }

                             },
                             //{
                             //    text: "Add/Update Impact ", href: "javascript:void(0);", action: function (e) {

                             //        var FinalNodeValue = lastRightClickNode.name;
                             //        var lastRightClickNode_ParentId;
                             //        var IDChildNameArr;
                             //        var lastRightClickNodeId;
                             //        var RightClickChildAppType;
                             //        if (isEmpty(FinalNodeValue)) {
                             //            var charCount = count_Char(FinalNodeValue, '@');

                             //            if (charCount > 0) {
                             //                var strChildresult = FinalNodeValue.substring(FinalNodeValue.lastIndexOf('@') + 1);
                             //                var strParentresult2 = FinalNodeValue.split('@');
                             //                if (strParentresult2.length > 0) {
                             //                    for (var i = 0; i < strParentresult2.length; i++) {
                             //                        var index = strParentresult2[i];
                             //                        if (isEmpty(index)) {
                             //                            var cCount = count_Char(index, '_');
                             //                            if (cCount > 0) {
                             //                                lastRightClickNode_ParentId = index.split('_')[1];
                             //                                break;
                             //                            }

                             //                        }

                             //                    }
                             //                }
                             //                var strchildArray = strChildresult.split('$');

                             //                if (strchildArray.length > 0) {
                             //                    for (var i = 0; i < strchildArray.length; i++) {
                             //                        var indexVal = strchildArray[i];
                             //                        if (isEmpty(indexVal)) {
                             //                            var cCount = count_Char(index, '_');
                             //                            if (cCount > 0) {
                             //                                IDChildNameArr = indexVal.split('_');
                             //                                if (IDChildNameArr.length > 0) {
                             //                                    lastRightClickNodeId = IDChildNameArr[1];
                             //                                    RightClickChildAppType = IDChildNameArr[0];

                             //                                    break;
                             //                                }
                             //                            }
                             //                        }
                             //                    }

                             //                }
                             //                if (RightClickChildAppType == 'S') {
                             //                    openRadWindow(linkToPage + "?" + "RuleType=BStoBs" + "&" + "ParentBsId=" + lastRightClickNode_ParentId + "&" + "ChildBSId=" + lastRightClickNodeId + "&" + "GroupServiceId=" + GroupServiceID);
                             //                }
                             //                if (RightClickChildAppType == 'I') {

                             //                    //var ParentInfraPRorDRNode = lastRightClickNode.parent;
                             //                    //var ParentInfraNode = lastRightClickNode.parent.parent;
                             //                    //var ParentBFNode = lastRightClickNode.parent.parent.parent;
                             //                    //var ParentBsNode = lastRightClickNode.parent.parent.parent.parent;
                             //                    //var INfraCompId; var INfraObjectId; var BfId; var BSId; var IPComp;
                             //                    //if (isEmpty(ParentInfraPRorDRNode.name)) {
                             //                    //    IPComp = GetInfraComponent(lastRightClickNode.name);
                             //                    //    INfraCompId = GetInfraCompId(ParentInfraPRorDRNode.name);
                             //                    //    INfraObjectId = GetInfraObjectId(ParentInfraNode.name);
                             //                    //    BfId = GetBusinessBFId(ParentBFNode.name);
                             //                    //    BSId = GetBusinessBSId(ParentBsNode.name);
                             //                    //}

                             //                    var lstNode = d;
                             //                    var parentbfId = d.parent.name.split('$')[0].split('_')[1];
                             //                    var parentbsId = d.parent.parent.name.split('$')[0].split('_')[1];
                             //                    var infrsId = d.name.split('$')[0].split('_')[1];
                             //                    var infraChildIds = "";
                             //                    var IPComp = "";
                             //                    for (var i = 0; i < d.children.length; i++) {
                             //                        infraChildIds += d.children[i].name.split('$')[1] + ":" + d.children[i].name.split('$')[0].split('_')[1] + ";";
                             //                    }

                             //                    //openInfratoBSRadWindow(linkToPage + "?" + "INfraCompId=" + INfraCompId + "&" + "INfraObjectId=" + INfraObjectId + "&" + "BFId=" + BfId + "&" + "BSId=" + BSId + "&" + "IPComp=" + IPComp + "&" + "GroupServiceId=" + GroupServiceID);

                             //                    openInfratoBSRadWindow(linkToPage + "?" + "INfraCompId=" + infraChildIds + "&" + "INfraObjectId=" + infrsId + "&" + "BFId=" + parentbfId + "&" + "BSId=" + parentbsId + "&" + "IPComp=" + IPComp + "&" + "GroupServiceId=" + GroupServiceID);



                             //                }

                             //            }

                             //        }


                             //    }
                             //},
                             {
                                 text: "Delete Service", href: "javascript:void(0);", action: function (e) {
                                     deleteServiceNode();
                                 }
                             }
                        ]);


                        $('[id$=bsbody]').on("contextmenu", true);

                    }
                    else if (apptype == "F" && apptype.lastIndexOf("F") >= 0) {
                        context.destroy(("svg image"));

                    }


                    //else {

                    //    // context.destroy(("svg image"));
                    //    context.attach(("svg image"),
                    //   [
                    //        {
                    //            text: "Add/Update Impact ", href: "javascript:void(0);", action: function (e) {

                    //                var FinalNodeValue = lastRightClickNode.name;
                    //                var lastRightClickNode_ParentId;
                    //                var IDChildNameArr;
                    //                var lastRightClickNodeId;
                    //                var RightClickChildAppType;
                    //                if (isEmpty(FinalNodeValue)) {
                    //                    var charCount = count_Char(FinalNodeValue, '@');

                    //                    if (charCount > 0) {
                    //                        var strChildresult = FinalNodeValue.substring(FinalNodeValue.lastIndexOf('@') + 1);
                    //                        var strParentresult2 = FinalNodeValue.split('@');
                    //                        if (strParentresult2.length > 0) {
                    //                            for (var i = 0; i < strParentresult2.length; i++) {
                    //                                var index = strParentresult2[i];
                    //                                if (isEmpty(index)) {
                    //                                    var cCount = count_Char(index, '_');
                    //                                    if (cCount > 0) {
                    //                                        lastRightClickNode_ParentId = index.split('_')[1];
                    //                                        break;
                    //                                    }

                    //                                }

                    //                            }
                    //                        }
                    //                        var strchildArray = strChildresult.split('$');

                    //                        if (strchildArray.length > 0) {
                    //                            for (var i = 0; i < strchildArray.length; i++) {
                    //                                var indexVal = strchildArray[i];
                    //                                if (isEmpty(indexVal)) {
                    //                                    var cCount = count_Char(index, '_');
                    //                                    if (cCount > 0) {
                    //                                        IDChildNameArr = indexVal.split('_');
                    //                                        if (IDChildNameArr.length > 0) {
                    //                                            lastRightClickNodeId = IDChildNameArr[1];
                    //                                            RightClickChildAppType = IDChildNameArr[0];

                    //                                            break;
                    //                                        }
                    //                                    }
                    //                                }
                    //                            }

                    //                        }
                    //                        if (RightClickChildAppType == 'S') {
                    //                            openRadWindow(linkToPage + "?" + "RuleType=BStoBs" + "&" + "ParentBsId=" + lastRightClickNode_ParentId + "&" + "ChildBSId=" + lastRightClickNodeId + "&" + "GroupServiceId=" + GroupServiceID);
                    //                        }
                    //                        if (RightClickChildAppType == 'I') {
                    //                            //var ParentInfraPRorDRNode = lastRightClickNode.parent;
                    //                            //var ParentInfraNode = lastRightClickNode.parent.parent;
                    //                            //var ParentBFNode = lastRightClickNode.parent.parent.parent;
                    //                            //var ParentBsNode = lastRightClickNode.parent.parent.parent.parent;
                    //                            //var INfraCompId; var INfraObjectId; var BfId; var BSId; var IPComp;
                    //                            //if (isEmpty(ParentInfraPRorDRNode.name)) {
                    //                            //    IPComp = GetInfraComponent(lastRightClickNode.name);
                    //                            //    INfraCompId = GetInfraCompId(ParentInfraPRorDRNode.name);
                    //                            //    INfraObjectId = GetInfraObjectId(ParentInfraNode.name);
                    //                            //    BfId = GetBusinessBFId(ParentBFNode.name);
                    //                            //    BSId = GetBusinessBSId(ParentBsNode.name);
                    //                            //}

                    //                            var lstNode = d;
                    //                            var parentbfId = d.parent.name.split('$')[0].split('_')[1];
                    //                            var parentbsId = d.parent.parent.name.split('$')[0].split('_')[1];
                    //                            var infrsId = d.name.split('$')[0].split('_')[1];
                    //                            var infraChildIds = "";
                    //                            var IPComp = "";
                    //                            for (var i = 0; i < d.children.length; i++) {
                    //                                infraChildIds += d.children[i].name.split('$')[1] + ":" + d.children[i].name.split('$')[0].split('_')[1] + ";";
                    //                            }

                    //                            //openInfratoBSRadWindow(linkToPage + "?" + "INfraCompId=" + INfraCompId + "&" + "INfraObjectId=" + INfraObjectId + "&" + "BFId=" + BfId + "&" + "BSId=" + BSId + "&" + "IPComp=" + IPComp + "&" + "GroupServiceId=" + GroupServiceID);

                    //                            openInfratoBSRadWindow(linkToPage + "?" + "INfraCompId=" + infraChildIds + "&" + "INfraObjectId=" + infrsId + "&" + "BFId=" + parentbfId + "&" + "BSId=" + parentbsId + "&" + "IPComp=" + IPComp + "&" + "GroupServiceId=" + GroupServiceID);



                    //                        }

                    //                    }

                    //                }


                    //            }
                    //        }
                    //   ]);
                    //}


                });



        // Transition nodes to their new position.
        var nodeUpdate = node.transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + d.y + "," + d.x + ")"; });

        nodeUpdate.select("circle")
        .attr("r", 0)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeUpdate.select("text")
        .style("fill-opacity", 1);

        // Transition exiting nodes to the parent's new position.
        var nodeExit = node.exit().transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + source.y + "," + source.x + ")"; })
        .remove();

        nodeExit.select("circle")
        .attr("r", 1e-6);

        nodeExit.select("text")
        .style("fill-opacity", 1e-6);

        // Update the links…
        var link = vis.selectAll("path.link")
        .data(tree.links(nodes), function (d) { return d.target.id; });

        // Enter any new links at the parent's previous position.
        link.enter().insert("svg:path", "g")


        //.attr("class", function (d) { return linkColor(d); })


        .attr("class", "link")
        //.attr("class", function (d) { return linkColor(d); })
        //.attr("opacity", function (d) { return hideLinkload(d); })



        .style("stroke", function (d) { return linkColor(d); })
        .attr("d", function (d) {
            var o = { x: source.x0, y: source.y0 };
            return diagonal({ source: o, target: o });
        })
        .transition()
        .duration(duration)
        .attr("d", diagonal);




        // Transition links to their new position.
        link.transition()
        .duration(duration)
        .attr("d", diagonal);

        // Transition exiting nodes to the parent's new position.
        link.exit().transition()
        .duration(duration)
        .attr("d", function (d) {
            var o = { x: source.x, y: source.y };
            return diagonal({ source: o, target: o });
        })
        .remove();

        // Stash the old positions for transition.
        nodes.forEach(function (d) {
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }

    //Hide ContextMenu according to tree Logic

    function ContextMenuStatus(ContextMenuName, AppType, d) {
        switch (ContextMenuName) {
            case "Add Service":

                return true;
                break;
            case "Add/Update Impact":
                return false;
                break;
            case "Delete Service":
                return true;
                break;

        }

    }


    // Toggle children when page loads.
    function toggleOnLoad(d) {
        if (d.children) {
            if (d.hide == "hide") {
                d._children = d.children;
                d.children = null;
            }
        } else {
            d.children = d._children;
            d._children = null;
        }
    }

    //Toggle children when node is clicked.
    function toggleOnClick(d) {

        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
        // If the node has a parent, then collapse its child nodes
        // except for this clicked node.
        if (level == 1) {
            if (d.parent) {
                if (d.children) {
                    d.parent.children.forEach(function (element) {
                        if (d != element) {
                            collapse(element);
                        }
                    });
                }
            }
        }
    }

    function linkColor(d) {

        if (d != undefined) {
            var StrSource = d.source.ImpactType;
            var StrTarget = d.target.ImpactType;

            var Str = StrSource + "_" + StrTarget;

            switch (Str) {
                case "PIBS_NABS":
                    return "#1b75bd";
                    break;
                case "PIBS_PIBS":
                    return "#ffb406";
                    break;
                case "PIBS_MIBS":
                    return "#fb5a0c";
                    break;
                case "PIBS_TIBS":
                    return "#FF0000";
                    break;
                case "MIBS_NABS":
                    return "#1b75bd";
                    break;
                case "MIBS_MIBS":
                    return "#fb5a0c";
                    break;
                case "MIBS_PIBS":
                    return "#ffb406";
                    break;
                case "MIBS_TIBS":
                    return "#FF0000";
                    break;
                case "TIBS_NABS":
                    return "#1b75bd";
                    break;
                case "TIBS_TIBS":
                    return "#FF0000";
                    break;
                case "TIBS_PIBS":
                    return "#ffb406";
                    break;
                case "TIBS_MIBS":
                    return "#fb5a0c";
                    break;
                case "NABS_NABS":
                    return "#1b75bd";
                    break;
                case "NABS_PIBS":
                    return "#1b75bd";
                    break;
                case "NABS_MIBS":
                    return "#1b75bd";
                    break;
                case "NABS_TIBS":
                    return "#1b75bd";
                    break;
                case "PIBS_NABF":
                    return "#1b75bd";
                    break;
                case "PIBS_PIBF":
                    return "#ffb406";
                    break;
                case "PIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "PIBS_TIBF":
                    return "#FF0000";
                    break;
                case "MIBS_NABF":
                    return "#1b75bd";
                    break;
                case "MIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "MIBS_PIBF":
                    return "#ffb406";
                    break;
                case "MIBS_TIBF":
                    return "#FF0000";
                    break;
                case "TIBS_NABF":
                    return "#1b75bd";
                    break;
                case "TIBS_TIBF":
                    return "#FF0000";
                    break;
                case "TIBS_PIBF":
                    return "#ffb406";
                    break;
                case "TIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "NABS_NABF":
                    return "#1b75bd";
                    break;
                case "NABS_PIBF":
                    return "#1b75bd";
                    break;
                case "NABS_MIBF":
                    return "#1b75bd";
                    break;
                case "NABS_TIBF":
                    return "#1b75bd";
                    break;
                case "PIBF_NABF":
                    return "#1b75bd";
                    break;
                case "PIBF_PIBF":
                    return "#ffb406";
                    break;
                case "PIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "PIBF_TIBF":
                    return "#ff0000";
                    break;
                case "MIBF_NABF":
                    return "#1b75bd";
                    break;
                case "MIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "MIBF_PIBF":
                    return "#ffb406";
                    break;
                case "MIBF_TIBF":
                    return "#FF0000";
                    break;
                case "TIBF_NABF":
                    return "#1b75bd";
                    break;
                case "TIBF_TIBF":
                    return "#FF0000";
                    break;
                case "TIBF_PIBF":
                    return "#ffb406";
                    break;
                case "TIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "NABF_NABF":
                    return "#1b75bd";
                    break;
                case "NABF_PIBF":
                    return "#1b75bd";
                    break;
                case "NABF_MIBF":
                    return "#1b75bd";
                    break;
                case "NABF_TIBF":
                    return "#1b75bd";
                    break;
                case "PIBF_NAI":
                    return "#1b75bd";
                    break;
                case "PIBF_PII":
                    return "#ffb406";
                    break;
                case "PIBF_MII":
                    return "#fb5a0c";
                    break;
                case "PIBF_TII":
                    return "#ff0000";
                    break;
                case "MIBF_NAI":
                    return "#1b75bd";
                    break;
                case "MIBF_MII":
                    return "#fb5a0c";
                    break;
                case "MIBF_PII":
                    return "#ffb406";
                    break;
                case "MIBF_TII":
                    return "#FF0000";
                    break;
                case "TIBF_NAI":
                    return "#1b75bd";
                    break;
                case "TIBF_TII":
                    return "#FF0000";
                    break;
                case "TIBF_PII":
                    return "#ffb406";
                    break;
                case "TIBF_MII":
                    return "#fb5a0c";
                    break;
                case "NABF_NAI":
                    return "#1b75bd";
                    break;
                case "NABF_PII":
                    return "#1b75bd";
                    break;
                case "NABF_MII":
                    return "#1b75bd";
                    break;
                case "NABF_TII":
                    return "#1b75bd";
                    break;
                case "TII_TIICSV":
                    return "#ff0000";
                    break;
                case "TII_NAICSV":
                    return "#1b75bd";
                case "NAI_TIICSV":
                    return "#1b75bd";
                    break;
                case "NAI_NAICSV":
                    return "#1b75bd";
                    break;
                case "TII_TIICD":
                    return "#FF0000";
                    break;
                case "TII_NAICD":
                    return "#1b75bd";
                case "NAI_TIICD":
                    return "#1b75bd";
                    break;
                case "NAI_NAICD":
                    return "#1b75bd";
                    break;
                case "TII_TIICR":
                    return "#FF0000";
                    break;
                case "TII_NAICR":
                    return "#1b75bd";
                case "NAI_TIICR":
                    return "#1b75bd";
                    break;
                case "NAI_NAICR":
                    return "#1b75bd";
                    break;
                case "NAI_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICSV_TIIS":
                    return "#FF0000";
                    break;
                case "TIICSV_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICSV_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICSV_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICD_TIIS":
                    return "#FF0000";
                    break;
                case "TIICD_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICD_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICD_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICR_TIIS":
                    return "#FF0000";
                    break;
                case "TIICR_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICR_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICR_NAIS":
                    return "#1b75bd";
                    break;
                case "NABF_NAICSV":
                    return "#1b75bd";
                    break;
                case "PIBF_NAICSV":
                    return "#1b75bd";
                    break;
                case "TII_TIICSM":
                    return "#FF0000";
                    break;
                case "TIICSM_TIICSS":
                    return "#FF0000";
                    break;
                case "TIQ_TIQQ":
                    return "#FF0000";
                    break;
                case "TII_TIQ":
                    return "#FF0000";
                    break;
                default:
                    return "#1b75bd";
            }
        }
    }

    function hidenodeonload1(name, impacttype) {
        if (name != null) {
            var strName;
            if (name.indexOf("^") >= 0) {
                var iscap = name.split('^');
                strName = iscap[1].split("$")[0]
            }
            else {
                strName = name.substring(name.lastIndexOf('@') + 1, name.indexOf('$'))
            }


            var splitArra = strName.split('_');

            var Str = splitArra[0] + "_" + impacttype;
            switch (Str) {

                case "F_PIBF":
                    return 0;
                    break;
                case "F_MIBF":
                    return 0;
                    break;
                case "F_TIBF":
                    return 0;
                    break;
                case "F_NABF":
                    return 0;
                    break;
                case "I_PII":
                    return 0;
                    break;
                case "I_MII":
                    return 0;
                    break;
                case "I_TII":
                    return 0;
                    break;
                case "I_NAI":
                    return 0;
                    break;
                case "ICSV_TIICSV":
                    return 0;
                    break;
                case "ICSV_NAICSV":
                    return 0;
                    break;
                case "ICD_TIICD":
                    return 0;
                    break;
                case "ICD_NAICD":
                    return 0;
                    break;
                case "ICR_TIICR":
                    return 0;
                    break;
                case "ICR_NAICR":
                    return 0;
                    break;
                case "IS_TIIS":
                    return 0;
                    break;
                case "IS_NAIS":
                    return 0;
                    break


            }
        }

    }


    function hideLinkload(d) {
        if (d != null) {
            var StrSource = d.source.ImpactType;
            var StrTarget = d.target.ImpactType;

            var Str = StrSource + "_" + StrTarget;


            switch (Str) {

                case "PIBS_NABS":
                    return 1;
                    break;
                case "PIBS_PIBS":
                    return 1;
                    break;
                case "PIBS_MIBS":
                    return 1;
                    break;
                case "PIBS_TIBS":
                    return 1;
                    break;
                case "MIBS_NABS":
                    return 1;
                    break;
                case "MIBS_MIBS":
                    return 1;
                    break;
                case "MIBS_PIBS":
                    return 1;
                    break;
                case "MIBS_TIBS":
                    return 1;
                    break;
                case "TIBS_NABS":
                    return 1;
                    break;
                case "TIBS_TIBS":
                    return 1;
                    break;
                case "TIBS_PIBS":
                    return 1;
                    break;
                case "TIBS_MIBS":
                    return 1;
                    break;
                case "NABS_NABS":
                    return 1;
                    break;
                case "NABS_PIBS":
                    return 1;
                    break;
                case "NABS_MIBS":
                    return 1;
                    break;
                case "NABS_TIBS":
                    return 1;
                    break;
                case "PIBS_NABF":
                    return 0;
                    break;
                case "PIBS_PIBF":
                    return 0;
                    break;
                case "PIBS_MIBF":
                    return 0;
                    break;
                case "PIBS_TIBF":
                    return 0;
                    break;
                case "MIBS_NABF":
                    return 0;
                    break;
                case "MIBS_MIBF":
                    return 0;
                    break;
                case "MIBS_PIBF":
                    return 0;
                    break;
                case "MIBS_TIBF":
                    return 0;
                    break;
                case "TIBS_NABF":
                    return 0;
                    break;
                case "TIBS_TIBF":
                    return 0;
                    break;
                case "TIBS_PIBF":
                    return 0;
                    break;
                case "TIBS_MIBF":
                    return 0;
                    break;
                case "NABS_NABF":
                    return 0;
                    break;
                case "NABS_PIBF":
                    return 0;
                    break;
                case "NABS_MIBF":
                    return 0;
                    break;
                case "NABS_TIBF":
                    return 0;
                    break;
                case "PIBF_NABF":
                    return 0;
                    break;
                case "PIBF_PIBF":
                    return 0;
                    break;
                case "PIBF_MIBF":
                    return 0;
                    break;
                case "PIBF_TIBF":
                    return 0;
                    break;
                case "MIBF_NABF":
                    return 0;
                    break;
                case "MIBF_MIBF":
                    return 0;
                    break;
                case "MIBF_PIBF":
                    return 0;
                    break;
                case "MIBF_TIBF":
                    return 0;
                    break;
                case "TIBF_NABF":
                    return 0;
                    break;
                case "TIBF_TIBF":
                    return 0;
                    break;
                case "TIBF_PIBF":
                    return 0;
                    break;
                case "TIBF_MIBF":
                    return 0;
                    break;
                case "NABF_NABF":
                    return 0;
                    break;
                case "NABF_PIBF":
                    return 0;
                    break;
                case "NABF_MIBF":
                    return 0;
                    break;
                case "NABF_TIBF":
                    return 0;
                    break;
                case "PIBF_NAI":
                    return 0;
                    break;
                case "PIBF_PII":
                    return 0;
                    break;
                case "PIBF_MII":
                    return 0;
                    break;
                case "PIBF_TII":
                    return 0;
                    break;
                case "MIBF_NAI":
                    return 0;
                    break;
                case "MIBF_MII":
                    return 0;
                    break;
                case "MIBF_PII":
                    return 0;
                    break;
                case "MIBF_TII":
                    return 0;
                    break;
                case "TIBF_NAI":
                    return 0;
                    break;
                case "TIBF_TII":
                    return 0;
                    break;
                case "TIBF_PII":
                    return 0;
                    break;
                case "TIBF_MII":
                    return 0;
                    break;
                case "NABF_NAI":
                    return 0;
                    break;
                case "NABF_PII":
                    return 0;
                    break;
                case "NABF_MII":
                    return 0;
                    break;
                case "NABF_TII":
                    return 0;
                    break;
                case "TII_TIICSV":
                    return 0;
                    break;
                case "TII_NAICSV":
                    return 0;
                case "NAI_TIICSV":
                    return 0;
                    break;
                case "NAI_NAICSV":
                    return 0;
                    break;
                case "TII_TIICD":
                    return 0;
                    break;
                case "TII_NAICD":
                    return 0;
                case "NAI_TIICD":
                    return 0;
                    break;
                case "NAI_NAICD":
                    return 0;
                    break;
                case "TII_TIICR":
                    return 0;
                    break;
                case "TII_NAICR":
                    return 0;
                case "NAI_TIICR":
                    return 0;
                    break;
                case "NAI_NAICR":
                    return 0;
                    break;
                case "NAI_NAIS":
                    return 0;
                    break;
                case "TIICSV_TIIS":
                    return 0;
                    break;
                case "TIICSV_NAIS":
                    return 0;
                    break;
                case "NAICSV_TIIS":
                    return 0;
                    break;
                case "NAICSV_NAIS":
                    return 0;
                    break;
                case "TIICD_TIIS":
                    return 0;
                    break;
                case "TIICD_NAIS":
                    return 0;
                    break;
                case "NAICD_TIIS":
                    return 0;
                    break;
                case "NAICD_NAIS":
                    return 0;
                    break;
                case "TIICR_TIIS":
                    return 0;
                    break;
                case "TIICR_NAIS":
                    return 0;
                    break;
                case "NAICR_TIIS":
                    return 0;
                    break;
                case "NAICR_NAIS":
                    return 0;
                    break;
                case "NABF_NAICSV":
                    return 0;
                    break;
                case "PIBF_NAICSV":
                    return 0;
                    break;
                default:
                    return 0;

            }
        }
    }





    //return x value for node
    function returnx(d) {

        var xPos = null;
        if (d.hide == "hide") {
            xPos = 30;
        }
        else if (d.children && d.hide != "hide") {
            xPos = 30;
        }
        else {
            xPos = 30;
        }
        return xPos;
    }

    //return y value for node
    function returny(d) {
        var yPos = null;
        if (d.hide == "hide") {
            yPos = "-2em";
        }
        else if (d.children && d.hide != "hide") {
            yPos = "-2em"
        }
        else {
            yPos = "-2em";
        }
        return yPos;
    }

    //return TextAnchor for node
    function returnTextAnchor(d) {
        var textAnchor = null;
        //if (d.hide == "hide") {
        //    textAnchor = "start";
        //}
        //else if (d.children && d.hide != "hide") {
        //    textAnchor = "end";
        //}
        //else {
        //    textAnchor = "start";
        //}
        return textAnchor = "end"
    }

    // Gets Json for infraObjects details from node relation
    function GetJsonFromNodeRelation(d) {
        if (d.hide == "hide" && level == 0) {
            level = level + 1;
            var requiredClickedName = null;
            if (d.logo)
                requiredClickedName = d.name + "/Hide/logo/red" + ":";
            else
                requiredClickedName = d.name + "/Hide" + ":";

            var splitNodeRelation = globalstring.split(";");
            if (splitNodeRelation.length > 0) {
                for (var i = 0; i < splitNodeRelation.length; i++) {
                    var index = splitNodeRelation[i].indexOf(requiredClickedName);
                    if (index == 0) {
                        var infraObjectRel = splitNodeRelation[i];
                        var splitInfraObjectRel = infraObjectRel.split(":");
                        var componentArray = splitInfraObjectRel[1].split(",");
                        if (componentArray.length > 0) {
                            for (var j = 0 ; j < componentArray.length; j++) {
                                var requiredComponentName = componentArray[j] + ":";
                                for (var k = i; k < splitNodeRelation.length; k++) {
                                    var componentArrayIndex = splitNodeRelation[k].indexOf(requiredComponentName)
                                    if (componentArrayIndex == 0) {
                                        infraObjectRel = infraObjectRel + ";" + splitNodeRelation[k];
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                var requiredNodeRelation = infraObjectRel;
                if (requiredNodeRelation != null) {
                    $('#bsbody3').html("");
                    renderAjaxDataForInfraObjects(requiredNodeRelation);
                    isbtnReloadHide = true;
                    d3.select("#btnReload").attr("style", "display:inline-block");
                }
            }
        }
    }

    // Resets tree to default full view
    function backToFullView() {
        $('#bsbody3').html("");
        level = 0;
        if (isbtnReloadHide)
            d3.select("#btnReload").attr("style", "display:none");
        treeShow(JsonForFullView);
    }
    d3.select("#btnReload").on("click", backToFullView);

    //collapse other node while expanding current clicked/selected node
    function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d._children.forEach(collapse);
            d.children = null;
        }
    }
}
// Ends ======================

///================== Return Node textAnchor class as per type===================

function returnTextAnchorClass(name, impacttype) {

    if (name != null) {
        var iscap = name.substring(name.lastIndexOf('@') + 1, name.lastIndexOf('$'))
        if (iscap.indexOf("_") >= 0) {
            apppre = iscap.split("_")[0];
            if (apppre.indexOf("^") >= 0) {
                apppre = apppre.split('^')[1];
            }
        }

        var Str = apppre + "_" + impacttype;

        switch (Str) {

            case "S_PIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_MIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_TIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_NABS":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_PIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_MIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_TIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_NABF":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_PII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_MII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_TII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_NAI":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSV_TIICSV":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSV_NAICSV":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICD_TIICD":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICD_NAICD":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICR_TIICR":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICR_NAICR":
                return textAnchorClass = "impact-text-black";
                break;
            case "IS_TIIS":
                return textAnchorClass = "impact-text-red";
                break;
            case "IS_NAIS":
                return textAnchorClass = "impact-text-black";
                break
            case "ICSM_NAICSM":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSM_TIICSM":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSS_NAICSS":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSS_TIICSS":
                return textAnchorClass = "impact-text-red";
                break;
            case "NQ_TIQQ":
                return textAnchorClass = "impact-text-red";
                break;
            default:
                return textAnchorClass = "impact-text-black";
                break;
        }
    }
}


///================== Return Node Image as per type===================
function GetNodeImgPathByName(name, impacttype) {

    var Str = name + "_" + impacttype;
    switch (Str) {

        case "S_PIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_yellow.png";
            break;
        case "S_MIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_orange.png";
            break;
        case "S_TIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_red.png";
            break;
        case "S_NABS":
            return "../Images/ServiceLogo/bussinessservice_icon_green.png";
            break;
        case "F_PIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_yellow.png";
            break;
        case "F_MIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_orange.png";
            break;
        case "F_TIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_red.png";
            break;
        case "F_NABF":
            return "../Images/ServiceLogo/bussinessfunction_icon_green.png";
            break;
        case "I_PII":
            return "../Images/ServiceLogo/infraobject_icon_yellow.png";
            break;
        case "I_MII":
            return "../Images/ServiceLogo/infraobject_icon_orange.png";
            break;
        case "I_TII":
            return "../Images/ServiceLogo/infraobject_icon_red.png";
            break;
        case "I_NAI":
            return "../Images/ServiceLogo/infraobject_icon_green.png";
            break;
        case "ICSV_TIICSV":
            return "../Images/ServiceLogo/ServerImpacted_Icon.png";
            break;
        case "ICSV_NAICSV":
            return "../Images/ServiceLogo/Server_Icon.png";
            break;
        case "ICD_TIICD":
            return "../Images/ServiceLogo/DatabaseImpacted_Icon.png";
            break;
        case "ICD_NAICD":
            return "../Images/ServiceLogo/Database_Icon.png";
            break;
        case "ICR_TIICR":
            return "../Images/ServiceLogo/ReplicationImpacted_Icon.png";
            break;
        case "ICR_NAICR":
            return "../Images/ServiceLogo/Replication_Icon.png";
            break;
        case "IS_TIIS":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "IS_NAIS":
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
        case "ICSM_NAICSM":
            return "../Images/ServiceLogo/Service_Cog_Green1.png";
            break;
        case "ICSM_TIICSM":
            return "../Images/ServiceLogo/Service_Cog_Red1.png";
            break;
        case "Q_TIQ":
            return "../Images/ServiceLogo/Queue_Icon_Red.png";
            break;
        case "Q_NAQ":
            return "../Images/ServiceLogo/Queue_Icon.png";
            break;
        case "NQ_NAQQ":
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
        case "NQ_TIQQ":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "ICSS_NAICSS":
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
        case "ICSS_TIICSS":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "S_TIICSS":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "Q_TIQQ":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        default:
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
    }
}


//====================Get Node img Hight =========================
function GetNodeImgHightByName(name, impactType) {


    if (name != null) {

        var iscap = name.substring(name.lastIndexOf('@') + 1, name.lastIndexOf('$'))
        if (iscap.indexOf("_") >= 0) {
            apppre = iscap.split("_")[0];
            if (apppre.indexOf("^") >= 0) {
                apppre = apppre.split('^')[1];
            }
        }

        var Str = apppre + "_" + impactType;


        switch (Str) {
            case "S_PIBS":
                return 47;
                break;
            case "S_MIBS":
                return 47;
                break;
            case "S_TIBS":
                return 47;
                break;
            case "S_NABS":
                return 47;
                break;
            case "F_PIBF":
                return 33;
                break;
            case "F_MIBF":
                return 33;
                break;
            case "F_TIBF":
                return 33;
                break;
            case "F_NABF":
                return 32;
                break;
            case "I_PII":
                return 33;
                break;
            case "I_MII":
                return 33;
                break;
            case "I_TII":
                return 33;
                break;
            case "I_NAI":
                return 32;
                break;
            case "ICSV_TIICSV":
                return 22;
                break;
            case "ICSV_NAICSV":
                return 22;
                break;
            case "ICD_TIICD":
                return 20;
                break;
            case "ICD_NAICD":
                return 20;
                break;
            case "ICR_TIICR":
                return 18;
                break;
            case "ICR_NAICR":
                return 18;
                break;
            case "ICSM_NAICSM":
                return 18;
                break;
            case "ICSM_TIICSM":
                return 18;
                break;
            case "Q_NAQ":
                return 18;
                break;
            case "Q_TIQ":
                return 18;
                break;
            case "IS_TIIS":
                return 14;
                break;
            case "IS_NAIS":
                return 14;
                break;
            case "ICSS_NAICSS":
                return 14;
                break;
            case "ICSS_TIICSS":
                return 14;
                break;
            case "NQ_NAQQ":
                return 14;
                break;
            case "NQ_TIQQ":
                return 14;
                break
            default:
                return 14;
                break;
        }

    }
}

//====================Get Node img Width =========================
function GetNodeImgWidthByName(name, impactType) {


    if (name != null) {

        var iscap = name.substring(name.lastIndexOf('@') + 1, name.lastIndexOf('$'))
        if (iscap.indexOf("_") >= 0) {
            apppre = iscap.split("_")[0];
            if (apppre.indexOf("^") >= 0) {
                apppre = apppre.split('^')[1];
            }
        }

        var Str = apppre + "_" + impactType;

        switch (Str) {
            case "S_PIBS":
                return 53;
                break;
            case "S_MIBS":
                return 53;
                break;
            case "S_TIBS":
                return 53;
                break;
            case "S_NABS":
                return 47;
                break;
            case "F_PIBF":
                return 33;
                break;
            case "F_MIBF":
                return 33;
                break;
            case "F_TIBF":
                return 33;
                break;
            case "F_NABF":
                return 32;
                break;
            case "I_PII":
                return 33;
                break;
            case "I_MII":
                return 33;
                break;
            case "I_TII":
                return 33;
                break;
            case "I_NAI":
                return 32;
                break;
            case "ICSV_TIICSV":
                return 18;
                break;
            case "ICSV_NAICSV":
                return 18;
                break;
            case "ICD_TIICD":
                return 16;
                break;
            case "ICD_NAICD":
                return 16;
                break;
            case "ICR_TIICR":
                return 18;
                break;
            case "ICR_NAICR":
                return 18;
                break;
            case "ICSM_NAICSM":
                return 18;
                break;
            case "ICSM_TIICSM":
                return 18;
                break;
            case "IS_TIIS":
                return 14;
                break;
            case "IS_NAIS":
                return 14;
                break;
            case "ICSS_NAICSS":
                return 14;
                break;
            case "ICSS_TIICSS":
                return 14;
                break;
            case "Q_NAQ":
                return 18;
                break;
            case "Q_TIQ":
                return 18;
                break;
            case "NQ_NAQQ":
                return 14;
                break;
            case "NQ_TIQQ":
                return 14;
                break
            default:
                return 14;
                break;
        }


    }
}

//====================Get Node img Hight =========================
function GetRadius(name, impactType) {

    if (name != null) {

        var iscap = name.substring(name.lastIndexOf('@') + 1, name.lastIndexOf('$'))
        if (iscap.indexOf("_") >= 0) {
            apppre = iscap.split("_")[0];
            if (apppre.indexOf("^") >= 0) {
                apppre = apppre.split('^')[1];
            }
        }

        var Str = apppre + "_" + impactType;

        switch (Str) {
            case "S_PIBS":
                return "-1.6em";
                break;
            case "S_MIBS":
                return "-1.6em";
                break;
            case "S_TIBS":
                return "-1.6em";
                break;
            case "S_NABS":
                return "-1.6em";
                break;
            case "F_PIBF":
                return "-1.2em";
                break;
            case "F_MIBF":
                return "-1.2em";
                break;
            case "F_TIBF":
                return "-1.2em";
                break;
            case "F_NABF":
                return "-1.2em";
                break;
            case "I_PII":
                return "-1.2em";
                break;
            case "I_MII":
                return "-1.2em";
                break;
            case "I_TII":
                return "-1.2em";
                break;
            case "I_NAI":
                return "-1.2em";
                break;
            case "ICSV_TIICSV":
                return "-0.9em";
                break;
            case "ICSV_NAICSV":
                return "-0.9em";
                break;
            case "ICD_TIICD":
                return "-0.9em";
                break;
            case "ICD_NAICD":
                return "-0.9em";
                break;
            case "ICR_TIICR":
                return "-0.9em";
                break;
            case "ICR_NAICR":
                return "-0.9em";
                break;
            case "ICSM_NAICSM":
                return "-0.9em";
                break;
            case "ICSM_TIICSM":
                return "-0.9em";
                break;
            case "ICSS_NAICSS":
                return "-0.5em";
                break;
            case "ICSS_TIICSS":
                return "-0.5em";
                break;
            case "IS_TIIS":
                return "-0.5em";
                break;
            case "IS_NAIS":
                return "-0.5em";
                break;
            case "NQ_NAQQ":
                return "-0.5em";
                break;
            case "NQ_TIQQ":
                return "-0.5em";
                break;
            case "Q_NAQ":
                return "-0.9em";
                break;
            case "Q_TIQ":
                return "-0.9em";
                break;

            default:
                return "-0.5em";
                break;
        }

    }
}


function GetUniqueIndexNumber(arr) {

    var MaxCountNumber;

    var maxtime = Math.max.apply(Math, arr);

    MaxCountNumber = maxtime + 1;

    return MaxCountNumber;
}
//Find Number Bs count in GlobalString


function GetBsNodeCount(inputString) {
    var nodeServiceCount = 1;
    var indexArr = [];
    indexArr.push(1);
    if (inputString != "" && inputString != undefined) {
        var strArr = inputString.split(';');
        for (var i = 0; i < strArr.length; i++) {
            if (strArr[i].indexOf(':') > 0) {
                if (strArr[i].split(':')[1] != "" && strArr[i].split(':')[1] != undefined) {
                    var chilArr = strArr[i].split(':')[1].split(',');
                    for (var t = 0; t < chilArr.length; t++) {
                        var serindex = chilArr[t].split('_')[0].split('^')[0];
                        if (serindex != "")
                            serindex = serindex.substring(1, serindex.length);
                        indexArr.push(Number(serindex));
                    }
                }
            }
        }
    }
    nodeServiceCount = Math.max.apply(Math, indexArr);
    return nodeServiceCount + 1;
}
//function GetBsNodeCount(inputString) {
//    var nodeServiceCount = 0;
//    if (inputString != "" && inputString != undefined) {
//        var strArr = inputString.split(';');
//        for (var i = 0; i < strArr.length; i++) {
//            if (strArr[i].indexOf(':') > 0) {
//                var strparent = strArr[i].split(':')[0];
//                if (strparent.indexOf('@') > 0) {
//                    var strPre = strparent.split('@')[2].split('_')[0];
//                    if (strPre.indexOf("^") >= 0) {
//                        strPre = strPre.split('^')[1];
//                    }
//                    if (strPre == "S")
//                        nodeServiceCount++;
//                }

//                else {
//                    var strPre = strparent.split('_')[0];
//                    if (strPre.indexOf("^") >= 0) {
//                        strPre = strPre.split('^')[1];
//                    }
//                    if (strPre == "S")
//                        nodeServiceCount++;
//                }
//            }

//        }
//    }
//    return nodeServiceCount + 1;
//}

//=========Add New Node to Tree===================
function AddNodeToTree(id) {
    isChange = true;
    isNewProfile = true;
    var compareName = "";
    var childAddName = "";
    var strFinalTemp = "";
    //get string with append prefix
    getNewNodeJSON(id, lastRightClickNode.name);
    if (newJSON != undefined && newJSON != "") {
        if (globalstring != null && globalstring != undefined) {
            var GBScount = GetBsNodeCount(globalstring);
            var splitGlobalStr = globalstring.split(';');
            var splitNewJsonStr = newJSON.split(';');
            if (splitGlobalStr.length > 0 && splitNewJsonStr.length > 0) {
                var flag = false;
                for (var i = 0; i < splitGlobalStr.length; i++) {
                    var lastNodeName = lastRightClickNode.name + "/" + lastRightClickNode.ImpactType;
                    if (splitGlobalStr[i].split(':')[0] == lastNodeName) {
                        flag = true;
                        if (splitGlobalStr[i].split(':')[1] != undefined) {
                            strFinalTemp += splitGlobalStr[i].split(':')[0] + ":" + "I" + GBScount + "^" + splitNewJsonStr[0].split(':')[0] + "," + splitGlobalStr[i].split(':')[1] + ";";
                            if (splitNewJsonStr[0].split(':')[1] != "" && splitNewJsonStr[0].split(':')[1] != undefined)
                                strFinalTemp += "I" + GBScount + "^" + newJSON;
                        }
                        else
                            strFinalTemp += splitGlobalStr[i].split(':')[0] + ":" + "I" + GBScount + "^" + splitNewJsonStr[0].split(':')[0] + ";";
                    }
                    else {
                        if (splitGlobalStr[i] != "" && splitGlobalStr[i] != undefined)
                            strFinalTemp += splitGlobalStr[i] + ";";
                    }
                }
                if (flag != true) {
                    strFinalTemp = "";
                    for (var i = 0; i < splitGlobalStr.length; i++) {
                        var lastNodeName = lastRightClickNode.name + "/" + lastRightClickNode.ImpactType;
                        if (splitGlobalStr[i].split(':')[1] == undefined && splitGlobalStr[i] != "") {
                            strFinalTemp += splitGlobalStr[i].split(':')[0] + ":" + "I" + GBScount + "^" + newJSON.split(';')[0].split(':')[0] + ";";
                            strFinalTemp += lastNodeName + ":" + "I" + GBScount + "^" + newJSON;
                        }
                        else if (splitGlobalStr[i].split(':')[1] != undefined)
                            if (splitGlobalStr[i].split(':')[1].indexOf(lastNodeName) >= 0) {
                                var child = splitGlobalStr[i].split(':')[1].split(',');
                                var childString = "";
                                for (var t = 0; t < child.length; t++) {
                                    if (child[t] != lastNodeName)
                                        childString += child[t] + ",";
                                }
                                if (childString != "") {
                                    childString = childString.substring(0, childString.length - 1);
                                    childString = lastNodeName + "," + childString;
                                }
                                else
                                    childString = lastNodeName;

                                strFinalTemp += splitGlobalStr[i].split(':')[0] + ":" + childString + ";";
                                strFinalTemp += lastNodeName + ":" + "I" + GBScount + "^" + newJSON + ";";
                            }
                            else {
                                strFinalTemp += splitGlobalStr[i] + ";";
                            }
                    }
                }
                if (strFinalTemp != undefined) {
                    $.ajax({
                        type: "POST",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        async: true,
                        data: JSON.stringify({ 'newJsonString': strFinalTemp }),
                        url: "DesignProcessMoniter.aspx/GetJSONFromString",
                        success: function (msg) {
                            var value = msg.d.split('#');
                            globalstring = value[1];
                            var JsonObj = eval('(' + value[0] + ')');
                            JsonForFullView = JsonObj;
                            treeShow(JsonObj);
                        }
                    });
                }
            }
        }
    }
}


//==================Open Popup=====================
function openAddServicePopup(d) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'appName': d }),
        url: "../Admin/DesignProcessMoniter.aspx/LoadServiceDDL",
        success: function (data) {
            if (data != undefined && data.d != "") {
                $("#ddlAppList").empty();
                var appNameArr = data.d.split(';');
                if (appNameArr.length > 0) {
                    for (var i = 0; i < appNameArr.length; i++) {
                        $("#ddlAppList").append($("<option></option>").val(appNameArr[i].split('@')[0]).html(appNameArr[i].split('@')[1]));
                    }
                }
            }
        }
    });

    $("#servicp").css("display", "block");
    $("#dvAddService").css("display", "block");
    $("#bg").css({ 'display': "block" });
}

//============== New Service Profile Create click============
function newServiceProfile() {
    $("#btnSaveProfile").attr("disabled", false);
    isNewProfile == true;
    isChange == true;
    clickNewService = true;
    globalstring = null;
    lastRightClickNode = null;
    newJSON = "";
    isChange = false;
    isNewProfile = true; IsUpdateGroupService = false;
    openAddServicePopup("S", "", "");
}

//==================Delete GroupService By Profile ==================
function DeleteGroupServiceByProfile(GId) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'GroupServiceId': GId }),
        url: "../Admin/DesignProcessMoniter.aspx/DeleteServiceProfile",
        success: function (data) {

            alert("Business Service Group deleted Successfully");
            CallAjaxForTree();
            CallAjaxForTreeForUser();
            treeShow("");
        }
    });




}
//====================Save Service Profile===========
function saveService() {

    $("#errServiceName").css("display", "none");
    $("#errAttachProfile").css("display", "none");
    $("#errProfileName").css("display", "none");
    $("#errDuplicateName").css("display", "none");
    if (validateSaveService()) {
        var serviceName = $("#ctl00_cphBody_txtServiceName").val();
        if (isNewProfile == true) {
            //=====Attache service to existing profile==============
            var selectedID = "";// $("#ddlAttachProfile option:selected").val();
            //$("#ddlAttachProfile option:selected").each(function () {
            //    selectedID += this.value + ",";
            //});
            var chkNewProfile = $('#chkNewProfile').prop('checked');
            if (chkNewProfile == false) {
                $("#ddlAttachProfile option:selected").each(function () { selectedID += this.value + ","; });
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: true,
                    data: JSON.stringify({ 'serviceName': serviceName, 'profileIds': selectedID, 'jsonStr': globalstring }),
                    url: "../Admin/DesignProcessMoniter.aspx/SaveServiceProfile",
                    success: function (data) {
                        isNewProfile = false;
                        $("#popup_service").css("display", "none");
                        alert("Saved Service Group Successfully with Existing Profile");
                        $("#btnSaveProfile").attr("disabled", true);
                        CallAjaxForTree();
                        CallAjaxForTreeForUser();
                        treeShow("");
                    }
                });
            }
            else {
                //========== Create New Profile==================
                var profileName = $("#ctl00_cphBody_txtProfileName").val();
                var attachRole = document.getElementById("ddlAttachRole").value;
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: true,
                    data: JSON.stringify({ 'serviceName': serviceName, 'profileName': profileName, 'attachRole': attachRole, 'jsonStr': globalstring }),
                    url: "../Admin/DesignProcessMoniter.aspx/SaveServiceWithNewProfile",
                    success: function (data) {
                        isNewProfile = false;
                        $("#popup_service").css("display", "none");
                        alert("Saved Service Group Successfully with New Profile");
                        $("#btnSaveProfile").attr("disabled", true);
                        CallAjaxForTree();
                        CallAjaxForTreeForUser();
                        treeShow("");
                    }
                });

            }

        }
        //CallAjaxForTree();
        //CallAjaxForTreeForUser();
    }
}

//$('#btnSaveProfile').on('click', function () {
//                      
//    saveServicePopup();

//});
//============ Show Save Popup==============
function saveServicePopup() {
    //&& IsUpdateGroupService == false
    if (isNewProfile == true) {
        loadProfileDDL();
        loadUserRoleDDL();
        $("#dvProfileName").css("display", "none");
        $("#popup_service").css("display", "block");
        $("#ctl00_cphBody_txtProfileName").val("");
        $("#ctl00_cphBody_txtServiceName").val("");
        $("#dvAttachRole").css("display", "none");
        $("#dvAttchedProfile").css("display", "block");

        //clear the checkboxNewProfile
        var chkProfile = $("#chkNewProfile").next().children().children();
        $(chkProfile[0]).css("display", "none");
        $(chkProfile[1]).css("display", "Inline-block");

    }
    if (IsUpdateGroupService == true && isChange == true) {
        //Update Serviec Profile

        if (isEmpty(GroupServiceID) && isEmpty(GroupProfileID)) {

            $.ajax({
                type: "POST",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                data: JSON.stringify({ 'GroupServiceID': GroupServiceID, 'GroupProfileID': GroupProfileID, 'jsonStr': globalstring, 'IsDeletedBsIndex': IsDeletedBsIndex }),
                url: "../Admin/DesignProcessMoniter.aspx/UpdateServiceGroupByID",
                success: function (data) {
                    isNewProfile = false;
                    $("#popup_service").css("display", "none");
                    alert("Service Save Successfully. UpdateServiceProfile");
                    CallAjaxForTree();
                    CallAjaxForTreeForUser();
                    treeShow("");
                }
            });

        }
    }




    return false;
}

//==========================Add Deleted Business Service INdex in Global ArrayList===========================

function GetDeletedBsNodeINdex(inputString) {
    var Bscount = 0;

    if (isEmpty(inputString)) {
        if (inputString.indexOf("^") >= 0) {

            var iscap = inputString.split("^")[0].substring(inputString.split("^")[0].length - 1);
            IsDeletedBsIndex.push(JSON.parse(iscap));
        }

    }

}


//===========================Delete Service Node from tree================
function deleteServiceNode() {
    isChange = true;
    //IsUpdateGroupService = true;
    $("#btnSaveProfile").attr("disabled", false);
    var tempStr = "";
    var sampleArr = null;
    var delNodeChild = "";
    if (lastRightClickNode != undefined && globalstring != "") {
        // GetDeletedBsNodeINdex(lastRightClickNode.name);
        var tmpSplitArr = globalstring.split(';');
        var nodename = lastRightClickNode.name + "/" + lastRightClickNode.ImpactType;
        for (var i = 0; i < tmpSplitArr.length; i++) {
            if (tmpSplitArr[i].indexOf(':') >= 0) {
                if (tmpSplitArr[i].split(':')[1].indexOf(nodename) >= 0) {


                    if (tmpSplitArr.length == 2) {
                        if (tmpSplitArr[0].split(':')[1] == nodename) {
                            tempStr = tmpSplitArr[0].split(':')[0];
                        }
                    }

                    //Get Delete Node Child
                    for (var t = 0; t < tmpSplitArr.length; t++) {
                        if (tmpSplitArr[t].split(':')[0] == nodename) {
                            delNodeChild = tmpSplitArr[t].split(':')[1];
                        }
                    }

                    //Replace Child
                    var oldChild = "";
                    var childArr = tmpSplitArr[i].split(':')[1].split(',');
                    for (var r = 0; r < childArr.length; r++) {
                        if (childArr[r] == nodename) {
                            if (delNodeChild != "")
                                oldChild += delNodeChild + ",";
                        }
                        else
                            if (childArr[r] != "")
                                oldChild += childArr[r] + ",";
                    }

                    if (oldChild != "") {
                        if (oldChild.substring(oldChild.length - 1) == ",")
                            oldChild = oldChild.substring(0, oldChild.length - 1);
                        tempStr += tmpSplitArr[i].split(':')[0] + ":" + oldChild + ";";
                    }

                }
                else
                    if (tmpSplitArr[i].split(':')[0] != nodename)
                        tempStr += tmpSplitArr[i] + ";";
            }
        }
        if (tempStr != undefined) {
            $.ajax({
                type: "POST",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                data: JSON.stringify({ 'newJsonString': tempStr }),
                url: "../Admin/DesignProcessMoniter.aspx/GetJSONFromString",
                success: function (msg) {
                    var value = msg.d.split('#');
                    globalstring = value[1];
                    var JsonObj = eval('(' + value[0] + ')');
                    JsonForFullView = JsonObj;
                    treeShow(JsonObj);
                }
            });

            tempStr = "";
            tmpSplitArr = null;
        }

    }
}

//============Show Left Tree Panel-============
function CallAjaxForTree() {
    $.ajax({
        type: "POST",
        url: "../Admin/DesignProcessMoniter.aspx/GetServiceProfile",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function (data) {
            $("#ctl00_cphBody_newList").empty();
            $("#ctl00_cphBody_newList").append(data.d);
            CollapsibleLists.applyTo(document.getElementById('ctl00_cphBody_newList'));
        }
    });
}

function CallAjaxForTreeForUser() {
    $.ajax({
        type: "POST",
        url: "../Admin/DesignProcessMoniter.aspx/GetServiceByUser",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function (data) {
            $("#userList").empty();
            $("#userList").append(data.d);
            CollapsibleLists.applyTo(document.getElementById('userList'));
        }
    });
}



//=================Show Service  on click service Tree Left Panel=======================
function renderService(id, apptype, profileId) {


    $("#btnSaveProfile").attr("disabled", true);
    GroupProfileID = profileId;
    GroupServiceID = id;
    isNewProfile = false;
    IsUpdateGroupService = true;


    if (apptype == "S") {
        $.ajax({
            type: "POST",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: true,
            data: JSON.stringify({ 'id': id, 'apptype': apptype, 'profileId': profileId }),
            url: "../Admin/DesignProcessMoniter.aspx/GetServiceJsonfromDB",
            success: function (msg) {

                var value = msg.d.split('#');
                globalstring = value[1];
                var JsonObj = eval('(' + value[0] + ')');
                JsonForFullView = JsonObj;
                treeShow(JsonObj);

            }
        });
    }
}


//===================Load  Existing profile in Dropdown list ========================
function loadProfileDDL() {

    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        url: "../Admin/DesignProcessMoniter.aspx/GetProfilelistString",
        success: function (data) {
            if (data != undefined && data.d != "") {
                $("#ddlAttachProfile").empty();
                var appNameArr = data.d.split(';');
                if (appNameArr.length > 0) {

                    for (var i = 0; i < appNameArr.length; i++) {
                        if (isEmpty(appNameArr[i])) {
                            $("#ddlAttachProfile").append($("<option></option>").val(appNameArr[i].split('@')[0]).html(appNameArr[i].split('@')[1]));
                        }
                    }

                }


                //if ($(this).multiselect("getChecked").length == $('select.multi > option').length) {
                //    $(this).multiselect("uncheckAll");
                //}
                //$(this).multiselect("uncheckAll");
                //  $('#ddlAttachProfile').prop('selectedIndex', 0);
                //$("#ddlAttachProfile option:selected").removeAttr("selected");


                $('#ddlAttachProfile').multiselect({
                    dropRight: true,
                    maxHeight: 168,
                    nonSelectedText: 'Select Service',
                    numberDisplayed: 2

                });


                var lst = $("div.btn-group ul.multiselect-container").children().children().children().children("span");
                $.each(lst, function (i, v) {
                    var spanchk = $(v).children().children();
                    $(spanchk[0]).css("display", "none");
                    $(spanchk[1]).css("display", "Inline-block");
                });

                $("button.multiselect").attr("title", "Select Service");
                $("button.multiselect").children("span").html("Select Service");
                $("div.btn-group ul.multiselect-container").children("li").removeAttr('class');


            }
        }
    });


}


//=============show hide profile name div====================
$("#ddlAttachProfile").change(function () {
    // var selectedId = $("#ddlAttachProfile option:selected").val();

});


//==================hide show control on chk new profile on save service
function hideShowControl(control) {
    if (control.checked) {
        $("#ddlAttachRole").attr('disabled', 'disabled');
        $("#dvAttachRole").css("display", "block");
        $("#dvProfileName").css("display", "block");
        $("#dvAttchedProfile").css("display", "none");


    }
    else {

        $("#ddlAttachRole").removeAttr('disabled');
        $("#dvProfileName").css("display", "none");
        $("#dvAttachRole").css("display", "none");
        $("#dvAttchedProfile").css("display", "block");

    }
}

//===============Load User Role DDL=========================
function loadUserRoleDDL() {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        url: "../Admin/DesignProcessMoniter.aspx/LoadUserRoleDDL",
        success: function (data) {
            if (data != undefined && data.d != "") {
                $("#ddlAttachRole").empty();
                var appNameArr = data.d.split(';');
                if (appNameArr.length > 0) {
                    for (var i = 0; i < appNameArr.length; i++) {
                        $("#ddlAttachRole").append($("<option></option>").val(appNameArr[i].split('@')[0]).html(appNameArr[i].split('@')[1]));
                    }
                }

                $('#ctl00_cphBody_pnlSaveService .bootstrap-select .filter-option').text('- Select Role -');
            }
        }
    });

}


//=============Clear All Data member
function clearAll() {
    $("#btnSaveProfile").attr("disabled", true);
    globalstring = null;
    lastRightClickNode = null;
    newJSON = "";
    isChange = false;
    isNewProfile = false;
    IsUpdateGroupService = false;
    BSIndexCount = 0;
    treeShow("");
    //d3.select("svg").remove();

}

//====================load service after click on popup load
function attachService() {
    var id = document.getElementById("ddlAppList").value;
    $("#btnSaveProfile").attr("disabled", false);


    if (clickNewService == true)
        getJSONByServiceId(id)
    else
        AddNodeToTree(id);

    $("#servicp").css("display", "none");
    $("#dvAddService").css("display", "none");
}


//==============Validation===============
function validateSaveService() {
    var isAllControlValidate = true;
    var serviceName = $("#ctl00_cphBody_txtServiceName").val();
    var ProfileName = $("#ctl00_cphBody_txtProfileName").val();
    var ddlAttachRole = document.getElementById("ddlAttachRole").value;
    var ddlAttachProfile = document.getElementById("ddlAttachProfile").value;

    if ($("#chkNewProfile").is(':checked')) {
        if ((ProfileName == undefined || ProfileName == null || ProfileName.length <= 0)) {
            $("#errProfileName").css("display", "block");
            return false;
        }
        else {
            $.ajax({
                type: "POST",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                data: JSON.stringify({ 'ProfileName': ProfileName }),
                url: "../Admin/DesignProcessMoniter.aspx/GetProfileNameExist",
                success: function (data) {
                    if (data.d) {
                        isAllControlValidate = false;
                    }
                    else {
                        isAllControlValidate = true;
                    }

                }
            });

            if (isAllControlValidate == false)
            { $("#errDuplicateName").css("display", "block"); return isAllControlValidate; }

        }
        if ((serviceName == undefined || serviceName == null || serviceName.length <= 0)) {

            $("#errServiceName").css("display", "block");
            return false;

        }
        else {

            $.ajax({
                type: "POST",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                data: JSON.stringify({ 'GroupServcieName': serviceName }),
                url: "../Admin/DesignProcessMoniter.aspx/GetGroupServiceNameExist",
                success: function (data) {
                    if (data.d) {
                        isAllControlValidate = false;
                    }
                    else {
                        isAllControlValidate = true;
                    }
                }
            });
            if (isAllControlValidate == false)
            { $("#errDuplicateName").css("display", "block"); return isAllControlValidate; }

        }
        if ((ddlAttachRole == undefined || ddlAttachRole == null || ddlAttachRole.length <= 0) || ddlAttachRole == "Select Role") {

            $("#errAttachRole").css("display", "block");
            return false;

        }
    }
    else {
        if ((serviceName == undefined || serviceName == null || serviceName.length <= 0)) {

            $("#errServiceName").css("display", "block");
            return false;

        }
        else {
            $.ajax({
                type: "POST",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                data: JSON.stringify({ 'GroupServcieName': serviceName }),
                url: "../Admin/DesignProcessMoniter.aspx/GetGroupServiceNameExist",
                success: function (data) {
                    if (data.d) {
                        isAllControlValidate = false;
                    }
                    else {
                        isAllControlValidate = true;
                    }
                }
            });

            if (isAllControlValidate == false)
            { $("#errDuplicateName").css("display", "block"); return isAllControlValidate; }

        }
        if ((ddlAttachProfile = undefined || ddlAttachProfile == null || ddlAttachProfile.length <= 0)) {
            $("#errAttachProfile").css("display", "block");
            return false;
        }
    }

    return true;

}






//==============Open Rule Popup===============

function openAddupdateRulesPopup(d, node, lastRightNode) {


    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'appName': d.name, 'parentNode': lastRightNode.name }),
        url: "../Admin/DesignProcessMoniter.aspx/LoadRuleDDL",
        success: function (data) {
            if (data != undefined && data.d != "") {
                $("#ddlParentBusinessServiceBStoBS").empty();
                $("#ddlChildBusinessServiceBStoBS").empty();
                $("#ddlinfraObjectComponent").empty();
                $("#ddlBusinessFunction").empty();
                $("#ddlImpactTypeBF").empty();
                $("#ddlBS_BFtoBS").empty();
                $("#ddlBSImpactType_BFtoBS").empty();

                var appRulePanel = data.d.split('#');
                var RulePanel = appRulePanel[0];

                if (RulePanel == "BStoBS") {
                    $('#pnlInfraToBF').css('display', 'none');
                    $('#pnlBStoBS').css('display', 'inline');

                    $("#ddlParentBusinessServiceBStoBS").attr("disabled", true);
                    $("#ddlChildBusinessServiceBStoBS").attr("disabled", true);

                    var appName = appRulePanel[1].split('$');
                    var appNameArr = appName[0].split(';');
                    if (appNameArr.length > 0) {
                        for (var i = 0; i < appNameArr.length; i++) {
                            if (isEmpty(appNameArr[i])) {
                                if (i == 0) {
                                    $("#ddlChildBusinessServiceBStoBS").append($("<option></option>").val(appNameArr[i].split('@')[0]).html(appNameArr[i].split('@')[1]));

                                }
                                if (i == 1) {
                                    $("#ddlParentBusinessServiceBStoBS").append($("<option></option>").val(appNameArr[i].split('@')[0]).html(appNameArr[i].split('@')[1]));
                                }

                            }
                        }
                    }
                    $("#dllParentImpactTypeBStoBs").empty();

                    var impactTypeArr = appName[1].split(';');
                    if (impactTypeArr.length > 0) {
                        for (var i = 0; i < impactTypeArr.length; i++) {
                            if (isEmpty(impactTypeArr[i])) {
                                $("#dllParentImpactTypeBStoBs").append($("<option></option>").val(impactTypeArr[i].split('@')[0]).html(impactTypeArr[i].split('@')[1]));
                            }

                        }


                    }
                }//End of RulePanel If
                else {
                    $('#pnlBStoBS').css('display', 'none');
                    $('#pnlInfraToBF').css('display', 'inline');



                }

            }//End of Data If
        }
    });
    $("#RulesCP").css({ 'display': "block" });
    $(".modal").css({ 'display': "block" });
    $(".bg").css({ 'display': "block" });
}


//==============Methods related Rule Telerik windows===============

function isEmpty(val) {
    return (val === undefined || val == null || val.length <= 0) ? false : true;
}
function count_Char(input, compareChar) {
    var counter = 0;

    for (var i = 0; i < input.length; i++) {
        var index_of_sub = input.indexOf(compareChar, i);

        if (index_of_sub > -1) {
            counter++;
            i = index_of_sub;
        }
    }
    return counter;
}
function GetInfraComponent(input) {
    var InfraComp = 0;

    if (isEmpty(input)) {
        var charCount = count_Char(input, '@');
        if (charCount > 0) {
            var strChildresult = input.substring(input.lastIndexOf('@') + 1);
            var strchildArray = strChildresult.split('$');

            if (strchildArray.length > 0) {
                for (var i = 0; i < strchildArray.length; i++) {
                    var indexVal = strchildArray[i];
                    if (isEmpty(indexVal)) {
                        var cCount = count_Char(indexVal, '_');
                        if (cCount > 0) {
                            InfraIDChildNameArr = indexVal.split('_');
                            if (InfraIDChildNameArr.length > 0) {
                                InfraComp = InfraIDChildNameArr[1];
                                break;
                            }
                        }
                    }
                }

            }

        }

    }


    return InfraComp;
}
function GetInfraCompId(input) {
    var InfraCompId = 0;
    var InfraICompIDChildNameArr;
    if (isEmpty(input)) {
        var charCount = count_Char(input, '@');
        if (charCount > 0) {
            var strChildresult = input.substring(input.lastIndexOf('@') + 1);
            var strchildArray = strChildresult.split('$');

            if (strchildArray.length > 0) {
                for (var i = 0; i < strchildArray.length; i++) {
                    var indexVal = strchildArray[i];
                    if (isEmpty(indexVal)) {
                        var cCount = count_Char(indexVal, '_');
                        if (cCount > 0) {
                            InfraICompIDChildNameArr = indexVal.split('_');
                            if (InfraICompIDChildNameArr.length > 0) {
                                InfraCompId = InfraICompIDChildNameArr[1];

                                break;
                            }
                        }
                    }
                }

            }

        }

    }
    return InfraCompId;
}
function GetInfraObjectId(input) {
    var InfraId = 0;
    var InfraIDChildNameArr;
    if (isEmpty(input)) {
        var charCount = count_Char(input, '@');
        if (charCount > 0) {
            var strChildresult = input.substring(input.lastIndexOf('@') + 1);
            var strchildArray = strChildresult.split('$');

            if (strchildArray.length > 0) {
                for (var i = 0; i < strchildArray.length; i++) {
                    var indexVal = strchildArray[i];
                    if (isEmpty(indexVal)) {
                        var cCount = count_Char(indexVal, '_');
                        if (cCount > 0) {
                            InfraIDChildNameArr = indexVal.split('_');
                            if (InfraIDChildNameArr.length > 0) {
                                InfraId = InfraIDChildNameArr[1];

                                break;
                            }
                        }
                    }
                }

            }

        }

    }

    return InfraId;
}
function GetBusinessBFId(input) {
    var BFId = 0;
    var BFIDChildNameArr;
    if (isEmpty(input)) {
        var charCount = count_Char(input, '@');
        if (charCount > 0) {
            var strChildresult = input.substring(input.lastIndexOf('@') + 1);
            var strchildArray = strChildresult.split('$');

            if (strchildArray.length > 0) {
                for (var i = 0; i < strchildArray.length; i++) {
                    var indexVal = strchildArray[i];
                    if (isEmpty(indexVal)) {
                        var cCount = count_Char(indexVal, '_');
                        if (cCount > 0) {
                            BFIDChildNameArr = indexVal.split('_');
                            if (BFIDChildNameArr.length > 0) {
                                BFId = BFIDChildNameArr[1];

                                break;
                            }
                        }
                    }
                }

            }

        }

    }

    return BFId;
}

function GetBusinessBSId(input) {
    var BSId = 0;
    var BSIDChildNameArr;
    if (isEmpty(input)) {
        var charCount = count_Char(input, '@');
        if (charCount > 0) {
            var strChildresult = input.substring(input.lastIndexOf('@') + 1);
            var strchildArray = strChildresult.split('$');

            if (strchildArray.length > 0) {
                for (var i = 0; i < strchildArray.length; i++) {
                    var indexVal = strchildArray[i];
                    if (isEmpty(indexVal)) {
                        var cCount = count_Char(indexVal, '_');
                        if (cCount > 0) {
                            BSIDChildNameArr = indexVal.split('_');
                            if (BSIDChildNameArr.length > 0) {
                                BSId = BSIDChildNameArr[1];

                                break;
                            }
                        }
                    }
                }

            }

        }

    }

    return BSId;
}

