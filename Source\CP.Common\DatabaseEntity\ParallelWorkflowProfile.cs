﻿using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    public class ParallelWorkflowProfile : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraobjectId { get; set; }

        [DataMember]
        public int WorkflowType { get; set; }
        
        [DataMember]
        public string InfraobjectName { get; set; }

        [DataMember]
        public string GroupState { get; set; }

        [DataMember]
        public int ProfileId { get; set; }

        [DataMember]
        public int WorkflowId { get; set; }

        [DataMember]
        public string WorkflowName { get; set; }

        [DataMember]
        public int CurrentActionId { get; set; }

        [DataMember]
        public string CurrentActionName { get; set; }

        [DataMember]
        public WorkflowActionStatus Status { get; set; }

        [DataMember]
        public string Message { get; set; }

        [DataMember]
        public string ProgressStatus { get; set; }

        [DataMember]
        public int ConditionalOperation { get; set; }

        [DataMember]
        public int CheckedStatus { get; set; }

        [DataMember]
        public string NameofTable { get; set; }

        #endregion Properties
    }
}