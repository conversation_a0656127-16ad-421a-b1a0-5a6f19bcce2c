﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="UserConfiguration.aspx.cs" Inherits="CP.UI.UserConfiguration" Title="Continuity Patrol :: User-UserConfiguration" %>

<%@ Register Assembly="MSCaptcha" Namespace="MSCaptcha" TagPrefix="rsv" %>



<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">


    <link href="../App_Themes/CPTheme/jquery.combobox/style.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/jquery.cookie.js"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script src="../Script/jquery.combobox.js" type="text/javascript"></script>
    <script src="../Script/UserConfig.js" type="text/javascript"></script>
    <%-- <script src="../Script/MaskedPassword.js"></script>--%>
    <script src="../Script/EncryptDecrypt.js"></script>
    <style type="text/css">
          .group_validation {
            float:revert; padding-left:10px;
            clear:both;
        }
        .combobox {
            display: block !important;
        }

        div#ctl00_cphBody_dvgrp2, div#ctl00_cphBody_domainUserDiv {
            display: inline-block;
        }

        .setwidth .btn-group .dropdown-menu {
            min-width: 95%;
            right: 19px !important;
        }
    </style>

     <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    
    <script src="../Script/chosen.jquery.js"></script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
       
    </script>
     <style>
        .chosen-select + .chosen-container {
            width: 48.5% !important;
            opacity: 1 !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
            <img src="../Images/user-icon.png">
            User Configuration</h3>

        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-md-12 form-horizontal uniformjs">



                        <ul id="ulMessage" runat="server" visible="false">
                            <li>
                                <asp:Label ID="lblMessage" runat="server" Text=""></asp:Label>
                            </li>
                            <li class="close-bt"></li>
                        </ul>

                        <div class="form-group">
                            <label class="col-md-3 control-label" for="txtDescription">
                                Authentication Type
                            </label>
                            <div class="col-md-9 setwidth">
                                <asp:DropDownList ID="ddlAuthenticatinType" runat="server" AutoPostBack="true" CssClass="chosen-select col-md-6" data-style="btn-default" style="width:48%" OnSelectedIndexChanged="ddlAuthenticatinType_OnSelectedIndexChanged">
                                    <%--<asp:ListItem Value="0">-Select Authentication Type-</asp:ListItem>--%>

                                    <%-- <asp:ListItem Text="Local Authentication" Value="Form Authentication"></asp:ListItem>
                                    <asp:ListItem Text="ActiveDirectory" Value="AD"></asp:ListItem>--%>
                                </asp:DropDownList>

                            </div>
                        </div>

                         <div class="form-group" id="divUserLogin" runat="server" visible="false">
                                    <label class="col-md-3 control-label" for="UsrType">
                                          Login Type </label>
                                    <div class="col-md-9">
                                        <asp:RadioButtonList ID="rdoLogin" runat="server" RepeatDirection="Horizontal" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="userlogintype_selectedindexchanged">
                                            <asp:ListItem  Value="Individual"  Selected="True" >Individual</asp:ListItem>
                                            <asp:ListItem  Value="Group">Group</asp:ListItem>
                                        </asp:RadioButtonList>

                                      </div>
                                </div>                           
                        <div class="" id="divAD" runat="server">

                            <div class="form-group">

                                <asp:Label ID="lblDomainName" CssClass="col-xs-3 control-label nopl" runat="server" Text="Domain Name"></asp:Label>
                                <div class="col-xs-9 padding-left" style="margin-top: -15px;">
                                    <div id="dvgrp3" runat="server" class="input-icon left" style="display: inline-block; vertical-align: sub;">
                                        <span class="glyphicons globe log-ext5"><i></i></span>
                                        <div>
                                            <input id="combobox1" type="text" runat="server" placeholder="Enter Domain" onchange="HideErrorMsg();" />
                                        </div>

                                    </div>
                                    <div id="dvgrp2" runat="server" class="input-icon left" style="display: none; vertical-align: sub;">
                                        <span class="glyphicons globe log-ext5"><i></i></span>
                                        <div>
                                            <input id="combobox2" type="text" runat="server" placeholder="Enter Domain" onchange="HideErrorMsg();" />
                                        </div>

                                    </div>

                                    <input id="btndomainname" class="btn btn-primary" type="button" value="Get Domains" style="padding: 4px 12px;margin-top: 4px;" />
                                </div>
                            </div>
                            <asp:UpdatePanel ID="upsearch" runat="server" UpdateMode="Conditional">
                               
                                <ContentTemplate>
                                    <div id="dvusername1" runat="server" class="row form-group">

                                        <asp:Label ID="Label1" CssClass="col-xs-3 control-label" runat="server">Search String</asp:Label>

<%--           <asp:Label ID="Label2" CssClass="col-xs-3 control-label" runat="server"> Search User Name</asp:Label>--%>

                                        <div class="col-xs-9">

                                            <asp:TextBox ID="txtDomainUserName" Text="" CssClass="form-control" runat="server"></asp:TextBox>

             <%--  <asp:ImageButton ID="btnsearch" runat="server" ImageUrl="~/Images/icons/icon-searcg.png" CausesValidation="false" />--%>
                                              <img id="btnsearch" src="../Images/icons/icon-searcg.png"/>

                                             <span Id="errordomainsearch" class="error group_validation" Visible="true" Text="" ></span>
                                            

                                             <asp:RequiredFieldValidator ID="rqdsearch" ControlToValidate="txtDomainUserName" CssClass="error"
                                     Display="Dynamic"></asp:RequiredFieldValidator>


                                        </div>

                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                            <div class="form-group" id="divusername">

                                <asp:Label ID="lblDomainUserName" CssClass="col-xs-3 control-label" runat="server">Enter User Name</asp:Label>

                                <div class="col-md-9">

                                    <div id="divDomainUser" runat="server" class="input-icon left" style="display: flex; width: 50%; vertical-align: sub;">
                                        <span class="glyphicons user log-ext5" style="margin-top: -6px;"><i></i></span>
                                        <div>
                                            <input id="txtDomainUserNew" type="text" runat="server" placeholder="Enter User Name" onchange="check();" />

                                             <a title="May,Specified access/privilege not available for the logged in domain users please enter the relevant group/user name" class="error" style="align-self: center;">
                                            <img class="vertical-align" src="../Images/icons/icon-tooltipQ.png">
                                        </a>
                                        </div>

                                    </div>

                                    <div id="domainUserDiv" runat="server" class="input-icon left" style="display:flex; width: 50%; vertical-align: sub;">
                                        <span class="glyphicons user log-ext5" style="margin-top: -6px;"><i></i></span>
                                        <div class="newcomboka">
                                            <input id="txtDomainUser" type="text" runat="server" placeholder="Enter User Name" onchange="check();" />

                                              <a title="May,Specified access/privileage not available for the loggedin domain users please enter the relevant group/user name" class="error" style="align-self: center;">
                                            <img class="vertical-align" src="../Images/icons/icon-tooltipQ.png">
                                        </a>
                                        </div>
                                    </div>
                                    <label id="lblerrorMsg" runat="server" class="error" style="display: none;"></label>

                                </div>
                            </div>

                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="txtDescription">
                                Login Name <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <asp:TextBox ID="txtLogin" runat="server" CssClass="form-control" TabIndex="1" AutoPostBack="True"
                                    OnTextChanged="TxtLoginTextChanged" onfocus="clearControl(this)" />
                                <a title=" * Only  & _ @ . - special characters, Numeric & alpha numeric allowed."
                                    class="error">
                                    <img class="vertical-align" src="../Images/icons/icon-tooltipQ.png"></a>
                                <asp:Label ID="lblUserName" runat="server" Visible="false" ForeColor="Red"></asp:Label>
                                <asp:RequiredFieldValidator ID="rqdUsrID" runat="server" ControlToValidate="txtLogin" CssClass="error"
                                    ErrorMessage="Enter Login Name" Display="Dynamic"></asp:RequiredFieldValidator>

                            </div>
                        </div>

                        <asp:UpdatePanel ID="upnlUserConfiguration" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div id="ucpwd" runat="server">
                                    <div class="form-group">

                                        <label class="col-md-3 control-label" for="txtDescription">
                                            Password <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtPwd" runat="server" autocomplete="OFF" CssClass="form-control chk-capsp" TextMode="Password"
                                                TabIndex="2" AutoPostBack="true" OnTextChanged="ValidationCheck"></asp:TextBox>
                                            <%--<onfocus="clearControl(this)">--%>

                                            <a title="" id="passExp" runat="server"
                                                class="error">
                                                <asp:Image ID="img123" CssClass="vertical-align" runat="server" src="../Images/icons/icon-tooltipQ.png" />
                                            </a>
                                            <span class="caps-error">Caps Lock is ON.</span>
                                            <asp:Label ID="lblMatchwithUname" runat="server" Visible="false" CssClass="error" Text=""></asp:Label>
                                            <asp:RequiredFieldValidator ID="rfvPwd" runat="server" ControlToValidate="txtPwd" CssClass="error"
                                                ErrorMessage="Enter Password" Visible="true" Display="Dynamic"></asp:RequiredFieldValidator>

                                            <%--<span class="caps-error">Caps Lock is ON.</span>--%>
                                            <asp:Label ID="lblErr" CssClass="error" runat="server" Visible="false"></asp:Label>

                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="txtDescription">
                                            Confirm Password <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtConPwd" runat="server" autocomplete="OFF" CssClass="form-control chk-capspc" TextMode="Password"
                                                TabIndex="3"></asp:TextBox>
                                            <%-- onfocus="clearControl(this)"--%>
                                            <asp:RequiredFieldValidator ID="rfvConPwd" runat="server" ControlToValidate="txtConPwd" CssClass="error"
                                                ErrorMessage="Enter Confirm Password" Display="Dynamic" Visible="true"></asp:RequiredFieldValidator>

                                            <span class="caps-error">Caps Lock is ON.</span>
                                            <%-- <asp:CompareValidator ID="CompareValidator1" runat="server" ErrorMessage="Password not match" CssClass="error"
                                        Display="Dynamic" ControlToCompare="txtPwd" ControlToValidate="txtConPwd" Visible="true"></asp:CompareValidator>--%>
                                            <asp:Label ID="lblerrorMessage" runat="server" Text="Password not match" CssClass="error" ForeColor="Red"
                                                Visible="false"></asp:Label>
                                        </div>
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                        <h4>User Details</h4>

                        <hr class="separator" />

                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <asp:Timer ID="Timer1" runat="server" Interval="3000" OnTick="tm_Tick" />
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtDescription">
                                        Full Name <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtUserName" runat="server" class="form-control" AutoPostBack="True" TabIndex="4" />
                                        <asp:RequiredFieldValidator ID="rfvUserName" runat="server" ControlToValidate="txtUserName" CssClass="error"
                                            ErrorMessage="Enter Full Name" Display="Dynamic"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="revUserName" runat="server" ControlToValidate="txtUserName"
                                            ErrorMessage="Enter Correct Name" ValidationExpression="^[a-zA-Z0-9 .]{1,100}$" CssClass="error"
                                            Display="Dynamic"></asp:RegularExpressionValidator>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtDescription">
                                        Company <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlCompany" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" OnSelectedIndexChanged="DdlCompanySelectedIndexChanged"
                                            AutoPostBack="true" TabIndex="5">
                                            <asp:ListItem Value="0">Select Company Name</asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvcompamny" runat="server" InitialValue="000" ControlToValidate="ddlCompany"
                                            ErrorMessage="Select Company Name" Display="Dynamic" CssClass="error"></asp:RequiredFieldValidator>
                                        <asp:Label ID="lblcompany" runat="server" ForeColor="red"></asp:Label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtDescription">
                                        Role <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlRole" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" TabIndex="6"
                                            AutoPostBack="True" Enabled="False" OnSelectedIndexChanged="DdlRoleSelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvRole" runat="server" InitialValue="0000" ControlToValidate="ddlRole" CssClass="error"
                                            ErrorMessage="Select Your Role" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>

                                <div class="form-group" runat="server" id="DivCustomSubRoleType" visible="false">
                                    <label class="col-md-3 control-label" for="txtDescription">
                                        Sub Role Type<span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtCustomSubRoleType" runat="server" AutoPostBack="true" class="form-control" TabIndex="8" />
                                        <asp:RequiredFieldValidator ID="rfvsubroletype" runat="server" ControlToValidate="txtCustomSubRoleType" CssClass="error"
                                            ErrorMessage="Enter Sub Role Type" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="cblstGroup">
                                        Assigned InfraObject <span class="inactive">*</span></label>
                                    <div class="col-md-9">

                                        <asp:Panel ID="Panel1" runat="server" ScrollBars="Vertical" Height="101px" class="padding pull-left"
                                            Width="48%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px" TabIndex="7">
                                            <asp:UpdatePanel ID="uplcbsltGroup" runat="server" UpdateMode="Conditional">
                                                <ContentTemplate>
                                                    <asp:CheckBoxList ID="cblstGroup" runat="server" AutoPostBack="True" OnSelectedIndexChanged="CblstGroupSelectedIndexChanged">
                                                    </asp:CheckBoxList>
                                                </ContentTemplate>
                                            </asp:UpdatePanel>
                                        </asp:Panel>

                                        <asp:Label ID="labelSDErrormessage" runat="server" CssClass="error" Visible="false"></asp:Label>
                                        <asp:Label ID="lblInfraUserErrMsg" runat="server" CssClass="error" Visible="false"></asp:Label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtAddress">
                                        Address <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtAddress" runat="server" class="form-control" TextMode="MultiLine" TabIndex="8" />
                                        <asp:RequiredFieldValidator ID="rfvAddress" runat="server" ControlToValidate="txtAddress" CssClass="error"
                                            ErrorMessage="Enter Address" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtPhone">
                                        Phone <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtCountryCode" runat="server" class="form-control" MaxLength="5" Width="7%" TabIndex="9" placeholder="+91"></asp:TextBox>
                                        <asp:TextBox ID="txtStateCode" runat="server" class="form-control" MaxLength="4" Width="10%" TabIndex="10" />
                                        <asp:TextBox ID="txtPhone" runat="server" class="form-control" MaxLength="10" Width="30.3%" TabIndex="11" />
                                        <asp:RequiredFieldValidator ID="rfvphone2" runat="server" ControlToValidate="txtCountryCode" CssClass="error"
                                            Display="Dynamic" ErrorMessage="Enter Country Code"></asp:RequiredFieldValidator>

                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator4" runat="server" CssClass="error"
                                            ControlToValidate="txtCountryCode" Display="Dynamic" ErrorMessage="Valid country code"
                                            ValidationExpression="^[0-9 ,+]+$"></asp:RegularExpressionValidator>

                                        <asp:RequiredFieldValidator ID="rfvphone1" runat="server" ControlToValidate="txtStateCode" CssClass="error"
                                            Display="Dynamic" ErrorMessage="Enter State Code"></asp:RequiredFieldValidator>

                                        <asp:RequiredFieldValidator ID="rfvphone" runat="server" ControlToValidate="txtPhone" CssClass="error"
                                            Display="Dynamic" ErrorMessage="Enter Phone Number"></asp:RequiredFieldValidator>

                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ControlToValidate="txtStateCode" CssClass="error"
                                            Display="Dynamic" ErrorMessage="Valid State Code is required" ValidationExpression="[0-9]{2,4}"></asp:RegularExpressionValidator>

                                        &nbsp;<asp:RegularExpressionValidator ID="RegularExpressionValidator2" runat="server" CssClass="error"
                                            ControlToValidate="txtPhone" Display="Dynamic" ErrorMessage="Valid Phone Number is required"
                                            ValidationExpression="[0-9]{4,10}"></asp:RegularExpressionValidator>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="ChkEmail">
                                        Preferred Mode <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="ChkEmail" runat="server" Text="Email" TabIndex="12" AutoPostBack="true"
                                            Checked="True" OnCheckedChanged="ChkEmailChanged" Enabled="False" />
                                        <asp:CheckBox ID="ChkMobile" runat="server" Text="Mobile" TabIndex="13" AutoPostBack="true"
                                            OnCheckedChanged="ChkMobileChanged" />
                                    </div>
                                </div>

                                <div id="divMobile" visible="false" runat="server">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="txtMCountryCode">
                                            Mobile</label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtMCountryCode" runat="server" class="form-control" MaxLength="5" Width="7%" placeholder="+91"></asp:TextBox>

                                            <asp:TextBox ID="txtMobile" runat="server" class="form-control" MaxLength="10" Width="40.6%" TabIndex="14" />

                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtMCountryCode" CssClass="error"
                                                Display="Dynamic" ErrorMessage="Enter Country Code"></asp:RequiredFieldValidator>
                                            <asp:RegularExpressionValidator ID="RegularExpressionValidator5" runat="server" CssClass="error"
                                                ControlToValidate="txtMCountryCode" Display="Dynamic" ErrorMessage="Valid country code"
                                                ValidationExpression="^[0-9 ,+]+$"></asp:RegularExpressionValidator>

                                            <asp:RequiredFieldValidator ID="rfvmobile" runat="server" ControlToValidate="txtMobile" CssClass="error"
                                                Display="Dynamic" ErrorMessage="Enter Mobile Number"></asp:RequiredFieldValidator>

                                            <asp:RegularExpressionValidator ID="RegularExpressionValidator3" runat="server" ControlToValidate="txtMobile" CssClass="error"
                                                Display="Dynamic" ErrorMessage="Valid Mobile Number is required" ValidationExpression="[0-9]{4,10}"></asp:RegularExpressionValidator>
                                        </div>
                                    </div>
                                </div>

                                <div id="divEmail" runat="server">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="txtEmail">
                                            Email <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtEmail" runat="server" class="form-control" TabIndex="15" CausesValidation="True" />
                                            <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail" CssClass="error"
                                                ErrorMessage="Enter Email-Id" Display="Dynamic"></asp:RequiredFieldValidator>
                                            <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail"
                                                ErrorMessage="Valid email address is required." CssClass="error"
                                                ValidationExpression="^([\w\.\-]+)@([\w\-]+)((\.(\w){2,20})+)$"
                                                Display="Dynamic"></asp:RegularExpressionValidator>
                                        </div>


                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-9">

                                        <asp:CheckBox ID="ckbEmail" runat="server" Text="Send confirmation mail to user" OnCheckedChanged="OnCheckSendMail"
                                            AutoPostBack="true" TextAlign="Right" />
                                        <asp:Label ID="lblMsg" runat="server" Visible="false" CssClass="error"></asp:Label>

                                    </div>
                                </div>
                                <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <div class="form-group">
                                            <label class="col-md-3 control-label margin-top">
                                                Captcha Code  <span class="inactive">*</span></label>
                                            <div class="col-md-9">
                                                <div class="col-md-12 padding-none-LR">
                                                    <rsv:CaptchaControl ID="captcha1" runat="server" CaptchaLength="5" CssClass="pull-left innerB  margin-right"
                                                        CaptchaHeight="50" CaptchaWidth="200" CaptchaLineNoise="None" ForeColor="#00FFCC" CaptchaMinTimeout="5" CaptchaMaxTimeout="240"
                                                        BackColor="White" CaptchaChars="ABCDEFGHIJKLNPQRTUVXYZ12346789"
                                                        FontColor="Darkblue" />
                                                    <asp:LinkButton runat="server" ID="LinkButton2" CssClass="reset-icon margin" CausesValidation="false" ToolTip="Refresh" OnClick="lnkbtnCaptcha_Click"></asp:LinkButton>
                                                </div>
                                                <div class='clearfix'></div>
                                                <asp:TextBox ID="txtcaptcha" runat="server" CssClass="form-control"></asp:TextBox>
                                                <asp:RequiredFieldValidator ID="rfvCaptcha" runat="server" ControlToValidate="txtcaptcha" CssClass="error"
                                                    ErrorMessage="Enter Captcha" Display="Dynamic"></asp:RequiredFieldValidator>
                                                <asp:Label ID="lblerror" runat="server" CssClass="error" Text="" Visible="true"></asp:Label>
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </ContentTemplate>
                        </asp:UpdatePanel>


                        <hr class="separator" />

                        <div class="form-actions row">
                            <div class="col-lg-5">
                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required Fields</span>
                                <asp:Label ID="lblupdatestatus" runat="server" Text=""></asp:Label>
                            </div>
                            <div class="col-lg-7">
                                <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="15%" runat="server" Text="Save" TabIndex="15" Style="margin-left: 5px;"
                                    OnClick="BtnSaveClick" />&nbsp;&nbsp;
                            <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server" Text="Cancel" CausesValidation="False"
                                TabIndex="16" OnClick="BtnCancelClick" />
                            </div>

                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>


    <%--  <script type="text/javascript">


           new MaskedPassword(document.getElementById("ctl00_cphBody_txtPwd"), '\u25CF');
           new MaskedPassword(document.getElementById("ctl00_cphBody_txtConPwd"), '\u25CF');

                        </script>--%>

    <script type="text/javascript">
        $(function () {
            function CheckBoxClicked() {

                var cnt = $("#ChkList input:checked").length;

                $("input:#txtlstPrefModeCnt").val(cnt = 0 ? "" : cnt);
            }

            $(":checkbox").click(CheckBoxClicked);
        })


    </script>

    <script>
        function pageLoad() {
            /*For Password and confirm password field chk-capsp and chk-capspc */
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });

            $(window).bind("capsOn", function (event) {
                if ($(".chk-capspc:focus").length > 0) {
                    $(".chk-capspc").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-capspc").nextAll(".caps-error").hide();
            });
            $(".chk-capspc").bind("focusout", function (event) {
                $(".chk-capspc").nextAll(".caps-error").hide();
            });
            $(".chk-capspc").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-capspc").nextAll(".caps-error").show();
                }
            });
            $(window).capslockstate();

            /*For Password and confirm password field chk-capsp and chk-capspc */


            $(window).bind("capsOn", function (event) {
                if ($(".chk-capsp:focus").length > 0) {
                    $(".chk-capsp").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-capsp").nextAll(".caps-error").hide();
            });
            $(".chk-capsp").bind("focusout", function (event) {
                $(".chk-capsp").nextAll(".caps-error").hide();
            });
            $(".chk-capsp").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-capsp").nextAll(".caps-error").show();
                }
            });
            $(window).capslockstate();
        }
    </script>
</asp:Content>
