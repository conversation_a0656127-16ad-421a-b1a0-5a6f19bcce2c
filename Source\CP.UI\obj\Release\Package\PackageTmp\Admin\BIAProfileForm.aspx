﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="BIAProfileForm.aspx.cs" MasterPageFile="~/Master/BcmsDefault.Master"
    Title="Continuity Patrol :: BIAProfileForm " Inherits="CP.UI.Admin.BIAProfileForm" %>



<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <style type="text/css">
        #ImpactCategory .btn-group.bootstrap-select.col-md-5 {
            width: 90%;
            padding-right: 0px;
            margin-right: 10px;
        }

        .error {
            display: inline-block !important;
        }

        div#ctl00_cphBody_ddlImpactType_chosen {
            width: 90% !important;
            float: left;
            /* margin-bottom: 15px; */
            margin-right: 10px;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function () {
            $("table[id$=Wizard1] > tbody").addClass("widget widget-tabs widget-wizard-pills");
            $("table[id$=Wizard1] > tbody > tr:first-child ").addClass("widget-head");
            $("table[id$=Wizard1] > tbody > tr:nth-child(2)").addClass("widget-body");
            $("#li0").addClass("no-padding");
        });
        var xPos, yPos;
        Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
        Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
        function BeginRequestHandler(sender, args) {
            if ($("#b Scrollbar").length > 0) {
                xPos = window.$get('bScrollbar').scrollLeft;
                yPos = window.$get('bScrollbar').scrollTop;
            }

        }
        function EndRequestHandler(sender, args) {
            if ($("#bScrollbar").length > 0) {
                window.$get('bScrollbar').scrollLeft = xPos;
                window.$get('bScrollbar').scrollTop = yPos;
            }
            $("table[id$=Wizard1] > tbody").addClass("widget widget-tabs widget-wizard-pills");
            $("table[id$=Wizard1] > tbody > tr:first-child ").addClass("widget-head");
            $("table[id$=Wizard1] > tbody > tr:nth-child(2)").addClass("widget-body");
            $("#li0").addClass("no-padding");

        }
        function CancelClick() {
            return false;
        }
    </script>
    <script>

        function openRadWindow(Url, windowName) {
            window.radopen(Url, windowName);
        }
        function openCatRadWindow(Url, windowName) {
            window.radopen(Url, windowName);
        }
        function openSubCatRadWindow(Url) {
            window.radopen(Url, "TelSubCatRadWindow");
        }


    </script>
    <script type="text/javascript">

        function ValidatechkBIAImpact(sender, args) {
            var checkBoxList = document.getElementById("<%=chkBIAImpact.ClientID %>");
            var checkboxes = checkBoxList.getElementsByTagName("input");
            var isValid = false;
            for (var i = 0; i < checkboxes.length; i++) {
                if (checkboxes[i].checked) {
                    isValid = true;
                    break;
                }
            }
            args.IsValid = isValid;

        }

        function RefreshPanelTimeIntervals() {
            <%= Page.ClientScript.GetPostBackEventReference(btnAddNewTimeInterval, String.Empty) %>;
            window["<%= UpdatePanel1.ClientID %>"].submit;
        }

        function RefreshPanelImpactCategory() {
            <%= Page.ClientScript.GetPostBackEventReference(btnAddNewImpactCategory, String.Empty) %>;
            window["<%= UpdatePanel1.ClientID %>"].submit;
        }
        function RefreshPanelImpactMaster() {
            <%= Page.ClientScript.GetPostBackEventReference(btnAddNewImpactSubCategory, String.Empty) %>;
            window["<%= UpdatePanel1.ClientID %>"].submit;
        }

    </script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script>
        $(document).ready(function () {
            $(".grid").mCustomScrollbar({
                axis: "y",
                setHeight: "150px",
            });
        });

        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });

            $(".grid").mCustomScrollbar({
                axis: "y",
                setHeight: "150px",
            });
        }


    </script>
    <telerik:RadWindowManager ID="RadWindowManager1" runat="server" ShowContentDuringLoad="false"
        Skin="WebBlue" IconUrl="~/Images/Telerik_CV_Icon.png">
        <Windows>

            <telerik:RadWindow ID="TelRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false" BackColor="White"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="450" VisibleTitlebar="true" Skin="Metro"
                Width="950" OnClientClose="RefreshPanelTimeIntervals" />


        </Windows>
        <Windows>
            <telerik:RadWindow ID="TelCatRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false" BackColor="White"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="410" VisibleTitlebar="true" Skin="Metro"
                Width="1150" OnClientClose="RefreshPanelImpactCategory" />
        </Windows>
        <Windows>
            <telerik:RadWindow ID="TelSubCatRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false" BackColor="White"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="470" VisibleTitlebar="true" Skin="Metro"
                Width="1150" OnClientClose="RefreshPanelImpactMaster" />
        </Windows>
    </telerik:RadWindowManager>

    <div id="Div1" class="innerLR" runat="server" visible="true">
        <div id="ulMessage" runat="server" visible="false">
            <asp:Label ID="lblMessage" runat="server" Text="ddddd"></asp:Label>
            <span class="close-bt"></span>
        </div>


        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <h3>
                    <img src="../Images/bia-icon.png">
                    Configure FIA Templates
                </h3>

                <asp:Button ID="btnprofileNew" runat="server" CssClass="btn" Width="13%" Text="Create New FIA Template" PostBackUrl="~/Admin/BIAProfileForm.aspx" OnClick="btnprofileNew_Click" Style="font-weight: normal; color: #428BCA; width: 100% !important; padding-left: 3px; text-align: right !important" />

                <asp:Label ID="lblError" runat="server" Text="" ForeColor="red"></asp:Label>
                <div class="widget widget-heading-simple widget-body-white">

                    <div class="widget-body" id="DivProfileList" runat="server">
                        <asp:ListView ID="lvBiaList" runat="server" OnItemDataBound="LvBiaItemDataBound" OnItemCommand="lvBiaList_ItemCommand"
                            OnPreRender="lvBiaListOverviewPreRender" OnItemDeleting="LvBiaDeleteing" OnItemEditing="LvBiaItemEditing">

                            <LayoutTemplate>
                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                                    <thead>
                                        <tr>
                                            <th style="width: 4%;">
                                                <span>
                                                    <img src="../Images/icons/database.png" /></span>
                                            </th>
                                            <th>Template Name
                                            </th>
                                            <th>User Name
                                            </th>
                                            <th>Template In Used
                                            </th>
                                            <th>Template Used By
                                            </th>
                                            <th>Created Date
                                            </th>

                                            <th class="text-center" style="width: 10%">Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                        </tr>
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td class="th table-check-cell">
                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                        <%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td>
                                        <%--  <asp:LinkButton ID="lbltemplateName" runat="server" Text='<%# Eval("ProfileName") %>' CommandName="LoadTemplate" />--%>
                                        <asp:Label ID="lbltemplateName" runat="server" Text='<%# Eval("ProfileName") %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="CriticalTimes" runat="server" Text='<%# Eval("UserName") %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="lbltempInuse" runat="server" Text='<%# GetTemplateInUse(Eval("Id")) %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="lbltempUsedBy" runat="server" Text='<%# GetTemplateUsedBF(Eval("Id")) %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="Type" runat="server" Text='<%# Eval("CreateDate") %>' />
                                    </td>

                                    <td class="text-center" style="width: 10%">
                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                            ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />


                                    </td>

                                </tr>
                                <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ImgDelete"
                                    ConfirmText='<%# "Please confirm if you want to Delete " + Eval("ProfileName") %>'
                                    OnClientCancel="CancelClick">
                                </TK1:ConfirmButtonExtender>
                            </ItemTemplate>
                            <%--   <EditItemTemplate>
                                <tr>
                                    <td class="th table-check-cell">
                                        <asp:Label ID="Label2" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                        <%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td>
                                        <asp:Label ID="application_NAME" runat="server" Text='<%# Eval("ProfileName") %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="CriticalTimes" runat="server" Text='<%# Eval("UserName") %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="lbltempInuse" runat="server" Text='<%# GetTemplateInUse(Eval("Id")) %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="lbltempUsedBy" runat="server" Text='<%# GetTemplateUsedBF(Eval("Id")) %>' />
                                    </td>

                                    <td>
                                        <asp:Label ID="Type" runat="server" Text='<%# Eval("CreateDate") %>' />
                                    </td>

                                    <td class="text-center" style="width: 10%">
                                        <asp:ImageButton ID="ImgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                            ToolTip="Update" ImageUrl="../Images/icons/Update_Icon.png" Width="14px" Style="margin-right: 4px;" />
                                        <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                            ToolTip="Cancel" ImageUrl="../Images/icons/Cancel_Icon.png" Width="12px" />

                                    </td>

                                </tr>

                            </EditItemTemplate>--%>
                            <EditItemTemplate>
                                <tr>
                                    <td>
                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td>
                                        <asp:ImageButton ID="ImgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                            ToolTip="Update" ImageUrl="../Images/icons/arrow-090.png" />
                                        <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                            ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                    </td>
                                </tr>
                            </EditItemTemplate>
                            <EmptyDataTemplate>
                                <tr>
                                    <td>
                                        <asp:Label ID="pageResult" runat="server" Text="No Records found"></asp:Label>
                                    </td>
                                </tr>
                            </EmptyDataTemplate>
                        </asp:ListView>
                        <div class="row">
                            <div class="col-md-6">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvBiaList" PageSize="4">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-md-6 text-right">
                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvBiaList" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText="..." NextPageText="..." ButtonCount="10"
                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                            NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>
                    </div>


                    <div class="widget-body margin-top" id="DivCreatePF" runat="server" visible="false">
                        <div class="row">
                            <div class="col-xs-12 form-horizontal uniformjs">
                                <div class="form-group margin-bottom-none">
                                    <div class="col-md-6">
                                        <asp:Label ID="Label1" runat="server" CssClass="control-label col-md-3 padding-none-LR" Text="Template Name"></asp:Label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtBIAProfile" runat="server"
                                                autocomplete="off" CssClass="form-control pull-left" ValidationGroup="p" Width="89%" />

                                            <br />

                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtBIAProfile" ValidationGroup="p"
                                                ErrorMessage="Enter Template Name" Display="Static" CssClass="error inlineblk"></asp:RequiredFieldValidator>

                                            <asp:RegularExpressionValidator ID="rgcheckName" runat="server" Display="Static" CssClass="error inlineblk"
                                                ControlToValidate="txtBIAProfile" ErrorMessage="InValid Template Name"
                                                ValidationGroup="p" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9_-]+$"></asp:RegularExpressionValidator>
                                        </div>
                                    </div>
                                    <asp:Label ID="lblFIAExists" runat="server" Text="" ForeColor="red"></asp:Label>
                                </div>
                                <div class="form-group margin-bottom-none">
                                    <div class="col-md-6">
                                        <label class="control-label  col-md-3 padding-none-LR" style="padding-right: 0px;">Select Impact Category</label>
                                        <div class="col-md-9" id="ImpactCategory">

                                            <asp:DropDownList ID="ddlImpactType" runat="server" CssClass="chosen-select col-md-5" data-style="btn-default"
                                                OnSelectedIndexChanged="ddlImpactType_onselectedindexchanged" AutoPostBack="true" Style="padding-right: 0px !important">
                                                <asp:ListItem Selected="true" Value="0" Text="Select One"> </asp:ListItem>
                                            </asp:DropDownList>
                                            <asp:LinkButton ID="btnAddNewImpactCategory" runat="server" CssClass="plus margin-right pull-left margin-top" ToolTip="Add New Category "
                                                OnClientClick="openCatRadWindow('../ImpactAnalysis/ImpactTypeMaster.aspx', 'TelCatRadWindow');" OnClick="btnAddNewImpactCategory_Click" />
                                            <br />
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator3" CssClass="error inlineblk" runat="server" InitialValue="0"
                                                ValidationGroup="p" ControlToValidate="ddlImpactType"
                                                ErrorMessage="Select Impact Category">
                                            </asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-md-6">
                                        <label class="control-label  col-md-3 padding-none-LR">Select Impact Type</label>

                                        <div class="col-md-9">
                                            <asp:Panel ID="Panel3" runat="server" CssClass="grid padding col-md-9 margin-right"
                                                Width="89%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px">
                                                <asp:CheckBoxList ID="chkImpactTypemaster" runat="server" OnSelectedIndexChanged="chkImpactTypemaster_SelectedIndexChanged" AutoPostBack="true">
                                                </asp:CheckBoxList>
                                            </asp:Panel>
                                            <asp:LinkButton ID="btnAddNewImpactSubCategory" runat="server" CssClass="plus margin-right pull-left" ToolTip="Add New Impact Type "
                                                OnClientClick="javascript:void(0)" OnClick="btnAddNewImpactSubCategory_Click" />
                                            <%--   <asp:CustomValidator ID="cvImpactMaster" runat="server" CssClass="error" ValidationGroup="p"
                                                OnServerValidate="cvImpactMaster_ServerValidate" Display="Dynamic"
                                                ErrorMessage="Select at least one Impact ">
                                            </asp:CustomValidator>--%>
                                            <asp:Label runat="server" ID="lblMessage5" CssClass="error" />
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="control-label  col-md-3">Select Interval</label>

                                        <div class="col-md-9">
                                            <asp:Panel ID="Panel2" runat="server" CssClass="grid padding margin-right pull-left"
                                                Width="89%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px">
                                                <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <asp:CheckBoxList ID="chkBIAImpact" runat="server" AutoPostBack="true"
                                                            OnSelectedIndexChanged="chklstBIAImpact_SelectedIndexChanged">
                                                        </asp:CheckBoxList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </asp:Panel>
                                            <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional">
                                                <ContentTemplate>
                                                    <asp:LinkButton ID="btnAddNewTimeInterval" runat="server" CssClass="plus margin-right pull-left" ToolTip="Add New "
                                                        OnClientClick="openRadWindow('BIATimeInterval.aspx', 'TelRadWindow');" OnClick="btnAddNewTimeInterval_Click" />
                                                    <asp:Label runat="server" ID="lblMessage6" CssClass="error" />
                                                    <asp:HiddenField ID="hdnt" runat="server" Value="" />
                                                    <asp:Label runat="server" ID="lblMessage4" CssClass="error" />
                                                    <asp:Label runat="server" ID="lblMessage3" CssClass="error" />
                                                </ContentTemplate>
                                            </asp:UpdatePanel>
                                        </div>

                                    </div>

                                </div>





                            </div>
                        </div>

                        <div class="row" id="divImpactListGrid" runat="server" style="padding-bottom: 10px;">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <asp:UpdatePanel ID="udplBussinessfunctionBIA" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <telerik:RadGrid ID="ImpactList" runat="server" AllowPaging="False" Font-Size="Small" ViewStateMode="Enabled" AllowSorting="false" AllowAutomaticInserts="false" AllowAutomaticDeletes="false"
                                            AutoGenerateColumns="false" AllowAutomaticUpdates="false" Skin="Default" AllowFilteringByColumn="false"
                                            AllowCustomPaging="false" CellSpacing="0" GridLines="None" Enabled="False">
                                        </telerik:RadGrid>

                                    </ContentTemplate>
                                </asp:UpdatePanel>
                                <div class="clearfix"></div>




                                <asp:Label runat="server" ID="lblCount" CssClass="text-success" />
                                <asp:Label runat="server" ID="lblMessage2" CssClass="error" />
                            </div>
                        </div>
                        <hr class="separator" />
                        <div class="form-actions row">
                            <div class="col-md-9">

                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required Fields</span>


                            </div>
                            <div class="col-md-3">

                                <asp:Button ID="btnPSave" CssClass="btn btn-primary" runat="server" Text="Save" OnClick="BtnPSaveClick" ValidationGroup="p" Style="width: 32%; margin-left: 30px;" />
                                <asp:Button ID="btnCancel" CssClass="btn btn-default" runat="server" Text="Cancel" CausesValidation="False" EnableViewState="False" OnClick="BtnCancelClick" Style="width: 32%" />

                                <asp:Button ID="btnToReloadImpactDDL" CssClass="btn btn-primary" runat="server" Text="Save" Visible="false" OnClick="btnToReloadImpactDDL_Click" />

                            </div>
                        </div>

                    </div>




                </div>

            </ContentTemplate>
            <Triggers>
            </Triggers>
        </asp:UpdatePanel>
    </div>

</asp:Content>
