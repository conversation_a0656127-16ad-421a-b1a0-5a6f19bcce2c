﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsLogin.Master" AutoEventWireup="true"
    CodeBehind="ManageCP.aspx.cs" Inherits="CP.UI.ManageCP" Title="Manage ContinuityPatrol" %>

<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <link href="Images/CP_Favicon.ico" rel="shortcut icon" type="image/icon" />

    <link rel="stylesheet/less" href="App_Themes/CPTheme/CPMaster.less" />
    <link href="App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="Script/jquery-migrate.min.js"></script>
    <script src="Script/modernizr.js"></script>
    <script src="Script/less.min.js"></script>
    <script src="Script/ie.prototype.polyfill.js"></script>
    <script src="Script/html5shiv.js"></script>
    <script type="text/javascript" src="Script/jquery-ui.min.js "></script>
    <link href="App_Themes/CPTheme/CPMaster.less" rel="stylesheet" />
    <link href="App_Themes/CPTheme/core/less/modals.less" rel="stylesheet" />
    <link href="App_Themes/CPTheme/core/less/timeline.less" rel="stylesheet" />
    <script src="Script/jquery.modal.js"></script>
    <link href="App_Themes/CPTheme/bootstrap.min.css" rel="stylesheet" />
    <link href="App_Themes/CPTheme/jquery.combobox/style.css" rel="stylesheet" />
    <script src="Script/jquery-migrate.min.js"></script>
    <script src="Script/less.min.js"></script>
    <script src="Script/Custom-chkbox-rdbtn.js"></script>
    <script type="text/javascript" src="Script/jquery.combobox.js"></script>
    <script src="Script/jquery.AssignZoneModal.js" type="text/javascript"></script>
    <script src="Script/jquery.modal.js" type="text/javascript"></script>
    <script src="Script/bootstrap.min.js"></script>
    <script src="Script/jquery.slimscroll.min.js"></script>
    <script src="Script/jquery.cookie.js"></script>
    <script src="Script/bootstrap-select.js"></script>
    <script src="Script/bootstrap-select.init.js"></script>
    <script src="Script/Custom-chkbox-rdbtn.js"></script>
    <script src="Script/core.init.js"></script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">


    <div style="height: 50px; background-color: #4a8bc2; border-bottom: 1px solid #346d9d;">
        <div class="row">
            <div class="col-md-6 text-left">
                <img src="Images/ContinuityPatrol_logo.png" style="margin-left: 15px; height: 25px; margin-top: 13px;" />
            </div>
            <div class="col-md-6 text-right">
                <img src="Images/pte-logo.png" style="margin-right: 15px; height: 25px; margin-top: 13px;" />
            </div>
        </div>
    </div>


    <div class="innerLR" style="width: 90%; margin-left: 5%;">
        <h3>
            <img src="Images/icons/UnlockUsers_Icon.png">
            Unlock Super Users</h3>

        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <asp:ListView ID="lvUser" runat="server" OnItemEditing="lvUser_ItemEditing" OnPreRender="lvUser_PreRender" OnItemDataBound="lvUser_ItemDataBound" OnItemCommand="lvUser_ItemCommand">
                            <LayoutTemplate>
                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                                    <thead>
                                        <tr>
                                            <th style="width: 4%;">No.
                                            </th>
                                            <th>User Name
                                            </th>
                                            <th>User Role
                                            </th>
                                            <th>Status
                                            </th>
                                            <th>Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <EmptyDataTemplate>
                                <div class="message warning align-center bold">
                                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                </div>
                            </EmptyDataTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td>
                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                        <asp:Label ID="lblUserID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                    </td>
                                    <td>
                                        <asp:Label ID="lblLoginName" runat="server" Text='<%# Eval("LoginName") %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="lblUserRole" runat="server" Text='<%# Eval("Role") %>' />
                                    </td>
                                    <td>
                                        <asp:Label ID="lblStatus" runat="server" Text='<%# Eval("IsActive") %>' />
                                    </td>
                                    <td>
                                        <asp:ImageButton ID="ImgUnlock" runat="server" CommandName="Click" AlternateText="Unlock"
                                            ToolTip="Unlock" ImageUrl="Images/icons/unlock.png" />
                                    </td>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                        <div class="row">
                            <div class="col-md-6">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvUser">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                                    <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                    Out Of
                                                    <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-md-6 text-right">
                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvUser" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                            NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>

                    </div>
                </div>
                <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
                </asp:Panel>
                <asp:Panel ID="pnlUnlock" runat="server" Width="100%" Visible="false">
                    <div class="modal" style="display: block; z-index: 9999999;">
                        <div class="modal-dialog" style="width: 635px;">
                            <div class="modal-content  widget-body-white">

                                <div class="modal-header">
                                    <h3 class="modal-title">Unlock User</h3>
                                    <asp:LinkButton ID="LkbtncloseUnlockUser" runat="server" ToolTip="Close window" OnClick="LkbtnCloseUnlockUser_Click"
                                        CausesValidation="False" class="close" CommandName="Close">x</asp:LinkButton>
                                </div>
                                <br />
                                <div class="modal-body">
                                    <asp:UpdatePanel ID="pnlUpdate_unlockUser" runat="server" UpdateMode="Conditional">
                                        <ContentTemplate>
                                            <div class="row">
                                                <div class="col-md-12 form-horizontal uniformjs">
                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label ">
                                                            <asp:HiddenField ID="hdnUserId" runat="server" Value="" />
                                                            <asp:HiddenField ID="hdnUserName" runat="server" Value="" />
                                                            <asp:Label ID="lblUserCredential" runat="server" Text="User Credential"></asp:Label>
                                                            <span class="inactive">*</span>
                                                        </label>
                                                        <div class="col-md-8">
                                                            <asp:TextBox ID="txtUserCredential" runat="server" CssClass="form-control" CausesValidation="True" Width="100%"></asp:TextBox>
                                                            <asp:RequiredFieldValidator ID="rfvsmtphost" runat="server" ErrorMessage="Please Enter User Credential" ControlToValidate="txtUserCredential"
                                                                Display="Dynamic" CssClass="error" SetFocusOnError="true" ValidationGroup="validateUnlock"></asp:RequiredFieldValidator>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <div class="col-md-4"></div>
                                                        <div class="col-xs-8 text-right">
                                                            <asp:Button ID="btnUnlock" runat="server" Text="Submit" CssClass="btn btn-primary" Width="20%"
                                                                OnClick="BtnUnlock_Click" ValidationGroup="validateUnlock" Visible="True" />
                                                            <asp:Button ID="btnClose" runat="server" Text="Close" CssClass="btn btn-default"
                                                                Width="20%" CausesValidation="False" OnClick="btnClose_Click" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </div>
                                <asp:Label ID="lblUnlockUserMsg" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>
                                <br />
                                <div class="modal-footer" style="width: 100%">
                                    <div class="text-left">
                                        <span class="bold">Note: </span>
                                        <br />
                                        Please contact Perpetuuiti Technosoft administrator to get user credential to unlock the user
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </asp:Panel>

            </ContentTemplate>
        </asp:UpdatePanel>



        <script type="text/javascript">

            $('[id$=divClass]').click(function () {
                $(this).fadeOut("slow");
            });

        </script>
    </div>
</asp:Content>
