﻿
function RequireField(id) {
    if (!$("#" + id).hasClass("ignipt")) {
        var txt = $("#" + id).val();
        var span = $("#" + id).next();

        if (id != undefined) {
            if (id.match("_chosen$")) {
                txt = $("#" + id.replace('_chosen', '')).val();
                span = $("#" + id.replace('_chosen', '')).next().next();
            }
        }
        if (txt == "000") {
            txt = "";
        }
        txt = jQuery.trim(txt);
        var length = txt.length;

        if (length == 0) {

            $(span).html("*");
            $(span).show();
            $(span).attr("class", "error");
            return true;
        }
        else {
            span = $("#" + id).next();
            $(span).hide();
            $(span).removeClass('error');
            return false;
        }
    }
}

function CheckRange(id, min, max) {

    min = parseInt(min);
    max = parseInt(max);

    var span = $("#" + id).next();
    var span1 = $(span).next();
    var txt = $("#" + id).val();

    $(span).hide();
    $(span1).hide();

    $(span1).attr("class", "error");

    txt = jQuery.trim(txt);
    var length = txt.length;
    if (length > 0) {
        if (txt > min && txt < max) {
            $(span1).hide();
        }
        else {
            $(span1).attr("class", "error");
            $(span1).show();
            return 0;
        }
    }
    else {
        $(span).show();
    }
    return 0;
}


function CheckOnlyChar(id) {

    var span = $("#" + id).next();
    var span1 = $(span).next();
    var txt = $("#" + id).val();

    $(span).hide();
    $(span1).hide();
    $(span1).attr("class", "error");

    txt = jQuery.trim(txt);
    var length = txt.length;
    if (length > 0) {
        var loginName = /^[a-zA-Z''-'\s]{5,15}$/;
        if (txt.search(loginName) == -1) {
            $(span1).attr("class", "error");
            $(span1).show();
        }
        else {
            $(span1).hide();
        }
    }
    else {
        $(span).show();
    }
}

function AlphaNumeric(id) {

    var span = $("#" + id).next();
    var span1 = $(span).next();
    var txt = $("#" + id).val();

    $(span).hide();
    $(span1).hide();
    $(span1).attr("class", "error");

    txt = jQuery.trim(txt);
    var length = txt.length;
    if (length > 0) { //''-'\s
        var alphaNumeric = /^[a-zA-Z0-9''-_'\s]{3,35}$/;

        if (txt.search(alphaNumeric) == -1) {
            $(span1).attr("class", "error");
            $(span1).show();
            return false;
        }
        else {
            $(span1).hide();
            return true;
        }
    }
    else {
        $(span).show();
        return false;
    }
}


function CheckEmail(id) {

    var span = $("#" + id).next();
    var span1 = $(span).next();
    var txt = $("#" + id).val();

    $(span).hide();
    $(span1).hide();
    $(span1).attr("class", "error");

    txt = jQuery.trim(txt);
    var length = txt.length;
    if (length > 0) {
        var email = /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        if (txt.search(email) == -1) {
            $(span1).attr("class", "error");
            $(span1).show();
        }
        else {
            $(span1).hide();
        }
    }
    else {
        $(span).show();
    }
}

function CheckMobileNo(id) {
    var span = $("#" + id).next();
    var span1 = $(span).next();
    var txt = $("#" + id).val();

    $(span).hide();
    $(span1).hide();
    $(span1).attr("class", "error");

    txt = jQuery.trim(txt);
    var length = txt.length;
    if (length > 0) {
        var mobileNo = /^\d{10}$/;
        if (txt.search(mobileNo) == -1) {
            $(span1).show();
        }
        else {
            $(span1).hide();
        }
    }
    else {
        $(span).show();
    }
}

function isNumeric(id) {
    var span = $("#" + id).next();
    var span1 = $(span).next();
    var txt = $("#" + id).val();
    $(span).hide();
    $(span1).hide();
    $(span1).attr("class", "error");

    txt = jQuery.trim(txt);
    var length = txt.length;
    if (length > 0) {
        var numericExpression = /^[0-9]+$/;
        if (txt.match(numericExpression)) {
            $(span1).hide();
        }
        else {
            $(span1).show();
            $(span1).html("Only integer allowed");
        }
        var i = parseInt(txt, 10);

        if (i == 0) {
            $(span1).show();
            $(span1).html("Only non zero allowed");
        }
    }
    else {
        $(span).show();
    }
}

function RequireDropDown(id) {
    var val = $("[id$=" + id + "] option:selected").val();

    var span = $("#" + id).next();
    if ($("#" + id).hasClass("chosen-select"))
        span = $("#" + id).next().next();

    if (val == "000") {
        $(span).show();
        $(span).html("*");
        $(span).attr("class", "error");
    }
    else {
        $(span).removeClass('error');
        $(span).hide();
    }
    $(".chosen-select").next("div").show();
}

function SpecialCharacter(id) {
    var user = /[(\*\(\)\[\]\+\.\,\/\}\{\?\:\;\'\"\`\~\\#\$\%\^\&\<\>\@\!)+]/;
    var span1 = $("#" + id).next();
    var span = $(span1).next();
    var value = $("[id$=" + id + "]").val().match(user);
    if (value != null) {
        $(span).show();
        $(span).html("No Special Character");
        $(span).attr("class", "error");
        return false;
    } else {
        $(span).hide();
        return true;
    };
}

function Pathvalidation(id) {
    var user = /[(\*\(\)\[\]\+\.\,\{\}\?\;\'\"\`\~\\#\$\%\^\&\<\>\@\!)+]/;
    var span1 = $("#" + id).next();
    var span = $(span1).next();
    var value = $("[id$=" + id + "]").val().match(user);
    if (value != null) {
        $(span).show();
        $(span).html("No Special Character");
        $(span).attr("class", "error");
        return false;
    } else {
        $(span).hide();
        return true;
    };
}