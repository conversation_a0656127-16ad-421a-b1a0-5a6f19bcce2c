namespace CP.UI.Report.TelerikReports
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Configuration;
    using System.Data;
    using System.Drawing;
    using System.IO;
    using System.Linq;
    using System.Web;
    using System.Web.UI;
    using System.Web.UI.WebControls;
    using CP.Common.DatabaseEntity;
    using CP.ExceptionHandler;
    using Gios.Pdf;
    using log4net;
    using SpreadsheetGear;
    using CP.Common.Shared;
    using CP.Common;


    using CP.BusinessFacade;


    /// <summary>
    /// Summary description for DataLag24HrsCurrentDataLag.
    /// </summary>
    public partial class DataLag24HrsCurrentDataLag : Telerik.Reporting.Report
    {
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();
        private readonly ILog _logger = LogManager.GetLogger(typeof(DataLag24HrsCurrentDataLag));
        private List<ExChangMntrStats> ExchangeDaglist = new List<ExChangMntrStats>();
        //private List<ExchangeDAGComponantMonitor> ExchangeDaglist = new List<ExchangeDAGComponantMonitor>();
        //  private ExchangeDAGComponantMonitor datalagExcDag;
        private ExChangMntrStats datalagExcDag;
        private List<OracleLog> datalaglist = new List<OracleLog>();
        private List<SqlNativeMonitor> nativelaglist = new List<SqlNativeMonitor>();
        private List<MySqlReplication> mysqllist = new List<MySqlReplication>();
        private List<ExchangeHealth> exchangelist = new List<ExchangeHealth>();
        private List<VmwareMonitor> vmwarelist = new List<VmwareMonitor>();
        private List<SybaseWithSRSMonitor> sybaseWithSRSMonitor = new List<SybaseWithSRSMonitor>();
        private List<MaxDBReplication> maxDBReplList = new List<MaxDBReplication>();
        private List<HADR> hadrlaglist = new List<HADR>();
        private List<SyBaseMonitor> sybaselist = new List<SyBaseMonitor>();
        private List<MimixDatalag> mimixlistlist = new List<MimixDatalag>();
        private OracleCloudDataGuard_DBSystemDetails _OcdgDatalag;
        private OC_ExaDb_Monitor _OcExaDatalag;
        private OracleLog _dataLag;
        private AodgRepliLogsDetails _Aodgdatalag;
        private Postgre9xMonitorStatus _postgre9xdblag;
        private SqlNativeMonitor _sqlLag;
        private ExchangeHealth _exchangeLag;
        private MSSqlDoubletek _mssqldblLag;
        private VMWareNetSnapMirrorComponentMonitor _netappsnapmirror;
        private SRMVmwareMonitor _srmvmwareLag;
        private VmwareMonitor _vmwareLag;
        private SybaseWithSRSMonitor _sybaseSrsLag;
        private MaxDBReplication _maXDBRepliLag;
        private EMCSRDF _emcLag;
        private HADR _hadrLag;
        private RecoverPStatisticMonitor _recmoniLag;
        private eBDRProfileReplication _eBDRProfileLag;
        private MSSQLAlwaysOnServerMoniter _MsqlAlwaysonProfileLag;
        private SVCGlobalMirrorMonitor _SvcGlobalMoniLag;
        private MSSQLDBMirrorReplicationMonitor _MSSQLDBMirrorMoniLag;
        private ActiveODGReplicationNonOdg _AodgNonOdgdatalag;
        private MySqlReplication _mysqlLag;
        private MimixDatalag _mimixLag;
        private EmcUnity_Repli_Monitor emcunityLag;
        private SybaseWithRSHADR_RepliMonitorNew _sybaseLag;
        private HanaDBMonitor HanaDBMonitorlag;
        private GoldenGateReplicationMonitor _GoldenGatelag;
        private HyperVDetails _HyperVDetailsLag;
        private RLinkMonitorSecUpdate _rLinkMoni;
        private RSyncMonitor _rsyncMonitor;
        private HP3PAR_Monitor _Hp3_parMoni;
        private NTNXLeapRcblEntReplMonitor _nutanixleap;
        private ActiveDirectoryMonitor _ADMonitor;
        private RoboCopyLogs _RoboCopyLogs;
        private Azure_Monitoring _azureLag;
        private zertomonitorstatus _zertovenb;
        private EMCSRDFCGMonitoring _emcsrdfcgMonitor;
        private MongoDBDMonitorStatus mongodb_Moni;
        private AzureCosmosDBMonitor _AzureCosmosDBMonitor;
        private string prevAppName = string.Empty;
        private string currAppName = string.Empty;
        private VsphereMonitorReport _vsphereLag;


        #region Properties


        private AzureCosmosDBMonitor CurrenteAzureCosmosDBMonitor
        {
            get
            {
                if (_AzureCosmosDBMonitor == null)
                    _AzureCosmosDBMonitor = new AzureCosmosDBMonitor();

                return _AzureCosmosDBMonitor;
            }
            set
            {
                _AzureCosmosDBMonitor = value;
            }
        }
        private MongoDBDMonitorStatus CurrentMongoRMoni
        {
            get
            {
                if (mongodb_Moni == null)
                    mongodb_Moni = new MongoDBDMonitorStatus();

                return mongodb_Moni;
            }
            set
            {
                mongodb_Moni = value;
            }
        }


        private EMCSRDFCGMonitoring CurrentEMCSRDFCGMonitorLag
        {
            get
            {
                if (_emcsrdfcgMonitor == null)
                    _emcsrdfcgMonitor = new EMCSRDFCGMonitoring();

                return _emcsrdfcgMonitor;
            }
            set
            {
                _emcsrdfcgMonitor = value;
            }
        }

        private zertomonitorstatus CurrentZertoMonitorLog
        {
            get
            {
                if (_zertovenb == null)
                {
                    _zertovenb = new zertomonitorstatus();
                }

                return _zertovenb;
            }
            set
            {
                _zertovenb = value;
            }

        }
        private RoboCopyLogs CurrentRoboLag
        {
            get
            {
                if (_RoboCopyLogs == null)
                    _RoboCopyLogs = new RoboCopyLogs();

                return _RoboCopyLogs;
            }
            set
            {
                _RoboCopyLogs = value;
            }
        }

        private ActiveDirectoryMonitor CurrentADMonitorLag
        {
            get
            {
                if (_ADMonitor == null)
                    _ADMonitor = new ActiveDirectoryMonitor();

                return _ADMonitor;
            }
            set
            {
                _ADMonitor = value;
            }
        }


        private NTNXLeapRcblEntReplMonitor CurrentNutanixLeapRcbl
        {
            get
            {
                if (_nutanixleap == null)
                    _nutanixleap = new NTNXLeapRcblEntReplMonitor();

                return _nutanixleap;
            }
            set
            {
                _nutanixleap = value;
            }
        }

        private RSyncMonitor CurrentRsyncMonitorLag
        {
            get
            {
                if (_rsyncMonitor == null)
                    _rsyncMonitor = new RSyncMonitor();

                return _rsyncMonitor;
            }
            set
            {
                _rsyncMonitor = value;
            }
        }

        private RLinkMonitorSecUpdate CurrentRLinkMoni
        {
            get
            {
                if (_rLinkMoni == null)
                    _rLinkMoni = new RLinkMonitorSecUpdate();

                return _rLinkMoni;
            }
            set
            {
                _rLinkMoni = value;
            }
        }

        private HyperVDetails CurrentHyperVDetailsLog
        {
            get
            {
                if (_HyperVDetailsLag == null)
                {
                    _HyperVDetailsLag = new HyperVDetails();
                }

                return _HyperVDetailsLag;
            }
            set
            {
                _HyperVDetailsLag = value;
            }
        }

        private HP3PAR_Monitor CurrentHP3PARMoni
        {
            get
            {
                if (_Hp3_parMoni == null)
                    _Hp3_parMoni = new HP3PAR_Monitor();

                return _Hp3_parMoni;
            }
            set
            {
                _Hp3_parMoni = value;
            }
        }

        private GoldenGateReplicationMonitor CurrentGoldenGateLag
        {
            get
            {
                if (_GoldenGatelag == null)
                    _GoldenGatelag = new GoldenGateReplicationMonitor();

                return _GoldenGatelag;
            }
            set
            {
                _GoldenGatelag = value;
            }
        }

        private HanaDBMonitor CurrentHanaDBMonitorLag
        {
            get
            {
                if (HanaDBMonitorlag == null)
                    HanaDBMonitorlag = new HanaDBMonitor();

                return HanaDBMonitorlag;
            }
            set
            {
                HanaDBMonitorlag = value;
            }
        }

        private SRMVmwareMonitor CurrentSrmVmwareLag
        {
            get
            {
                if (_srmvmwareLag == null)
                {
                    _srmvmwareLag = new SRMVmwareMonitor();
                }

                return _srmvmwareLag;
            }
            set
            {
                _srmvmwareLag = value;
            }
        }
        private Postgre9xMonitorStatus CurrentPostgre9xlag
        {
            get
            {
                if (_postgre9xdblag == null)
                {
                    _postgre9xdblag = new Postgre9xMonitorStatus();
                }

                return _postgre9xdblag;
            }
            set
            {
                _postgre9xdblag = value;
            }
        }
        private OracleLog CurrentDatalag
        {
            get
            {
                if (_dataLag == null)
                {
                    _dataLag = new OracleLog();
                }

                return _dataLag;
            }
            set
            {
                _dataLag = value;
            }
        }

        private OracleCloudDataGuard_DBSystemDetails OcdgCurrentDatalag
        {
            get
            {
                if (_OcdgDatalag == null)
                {
                    _OcdgDatalag = new OracleCloudDataGuard_DBSystemDetails();
                }

                return _OcdgDatalag;
            }
            set
            {
                _OcdgDatalag = value;
            }
        }

        private OC_ExaDb_Monitor OcExaCurrentDatalag
        {
            get
            {
                if (_OcExaDatalag == null)
                {
                    _OcExaDatalag = new OC_ExaDb_Monitor();
                }

                return _OcExaDatalag;
            }
            set
            {
                _OcExaDatalag = value;
            }
        }

        private AodgRepliLogsDetails AodgCurrentDatalag
        {
            get
            {
                if (_Aodgdatalag == null)
                {
                    _Aodgdatalag = new AodgRepliLogsDetails();
                }

                return _Aodgdatalag;
            }
            set
            {
                _Aodgdatalag = value;
            }
        }

        private ExChangMntrStats CurrentDatalagExcDag
        {
            get
            {
                if (datalagExcDag == null)
                {
                    datalagExcDag = new ExChangMntrStats();
                }

                return datalagExcDag;
            }
            set
            {
                datalagExcDag = value;
            }
        }


        //private ExchangeDAGComponantMonitor CurrentDatalagExcDag
        //{
        //    get
        //    {
        //        if (datalagExcDag == null)
        //        {
        //            datalagExcDag = new ExchangeDAGComponantMonitor();
        //        }

        //        return datalagExcDag;
        //    }
        //    set
        //    {
        //        datalagExcDag = value;
        //    }
        //}

        private MSSqlDoubletek CurrentMSSQLDblLog
        {
            get
            {
                if (_mssqldblLag == null)
                {
                    _mssqldblLag = new MSSqlDoubletek();
                }

                return _mssqldblLag;
            }
            set
            {
                _mssqldblLag = value;
            }
        }
        private SqlNativeMonitor CurrentSqlNativeLog
        {
            get
            {
                if (_sqlLag == null)
                {
                    _sqlLag = new SqlNativeMonitor();
                }

                return _sqlLag;
            }
            set
            {
                _sqlLag = value;
            }
        }
        private ExchangeHealth CurrentExchangelag
        {
            get
            {
                if (_exchangeLag == null)
                {
                    _exchangeLag = new ExchangeHealth();
                }
                return _exchangeLag;
            }
            set
            {
                _exchangeLag = value;
            }
        }
        private VmwareMonitor CurrentVmwareLag
        {
            get
            {
                if (_vmwareLag == null)
                    _vmwareLag = new VmwareMonitor();

                return _vmwareLag;
            }
            set
            {
                _vmwareLag = value;
            }
        }
        private MaxDBReplication CurrentMaxDBReplicationLag
        {
            get
            {
                if (_maXDBRepliLag == null)
                    _maXDBRepliLag = new MaxDBReplication();

                return _maXDBRepliLag;
            }
            set
            {
                _maXDBRepliLag = value;
            }
        }
        private SybaseWithSRSMonitor CurrentSybseWithSRSLog
        {
            get
            {
                if (_sybaseSrsLag == null)
                    _sybaseSrsLag = new SybaseWithSRSMonitor();

                return _sybaseSrsLag;
            }
            set
            {
                _sybaseSrsLag = value;
            }
        }
        private EMCSRDF CurrentEmcLag
        {
            get
            {
                if (_emcLag == null)
                    _emcLag = new EMCSRDF();

                return _emcLag;
            }
            set
            {
                _emcLag = value;
            }
        }
        private RecoverPStatisticMonitor Currentrecmoni
        {
            get
            {
                if (_recmoniLag == null)
                    _recmoniLag = new RecoverPStatisticMonitor();

                return _recmoniLag;
            }
            set
            {
                _recmoniLag = value;
            }
        }
        private HADR CurrentHadrLag
        {
            get
            {
                if (_hadrLag == null)
                    _hadrLag = new HADR();

                return _hadrLag;
            }
            set
            {
                _hadrLag = value;
            }
        }
        private eBDRProfileReplication CurrenteBDRPLag
        {
            get
            {
                if (_eBDRProfileLag == null)
                    _eBDRProfileLag = new eBDRProfileReplication();

                return _eBDRProfileLag;
            }
            set
            {
                _eBDRProfileLag = value;
            }
        }
        private MSSQLAlwaysOnServerMoniter CurrenteAlwaysonLag
        {
            get
            {
                if (_MsqlAlwaysonProfileLag == null)
                    _MsqlAlwaysonProfileLag = new MSSQLAlwaysOnServerMoniter();

                return _MsqlAlwaysonProfileLag;
            }
            set
            {
                _MsqlAlwaysonProfileLag = value;
            }
        }
        private SVCGlobalMirrorMonitor CurrentSvcGlobalMoni
        {
            get
            {
                if (_SvcGlobalMoniLag == null)
                {
                    _SvcGlobalMoniLag = new SVCGlobalMirrorMonitor();
                }
                return _SvcGlobalMoniLag;
            }
            set
            {
                _SvcGlobalMoniLag = value;
            }
        }
        private MSSQLDBMirrorReplicationMonitor CurrentMSSQLDBMirrorMoni
        {
            get
            {
                if (_MSSQLDBMirrorMoniLag == null)
                {
                    _MSSQLDBMirrorMoniLag = new MSSQLDBMirrorReplicationMonitor();
                }
                return _MSSQLDBMirrorMoniLag;
            }
            set
            {
                _MSSQLDBMirrorMoniLag = value;
            }
        }
        private VMWareNetSnapMirrorComponentMonitor Currentnetappsnapmirror
        {
            get
            {
                if (_netappsnapmirror == null)
                {
                    _netappsnapmirror = new VMWareNetSnapMirrorComponentMonitor();
                }
                return _netappsnapmirror;
            }
            set
            {
                _netappsnapmirror = value;
            }
        }

        private ActiveODGReplicationNonOdg AodgNonODGCurrentDatalag
        {
            get
            {
                if (_AodgNonOdgdatalag == null)
                {
                    _AodgNonOdgdatalag = new ActiveODGReplicationNonOdg();
                }

                return _AodgNonOdgdatalag;
            }
            set
            {
                _AodgNonOdgdatalag = value;
            }
        }

        private MySqlReplication CurrentMySqlRepliLog
        {
            get
            {
                if (_mysqlLag == null)
                {
                    _mysqlLag = new MySqlReplication();
                }

                return _mysqlLag;
            }
            set
            {
                _mysqlLag = value;
            }
        }
        private MimixDatalag mimixdatalagmoni
        {
            get
            {
                if (_mimixLag == null)
                    _mimixLag = new MimixDatalag();

                return _mimixLag;
            }
            set
            {
                _mimixLag = value;
            }
        }

        private EmcUnity_Repli_Monitor CurrentEmcUnityLag
        {
            get
            {
                if (emcunityLag == null)
                    emcunityLag = new EmcUnity_Repli_Monitor();

                return emcunityLag;
            }
            set
            {
                emcunityLag = value;
            }
        }

        private SybaseWithRSHADR_RepliMonitorNew CurrenteSybaseLag
        {
            get
            {
                if (_sybaseLag == null)
                    _sybaseLag = new SybaseWithRSHADR_RepliMonitorNew();

                return _sybaseLag;
            }
            set
            {
                _sybaseLag = value;
            }
        }
        private Azure_Monitoring CurrentAzurLag
        {
            get
            {
                if (_azureLag == null)
                    _azureLag = new Azure_Monitoring();

                return _azureLag;
            }
            set
            {
                _azureLag = value;
            }
        }

        private VsphereMonitorReport CurrentvsphereLag
        {
            get
            {
                if (_vsphereLag == null)
                {
                    _vsphereLag = new VsphereMonitorReport();
                }

                return _vsphereLag;
            }
            set
            {
                _vsphereLag = value;
            }
        }

        #endregion Properties

        int id = (int)(HttpContext.Current.Session["LoggedInUserId"]);
        int companyid = (int)(HttpContext.Current.Session["LoggedInUserCompanyId"]);
        UserRole user = (UserRole)(HttpContext.Current.Session["LoggedInUserRole"]);
        bool parent = (bool)(HttpContext.Current.Session["IsParentCompnay"]);
        bool flag = (bool)(HttpContext.Current.Session["AllFlag"]);



        public DataLag24HrsCurrentDataLag()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }

        private void DataLag24HrsCurrentDataLag_NeedDataSource(object sender, EventArgs e)
        {
            ShowTable();
        }


        private void ShowTable()
        {
            try
            {
                DataTable dt = new DataTable();
                dt.Columns.Add("BSName");
                dt.Columns.Add("BSDescription");
                dt.Columns.Add("InfraObjectName");
                dt.Columns.Add("Hour0");
                dt.Columns.Add("Hour1");
                dt.Columns.Add("Hour2");
                dt.Columns.Add("Hour3");
                dt.Columns.Add("Hour4");
                dt.Columns.Add("Hour5");
                dt.Columns.Add("Hour6");
                dt.Columns.Add("Hour7");
                dt.Columns.Add("Hour8");
                dt.Columns.Add("Hour9");
                dt.Columns.Add("Hour10");
                dt.Columns.Add("Hour11");
                dt.Columns.Add("Hour12");
                dt.Columns.Add("Hour13");
                dt.Columns.Add("Hour14");
                dt.Columns.Add("Hour15");
                dt.Columns.Add("Hour16");
                dt.Columns.Add("Hour17");
                dt.Columns.Add("Hour18");
                dt.Columns.Add("Hour19");
                dt.Columns.Add("Hour20");
                dt.Columns.Add("Hour21");
                dt.Columns.Add("Hour22");
                dt.Columns.Add("Hour23");


                //IList<BusinessService> appGroups = Facade.GetAllBusinessServices();
                //IList<InfraObject> allGroup = Facade.GetAllInfraObject();

                IList<BusinessService> appGroups = Facade.GetBusinessServiceByCompanyIdAndRole(id, companyid, user, parent, flag);
                IList<InfraObject> allGroup = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(id, companyid, user, parent, flag); //Facade.GetAllInfraObject();

                allGroup = (from res in allGroup
                            where res.RecoveryType == 95 || res.RecoveryType == 6 || res.RecoveryType == 108 || res.RecoveryType == 75 || res.RecoveryType == 127 ||
                            res.RecoveryType == 93 || res.RecoveryType == 132 || res.RecoveryType == 62 || res.RecoveryType == 154 || res.Type != 1
                            select res).ToList();

                var result = from grp in allGroup
                             join appname in appGroups on grp.BusinessServiceId equals appname.Id
                             where grp.RecoveryType != 3 || grp.IsActive == '1'
                             orderby grp.Name
                             //select new { Id = grp.Id, Name = grp.Name, appName = appname.Name, PRReplicationId = grp.PRReplicationId, Recoverytype = grp.RecoveryType, Businessfunctionid = grp.BusinessFunctionId, PRDatabaseId = grp.PRDatabaseId, desc = grp.Description };
                             select new { Id = grp.Id, Name = grp.Name, appName = appname.Name, PRReplicationId = grp.PRReplicationId, Recoverytype = grp.RecoveryType, Businessfunctionid = grp.BusinessFunctionId, PRDatabaseId = grp.PRDatabaseId, desc = appname.Description };

                var finalResult = result.ToList();


                _logger.Info("24 HRS DataLag Status Telerik Report Creation Start.");

                foreach (var group in finalResult)
                {
                    BusinessFunction businessFtn = Facade.GetBusinessFunctionById(group != null ? group.Businessfunctionid : 0);

                    // TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                    switch (group.Recoverytype)
                    {
                        #region RecoveryPoint
                        case (int)ReplicationType.RecoverPoint:
                        case (int)ReplicationType.RecoverPointMSSQLFULLDB:
                        case (int)ReplicationType.RecoverPointMYSQLFULLDB:
                        case (int)ReplicationType.RecoverPointOracleFULLDB:
                            {

                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<RecoverPStatisticMonitor> hadrlaglist = new List<RecoverPStatisticMonitor>();
                                    // To do - Need to send infraid instead of groupid
                                    var datalag = Facade.GetRecoveryPointMoniHourlyByGroupId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new RecoverPStatisticMonitor
                                                {
                                                    // BusinessServiceName = group.appName,
                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                hadrlaglist.Add(datalagList);
                                            }
                                        }
                                        hadrlaglist.Reverse();


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            Currentrecmoni = null;

                                            foreach (var datalag1 in hadrlaglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                Currentrecmoni = datalag1;


                                            }
                                            if (datalagFind)
                                            {
                                                string currentDatalag = Currentrecmoni.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");


                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }

                                                }
                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }

                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In RecoverPoint Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In RecoverPoint Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion RecoveryPoint

                        #region ORACLEDATAGAURD
                        case (int)ReplicationType.OracleDataGuard:
                        case (int)ReplicationType.IBMGlobalMirror:
                        case (int)ReplicationType.OracleFullDBIBMGlobalMirror:
                            {
                                try
                                {
                                    var fetchResult = Facade.GetDatabaseBaseById(group.PRDatabaseId);
                                    if (fetchResult.Version == "11g" || fetchResult.Version == "12c" || fetchResult.Version == "19c" || fetchResult.Version == "21c")
                                    {
                                        List<string> colorlist = new List<string>();

                                        List<AodgRepliLogsDetails> aodgloglist = new List<AodgRepliLogsDetails>();

                                        var datalag = Facade.GetAodgReplilogsHourlyByInfraId(group.Id);

                                        if (datalag != null && datalag.Count > 0)
                                        {
                                            var addhour = new List<int>();
                                            foreach (var data in datalag)
                                            {
                                                int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                                if (!addhour.Contains(hour))
                                                {
                                                    addhour.Add(hour);

                                                    var datalagList = new AodgRepliLogsDetails
                                                    {
                                                        InfraID = data.InfraID,
                                                        DRApply_lag = data.DRApply_lag,
                                                        CreateDate = data.CreateDate
                                                    };
                                                    aodgloglist.Add(datalagList);
                                                }
                                            }

                                            aodgloglist.OrderBy(i => i.CreateDate.Hour);


                                            for (int i = 0; i < 24; i++)
                                            {
                                                bool datalagFind = false;

                                                AodgCurrentDatalag = null;

                                                foreach (var datalag1 in aodgloglist)
                                                {
                                                    if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                        continue;
                                                    datalagFind = true;

                                                    AodgCurrentDatalag = datalag1;

                                                }

                                                if (datalagFind)
                                                {


                                                    TableCell Curdtalg = new TableCell();
                                                    Curdtalg.CssClass = "rowStyle1";
                                                    string currentDatalag = AodgCurrentDatalag.DRApply_lag;
                                                    if (string.IsNullOrEmpty(currentDatalag))
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                    else
                                                    {
                                                        if (currentDatalag != "NA")
                                                        {
                                                            bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                            if (isHealth)
                                                            {

                                                                colorlist.Add("TRUE");
                                                            }
                                                            else
                                                            {
                                                                colorlist.Add("FALSE");
                                                            }
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("NA");
                                                        }
                                                    }


                                                }  // parent if closed
                                                else
                                                {
                                                    colorlist.Add("NA");
                                                    //yellow
                                                }





                                                //}

                                            }

                                        }
                                        else
                                        {
                                            for (int t = 0; t < 24; t++)
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }
                                        //lblDatalagG.Text = "DataLag <= Agreed RPO";
                                        //lblDatalagL.Text = "DataLag > Agreed RPO";
                                        //lblnotAvail.Text = "Not Available";

                                        dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                    }

                                    else
                                    {

                                        List<string> colorlist = new List<string>();
                                        // _logger.Info("Oracle DataGuard start");
                                        List<OracleLog> oraloglist = new List<OracleLog>();
                                        // To do - Need to send infraid instead of groupid 
                                        var datalag = Facade.GetHourlyOracleLogsByInfraObjectId(group.Id, "24");

                                        if (datalag != null && datalag.Count > 0)
                                        {
                                            var addhour = new List<int>();
                                            foreach (var data in datalag)
                                            {
                                                int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                                if (!addhour.Contains(hour))
                                                {
                                                    addhour.Add(hour);

                                                    var datalagList = new OracleLog
                                                    {
                                                        BusinessServiceName = group.appName,
                                                        InfraObjectName = group.Name,
                                                        InfraObjectId = data.InfraObjectId,
                                                        CurrentDataLag = data.CurrentDataLag,
                                                        CreateDate = data.CreateDate
                                                    };
                                                    oraloglist.Add(datalagList);
                                                }
                                            }

                                            oraloglist.OrderBy(i => i.CreateDate.Hour);
                                            //  oraloglist.Reverse();

                                            //string[] xlColumn = { "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z" };



                                            for (int i = 0; i < 24; i++)
                                            {
                                                bool datalagFind = false;

                                                CurrentDatalag = null;

                                                foreach (var datalag1 in oraloglist)
                                                {
                                                    if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                        continue;
                                                    datalagFind = true;

                                                    CurrentDatalag = datalag1;


                                                }

                                                if (datalagFind)
                                                {

                                                    string currentDatalag = CurrentDatalag.CurrentDataLag;
                                                    if (string.IsNullOrEmpty(currentDatalag))
                                                    {
                                                        colorlist.Add("NA");


                                                    }
                                                    else
                                                    {
                                                        if (currentDatalag != "NA")
                                                        {
                                                            bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                            if (isHealth)
                                                            {

                                                                colorlist.Add("TRUE");
                                                            }
                                                            else
                                                            {

                                                                colorlist.Add("FALSE");
                                                            }
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("NA");

                                                        }
                                                    }


                                                }  // parent if closed
                                                else
                                                {

                                                    colorlist.Add("NA");

                                                }


                                            }

                                        }
                                        else
                                        {
                                            for (int t = 0; t < 24; t++)
                                            {
                                                colorlist.Add("NA");

                                            }

                                        }

                                        dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                    }

                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In OracleDataGuard Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In OracleDataGuard Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion ORACLEDATAGAURD

                        #region ORACLE_DATASYNC

                        case (int)ReplicationType.OracleWithDataSync:
                        case (int)ReplicationType.OracleWithRSync:
                            {

                                try
                                {
                                    var fetchResult = Facade.GetDatabaseBaseById(group.PRDatabaseId);

                                    if (fetchResult.Version == "11g" || fetchResult.Version == "12c" || fetchResult.Version == "19c" || fetchResult.Version == "21c")
                                    {
                                        try
                                        {
                                            #region 11G_12C

                                            List<string> colorlist = new List<string>();
                                            List<ActiveODGReplicationNonOdg> hadrlaglist = new List<ActiveODGReplicationNonOdg>();
                                            // To do - Need to send infraid instead of groupid
                                            var datalag = Facade.GetAodgRepliNonOdglogByHourlyInfraObjectId(group.Id);

                                            if (datalag != null && datalag.Count > 0)
                                            {
                                                var addhour = new List<int>();
                                                foreach (var data in datalag)
                                                {
                                                    int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                                    if (!addhour.Contains(hour))
                                                    {
                                                        addhour.Add(hour);

                                                        var datalagList = new ActiveODGReplicationNonOdg
                                                        {
                                                            // BusinessServiceName = group.appName,
                                                            //InfraObjectName = group.Name,
                                                            InfraID = data.InfraID,
                                                            DataLag = data.DataLag,
                                                            CreateDate = data.CreateDate
                                                        };
                                                        hadrlaglist.Add(datalagList);
                                                    }
                                                }
                                                hadrlaglist.Reverse();

                                                for (int i = 0; i < 24; i++)
                                                {
                                                    bool datalagFind = false;

                                                    AodgNonODGCurrentDatalag = null;

                                                    foreach (var datalag1 in hadrlaglist)
                                                    {
                                                        if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                            continue;
                                                        datalagFind = true;

                                                        AodgNonODGCurrentDatalag = datalag1;
                                                    }

                                                    if (datalagFind)
                                                    {
                                                        string currentDatalag = AodgNonODGCurrentDatalag.DataLag;
                                                        if (string.IsNullOrEmpty(currentDatalag))
                                                        {
                                                            colorlist.Add("NA");
                                                        }
                                                        else
                                                        {
                                                            if (currentDatalag != "NA")
                                                            {
                                                                bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                                if (isHealth)
                                                                {
                                                                    colorlist.Add("TRUE");
                                                                }
                                                                else
                                                                {
                                                                    colorlist.Add("FALSE");
                                                                }
                                                            }
                                                            else
                                                            {
                                                                colorlist.Add("NA");
                                                            }
                                                        }
                                                    }  // parent if closed
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                for (int t = 0; t < 24; t++)
                                                {
                                                    colorlist.Add("NA");
                                                }
                                            }
                                            dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);

                                            #endregion 11G_12C
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.Error("Exception Occured In OracleWithDataSync 11G & 12C Solution, Error Message " + ex.Message);
                                            if (ex.InnerException != null)
                                                _logger.Error("Exception Occured In OracleWithDataSync 11G & 12C Solution, InnerException Message " + ex.InnerException.Message);
                                        }
                                    }
                                    else
                                    {
                                        try
                                        {
                                            #region 10G_Other

                                            List<string> colorlist = new List<string>();
                                            List<OracleLog> oraloglist = new List<OracleLog>();
                                            // To do - Need to send infraid instead of groupid 
                                            var datalag = Facade.GetHourlyOracleLogsByInfraObjectId(group.Id, "24");

                                            if (datalag != null && datalag.Count > 0)
                                            {
                                                var addhour = new List<int>();
                                                foreach (var data in datalag)
                                                {
                                                    int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                                    if (!addhour.Contains(hour))
                                                    {
                                                        addhour.Add(hour);

                                                        var datalagList = new OracleLog
                                                        {
                                                            BusinessServiceName = group.appName,
                                                            InfraObjectName = group.Name,
                                                            InfraObjectId = data.InfraObjectId,
                                                            CurrentDataLag = data.CurrentDataLag,
                                                            CreateDate = data.CreateDate
                                                        };
                                                        oraloglist.Add(datalagList);

                                                    }
                                                }

                                                oraloglist.OrderBy(i => i.CreateDate.Hour);

                                                for (int i = 0; i < 24; i++)
                                                {
                                                    bool datalagFind = false;
                                                    CurrentDatalag = null;
                                                    foreach (var datalag1 in oraloglist)
                                                    {
                                                        if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                            continue;
                                                        datalagFind = true;
                                                        CurrentDatalag = datalag1;
                                                    }
                                                    if (datalagFind)
                                                    {
                                                        string currentDatalag = CurrentDatalag.CurrentDataLag;
                                                        if (string.IsNullOrEmpty(currentDatalag))
                                                        {
                                                            colorlist.Add("NA");
                                                        }
                                                        else
                                                        {
                                                            if (currentDatalag != "NA")
                                                            {
                                                                bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                                if (isHealth)
                                                                {
                                                                    colorlist.Add("TRUE");
                                                                }
                                                                else
                                                                {
                                                                    colorlist.Add("FALSE");
                                                                }
                                                            }
                                                            else
                                                            {
                                                                colorlist.Add("NA");
                                                            }
                                                        }
                                                    }  // parent if closed
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                for (int t = 0; t < 24; t++)
                                                {
                                                    colorlist.Add("NA");
                                                }
                                            }
                                            dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);

                                            #endregion 10G_Other
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.Error("Exception Occured In OracleWithDataSync 10G Solution, Error Message " + ex.Message);
                                            if (ex.InnerException != null)
                                                _logger.Error("Exception Occured In OracleWithDataSync 10G Solution, InnerException Message " + ex.InnerException.Message);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In OracleWithDataSync Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In OracleWithDataSync Solution, InnerException Message " + ex.InnerException.Message);
                                }

                                break;
                            }

                        #endregion ORACLE_DATASYNC

                        #region MSSQLDoubleTakeFullDB

                        case (int)ReplicationType.MSSQLDoubleTakeFullDB:
                        case (int)ReplicationType.ApplicationDoubleTake:
                        case (int)ReplicationType.OracleDoubleTakeFullDB:
                            //else if (group.Recoverytype == 32)
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<MSSqlDoubletek> mssqldbllist = new List<MSSqlDoubletek>();
                                    // To do - Need to send infraid instead of groupid
                                    var datalag = Facade.GetHourlyMSSQLDoubletekByInfraId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new MSSqlDoubletek
                                                {
                                                    //BusinessServiceName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraobjectId = data.InfraobjectId,
                                                    MirrorStatus = data.MirrorStatus,
                                                    MirrorPercentComplete = data.MirrorPercentComplete,
                                                    MirrorBytesRemaining = data.MirrorBytesRemaining,
                                                    //DataLag = v.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                mssqldbllist.Add(datalagList);
                                            }
                                        }
                                        mssqldbllist.Reverse();


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentMSSQLDblLog = null;

                                            foreach (var datalag1 in mssqldbllist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentMSSQLDblLog = datalag1;


                                            }


                                            var Business_Fun = Facade.GetBusinessFunctionByInfraObjectId(group.Id);
                                            double DataLagInBye = Convert.ToDouble(Business_Fun.DataLagInByte);

                                            double MirrorByteRemain = 0;
                                            double DataLagInBytemb = (DataLagInBye / 1024) / 1024;
                                            double MirrorByteRemainMB = -1;

                                            if (datalagFind)
                                            {


                                                if (CurrentMSSQLDblLog.MirrorBytesRemaining != null && CurrentMSSQLDblLog.MirrorBytesRemaining != "NA")
                                                {
                                                    MirrorByteRemain = Convert.ToDouble(CurrentMSSQLDblLog.MirrorBytesRemaining);


                                                    MirrorByteRemainMB = (MirrorByteRemain / 1024) / 1024;
                                                }

                                                string currentDatalag = MirrorByteRemainMB.ToString();
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {

                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (MirrorByteRemainMB != -1)
                                                    {
                                                        // bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (DataLagInBye >= MirrorByteRemain)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }


                                            // }
                                        }
                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }
                                    }

                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MSSQLDoubleTakeFullDB Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MSSQLDoubleTakeFullDB Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion MSSQLDoubleTakeFullDB

                        #region Postgre9x

                        case (int)ReplicationType.Postgres9X:
                        case (int)ReplicationType.Postgres10:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<Postgre9xMonitorStatus> postgre9xloglist = new List<Postgre9xMonitorStatus>();

                                    var datalag = Facade.GetHourlyPostgre9xMonitorStatusSqlLogsByInfraId(group.Id, "");

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new Postgre9xMonitorStatus
                                                {
                                                    //ApplicationName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag_HHMMSS = data.DataLag_HHMMSS,
                                                    CreateDate = data.CreateDate
                                                };
                                                postgre9xloglist.Add(datalagList);
                                            }
                                        }


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentPostgre9xlag = null;

                                            foreach (var datalag1 in postgre9xloglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentPostgre9xlag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }


                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentPostgre9xlag.DataLag_HHMMSS;
                                                if (string.IsNullOrEmpty(CurrentPostgre9xlag.DataLag_HHMMSS))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                    if (isHealth)
                                                    {

                                                        colorlist.Add("TRUE");
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("FALSE");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {


                                                colorlist.Add("NA");
                                            }

                                        }
                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }
                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In Postgres9X Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In Postgres9X Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion postgre9x

                        #region VMWareSnapMirror
                        case (int)ReplicationType.VMWareSnapMirror:
                        case (int)ReplicationType.MSSqlNetAppSnapMirror:
                        case (int)ReplicationType.NetAppSnapMirror:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    IList<VMWareNetSnapMirrorComponentMonitor> vmnetspanlist = new List<VMWareNetSnapMirrorComponentMonitor>();
                                    var datalag = Facade.GetvmwareHourly_NetSnapMirrorById(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new VMWareNetSnapMirrorComponentMonitor
                                                {
                                                    //BusinessServiceName = group.appName,
                                                    // InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    Lag = data.Lag,
                                                    CreateDate = data.CreateDate
                                                };
                                                vmnetspanlist.Add(datalagList);
                                            }
                                        }
                                        vmnetspanlist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            Currentnetappsnapmirror = null;

                                            foreach (var datalag1 in vmnetspanlist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                Currentnetappsnapmirror = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = Currentnetappsnapmirror.Lag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {

                                                colorlist.Add("NA");
                                            }
                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }
                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In VMWareSnapMirror Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In VMWareSnapMirror Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion VMWareSnapMirror

                        #region SYBASEWITHDATASYNC
                        case (int)ReplicationType.SyBaseWithDataSync:
                            //else if (group.recoveryType == 41)
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    var datalag = Facade.GetHourlySybaseMonitorByInfraObjectId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new SyBaseMonitor
                                                {
                                                    BusinessServiceName = group.appName,
                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                sybaselist.Add(datalagList);
                                            }
                                        }
                                        sybaselist.Reverse();


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;

                                            foreach (var datalag1 in sybaselist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                {
                                                    datalagFind = false;
                                                }
                                                else
                                                {
                                                    datalagFind = true;
                                                }



                                                if (datalagFind)
                                                {

                                                    string currentDatalag = datalag1.DataLag;
                                                    if (string.IsNullOrEmpty(datalag1.DataLag))
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                    else
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {

                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }


                                                }  // parent if closed
                                                else
                                                {

                                                    colorlist.Add("NA");
                                                }
                                            }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In SyBaseWithDataSync Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In SyBaseWithDataSync Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion SYBASEWITHDATASYNC

                        #region SQLNATIVE
                        case (int)ReplicationType.SQLNative2008:
                        case (int)ReplicationType.MSSQLServerNative:
                            //else if (group.recoveryType == 4 || group.recoveryType == 28)
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<SqlNativeMonitor> nativelaglist = new List<SqlNativeMonitor>();
                                    var datalag = Facade.GetHourlySqlNativeMonitorsByInfraObjectId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new SqlNativeMonitor
                                                {
                                                    ApplicationName = group.appName,
                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                nativelaglist.Add(datalagList);
                                            }
                                        }
                                        //     nativelaglist.Reverse();
                                        nativelaglist.OrderBy(i => i.CreateDate.Hour);



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentSqlNativeLog = null;

                                            foreach (var datalag1 in nativelaglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentSqlNativeLog = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }


                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentSqlNativeLog.DataLag;
                                                if (string.IsNullOrEmpty(CurrentSqlNativeLog.DataLag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                    if (isHealth)
                                                    {

                                                        colorlist.Add("TRUE");
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("FALSE");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {

                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MSSQLServerNative Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MSSQLServerNative Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion SQLNATIVE

                        #region SRMVMWARE

                        case (int)ReplicationType.SRMVMware:
                            {

                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<SRMVmwareMonitor> srmloglist = new List<SRMVmwareMonitor>();
                                    var datalag = Facade.GetSRMVMWAREHourlyByInfraId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new SRMVmwareMonitor
                                                {
                                                    //ApplicationName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    //  DataLag = "00:00:00",
                                                    CreateDate = data.CreateDate
                                                };
                                                srmloglist.Add(datalagList);
                                            }
                                        }
                                        srmloglist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentSrmVmwareLag = null;

                                            foreach (var datalag1 in srmloglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentSrmVmwareLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }


                                            if (datalagFind)
                                            {

                                                string currentDatalag = "00:00:00";
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {

                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");

                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {


                                                colorlist.Add("NA");
                                            }




                                            //     }

                                        }
                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }
                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In SRMVMware Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In SRMVMware Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion SRMVMWARE

                        #region MSSQLDBMirror

                        case (int)ReplicationType.MSSQLDBMirroring:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();

                                    List<MSSQLDBMirrorReplicationMonitor> MSSqlList = new List<MSSQLDBMirrorReplicationMonitor>();

                                    //var datalag1 = Facade.GetSVCGlobalMirrorHourlyByInfraObjectId(group.Id);
                                    var datalag = Facade.GetMSSQLDBMirrorByHrsByInfraObjId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new MSSQLDBMirrorReplicationMonitor
                                                {
                                                    //ApplicationName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                MSSqlList.Add(datalagList);
                                            }
                                        }

                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentMSSQLDBMirrorMoni = null;

                                            foreach (var datalag1 in MSSqlList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentMSSQLDBMirrorMoni = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentMSSQLDBMirrorMoni.DataLag;
                                                if (string.IsNullOrEmpty(CurrentMSSQLDBMirrorMoni.DataLag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {

                                                colorlist.Add("NA");
                                            }





                                            // }

                                        }


                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }

                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MSSQLDBMirroring Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MSSQLDBMirroring Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }


                        #endregion MSSQLDBMirror

                        #region SVCGLOBALMIRRORMONITOR
                        case (int)ReplicationType.SVCGlobalMirrorOracleFullDBRac:
                        case (int)ReplicationType.OracleFullDBSVCGlobalMirror:
                        case (int)ReplicationType.SVCGlobalMirrorORMetroMirror:
                        case (int)ReplicationType.DB2FullDBSVC:
                        case (int)ReplicationType.PostgressFullDBSVC:
                            //else if (group.Recoverytype == (int)ReplicationType.OracleFullDBSVCGlobalMirror)
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<SVCGlobalMirrorMonitor> svcgloballist = new List<SVCGlobalMirrorMonitor>();

                                    var datalag = Facade.GetSVCGlobalMirrorHourlyByInfraObjectId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new SVCGlobalMirrorMonitor
                                                {
                                                    //ApplicationName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                svcgloballist.Add(datalagList);
                                            }
                                        }


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentSvcGlobalMoni = null;

                                            foreach (var datalag1 in svcgloballist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentSvcGlobalMoni = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentSvcGlobalMoni.DataLag;
                                                if (string.IsNullOrEmpty(CurrentSvcGlobalMoni.DataLag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {

                                                colorlist.Add("NA");
                                            }
                                            // }
                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);

                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In SVCGLOBALMIRRORMONITOR Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In SVCGLOBALMIRRORMONITOR Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion SRMVMWARE

                        #region MYSQLNATIVELOGSHIPPING
                        case (int)ReplicationType.MySqlNativeLogShipping:
                            //else if (group.recoveryType == 51)
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();

                                    List<MySqlReplication> mysqllist = new List<MySqlReplication>();
                                    var datalag = Facade.GetHourlyDataByInfraObjectId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new MySqlReplication
                                                {
                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    MySqlDataLag = data.MySqlDataLag,
                                                    CreateDate = data.CreateDate


                                                };
                                                mysqllist.Add(datalagList);
                                            }
                                        }

                                        mysqllist.OrderBy(i => i.CreateDate.Hour);
                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentMySqlRepliLog = null;

                                            foreach (var datalag1 in mysqllist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentMySqlRepliLog = datalag1;

                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentMySqlRepliLog.MySqlDataLag;
                                                if (string.IsNullOrEmpty(CurrentMySqlRepliLog.MySqlDataLag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }
                                            else
                                            {

                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MySqlNativeLogShipping Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MySqlNativeLogShipping Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion MYSQLNATIVELOGSHIPPING

                        #region MSEXCHANGEDAG
                        //case (int)ReplicationType.MSExchangeDAG:
                        //    // else if (group.recoveryType == 20)
                        //    {
                        //        try
                        //        {

                        //            List<string> colorlist = new List<string>();
                        //            List<ExchangeDAGComponantMonitor> ExchangeDaglist = new List<ExchangeDAGComponantMonitor>();
                        //            var ExchangeDaglag = Facade.GetExchangeDagHourlyByGroupId(group.Id);

                        //            if (ExchangeDaglag != null && ExchangeDaglag.Count > 0)
                        //            {
                        //                var addhour = new List<int>();
                        //                foreach (var data in ExchangeDaglag)
                        //                {
                        //                    int hour = Convert.ToDateTime(data.CreateDate).Hour;

                        //                    if (!addhour.Contains(hour))
                        //                    {
                        //                        addhour.Add(hour);

                        //                        var datalagList = new ExchangeDAGComponantMonitor
                        //                        {
                        //                            BusinessServiceName = group.appName,
                        //                            InfraObjectName = group.Name,
                        //                            InfraObjectID = data.InfraObjectID,
                        //                            CurrentDatalag = data.CurrentDatalag,
                        //                            CreateDate = data.CreateDate
                        //                        };
                        //                        ExchangeDaglist.Add(datalagList);
                        //                    }
                        //                }
                        //                ExchangeDaglist.Reverse();


                        //                for (int i = 0; i < 24; i++)
                        //                {
                        //                    bool datalagFind = false;


                        //                    CurrentDatalagExcDag = null;

                        //                    foreach (var datalag1 in ExchangeDaglist)
                        //                    {
                        //                        if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                        //                            continue;
                        //                        datalagFind = true;

                        //                        CurrentDatalagExcDag = datalag1;

                        //                        //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                        //                        //{
                        //                        //    datalagFind = false;
                        //                        //}
                        //                        //else
                        //                        //{
                        //                        //    datalagFind = true;
                        //                        //}
                        //                    }


                        //                    if (datalagFind)
                        //                    {

                        //                        string currentDatalag = CurrentDatalagExcDag.CurrentDatalag;
                        //                        if (string.IsNullOrEmpty(CurrentDatalagExcDag.CurrentDatalag))
                        //                        {

                        //                            colorlist.Add("NA");
                        //                        }
                        //                        else
                        //                        {
                        //                            if (currentDatalag != "NA")
                        //                            {
                        //                                bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                        //                                if (isHealth)
                        //                                {
                        //                                    colorlist.Add("TRUE");
                        //                                }
                        //                                else
                        //                                {
                        //                                    colorlist.Add("FALSE");
                        //                                }
                        //                            }
                        //                            else
                        //                            {

                        //                                colorlist.Add("NA");
                        //                            }
                        //                        }


                        //                    }  // parent if closed
                        //                    else
                        //                    {



                        //                        colorlist.Add("NA");
                        //                    }




                        //                    // }

                        //                }

                        //            }
                        //            else
                        //            {
                        //                for (int t = 0; t < 24; t++)
                        //                {

                        //                    colorlist.Add("NA");
                        //                }

                        //            }
                        //            dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                        //        }
                        //        catch (Exception ex)
                        //        {
                        //            _logger.Error("Exception Occured In MSExchangeDAG Solution, Error Message " + ex.Message);
                        //            if (ex.InnerException != null)
                        //                _logger.Error("Exception Occured In MSExchangeDAG Solution, InnerException Message " + ex.InnerException.Message);
                        //        }
                        //        break;
                        //    }
                        #endregion MSEXCHANGEDAG


                        #region MSEXCHANGEDAG
                        case (int)ReplicationType.MSExchangeDAG:
                            // else if (group.recoveryType == 20)
                            {
                                try
                                {

                                    List<string> colorlist = new List<string>();
                                    List<ExChangMntrStats> ExchangeDaglist = new List<ExChangMntrStats>();
                                    IList<ExChangMntrStats> ExchangeDaglag = Facade.GetExchngCompmntrLogByInfraObjectId(group.Id);

                                    if (ExchangeDaglag != null && ExchangeDaglag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in ExchangeDaglag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new ExChangMntrStats
                                                {
                                                    BusinessServiceName = group.appName,
                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    CurrentDataLag = data.CurrentDataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                ExchangeDaglist.Add(datalagList);
                                            }
                                        }
                                        ExchangeDaglist.Reverse();


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentDatalagExcDag = null;

                                            foreach (var datalag1 in ExchangeDaglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentDatalagExcDag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }


                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentDatalagExcDag.CurrentDataLag;
                                                if (string.IsNullOrEmpty(CurrentDatalagExcDag.CurrentDataLag))
                                                {

                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {

                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {



                                                colorlist.Add("NA");
                                            }




                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {

                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MSExchangeDAG Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MSExchangeDAG Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion MSEXCHANGEDAG






                        #region DB2HADR
                        case (int)ReplicationType.DB2DataSync:
                        case (int)ReplicationType.DB2HADR:
                        case (int)ReplicationType.DB2HADR9X:
                            //else if (group.recoveryType == 15 || group.recoveryType == 21 || group.recoveryType == 24)
                            {

                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<HADR> hadrlaglist = new List<HADR>();
                                    // To do - Need to send infraid instead of groupid
                                    var datalag = Facade.GetHadrMonitorHourlyByGroupId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new HADR
                                                {
                                                    BusinessServiceName = group.appName,
                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    Datalag = data.Datalag,
                                                    CreateDate = data.CreateDate
                                                };
                                                hadrlaglist.Add(datalagList);
                                            }
                                        }


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentHadrLag = null;

                                            foreach (var datalag1 in hadrlaglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentHadrLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentHadrLag.Datalag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {

                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {


                                                colorlist.Add("NA");
                                            }





                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }

                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In DB2HADR Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In DB2HADR Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion DB2HADR

                        #region MSSCR
                        case (int)ReplicationType.MSSCR:

                            //else if (group.recoveryType == 5)
                            {
                                try
                                {

                                    List<string> colorlist = new List<string>();
                                    List<ExchangeHealth> exchangelist = new List<ExchangeHealth>();
                                    var datalag = Facade.GetExchangeHealthHourlyByInfraObjectId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new ExchangeHealth
                                                {

                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLagTime = data.DataLagTime,
                                                    CreateDate = data.CreateDate
                                                };
                                                exchangelist.Add(datalagList);
                                            }
                                        }
                                        exchangelist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentExchangelag = null;

                                            foreach (var datalag1 in exchangelist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentExchangelag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {
                                                string currentDatalag = CurrentExchangelag.DataLagTime;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }




                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }

                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MSSCR Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MSSCR Solution, InnerException Message " + ex.InnerException.Message);
                                }

                                break;
                            }
                        #endregion MSSCR

                        #region VMWAREDATASYNC
                        case (int)ReplicationType.VMWareDataSync:
                            //else if (group.recoveryType == 10)
                            {
                                try
                                {

                                    List<string> colorlist = new List<string>();

                                    List<VmwareMonitor> vmwarelist = new List<VmwareMonitor>();
                                    //GetVmWareMonitorHourlyByGroupId method has been changed by GetVmWareMonitorHourlyByInfraObjectId in CPV4.0
                                    var datalag = Facade.GetVmWareMonitorHourlyByInfraObjId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new VmwareMonitor
                                                {

                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    CurrentDataLag = data.CurrentDataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                vmwarelist.Add(datalagList);
                                            }
                                        }

                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentVmwareLag = null;

                                            foreach (var datalag1 in vmwarelist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentVmwareLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }


                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentVmwareLag.CurrentDataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {


                                                colorlist.Add("NA");
                                            }





                                            //   }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In VMWareDataSync Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In VMWareDataSync Solution, InnerException Message " + ex.InnerException.Message);
                                }

                                break;
                            }
                        #endregion VMWAREDATASYNC

                        #region SybaseWithSRS
                        case (int)ReplicationType.SybaseWithSRS:
                            {
                                try
                                {

                                    List<string> colorlist = new List<string>();
                                    List<SybaseWithSRSMonitor> sybaseWithSRSMonitor = new List<SybaseWithSRSMonitor>();
                                    var datalag = Facade.GetSybaseWithSRSRepliHourlyByInfraObjId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new SybaseWithSRSMonitor
                                                {

                                                    //  InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                sybaseWithSRSMonitor.Add(datalagList);
                                            }
                                        }
                                        sybaseWithSRSMonitor.Reverse();


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentSybseWithSRSLog = null;

                                            foreach (var datalag1 in sybaseWithSRSMonitor)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentSybseWithSRSLog = datalag1;


                                            }


                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentSybseWithSRSLog.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {

                                                colorlist.Add("NA");
                                            }





                                            //   }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In SybaseWithSRS Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In SybaseWithSRS Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion SybaseWithSRS

                        #region MaxDBReplication
                        case (int)ReplicationType.MaxDBWithDataSync:
                            //else if (group.recoveryType == 64)
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<MaxDBReplication> maxDBReplList = new List<MaxDBReplication>();
                                    var datalag = Facade.GetMaxReplMonitorHourlyByInfraId(group.Id);
                                    //  var datalag = Facade.GetMaxDBReplMonitorHourlyByInfraId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new MaxDBReplication
                                                {

                                                    // InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    Datalag = data.Datalag,
                                                    CreateDate = data.CreateDate
                                                };
                                                maxDBReplList.Add(datalagList);
                                            }
                                        }
                                        maxDBReplList.Reverse();


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentMaxDBReplicationLag = null;

                                            foreach (var datalag1 in maxDBReplList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentMaxDBReplicationLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }


                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentMaxDBReplicationLag.Datalag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                            //   }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MaxDBWithDataSync Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MaxDBWithDataSync Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion  MaxDBReplication

                        #region EMCSRDF
                        case (int)ReplicationType.EMCSRDF:
                        case (int)ReplicationType.EMCSRDFOracleFullDB:
                        case (int)ReplicationType.EMCSRDFOracleLogShipping:
                        case (int)ReplicationType.EMCSRDFMSSQLFullDB:
                        case (int)ReplicationType.EMCSRDFMysqlFullDB:
                        case (int)ReplicationType.EMCSRDFOracleRacFullDB:
                            // else if (group.RecoveryType == 7 || group.RecoveryType == 17 || group.RecoveryType == 18 || group.RecoveryType == 26)
                            {
                                try
                                {

                                    List<string> colorlist = new List<string>();
                                    List<EMCSRDF> emcsrdflist = new List<EMCSRDF>();
                                    var datalag = Facade.GetEMCSRDFHourlyByReplicationId(group.PRReplicationId);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new EMCSRDF
                                                {
                                                    //BusinessServiceName = group.appName,
                                                    InfraObjectName = group.Name,
                                                    //InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                emcsrdflist.Add(datalagList);
                                            }
                                        }
                                        emcsrdflist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentEmcLag = null;

                                            foreach (var datalag1 in emcsrdflist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentEmcLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }



                                            if (datalagFind)
                                            {
                                                string currentDatalag = CurrentEmcLag.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {

                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {

                                                colorlist.Add("NA");
                                            }




                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {


                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In EMCSRDF Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In EMCSRDF Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion EMCSRDF

                        #region ApplicationeBDR
                        case (int)ReplicationType.ApplicationeBDR:
                            {
                                try
                                {

                                    List<string> colorlist = new List<string>();
                                    List<eBDRProfileReplication> eBDRProfileList = new List<eBDRProfileReplication>();
                                    // To do - Need to send infraid instead of groupid
                                    //  var datalag = Facade.GetHadrMonitorHourlyByGroupId(group.Id);
                                    var datalag = Facade.GeteBDRProfileMonitorHourlyByGroupId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new eBDRProfileReplication
                                                {
                                                    //BusinessServiceName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraobjectID = data.InfraobjectID,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                eBDRProfileList.Add(datalagList);
                                            }
                                        }
                                        eBDRProfileList.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrenteBDRPLag = null;

                                            foreach (var datalag1 in eBDRProfileList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrenteBDRPLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrenteBDRPLag.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }





                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In ApplicationeBDR Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In ApplicationeBDR Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion ApplicationeBDR

                        #region MSSQLAlwaysOn
                        case (int)ReplicationType.MSSQLAlwaysOn:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<MSSQLAlwaysOnServerMoniter> eBDRProfileList = new List<MSSQLAlwaysOnServerMoniter>();
                                    // To do - Need to send infraid instead of groupid
                                    //  var datalag = Facade.GetHadrMonitorHourlyByGroupId(group.Id);
                                    var datalag = Facade.GetMSSQLAlwaysOnByHrsByInfraObjId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new MSSQLAlwaysOnServerMoniter
                                                {
                                                    //BusinessServiceName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraobjectID = data.InfraobjectID,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                eBDRProfileList.Add(datalagList);
                                            }
                                        }
                                        eBDRProfileList.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrenteBDRPLag = null;

                                            foreach (var datalag1 in eBDRProfileList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrenteAlwaysonLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrenteAlwaysonLag.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }





                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MSSQLAlwaysOn Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MSSQLAlwaysOn Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion MSSQLAlwaysOn


                        #region Mimix
                        case (int)ReplicationType.MIMIX:
                            {
                                try
                                {

                                    _logger.Info(Environment.NewLine);
                                    _logger.Info(" ReplicationType.MIMIX: case execute Telerik");
                                    _logger.Info(Environment.NewLine);

                                    List<string> colorlist = new List<string>();
                                    List<MimixDatalag> mimixlistlist = new List<MimixDatalag>();
                                    // To do - Need to send infraid instead of groupid
                                    var datalag = Facade.GetHourlyMinDatalagbyInfraObjId(group.Id);

                                    _logger.Info(Environment.NewLine);
                                    _logger.Info("Telerik  Total datalag records got :" + datalag.Count());
                                    _logger.Info(Environment.NewLine);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            _logger.Info(Environment.NewLine);
                                            _logger.Info("Telerik Datalag date  :" + data.CreateDate);
                                            _logger.Info(Environment.NewLine);

                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new MimixDatalag
                                                {
                                                    // BusinessServiceName = group.appName,
                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    CurrentDatalag = data.CurrentDatalag,
                                                    CreateDate = data.CreateDate
                                                };
                                                mimixlistlist.Add(datalagList);
                                            }
                                        }
                                        mimixlistlist.Reverse();


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            mimixdatalagmoni = null;

                                            foreach (var datalag1 in mimixlistlist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                mimixdatalagmoni = datalag1;


                                            }
                                            if (datalagFind)
                                            {
                                                string currentDatalag = mimixdatalagmoni.CurrentDatalag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");


                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }

                                                }
                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                            // }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info(Environment.NewLine);
                                        _logger.Info("Telerik Datalag replicationtype.MIMIMX else case executed");
                                        _logger.Info(Environment.NewLine);

                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }

                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MIMIX Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MIMIX Solution, InnerException Message " + ex.InnerException.Message);
                                }

                                break;
                            }
                        #endregion Mimix

                        #region EMCUnityregion
                        case (int)ReplicationType.EmcUnityApp:
                            {
                                try
                                {

                                    List<string> colorlist = new List<string>();
                                    // List<Emc_MV_Mirror_Monitor> EmcMirrorList = new List<Emc_MV_Mirror_Monitor>();

                                    List<EmcUnity_Repli_Monitor> EmcUnitylist = new List<EmcUnity_Repli_Monitor>();

                                    var datalag = Facade.GetEmcUnityMonitorHourlyByGroupId(group.Id);      //GetEmcMirrorMonitorHourlyByGroupId(group.Id);EmcUnity_Repli_Monitor
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new EmcUnity_Repli_Monitor
                                                {
                                                    InfraobjectId = data.InfraobjectId,
                                                    DRDataLag = data.DRDataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                EmcUnitylist.Add(datalagList);
                                            }
                                        }
                                        EmcUnitylist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentEmcUnityLag = null;

                                            foreach (var datalag1 in EmcUnitylist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentEmcUnityLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentEmcUnityLag.DRDataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }





                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In EmcUnityApp Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In EmcUnityApp Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }


                        #endregion EMCUnityregion

                        #region SybaseRSHADRRegion
                        case (int)ReplicationType.SybaseWithRSHADR:
                            {

                                try
                                {

                                    List<string> colorlist = new List<string>();
                                    List<SybaseWithRSHADR_RepliMonitorNew> SybaseList = new List<SybaseWithRSHADR_RepliMonitorNew>();

                                    var datalag = Facade.GetSybaseRSHadrHourlyById(group.Id);//Facade.GetMSSQLAlwaysOnByHrsByInfraObjId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreatedDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new SybaseWithRSHADR_RepliMonitorNew
                                                {
                                                    //BusinessServiceName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    // DRDatalag = data.DRDatalag,
                                                    Datalag = data.Datalag,
                                                    CreatedDate = data.CreatedDate
                                                };
                                                SybaseList.Add(datalagList);
                                            }
                                        }
                                        SybaseList.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrenteSybaseLag = null;

                                            foreach (var datalag1 in SybaseList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreatedDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrenteSybaseLag = datalag1;

                                                //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                //{
                                                //    datalagFind = false;
                                                //}
                                                //else
                                                //{
                                                //    datalagFind = true;
                                                //}
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrenteSybaseLag.Datalag;
                                                if (string.IsNullOrEmpty(currentDatalag) || currentDatalag == "Unknown")
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }





                                            // }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In SybaseWithRSHADR Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In SybaseWithRSHADR Solution, InnerException Message " + ex.InnerException.Message);
                                }

                                break;
                            }
                        #endregion SybseRSHADRRegion

                        #region SAPHANADBRegion

                        case (int)ReplicationType.SAPHANADBReplication:
                            {
                                try
                                {

                                    List<string> colorlist = new List<string>();
                                    List<HanaDBMonitor> HanaDBMonitorlist = new List<HanaDBMonitor>();
                                    var datalag = Facade.GetHanaDBMonitorByInfraId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new HanaDBMonitor
                                                {
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                HanaDBMonitorlist.Add(datalagList);
                                            }
                                        }
                                        HanaDBMonitorlist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentHanaDBMonitorLag = null;

                                            foreach (var datalag1 in HanaDBMonitorlist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentHanaDBMonitorLag = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentHanaDBMonitorLag.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In SAPHANADBReplication Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In SAPHANADBReplication Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion SAPHANADBRegion

                        #region GoldenGateReplicationMonitor

                        case (int)ReplicationType.GoldenGateRepli:
                            {
                                try
                                {


                                    List<string> colorlist = new List<string>();
                                    List<GoldenGateReplicationMonitor> GGList = new List<GoldenGateReplicationMonitor>();
                                    var datalag = Facade.GoldenGateRepliMoniGetLast24Hrs(group.Id, "PRDR");

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new GoldenGateReplicationMonitor
                                                {
                                                    InfraObjectId = data.InfraObjectId,
                                                    TimeAtChkPt = data.TimeAtChkPt,
                                                    CreateDate = data.CreateDate
                                                };
                                                GGList.Add(datalagList);
                                            }
                                        }
                                        GGList.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentGoldenGateLag = null;

                                            foreach (var datalag1 in GGList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentGoldenGateLag = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentGoldenGateLag.TimeAtChkPt;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In GoldenGateRepli Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In GoldenGateRepli Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion GoldenGateReplicationMonitor

                        #region HyperV

                        case (int)ReplicationType.HyperV:
                            {
                                try
                                {
                                    _logger.Info("24 DataLag Telerik Report Genrating for Hyper V replication type group Id - " + group.Id + "  ,group name -" + group.Name);
                                    List<HyperVDetails> nativelaglist = new List<HyperVDetails>();

                                    List<string> colorlist = new List<string>();
                                    //List<GoldenGateReplicationMonitor> GGList = new List<GoldenGateReplicationMonitor>();
                                    //var datalag = Facade.GoldenGateRepliMoniGetLast24Hrs(group.Id, "PRDR");
                                    var datalag = Facade.GetHourlyHyperVMonitorByInfraObjectId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        _logger.Info("datalag Is Not Null For Hyper V");
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                //var datalagList = new GoldenGateReplicationMonitor
                                                //{
                                                //    InfraObjectId = data.InfraObjectId,
                                                //    TimeAtChkPt = data.TimeAtChkPt,
                                                //    CreateDate = data.CreateDate
                                                //};
                                                //GGList.Add(datalagList);
                                                var datalagList = new HyperVDetails
                                                {
                                                    InfraObjectId = data.InfraObjectId,
                                                    HyperVDatalag = data.HyperVDatalag,
                                                    CreateDate = data.CreateDate
                                                };
                                                nativelaglist.Add(datalagList);
                                            }
                                        }
                                        nativelaglist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentHyperVDetailsLog = null;

                                            foreach (var datalag1 in nativelaglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentHyperVDetailsLog = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentHyperVDetailsLog.HyperVDatalag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("datalag Is  Null For Hyper V.");
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                    _logger.Info("24 DataLag Telerik Report Creation Completed for Hyper V.");
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In HyperV Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In HyperV Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion HyperV

                        #region VVR
                        case (int)ReplicationType.MSSQLFullDBVVRReplication:
                            {
                                try
                                {
                                    _logger.Info("24 DataLag Telerik Report Genrating for MSSQLFullDBVVRReplication replication type group Id - " + group.Id + "  ,group name -" + group.Name);

                                    List<RLinkMonitorSecUpdate> RlinkSecUpdate_List = new List<RLinkMonitorSecUpdate>();
                                    List<string> colorlist = new List<string>();

                                    var datalag = Facade.GetRlinkMonitorSecUpdate_Hour_ByInfraId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        _logger.Info("datalag Is Not Null For MSSQLFullDBVVRReplication.");

                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new RLinkMonitorSecUpdate
                                                {
                                                    InfraobjectID = data.InfraobjectID,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                RlinkSecUpdate_List.Add(datalagList);
                                            }
                                        }
                                        RlinkSecUpdate_List.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;

                                            CurrentRLinkMoni = null;
                                            //CurrentHyperVDetailsLog = null;

                                            foreach (var datalag1 in RlinkSecUpdate_List)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentRLinkMoni = datalag1;
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentRLinkMoni.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("datalag Is Null For MSSQLFullDBVVRReplication.");
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);

                                    _logger.Info("24 DataLag Telerik Report Creation Completed for MSSQLFullDBVVRReplication.");
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MSSQLFullDBVVRReplication Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MSSQLFullDBVVRReplication Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion VVR

                        #region Rsync

                        case (int)ReplicationType.RSync:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    //List<HanaDBMonitor> HanaDBMonitorlist = new List<HanaDBMonitor>();
                                    List<RSyncMonitor> RSyncMonitorlist = new List<RSyncMonitor>();
                                    //var datalag = Facade.GetHanaDBMonitorByInfraId(group.Id);
                                    var datalag = Facade.GetRsyncMonitorByInfraId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        _logger.Info("Records Available For 24 HRS Datalag Status Rsync Infra Id " + group.Id);
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new RSyncMonitor
                                                {
                                                    InfraobjectId = data.InfraobjectId,
                                                    Datalag = data.Datalag,
                                                    CreateDate = data.CreateDate
                                                };
                                                RSyncMonitorlist.Add(datalagList);
                                            }
                                        }
                                        RSyncMonitorlist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentRsyncMonitorLag = null;

                                            foreach (var datalag1 in RSyncMonitorlist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentRsyncMonitorLag = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentRsyncMonitorLag.Datalag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("Records Not Available For 24 HRS Datalag Status Rsync Infra Id " + group.Id);
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occcurred In ShowTable Method For RSync Case, While Generating 24 HRS DataLag Status Telerik Report, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occcurred In ShowTable Method For RSync Case, While Generating 24 HRS DataLag Status Telerik Report, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion Rsync

                        #region HP3PAR

                        case (int)ReplicationType.HP3PARMSSQLFULLDB:
                        case (int)ReplicationType.HP3PARORACLEFULLDB:
                        case (int)ReplicationType.HP3ParwithApplication:
                        case (int)ReplicationType.HP3PARMAXFULLDB:
                            {
                                try
                                {
                                    _logger.Info("24 DataLag Telerik Report Genrating for HP3PAR replication type group Id - " + group.Id + "  ,group name -" + group.Name);
                                    //  List<HyperVDetails> nativelaglist = new List<HyperVDetails>();
                                    List<HP3PAR_Monitor> HP3PAR_list = new List<HP3PAR_Monitor>();
                                    List<string> colorlist = new List<string>();

                                    // var datalag = Facade.GetHourlyHyperVMonitorByInfraObjectId(group.Id);
                                    var datalag = Facade.GetHP3PARMonitor_Hour_ByInfraId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        _logger.Info("datalag Is Not Null For HP3PAR ");
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);


                                                var datalagList = new HP3PAR_Monitor
                                                {
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                HP3PAR_list.Add(datalagList);
                                            }
                                        }
                                        HP3PAR_list.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentHP3PARMoni = null;

                                            foreach (var datalag1 in HP3PAR_list)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentHP3PARMoni = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentHP3PARMoni.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("datalag Is  Null For HP3PAR.");
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                    _logger.Info("24 DataLag Telerik Report Creation Completed for HP3PAR.");
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In HP3PAR Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In HP3PAR Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion HP3PAR

                        #region NutanixLeapRepl
                        case (int)ReplicationType.NutanixLeapReplication:
                            {
                                try
                                {
                                    _logger.Info("24 DataLag Telerik Report Genrating for NutanixLeapReplication replication type group Id - " + group.Id + "  ,group name -" + group.Name);

                                    List<NTNXLeapRcblEntReplMonitor> NutanixLeapRepMoniList = new List<NTNXLeapRcblEntReplMonitor>();
                                    List<string> colorlist = new List<string>();

                                    var datalag = Facade.GetNutanixRcblEntReplMonitor_GetByInfraId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        _logger.Info("datalag Is Not Null For NutanixLeapReplication.");

                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new NTNXLeapRcblEntReplMonitor
                                                {
                                                    InfraObjectId = data.InfraObjectId,
                                                    DataLagMAx = data.DataLagMAx,
                                                    CreateDate = data.CreateDate
                                                };
                                                NutanixLeapRepMoniList.Add(datalagList);
                                            }
                                        }
                                        NutanixLeapRepMoniList.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;

                                            CurrentNutanixLeapRcbl = null;

                                            foreach (var datalag1 in NutanixLeapRepMoniList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentNutanixLeapRcbl = datalag1;
                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentNutanixLeapRcbl.DataLagMAx;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("datalag Is Null For NutanixLeapReplication.");
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);

                                    _logger.Info("24 DataLag Telerik Report Creation Completed for NutanixLeapReplication.");
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In NutanixLeapReplication Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In NutanixLeapReplication Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion NutanixLeapRepl

                        #region AD
                        case (int)ReplicationType.ActiveDirectory:
                            {
                                List<string> colorlist = new List<string>();

                                List<ActiveDirectoryMonitor> ADMonitorlist = new List<ActiveDirectoryMonitor>();

                                var datalag = Facade.GetActiveDirectoryByHrsByInfraObjId(group.Id);


                                if (datalag != null && datalag.Count > 0)
                                {
                                    var addhour = new List<int>();
                                    foreach (var data in datalag)
                                    {
                                        int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                        if (!addhour.Contains(hour))
                                        {
                                            addhour.Add(hour);

                                            var datalagList = new ActiveDirectoryMonitor
                                            {
                                                //BusinessServiceName = group.appName,
                                                //InfraObjectName = group.Name,
                                                InfraObjectId = data.InfraObjectId,
                                                DataLag = data.DataLag,
                                                CreateDate = data.CreateDate
                                            };
                                            ADMonitorlist.Add(datalagList);
                                        }
                                    }
                                    ADMonitorlist.Reverse();



                                    for (int i = 0; i < 24; i++)
                                    {
                                        bool datalagFind = false;


                                        CurrenteBDRPLag = null;

                                        foreach (var datalag1 in ADMonitorlist)
                                        {
                                            if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                continue;
                                            datalagFind = true;

                                            CurrentADMonitorLag = datalag1;

                                            //if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                            //{
                                            //    datalagFind = false;
                                            //}
                                            //else
                                            //{
                                            //    datalagFind = true;
                                            //}
                                        }

                                        if (datalagFind)
                                        {

                                            string currentDatalag = CurrentADMonitorLag.DataLag;
                                            if (string.IsNullOrEmpty(currentDatalag))
                                            {
                                                colorlist.Add("NA");
                                            }
                                            else
                                            {
                                                if (currentDatalag != "NA")
                                                {
                                                    bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                    if (isHealth)
                                                    {
                                                        colorlist.Add("TRUE");
                                                    }
                                                    else
                                                    {

                                                        colorlist.Add("FALSE");
                                                    }
                                                }
                                                else
                                                {
                                                    colorlist.Add("NA");
                                                }
                                            }


                                        }  // parent if closed
                                        else
                                        {
                                            colorlist.Add("NA");
                                        }





                                        // }

                                    }

                                }
                                else
                                {
                                    for (int t = 0; t < 24; t++)
                                    {
                                        colorlist.Add("NA");
                                    }

                                }
                                dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                break;
                            }
                        #endregion AD

                        #region RoboCopy

                        case (int)ReplicationType.RoboCopy:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    List<RoboCopyLogs> RoboCopyLogsList = new List<RoboCopyLogs>();

                                    var datalag = Facade.GetRoboCopyLogsByInfraId(group.Id);
                                    _logger.Info("Datalag Status Report Creation Start For Infra Id -" + group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        _logger.Info("Records Available For 24 HRS Datalag Status RoboCopy Infra Id " + group.Id);
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new RoboCopyLogs
                                                {
                                                    InfraObjectId = data.InfraObjectId,
                                                    Datalag = data.Datalag,
                                                    CreateDate = data.CreateDate
                                                };
                                                RoboCopyLogsList.Add(datalagList);
                                            }
                                        }
                                        RoboCopyLogsList.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentRoboLag = null;

                                            foreach (var datalag1 in RoboCopyLogsList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentRoboLag = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentRoboLag.Datalag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("Records Not Available For 24 HRS Datalag Status RoboCopy Infra Id " + group.Id);
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occcurred In ShowTable Method For RoboCopy Case, While Generating 24 HRS DataLag Status Telerik Report, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occcurred In ShowTable Method For RoboCopy Case, While Generating 24 HRS DataLag Status Telerik Report, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion RoboCopy

                        #region AZURE
                        case (int)ReplicationType.RecoveryAzureSite:
                            {
                                List<string> colorlist = new List<string>();
                                List<Azure_Monitoring> azurelist = new List<Azure_Monitoring>();
                                //var datalag = Facade.GetEMCSRDFHourlyByReplicationId(group.PRReplicationId);
                                var datalag = Facade.Azure_MonitorHourlyByInfra(group.Id);
                                if (datalag != null && datalag.Count > 0)
                                {
                                    var addhour = new List<int>();
                                    foreach (var data in datalag)
                                    {
                                        int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                        if (!addhour.Contains(hour))
                                        {
                                            addhour.Add(hour);

                                            var datalagList = new Azure_Monitoring
                                            {

                                                InfraObjectId = data.InfraObjectId,
                                                DataLag = data.DataLag,
                                                CreateDate = data.CreateDate
                                            };
                                            azurelist.Add(datalagList);
                                        }
                                    }
                                    azurelist.Reverse();



                                    for (int i = 0; i < 24; i++)
                                    {
                                        bool datalagFind = false;


                                        CurrentAzurLag = null;

                                        foreach (var datalag1 in azurelist)
                                        {
                                            if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                continue;
                                            datalagFind = true;

                                            CurrentAzurLag = datalag1;


                                        }



                                        if (datalagFind)
                                        {
                                            string currentDatalag = CurrentAzurLag.DataLag;
                                            if (string.IsNullOrEmpty(currentDatalag))
                                            {
                                                colorlist.Add("NA");
                                            }
                                            else
                                            {
                                                if (currentDatalag != "NA")
                                                {
                                                    bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                    if (isHealth)
                                                    {

                                                        colorlist.Add("TRUE");
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("FALSE");
                                                    }
                                                }
                                                else
                                                {
                                                    colorlist.Add("NA");
                                                }
                                            }


                                        }  // parent if closed
                                        else
                                        {

                                            colorlist.Add("NA");
                                        }




                                        // }

                                    }

                                }
                                else
                                {
                                    for (int t = 0; t < 24; t++)
                                    {


                                        colorlist.Add("NA");
                                    }

                                }
                                dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                break;
                            }
                        #endregion AZURE


                        #region Zerto
                        case (int)ReplicationType.Zerto:
                        case (int)ReplicationType.ZertoMssqlFULLDB:
                        case (int)ReplicationType.ZertoOracleFULLDB:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();

                                    List<zertomonitorstatus> Zertolist = new List<zertomonitorstatus>();

                                    var datalag = Facade.Zerto_MonitorHourlyByInfra(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new zertomonitorstatus
                                                {

                                                    InfraObjectID = data.InfraObjectID,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                Zertolist.Add(datalagList);
                                            }
                                        }
                                        Zertolist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentZertoMonitorLog = null;

                                            foreach (var datalag1 in Zertolist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentZertoMonitorLog = datalag1;


                                            }



                                            if (datalagFind)
                                            {
                                                string currentDatalag = CurrentZertoMonitorLog.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {

                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {

                                                colorlist.Add("NA");
                                            }
                                        }

                                    }
                                    else
                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);

                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In Zerto Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In Zerto Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion Zerto

                        #region EMCSRDFCG

                        case (int)ReplicationType.EMCSRDFCGAPPLICATION:
                        case (int)ReplicationType.EMCSRDFCGORACLEFULLDB:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();
                                    _logger.Info("24 HRS DataLag Report Creation Start For EMCSRDFCGAPPLICATION.");
                                    // List<HanaDBMonitor> HanaDBMonitorlist = new List<HanaDBMonitor>();
                                    List<EMCSRDFCGMonitoring> EMCSRDFCGList = new List<EMCSRDFCGMonitoring>();
                                    // var datalag = Facade.GetHanaDBMonitorByInfraId(group.Id);
                                    var datalag = Facade.GetEMCSRDFCGLog_GetByInfraID(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new EMCSRDFCGMonitoring
                                                {
                                                    InfraobjectId = data.InfraobjectId,
                                                    DataLag = data.DataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                EMCSRDFCGList.Add(datalagList);
                                            }
                                        }
                                        EMCSRDFCGList.Reverse();


                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentEMCSRDFCGMonitorLag = null;

                                            foreach (var datalag1 in EMCSRDFCGList)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentEMCSRDFCGMonitorLag = datalag1;


                                            }
                                            //string[] xlColumn = { "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z" };

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentEMCSRDFCGMonitorLag.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("Records Not Available For 24 HRS Datalag Status Rsync Infra Id " + group.Id);
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occurred In PrepareScreenReport Method, Error Messgae " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occurred In PrepareScreenReport Method, InnerException Messgae " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion EMCSRDFCG

                        #region MongoDB

                        case (int)ReplicationType.MongoDB:
                            {
                                try
                                {
                                    _logger.Info("24 DataLag Telerik Report Genrating for MongoDB  type group Id - " + group.Id + "  ,group name -" + group.Name);
                                    //  List<HyperVDetails> nativelaglist = new List<HyperVDetails>();
                                    List<MongoDBDMonitorStatus> Mongo_list = new List<MongoDBDMonitorStatus>();
                                    List<string> colorlist = new List<string>();

                                    // var datalag = Facade.GetHourlyHyperVMonitorByInfraObjectId(group.Id);
                                    var datalag = Facade.GetMongoDBReplication_Hour_ByInfraId(group.Id);
                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        _logger.Info("datalag Is Not Null For MongoDB ");
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);


                                                var datalagList = new MongoDBDMonitorStatus
                                                {
                                                    InfraObjectId = data.InfraObjectId,
                                                    PRDatalag = data.PRDatalag,
                                                    CreateDate = data.CreateDate
                                                };
                                                Mongo_list.Add(datalagList);
                                            }
                                        }
                                        Mongo_list.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentMongoRMoni = null;

                                            foreach (var datalag1 in Mongo_list)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentMongoRMoni = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrentMongoRMoni.PRDatalag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("datalag Is  Null For MongoDB.");
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                    _logger.Info("24 DataLag Telerik Report Creation Completed for MongoDB.");
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MongoDB Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MongoDB Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion MongoDB

                        #region AzureGatewayMonitor
                        case (int)ReplicationType.AzureGatewayReplication:

                            try
                            {
                                List<string> colorlist = new List<string>();
                                colorlist.Add("NA");
                                dt.Rows.Add("NA");
                            }
                            catch (Exception ex)
                            {

                                _logger.Error("Error in AzureGatewayMonitor DataLag:" + ex.Message);
                            }
                            break;
                        #endregion

                        #region OracleCloudDataGuard

                        case (int)ReplicationType.OracleCloudDataGuard:
                            {

                                List<string> colorlist = new List<string>();
                                // _logger.Info("Oracle DataGuard start");
                                List<OracleCloudDataGuard_DBSystemDetails> ocdgList = new List<OracleCloudDataGuard_DBSystemDetails>();
                                // To do - Need to send infraid instead of groupid 
                                var datalag = Facade.Get_OracleCloudDataGuard_Logs_ByInfra(group.Id);

                                if (datalag != null && datalag.Count > 0)
                                {
                                    var addhour = new List<int>();
                                    foreach (var data in datalag)
                                    {
                                        int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                        if (!addhour.Contains(hour))
                                        {
                                            addhour.Add(hour);

                                            var datalagList = new OracleCloudDataGuard_DBSystemDetails
                                            {
                                                PRCompartmentName = group.appName,
                                                PRDBSystemDisplayName = group.Name,
                                                InfraObjectId = data.InfraObjectId,
                                                ApplyLag = !data.ApplyLag.Contains(":") ? Utility.ConvertSecondsToHHMMSS(data.ApplyLag.Split()[0]) : data.ApplyLag,
                                                CreateDate = data.CreateDate
                                            };
                                            ocdgList.Add(datalagList);
                                        }
                                    }

                                    ocdgList.OrderBy(i => i.CreateDate.Hour);
                                    //  oraloglist.Reverse();

                                    //string[] xlColumn = { "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z" };



                                    for (int i = 0; i < 24; i++)
                                    {
                                        bool datalagFind = false;

                                        CurrentDatalag = null;

                                        foreach (var datalag1 in ocdgList)
                                        {
                                            if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                continue;
                                            datalagFind = true;

                                            OcdgCurrentDatalag = datalag1;


                                        }

                                        if (datalagFind)
                                        {

                                            string currentDatalag = OcdgCurrentDatalag.ApplyLag;
                                            if (string.IsNullOrEmpty(currentDatalag))
                                            {
                                                colorlist.Add("NA");


                                            }
                                            else
                                            {
                                                if (currentDatalag != "NA")
                                                {
                                                    bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                    if (isHealth)
                                                    {

                                                        colorlist.Add("TRUE");
                                                    }
                                                    else
                                                    {

                                                        colorlist.Add("FALSE");
                                                    }
                                                }
                                                else
                                                {
                                                    colorlist.Add("NA");

                                                }
                                            }


                                        }  // parent if closed
                                        else
                                        {

                                            colorlist.Add("NA");

                                        }


                                    }

                                }
                                else
                                {
                                    for (int t = 0; t < 24; t++)
                                    {
                                        colorlist.Add("NA");

                                    }

                                }

                                dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                break;
                            }

                        #endregion

                        #region OCExaDB

                        case (int)ReplicationType.OC_ExaDB:
                            {

                                List<string> colorlist = new List<string>();
                                // _logger.Info("Oracle DataGuard start");
                                List<OC_ExaDb_Monitor> ocdgList = new List<OC_ExaDb_Monitor>();
                                // To do - Need to send infraid instead of groupid 
                                var datalag = Facade.Get_OCExaDBMonitor_Logs_ByInfra(group.Id);

                                if (datalag != null && datalag.Count > 0)
                                {
                                    var addhour = new List<int>();
                                    foreach (var data in datalag)
                                    {
                                        int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                        if (!addhour.Contains(hour))
                                        {
                                            addhour.Add(hour);

                                            var datalagList = new OC_ExaDb_Monitor
                                            {
                                                PRCompartmentName = group.appName,
                                                PRDBSystemDisplayName = group.Name,
                                                InfraObjectId = data.InfraObjectId,
                                                ApplyLag = !data.ApplyLag.Contains(":") ? Utility.ConvertSecondsToHHMMSS(data.ApplyLag.Split()[0]) : data.ApplyLag,
                                                CreateDate = data.CreateDate
                                            };
                                            ocdgList.Add(datalagList);
                                        }
                                    }

                                    ocdgList.OrderBy(i => i.CreateDate.Hour);
                                    //  oraloglist.Reverse();

                                    //string[] xlColumn = { "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z" };



                                    for (int i = 0; i < 24; i++)
                                    {
                                        bool datalagFind = false;

                                        CurrentDatalag = null;

                                        foreach (var datalag1 in ocdgList)
                                        {
                                            if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                continue;
                                            datalagFind = true;

                                            OcExaCurrentDatalag = datalag1;


                                        }

                                        if (datalagFind)
                                        {

                                            string currentDatalag = OcdgCurrentDatalag.ApplyLag;
                                            if (string.IsNullOrEmpty(currentDatalag))
                                            {
                                                colorlist.Add("NA");


                                            }
                                            else
                                            {
                                                if (currentDatalag != "NA")
                                                {
                                                    bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                    if (isHealth)
                                                    {

                                                        colorlist.Add("TRUE");
                                                    }
                                                    else
                                                    {

                                                        colorlist.Add("FALSE");
                                                    }
                                                }
                                                else
                                                {
                                                    colorlist.Add("NA");

                                                }
                                            }


                                        }  // parent if closed
                                        else
                                        {

                                            colorlist.Add("NA");

                                        }


                                    }

                                }
                                else
                                {
                                    for (int t = 0; t < 24; t++)
                                    {
                                        colorlist.Add("NA");

                                    }

                                }

                                dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                break;
                            }

                        #endregion

                        #region Azurecosmos

                        case (int)ReplicationType.AzureCosmosDB:
                            {
                                try
                                {
                                    _logger.Info("24 DataLag Telerik Report Genrating for AzureCosmosDB  type group Id - " + group.Id + "  ,group name -" + group.Name);
                                    //  List<HyperVDetails> nativelaglist = new List<HyperVDetails>();
                                    List<AzureCosmosDBMonitor> hadrlaglist = new List<AzureCosmosDBMonitor>();

                                    var datalag = Facade.AzureCosmosDBMonitorHourlyByInfra(group.Id);

                                    List<string> colorlist = new List<string>();


                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        _logger.Info("datalag Is Not Null For CurrenteAzurePaasMonitor ");
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);


                                                var datalagList = new AzureCosmosDBMonitor
                                                {

                                                    Datalag = data.Datalag,
                                                    CreateDate = data.CreateDate
                                                };
                                                hadrlaglist.Add(datalagList);
                                            }
                                        }
                                        hadrlaglist.Reverse();



                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrenteAzureCosmosDBMonitor = null;

                                            foreach (var datalag1 in hadrlaglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrenteAzureCosmosDBMonitor = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                string currentDatalag = CurrenteAzureCosmosDBMonitor.Datalag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("datalag Is  Null For AzureCosmosDBMonitor.");
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                    _logger.Info("24 DataLag Telerik Report Creation Completed for AzureCosmosDBMonitor.");
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In AzureCosmosDBMonitor Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In AzureCosmosDBMonitor Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }

                        #endregion Azurecosmos

                        #region vSphereReplication

                        case (int)ReplicationType.vSphereReplication:
                            {

                                int Jcount = datalaglist.Count - 1;

                                int k = 2, l = 7;
                                int row = 7;
                                int dataCount = finalResult.Count;

                                try
                                {
                                    _logger.Info("24 DataLag Telerik Report Genrating for vSphereReplication  type group Id - " + group.Id + "  ,group name -" + group.Name);

                                    List<string> colorlist = new List<string>();
                                    List<VsphereMonitorReport> vsphereloglist = new List<VsphereMonitorReport>();
                                    var datalag = Facade.GetvSphereHourlyByInfraId(group.Id);

                                    if (datalag != null && datalag.Count > 0)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new VsphereMonitorReport
                                                {
                                                    //ApplicationName = group.appName,
                                                    //InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    //  DataLag = "00:00:00",
                                                    CreateDate = data.CreateDate
                                                };
                                                vsphereloglist.Add(datalagList);
                                            }
                                        }
                                        vsphereloglist.Reverse();

                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;


                                            CurrentvsphereLag = null;

                                            foreach (var datalag1 in vsphereloglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                    continue;
                                                datalagFind = true;

                                                CurrentvsphereLag = datalag1;


                                            }

                                            if (datalagFind)
                                            {

                                                //string currentDatalag = CurrentvsphereLag.PRDatalag;
                                                string currentDatalag = "00:00:00";

                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {

                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }

                                        }

                                    }
                                    else
                                    {
                                        _logger.Info("datalag Is  Null For vSphereReplication.");
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }

                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                    _logger.Info("24 DataLag Telerik Report Creation Completed for vSphereReplication.");
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MongoDB Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In vSphereReplication Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;
                            }
                        #endregion vSphereReplication

                        #region default
                        default:
                            {
                                try
                                {
                                    List<string> colorlist = new List<string>();

                                    var datalag = Facade.GetHourlyOracleLogsByInfraObjectId(group.Id, "24");

                                    if (datalag != null)
                                    {
                                        var addhour = new List<int>();
                                        foreach (var data in datalag)
                                        {
                                            int hour = Convert.ToDateTime(data.CreateDate).Hour;

                                            if (!addhour.Contains(hour))
                                            {
                                                addhour.Add(hour);

                                                var datalagList = new OracleLog
                                                {
                                                    BusinessServiceName = group.appName,
                                                    InfraObjectName = group.Name,
                                                    InfraObjectId = data.InfraObjectId,
                                                    PRLogTime = data.PRLogTime,
                                                    PRSequenceNo = data.PRSequenceNo,
                                                    DRLogTime = data.DRLogTime,
                                                    DRSequenceNo = data.DRSequenceNo,
                                                    CurrentDataLag = data.CurrentDataLag,
                                                    CreateDate = data.CreateDate
                                                };
                                                datalaglist.Add(datalagList);
                                            }
                                        }
                                        datalaglist.Reverse();




                                        for (int i = 0; i < 24; i++)
                                        {
                                            bool datalagFind = false;

                                            foreach (var datalag1 in datalaglist)
                                            {
                                                if (Convert.ToDateTime(datalag1.CreateDate).Hour != i)
                                                {
                                                    datalagFind = false;
                                                }
                                                else
                                                {
                                                    datalagFind = true;
                                                }

                                                CurrentDatalag = datalag1;

                                            }

                                            if (datalagFind)
                                            {
                                                string currentDatalag = CurrenteAlwaysonLag.DataLag;
                                                if (string.IsNullOrEmpty(currentDatalag))
                                                {
                                                    colorlist.Add("NA");
                                                }
                                                else
                                                {
                                                    if (currentDatalag != "NA")
                                                    {
                                                        bool isHealth = Utility.GetReportDatlagHealth(currentDatalag, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                                                        if (isHealth)
                                                        {
                                                            colorlist.Add("TRUE");
                                                        }
                                                        else
                                                        {
                                                            colorlist.Add("FALSE");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        colorlist.Add("NA");
                                                    }
                                                }


                                            }  // parent if closed
                                            else
                                            {
                                                colorlist.Add("NA");
                                            }
                                        }

                                    }


                                    {
                                        for (int t = 0; t < 24; t++)
                                        {
                                            colorlist.Add("NA");
                                        }


                                    }
                                    dt.Rows.Add(group.appName, group.desc, group.Name, colorlist[0], colorlist[1], colorlist[2], colorlist[3], colorlist[4], colorlist[5], colorlist[6], colorlist[7], colorlist[8], colorlist[9], colorlist[10], colorlist[11], colorlist[12], colorlist[13], colorlist[14], colorlist[15], colorlist[16], colorlist[17], colorlist[18], colorlist[19], colorlist[20], colorlist[21], colorlist[22], colorlist[23]);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception Occured In MSSQLAlwaysOn Solution, Error Message " + ex.Message);
                                    if (ex.InnerException != null)
                                        _logger.Error("Exception Occured In MSSQLAlwaysOn Solution, InnerException Message " + ex.InnerException.Message);
                                }
                                break;




                            }









                        #endregion default


                    }
                    _logger.Info("24 HRS DataLag Status Telerik Report Creation Completed.");

                    this.DataSource = dt;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method Of 24 HRS DataLag Status Telerik Report, Error Message " + ex.Message);

                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method Of 24 HRS DataLag Status Telerik Report, InnerException Message " + ex.InnerException.Message);

            }

        }




    }
}


