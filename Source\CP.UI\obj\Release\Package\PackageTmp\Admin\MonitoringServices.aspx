﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="MonitoringServices.aspx.cs" Inherits="CP.UI.Admin.MonitoringServices" Title="Continuity Patrol :: Manage Monitoring Services" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.combobox/style.css" rel="stylesheet" />
    <style>
        .chosen-select + .chosen-container {
            width: 48% !important;
            opacity: 1 !important;
        }

        .combobox_selector {
            width: 240px !important;
        }
    </style>
    <style type="text/css">
        .bootstrap-select[class*="col"] .btn {
            width: 99% !important;
        }

        .col-md-6 {
            padding-right: 0px !important;
            width: 48.5% !important;
        }

        .widget.widget-heading-simple > .widget-head {
            height: 34px !important;
        }

        tr > td:first-child {
            font-weight: normal;
            /*vertical-align: top;*/
        }
    </style>
    <style type="text/css">
        #ctl00_cphBody_pnlDomain #ctl00_cphBody_combobox1 {
            width: 225px;
        }

        .st_div .combobox_button {
            left: 225px !important;
        }

        .st_div .log-ext3 i:before {
            font: 16px 'Glyphicons Regular' !important;
            left: 4px !important;
            top: 11px !important;
        }

        .st_div {
            position: relative;
            margin-top: -15px;
        }
    </style>
    <style>
        .chosen-select + .chosen-container {
            width: 48% !important;
            opacity: 1 !important;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdndelid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="Updatepanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <h3><span class="business-setting-icon vertical-sub"></span>Manage Monitoring Services</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Business Service <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="DdlApplication" runat="server" AutoPostBack="true" OnSelectedIndexChanged="DdlApplication_SelectedIndexChanged"
                                            CssClass="chosen-select col-md-6" data-style="btn-default">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator6" ControlToValidate="DdlApplication" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>

                                </div>
                                <%--<asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>--%>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        InfraObject <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="DdlGroup" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="True" Enabled="false"
                                            OnSelectedIndexChanged="DdlGroup_SelectedIndexChanged">
                                            <asp:ListItem Value="0">- Select InfraObject -</asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator2" ControlToValidate="DdlGroup" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Server <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="DdlServer" runat="server" Enabled="false" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="True"
                                            OnSelectedIndexChanged="DdlServer_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator3" ControlToValidate="DdlServer" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="ddlType">
                                        Server Authentication Type <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlType" runat="server" TabIndex="3" AutoPostBack="true"
                                            CssClass="selectpicker col-md-6" data-style="btn-default" OnSelectedIndexChanged="ddlType_SelectedIndexChanged">
                                            <asp:ListItem Value="0">Select Type</asp:ListItem>
                                            <asp:ListItem Value="1">Use ps -ef</asp:ListItem>
                                            <asp:ListItem Value="2">Use WMI</asp:ListItem>
                                            <asp:ListItem Value="3">Use Workflow</asp:ListItem>
                                            <asp:ListItem Value="4">Use SSH</asp:ListItem>
                                            <asp:ListItem Value="5">Use PowerShell</asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator5" ControlToValidate="ddlType" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group" id="workflowtypediv" visible="false" runat="server">
                                    <label class="col-md-3 control-label" for="ddlType">
                                        Workflow Type <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="wrkflwtype" runat="server" TabIndex="3" AutoPostBack="true"
                                            CssClass="selectpicker col-md-6" data-style="btn-default" OnSelectedIndexChanged="wrkflwtype_SelectedIndexChanged">
                                            <asp:ListItem Value="0">Select Workflow Type</asp:ListItem>
                                            <asp:ListItem Value="1">Monitor</asp:ListItem>
                                            <asp:ListItem Value="2">DR Ready</asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator1" ControlToValidate="wrkflwtype" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group" id="workflowdiv" visible="false" runat="server">
                                    <label class="col-md-3 control-label">
                                        Workflow <span class="inactive">*</span>
                                    </label>
                                    <%-- Removed as per discussion with Kiran Sir CPROOT-3182 , CPROOT-3371 <span class="inactive">*</span>--%>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlworkflow" runat="server" Enabled="false" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="True"
                                            OnSelectedIndexChanged="ddlworkflow_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator7" ControlToValidate="ddlworkflow" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group" id="ServicePathdiv" visible="true" runat="server">
                                    <label class="col-md-3 control-label">
                                        Service / Process Name  <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtServicePath" runat="server" Enabled="false" CssClass="form-control"></asp:TextBox>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator4" ControlToValidate="txtServicePath" ValidationGroup="AddGroup" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                        <asp:regularexpressionvalidator id="rev" runat="server" cssclass="error" controltovalidate="txtServicePath" xmlns:asp="#unknown" errormessage="Spaces are not allowed!" validationexpression="[^\s]+"></asp:regularexpressionvalidator>
                                    </div>
                                </div>
                                <hr class="separator" />
                                <div class="form-group">
                                    <div class="col-md-6">
                                    </div>
                                    <div class="col-md-6">
                                        <asp:Button ID="btnAdd" runat="server" Text="Save" Enabled="false" Style="margin-left: 35px" CommandArgument="Save"
                                            CssClass="btn btn-primary" Width="15%" ValidationGroup="AddGroup" OnClick="btnAdd_Click" />
                                        <asp:Label ID="lblexist" runat="server" Text="Monitoring service already exist" CssClass="error" Visible="false"></asp:Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <asp:Panel ID="panelListView" CssClass="row" runat="server">

                            <div class="col-md-12 form-horizontal uniformjs">
                                <asp:ListView ID="lvGroup" runat="server" OnItemDeleting="lvGroup_ItemDeleting"
                                    OnItemEditing="lvGroup_ItemEditing">
                                    <LayoutTemplate>
                                        <table class="table table-striped table-bordered table-condensed margin-bottom-none" width="100%">
                                            <thead>
                                                <tr>
                                                    <th style="width: 10%">S.No
                                                    </th>
                                                    <th style="width: 20%">Business Service
                                                    </th>
                                                    <th style="width: 20%">InfraObject Name
                                                    </th>
                                                    <th style="width: 20%;">Server
                                                    </th>
                                                    <th style="width: 20%;">Service/Process /
                                                                            </br> Workflow
                                                    </th>
                                                    <th>Action
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div style="overflow: auto; height: 190px;">
                                            <%--class="slim-scroll chat-items" data-scroll-height="190px" data-scroll-size="0"--%>
                                            <table class="table table-striped table-bordered table-condensed" width="100%"
                                                id="lvGrouptbl">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td class="th table-check-cell" style="width: 10%">
                                                <asp:Label ID="lblId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                <%#Container.DataItemIndex+1%>
                                            </td>
                                            <td style="width: 20%;">
                                                <div class="tdword-wrap">
                                                    <asp:Label ID="lblapplicationId" runat="server" Text='<%#GetApplicationName(Eval("InfraobjectId")) %>' />
                                                </div>
                                            </td>
                                            <td style="width: 20%">
                                                <div class="tdword-wrap">

                                                    <asp:Label ID="lbGroupId" Visible="true" runat="server" Text='<%#GetGroupName(Eval("InfraobjectId")) %>' />
                                                </div>
                                            </td>
                                            <td style="width: 20%;">
                                                <div class="tdword-wrap">
                                                    <asp:Label ID="lblServerId" runat="server" Text='<%#GetServerName( Eval("ServerId")) %>' />
                                                </div>
                                            </td>

                                            <td style="width: 20%;">
                                                <div class="tdword-wrap">
                                                    <%--<asp:Label ID="lblServerPath" runat="server" Text='<%# Eval("ServicePath") %>' />--%>
                                                    <asp:Label ID="Label1" runat="server" Text='<%#GetServiceOrWorkflow(Eval("Id")) %>' />
                                                </div>
                                            </td>
                                            <td style="width: 10%;">
                                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                <asp:ImageButton ID="ImgDelete" runat="server" Visible="true" Enabled="true"
                                                    CommandName="Delete" AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                <%--<TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                    ConfirmText='<%# "Are you sure you want to delete server " + GetServerName( Eval("ServerId")) +  "? " %>'
                                                    OnClientCancel="CancelClick">
                                                </TK1:ConfirmButtonExtender>--%>
                                                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                    ConfirmText='<%# "Are you sure you want to delete service ? " %>'
                                                    OnClientCancel="CancelClick">
                                                </TK1:ConfirmButtonExtender>
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                    <EmptyDataTemplate>
                                        <tr>
                                            <td>
                                                <div class="message warning align-center bold no-margin no-bottom-margin">
                                                    <asp:Label ID="pageResult" runat="server" Text="No Records"></asp:Label>
                                                </div>
                                            </td>
                                        </tr>
                                    </EmptyDataTemplate>
                                </asp:ListView>
                            </div>
                        </asp:Panel>

                        <asp:Panel ID="pnlPrivType" runat="server" Style="margin: auto;" Visible="false">
                            <%--display: none--%>
                            <div class="modal bg" style="display: block;">
                                <div class="modal-dialog" style="width: 500px;">
                                    <div class="modal-content  widget-body-white">
                                        <div class="modal-header">
                                            <asp:LinkButton ID="lnkbtnClose" class="close" runat="server" CommandName="Close" OnClick="lnkbtnClose_Click"> ×</asp:LinkButton>
                                            <h3 class="modal-title">User Authentication</h3>
                                        </div>
                                        <div class="modal-body">
                                            <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                                <ContentTemplate>
                                                    <div class="col-md-12 form-horizontal uniformjs">

                                                        <div class="col-md-12 form-group">
                                                            <div class="col-md-4">
                                                                <label>User Name</label><span style="color: red">*</span>
                                                            </div>
                                                            <div class="col-md-8">
                                                                <input type="hidden" id="UserEncrypt" runat="server" />
                                                                <asp:TextBox ID="UserName" Width="100%" runat="server" autocomplete="off" CssClass="form-control" placeholder="Username" onfocus="cleartext()" onblur="getUserNameHash(this)"></asp:TextBox>
                                                                <asp:RequiredFieldValidator ID="UserNameRequired" runat="server" ControlToValidate="UserName" Display="Dynamic" ErrorMessage="Required." ToolTip="User Name is required." CssClass="error"
                                                                    ValidationGroup="LoginUser"></asp:RequiredFieldValidator>
                                                            </div>
                                                        </div>
                                                        <div class="clearfix"></div>
                                                        <div class="col-md-12 form-group">
                                                            <div class="col-md-4">
                                                                <label>Password</label><span style="color: red">*</span>
                                                            </div>
                                                            <div class="col-md-8">

                                                                <input type="hidden" id="PassEncyptHidden" runat="server" />
                                                                <input type="hidden" id="Hidden1" runat="server" />
                                                                <asp:TextBox ID="Password" runat="server" EnableViewState="false" TextMode="Password" autocomplete="off" CssClass="form-control" placeholder="Password" ReadOnly="false" onblur="getPasswordHash(this)" onfocus="clearControlData(this)" Width="100%"></asp:TextBox>

                                                                <asp:RequiredFieldValidator ID="PasswordRequired" runat="server" ControlToValidate="Password" ErrorMessage="Required." CssClass="error" Display="Dynamic"
                                                                    ToolTip="Password is required." ValidationGroup="LoginUser">

                                                                </asp:RequiredFieldValidator>
                                                            </div>
                                                        </div>
                                                        <div class="clearfix"></div>
                                                        <div class="col-ms-12 form-group">
                                                            <div class="col-md-4"></div>
                                                            <div class="col-md-8">
                                                                <asp:CheckBox ID="chkActiveDirectory" runat="server" AutoPostBack="true" OnCheckedChanged="chkActiveDirectory_CheckedChanged" />

                                                                <asp:Label ID="Label2" Text="AD" runat="server" CssClass="padding padding-none-TB"></asp:Label>
                                                            </div>
                                                        </div>
                                                        <div class="clearfix"></div>

                                                        <asp:Panel ID="pnlDomain" CssClass="col-md-12 form-group" runat="server" Visible="false">
                                                            <div class="col-md-4">
                                                                <label>
                                                                    <asp:Label ID="lbldomainName" runat="server" Text="Domain"></asp:Label><span style="color: red">*</span></label>
                                                            </div>
                                                            <div class="col-md-8 st_div">
                                                                <div class="input-icon left">
                                                                    <span class="glyphicons globe log-ext3"><i></i></span>
                                                                    <div>
                                                                        <input id="combobox1" type="text" runat="server" visible="false" placeholder="Enter Domain" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </asp:Panel>
                                                    </div>
                                                    <div>
                                                        <asp:Label ID="labelSDErrormessage" runat="server" CssClass="error" Visible="false"></asp:Label>
                                                    </div>
                                                </ContentTemplate>
                                            </asp:UpdatePanel>
                                        </div>
                                        <div class="modal-footer">
                                            <div class="col-xs-5 text-left" style="padding-top: 10px;">
                                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required Fields</span>
                                            </div>
                                            <div class="col-xs-7">
                                                <asp:Button ID="btnSave" CssClass="btn btn-primary" Visible="false" runat="server" Text="OK" CommandArgument="Login"
                                                    TabIndex="18" OnClick="btnAdd_Click" />

                                                <asp:Button ID="btnsavdel" CssClass="btn btn-primary" Visible="false" runat="server" Text="OK" CommandArgument="Login"
                                                    TabIndex="18" OnClick="btnsavdel_Click" />

                                                <asp:Button ID="Button1" CssClass="btn btn-primary" runat="server"
                                                    Text="Close" CausesValidation="False" TabIndex="19" OnClick="Button1_Click" Style="width: 25%; /*display: inline-block;*/" />

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>

                        <div class="message no-margin no-bottom-margin">
                            <asp:Label ID="lblError" runat="server" ForeColor="Red"></asp:Label>
                            <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        </div>
                    </div>
                </div>
                <%--</ContentTemplate>
                                </asp:UpdatePanel>--%>
                           <%-- </div>
                        </div>
                    </div>
                </div>--%>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>

    
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script type="text/javascript" src="../Script/jquery.combobox.js"></script>
    <script src="../Script/Login.js" type="text/javascript"></script>
    <script src="../Script/MaskedPassword.js"></script>
    <script src="../Script/EncryptDecrypt.js"></script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="../Script/jquery.simple-dtpicker.js" type="text/javascript"></script>
    <script src="../Script/jquery.timepicker.min.js" type="text/javascript"></script>

    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
        $(document).ready(function () {
            //$(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
    <script type="text/javascript">

        function binddropdown() {
            $.ajax({
                type: "POST",
                url: "MonitoringServices.aspx/DiscoverDomains",
                data: "{}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                success: function Success(data) {
                    getBindValue(data.d);
                }
            });
        }

        function getBindValue(value) {

            var data = value.split(":");
            for (var i = 0; i < data.length; i++) {

                var volume = data[i].split(",");
                var text = volume[0];
                jQuery(function () {
                    jQuery('[id$=combobox1]').combobox([
                            text
                    ]);
                    $('[id$=combobox1]').val(volume[0]);

                });
            }
        }

        function cleartext() {

            var input = document.createElement('input');
            input.value = $('[id$=UserEncrypt]').val();
            if (input.value != "" && input.value != null && $('[id$=ctl00_cphBody_UserName]').val() != "") {
                $('[id$=ctl00_cphBody_UserName]').val(getOrignalData(input, $('#ctl00_cphBody_hdfStaticGuid').val()));
            }
            $('[id$=UserEncrypt]').val("");
            $('[id$=txtcaptcha]').val("");
            $('[id$=ctl00_cphBody_Password]').val("");
            $('[id$=passHidden]').val("");
            $('[id$=ctl00_cphBody_PassEncyptHidden]').val("");
        }

    </script>
</asp:Content>
