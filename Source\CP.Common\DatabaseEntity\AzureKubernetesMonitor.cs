﻿using CP.Common.Base;
using CP.Common.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{

    [Serializable]
    [DataContract(Name = "AzureKubernetesMonitor", Namespace = "http://www.CP.com/types")]
    public class AzureKubernetesMonitor: BaseEntity
    {
        #region 
        [DataMember]
        public string kubernetServiceName
        {
            get;
            set;
        }

        [DataMember]
        public string kubernetServiceResourceGroup
        {
            get;
            set;
        }

        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }
     

        [DataMember]
        public string kubernetServiceStatus
        {
            get;
            set;
        }

        [DataMember]
        public string NodePools
        {
            get;
            set;
        }

        [DataMember]
        public int Id
        {
            get;
            set;
        }

        [DataMember]
        public string SetName
        {
            get;
            set;
        }


        [DataMember]
        public string SetStatus
        {
            get;
            set;
        }

        [DataMember]
        public string SetResourceGroup
        {
            get;
            set;
        }


        [DataMember]
        public string SetLocation
        {
            get;
            set;
        }

    
        [DataMember]
        public string InstanceCount
        {
            get;
            set;
        }

        [DataMember]
        public string AvailabilityZone
        {
            get;
            set;
        }

        [DataMember]
        public string Scaling
        {
            get;
            set;
        }

        [DataMember]
        public string Instances_Status
        {
            get;
            set;
        }

        [DataMember]
        public string Instances_ProvisioningState
        {
            get;
            set;
        }


        [DataMember]
        public string ReplicationId
        {
            get;
            set;
        }

      
        public AzureKubernetesMonitor() : base()
        {
        }
        #endregion
    }
}
