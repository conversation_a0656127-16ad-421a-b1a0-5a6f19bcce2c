﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseDB2", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DatabaseDB2 : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string DatabaseSID { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public string Password { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public string InstanceName { get; set; }

        [DataMember]
        public string EnvironmentVariable { get; set; }

        #endregion Properties
    }
}