﻿using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    public class ExchangeDAGComponantMonitor : BaseEntity
    {
        #region properties

        [DataMember]
        public int InfraObjectID { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int DataBaseId { get; set; }

        [DataMember]
        public string PRIPAddress { get; set; }

        [DataMember]
        public string DRIPAddress { get; set; }

        [DataMember]
        public string PRMailboxDatabase { get; set; }

        [DataMember]
        public string DRMailboxDatabase { get; set; }

        [DataMember]
        public string PRMailboxDBStatus { get; set; }

        [DataMember]
        public string DRMailboxDBStatus { get; set; }

        [DataMember]
        public string PRLastGenLog { get; set; }

        [DataMember]
        public string DRLastLogReplayed { get; set; }

        [DataMember]
        public string PRLastGenLogTime { get; set; }

        [DataMember]
        public string DRLastReplayedLogTime { get; set; }

        [DataMember]
        public string CurrentDatalag { get; set; }

        [DataMember]
        public string PRContentIndexState { get; set; }

        [DataMember]
        public string DRContentIndexState { get; set; }

        [DataMember]
        public string PRContIndexErrorMsg { get; set; }

        [DataMember]
        public string DRContIndexErrorMsg { get; set; }

        [DataMember]
        public string PRLatestFullbkpTime { get; set; }

        [DataMember]
        public string DRLatestFullbkpTime { get; set; }

        [DataMember]
        public string PRLastLogInspected { get; set; }

        [DataMember]
        public string DRLastLogInspected { get; set; }

        [DataMember]
        public string PRLastLogCopyNotified { get; set; }

        [DataMember]
        public string DRLastLogCopyNotified { get; set; }

        [DataMember]
        public string PRLastLogCopied { get; set; }

        [DataMember]
        public string DRLastLogCopied { get; set; }

        [DataMember]
        public string DRLatLogCopiedTime { get; set; }

        public string InfraObjectName
        {
            get;
            set;
        }
        public string BusinessServiceName
        {
            get;
            set;
        }
                
        [DataMember]
        public string PRLatestAvailableLogTime { get; set; }

        [DataMember]
        public string DRLatestAvailableLogTime { get; set; }

        [DataMember]
        public string PRLastReplayedLogTime { get; set; }

        [DataMember]
        public string PRLastLogReplayed { get; set; }

        [DataMember]
        public string DRLastGenLog { get; set; }

        [DataMember]
        public string PRLastCopiedLogTime { get; set; }

        [DataMember]
        public string DRLastCopiedLogTime { get; set; }

        #endregion properties
    }
}
      