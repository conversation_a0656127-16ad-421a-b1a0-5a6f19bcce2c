﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="NotificationManager.aspx.cs" Inherits="CP.UI.NotificationManager"
    Title="Continuity Patrol :: Notification-Manager" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <script src="../Script/MaskedPassword.js"></script>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>


                <h3>
                    <img src="../Images/email-icon-notfication.png">
                    Notification Manager</h3>

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none">
                    <div class="widget-body">

                        <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">

                                    <label class="col-md-3 control-label">
                                        <asp:Label ID="labelName" runat="server" Text="Name"></asp:Label><span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="textName" runat="server" TabIndex="1" CssClass="form-control"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfv_name1" runat="server" ErrorMessage="Enter Name"
                                            ControlToValidate="textName" Display="Dynamic" ValidationGroup="validIsActive" CssClass="error"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="regxnotimngr" runat="server" ControlToValidate="textName" ValidationGroup="validIsActive"
                                            ErrorMessage="Enter Valid Name" ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"
                                            Display="Dynamic" CssClass="error"></asp:RegularExpressionValidator>
                                        <asp:LinkButton ID="Lksmtphost" runat="server" CssClass="btn btn-primary"
                                            Width="13%" OnClick="libtnsmtphost_Click">SMTPHost</asp:LinkButton>


                                        <asp:LinkButton ID="lnkSMS" runat="server" CausesValidation="False" CssClass="btn btn-primary" Width="13%" OnClick="lnkSMS_Click">SMS</asp:LinkButton>
                                        <cc1:ModalPopupExtender ID="mpSMS" runat="server" TargetControlID="lnkSMS" BackgroundCssClass="bg" PopupControlID="Panel_SMS">
                                        </cc1:ModalPopupExtender>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtName">
                                        <asp:Label ID="lablemail" runat="server" Text="E-mail"></asp:Label>
                                        <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtemailbox1" runat="server" TabIndex="2" CssClass="form-control"
                                            AutoCompleteType="Disabled" OnTextChanged="TxtNameTextChanged" AutoPostBack="true"></asp:TextBox>
                                        <asp:Label ID="lblName1" runat="server" ForeColor="Red"></asp:Label>

                                        <asp:RequiredFieldValidator ID="rfvEmail" CssClass="error" ControlToValidate="txtemailbox1" runat="server"
                                            ErrorMessage="Enter Email Address" Display="Dynamic" ValidationGroup="validIsActive"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="revEmail" runat="server" CssClass="error" ControlToValidate="txtemailbox1"
                                            ErrorMessage="Valid email address is required." ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                                            Display="Dynamic" ValidationGroup="validIsActive"></asp:RegularExpressionValidator>

                                        <asp:Label ID="lblActive" runat="server" Text="Active User"></asp:Label>
                                        <asp:CheckBox ID="chkboxActive" runat="server" Enabled="true" Visible="true" />

                                        <asp:Label ID="lblEmail" runat="server" Text="Send Report"></asp:Label>
                                        <asp:CheckBox ID="chkboxEmail" runat="server" Enabled="true" Visible="true" />

                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="chkMobile">
                                        <asp:Label ID="lblMobile" runat="server" Text="Mobile"></asp:Label></label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkMobile" CssClass="vertical-align" runat="server" AutoPostBack="true" OnCheckedChanged="chkMobile_OnCheckedChanged"></asp:CheckBox>
                                    </div>
                                </div>
                                <div class="form-group" runat="server" id="divMobile" visible="false">
                                    <label class="col-md-3 control-label" for="txtMCountryCode">
                                        <asp:Label ID="lblTemp" runat="server" Text=""></asp:Label></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtMCountryCode" runat="server" class="form-control" MaxLength="3" Width="7%" placeholder="+91"></asp:TextBox>
                                        <asp:TextBox ID="txtMobile" runat="server" class="form-control" MaxLength="10" Width="40.6%" TabIndex="14" />
                                        <asp:RequiredFieldValidator ID="rfvmobcntrycode" ControlToValidate="txtMCountryCode" runat="server" CssClass="error"
                                            ErrorMessage="Enter Country Code" ValidationGroup="validIsActive"></asp:RequiredFieldValidator>

                                        <asp:RegularExpressionValidator ID="revMCountryCode" runat="server" CssClass="error"
                                            ControlToValidate="txtMCountryCode" Display="Dynamic" ErrorMessage="Valid Country Code is required"
                                            ValidationExpression="^\+\d+$" ValidationGroup="validIsActive"></asp:RegularExpressionValidator>

                                        <asp:RequiredFieldValidator ID="rfvmobile" runat="server" ControlToValidate="txtMobile" CssClass="error"
                                            Display="Dynamic" ErrorMessage="Enter Mobile Number" ValidationGroup="validIsActive"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="revMobile" runat="server" ControlToValidate="txtMobile" CssClass="error"
                                            Display="Dynamic" ErrorMessage="Valid Mobile Number is required" ValidationExpression="[0-9]{10}" ValidationGroup="validIsActive"></asp:RegularExpressionValidator>
                                        <%-- <asp:RegularExpressionValidator ID="revMCountryCode" runat="server" ControlToValidate="txtMCountryCode" CssClass="error"
                                            Display="Dynamic" ErrorMessage="Valid Country Code is required" ValidationExpression="^[0-9 ,+]+$"></asp:RegularExpressionValidator>--%>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtName">
                                        Assigned InfraObject <span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:Panel ID="Panel_cblstGroup" runat="server" Width="48%" BorderColor="#cccccc"
                                            BorderStyle="Solid" BorderWidth="1" Height="100px" ScrollBars="Vertical" TabIndex="6">
                                            <asp:CheckBoxList ID="CblstGroup" AutoPostBack="true" runat="server" OnSelectedIndexChanged="CblstGroupSelectedIndexChanged">
                                                <asp:ListItem Value="0">ALL</asp:ListItem>
                                            </asp:CheckBoxList>
                                        </asp:Panel>
                                        <asp:Label ID="lblGroup" runat="server" CssClass="Rerror" Visible="False" ForeColor="Red">Please
                Select InfraObject</asp:Label>
                                    </div>
                                </div>
                                <div class="form-group">

                                    <asp:Label ID="lblApp" runat="server" CssClass="Rerror" Visible="False" ForeColor="Red">Please
                Select Application</asp:Label>
                                </div>
                                <hr class="separator" />
                                <div class="form-actions row">
                                    <div class="col-xs-5">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                Fields</span>

                                    </div>
                                    <div class="col-xs-7 ">
                                        <asp:Button ID="btnsave_newuser" runat="server" Text="Save" OnClick="BtnsaveNewuserClick" Style="margin-left: 10px"
                                            CssClass="btn btn-primary" Width="15%" ValidationGroup="validIsActive" />
                                        <asp:Button ID="btncancel" runat="server" Text="Cancel" CssClass="btn btn-default" CausesValidation="false"
                                            Width="15%" OnClick="btncancel_Click" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <h3>Notification List
                     <div class="" style="display: inline-block; float: right; width: 310px;">
                         <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="User Name" Style="width: 68%;"></asp:TextBox>
                         <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" Style="width: 28%;" OnClick="btnSearch_Click" />
                     </div>
                </h3>
                <div class="widget widget-heading-simple widget-body-white">

                    <div class="widget-body">

                        <asp:Panel ID="Panel_listview" runat="server">
                            <asp:ListView ID="ListView1" runat="server" Visible="true" OnItemEditing="ListView1ItemEditing"
                                OnItemCommand="ListView1ItemCommand" OnItemDataBound="ListView1ItemDataBound"
                                OnItemDeleting="ListView1ItemDeleting" GroupItemCount="1">
                                <LayoutTemplate>
                                    <table class="table table-striped table-bordered table-condensed" width="100%" id="table1"
                                        runat="server" style="margin-bottom: 0;">
                                        <thead>
                                            <tr>

                                                <th style="width: 3%;">No.
                                                </th>
                                                <th style="width: 15%;">Name
                                                </th>
                                                <th style="width: 20%;">Email
                                                </th>
                                                <th style="width: 15%;">Mobile
                                                </th>
                                                <th style="width: 27%;">InfraObject Name
                                                </th>
                                                <th style="width: 10%;">Active
                                                </th>
                                                <th>Action
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                    <div id="notifyscroll">
                                        <table class="table table-striped table-bordered table-condensed" width="100%">
                                            <tbody>
                                                <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />
                                            </tbody>
                                        </table>
                                    </div>
                                </LayoutTemplate>
                                <%--  <EmptyDataTemplate>
                                    <asp:Label runat="server" ID="lblemptytext" Text="No Records Found" Visible="true"></asp:Label>
                                </EmptyDataTemplate>--%>
                                <ItemTemplate>
                                    <tr>
                                        <td style="width: 3%;">
                                            <asp:Label ID="ID" runat="server" Text='<%#Eval("Id") %>' Visible="False"></asp:Label>
                                            <%#Container.DataItemIndex+1 %>
                                        </td>
                                        <td style="width: 15%;">
                                            <asp:Label ID="lblName" runat="server" Text='<%# Eval("Name")%>'></asp:Label>
                                        </td>
                                        <td style="width: 20%;">
                                            <asp:Label ID="lblEmail" runat="server" Text='<%# Eval("EmailAddress")%>'></asp:Label>
                                        </td>
                                        <td style="width: 15%;">
                                            <asp:Label ID="lblMobile" runat="server" Text='<%# Eval("MobileNumber")%>'></asp:Label>
                                        </td>
                                        <td style="width: 27%;">

                                            <asp:Label ID="lblGroupName" runat="server" Text='<%#GetInfraObjectName(Eval("InfraObjectId"))%>'></asp:Label>
                                        </td>
                                        <td style="width: 10%;">
                                            <asp:ImageButton ID="imgActive" CommandName="ActiveState" ImageUrl='<%# CheckActive(Eval("IsActive")) %>' ToolTip='<%# CheckToolTip(Eval("IsActive")) %>'
                                                runat="server" />
                                            <asp:HiddenField ID="hdnIsActive" runat="server" Value='<%# Eval("IsActive")%>' />

                                        </td>
                                        <td>
                                            <asp:ImageButton ID="ibtnEdit" runat="server" CommandName="Edit" AlternateText="Edit" CommandArgument='<%#Eval("Id") %>'
                                                ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                                            <asp:ImageButton ID="ibtnDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                ToolTip="Deactivate" ImageUrl="../images/icons/cross-circle.png" />
                                        </td>
                                    </tr>
                                    <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ibtnDelete"
                                        ConfirmText='<%# "Are you sure want to Deactivate " + Eval("Name") + " ? " %>'
                                        OnClientCancel="CancelClick">
                                    </cc1:ConfirmButtonExtender>
                                    <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender3" runat="server" TargetControlID="imgActive"
                                        ConfirmText='<%# "Are you sure want to Active " + Eval("Name") + " ? " %>'
                                        OnClientCancel="CancelClick">
                                    </cc1:ConfirmButtonExtender>
                                </ItemTemplate>
                                <EmptyDataTemplate>
                                    <asp:Label ID="lblEmpty" runat="server" Text="No Records Found" Visible="true"></asp:Label>
                                </EmptyDataTemplate>
                            </asp:ListView>
                        </asp:Panel>
                    </div>
                </div>
                <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
                </asp:Panel>
                <asp:Panel ID="Panel_smtphost" runat="server" Width="100%" Visible="false">

                    <div class="modal" style="display: block;">
                        <div class="modal-dialog" style="width: 635px;">
                            <div class="modal-content  widget-body-white">
                                <div class="modal-header">
                                    <h3 class="modal-title">SMTP Host</h3>
                                    <asp:LinkButton ID="Lkbtnclosesmtphost" runat="server" ToolTip="Close window" OnClick="Lkbtnclosesmtphost_Click"
                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                                </div>
                                <div class="modal-body">
                                    <asp:Label ID="lblsmtpsavemessage" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>
                                    <asp:UpdatePanel ID="UpdatePanel_smtphost" runat="server" UpdateMode="Conditional">
                                        <ContentTemplate>
                                            <div class="">
                                                <div class="col-md-12 form-horizontal uniformjs">
                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label ">
                                                            <asp:Label ID="lblsmtp" runat="server" Text="SMTP Host"></asp:Label>
                                                            <span class="inactive">*</span>
                                                        </label>
                                                        <div class="col-md-8">
                                                            <asp:HiddenField ID="smtpId" runat="server" />
                                                            <asp:TextBox ID="smtpHost" runat="server" CssClass="form-control" CausesValidation="True" Width="62%"
                                                                Enabled="False"></asp:TextBox>
                                                            <asp:RequiredFieldValidator ID="rfvsmtphost" runat="server" ErrorMessage="Please Enter SMTP Host" ControlToValidate="smtpHost"
                                                                Display="Dynamic" CssClass="error" SetFocusOnError="true" ValidationGroup="validsmtp"></asp:RequiredFieldValidator>
                                                            <asp:RegularExpressionValidator ID="REV_smtphost" runat="server" ControlToValidate="smtpHost"
                                                                ErrorMessage="valid host address required" ValidationExpression="([\w-]+\.)+[\w-]+(/[\w- ./?%&amp;=]*)?"
                                                                ValidationGroup="validsmtp" Display="Dynamic" SetFocusOnError="True" CssClass="error"></asp:RegularExpressionValidator>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label ">
                                                            <asp:Label ID="lblport" runat="server" Text="Port"></asp:Label>
                                                            <span class="inactive">*</span>
                                                        </label>
                                                        <div class="col-md-8">
                                                            <asp:TextBox ID="txtPort" runat="server" CssClass="form-control" onkeypress="return NumberOnly(event)" Width="62%"
                                                                CausesValidation="True" Enabled="False"></asp:TextBox>
                                                            <asp:RequiredFieldValidator ID="rfv_txtport" runat="server" ErrorMessage="Please Enter Port No." ControlToValidate="txtPort"
                                                                ValidationGroup="validsmtp" Display="Dynamic" SetFocusOnError="True" CssClass="error"></asp:RequiredFieldValidator>
                                                            <asp:RegularExpressionValidator ID="revPort" runat="server" ErrorMessage="Enter Correct Port Number"
                                                                ControlToValidate="txtPort" ValidationExpression="[0-9]{2,4}" Display="Dynamic"
                                                                ValidationGroup="validsmtp" SetFocusOnError="True" CssClass="error"></asp:RegularExpressionValidator>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label ">
                                                            <asp:Label ID="Label4" runat="server" Text="Email"></asp:Label>
                                                            <span class="inactive">*</span>
                                                        </label>
                                                        <div class="col-md-8">
                                                            <asp:TextBox ID="fromMail" runat="server" CssClass="form-control" CausesValidation="true" Width="62%"
                                                                Enabled="False"></asp:TextBox>
                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="fromMail" Display="Dynamic"
                                                                ErrorMessage="Please Enter Email Address" ValidationGroup="validsmtp" CssClass="error"></asp:RequiredFieldValidator>
                                                            <asp:RegularExpressionValidator ID="revFromemail" runat="server" ErrorMessage="Enter Correct Email Address"
                                                                ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ControlToValidate="fromMail"
                                                                Display="Dynamic" ValidationGroup="validsmtp" SetFocusOnError="True" CssClass="error"></asp:RegularExpressionValidator>
                                                            <asp:CheckBox ID="ChkPasswordless" runat="server" AutoPostBack="True" OnCheckedChanged="Checkpwdless_CheckedChanged"
                                                                Text="IsPasswordLess" Enabled="true" />
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label ">
                                                            <asp:Label ID="Label5" runat="server" Text="Password"></asp:Label>
                                                        </label>
                                                        <div class="col-md-8">
                                                            <asp:TextBox ID="txtPassword" runat="server" CssClass="form-control" autocomplete="false" TextMode="Password" Width="62%"
                                                                OnTextChanged="txtPassword_TextChanged"></asp:TextBox>
                                                            <asp:Label ID="lblPasswordless" runat="server" Text="Enter Password" Visible="False"></asp:Label>

                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label ">
                                                            <asp:Label ID="lblISSSL" runat="server" Text="SSL for sending E-Mail"></asp:Label>
                                                        </label>
                                                        <div class="col-md-8 margin-top">
                                                            <asp:CheckBox ID="chkboxEnableSSL" runat="server" OnCheckedChanged="chkboxEnableSSL_CheckedChanged"
                                                                Enabled="False" />
                                                            <asp:Label ID="lblradiosmtp_ssl_display" runat="server" Text="Label" Visible="False"></asp:Label>

                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label ">
                                                            <asp:Label ID="lblIShtml" runat="server" Text="HTML for Email Formatting"></asp:Label>
                                                        </label>
                                                        <div class="col-md-8 margin-top">
                                                            <asp:CheckBox ID="chkbxISbodyHTML" runat="server" OnCheckedChanged="chkbxISbodyHTML_CheckedChanged"
                                                                Enabled="False" />
                                                            <asp:Label ID="lbl_radio_Isbodyhtml" runat="server" Text="Label" Visible="False"></asp:Label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </div>

                                <div class="modal-footer">
                                    <div class="col-xs-4 text-left">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                Fields</span>
                                    </div>
                                    <div class="col-xs-8 text-right">

                                        <asp:Button ID="btnTestMail" runat="server" CssClass="btn btn-primary"
                                            Text="Test Mail" ValidationGroup="validsmtp" OnClick="btnTestMail_Click" Visible="True" />
                                        <asp:Button ID="btnsave" runat="server" Text="Save" CssClass="btn btn-primary" Width="21%"
                                            OnClick="btnsave_Click" ValidationGroup="validsmtp" Visible="True" />
                                        <asp:Button ID="btnclose" runat="server" Text="Close" CssClass="btn btn-default"
                                            Width="20%" CausesValidation="False" OnClick="btnclose_Click" />

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </asp:Panel>
            </ContentTemplate>
        </asp:UpdatePanel>



        <asp:Panel ID="Panel_SMS" runat="server" Width="100%">
            <asp:UpdatePanel ID="UpdatePanel_SMS" runat="server" UpdateMode="Conditional">
                <ContentTemplate>
                    <div class="modal" style="display: block;">
                        <div class="modal-dialog" style="width: 720px;">
                            <div class="modal-content  widget-body-white">
                                <div class="modal-header">
                                    <h3 class="modal-title">SMS Gateway</h3>
                                    <asp:LinkButton ID="lkbtnSMS" runat="server" ToolTip="Close window" OnClick="lkbtnCloseSMS_Click"
                                        CausesValidation="False" class="close" CommandName="Close">×</asp:LinkButton>
                                </div>
                                <div class="modal-body">
                                    <asp:Label ID="lblSMSSaveMessage" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>
                                    <div class="">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label ">
                                                    <asp:Label ID="lblURL" runat="server" Text="URL"></asp:Label>
                                                    <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:HiddenField ID="smsId" runat="server" />
                                                    <asp:TextBox ID="txtURL" runat="server" CssClass="form-control" Style="width: 70%"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvURL" runat="server" ErrorMessage="Please Enter URL" ControlToValidate="txtURL"
                                                        Display="Dynamic" CssClass="error" SetFocusOnError="true" ValidationGroup="validSMS"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label ">
                                                    <asp:Label ID="lblSender" runat="server" Text="Sender"></asp:Label>
                                                    <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtSender" runat="server" CssClass="form-control" Style="width: 70%"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvSender" runat="server" ErrorMessage="Please Enter Sender" ControlToValidate="txtSender"
                                                        ValidationGroup="validSMS" CssClass="error" Display="Dynamic" SetFocusOnError="True"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label ">
                                                    <asp:Label ID="Label6" runat="server" Text="UserName"></asp:Label>
                                                    <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtUserName" runat="server" CssClass="form-control" Style="width: 70%"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvUserName" runat="server" ControlToValidate="txtUserName"
                                                        ErrorMessage="Please Enter User Name" CssClass="error" ValidationGroup="validSMS"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label ">
                                                    <asp:Label ID="lblPassword" runat="server" Text="Password"></asp:Label>
                                                    <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtPasswordSMS" runat="server" CssClass="form-control" autocomplete="off" TextMode="Password" Style="width: 70%"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvPasswordSMS" runat="server" ControlToValidate="txtPasswordSMS"
                                                        ErrorMessage="Please Enter Password" CssClass="error" ValidationGroup="validSMS"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label ">
                                                    <asp:Label ID="lblRecipientNumber" runat="server" Text="Recipient Number"></asp:Label>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtRecipientNumber" runat="server" CssClass="form-control" Style="width: 70%"></asp:TextBox>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="modal-footer">
                                    <div class="col-xs-4 text-left">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                Fields</span>
                                    </div>
                                    <div class="col-xs-8 text-right">

                                        <asp:Button ID="btntestsms" runat="server" Text="Test SMS" CssClass="btn btn-primary"
                                            OnClick="BtnTestSMS_Click" ValidationGroup="validSMS" Visible="True" />
                                        <asp:Button ID="btnSaveSMS" runat="server" Text="Save" CssClass="btn btn-primary" Width="21%"
                                            OnClick="SaveBtn_Click" ValidationGroup="validSMS" Visible="True" />
                                        <asp:Button ID="btnCloseSMS" runat="server" Text="Close" CssClass="btn btn-default"
                                            Width="20%" CausesValidation="False" OnClick="CloseBtn_Click" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>
        </asp:Panel>


    </div>
    <script type="text/javascript">


        new MaskedPassword(document.getElementById("ctl00_cphBody_txtPassword"), '\u25CF');

    </script>

    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script>
        $(document).ready(function () {
            $("#notifyscroll").mCustomScrollbar({
                axis: "y",
                setHeight: "190px",
            });
        });

        function pageLoad() {

            $("#notifyscroll").mCustomScrollbar({
                axis: "y",
                setHeight: "190px",
            });
        }


    </script>
</asp:Content>
