﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "CPLoadConfiguration", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class CPLoadMaster : BaseEntity
    {

        #region Properties

        [DataMember]
        public string NodeID { get; set; }

        [DataMember]
        public int BServiceID { get; set; }

        [DataMember]
        public int BFunctionID { get; set; }

        [DataMember]
        public string InfraIDs { get; set; }

        [DataMember]
        public string IPHostName { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public string Password { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string TransferNodeId { get; set; }

        [DataMember]
        public int IsTransferLoad { get; set; }

        #endregion Properties
    }
}
