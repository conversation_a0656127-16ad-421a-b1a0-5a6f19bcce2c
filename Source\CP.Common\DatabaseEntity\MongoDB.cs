﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;


namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "MongoDB", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class MongoDB : BaseEntity

    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId { get; set; }

        [DataMember]
        public string InstallationPath { get; set; }

        [DataMember]
        public string InstanceName { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public string Password { get; set; }

        [DataMember]
        public string BinaryLocation { get; set; }

       
        #endregion Properties
}
    }
