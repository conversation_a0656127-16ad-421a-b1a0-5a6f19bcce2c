﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common
{
    [Serializable]
    [DataContract(Name = "HuwaiStorageMonitorStatslogs", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class HuwaiStorageMonitorStatslogs : BaseEntity
    {
        #region Properties

        [DataMember]
        public string DatabaseName { get; set; }

        [DataMember]
        public int Id { get; set; }
        [DataMember]
        public int InfraObjectId { get; set; }
        [DataMember]
        public int ReplicationId { get; set; }
        [DataMember]
        public string HSModel { get; set; }
        [DataMember]
        public string HSSystemName { get; set; }
        [DataMember]
        public String CGID { get; set; }
        [DataMember]
        public string CGName { get; set; }
        [DataMember]
        public string CGRole { get; set; }
        [DataMember]
        public string CGReplicationMode { get; set; }
        [DataMember]
        public string CGHealthStatus { get; set; }
        [DataMember]
        public string CGRepRunningStatus { get; set; }
        [DataMember]
        public string DataLag { get; set; }
        [DataMember]
        public string CGRepStartTime { get; set; }
        [DataMember]
        public string CGRepEndTime { get; set; }
        [DataMember]
        public string CGRepSyncDirection { get; set; }
        [DataMember]
        public string PRDRType { get; set; }

        public string InfraObjectName
        {
            get;
            set;
        }
        public string BusinessServiceName
        {
            get;
            set;
        }

        #endregion Properties



    }
}
