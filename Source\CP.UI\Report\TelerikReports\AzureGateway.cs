namespace CP.UI.Report.TelerikReports
{
    using System;
    using System.ComponentModel;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;
    using BusinessFacade;
    using DataAccess;
    using Common.DatabaseEntity;
    using System.Collections.Generic;
    using System.Collections;
    using System.Linq;
    using System.Configuration;
    using System.Data;
    using System.Globalization;
    using System.IO;
    using System.Web;
    using System.Web.UI;
    using System.Web.UI.WebControls;
    using CP.ExceptionHandler;
    using CP.UI.Controls.ReportClients;
    using Gios.Pdf;
    using log4net;
    using SpreadsheetGear;
    using CP.Common.BusinessEntity;

    /// <summary>
    /// Summary description for MimixDatalg.
    /// </summary>
    public partial class AzureGateway : Telerik.Reporting.Report
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(AzureGateway));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();
        public AzureGateway()
        {

            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }


        private void ShowTable()
        {
            try
            {

                int iInfraObjId = Convert.ToInt32(this.ReportParameters["iInfraObjId"].Value);
                string strDate = (this.ReportParameters["iStartDate"].Value).ToString();
                string endDate = (this.ReportParameters["iEndDate"].Value).ToString();
                //string prname= (this.ReportParameters["PRHosts"].Value).ToString();
                // string drname = (this.ReportParameters["DRHosts"].Value).ToString();

                var table = new DataTable();
                table.Columns.Add("SrNo");
                table.Columns.Add("Port");


                table.Columns.Add("Server");
                table.Columns.Add("Status");
                table.Columns.Add("ProvoisioningState");
                table.Columns.Add("TimeStamp");
                //table.Columns.Add("ActualRPO");

                //table.Columns.Add("CreateDate");
                //table.Columns.Add("PRCurrentConnectedSite");
                //table.Columns.Add("PRProtectionGroupStatus");
                //table.Columns.Add("PRAvailableSites");



                IList<Common.DatabaseEntity.AzureGateway> gateway = Facade.AzureGateWay_GETBYDATE(iInfraObjId, strDate, endDate);

                if (gateway != null && gateway.Count > 0)
                {
                    int i = 1;
                    foreach (var emc in gateway)
                    {
                        DataRow dr = table.NewRow();

                        dr["SrNo"] = i.ToString();

                        dr["Port"] = emc.PortHTTPSetting;
                        dr["Server"] = emc.Servers;
                        dr["Status"] = emc.Status;
                        dr["ProvoisioningState"] = emc.ProvisioningState;

                        dr["TimeStamp"] = emc.CreateDate != DateTime.MinValue ? Convert.ToString(emc.CreateDate) : "NA";
                        table.Rows.Add(dr);
                        i++;

                    }
                }
                this.DataSource = table;
            }

            catch(Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method, InnerException Message " + ex.InnerException.Message);
            }
        }
        private void HyperVRPT_NeedDataSource(object sender, EventArgs e)
        {
            ShowTable();
        }


        //private void HyperVRPTNew_NeedDataSource(object sender, EventArgs e)
        //{
        //    ShowTable();
        //}
    }
}