﻿

function OnGroupNodeChange(oId) {
    localStorage.SelectedNode = oId;

    $.ajax({
        type: "POST",
        url: "CommandCenter.aspx/GetGroupNodeDetailsById",
        data: "{'GroupNodeId':'" + oId + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function (data) {
            FillComponantDetails(data.d);
        },
        error: function (data) {
            OnError(data);
        }
    });


}
function FillComponantDetails(result) {
    if (result.length > 0) {
        var mainresult = result.split("@");
        var prelement = mainresult[0].split(":");
        if (prelement.length > 0) {
            $("[id$='lblPRServerName']").text(prelement[0]);
            $("[id$='lblPRIPAdd']").text(prelement[1]);
            $("[id$='lblPRSID']").text(prelement[2]);
            $("[id$='lblLastLogSeq']").text(prelement[3]);
            $("[id$=spnPRcomp]").attr("class", prelement[4]);
            $("[id$='lblOracleDataguardRepStatusPR']").text(prelement[5]);
        }
        var drelement = mainresult[1].split(":");
        if (drelement.length > 0) {
            $("[id$='lblDRServerName']").text(drelement[0]);
            $("[id$='lblDRIPAdd']").text(drelement[1]);
            $("[id$='lblDRSID']").text(drelement[2]);
            $("[id$='lblApplySeq']").text(drelement[3]);
            $("[id$=spnDRcomp]").attr("class", drelement[4]);
            $("[id$='lblOracleDataguardRepStatusDR']").text(drelement[5]);
        }
    }
}




function OnMailBoxDatabaseChange(mailboxId, mailboxName) {
    $.ajax({
        type: "POST",
        url: "CommandCenter.aspx/FillComponentMonitor",
        data: "{'MailBoxId':'" + mailboxId + "','MailBoxName':'" + mailboxName + "'}",

        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function (data) {
            FillMailboxComponantDetails(data.d);
        },
        error: function (data) {
            OnError(data);
        }
    });

    $.ajax({
        type: "POST",
        url: "CommandCenter.aspx/FillReplicationMonitor",
        data: "{'MailBoxId':'" + mailboxId + "','MailBoxName':'" + mailboxName + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function (data) {
            FillReplicationStatus(data.d);
        },
        error: function (data) {
            OnError(data);
        }
    });

}

function FillReplicationStatus(result) {
    if (result.length > 0) {
        var prelement = result.split(":");
        if (prelement.length > 0) {
            //$("[id$='lblDAGReplicationType']").text(prelement[0])
            $("[id$='lblDAGRepName']").text(prelement[0]);
            $("[id$='lblDAGRepPrMailBoxName']").text(prelement[1]);
            $("[id$='lblDAGRepDrMailBoxName']").text(prelement[2]);
            $("[id$='lblDAGRepCopyQueLen']").text(prelement[3]);
            $("[id$='lblDAGRepReplyQueLen']").text(prelement[4]);
            $("[id$='lblDAGRepStatus']").text(prelement[5]);
        }
    }
}

function FillMailboxComponantDetails(result) {
    if (result.length > 0) {
        var mainresult = result.split("#");
        var prelement = mainresult[0].split(":");
        if (prelement.length > 0) {
            $("[id$='lblDagComPrIpAddress']").text(prelement[0]);
            $("[id$='lblDagComPRMailBoxName']").text(prelement[1]);
            $("[id$='lblDagComPRMailBoxStatus']").text(prelement[2]);
            $("[id$='lblDagComPrLastLogGenerated']").text(prelement[3]);
            $("[id$='lblDagComPrGeneratedLogTime']").text(prelement[4]);
        }
        var drelement = mainresult[1].split(":");
        if (drelement.length > 0) {
            $("[id$='lblDagComDrIpAddress']").text(drelement[0]);
            $("[id$='lblDagComDRMailBoxName']").text(drelement[1]);
            $("[id$='lblDagComDRMailBoxStatus']").text(drelement[2]);
            $("[id$='lblDagComDrLastLogGenerated']").text(drelement[3]);
            $("[id$='lblDagComDrRepliedLogTime']").text(prelement[4]);
            //$("[id$=spnDRcomp]").attr("class", drelement[4]);
        }
    }
















}

function CallAjax(id) {
    if (id != "") {
        $.ajax({
            type: "POST",
            url: "CommandCenter.aspx/GetInfraDetils",
            data: "{'id':'" + id + "'}",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: true,
            success: function (data) {
                $("[id$=divHeatmap]").empty();
                $("[id$=divHeatmap]").html(data.d);
                $("[id$=divHeatmap]").show();
                $("#modelbg").show();
                $(".divHeatmapServer").mCustomScrollbar({
                    axis: "y",
                });
                var mridul = $("[id$=divHeatmapServer] tr:first-child td:nth-child(4) span:nth-child(2)").text();
                // $("#divHeatmapTitle").text(mridul + " Heatmap Details");

                if (id.indexOf("Server") > 0) {
                    $("#typeid").text("IP Address");
                    $("#divHeatmapTitle").text(" Server Heatmap Details");
                }
                if (id.indexOf("Database") > 0 || id.indexOf("DBNode") > 0) {
                    $("#typeid").text("Database Name");
                    $("#divHeatmapTitle").text(" Database Heatmap Details");
                }
                else if (id.indexOf("Replication") > 0) {
                    $("#typeid").text("Replication Type");
                    $("#divHeatmapTitle").text(" Replication Heatmap Details");
                }

                if (mridul == 'Server') {
                    $("#typeid").text("IP Address");
                    $("#divHeatmapTitle").text(" Server Heatmap Details");
                }
                if (mridul == 'Database' || mridul == 'DBNode') {
                    $("#typeid").text("Database Name");
                    $("#divHeatmapTitle").text(" Database Heatmap Details");
                }
                if (mridul == 'Replication') {
                    $("#typeid").text("Replication Type");
                }
                localStorage.setItem("mridulpopup", "true");
            },
            error: function (data) {
                OnError(data);
            }
        });
    }

}