﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;


namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "OracleCloudDb", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class OracleCloudDb : BaseEntity
    {
         #region Properties


        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public string TenancyId
        {
            get;
            set;
        }

        [DataMember]
        public string UserId
        {
            get;
            set;
        }

        [DataMember]
        public string IsDBSystemExistUnderRoot
        {
            get;
            set;
        }

        [DataMember]
        public string CompartmentName
        {
            get;
            set;
        }

        [DataMember]
        public string CompartmentID
        {
            get;
            set;
        }

        [DataMember]
        public string DBSystemDisplayName
        {
            get;
            set;
        }

        [DataMember]
        public string DatabaseName

        {
            get;
            set;
        }

        [DataMember]
        public string HomeRegion
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public OracleCloudDb()
            : base()
        {

        }

        #endregion
    }
}
