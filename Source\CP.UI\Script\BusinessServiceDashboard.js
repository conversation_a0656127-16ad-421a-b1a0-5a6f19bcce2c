﻿
function BSAvail(data) {
    debugger
    var datajson = data.split(",");
    var chart = new CanvasJS.Chart("bsavail", {
        animationEnabled: true,
        height: 140,
        width: 200,
        data: [{
            type: "doughnut",
            startAngle: 60,

            //innerRadius: 60,
            indexLabelFontSize: 0.1,
            indexLabel: "",
            indexLabelLineThickness: 0,
            //toolTipContent: "<b>{label}:</b> {y}",
            radius: "90%",
            innerRadius: "50%",
            dataPoints: [
                //{ y: parseFloat(datajson[0]), label: "Up", color: "#f8a217", toolTipContent: "Up :{y}" },
                 { y: parseFloat(datajson[0]), label: "Up", color: "#008000", toolTipContent: "Up :{y}" },
                { y: parseFloat(datajson[1]), label: "Down", color: "#ff1c1c", toolTipContent: "Down :{y}" },
                { y: parseFloat(datajson[2]), label: "Maintenance", color: "#055bb3", toolTipContent: "Maintenance :{y}" },
                { y: parseFloat(datajson[3]), label: "Configuration Inprogress", color: "#7d7b7b", toolTipContent: "Configuration Inprogress:{y}" }
            ]
        }]
    });
    chart.render();
}

function BSHealth(data) {
     
    var datajson = data.split(",");
    var chart = new CanvasJS.Chart("bsHealth", {
        animationEnabled: true,
         
        axisX: {
            interval: 1,
            gridColor: "#000",
            color: "#fff"
        },
        axisY2: {
            //interlacedColor: "rgba(1,77,101,.2)",
            gridColor: "#fff",
            lineThickness: 0,
            lineColor: "#fff",
            labelFontColor: "#fff",
            tickColor: "#fff"
            //title: "Number of Companies"
        },
        data: [{
            type: "bar",
            //name: "companies",
            axisYType: "secondary",
            //color: "#000",
            dataPoints: [
                { y: parseFloat(datajson[3]), label: "Configuration Inprogress", color: "#7d7b7b" },
                { y: parseFloat(datajson[2]), label: "DR", color: "#2c8ad9" },
                { y: parseFloat(datajson[1]), label: "Production", color: "#0b6623" },
                { y: parseFloat(datajson[0]), label: "Total", color: "#007c97" },
            ]
        }]
    });
    chart.render();
}

function BSreplication(data) {
     
    var datajson = data.split(",");
    var chart = new CanvasJS.Chart("bsReplication", {
        animationEnabled: true,
        height: 140,
        width: 190,
        data: [{
            type: "pie",
            startAngle: 60,

            //innerRadius: 60,
            indexLabelFontSize: 0.1,
            indexLabel: "",
            indexLabelLineThickness: 0,
            toolTipContent: "<b>{label}:</b> {y}",
            dataPoints: [
                //{ y: datajson[0], label: "Non Impacted", color: "#f8a217" },
                  { y: datajson[0], label: "Non Impacted", color: "#008000" },
                { y: datajson[1], label: "Impacted", color: "#ff1c1c" },
            ]
        }]
    });
    chart.render();
}

function BSDrill(spdum) { 
    //var spdum = "14 Feb,13 Feb,12 Feb,11 Feb,10 Feb,9 Feb,8 Feb,7 Feb,6 Feb,5 Feb,4 Feb,3 Feb,2 Feb,1 Feb###12,45,1,5,2,7,6,4,1,88,22,45,14,95###8,4,1,6,77,2,5,66,55,7,2,1,55,6###18,4,5,1,9,44,2,44,7,6,3,1,2,8";
    var spl = spdum.split("###");
    var l1 = spl[0].split(",");
    var l2 = spl[1].split(",");
    var l3 = spl[2].split(",");
    var l4 = spl[3].split(",");
    var l5 = spl[4].split(",");
    var chart = new CanvasJS.Chart("bsdrill", {
        animationEnabled: true,
        height: 150,
        width: 500,

        //title: {
        //    text: "Products Sold by XYZ Ltd. in 2016"
        //},
        axisX: {
            //title: "Seasons",
            minimum: 0,
            maximum: 13.02
        },
        axisY: {
            //title: "Sales"
        },
        //toolTip: {
        //    shared: true
        //},
        data: [{
            type: "line",
            name: "Performed",
            color: "#4a8bc2",
            dataPoints: [
                { y: parseFloat(l2[0]), label: l1[0] },
                { y: parseFloat(l2[1]), label: l1[1] },
                { y: parseFloat(l2[2]), label: l1[2] },
                { y: parseFloat(l2[3]), label: l1[3] },
                { y: parseFloat(l2[4]), label: l1[4] },
                { y: parseFloat(l2[5]), label: l1[5] },
                { y: parseFloat(l2[6]), label: l1[6] },
                { y: parseFloat(l2[7]), label: l1[7] },
                { y: parseFloat(l2[8]), label: l1[8]},
                { y: parseFloat(l2[9]), label: l1[9] },
                { y: parseFloat(l2[10]), label: l1[10] },
                { y: parseFloat(l2[11]), label: l1[11] },
                { y: parseFloat(l2[12]), label: l1[12] },
                { y: parseFloat(l2[13]), label: l1[13] }
            ]
        },
        {
            type: "line",
            name: "Success",
            color: "#1a8900",
            dataPoints: [
                { y: parseFloat(l3[0]), label: l1[0] },
                { y: parseFloat(l3[1]), label: l1[1] },
                { y: parseFloat(l3[2]), label: l1[2] },
                { y: parseFloat(l3[3]), label: l1[3] },
                { y: parseFloat(l3[4]), label: l1[4] },
                { y: parseFloat(l3[5]), label: l1[5] },
                { y: parseFloat(l3[6]), label: l1[6] },
                { y: parseFloat(l3[7]), label: l1[7] },
                { y: parseFloat(l3[8]), label: l1[8] },
                { y: parseFloat(l3[9]), label: l1[9] },
                { y: parseFloat(l3[10]), label: l1[10] },
                { y: parseFloat(l3[11]), label: l1[11] },
                { y: parseFloat(l3[12]), label: l1[12] },
                { y: parseFloat(l3[13]), label: l1[13] }
            ]
        },
        {
            type: "line",
            name: "Part Success",
            color: "#ff7d1d",
            dataPoints: [
                { y: parseFloat(l4[0]), label: l1[0] },
                { y: parseFloat(l4[1]), label: l1[1] },
                { y: parseFloat(l4[2]), label: l1[2] },
                { y: parseFloat(l4[3]), label: l1[3] },
                { y: parseFloat(l4[4]), label: l1[4] },
                { y: parseFloat(l4[5]), label: l1[5] },
                { y: parseFloat(l4[6]), label: l1[6] },
                { y: parseFloat(l4[7]), label: l1[7] },
                { y: parseFloat(l4[8]), label: l1[8] },
                { y: parseFloat(l4[9]), label: l1[9] },
                { y: parseFloat(l4[10]), label: l1[10] },
                { y: parseFloat(l4[11]), label: l1[11] },
                { y: parseFloat(l4[12]), label: l1[12] },
                { y: parseFloat(l4[13]), label: l1[13] }
            ]
        },
        {
            type: "line",
            name: "Failed",
            color: "#cc2a3c",
            dataPoints: [
                { y: parseFloat(l5[0]), label: l1[0] },
                { y: parseFloat(l5[1]), label: l1[1] },
                { y: parseFloat(l5[2]), label: l1[2] },
                { y: parseFloat(l5[3]), label: l1[3] },
                { y: parseFloat(l5[4]), label: l1[4] },
                { y: parseFloat(l5[5]), label: l1[5] },
                { y: parseFloat(l5[6]), label: l1[6] },
                { y: parseFloat(l5[7]), label: l1[7] },
                { y: parseFloat(l5[8]), label: l1[8] },
                { y: parseFloat(l5[9]), label: l1[9] },
                { y: parseFloat(l5[10]), label: l1[10] },
                { y: parseFloat(l5[11]), label: l1[11] },
                { y: parseFloat(l5[12]), label: l1[12] },
                { y: parseFloat(l5[13]), label: l1[13] }
            ]
        }]
    });
    chart.render();

}