﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EmcMirrorViewConfiguration.ascx.cs" Inherits="CP.UI.Controls.EmcMirrorViewConfiguration" %>

<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>

<asp:UpdatePanel ID="upnEmcMirrorView" runat="server" UpdateMode="Conditional">
    <ContentTemplate>
        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-replication" for="txtName">
                            Storage Console(NaviseccliServer)<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:DropDownList ID="ddlServer" runat="server" AutoPostBack="true" CssClass="selectpicker col-md-6 ddlsite" data-style="btn-default">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvServer" runat="server" ControlToValidate="ddlServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select Production Server"></asp:RequiredFieldValidator>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication" for="txtName">
                            Navisphere Path</label>
                        <%--<span class="inactive">*</span>--%>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtNavisperePath" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                            <%-- <asp:RequiredFieldValidator ID="rfvtxtNavisperePath" runat="server" CssClass="error" ControlToValidate="txtNavisperePath"
                                Display="Dynamic" ErrorMessage="Please Enter Navispere Path"></asp:RequiredFieldValidator>--%>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-replication" for="txtName">
                            CG Name<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtCGName" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvtxtCGName" runat="server" CssClass="error" ControlToValidate="txtCGName"
                                Display="Dynamic" ErrorMessage="Please Enter CG Name"></asp:RequiredFieldValidator>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-replication" for="txtName">
                            SP Name/IP<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSPNameIP" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvtxtSPNameIP" runat="server" CssClass="error" ControlToValidate="txtSPNameIP"
                                Display="Dynamic" ErrorMessage="Please Enter SP Name/IP"></asp:RequiredFieldValidator>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-replication" for="txtName">
                            SP Username</label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSPUserName" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                            <%--<asp:RequiredFieldValidator ID="rfvtxtSPUserName" runat="server" CssClass="error" ControlToValidate="txtSPUserName"
                                Display="Dynamic" ErrorMessage="Please Enter SP Username"></asp:RequiredFieldValidator>--%>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-replication" for="txtName">
                            SP Password</label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSP_Password" autocomplete="off" CssClass="form-control" Style="width: 49% !important;" runat="server" TextMode="Password"></asp:TextBox>
                            <%--<asp:RequiredFieldValidator ID="rfvtxtSP_Password" runat="server" CssClass="error" ControlToValidate="txtSP_Password"
                                Display="Dynamic" ErrorMessage="Please Enter SP Password"></asp:RequiredFieldValidator>--%>
                        </div>
                    </div>
                                                    

                    <hr class="separator" />
                    <label><span class="inactive">*</span>SP - Storage Processor</label>
                </div>
            </div>
        </div>
        <div class="form-actions row">
            <div class="col-lg-3">
                <asp:Label ID="Label7" runat="server" Text="&nbsp;"></asp:Label>
            </div>
            <div class="col-lg-6" style="margin-left: 40.3%;">
                <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save"
                    OnClick="btnSave_Click" />
                <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server"
                    Text="Cancel" CausesValidation="false" OnClick="btnCancel_Click" />
            </div>
        </div>
    </ContentTemplate>
</asp:UpdatePanel>

<%--<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>

<link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
<script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
<script>
    $(document).ready(function () {
        $("#subanunth-content").mCustomScrollbar({
            axis: "y"
        });
    });

    function pageLoad() {

        $("#subanunth-content").mCustomScrollbar({
            axis: "y"
        });
    }
</script>
<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">Emc MirrorView Replication Details</h4>
        </div>
    

        
                <div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <div class="col-md-3">

            </div>
            <div class="col-md-4" style="margin-left: -12px; margin-right: 50px;">
                <h4 class="heading">Production  </h4>
            </div>
            <div class="col-md-4">
                <h4 class="heading">DR </h4>
            </div>
        </div>
        <div class="widget-body">
            <table class="table">
                <tbody>
                    <tr>
                        <td style="width: 24%;">
                            <label>
                                Storage Console <span class="inactive">*</span></label>
                        </td>
                        <td>
                            <asp:DropDownList ID="ddlPrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" data-style="btn-default">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvPRServer" runat="server" ControlToValidate="ddlPrServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select Production Server"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:DropDownList ID="ddlDrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" data-style="btn-default">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvDRServer" runat="server" ControlToValidate="ddlDrServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select DR Server"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                Navispere Path </label> 
                        </td>
                        <td>
                            <asp:TextBox ID="txtPrHorcomInstance" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ErrorMessage="Please Enter  Navispere Path" CssClass="error"
                                ControlToValidate="txtPrHorcomInstance"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDrHorcomInstance" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ErrorMessage="Please Enter  Navispere Path" CssClass="error"
                                ControlToValidate="txtDrHorcomInstance"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                CG Name<span class="inactive">*</span></label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPrHorcomDeviceGroup" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator5" runat="server" ErrorMessage="Please Enter CG Name" CssClass="error"
                                ControlToValidate="txtPrHorcomDeviceGroup"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDrHorcomDeviceGroup" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator6" runat="server" ErrorMessage="Please Enter CG Name" CssClass="error"
                                ControlToValidate="txtDrHorcomDeviceGroup"></asp:RequiredFieldValidator>
                        </td>
                    </tr>

                        <tr>
                        <td>
                            <label>
                                SP Name/IP<span class="inactive">*</span></label>
                        </td>
                        <td>
                            <asp:TextBox ID="TextBox1" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ErrorMessage="Please Enter SP Name/IP" CssClass="error"
                                ControlToValidate="txtPrHorcomDeviceGroup"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="TextBox2" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ErrorMessage="Please Enter SP Name/IP" CssClass="error"
                                ControlToValidate="txtDrHorcomDeviceGroup"></asp:RequiredFieldValidator>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <label>
                                SP Username
                            </label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPrJournalVolume" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDrJournalVolume" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                SP Password</label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPrCommandDevice" CssClass="form-control" Style="Width: 56% !important" runat="server" TextMode="Password"></asp:TextBox>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDrCommandDevice" CssClass="form-control" Style="Width: 56% !important" runat="server" TextMode="Password"></asp:TextBox>
                        </td>
                    </tr>

              <%--      <tr id="trdatastorename" runat="server">
                        <td style="border-bottom: 1px solid #ddd">
                            <label>DataStore Name</label>
                        </td>
                        <td style="border-bottom: 1px solid #ddd">
                            <asp:TextBox ID="txtPRDataStorename" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>                               
                        </td>
                        <td style="border-bottom: 1px solid #ddd">
                            <asp:TextBox ID="txtDRDataStorename" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>                                
                        </td>
                    </tr>

                  <%--  <tr id="trvmwarepath" runat="server">
                        <td colspan="3">
                            <asp:Panel ID="pnlConVmWarepath" runat="server" class="form-group" Visible="false">
                                <label class="col-md-3 control-label" style="padding-left: 24px">VmPath Details</label>
                                <div class="col-md-7" style="padding-left: 6px; padding-right: 10px;">
                                    <div class="widget margin-bottom-none">

                                        <asp:ListView ID="lvVmWarepath" runat="server" OnItemDeleting="lvVmWarepathDeleting"
                                            OnItemInserting="lvVmWarepathItemInserting" OnItemEditing="lvVmWarepathEditing"
                                            OnItemUpdating="lvVmWarepathUpdating" OnItemCanceling="lvVmWarepathCanceling"
                                            InsertItemPosition="LastItem" OnItemDataBound="lvVmWarepath_ItemDataBound">
                                            <LayoutTemplate>
                                                <table class="table table-bordered margin-bottom-none" style="table-layout: fixed">
                                                    <thead>
                                                        <tr>

                                                            <th style="width: 30%;">VmName
                                                            </th>
                                                            <th style="width: 30%;">PRVm(vmx) File Path
                                                            </th>
                                                            <th style="width: 30%;">DRVm(vmx) File Path
                                                            </th>
                                                            <th style="width: 12%;">Edit/Delete
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                </table>
                                                <div id="subanunth-content">
                                                    <table class="table table-bordered margin-bottom-none">
                                                        <tbody>
                                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                    <td style="width: 29%;">
                                                        <asp:TextBox ID="txtdisvmwarename" Text='<%# Eval("VmwareName") %>' CssClass="form-control" Style="width: 100%" Enabled="False" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 30%;">
                                                        <asp:TextBox ID="txtdisPRvmfilepath" Text='<%# Eval("PRVmFilePath") %>' CssClass="form-control" Style="width: 100%" Enabled="False" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 30%;">
                                                        <asp:TextBox ID="txtdisDRvmfilepath" Text='<%# Eval("DRVmFilePath") %>' CssClass="form-control" Style="width: 100%" Enabled="False" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 11%; text-align: center; vertical-align: middle">
                                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                            ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" CausesValidation="false" />
                                                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" CausesValidation="false" />
                                                        <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                            ConfirmText='<%# "Are you sure want to delete ? " %>' OnClientCancel="CancelClick">
                                                        </TK1:ConfirmButtonExtender>
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EditItemTemplate>
                                                <tr>
                                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                    <td style="width: 29%;">
                                                        <asp:TextBox ID="txtEditvmwarename" Text='<%# Eval("VmwareName") %>' Style="width: 100%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 30%;">
                                                        <asp:TextBox ID="txtEditPRvmfilepath" Text='<%# Eval("PRVmFilePath") %>' Style="width: 100%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 30%;">
                                                        <asp:TextBox ID="txtEditDRvmfilepath" Text='<%# Eval("DRVmFilePath") %>' Style="width: 100%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 11%; text-align: center; vertical-align: middle">
                                                        <asp:LinkButton ID="imgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                                            ToolTip="Update" CssClass="health-up" CausesValidation="false" />
                                                        <asp:LinkButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                            ToolTip="Cancel" CssClass="InActive" CausesValidation="false" />
                                                    </td>
                                                </tr>
                                            </EditItemTemplate>
                                            <InsertItemTemplate>
                                                <tr>
                                                    <td style="width: 29%;">
                                                        <asp:TextBox ID="txtvmwarename" Text="" CssClass="form-control" Style="width: 100%" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 30%;">
                                                        <asp:TextBox ID="txtPRvmfilename" Text="" CssClass="form-control" Style="width: 100%" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 30%;">
                                                        <asp:TextBox ID="txtDRvmfilename" Text="" CssClass="form-control" Style="width: 100%" runat="server"> </asp:TextBox>
                                                    </td>
                                                    <td style="width: 11%; text-align: center; vertical-align: middle">
                                                        <asp:LinkButton ID="imgInsert" runat="server" CommandName="Insert" AlternateText="Insert"
                                                            ToolTip="Insert" CssClass="plus" ValidationGroup="VmWarepath" />
                                                    </td>
                                                </tr>
                                            </InsertItemTemplate>
                                        </asp:ListView>

                                    </div>
                                </div>
                            </asp:Panel>
                        </td>
                    </tr>--%>

<%--                </tbody>
            </table>
        </div>
    </div>
</div>

    </div>
</div>


<div class="form-actions row">
    <div class="col-md-3">
        <asp:Label ID="lblMsg" runat="server" Text=""></asp:Label>
    </div>
    <div class="col-md-7 text-right">
        <asp:Button ID="SaveRep" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save"
            />
        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server"
            Text="Cancel" CausesValidation="false"/>
    </div>
</div>--%>