﻿<%@ Page Title="" Language="C#" AutoEventWireup="true" CodeBehind="DashboardImpactSummary.aspx.cs" Inherits="CP.UI.ImpactAnalysis.DashboardImpactSummary" %>


<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">

    <title><%--<asp:Literal runat="server" ID="ltrTitle" Text="What-If Analysis - Business Service Impact"></asp:Literal>--%> </title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />

    <script src="../Script/d3.min.js"></script>
    <script src="../Script/ImpactSummaryJs.js"></script>
    <script src="../Script/demoImpactJs.js"></script>

    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>

    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>

    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>




    <style type="text/css">
        .padding-noneLRnew {
            padding-left: 5px;
            padding-right: 5px;
        }

        #impacttable.table thead > tr > th, #impacttable.table tbody > tr > th, #impacttable.table tfoot > tr > th, #impacttable.table thead > tr > td, #impacttable.table tbody > tr > td, .table tfoot > tr > td, #impactincidentdetailstable.table thead > tr > th, #impactincidentdetailstable.table tbody > tr > th, #impactincidentdetailstable.table tfoot > tr > th, #impactincidentdetailstable.table thead > tr > td, #impactincidentdetailstable.table tbody > tr > td, .table tfoot > tr > td {
            padding: 5px;
        }

        #impactincidentdetailstable.table thead > tr > th, #impactincidentdetailstable.table tbody > tr > th, #impactincidentdetailstable.table tfoot > tr > th, #impactincidentdetailstable.table thead > tr > td, #impactincidentdetailstable.table tbody > tr > td, .table tfoot > tr > td {
            border-top: 0px;
            border-bottom: 0px;
        }

        #impacttable.text-small, #impactincidentdetailstable.text-small {
            font-size: 11px !important;
        }

        #impactincidentdetailstable .spnBreadCrum {
            text-overflow: ellipsis;
            overflow: hidden;
            display: block;
            width: 90%;
            white-space: nowrap;
        }

        .FinancialImapactTotalCost {
            display: block;
            color: #346d9d;
            width: 100%;
            font-size: 36px !important;
            text-align: center;
            font-weight: bold;
            font-family: segoe_uiregular;
        }

        .impactcostdetails {
            width: 47%;
            display: inline-block;
        }

            .impactcostdetails label {
                font-size: 12px !important;
                display: block;
                margin-bottom: 1px !important;
            }

            .impactcostdetails hr {
                border-top-color: #346d9d;
                margin: 0px 0 0px 0px;
                border-top-width: 1px;
            }

        #ctl00_cphBody_lblIncidentTime2, #ctl00_cphBody_lblIncdowntime2 {
            display: block;
            color: #346d9d;
            font-weight: bold;
        }

        /*#ctl00_cphBody_pnlBSCostAppend .border-margin-bottom,#ctl00_cphBody_pnlappend .border-margin-bottom {
            margin-bottom: 10px;
        }

        #impacttable.table td {
            padding:5px 5px 5px 0px;
        }*/
    </style>



</head>
<body>
    <telerik:RadWindowManager ID="RadWindowManager1" runat="server" IconUrl="~/Images/icons/alerts/sign_add.png">
        <Windows>
            <telerik:RadWindow ID="TelIncRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="600" ShowContentDuringLoad="false"
                Width="1300" Skin="Metro" />
        </Windows>
    </telerik:RadWindowManager>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="sp" runat="server"></asp:ScriptManager>




        <div class="innerLR">
            <span id="s1" runat="server">&nbsp;</span>
            <div class="widget margin-top">
                <div class="widget-body">
                    <asp:UpdatePanel ID="upImpactSummary" runat="server">
                        <ContentTemplate>


                            <telerik:RadTabStrip ID="RadTabStrip1" runat="server" Skin="Glow" MultiPageID="RadMultiPage1"
                                SelectedIndex="0">
                                <Tabs>
                                    <telerik:RadTab Text="Incident Summary" runat="server" Selected="True">
                                    </telerik:RadTab>
                                    <telerik:RadTab Text="Incident What-If Analysis" runat="server">
                                    </telerik:RadTab>


                                </Tabs>
                            </telerik:RadTabStrip>
                            <telerik:RadMultiPage runat="server" ID="RadMultiPage1" SelectedIndex="0">
                                <telerik:RadPageView runat="server" ID="RadPageView1">


                                    <div class="widget margin-top">
                                        <div class="widget-body">
                                            <asp:Panel ID="PnlBsSearch" runat="server">

                                                <div class="row" style="margin-bottom: 10px;">

                                                    <div class="col-md-12">
                                                        <div class="col-md-6"></div>
                                                        <div class="col-md-6 padding-none text-right">
                                                            <label class="col-md-5 control-label">
                                                                Select Business Service</label>
                                                            <div class="col-md-7 padding-none">
                                                                <asp:DropDownList ID="ddlBusinessService" runat="server" CssClass="selectpicker col-md-12"
                                                                    data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlBusinessService_SelectedIndexChanged">
                                                                </asp:DropDownList>
                                                            </div>
                                                        </div>

                                                        <%--  <asp:TextBox ID="txtsearchvalue" placeholder="Service Name" CssClass="form-control"
                                    runat="server"></asp:TextBox>--%>
                                                    </div>
                                                </div>

                                            </asp:Panel>
                                            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                                <ContentTemplate>

                                                    <asp:ListView ID="lvHeatmap" runat="server"
                                                        DataKeyNames="Id" OnItemDataBound="lvHeatmap_ItemDataBound"
                                                        OnPagePropertiesChanging="lvHeatmap_PagePropertiesChanging"
                                                        OnItemCommand="lvHeatmap_ItemCommand"
                                                        OnSelectedIndexChanged="lvHeatmap_SelectedIndexChanged"
                                                        OnPreRender="lvHeatmap_PreRender">
                                                        <LayoutTemplate>
                                                            <table class="table table-striped table-bordered table-condensed table-white" width="100%">
                                                                <thead>
                                                                    <tr>
                                                                        <th style="width: 4%;">
                                                                            <span>
                                                                                <img src="../Images/icons/sites_list.png" /></span>
                                                                        </th>
                                                                        <th>Business Service
                                                                        </th>
                                                                        <th>Infraobject
                                                                        </th>

                                                                        <th>Type
                                                                        </th>
                                                                        <th>Incident ID
                                                                        </th>
                                                                        <th>Incident Time
                                                                        </th>
                                                                        <th>Incident Status
                                                                        </th>
                                                                        <th>Component Name
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                </tbody>
                                                            </table>
                                                        </LayoutTemplate>
                                                        <ItemTemplate>
                                                            <tr>
                                                                <td>
                                                                    <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                    <%#Container.DataItemIndex+1 %>
                                                                </td>
                                                                <td style="display: none">
                                                                    <asp:Label ID="lblInfraComponentID" runat="server" Text='<%# Eval("InfraComponentID") %>' Visible="false" />
                                                                </td>

                                                                <td style='width: 20%;'>
                                                                    <asp:Image ID="imgbs" runat="server" ImageUrl="~/Images/bus-func-icon.png" Style='vertical-align: bottom; margin-right: 2px' />
                                                                    <asp:Label ID="lnkBsname" runat="server" Text='<%# Eval("BsName") %>' />
                                                                </td>
                                                                <td>
                                                                    <asp:Label ID="lblinfra" runat="server" Text='<%# Eval("Infraobject") %>' />
                                                                </td>

                                                                <td style='width: 10%;'><span id="ServerIcon" runat="server" style='vertical-align: bottom; margin-right: 2px'>&nbsp;</span> <span>
                                                                    <asp:Label ID="lblinfracomptype" runat="server" Text='<%# Eval("InfraComponentType") %>' /></span>
                                                                </td>
                                                                <td>
                                                                    <asp:Label ID="lblIncidentID" runat="server" Text='<%# Eval("IncidentID") %>' />
                                                                </td>
                                                                <td>
                                                                    <asp:Label ID="lblIncidetTime" runat="server" Text='<%# Eval("IncidentTime") %>' />
                                                                </td>
                                                                <td>
                                                                    <asp:Label ID="lblIncidentStatus" runat="server" Text='<%# Eval("IncidentStatus") %>' />
                                                                    <asp:Label ID="lblIncidentJob" runat="server" Text='<%# Eval("jobname") %>' Visible="false" />
                                                                </td>



                                                                <td style='width: 17%;'><span class='down-arrow-icon' style='vertical-align: bottom; margin-right: 2px'>&nbsp;</span> <span>
                                                                    <asp:LinkButton ID="lblinfracomp" runat="server" Text='<%# Eval("InfraComponentName") %>' CommandName="ICBusinessName" /></span>
                                                                </td>

                                                            </tr>
                                                        </ItemTemplate>
                                                        <EmptyDataTemplate>
                                                            <div class="message warning align-center bold no-bottom-margin">
                                                                <asp:Label ID="lblError" Text="No Record Found" ForeColor="Red" runat="server" Visible="true"></asp:Label>
                                                            </div>
                                                        </EmptyDataTemplate>

                                                    </asp:ListView>

                                                    <%--    <div class="row">
                                                <div class="col-md-6">
                                                    <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvHeatmap">
                                                        <Fields>
                                                            <asp:TemplatePagerField>
                                                                <PagerTemplate>
                                                                    <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                    Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                    <br />
                                                                </PagerTemplate>
                                                            </asp:TemplatePagerField>
                                                        </Fields>
                                                    </asp:DataPager>
                                                </div>
                                                <div class="col-md-6 text-right">
                                                    <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvHeatmap" PageSize="10">
                                                        <Fields>
                                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                                ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                            <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                                NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                                NumericButtonCssClass="btn-pagination" />
                                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                                ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                        </Fields>
                                                    </asp:DataPager>
                                                </div>
                                            </div>--%>
                                                </ContentTemplate>
                                            </asp:UpdatePanel>
                                        </div>
                                    </div>

                                    <telerik:RadSplitter ID="RadSplitter2" runat="server" Height="450" Width="1213" Orientation="Horizontal">
                                        <telerik:RadPane ID="RadPane3" runat="server" Height="400" MinHeight="350" MaxHeight="750">

                                            <telerik:RadSplitter ID="RadSplitter1" runat="server" Height="300">
                                                <telerik:RadPane ID="RadPane1" runat="server" MinWidth="80" MaxWidth="300" Width="200">
                                                    <div class="widget box-shadow-none border-none margin-bottom-none">
                                                        <div class="widget-head">
                                                            <img src="../Images/icons/busfunc_icon.png" style="float: left; margin: 10px 0 0 5px;" />
                                                            <h4 class=" heading text-primary " style="padding: 0 5px">Business Services</h4>
                                                        </div>
                                                        <div class="widget-body scroll-pane">
                                                            <asp:UpdatePanel ID="udpTreeView" runat="server" UpdateMode="Conditional">
                                                                <ContentTemplate>
                                                                    <asp:TreeView ID="tvBSHierarchy" OnSelectedNodeChanged="tvBSHierarchy_SelectedNodeChanged" runat="server" ShowLines="true"></asp:TreeView>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>
                                                        </div>
                                                    </div>
                                                </telerik:RadPane>
                                                <telerik:RadSplitBar ID="RadSplitbar1" runat="server">
                                                </telerik:RadSplitBar>
                                                <telerik:RadPane ID="RadPane2" runat="server" MinWidth="400" MaxWidth="950" Width="775">
                                                    <div class="widget box-shadow-none border-none margin-bottom-none">
                                                        <div class="widget-head">
                                                            <img src="../Images/icons/icon-impact-diagram-2.png" style="float: left; margin: 10px 0 0 5px;" />
                                                            <h4 class=" heading" style="padding: 0 0px;"><span id="spnBreadCrum" class="spnBreadCrum"></span></h4>
                                                            <div class="pull-right">
                                                                <a id="btnReload" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                                                <a id="btnReset" class="reset-icon" title="Back to initial zoom position">&nbsp;</a>
                                                            </div>
                                                        </div>
                                                        <div class="widget-body" style="padding: 5px;">
                                                            <asp:UpdatePanel ID="udpgraph" runat="server" UpdateMode="Conditional">
                                                                <ContentTemplate>
                                                                    <div class="message warning align-center bold no-bottom-margin">
                                                                        <asp:Label ID="lblError" Text="" ForeColor="Red" runat="server" Visible="false"></asp:Label>
                                                                    </div>
                                                                    <div id="divemptyFunctionPopup"></div>
                                                                    <div id="divbsView" style="display: none;">
                                                                        <div class="scroll-pane-diagram">

                                                                            <div id="bsbody" runat="server">
                                                                            </div>
                                                                        </div>
                                                                        <div id="divLegends" runat="server">
                                                                            <div class="col-md-4 d3-legend">
                                                                                <span class="label label-danger">&nbsp;</span>
                                                                                <span class="text-legend">Business Service having more than 30% InfraObjects affected</span>
                                                                            </div>
                                                                            <div class="col-md-4 d3-legend">
                                                                                <span class="label label-warning">&nbsp;</span>
                                                                                <span class="text-legend">Business Service having less than 30% InfraObjects affected</span>
                                                                            </div>
                                                                            <div class="col-md-4 d3-legend">
                                                                                <span class="label label-path-link">&nbsp;</span>
                                                                                <span class="text-legend">Business service having none of InfraObjects affected Or Business Service having InfraObjects with low priorities are affected</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div id="divIncView" style="display: none;">

                                                                        <div class="row" style="margin-left: -5px; margin-right: -5px;">
                                                                            <div id="incSummary" runat="server">

                                                                                <div class="col-md-4 padding-noneLRnew">
                                                                                    <img src="../Images/icons/icon-impact-incident-2.png" style="margin: 0px 5px; float: left;" />
                                                                                    <h4 class=" heading" style="padding: 0 5px">Incident Details</h4>
                                                                                    <%--<label class="heading text-primary" style="display: inline; vertical-align: middle; margin-left: 2px; width: 100%; text-transform:lowercase; font-size: 12px !important;">
                                                                                Incident Details
                                                                            </label>--%>

                                                                                    <hr style="border-top-color: #346d9d; margin: 5px 0 0px 0px; border-top-width: 2px" />
                                                                                    <div class="scroll-pane-new" style="max-height: 200px">
                                                                                        <table id="impactincidentdetailstable" class="table text-small margin-none smallimpacttable" style="width: 100%; table-layout: fixed;">
                                                                                            <tbody>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Incident ID :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblIncId" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">InfraObject Name :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblInfraID" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Impact Status :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblImpactStatus" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>

                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Impacted Component:</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblImpactedComp" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Component Name:</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblDBCompName" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Impacted At :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblIncidentTime" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>

                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Impact Down Time :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblIncdowntime" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Impact Resolve Time :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblResolveTime" class="spnBreadCrum" Text=""></asp:Label>
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Incident Description :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblIncidentDescription" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Component DR Available :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblDRavailable" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td style="width: 50%;">Component DR Activated :</td>

                                                                                                    <td style="width: 50%;">
                                                                                                        <asp:Label runat="server" ID="lblDRActivated" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                                </tr>
                                                                                                <%-- <tr>
                                                                                        <td style="width: 48%;">Financial Impact :</td>

                                                                                        <td style="width: 52%;">
                                                                                            <asp:Label runat="server" ID="lblFinancialImpact" class="spnBreadCrum" Text=""> </asp:Label></td>
                                                                                    </tr>--%>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </div>
                                                                                </div>

                                                                                <div class="col-md-8 padding-noneLRnew">
                                                                                    <img src="../Images/icons/icon-impact-diagram-2.png" style="margin: 0px 5px; float: left;" />
                                                                                    <h4 class=" heading" style="padding: 0 5px">Impact Diagram</h4>
                                                                                    <%--<label style="display: inline; vertical-align: middle; margin-left: 2px; width: 100%; text-transform:lowercase; font-size: 12px !important;">
                                                                                Impact Diagram
                                                                            </label>--%>
                                                                                    <hr style="border-top-color: #346d9d; margin: 5px 0 0px 0px; border-top-width: 2px" />
                                                                                    <div class="scroll-pane-new-graph">

                                                                                        <div id="Incbody" runat="server">
                                                                                        </div>
                                                                                        <div class="col-md-12 text-right graph-legend">
                                                                                            <img src="../Images/icons/yellow-dot.png" />
                                                                                            <label class="margin-right">Partial Impact</label>
                                                                                            <img src="../Images/icons/orange-dot.png" />
                                                                                            <label class="margin-right">Major Impact</label>
                                                                                            <img src="../Images/icons/red-dot.png" />
                                                                                            <label class="margin-right">Total Impact</label>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                            </div>
                                                                        </div>
                                                                        <div class="row" style="margin-left: -5px; margin-right: -5px;">
                                                                            <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                                                                                <div class="col-md-8 padding-noneLRnew" style="height: 110px; margin-top: 10px;">
                                                                                    <img src="../Images/icons/icon-impact-details-1.png" style="margin: 0px 5px; float: left;" />
                                                                                    <h4 class=" heading" style="padding: 0 5px">Current Financial Impact Due To Incident</h4>
                                                                                    <%--    <label style="display: inline; vertical-align: middle; margin-left: 2px; width: 100%; text-transform:lowercase ; font-size: 12px !important;">
                                                                                Current Financial Impact Due To Incident
                                                                            </label>--%>
                                                                                    <hr style="border-top-color: #346d9d; margin: 5px 0 0px 0px; border-top-width: 2px" />
                                                                                    <div class="scroll-pane-new" style="max-height: 100px">
                                                                                        <table id="impacttable" class="table text-small margin-none smallimpacttable">
                                                                                            <tbody>
                                                                                                <tr>
                                                                                                    <td style="border-top: 0px solid #ddd; width: 26%;"><b>Impacted Infra Component</b></td>
                                                                                                    <td style="border-top: 0px solid #ddd; width: 27%;"><b>Impacted Business Service</b></td>
                                                                                                    <td style="border-top: 0px solid #ddd; width: 34%;"><b>Impacted Business Application</b></td>
                                                                                                </tr>
                                                                                                <tr>

                                                                                                    <td>
                                                                                                        <asp:Label runat="server" ID="lblIconComponent" CssClass=""></asp:Label>
                                                                                                        <asp:Label Text="" runat="server" ID="lblComponentName"></asp:Label></td>

                                                                                                    <td>
                                                                                                        <asp:Panel ID="pnlBSCostAppend" runat="server">
                                                                                                        </asp:Panel>
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <asp:Panel ID="pnlappend" runat="server">
                                                                                                        </asp:Panel>

                                                                                                    </td>


                                                                                                </tr>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-md-4 padding-noneLRnew" style="margin-top: 10px;">
                                                                                    <img src="../Images/icons/icon-impact-cost-1.png" style="margin: 0px 5px; float: left;" />
                                                                                    <h4 class=" heading" style="padding: 0 5px">Impact Cost</h4>
                                                                                    <%--   <label style="display: inline; vertical-align: middle; margin-left: 2px; width: 100%; text-transform:lowercase; font-size: 12px !important;">
                                                                                Impact Cost
                                                                            </label>--%>
                                                                                    <hr style="border-top-color: #346d9d; margin: 5px 0 0px 0px; border-top-width: 2px" />
                                                                                    <%--border-bottom: 1px solid #346d9d;--%>
                                                                                    <div>
                                                                                        <asp:Label ID="lblFinancialImapactTotalCost" CssClass="FinancialImapactTotalCost" runat="server" Text=""></asp:Label>
                                                                                    </div>
                                                                                    <div>
                                                                                        <div class="impactcostdetails" style="margin-right: 8px;">
                                                                                            <label>Impacted At</label>
                                                                                            <hr />
                                                                                            <asp:Label ID="lblIncidentTime2" runat="server" Text="" CssClass="text-center"></asp:Label>
                                                                                        </div>
                                                                                        <div class="impactcostdetails">
                                                                                            <label>Impact Down Time</label>
                                                                                            <hr />
                                                                                            <asp:Label ID="lblIncdowntime2" runat="server" Text="" CssClass="text-center"></asp:Label>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                        </div>



                                                                    </div>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>
                                                        </div>
                                                    </div>

                                                </telerik:RadPane>
                                                <telerik:RadSplitBar ID="RadSplitbar3" runat="server">
                                                </telerik:RadSplitBar>
                                                <telerik:RadPane ID="RadPane5" runat="server" MinWidth="80" MaxWidth="300" Scrolling="None" Width="224">
                                                    <div class="widget box-shadow-none border-none">
                                                        <div class="widget-head">
                                                            <img src="../Images/icons/icon-impact-incident-2.png" style="float: left; margin: 10px 0px 0px 5px;" />
                                                            <h4 class=" heading" style="padding: 0 5px;">Incident Details</h4>

                                                        </div>

                                                        <div class="widget widget-tabs widget-tabs-icons-only-1 widget-activity margin-none">
                                                            <asp:UpdatePanel ID="updIcidentDetailsTabButton" runat="server" UpdateMode="Conditional">
                                                                <ContentTemplate>
                                                                    <div class="widget widget-tabs widget-tabs-icons-only-1 widget-activity margin-none">
                                                                        <div class="widget-head">

                                                                            <ul>
                                                                                <li class="active" runat="server" id="liOpen" style="width: 185px;">

                                                                                    <asp:LinkButton ID="lnkOpen" runat="server">Open Incidents</asp:LinkButton>

                                                                                </li>

                                                                                <li class=" " runat="server" id="li7days" style="width: 185px;">


                                                                                    <asp:LinkButton ID="lnk7Days" runat="server">Last 7 Days  (Open And Closed)</asp:LinkButton>
                                                                                </li>

                                                                                <li class="" runat="server" id="liclose" style="width: 185px;">

                                                                                    <asp:LinkButton ID="lnkClose" runat="server">All Closed Incidents  (30 Days)</asp:LinkButton>
                                                                                </li>




                                                                            </ul>

                                                                        </div>


                                                                        <div class="widget-body list">

                                                                            <div class="tab-content">
                                                                                <asp:Panel ID="tabOpenIncident" runat="server" class="tab-pane active">
                                                                                    <div class="widget-body scroll-pane-inc-det" style="height: 328px;">

                                                                                        <asp:TreeView ID="tvOpenIncident" runat="server" ShowLines="true" OnSelectedNodeChanged="tvOpenIncident_SelectedNodeChanged">
                                                                                        </asp:TreeView>

                                                                                    </div>
                                                                                </asp:Panel>
                                                                                <asp:Panel ID="tab7DaysIncident" runat="server" class="tab-pane">
                                                                                    <div class="widget-body scroll-pane">

                                                                                        <asp:TreeView ID="tv7DaysIncident" runat="server" ShowLines="true" OnSelectedNodeChanged="tv7DaysIncident_SelectedNodeChanged">
                                                                                        </asp:TreeView>

                                                                                    </div>
                                                                                </asp:Panel>
                                                                                <asp:Panel ID="tabAllCloseIncident" runat="server" class="tab-pane">
                                                                                    <div class="widget-body scroll-pane">

                                                                                        <asp:TreeView ID="tvAllCloseHierarchy" OnSelectedNodeChanged="tvAllCloseIncident_SelectedNodeChanged" runat="server" ShowLines="true">
                                                                                        </asp:TreeView>

                                                                                    </div>
                                                                                </asp:Panel>
                                                                            </div>
                                                                        </div>

                                                                    </div>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>
                                                        </div>

                                                    </div>
                                                </telerik:RadPane>
                                            </telerik:RadSplitter>
                                        </telerik:RadPane>
                                        <telerik:RadSplitBar ID="Radsplitbar2" runat="server">
                                        </telerik:RadSplitBar>
                                        <telerik:RadPane ID="RadPane4" runat="server" Height="170">
                                            <div class="demo-container size-thin">



                                                <div class="widget box-shadow-none border" style="margin-bottom: 5px;">
                                                    <div class="widget-head">
                                                        <img src="../Images/icons/icon-impact-incident-2.png" style="float: left; margin: 10px 0 0 5px;" />
                                                        <h4 class=" heading" style="padding: 0 5px;">Incident Record</h4>
                                                    </div>

                                                    <asp:UpdatePanel ID="updPnlIncidentList" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                            <div class="widget-body ">
                                                                <telerik:RadTreeList ID="RdTreeListIncidentRecord" runat="server" DataKeyNames="Id" ParentDataKeyNames="ParentID"
                                                                    Skin="Default" AutoGenerateColumns="false" AllowPaging="true" AllowSorting="true" PageSize="10" Width="100%"
                                                                    OnNeedDataSource="RdTreeListIncidentRecord_NeedDataSource">
                                                                    <Columns>
                                                                        <telerik:TreeListBoundColumn DataField="Id" UniqueName="Id" HeaderText="" Visible="false">
                                                                        </telerik:TreeListBoundColumn>
                                                                        <telerik:TreeListTemplateColumn DataField="Item" UniqueName="Item"
                                                                            HeaderText="Impact Severity">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactSeverity" runat="server" Text='<%#Eval("ParentID") %>' Visible="false" />
                                                                                <asp:Label ID="lblID" runat="server" Text='<%#Eval("Id") %>' Visible="false" />
                                                                                <asp:Label ID="lblItemId" runat="server" Text='<%#Eval("ItemId") %>' Visible="false" />
                                                                                <asp:Label ID="lblItemType" runat="server" Text='<%#Eval("ItemType") %>' Visible="false" />
                                                                                <asp:Label ID="Label1" runat="server" Text='<%#Eval("Item") %>' Visible="false" />
                                                                                <a>
                                                                                    <asp:Label ID="lblItem" runat="server" Font-Bold="true" CssClass="text text-info" Visible="true" Text='<%#Eval("Item")%>' /></a>
                                                                                <br />
                                                                                <asp:Label ID="lblImpactType" runat="server" Text='<%#Eval("ImpactType") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ImpactEntity" UniqueName="ImpactEntity"
                                                                            HeaderText="Impact Entity" ItemStyle-VerticalAlign="Middle">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactEntity" runat="server" Text='<%#Eval("ImpactEntity") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="IncidentName" UniqueName="IncidentName"
                                                                            HeaderText="CP Incident ID" ItemStyle-VerticalAlign="Middle">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblCpIncidentName" runat="server" Text='<%#Eval("incidentCode") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ClientSystemTicketID" UniqueName="BelongsTo"
                                                                            HeaderText="Client System Ticket ID(HP/BMC)" ItemStyle-VerticalAlign="Middle">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblClientSystemTicketID" runat="server" Text='<%#Eval("ClientSystemTicketID") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ImpactStartEndDateTime" UniqueName="ImpactStartEndDateTime"
                                                                            HeaderText="Impact Start-End Date Time" ItemStyle-VerticalAlign="Middle">

                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactStartDate" runat="server" Text='<%#Eval("ImpactStartDateTime") %>' />
                                                                                <asp:Label ID="lblImpactEndDate" runat="server" Text='<%#Eval("ImpactEndDateTime") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ImpactReason" UniqueName="ImpactReason"
                                                                            HeaderText="Impact Details" ItemStyle-HorizontalAlign="Center" ItemStyle-VerticalAlign="Middle">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactReason" runat="server" Text='<%#Eval("ImpactReason") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ImpactCost" UniqueName="ImpactCost"
                                                                            HeaderText="Impact Cost" ItemStyle-VerticalAlign="Middle" Visible="false">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactCost" runat="server" Text='<%#Eval("ImpactCost") %>' />

                                                                            </ItemTemplate>
                                                                            <HeaderStyle Width="10px"></HeaderStyle>
                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ParentID" Visible="false" UniqueName="ParentID"
                                                                            HeaderText="ParentId" ItemStyle-HorizontalAlign="Center" ItemStyle-VerticalAlign="Middle">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblIParent" runat="server" Text='<%#Eval("ParentID") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>


                                                                    </Columns>
                                                                </telerik:RadTreeList>
                                                            </div>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>

                                                </div>


                                            </div>
                                        </telerik:RadPane>
                                    </telerik:RadSplitter>
                                </telerik:RadPageView>
                                <telerik:RadPageView ID="RadPageView2" runat="server">
                                </telerik:RadPageView>

                            </telerik:RadMultiPage>

                        </ContentTemplate>
                    </asp:UpdatePanel>
                </div>
            </div>

        </div>


    </form>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script type="text/javascript">

        $(document).ready(function () {
            $(".scroll-pane-new").mCustomScrollbar({
                axis: "y",
                // setHeight: "120px",
            });
            $(".scroll-pane-new-graph").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
            });
            $(".scroll-pane-bottom-new-bottom").mCustomScrollbar({
                axis: "y",
                setHeight: "180px",
            });
            //$(".scroll-pane-new").mCustomScrollbar({
            //    axis: "y",
            //    setHeight: "100px",
            //});
            //topimpactscroll
            $(".scroll-pane-inc-det").mCustomScrollbar({
                axis: "y",
                setHeight: "328px",
            });
        });

        function pageLoad() {

            $(".scroll-pane-new").mCustomScrollbar({
                axis: "y",
                //setHeight: "120px",
            });
            $(".scroll-pane-new-graph").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
            });
            $(".scroll-pane-bottom-new-bottom").mCustomScrollbar({
                axis: "y",
                setHeight: "180px",
            });
            $(".scroll-pane").mCustomScrollbar({
                axis: "yx",
                setHeight: "365px",
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });
            $(".scroll-pane-incident-details").mCustomScrollbar({
                axis: "yx",
                setHeight: "325px",
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });
            $(".scroll-pane-diagram").mCustomScrollbar({
                axis: "y",
                setHeight: "270px",
            });
            $(".scroll-pane-inc-det").mCustomScrollbar({
                axis: "y",
                setHeight: "328px",
            });

        }
        function openIncRadWindow(Url) {
            window.radopen(Url, "TelIncRadWindow");
        }


    </script>
</body>

</html>

