﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="BussinessImpactAnalysis.aspx.cs" Inherits="CP.UI.Admin.BussinessImpactAnalysis"
    Title="Continuity Patrol :: Busines Impact Analysis " %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <script type="text/javascript">
        $(document).ready(function () {
            $("table[id$=Wizard1] > tbody").addClass("widget widget-tabs widget-wizard-pills");
            $("table[id$=Wizard1] > tbody > tr:first-child ").addClass("widget-head");
            $("table[id$=Wizard1] > tbody > tr:nth-child(2)").addClass("widget-body");
            $("#li0").addClass("no-padding");
        });
        var xPos, yPos;
        Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
        Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
        function BeginRequestHandler(sender, args) {
            if ($("#b Scrollbar").length > 0) {
                xPos = window.$get('bScrollbar').scrollLeft;
                yPos = window.$get('bScrollbar').scrollTop;
            }
            
        }
        function EndRequestHandler(sender, args) {
            if ($("#bScrollbar").length > 0) {
                window.$get('bScrollbar').scrollLeft = xPos;
                window.$get('bScrollbar').scrollTop = yPos;
            }
            $("table[id$=Wizard1] > tbody").addClass("widget widget-tabs widget-wizard-pills");
            $("table[id$=Wizard1] > tbody > tr:first-child ").addClass("widget-head");
            $("table[id$=Wizard1] > tbody > tr:nth-child(2)").addClass("widget-body");
            $("#li0").addClass("no-padding");

        }
        function CancelClick() {
            return false;
        }
    </script>

    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

               

                <h3>DR Business Impact Analysis
                </h3>
                <div class="widget widget-heading-simple widget-body-white">

                    <div class="wizard ">
                        <asp:Wizard ID="Wizard1" runat="server" Width="100%" OnFinishButtonClick="BtnSaveFinish"
                            DisplaySideBar="False" ActiveStepIndex="0" StepPreviousButtonStyle-CssClass="btn btn-primary previous"
                            StartNextButtonStyle-CssClass="btn btn-primary previous" FinishPreviousButtonStyle-CssClass="wizardbutton prev"
                            OnPreRender="Wizard1_PreRender" OnActiveStepChanged="OnStepChange">
                            <NavigationStyle CssClass="pagination margin-bottom-none pull-right" HorizontalAlign="Center"
                                VerticalAlign="Middle" Width="83%" />
                            <FinishPreviousButtonStyle CssClass="btn btn-primary" />
                            <StepStyle CssClass="tab-content padding"></StepStyle>
                            <NavigationButtonStyle CssClass="btn btn-primary" />
                            <StartNextButtonStyle CssClass="btn btn-primary next"></StartNextButtonStyle>
                            <HeaderTemplate>
                                <ul>
                                    <li class="status"><span class="r">Step <span class="step-current">
                                        <%=CurrentStep%></span> of <span class="steps-total">
                                            <%=TotalSteps%></span></span> <span class="r text-primary">Completed: <span class="steps-complete">
                                                <%=CompletedSteps%></span></span> </li>
                                    <asp:Repeater ID="SideBarList" runat="server">
                                        <ItemTemplate>
                                            <li id="li<%#Container.ItemIndex%>" class="<%# GetClassForWizardStep(Container.DataItem) %>">
                                                <a title="<%# DataBinder.Eval(Container, "DataItem.Name")%>">
                                                    <span></span>
                                                    <%# Wizard1.WizardSteps.IndexOf(Container.DataItem as WizardStep) + 1%>
                                                </a></li>
                                        </ItemTemplate>
                                    </asp:Repeater>
                                </ul>
                            </HeaderTemplate>
                            <WizardSteps>
                                <asp:WizardStep ID="WizardStep1" runat="server" Title="Step1" StepType="Start">
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <h4>Application Description</h4>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Application Infraobject Name <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlApplicationGroupName" runat="server" AutoPostBack="True" OnSelectedIndexChanged="DdlApplicationChanged"
                                                        CssClass="selectpicker col-md-6" data-style="btn-default">
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="rfvAppName" runat="server" ControlToValidate="ddlApplicationGroupName"
                                                        InitialValue="0" ErrorMessage="Select Application Name"></asp:RequiredFieldValidator>
                                                    <asp:Label ID="lblApplication" runat="server" Visible="false" Text="Select One"></asp:Label>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Business Owner <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtBusinessOwner" MaxLength="100" CssClass="form-control" runat="server"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator15" runat="server" ControlToValidate="txtBusinessOwner"
                                                        ErrorMessage="Enter Name"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="rfvtxtName" ControlToValidate="txtBusinessOwner" Display="Dynamic"
                                                        runat="server" ErrorMessage="Only Numeric values & Special characters not allowed"
                                                        ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9 _]+$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Alternative Name(s)
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtAlternative" MaxLength="100" runat="server" CssClass="form-control" ValidationGroup="appDesc"></asp:TextBox>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator12" ControlToValidate="txtAlternative" Display="Dynamic"
                                                        runat="server" ErrorMessage="Only Numeric values & Special characters not allowed"
                                                        ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9 _]+$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Support Team<span class="inactive">*</span></label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtSupportTeam" CssClass="form-control" runat="server"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator17" runat="server" ControlToValidate="txtSupportTeam"
                                                        ErrorMessage="Enter Support Team Name"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator13" ControlToValidate="txtSupportTeam" Display="Dynamic"
                                                        runat="server" ErrorMessage="Only Numeric values & Special characters not allowed"
                                                        ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9 _]+$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Application Functions<span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlApplicationFunctions" runat="server" CssClass="selectpicker col-md-6"
                                                        data-style="btn-default">
                                                        <asp:ListItem Selected="true" Value="0">Choose One</asp:ListItem>
                                                        <asp:ListItem Value="1">Subscriber & Service </asp:ListItem>
                                                        <asp:ListItem Value="2">CDR Collection</asp:ListItem>
                                                        <asp:ListItem Value="3">Despatching data </asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator13" runat="server" ControlToValidate="ddlApplicationFunctions"
                                                        InitialValue="0" ErrorMessage="Select One!">
                                                    </asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Heavy Load Times <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtHeavyLoadTimes" runat="server" CssClass="form-control" ValidationGroup="appDesc"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator18" runat="server" ControlToValidate="txtHeavyLoadTimes"
                                                        ErrorMessage="Enter Time"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator2" runat="server" ControlToValidate="txtHeavyLoadTimes"
                                                        ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9]+$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Critical Times<span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtCriticalTimes" runat="server" CssClass="form-control" ValidationGroup="appDesc"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator19" runat="server" ControlToValidate="txtCriticalTimes"
                                                        ErrorMessage="Enter Critical Time"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ControlToValidate="txtCriticalTimes"
                                                        ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9]+$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </asp:WizardStep>
                                <asp:WizardStep ID="WizardStep2" runat="server" Title="Step2" OnDeactivate="WizardStep2_DeActive">
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <h4>Application Dependencies</h4>
                                            <div id="jScrollbar" style="height: 310px; overflow: auto;">
                                                <asp:ListView ID="lvInward" runat="server" OnItemUpdating="LvInwardItemUpdating"
                                                    OnItemDeleting="LvInwardItemDeleting" OnItemCreated="LvInwardItemCreated" OnItemInserting="LvInwardItemInserting"
                                                    OnItemEditing="LvInwardItemEditing" InsertItemPosition="LastItem">
                                                    <LayoutTemplate>
                                                        <table class=" dynamicTable table table-bordered table-condensed table-striped table-white" width="100%">
                                                            <thead>
                                                                <tr>
                                                                    <th colspan="5">Known Dependencies on Other Systems (Inward Data Flows)
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tr class="bold">

                                                                <td style="width: 20%">Server Name
                                                                </td>
                                                                <td style="width: 20%">Database Name
                                                                </td>
                                                                <td style="width: 30%;">Description of dependency
                                                                </td>
                                                                <td>DR Cat(RM use)
                                                                </td>
                                                                <td style="width: 10%">Action
                                                                </td>
                                                            </tr>
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </LayoutTemplate>
                                                    <ItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                <asp:Label ID="ServerName" Text='<%# ConvertServerName(Eval("ServerId")) %>' runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="DatabaseName" Text='<%# ConvertDatabaseName(Eval("DatabaseId")) %>'
                                                                    runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="eDescription" Text='<%# Eval("Description") %>' runat="server" Style="width: 90%; height: 15px"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="DRCategory" runat="server" Enabled="false" Text='<%# Eval("DRCategory") %>'
                                                                    CssClass="selectpicker col-md-11" data-style="btn-default">
                                                                    <asp:ListItem Value="0">--Choose One--</asp:ListItem>
                                                                    <asp:ListItem Value="1">Normal</asp:ListItem>
                                                                    <asp:ListItem Value="2">High</asp:ListItem>
                                                                    <asp:ListItem Value="3">Critical</asp:ListItem>
                                                                    <asp:ListItem Value="4">Very Critical</asp:ListItem>
                                                                </asp:DropDownList>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                    ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                            </td>
                                                            <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                                ConfirmText='<%# "Are you sure want to delete ? " %>' OnClientCancel="CancelClick">
                                                            </TK1:ConfirmButtonExtender>
                                                        </tr>
                                                    </ItemTemplate>
                                                    <EditItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                <asp:DropDownList ID="ddlEditServerList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="ddlEditDatabaseList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="Description" Text='<%# Eval("Description") %>' runat="server" CssClass="form-control"> </asp:TextBox>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="DRCategory" runat="server" Enabled="true" Text='<%# Eval("DRCategory") %>'
                                                                    CssClass="selectpicker col-md-11" data-style="btn-default">
                                                                    <asp:ListItem Value="0">--Choose One--</asp:ListItem>
                                                                    <asp:ListItem Value="1">Normal</asp:ListItem>
                                                                    <asp:ListItem Value="2">High</asp:ListItem>
                                                                    <asp:ListItem Value="3">Critical</asp:ListItem>
                                                                    <asp:ListItem Value="4">Very Critical</asp:ListItem>
                                                                </asp:DropDownList>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="ImgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                                                    ToolTip="Update" ImageUrl="~/Images/icons/up-arrow.jpg" />
                                                                <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                                    ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                                            </td>
                                                        </tr>
                                                    </EditItemTemplate>
                                                    <InsertItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:DropDownList ID="ddlInwardServerList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblInServer" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="ddlInwardDatabaseList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblApplicationName" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                                                            </td>
                                                            </td>
                                                                <td>
                                                                    <asp:TextBox ID="Description" Text='<%# Eval("Description") %>' runat="server" CssClass="form-control"> </asp:TextBox>
                                                                    <asp:Label ID="lblDescription" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                                                </td>
                                                            <td>
                                                                <asp:DropDownList ID="DRCategory" runat="server" Text='<%# Eval("DRCategory") %>'
                                                                    CssClass="selectpicker col-md-11" data-style="btn-default">
                                                                    <asp:ListItem Value="0">--Choose One--</asp:ListItem>
                                                                    <asp:ListItem Value="1">Normal</asp:ListItem>
                                                                    <asp:ListItem Value="2">High</asp:ListItem>
                                                                    <asp:ListItem Value="3">Critical</asp:ListItem>
                                                                    <asp:ListItem Value="4">Very Critical</asp:ListItem>
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblDRCategory" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                    ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                            </td>
                                                        </tr>
                                                    </InsertItemTemplate>
                                                </asp:ListView>
                                                <asp:ListView ID="lvOutward" runat="server" OnItemInserting="LvOutwareItemInserting"
                                                    OnItemCreated="LvOutwardItemCreated" InsertItemPosition="LastItem" OnItemDeleting="LvOutwardItemDeleting">
                                                    <LayoutTemplate>
                                                        <table class="dynamicTable table table-bordered table-condensed table-striped" width="100%">
                                                            <thead>
                                                                <tr>
                                                                    <th colspan="5">Other Systems known to Depend on this system (Outward Data Flows)
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tr class="bold">

                                                                <td style="width: 20%">Server Name
                                                                </td>
                                                                <td style="width: 20%">Database Name
                                                                </td>
                                                                <td style="width: 30%;">Description of dependency
                                                                </td>
                                                                <td>DR Cat(RM use)
                                                                </td>
                                                                <td style="width: 10%">Action
                                                                </td>
                                                            </tr>
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </LayoutTemplate>
                                                    <ItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                <asp:Label ID="ServerName" Text='<%# ConvertServerName(Eval("ServerId")) %>' runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="DatabaseName" Text='<%# ConvertDatabaseName(Eval("DatabaseId")) %>'
                                                                    runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="Description1" Text='<%# Eval("Description") %>' runat="server" Style="width: 90%; height: 15px"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="DRCategory1" Enabled="false" runat="server" Text='<%# Eval("DRCategory") %>'
                                                                    CssClass="selectpicker col-md-11" data-style="btn-default">
                                                                    <asp:ListItem Value="0">--Choose One--</asp:ListItem>
                                                                    <asp:ListItem Value="1">Normal</asp:ListItem>
                                                                    <asp:ListItem Value="2">High</asp:ListItem>
                                                                    <asp:ListItem Value="3">Critical</asp:ListItem>
                                                                    <asp:ListItem Value="4">Very Critical</asp:ListItem>
                                                                </asp:DropDownList>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                    ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                            </td>
                                                            <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                                ConfirmText='<%# "Are you sure want to delete ? " %>' OnClientCancel="CancelClick">
                                                            </TK1:ConfirmButtonExtender>
                                                        </tr>
                                                    </ItemTemplate>
                                                    <InsertItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:DropDownList ID="ddlOutwardServerList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblOutServer" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="ddlOutwardDatabaseList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblApplicationName" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="Description" Text='<%# Eval("Description") %>' runat="server" CssClass="form-control"> </asp:TextBox>
                                                                <asp:Label ID="lblDescription" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="DRCategory" runat="server" Text='<%# Eval("DRCategory") %>'
                                                                    CssClass="selectpicker col-md-11" data-style="btn-default">
                                                                    <asp:ListItem Value="0">--Choose One--</asp:ListItem>
                                                                    <asp:ListItem Value="1">Normal</asp:ListItem>
                                                                    <asp:ListItem Value="2">High</asp:ListItem>
                                                                    <asp:ListItem Value="3">Critical</asp:ListItem>
                                                                    <asp:ListItem Value="4">Very Critical</asp:ListItem>
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblDRCategory" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                    ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                            </td>
                                                        </tr>
                                                    </InsertItemTemplate>
                                                </asp:ListView>
                                                <asp:ListView ID="lvInfrastructure" runat="server" OnItemDeleting="LvInfrastructureItemDeleting"
                                                    OnItemCreated="LvInfraItemCreated" InsertItemPosition="LastItem" OnItemInserting="LvInfrastructureInserting">
                                                    <LayoutTemplate>
                                                        <table class=" dynamicTable table table-bordered table-condensed table-striped" width="100%">
                                                            <thead>
                                                                <tr>
                                                                    <th colspan="5">Infrastructure and Central Systems & Utilities
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tr class="bold">

                                                                <td style="width: 20%">Server Name
                                                                </td>
                                                                <td style="width: 20%">Database Name
                                                                </td>
                                                                <td style="width: 30%;">Description of dependency
                                                                </td>
                                                                <td>DR Cat(RM use)
                                                                </td>
                                                                <td style="width: 10%">Action
                                                                </td>
                                                            </tr>
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </LayoutTemplate>
                                                    <ItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                <asp:Label ID="ServerName" Text='<%# ConvertServerName(Eval("ServerId")) %>' runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="DatabaseName" Text='<%# ConvertDatabaseName(Eval("DatabaseId")) %>'
                                                                    runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="Description2" Text='<%# Eval("Description") %>' runat="server" Style="width: 90%; height: 15px"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="DRCategory2" Enabled="false" runat="server" Text='<%# Eval("DRCategory") %>'
                                                                    CssClass="selectpicker col-md-11" data-style="btn-default">
                                                                    <asp:ListItem Value="0">--Choose One--</asp:ListItem>
                                                                    <asp:ListItem Value="1">Normal</asp:ListItem>
                                                                    <asp:ListItem Value="2">High</asp:ListItem>
                                                                    <asp:ListItem Value="3">Critical</asp:ListItem>
                                                                    <asp:ListItem Value="4">Very Critical</asp:ListItem>
                                                                </asp:DropDownList>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                    ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                            </td>
                                                            <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                                ConfirmText='<%# "Are you sure want to delete ? " %>' OnClientCancel="CancelClick">
                                                            </TK1:ConfirmButtonExtender>
                                                        </tr>
                                                    </ItemTemplate>
                                                    <InsertItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:DropDownList ID="ddlInfrawardServerList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblInfraaServer" runat="server" Text="" ForeColor="red" Visible="false"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="ddlInfrawardDatabaseList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblInfraDBName" runat="server" Text="" ForeColor="red" Visible="false"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="Description" Text='<%# Eval("Description") %>' runat="server" CssClass="form-control"> </asp:TextBox>
                                                                <asp:Label ID="lblDescription" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="DRCategory" runat="server" Text='<%# Eval("DRCategory") %>'
                                                                    CssClass="selectpicker col-md-11" data-style="btn-default">
                                                                    <asp:ListItem Value="0">--Choose One--</asp:ListItem>
                                                                    <asp:ListItem Value="1">Normal</asp:ListItem>
                                                                    <asp:ListItem Value="2">High</asp:ListItem>
                                                                    <asp:ListItem Value="3">Critical</asp:ListItem>
                                                                    <asp:ListItem Value="4">Very Critical</asp:ListItem>
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblDRCategory" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                    ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                            </td>
                                                        </tr>
                                                    </InsertItemTemplate>
                                                </asp:ListView>
                                                <asp:ListView ID="LvExternalInward" runat="server" OnItemDeleting="LvExternalInwardDeleting"
                                                    OnItemCreated="LvExternalInwardItemCreated" InsertItemPosition="LastItem" OnItemInserting="LvExternalInwardInserting">
                                                    <LayoutTemplate>
                                                        <table class="dynamicTable table table-bordered table-condensed table-striped" width="100%">
                                                            <thead>
                                                                <tr>
                                                                    <th colspan="5">External Inward Data Flows
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tr class="bold">

                                                                <td style="width: 19%">Server Name
                                                                </td>
                                                                <td style="width: 19%">Database Name
                                                                </td>
                                                                <td>Description of dependency
                                                                </td>
                                                                <td style="width: 10%">Action
                                                                </td>
                                                            </tr>
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </LayoutTemplate>
                                                    <ItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                <asp:Label ID="ServerName" Text='<%# ConvertServerName(Eval("ServerId")) %>' runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="DatabaseName" Text='<%# ConvertDatabaseName(Eval("DatabaseId")) %>'
                                                                    runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="BVolume" Text='<%# Eval("Description") %>' runat="server" Style="width: 83%; height: 15px"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                    ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                            </td>
                                                        </tr>
                                                    </ItemTemplate>
                                                    <InsertItemTemplate>
                                                        <tr>
                                                            <td>
                                                                <asp:DropDownList ID="ddlExternalInwardServerList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblExterServer" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="ddlExternalInwardDatabaseList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblApplicationName" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="Description" Text='<%# Eval("Description") %>' runat="server" CssClass="form-control"> </asp:TextBox>
                                                                <asp:Label ID="lblDescription" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                    ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                            </td>
                                                        </tr>
                                                    </InsertItemTemplate>
                                                </asp:ListView>
                                                <asp:ListView ID="lvExternalOutward" runat="server" OnItemDeleting="LvExternalOutwardDeleting"
                                                    OnItemCreated="LvExternalOutwardItemCreated" OnItemInserting="LvExternalOutwardInserting"
                                                    InsertItemPosition="LastItem">
                                                    <LayoutTemplate>
                                                        <table class=" dynamicTable table table-bordered table-condensed table-striped" width="100%">
                                                            <thead>
                                                                <tr>
                                                                    <th colspan="5">External Outward Data Flows
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tr class="bold">

                                                                <td style="width: 19%">Server Name
                                                                </td>
                                                                <td style="width: 19%">Database Name
                                                                </td>
                                                                <td>Description of dependency
                                                                </td>
                                                                <td style="width: 10%">Action
                                                                </td>
                                                            </tr>
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </LayoutTemplate>
                                                    <ItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                <asp:Label ID="ServerName" Text='<%# ConvertServerName(Eval("ServerId")) %>' runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="DatabaseName" Text='<%# ConvertDatabaseName(Eval("DatabaseId")) %>'
                                                                    runat="server"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="Description3" Text='<%# Eval("Description") %>' runat="server" Style="width: 83%; height: 15px"> </asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                    ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                            </td>
                                                            <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                                ConfirmText='<%# "Are you sure want to delete? " %>' OnClientCancel="CancelClick">
                                                            </TK1:ConfirmButtonExtender>
                                                        </tr>
                                                    </ItemTemplate>
                                                    <InsertItemTemplate>
                                                        <tr>

                                                            <td>
                                                                <asp:DropDownList ID="ddlExternalOutwardServerList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblExtOutServer" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:DropDownList ID="ddlExternalOutwardDatabaseList" runat="server" CssClass="selectpicker col-md-11"
                                                                    data-style="btn-default">
                                                                </asp:DropDownList>
                                                                <asp:Label ID="lblApplicationName" runat="server" Text="" Visible="false" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="Description" Text='<%# Eval("Description") %>' runat="server" CssClass="form-control"> </asp:TextBox>
                                                                <asp:Label ID="lblDescription" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                                            </td>
                                                            <td>
                                                                <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                    ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                            </td>
                                                        </tr>
                                                    </InsertItemTemplate>
                                                </asp:ListView>
                                            </div>
                                        </div>
                                    </div>
                                </asp:WizardStep>
                                <asp:WizardStep ID="WizardStep3" runat="server" Title="Step 3">
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <h4>Business Information
                                            </h4>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Business Representatives <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtBusinessRepresentatives" CssClass="form-control" runat="server"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator20" runat="server" ControlToValidate="txtBusinessRepresentatives"
                                                        ErrorMessage="Enter Name"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Location of Business <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtLocationofBusiness" runat="server" CssClass="form-control" TextMode="MultiLine"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator21" runat="server" ControlToValidate="txtLocationofBusiness"
                                                        ErrorMessage="Ente Location"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Brief Overview of impact <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlBriefOverviewofImpact" runat="server"
                                                        CssClass="selectpicker col-md-6" data-style="btn-default">
                                                        <asp:ListItem Selected="true" Value="0">Choose One</asp:ListItem>
                                                        <asp:ListItem Value="1">Revenue </asp:ListItem>
                                                        <asp:ListItem Value="2">Reporting  </asp:ListItem>
                                                        <asp:ListItem Value="3">Subscriber </asp:ListItem>
                                                        <asp:ListItem Value="4">Fraud Detection  </asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator36" runat="server" ControlToValidate="ddlBriefOverviewofImpact"
                                                        InitialValue="0" ErrorMessage="Select One!">
                                                    </asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Methods of alternative processing <span class="inactive">*</span></label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtMethods" CssClass="form-control" runat="server"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator22" runat="server" ControlToValidate="txtMethods"
                                                        ErrorMessage="Enter Method Name"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    BCP Availability <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:RadioButtonList ID="rdBCPAvailability" runat="server" RepeatDirection="Horizontal">
                                                        <asp:ListItem Selected="true" Value="1">Yes</asp:ListItem>
                                                        <asp:ListItem Value="0">No</asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Maximum period of downtime <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtMaxPeriodofDowntime" CssClass="form-control" runat="server"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator23" runat="server" ControlToValidate="txtMaxPeriodofDowntime"
                                                        ErrorMessage="Enter DownTime"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <asp:ListView ID="lvBUFunction" runat="server" InsertItemPosition="LastItem" OnItemInserting="LvBuFunctionInsertingClick"
                                                OnItemEditing="LvBuFunctionEditingClick" OnItemUpdating="LvBuFunctionUpdatingClick"
                                                OnItemDeleting="LvBuFunctionDeletingClick">
                                                <LayoutTemplate>
                                                    <table class=" dynamicTable table table-bordered table-condensed table-striped" width="100%">
                                                        <thead>
                                                            <tr>
                                                                <th colspan="7"></th>
                                                            </tr>
                                                        </thead>
                                                        <tr class="bold">

                                                            <td style="width: 25%">BU Function supported
                                                            </td>
                                                            <td style="width: 13%;">Tier 1a
                                                            </td>
                                                            <td style="width: 13%;">Tier 1b
                                                            </td>
                                                            <td style="width: 13%;">Tier 2
                                                            </td>
                                                            <td style="width: 13%;">Tier 3
                                                            </td>
                                                            <td style="width: 13%;">Tier 4
                                                            </td>
                                                            <td style="width: 10%;">Action
                                                            </td>
                                                        </tr>
                                                        <tbody>
                                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                        </tbody>
                                                    </table>
                                                </LayoutTemplate>
                                                <ItemTemplate>
                                                    <tr>

                                                        <td>
                                                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                            <asp:Label ID="SupportField" Text='<%# Eval("SupportField") %>' runat="server"> </asp:Label>
                                                            <asp:TextBox ID="txtSupportField" CssClass="form-control" Text='<%# Eval("SupportField") %>' runat="server"
                                                                Visible="false"></asp:TextBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier1avalue" Checked='<%# IsCheckedTier1a( Eval("Tier1a")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier1bvalue" Checked='<%# IsCheckedTier1b( Eval("Tier1b")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier2value" Checked='<%# IsCheckedTier2( Eval("Tier2")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier3value" Checked='<%# IsCheckedTier3( Eval("Tier3")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier4value" Checked='<%# IsCheckedTier4( Eval("Tier4")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                            <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Update" ToolTip="Update"
                                                                AlternateText="Update" ImageUrl="~/Images/icons/navigation-090.png" Visible="false" />
                                                            <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                        </td>
                                                        <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                            ConfirmText='<%# "Are you sure want to delete " + Eval("SupportField") + " ? " %>'
                                                            OnClientCancel="CancelClick">
                                                        </TK1:ConfirmButtonExtender>
                                                    </tr>
                                                </ItemTemplate>
                                                <EditItemTemplate>
                                                    <tr>

                                                        <td>
                                                            <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                            <asp:Label ID="SupportField" Text='<%# Eval("SupportField") %>' runat="server"> </asp:Label>
                                                            <asp:TextBox ID="txtSupportField" CssClass="form-control" Text='<%# Eval("SupportField") %>' runat="server"
                                                                Visible="false"></asp:TextBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier1a" Checked='<%# IsCheckedTier1a( Eval("Tier1a")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier1b" Checked='<%# IsCheckedTier1b( Eval("Tier1b")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier2" Checked='<%# IsCheckedTier2( Eval("Tier2")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier3" Checked='<%# IsCheckedTier3( Eval("Tier3")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier4" Checked='<%# IsCheckedTier4( Eval("Tier4")) %>'></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" Visible="false" />
                                                            <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Update" ToolTip="Update"
                                                                AlternateText="Update" ImageUrl="~/Images/icons/navigation-090.png" />
                                                            <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                                ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                                        </td>
                                                    </tr>
                                                </EditItemTemplate>
                                                <InsertItemTemplate>
                                                    <tr>

                                                        <td>
                                                            <asp:TextBox ID="SupportField" CssClass="form-control" Text='<%# Eval("SupportField") %>' runat="server"> </asp:TextBox>
                                                            <asp:Label ID="lblerror" Visible="false" Text="*" runat="server"></asp:Label>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier1a" Enabled="false"></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier1b" Enabled="false"></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier2" Enabled="false"></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier3" Enabled="false"></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:CheckBox runat="server" ID="chkTier4" Enabled="false"></asp:CheckBox>
                                                        </td>
                                                        <td>
                                                            <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                        </td>
                                                    </tr>
                                                </InsertItemTemplate>
                                            </asp:ListView>
                                        </div>
                                    </div>
                                </asp:WizardStep>
                                <asp:WizardStep ID="WizardStep4" runat="server">
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <h4>IT Disaster Impact Assessment
                                            </h4>
                                            <div class="grid-18" style="height: 310px; overflow: auto;">
                                                <table class="dynamicTable table table-bordered table-striped table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th>
                                                                <asp:CheckBox ID="chkNone" runat="server" OnCheckedChanged="chkNoneChecked" AutoPostBack="true"
                                                                    CssClass="vertical-align" ValidationGroup="valcheckbox" />None
                                                            </th>
                                                            <th>
                                                                <asp:CheckBox ID="chkG24Hours" runat="server" OnCheckedChanged="chkG24HoursChecked"
                                                                    AutoPostBack="true" CssClass="vertical-align" />>24 Hours
                                                            </th>
                                                            <th>
                                                                <asp:CheckBox ID="chkL24Hours" runat="server" OnCheckedChanged="chkL24HoursChecked"
                                                                    AutoPostBack="true" CssClass="vertical-align" Text="<24 Hours"></asp:CheckBox>
                                                            </th>
                                                            <th>
                                                                <asp:CheckBox ID="chkMinimal" runat="server" OnCheckedChanged="chkMinimalChecked"
                                                                    AutoPostBack="true" CssClass="vertical-align" />Minimal
                                                            </th>
                                                            <th>
                                                                <asp:CheckBox ID="chkAll" runat="server" OnCheckedChanged="chkAllChecked" AutoPostBack="true"
                                                                    CssClass="vertical-align" ValidationGroup="valBiA" />All
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td>No data needs to be recovered, business does not need historical data
                                                            </td>
                                                            <td>Specify the amount of business processing that can be recovered manually, e.g. 2,
                                                                    5, 10 days work.
                                                            </td>
                                                            <td>Any work processed since the last daily data backup can be recovered (re-key, scan)?
                                                            </td>
                                                            <td>As many transactions as possible must be recovered, lost transactions can be recovered
                                                                    manually (re-key, scan)
                                                            </td>
                                                            <td>Every transaction must be recovered right up to the moment the system became unavailable
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <asp:TextBox ID="txtNone" runat="server" CssClass="form-control" Enabled="false"></asp:TextBox>
                                                                <asp:RequiredFieldValidator ID="rfvNone" runat="server" ControlToValidate="txtNone"
                                                                    ErrorMessage="*" Enabled="false"></asp:RequiredFieldValidator>
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="txtG24Hours" runat="server" CssClass="form-control" Enabled="false"></asp:TextBox>
                                                                <asp:RequiredFieldValidator ID="rfvGHours" runat="server" ControlToValidate="txtG24Hours"
                                                                    ErrorMessage="*" Enabled="false"></asp:RequiredFieldValidator><br />
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="txtL24Hours" runat="server" CssClass="form-control" Enabled="false"></asp:TextBox>
                                                                <asp:RequiredFieldValidator ID="rfvLHours" runat="server" ControlToValidate="txtL24Hours"
                                                                    ErrorMessage="*" Enabled="false"></asp:RequiredFieldValidator>
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="txtMinimal" runat="server" CssClass="form-control" Enabled="false"></asp:TextBox>
                                                                <asp:RequiredFieldValidator ID="RfvMini" runat="server" ControlToValidate="txtMinimal"
                                                                    ErrorMessage="*" Enabled="false"></asp:RequiredFieldValidator>
                                                            </td>
                                                            <td>
                                                                <asp:TextBox ID="txtAll" runat="server" CssClass="form-control" Enabled="false"></asp:TextBox>
                                                                <asp:RequiredFieldValidator ID="rfvAll" runat="server" ControlToValidate="txtAll"
                                                                    ErrorMessage="*" Enabled="false"></asp:RequiredFieldValidator>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <table class="dynamicTable table table-bordered table-condensed table-striped" width="95%">
                                                    <thead>
                                                        <tr>
                                                            <th colspan="2">Business Impacts due to System Outage
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td colspan="2">The table below should outline the impact of the loss of this system following a
                                                                        disaster (Not a limited service interruption) based on a worst possible case scenario
                                                                        for your business.
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2">Impacts are classified into 6 categories:
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>• Financial loss/profit reduction ($$$)
                                                            </td>
                                                            <td>• Reputation impact / credibility (brand name)
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>• Regulatory impact (investigation / fines)
                                                            </td>
                                                            <td>• Operational impact (service)
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>• Customer impact (sales)
                                                            </td>
                                                            <td>• Compliance impact (license obligation)
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2" class="bold">Instructions:
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2">Under each heading enter the time taken to reach that level of impact using the
                                                                        following time periods:
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2" class="align-center">
                                                                <b>4</b> hours, <b>8 </b>hours, <b>12</b> hours, <b>24</b> hours, <b>48</b> hours,
                                                                        <b>72</b> hours
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2">Show the maximum impact that can be reached under the last column where you enter
                                                                        data.
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <hr />
                                                <h5>Financial Loss/Profit Reduction</h5>
                                                <hr />
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Financial Loss/Profit Reduction
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:RadioButtonList ID="rblFinancialLoss" runat="server" RepeatDirection="Horizontal">
                                                            <asp:ListItem Selected="true" Value="1">Yes</asp:ListItem>
                                                            <asp:ListItem Value="0">No</asp:ListItem>
                                                        </asp:RadioButtonList>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtFinancialHours" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator24" runat="server" ControlToValidate="txtFinancialHours"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator3" runat="server" ControlToValidate="txtFinancialHours"
                                                            ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9 ]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <hr />
                                                <h5>Regulatory Impact</h5>
                                                <hr />
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Regulatory Impact <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:RadioButtonList ID="rblRegulatoryImpact" runat="server" RepeatDirection="Horizontal">
                                                            <asp:ListItem Selected="true" Value="1">Yes</asp:ListItem>
                                                            <asp:ListItem Value="0">No</asp:ListItem>
                                                        </asp:RadioButtonList>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtRegulatoryHours" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator25" runat="server" ControlToValidate="txtRegulatoryHours"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator4" runat="server" ControlToValidate="txtRegulatoryHours"
                                                            ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9 ]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <hr />
                                                <h5>Customer Impact</h5>
                                                <hr />
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Customer retention <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlCustomerRetention" runat="server"
                                                            CssClass="selectpicker col-md-6" data-style="btn-default">
                                                            <asp:ListItem Selected="true" Value="0">Select Customer Retention</asp:ListItem>
                                                            <asp:ListItem Value="1">Increase in customer complaints</asp:ListItem>
                                                            <asp:ListItem Value="2">Serious complaints, possible account closures</asp:ListItem>
                                                            <asp:ListItem Value="3">Large scale complaints, some customer loss account closures</asp:ListItem>
                                                            <asp:ListItem Value="4">Serious customer loss</asp:ListItem>
                                                            <asp:ListItem Value="5">Large scale customer loss</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator37" runat="server" ControlToValidate="ddlCustomerRetention"
                                                            InitialValue="0" ErrorMessage="Select One!">
                                                        </asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtCustomerRetentionHours" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator26" runat="server" ControlToValidate="txtCustomerRetentionHours"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator5" runat="server" ControlToValidate="txtCustomerRetentionHours"
                                                            ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9 ]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <hr />
                                                <h5>Brand Name Impact</h5>
                                                <hr />
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Reputation <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlBrandNameReputation" runat="server"
                                                            CssClass="selectpicker col-md-6" data-style="btn-default">
                                                            <asp:ListItem Selected="true" Value="0">Choose One</asp:ListItem>
                                                            <asp:ListItem Value="1">No media coverage</asp:ListItem>
                                                            <asp:ListItem Value="2">Limited local or industry media coverage</asp:ListItem>
                                                            <asp:ListItem Value="3">Limited national media coverage</asp:ListItem>
                                                            <asp:ListItem Value="4">Sustained national media, Possible international media coverage </asp:ListItem>
                                                            <asp:ListItem Value="5">Sustained negative national and international media coverage</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator38" runat="server" ControlToValidate="ddlBrandNameReputation"
                                                            InitialValue="0" ErrorMessage="Select One!">
                                                        </asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtBrandHMI" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator27" runat="server" ControlToValidate="txtBrandHMI"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator6" runat="server" ControlToValidate="txtBrandHMI"
                                                            ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9 ]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <hr />
                                                <h5>Operational Impact</h5>
                                                <hr />
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Effect <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlOperationalEffect" runat="server"
                                                            CssClass="selectpicker col-md-6" data-style="btn-default">
                                                            <asp:ListItem Value="0" Selected="true">Choose One</asp:ListItem>
                                                            <asp:ListItem Value="1">Local annoyance</asp:ListItem>
                                                            <asp:ListItem Value="2">Local inconvenience, possible catch-up required</asp:ListItem>
                                                            <asp:ListItem Value="3">Overtime, possible BCP invocation, additional staff</asp:ListItem>
                                                            <asp:ListItem Value="4">Serious operational impact, including BCP invocation</asp:ListItem>
                                                            <asp:ListItem Value="5">Significant operational impact</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator39" runat="server" ControlToValidate="ddlOperationalEffect"
                                                            InitialValue="0" ErrorMessage="Select One!">
                                                        </asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtOperationalEffect" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator28" runat="server" ControlToValidate="txtOperationalEffect"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator7" runat="server" ControlToValidate="txtOperationalEffect"
                                                            ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9 ]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Management Control <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlManagementControl" runat="server"
                                                            CssClass="selectpicker col-md-6" data-style="btn-default">
                                                            <asp:ListItem Selected="true" Value="0">Choose One</asp:ListItem>
                                                            <asp:ListItem Value="1">No impact</asp:ListItem>
                                                            <asp:ListItem Value="2">Local management</asp:ListItem>
                                                            <asp:ListItem Value="3">Senior Management, possible BU Executive involvement</asp:ListItem>
                                                            <asp:ListItem Value="4">Senior Executive Management involvement </asp:ListItem>
                                                            <asp:ListItem Value="5">Direct Executive Management control, Board involvement</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator40" runat="server" ControlToValidate="ddlManagementControl"
                                                            InitialValue="0" ErrorMessage="Select One!">
                                                        </asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtManagementControlHours" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator29" runat="server" ControlToValidate="txtOperationalEffect"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator8" runat="server" ControlToValidate="txtManagementControlHours"
                                                            ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <hr />
                                                <h5>Compliance Impact</h5>
                                                <hr />
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Compliance <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtCompliance" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator30" runat="server" ControlToValidate="txtCompliance"
                                                            ErrorMessage="Enter Compliance"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtComplianceHours" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator31" runat="server" ControlToValidate="txtComplianceHours"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator9" runat="server" ControlToValidate="txtComplianceHours"
                                                            ErrorMessage="Special Character not allowed" ValidationExpression="^[a-zA-Z0-9]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        License <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtLicence" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator32" runat="server" ControlToValidate="txtLicence"
                                                            ErrorMessage="Enter Lincence Info"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtLicenceHours" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator33" runat="server" ControlToValidate="txtLicenceHours"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator10" runat="server"
                                                            ControlToValidate="txtLicenceHours" ErrorMessage="Special Character not allowed"
                                                            ValidationExpression="^[a-zA-Z0-9]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Credit Fraud <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtCreditFraud" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator34" runat="server" ControlToValidate="txtCreditFraud"
                                                            ErrorMessage="Create Fraud"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Hours <span class="inactive">*</span>
                                                    </label>

                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtCreditFraudHours" CssClass="form-control" runat="server"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator35" runat="server" ControlToValidate="txtCreditFraudHours"
                                                            ErrorMessage="Enter Hours"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator11" runat="server"
                                                            ControlToValidate="txtCreditFraudHours" ErrorMessage="Special Character not allowed"
                                                            ValidationExpression="^[a-zA-Z0-9]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </asp:WizardStep>
                            </WizardSteps>
                        </asp:Wizard>
                    </div>
                </div>
                <TK1:ModalPopupExtender ID="mdlPopup" runat="server" PopupControlID="Panel1" TargetControlID="hf">
                </TK1:ModalPopupExtender>
                <asp:HiddenField ID="hf" runat="server" />
                <asp:Panel ID="Panel1" runat="server" Style="display: none" Width="450px" Height="450px">
                    <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
                        <ul class="action-tabs">
                            <li title="Close">
                                <asp:LinkButton ID="LinkButton2" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                            </li>
                        </ul>
                        <div class="block-content  margin-right margin-top1em">
                            <div class="block-controls">
                                <h1>
                                    <asp:Label ID="lblInfo" runat="server" Text="BIA Information"></asp:Label>
                                </h1>
                            </div>
                            <div>
                                <img src="~/Images/icons/high_16.png" runat="server" />
                                &nbsp
                                        <asp:Label ID="lblErr" runat="server" Text="Please Insert Values Any One of List In Application Dependencies"></asp:Label>
                            </div>
                            &nbsp
                                    <div class="message no-margin">
                                    </div>
                        </div>
                    </div>
                </asp:Panel>
                <span class="bold">Note:</span>
                <span class="inactive">*</span>
                <span class="black">Required Fields</span>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>