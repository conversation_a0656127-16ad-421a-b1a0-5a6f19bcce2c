﻿using CP.BusinessFacade;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common;
using CP.Common.Shared;
using System.Web.UI.HtmlControls;
using CP.Common.DatabaseEntity;


namespace CP.UI.Controls
{
    public partial class AzureSiteDetailsMonitoring : BaseControl
    {
        //protected void Page_Load(object sender, EventArgs e)
        //{

        //}

        private readonly ILog _logger = LogManager.GetLogger(typeof(AzureSiteDetailsMonitoring));

        private static readonly ILog Log = LogManager.GetLogger(typeof(AzureSiteDetailsMonitoring));

        private static IFacade facade = new Facade();

        public override void PrepareView()
        {

            int infraid = Convert.ToInt32(Session["AzureInfraid"]);
            AzureDetailsMonitor(infraid);
            //var infradetails = Facade.GetInfraObjectById(infraid);
            //var Replidetails = Facade.GetEMCSRDFSGbyReplicationId(infradetails.PRReplicationId);
            //EmcsrdfSgLogs(infraid);
        }

          public void AzureDetailsMonitor(int id)
        {
            int infraobjectid = Convert.ToInt32(Session["AzureInfraid"]);
            var databaseAzure = Facade.AzureMonitorStatusGetByInfraId(infraobjectid);
            var infradetails = Facade.GetInfraObjectById(infraobjectid);
            var prrep = Facade.GetAzureRepliId(infradetails.PRReplicationId);
            var drrep = Facade.GetAzureRepliId(infradetails.DRReplicationId);
            if (databaseAzure != null)
            {
                lblPRVMNm.Text = prrep.PublicIPAddress;//databaseAzure.FriendlyName;
                lblDRVMNm.Text = drrep.PublicIPAddress;//databaseAzure.FriendlyName;
                //lblDRVaultNm.Text = databaseAzure.VirtualMachineNameDR;
                lblPRLocationNm.Text = databaseAzure.PrimaryFabricFriendlyName;
                lblDRLocationNm.Text = databaseAzure.RecoveryFabricFriendlyName;
                lblPRVaultNm.Text = databaseAzure.RecoveryServicesVaultName;
                lblDRVaultNm.Text = databaseAzure.RecoveryServicesVaultName;
                lblPRProvider.Text = databaseAzure.ReplicationProvider;
                lblDRProvider.Text = databaseAzure.ReplicationProvider;
                lblPRAllowedOperation.Text = !string.IsNullOrEmpty(databaseAzure.AllowedOperations) ? databaseAzure.AllowedOperations : "NA";
                lblDRAllowedOperation.Text = "NA";
                lblPRActiveLoc.Text = databaseAzure.ActiveLocationState;
                lblDRActiveLoc.Text = "NA";

               // PRIPAddress.Text = databaseAzure.IPAddress;
                
                    //PRIPAddress.Text = databaseAzure.IPAddress != string.Empty ? databaseAzure.IPAddress : "N/A";
                    ////  DRIPAddress.Text = databaseAzure.DRIPAddress;
                    //DRIPAddress.Text = databaseAzure.DRIPAddress != string.Empty ? databaseAzure.DRIPAddress : "N/A";
               

                healthPR141.Attributes.Add("class", databaseAzure.IPAddress != string.Empty ? "health-up" : "Active");
                healthDR142.Attributes.Add("class", databaseAzure.DRIPAddress != string.Empty ? "health-up" : "Active");
            

                lblPR_RepliHealth.Text = "NA";
                lblDR_RepliHealth.Text = databaseAzure.ReplicationHealth;
                lblPRStat.Text = databaseAzure.ProtectionState;
                lblDRStat.Text = "NA";
                lblPR_RPO.Text = "NA";
                lblDR_RPO.Text = databaseAzure.DataLag;
                lblPRCrash.Text = "NA";
                lblDRCrash.Text = databaseAzure.CrashConsistent;
                lblPRApp.Text = "NA";
                lblDRApp.Text = databaseAzure.ApplicationConsistent;
                lblPR_FailoverTime.Text = databaseAzure.LastSuccessfulFailoverTime;
                lblDR_FailoverTime.Text = "NA";
                lblPR_TestTime.Text = databaseAzure.LastSuccessfulTestFailoverTime;
                lblDR_TestTime.Text = "NA";


            }
            else
            {

                lblPRVMNm.Text = "N/A";
                lblDRVMNm.Text = "N/A";
                lblDRVaultNm.Text = "N/A";
                lblPRLocationNm.Text = "N/A";
                lblDRLocationNm.Text = "N/A";
                lblPRVaultNm.Text = "N/A";
                lblDRVaultNm.Text = "N/A";
                lblPRProvider.Text = "N/A";
                lblDRProvider.Text = "N/A";
                lblPRAllowedOperation.Text = "N/A";
                lblDRAllowedOperation.Text = "N/A";
                lblPRActiveLoc.Text = "N/A";
                lblDRActiveLoc.Text = "N/A";

                lblPR_RepliHealth.Text = "N/A";
                lblDR_RepliHealth.Text = "N/A";
                lblPRStat.Text = "N/A";
                lblDRStat.Text = "N/A";
                lblPR_RPO.Text = "N/A";
                lblDR_RPO.Text = "N/A";
                lblPRCrash.Text = "N/A";
                lblDRCrash.Text = "N/A";
                lblPRApp.Text = "N/A";
                lblDRApp.Text = "N/A";
                lblPR_FailoverTime.Text = "N/A";
                lblDR_FailoverTime.Text = "N/A";
                lblPR_TestTime.Text = "N/A";
                lblDR_TestTime.Text = "N/A";
             
            }

            var infradetail = facade.GetInfraObjectById(id);
            if (infradetail != null)
            {
                if (infradetail.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                {
                    PRIPAddress.Text = databaseAzure.IPAddress != string.Empty ? databaseAzure.IPAddress : "N/A";
                    //  DRIPAddress.Text = databaseAzure.DRIPAddress;
                    DRIPAddress.Text = databaseAzure.DRIPAddress != string.Empty ? databaseAzure.DRIPAddress : "N/A";

                    lblPRLocationNm.Text = databaseAzure.RecoveryFabricFriendlyName;
                    lblDRLocationNm.Text = databaseAzure.PrimaryFabricFriendlyName;

                    lblPRVMNm.Text = drrep.PublicIPAddress;//databaseAzure.FriendlyName;
                    lblDRVMNm.Text = prrep.PublicIPAddress;//databaseAzure.FriendlyName;
                }
                else
                {
                    PRIPAddress.Text = databaseAzure.IPAddress != string.Empty ? databaseAzure.IPAddress : "N/A";
                    //  DRIPAddress.Text = databaseAzure.DRIPAddress;
                    DRIPAddress.Text = databaseAzure.DRIPAddress != string.Empty ? databaseAzure.DRIPAddress : "N/A";

                    lblPRLocationNm.Text = databaseAzure.PrimaryFabricFriendlyName;
                    lblDRLocationNm.Text = databaseAzure.RecoveryFabricFriendlyName;

                    lblPRVMNm.Text = prrep.PublicIPAddress;//databaseAzure.FriendlyName;
                    lblDRVMNm.Text = drrep.PublicIPAddress;//databaseAzure.FriendlyName;
                }

                //if (infradetail.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                //{
                //    var prvmname = facade.GetAzureRepliId(infradetail.DRReplicationId);
                //    {
                //        if (prvmname.PublicIPAddress !="")
                //        {
                //            lblPRVMNm.Text = prvmname.PublicIPAddress;
                //        }
                //        else
                //        {
                //            lblPRVMNm.Text = "N/A";
                //        }
                        
                //        var databaseAzured = Facade.AzureMonitorStatusGetByInfraId(infradetail.Id);
                //        if (databaseAzured.DRIPAddress !="")
                //        {
                //            PRIPAddress.Text = databaseAzured.DRIPAddress;
                //        }
                //        else
                //        {
                //            PRIPAddress.Text = "N/A";
                //        }
                        
                //    }
                //    var drvmname = facade.GetAzureRepliId(infradetail.PRReplicationId);
                //    {
                //        if (drvmname.PublicIPAddress != "")
                //        {
                //            lblDRVMNm.Text = drvmname.PublicIPAddress;
                //        }
                //        else
                //        {
                //            lblDRVMNm.Text = "N/A";
                //        }
                //        var databaseAzured = Facade.AzureMonitorStatusGetByInfraId(infradetail.Id);
                //        if (databaseAzured.IPAddress !="")
                //        {
                //            DRIPAddress.Text = databaseAzured.IPAddress;
                //        }
                //        else
                //        {
                //            DRIPAddress.Text = "N/A";
                //        }
                       
                //    }

                //}

            }
            else
            {
                lblPRVMNm.Text = "N/A";
                lblDRVMNm.Text = "N/A";
                PRIPAddress.Text = "N/A";
                DRIPAddress.Text = "N/A";
            }

        }
    }
}