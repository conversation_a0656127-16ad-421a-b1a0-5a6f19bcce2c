using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using log4net;

namespace CP.UI.Controls
{
    public partial class AvamarReplicationList : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;
        private readonly ILog _logger = LogManager.GetLogger(typeof(AvamarReplication));
        public static string IPAddress = string.Empty;
        DropDownList _ddlReplicationType = new DropDownList();
        ReplicationType _type;

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }
        
        public bool IsSearch;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            BindData();
        }
        
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        private void BindData()
        {
            _type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            var result = GetAvamarReplicationListByReplicationType(_type.ToString());
            lvAvamarReplication.DataSource = result;
            lvAvamarReplication.DataBind();
            upnlAvamarReplication.Update();
        }

        public IList<AvamarReplication> GetAvamarReplicationListByReplicationType(string iType)
        {
            _logger.Info("GetAvamarReplicationListByReplicationType method started");

            var replicationList = Facade.GetAllAvamarReplications();

            if (replicationList != null)
            {
                var result = (from replication in replicationList
                              where replication.ReplicationBase.Type.ToString() == iType
                    select replication).ToList();

                return result;
            }
            return null;
        }


        public IList<AvamarReplication> GetAvamarReplicationList(string searchValue)
        {
            _type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);

            var list = GetAvamarReplicationListByReplicationType(_type.ToString());
 
            searchValue = searchValue.Trim();

            if (!String.IsNullOrEmpty(searchValue) || list != null)
            {
                var result = (from replication in list
                              where replication.ReplicationBase.Name.ToLower().Contains(searchValue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvAvamarReplication.Items.Clear();
                lvAvamarReplication.DataSource = GetAvamarReplicationList(txtsearchvalue.Text);
                lvAvamarReplication.DataBind();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void lvAvamarReplication_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageAvamarReplicationList"] = dataPager1.StartRowIndex;

            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = lvAvamarReplication.Items[e.NewEditIndex].FindControl("Id") as Label;

            if (lbl1 != null)
            {
                Session["AvamarReplicationReplicationId"] = lbl1.Text;

                var lblName = lvAvamarReplication.Items[e.NewEditIndex].FindControl("Rep_NAME") as Label;

                if (lblName != null)
                {
                    secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty,
                        Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                        Constants.UrlConstants.Params.ReplicationName, lblName.Text,
                        Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                }
            }

            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvAvamarReplication_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageAvamarReplicationList"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCountAzureList"] = dataPager1.TotalRowCount;
                var lblId = lvAvamarReplication.Items[e.ItemIndex].FindControl("Id") as Label;
                var lblName = (lvAvamarReplication.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;

                if (lblId != null)
                {
                    var infraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));
               
                    if (lblId.Text != null && ValidateRequest("AvamarReplication Delete", UserActionType.ReplicationList))
                    {
                        if (infraObjects != null && infraObjects.Count > 0)
                        {
                            ErrorSuccessNotifier.AddSuccessMessage("The AvamarReplication Replication component is in use.");

                            if (lblName != null)
                            {
                                ActivityLogger.AddLog1(LoggedInUserName, "AvamarReplication",
                                    UserActionType.DeleteReplicationComponent,
                                    "The AvamarReplication Replication component  '" + lblName.Text + "' is in use", LoggedInUserId,
                                    IPAddress);

                                _logger.Info("Replication component " + "'" + lblName.Text + "'" +
                                             "is in use.  User IP Address " + "'" + IPAddress + "'");
                            }
                        }
                        else
                        {

                            Facade.DeleteAvamarReplicationById(Convert.ToInt32(lblId.Text));

                            if (lblName != null)
                            {
                                ActivityLogger.AddLog(LoggedInUserName, "Azure",
                                    UserActionType.DeleteReplicationComponent,
                                    "The AvamarReplication Replication component '" + lblName.Text +
                                    "' was deleted from the replication component", LoggedInUserId);
                                _logger.Info("AvamarReplication" + " " + '"' + lblName.Text + '"' +
                                             "deleted successfully. With User IP Address " + "'" + IPAddress + "'");
                                ErrorSuccessNotifier.AddSuccessMessage(
                                    Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                        "AvamarReplication Replication Component" + " " + '"' + lblName.Text + '"',
                                        TransactionType.Delete));
                            }
                        }
                    }
                }
            }
            catch (CpException ex)
            {
                _logger.Error("CP exception while loading Recovery AvamarReplication SiteList in  lvAvamarReplication_ItemDeleting method on RecoveryAzureSiteList page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Inner Exception : " + ex.InnerException);
                if (ex.StackTrace != null)
                    _logger.Error("Exception details : " + ex.StackTrace);
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);

            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);

                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType,ReplicationType.SelectedValue);

                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvAvamarReplication_PreRender(object sender, EventArgs e)
        {
            IsSearch = string.IsNullOrEmpty(txtsearchvalue.Text);

            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                BindData();
            }
        }

        protected void lvAvamarReplication_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ImgEdit") as ImageButton;
            var delete = e.Item.FindControl("ImgDelete") as ImageButton;
            if (IsUserCustom)
            {
                edit.Enabled = false;
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.Where(x => x.AccessMenuType == AccessManagerType.View.ToString()).ToList();
                    if (ObjAccess == null)
                    {
                        edit.Enabled = false;
                        edit.ImageUrl = "../images/icons/pencil_disable.png";
                        delete.Enabled = false;
                        delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                    }
                    else if (ObjAccess != null)
                    {
                        foreach (var Submenu in ObjAccess)
                        {
                            var Edit = Submenu.AccessSubMenuType.ToString();
                            var deleted = Submenu.AccessSubMenuType.ToString();
                            if (Edit == "4")
                            {
                                edit.Enabled = true;
                                edit.ImageUrl = "../images/icons/pencil.png";
                            }
                            else if (deleted == "5")
                            {
                                delete.Enabled = true;
                                delete.ImageUrl = "../images/icons/cross-circle.png";
                            }
                            else
                            {
                                edit.Enabled = false;
                                edit.ImageUrl = "../images/icons/pencil_disable.png";
                                delete.Enabled = false;
                                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                            }
                        }
                    }
                }
                else
                {
                    edit.Enabled = true;
                    edit.ImageUrl = "../images/icons/pencil.png";
                    delete.Enabled = true;
                    delete.ImageUrl = "../images/icons/cross-circle.png";
                }
            }

            if (IsUserOperator || IsUserManager)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }


        protected bool ValidateRequest(string entity, UserActionType userActionType)
        {
            if ((Session["_token"] != null))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, userActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout logout = new Logout();
                    logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, userActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout logout = new Logout();
                logout.PrepareView();
                return false;
            }
            return true;
        }



    }
}
