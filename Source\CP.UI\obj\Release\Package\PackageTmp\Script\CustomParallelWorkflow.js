﻿function pageLoad() {
    var ind = $(".timeline li .fa.fa-spinner.fa-spin,.timeline li .glyphicons.remove, .timeline li .glyphicons.single.ok:last").attr("data-count");
    var liht = ind * 40;
    $("#scrolling_div").scrollTop(liht);
    
    $('dt').click(function () {
        theId = $(this).attr('id').toString();
        //localStorage.dtclickId = localStorage.dtclickId + "^" + theId;
        var IDdd = theId + "" + "EX";
        if ($("#" + IDdd).css('display') == 'none') {
            localStorage.dtclickId = localStorage.dtclickId + "^" + theId + "~" + "expand";
        }
        else {
            localStorage.dtclickId = localStorage.dtclickId + "^" + theId + "~" + "collapse";
        }
        localStorage.ExpandedValue = "";
        
    });



    $.timeliner({
        startOpen: ['#19550828EX', '#19630828EX']
    });
    $.timeliner({
        timelineContainer: '#timelineContainer_2'
    });
   
    $(".CBmodal").colorbox({ inline: true, initialWidth: 100, maxWidth: 682, initialHeight: 100, transition: "elastic", speed: 750 });
}