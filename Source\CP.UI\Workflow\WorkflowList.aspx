﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="WorkflowList.aspx.cs" Inherits="CP.UI.WorkflowList" %>

<%@ Import Namespace="CP.Helper" %>

<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
    <script src="../Script/EncryptDecrypt.js"></script>
    <script type="text/javascript">
        function ConfirmDelete(a) {
            alert(a)
            var x = confirm("Are you sure you want to delete?");
            if (x)
                return true;
            else
                return false;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
            <img src="../Images/worflow_new.png">
            Workflow List</h3>
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body fl_wb">
                <div class="row">
                    <%-- <div class="col-md-7">

                        <div class="serverlistmessage" id="CMDBMsg" runat="server" visible="false">
                            <span style="padding-right: 5px; color: #262626 !important">Note : </span>
                            <asp:Label ID="lblOut" runat="server" Text="" Style="font-size: 16px; font-weight: bold;"></asp:Label>
                            out of
                            <asp:Label ID="lblOutOf" runat="server" Text="" Style="font-size: 16px; font-weight: bold;"></asp:Label>
                            CMDB components have been imported successfully from
                        <asp:Label ID="lblExcelName" runat="server" Text="" Style="font-weight: bold;"></asp:Label>
                        </div>
                    </div>--%>

                    <div class="col-xs-5 col-md-push-7 text-right">
                        <asp:UpdatePanel ID="updtbtnsearch" runat="server" UpdateMode="Conditional">
                            <Triggers>
                                <asp:AsyncPostBackTrigger ControlID="btnSearch" />
                            </Triggers>

                            <ContentTemplate>
                                <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Workflow Name"></asp:TextBox>
                                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" OnClick="BtnSearchClick" />
                            </ContentTemplate>

                        </asp:UpdatePanel>
                    </div>
                </div>
                <hr />


                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                    <Triggers>
                        <asp:AsyncPostBackTrigger ControlID="btnSearch" />
                    </Triggers>
                    <ContentTemplate>
                        <asp:ListView ID="lvComponent" runat="server" OnPreRender="LvComponentPreRender" OnItemCommand="LvComponentItemCommand" OnItemDataBound="LvComponentItemDataBound"
                            OnPagePropertiesChanging="lvComponent_PagePropertiesChanging">
                            <LayoutTemplate>
                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                                    <thead>
                                        <tr>
                                            <th style="width: 5%;" class="text-center">
                                                <span>
                                                    <img src="../Images/worflow_new_white.png" /></span>
                                            </th>
                                            <th style="width: 16%;">Workflow Name
                                            </th>
                                            <th style="width: 16%;">BusinessService Name
                                            </th>
                                            <th style="width: 16%;">BusinessFunction Name
                                            </th>
                                            <th style="width: 16%;">InfraObject Name
                                            </th>
                                            <th style="width:18%;">Profile Name
                                            </th>
                                               <th style="width:15%;">ActionType
                                            </th>
                                            <th style="width: 15%;">Created By
                                            </th>
                                            <th style="width: 5%;" class="text-center">Status
                                            </th>
                                            <th style="width: 5%;" class="text-center">Action
                                            </th>

                                            <%-- 
                                            <th class="text-center" style="width: 8%;">Action
                                            </th>--%>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <EmptyDataTemplate>
                                <div class="message warning align-center bold no-bottom-margin">
                                    <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                                </div>
                            </EmptyDataTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td class="text-center" style="width: 5%;">
                                        <%#Container.DataItemIndex+1 %>
                                    </td>
                                    <asp:Label ID="ID" runat="server" Text='<%# Eval("id") %>' Visible="false" />
                                    <%-- <asp:Label ID="IsVerified" runat="server" Text='<%#Eval("IsVerified") %>' Visible="false" />
                                    <asp:Label ID="Status" runat="server" Text='<%#Eval("Status") %>' Visible="false" />--%>
                                    <td style="width: 20%;" class="tdword-wrap">
                                      <asp:Label ID="Workflowname" runat="server" Text='<%# Eval("WorkflowName") %>' ToolTip='<%# Eval("WorkflowName") %>'></asp:Label>
                                    </td>
                                    <td style="width: 20%;"" class="tdword-wrap">
                                        <asp:Label ID="Label1" Text='<%#  Eval("BusinessService") %>' ToolTip='<%# Eval("BusinessService") %>' runat="server"></asp:Label>

                                    </td>
                                    <td style="width: 20%;" class="tdword-wrap">
                                        <asp:Label ID="Label2" Text='<%#  Eval("BusinessFunction") %>' ToolTip='<%# Eval("BusinessFunction") %>' runat="server"></asp:Label>

                                    </td>
                                    <td style="width: 20%;" class="tdword-wrap">
                                        <asp:Label ID="Labelgroupname" Text='<%#  Eval("InfraobjectName") %> ' ToolTip='<%# Eval("InfraobjectName") %>' runat="server"></asp:Label>

                                    </td>
                                    <td style="width: 28%;"class="tdword-wrap">
                                        <asp:Label ID="lblprofile" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' runat="server"></asp:Label>

                                    </td>
                                        <td style="width: 28%;"class="tdword-wrap">
                                      <asp:Label ID="lblactiontype" Text='<%# ActionType(Eval("ActionType")) %>' ToolTip='<%# Eval("ActionType") %>' runat="server"></asp:Label>

                                    </td>

                                    <td style="width: 17%;" class="tdword-wrap">
                                        <asp:Label ID="lblcreatename" Text='<%# Eval("Username") %>' ToolTip='<%# Eval("Username") %>' runat="server"></asp:Label>

                                    </td>
                                    <td style="width: 5%;">
                                        <asp:Label ID="lblStatus" Text='<%# Status(Eval("IsLock")) %>' runat="server"></asp:Label>

                                    </td>                                   
                                     <td style="width: 5%;" class="text-center">
                                        <asp:ImageButton ID="Imagelock" runat="server" CommandName="Lock" ImageUrl='<%# Checklock(Eval("IsLock")) %>' ToolTip='<%# CheckToolTip(Eval("IsLock")) %>' Style="height: 20px; width: 20px"
                                            ValidationGroup="IDT" CausesValidation="false" />
                                        <asp:HiddenField ID="hdnIsActive" runat="server" Value='<%# Eval("IsLock")%>' />

                                        <asp:HiddenField ID="Hdnmsg" runat="server" />
                                    </td>


                                    <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText='<%# "Are you sure want to  "+ Eval("Lockname") + " " + Eval("WorkflowName") + " ? " %>' TargetControlID="Imagelock" OnClientCancel="CancelClick">
                                    </cc1:ConfirmButtonExtender>


                                    <%-- 
                                    <td class="text-center" style="width: 8%;">
                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" ToolTip="Edit" ImageUrl="../images/icons/pencil.png"
                                            ValidationGroup="IDT" CausesValidation="false" />
                                      
                                    </td>--%>
                                    <%--<cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText='<%# "Are you sure want to delete " + Eval("Name") + " ? " %>'
                                        TargetControlID="ImgDelete" OnClientCancel="CancelClick">
                                    </cc1:ConfirmButtonExtender>--%>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                        <div class="row">
                            <div class="col-md-6">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvComponent">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-md-6 text-right">
                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvComponent" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                            NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </div>
        </div>
    </div>
</asp:Content>
