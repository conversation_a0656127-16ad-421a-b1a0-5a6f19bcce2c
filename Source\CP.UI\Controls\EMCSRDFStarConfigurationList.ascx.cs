﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using log4net;
using System.Net;

namespace CP.UI.Controls
{
    public partial class EMCSRDFStarConfigurationList : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;
        DropDownList _ddlReplicationType = new DropDownList();
        ReplicationType type;
        private readonly ILog _logger = LogManager.GetLogger(typeof(EMCSRDFStarConfigurationList));
        public static string IPAddress = string.Empty;
        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST 
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void lvEMCSRDFStar_PreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }

        protected void lvEMCSRDFStar_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPagelvEMCSRDFStar"] = dataPager1.StartRowIndex;
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvEMCSRDFStar.Items[e.NewEditIndex].FindControl("Id")) as Label;

            var lblName = (lvEMCSRDFStar.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;
            ActivityLogger.AddLog(LoggedInUserName, "EMCSRDFStar", UserActionType.UpdateReplicationComponent,
                                      "The EMCSRDFStar Replication component '" + lblName.Text +
                                      "' Opened as Editing Mode", LoggedInUserId);

            if (lbl1 != null && lblName != null && ValidateRequest("EMCSRDFStar Edit", UserActionType.UpdateReplicationComponent))
            //if (lbl1 != null && lblName != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                 Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                //Helper.Url.Redirect(secureUrl);


                if (secureUrl != null)
                {
                    Helper.Url.Redirect(secureUrl);
                }
            }
        }

        protected void lvEMCSRDFStar_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPagelvEMCSRDFStar"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCountlvEMCSRDFStar"] = dataPager1.TotalRowCount;
                var lblId = lvEMCSRDFStar.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvEMCSRDFStar.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
                var InfraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));
                if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("EMCSRDFStar Delete", UserActionType.DeleteReplicationComponent))
                {
                    if (InfraObjects != null && InfraObjects.Count > 0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The EMCSRDFStar Replication component is in use.");
                        ActivityLogger.AddLog1(LoggedInUserName, "EMCSRDFStar", UserActionType.DeleteReplicationComponent, "The EMCSRDFStar Replication component  '" + lblName.Text + "' is in use", LoggedInUserId, IPAddress);

                        _logger.Info("Replication component " + "'" + lblName.Text + "'" + "is in use.  User IP Address " + "'" + IPAddress + "'");
                    }
                    else
                    {
                        Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog1(LoggedInUserName, "EMCSRDFStar", UserActionType.DeleteReplicationComponent,
                                              "The EMCSRDFStar Replication component '" + lblName.Text +
                                              "' was deleted from the replication component", LoggedInUserId, IPAddress);
                        _logger.Info("EMCSRDFStar" + " " + '"' + lblName.Text + '"' + "deleted successfully. With User IP Address " + "'" + IPAddress + "'");
                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "EMCSRDFStar Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));

                    }
                }

            }
            catch (CpException ex)
            {
                if (ex != null)
                {
                    _logger.Error("CP exception while loading EMCSRDFStarConfigurationList in  lvEMCSRDFStar_ItemDeleting method on EMCSRDFStarConfigurationList page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        _logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);

            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);

                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "74");

                Helper.Url.Redirect(secureUrl);

                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                //Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvEMCSRDFStar_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;
            if (IsUserCustom)
            {
                edit.Enabled = false;
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.Where(x => x.AccessMenuType == AccessManagerType.View.ToString()).ToList();
                    if (ObjAccess == null)
                    {
                        edit.Enabled = false;
                        edit.ImageUrl = "../images/icons/pencil_disable.png";
                        delete.Enabled = false;
                        delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                    }
                    else if (ObjAccess != null)
                    {
                        foreach (var Submenu in ObjAccess)
                        {
                            var Edit = Submenu.AccessSubMenuType.ToString();
                            var deleted = Submenu.AccessSubMenuType.ToString();
                            if (Edit == "4")
                            {
                                edit.Enabled = true;
                                edit.ImageUrl = "../images/icons/pencil.png";
                            }
                            else if (deleted == "5")
                            {
                                delete.Enabled = true;
                                delete.ImageUrl = "../images/icons/cross-circle.png";
                            }
                            else
                            {
                                edit.Enabled = false;
                                edit.ImageUrl = "../images/icons/pencil_disable.png";
                                delete.Enabled = false;
                                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                            }
                        }
                    }
                }
                else
                {
                    edit.Enabled = true;
                    edit.ImageUrl = "../images/icons/pencil.png";
                    delete.Enabled = true;
                    delete.ImageUrl = "../images/icons/cross-circle.png";
                }
            }

            if (IsUserOperator || IsUserManager)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }

        private void Binddata()
        {
            //setListViewPage();

            type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            IList<EMCSRDFStar> result = GetlvEMCSRDFStarReplicationType(type.ToString());
            if (result == null)
            {
                IList<EMCSRDFStar> val = new List<EMCSRDFStar>();
                lvEMCSRDFStar.DataSource = val;
                lvEMCSRDFStar.DataBind();
            }
            else
            {
                lvEMCSRDFStar.DataSource = result;
                lvEMCSRDFStar.DataBind();
            }

        }

        private IList<EMCSRDFStar> GetlvEMCSRDFStarReplicationType(string iType)
        {
            var replicationlist = Facade.EMCSRDFStar_GetAllByReplicationType(iType);

            if (replicationlist != null)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.reptype == iType
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public IList<EMCSRDFStar> GetEMCSRDFStarList(string searchvalue)
        {
            var replicationlist = GetlvEMCSRDFStarReplicationType(Session["ReplicationType"].ToString());
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count > 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvEMCSRDFStar.Items.Clear();
                lvEMCSRDFStar.DataSource = GetEMCSRDFStarList(txtsearchvalue.Text);
                lvEMCSRDFStar.DataBind();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
    }
}