﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" EnableViewStateMac="false"
    CodeBehind="BusinessService_DashboardDetails.aspx.cs" Inherits="CP.UI.Admin.BusinessService_DashboardDetails" %>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <style>
        .dynamicTable th, .dynamicTable tbody > tr > td {
            font-size: 15px !important;
        }

            .dynamicTable tbody > tr > td a {
                color: #05589e;
            }

        hr {
            margin: 10px 0;
        }

        .morecontent {
            display: none;
            cursor: pointer;
        }

        a.morelink {
            color: #00bbf0;
            text-decoration: underline;
        }

        .more {
            font-size: 14px;
        }

        .wrap {
            width: 88%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
        }
    </style>

    <script src="../Script/jquery-ui.js"></script>
    <script src="../Script/jquery-ui.min.js"></script>

    <script type="text/javascript">
        function Redirectmonitor(id) {
            $('#<%= hndInfraId.ClientID %>').val(id);
            $('#<%= btnDummy.ClientID %>').click();
        }
    </script>
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">

        <h3><span class="business-setting-icon vertical-sub"></span>
            <b>Business Service Overview</b></h3>

        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-xs-5 col-md-push-7 text-right">

                        <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Business Service"></asp:TextBox>
                        <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" OnClick="btnSearch_Click" />
                    </div>
                </div>
                <hr />
                <asp:UpdatePanel ID="updmain" runat="server" UpdateMode="Conditional">
                    <Triggers>
                        <asp:AsyncPostBackTrigger ControlID="UpdateTimer" EventName="Tick" />
                    </Triggers>
                    <ContentTemplate>
                        <asp:ListView ID="lvBusinessService" DataKeyNames="Id" runat="server" OnPreRender="lvBusinessService_PreRender" OnItemDataBound="lvBusinessService_ItemDataBound">
                            <LayoutTemplate>
                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                                    <thead>
                                        <tr>
                                            <th class="text-center" style="width: 5%;">
                                                <span class="glyphicons cogwheel">
                                                    <i></i>
                                                </span>
                                            </th>
                                            <th style="width: 21%;"><i class="cogwheel_icon-white"></i>Business Service Name
                                            </th>
                                            <th style="width: 12%;"><i class="config-rpo-icon"></i>Configured RPO 
                                            </th>
                                            <th style="width: 12%;"><i class="config-rpo-icon"></i>Computed RPO 
                                            </th>

                                            <%-- <th style="width: 12%;"><i class="config-rpo-icon"></i> RTO 
                                            </th>
                                            <th style="width: 12%;"><i class="config-rpo-icon"></i> Configured RTO 
                                            </th>--%>
                                            <th style="width: 40%;"><i class="infreObject-icon"></i>InfraObjects
                                            </th>
                                            <th style="width: 10%;"><i class="health-icon"></i>DR Health
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <EmptyDataTemplate>
                                <div class="">
                                    <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                                </div>
                            </EmptyDataTemplate>

                            <ItemTemplate>
                                <tr>
                                    <td class="th table-check-cell text-center" style="width: 5%;">
                                        <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td class="tdword-wrap" style="width: 21%;">
                                        <%--  <asp:Label ID="lblBusinessServiceName" runat="server" Text='<%# Eval("Name") %>' />--%>
                                        <asp:LinkButton ID="lblBusinessServiceName" runat="server" Text=' <%#Eval("Name")%>' OnClick="lblBusinessServiceName_Click"></asp:LinkButton>
                                    </td>
                                    <td class="tdword-wrap" style="width: 12%;">
                                        <asp:Label ID="lblConfiguredRpoIcon" runat="server" CssClass="clock-blue"></asp:Label>
                                        <asp:Label ID="lblConfiguredRpo" runat="server" Text='<%# ConvertSecondToString(Eval("ConfiguredRpo")) %>'></asp:Label>
                                    </td>
                                    <td class="tdword-wrap" style="width: 12%;">
                                        <asp:Label ID="lblActualRpoIcon" runat="server"></asp:Label>
                                        <asp:Label ID="lblActualRpo" runat="server" Text='<%# ConvertSecondToString(Eval("ActualRpo")) %>'></asp:Label>
                                    </td>
                                    <td class="tdword-wrap" style="width: 12%;" runat="server" visible="false">
                                        <asp:Label ID="lblActualRtoIcon" runat="server" CssClass="clock-blue"></asp:Label>
                                        <asp:Label ID="lblActualRto" runat="server" Text='<%# ConvertSecondToString(Eval("ActualRto"))%>'></asp:Label>
                                    </td>
                                    <td class="tdword-wrap" style="width: 12%;" runat="server" visible="false">
                                        <asp:Label ID="lblConfiguredRtoIcon" runat="server" CssClass="clock-blue"></asp:Label>
                                        <asp:Label ID="lblConfiguredRto" runat="server" Text='<%# ConvertSecondToString(Eval("ConfiguredRto"))%>'></asp:Label>
                                    </td>
                                    <td style="width: 40%;" runat="server" id="tdinfraname">
                                        <asp:Label ID="lblmore" runat="server" CssClass="wrap"></asp:Label>
                                        <a class="morecontent"><span>more</span></a>
                                        <asp:LinkButton ID="lblinfraId" runat="server" CssClass="more" Text='<%#Eval("InfraObjectID")%>' Visible="false"></asp:LinkButton>
                                        <asp:LinkButton ID="lblInfra" runat="server" CssClass="more" Text='<%#Eval("InfraObjectName")%>' Visible="false"></asp:LinkButton>

                                    </td>
                                    <td style="width: 10%;">
                                        <asp:Label ID="lblHealthIcon" runat="server" CssClass=""></asp:Label>
                                        <asp:Label ID="lblHelth" runat="server" Text='<%#Eval("DRHealth")%>'></asp:Label>
                                    </td>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                    </ContentTemplate>
                </asp:UpdatePanel>
                <asp:Timer runat="server" ID="UpdateTimer" Interval="300000" OnTick="UpdateTimer_Tick" Enabled="false"/>
                <div class="row">
                    <div class="col-xs-6">
                        <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvBusinessService">
                            <Fields>
                                <asp:TemplatePagerField>
                                    <PagerTemplate>
                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                        Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                        <br />
                                    </PagerTemplate>
                                </asp:TemplatePagerField>
                            </Fields>
                        </asp:DataPager>
                    </div>
                    <div class="col-xs-6 text-right">
                        <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvBusinessService" PageSize="10">
                            <Fields>
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                    ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                    NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                    NumericButtonCssClass="btn-pagination" />
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                    ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                            </Fields>
                        </asp:DataPager>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <asp:Button Style="display: none" ID="btnDummy" OnClick="btnDummy_Click" runat="server" />
    <asp:HiddenField Value="" runat="server" ID="hndInfraId" />
    <script>
        $(function () {
            //compactText();
        });
        function compactText() {
            var showChar = 55;

            $('.wrap').each(function () {
                var content = $(this).text();
                var $this = $(this);
                if (content.length > showChar) {
                    $this.next().show();
                }
            });
            $('.morecontent span').click(function () {
                if ($(this).text() == "more") {
                    $(this).text("less");
                    $(this).parent().prev().removeClass("wrap");
                }
                else {
                    $(this).text("more");
                    $(this).parent().prev().addClass("wrap");
                }
            });
        }

        function pageLoad() {
            // compactText();
        }
    </script>
</asp:Content>
