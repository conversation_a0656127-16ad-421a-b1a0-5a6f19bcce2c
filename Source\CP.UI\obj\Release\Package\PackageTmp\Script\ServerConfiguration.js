﻿$(document).ready(function (e) {
    $('#spnRunning').hide();
    $('[id$=btnTestConnection]').live("click", function () {
        $('#spnRunning').show();
        $('#spnValidateConnection').html("");
        var ipaddress = $("[id$=txtIPAddress]").val();
        var sshusername = $("[id$=txtSSHUser]").val();
        var sshpassword = $("[id$=txtSSHPassword]").val();
        //alert("ipaddress:" + ipaddress + " sshusername:" + sshusername + " sshpassword:" + sshpassword);
        $.ajax({
            type: "POST",
            url: "ServerConfiguration.aspx/CheckValidation_Credetials",
            data: "{'ipaddress':'" + ipaddress + "','sshuser':'" + sshusername + "','sshpassword':'" + sshpassword + "'}",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: true,
            success: function(data) {
                OnServerSuccess(data);
            }
        });
    });

    function OnServerSuccess(data) {
        var result = data.d.split(':');
        var IsConnected = result[0];
        //alert("IsConneced:" + IsConnected);
        var message = result[1];
        //alert("message:" + message);
            if (IsConnected=="True") {
                $('#spnRunning').hide();
             
                $('#spnValidateConnection').css("font-size", "90%");
                $('#spnValidateConnection').css("color", "green");
                $('#spnValidateConnection').html("Connection is Valid");

        }
        else {
            $('#spnRunning').hide();
            $('#spnValidateConnection').css("font-size", "90%");
            $('#spnValidateConnection').css("color", "red");
            $('#spnValidateConnection').html(message);
        }
    }
});

function clearText(control) {
    if (control.value != "") {
        control.value = getOrignalData(control, $('#ctl00_cphBody_hdfStaticGuid').val());
    }
}
function getHashData(control) {
    control.value = genrateUserNameHash(control, $('#ctl00_cphBody_hdfStaticGuid').val());
}

function pageLoad() {

    $(window).bind("capsOn", function (event) {
        if ($(".chk-caps:focus").length > 0) {
            $(".chk-caps").nextAll(".caps-error").show();
        }
    });
    $(window).bind("capsOff capsUnknown", function (event) {
        $(".chk-caps").nextAll(".caps-error").hide();
    });
    $(".chk-caps").bind("focusout", function (event) {
        $(".chk-caps").nextAll(".caps-error").hide();
    });
    $(".chk-caps").bind("focusin", function (event) {
        if ($(window).capslockstate("state") === true) {
            $(".chk-caps").nextAll(".caps-error").show();
        }
    });
    $(window).capslockstate();

    $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });

    //Add Custom scroll for Substitude Aunth-List and also removing on condition
    if ($(".nicescroll-rails").length > 0) {
        $(".nicescroll-rails").remove();
    }
    $("#subanunth-content").niceScroll({ touchbehavior: true, cursordragontouch: true, cursorcolor: "#4a8bc2", cursoropacitymax: 1, cursorwidth: 8, autohidemode: false, horizrailenabled: false });

    //Added code for Custom dropdown onclick changing the css 
    $("button[id$=ddlAuthType],button[id$=ddlEditAuthType]").on("click", function () {
        if ($(this).parent().hasClass("open")) {
            $(this).parent().removeAttr("style");
        } else {
            $(this).parent().css({ "width": "17.6%", "position": "absolute" });
        }
    });
    $(".dropdown-menu ul li a").on("click", function () {
        if ($(this).parents(".bootstrap-select").hasClass("open")) {
            $(this).parents(".bootstrap-select").removeAttr("style");
        } else {
            $(this).parents(".bootstrap-select").css({ "width": "17.6%", "position": "absolute" });
        }
    });
};
