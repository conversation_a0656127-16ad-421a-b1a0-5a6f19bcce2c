﻿using CP.Common.Base;
using System;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ReplicationAzureCosmosDB", Namespace = "http://www.CP.com/types")]
    public class ReplicationAzureCosmosDB : BaseEntity
    {
        #region Member Variables

        public ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables
        #region Properties

        [DataMember]
        public int Id
        {
            get;
            set;
        }

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }
        [DataMember]
        public string AzureSubscriptionName
        {
            get;
            set;
        }

        [DataMember]
        public string AccountName
        {
            get;
            set;
        }

        [DataMember]
        public string ResourceGroup
        {
            get;
            set;
        }

        [DataMember]
        public string ClientId
        {
            get;
            set;
        }

        [DataMember]
        public string ClientSecret
        {
            get;
            set;
        }

        [DataMember]
        public string SubscriptionId
        {
            get;
            set;
        }

        [DataMember]
        public string TenantId
        {
            get;
            set;
        }
        //TenantId

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }
        #endregion

        #region Constructor

        public ReplicationAzureCosmosDB() : base()
        {
        }

        #endregion
    }
}
