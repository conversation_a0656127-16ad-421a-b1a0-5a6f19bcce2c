﻿using CP.BusinessFacade;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;

using CP.Common.BusinessEntity;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.UI.Controls;
using CP.Helper;
using Telerik.Web.UI;
using System.Collections;



namespace CP.UI.ImpactAnalysis
{
    public partial class BussinessFunctionBIADynamic : BussinessFunctionBIADynamicBasePageEditor
    {

        #region Varrible

        #region Commom variables

        private static IFacade facade = new Facade();
        private static int _currentLoginUserId;


        private static string _currentLoggedUserName;
        private static int _companyId;
        private static bool _isUserSuperAdmin;
        private static bool _isParent;
        private static String _IncidentRecord = string.Empty;
        private static int _IncidentRecordId = 0;
        private DataTable table;

        private static int _businessFunctionIDId;
        private static string _businessImpactID;
        private static string _FIAProfileId;
        private static int _bfProfileId;
        private static List<string> BusinessFunctionList = new List<string>();
        private static bool _isTemplateEditable;

        public override string MessageInitials
        {
            get { return "FIA Template"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.ImpactAnalysis.BusinessFunctionBIA;
                }
                return string.Empty;
            }
        }

        #endregion


        #endregion

        #region Properties

        #endregion

        #region Methods


        private void GetBIAProfileDetails_ForMatrix(int iBFID, int iProfileID)
        {

            try
            {
                int ImpactID = 0;
                DataTable dt = new DataTable();

                dt.Columns.Add("ProfileID");
                dt.Columns.Add("ImpactID");
                dt.Columns.Add("ImpactName");
                dt.Columns.Add("ImpactTypeID", typeof(int));
                dt.Columns.Add("ImpactTypeName");
                dt.Columns.Add("TimeIntervalID", typeof(int));
                dt.Columns.Add("TimeInterval");
                dt.Columns.Add("Cost");
                dt.Columns.Add("ImpactSeverityID");

                //ImpactID = !string.IsNullOrEmpty(BusniessIMpactId) ?Convert.ToInt32(BusniessIMpactId) : 0;

                IList<BusinessProfile> objBIAProfileMatrix = Facade.GetBIAProfileDetails_BIAMatrixByPID(iBFID, iProfileID);

                foreach (BusinessProfile objBuss in objBIAProfileMatrix)
                {
                    dt.Rows.Add(objBuss.Id, objBuss.ImpactID, objBuss.ImpactName, objBuss.ImpactTypeID, objBuss.ImpactTypeName,
                                objBuss.TimeIntervalID, objBuss.TimeIntervalText, objBuss.Cost, objBuss.ImpactSeverityID);
                }

                ViewState["BIAProfile"] = dt;

            }
            catch (Exception)
            { }
        }


        private DataTable GetBIAProfile()
        {
            DataTable dt = new DataTable();


            dt = (DataTable)ViewState["BIAProfile"];


            //dt.Columns.Add("ProfileID");
            //dt.Columns.Add("ImpactID");
            //dt.Columns.Add("ImpactName");
            //dt.Columns.Add("ImpactTypeID");
            //dt.Columns.Add("ImpactTypeName");
            //dt.Columns.Add("TimeIntervalID");
            //dt.Columns.Add("TimeInterval");
            //dt.Columns.Add("Cost");




            //dt.Rows.Add("1", "2", "Competitive advantage", "3", "Financial Impact", "1", "0 to 2Hours", "10");
            //dt.Rows.Add("1", "3", "Penalties /Claims", "3", "Financial Impact", "1", "0 to 2Hours", "20");
            //dt.Rows.Add("1", "4", "Productivity Loss", "3", "Financial Impact", "1", "0 to 2Hours", "30");
            //dt.Rows.Add("1", "5", "Employee Morale", "4", "Operation Impact", "1", "0 to 2Hours", "40");
            //dt.Rows.Add("1", "6", "Service Availability", "4", "Operation Impact", "1", "0 to 2Hours", "50");

            //dt.Rows.Add("1", "2", "Competitive advantage", "3", "Financial Impact", "2", "2 to 4Hours", "20");
            //dt.Rows.Add("1", "3", "Penalties /Claims", "3", "Financial Impact", "2", "2 to 4Hours", "30");
            //dt.Rows.Add("1", "4", "Productivity Loss", "3", "Financial Impact", "2", "2 to 4Hours", "40");
            //dt.Rows.Add("1", "5", "Employee Morale", "4", "Operation Impact", "2", "2 to 4Hours", "50");
            //dt.Rows.Add("1", "6", "Service Availability", "4", "Operation Impact", "2", "2 to 4Hours", "60");

            //dt.Rows.Add("1", "2", "Competitive advantage", "3", "Financial Impact", "3", "4 to 8Hours", "30");
            //dt.Rows.Add("1", "3", "Penalties /Claims", "3", "Financial Impact", "3", "4 to 8Hours", "40");
            //dt.Rows.Add("1", "4", "Productivity Loss", "3", "Financial Impact", "3", "4 to 8Hours", "50");
            //dt.Rows.Add("1", "5", "Employee Morale", "4", "Operation Impact", "3", "4 to 8Hours", "60");
            //dt.Rows.Add("1", "6", "Service Availability", "4", "Operation Impact", "3", "4 to 8Hours", "70");

            //dt.Rows.Add("1", "2", "Competitive advantage", "3", "Financial Impact", "4", "8 to 12Hours", "40");
            //dt.Rows.Add("1", "3", "Penalties /Claims", "3", "Financial Impact", "4", "8 to 12Hours", "50");
            //dt.Rows.Add("1", "4", "Productivity Loss", "3", "Financial Impact", "4", "8 to 12Hours", "60");
            //dt.Rows.Add("1", "5", "Employee Morale", "4", "Operation Impact", "4", "8 to 12Hours", "70");
            //dt.Rows.Add("1", "6", "Service Availability", "4", "Operation Impact", "4", "8 to 12Hours", "80");

            //dt.Rows.Add("1", "2", "Competitive advantage", "3", "Financial Impact", "5", "12 to 24Hours", "50");
            //dt.Rows.Add("1", "3", "Penalties /Claims", "3", "Financial Impact", "5", "12 to 24Hours", "60");
            //dt.Rows.Add("1", "4", "Productivity Loss", "3", "Financial Impact", "5", "12 to 24Hours", "70");
            //dt.Rows.Add("1", "5", "Employee Morale", "4", "Operation Impact", "5", "12 to 24Hours", "80");
            //dt.Rows.Add("1", "6", "Service Availability", "4", "Operation Impact", "5", "12 to 24Hours", "90");

            //dt.Rows.Add("1", "2", "Competitive advantage", "3", "Financial Impact", "6", "24 to 48Hours", "60");
            //dt.Rows.Add("1", "3", "Penalties /Claims", "3", "Financial Impact", "6", "24 to 48Hours", "70");
            //dt.Rows.Add("1", "4", "Productivity Loss", "3", "Financial Impact", "6", "24 to 48Hours", "80");
            //dt.Rows.Add("1", "5", "Employee Morale", "4", "Operation Impact", "6", "24 to 48Hours", "90");
            //dt.Rows.Add("1", "6", "Service Availability", "4", "Operation Impact", "6", "24 to 48Hours", "100");

            return dt;
        }


        private void GenerateDataGridView()
        {
            // //this.rgvMatrix.DataSource = null;
            this.rgvMatrix.DataBind();

            rgvMatrix.MasterTableView.Columns.Clear();
            udplBussinessfunctionBIA.Update();

            DataTable dt = GetBIAProfile();

            DataTable dtHeader = dt.DefaultView.ToTable(true, new string[] { "TimeIntervalID", "TimeInterval" });

            DataTable dtImpacts = dt.DefaultView.ToTable(true, new string[] { "ImpactTypeID", "ImpactTypeName", "ImpactID", "ImpactName" });

            GridBoundColumn gridBoundColumn = new GridBoundColumn();
            gridBoundColumn.DataField = "ImpactName";
            gridBoundColumn.HeaderText = "Impact";
            gridBoundColumn.FooterText = "Total";
            // gridBoundColumn.FooterStyle.HorizontalAlign = HorizontalAlign.Right;

            rgvMatrix.MasterTableView.Columns.Add(gridBoundColumn);
            int ColumnCount = 0;

            foreach (DataRow drHeader in dtHeader.Rows)
            {
                string strColumnName = drHeader["TimeInterval"].ToString();
                string strColumnID = drHeader["TimeIntervalID"].ToString();

                if (string.IsNullOrEmpty(strColumnName)) continue;
                ColumnCount += 1;
                GridTemplateColumn templateColumn = new GridTemplateColumn();
                templateColumn.ItemTemplate = new BIAMatrixColumnTemplate(strColumnName, ColumnCount, Convert.ToInt32(strColumnID));
                //  templateColumn.DataField = "Cost";
                templateColumn.HeaderText = strColumnName;
                templateColumn.FooterTemplate = new BIAMatrixFooterTemplate(ColumnCount.ToString());
                //  templateColumn.Groupable = true;
                //  templateColumn.Aggregate = GridAggregateFunction.Sum;

                rgvMatrix.MasterTableView.Columns.Add(templateColumn);
            }

            GridGroupByExpression exp = new GridGroupByExpression();
            GridGroupByField field1 = new GridGroupByField();

            field1.FieldName = "ImpactTypeName";

            exp.GroupByFields.Add(field1);
            exp.SelectFields.Add(field1);

            rgvMatrix.MasterTableView.GroupByExpressions.Add(exp);

            //   rgvMatrix.MasterTableView.DataKeyNames = new string[]{"ImpactTypeID","ImpactID"};

            rgvMatrix.DataSource = dtImpacts;
            rgvMatrix.DataBind();
        }


        private void AssignDataToMatrix()
        {
            int iColCount = this.rgvMatrix.Columns.Count;

            for (int iIndex = 1; iIndex < iColCount; iIndex++)
            {

                foreach (GridItem objItem in rgvMatrix.Items)
                {
                    if (objItem.ItemType == GridItemType.Item || objItem.ItemType == GridItemType.AlternatingItem)
                    {

                        Label lblImpactID = objItem.FindControl("lblImpactID_" + iIndex) as Label;
                        Label lblRTOID = objItem.FindControl("lblRTOID_" + iIndex.ToString()) as Label;

                        RadNumericTextBox txtCost = objItem.FindControl("txt_" + iIndex.ToString()) as RadNumericTextBox;

                        if (lblImpactID != null && lblRTOID != null)
                        {
                            txtCost.Text = GetRTOData(Convert.ToInt32(lblImpactID.Text), Convert.ToInt32(lblRTOID.Text));
                        }
                    }
                }
            }
        }


        private string GetRTOData(int ImpactID, int RTOID)
        {
            DataTable dt = GetBIAProfile();

            int iSeverityID = Convert.ToInt32(ddlImactSeverityType.SelectedValue);

            DataRow[] drAll = dt.Select("ImpactID = '" + ImpactID + "' And TimeIntervalID = '" + RTOID + "'  And ImpactSeverityID = '" + iSeverityID + "'");
            string Cost = "0";
            foreach (DataRow dr in drAll)
            {
                Cost = dr["Cost"].ToString();
            }

            return Cost;

        }


        private void GetSumInFooterNEW()
        {

            double Total = 0;
            int iColCount = this.rgvMatrix.MasterTableView.Columns.Count;

            for (int iIndex = 1; iIndex < iColCount; iIndex++)
            {

                foreach (GridItem objItem in rgvMatrix.Items)
                {
                    if (objItem.ItemType == GridItemType.Item || objItem.ItemType == GridItemType.AlternatingItem)
                    {
                        RadNumericTextBox txtCost = objItem.FindControl("txt_" + iIndex.ToString()) as RadNumericTextBox;

                        if (txtCost != null)
                        {
                            Total += Convert.ToDouble(string.IsNullOrEmpty(txtCost.Text) ? "0" : txtCost.Text);
                        }
                    }
                }

                GridItem[] FooterItem = rgvMatrix.MasterTableView.GetItems(GridItemType.Footer);

                if (FooterItem.Length > 0)
                {

                    RadNumericTextBox txtFooterCostNEW = FooterItem[0].FindControl("txtFooter_" + iIndex) as RadNumericTextBox;
                    if (txtFooterCostNEW != null)
                    {
                        txtFooterCostNEW.Text = Total.ToString();
                    }
                }

                Total = 0;
            }

            udplBussinessfunctionBIA.Update();
        }


        private void GetUpdateValidatorControl()
        {

            int iColCount = this.rgvMatrix.Columns.Count;

            for (int iIndex = 1; iIndex < iColCount; iIndex++)
            {

                foreach (GridItem objItem in rgvMatrix.Items)
                {
                    if (objItem.ItemType == GridItemType.Item || objItem.ItemType == GridItemType.AlternatingItem)
                    {
                        //CompareValidator cmpvalCostPre = objItem.FindControl("cmpval_" + (iIndex -1).ToString()) as CompareValidator;
                        RadNumericTextBox txtCost = objItem.FindControl("txt_" + (iIndex - 1).ToString()) as RadNumericTextBox;
                        CompareValidator cmpvalCost = objItem.FindControl("cmpval_" + iIndex.ToString()) as CompareValidator;


                        if (cmpvalCost != null)
                        {
                            if (iIndex == 1)
                            {
                                // RadNumericTextBox txtCost1 = objItem.FindControl("txt_" + (iIndex).ToString()) as RadNumericTextBox;
                                cmpvalCost.Enabled = false;
                                //cmpvalCost.Visible = false;
                                // cmpvalCost.ValueToCompare = txtCost1.Text;
                                //  cmpvalCost.Operator = ValidationCompareOperator.GreaterThanEqual;
                            }
                            else
                            {

                                // cmpvalCost.Type = ValidationDataType.Double;
                                cmpvalCost.ControlToCompare = txtCost.ID;
                                cmpvalCost.ValueToCompare = txtCost.Text;
                                cmpvalCost.Operator = ValidationCompareOperator.GreaterThanEqual;





                            }
                        }
                    }
                }


            }

            Page.Validate();
            //  udplBussinessfunctionBIA.Update();
        }


        private void PopulateImpactSeverityTypes()
        {
            Utility.PopulateImpactRelType(ddlImactSeverityType, false);
        }


        private void GetDataFromGridToCollection()
        {
            try
            {
                int iColCount = Convert.ToInt32(ViewState["ColumnCount"].ToString());
                int iSeverityID = Convert.ToInt32(ddlImactSeverityType.SelectedValue);
                bool res = facade.DeleteBIAMatrixByBFId(_businessFunctionIDId, iSeverityID, Convert.ToInt32(ddlBusinessBIAProfile.SelectedValue));

                foreach (GridItem objItem in rgvMatrix.Items)
                {
                    for (int iIndex = 1; iIndex < iColCount; iIndex++)
                    {
                        if (objItem.ItemType == GridItemType.Item || objItem.ItemType == GridItemType.AlternatingItem)
                        {
                            RadNumericTextBox txtCost = objItem.FindControl("txt_" + iIndex.ToString()) as RadNumericTextBox;
                            Label lblImpactID = objItem.FindControl("lblImpactID_" + iIndex.ToString()) as Label;
                            Label lblImpactTypeID = objItem.FindControl("lblImpactCategoryID_" + iIndex.ToString()) as Label;
                            Label lblRTOID = objItem.FindControl("lblRTOID_" + iIndex.ToString()) as Label;

                            if (txtCost != null && iIndex == 1)
                            {
                                BFBIAMatrix objBFBIAMatrix = new BFBIAMatrix();

                                objBFBIAMatrix.BusinessFunctionID = _businessFunctionIDId;
                                objBFBIAMatrix.ImpactSeverityID = iSeverityID;
                                objBFBIAMatrix.ImpactTypeID = Convert.ToInt32(lblImpactTypeID.Text);
                                objBFBIAMatrix.ImpactID = Convert.ToInt32(lblImpactID.Text);
                                objBFBIAMatrix.UpdatorId = LoggedInUserId;
                                objBFBIAMatrix.CreatorId = LoggedInUserId;
                                objBFBIAMatrix.ProfileID = Convert.ToInt32(ddlBusinessBIAProfile.SelectedValue);
                                objBFBIAMatrix = facade.AddBFBIAMatrix(objBFBIAMatrix);

                                ViewState["BFBIAMatrixID"] = objBFBIAMatrix.Id;
                            }

                            BFBIAMatrixDetails objBFBIAMatrixDetails = new BFBIAMatrixDetails();
                            objBFBIAMatrixDetails.BFMatrixID = Convert.ToInt32(ViewState["BFBIAMatrixID"].ToString());
                            objBFBIAMatrixDetails.Cost = Convert.ToDouble(txtCost.Text);
                            objBFBIAMatrixDetails.TimeIntervalID = Convert.ToInt32(lblRTOID.Text);
                            objBFBIAMatrixDetails = facade.AddBFBIAMatrixDetails(objBFBIAMatrixDetails);

                        }
                    }
                }


                lblNotify.Visible = true;
                lblNotify.Text = "FIA Template Saved Successfully.";
                //btnSaveProfile.Text = "Save";
                Panel2.Visible = true;
                UpdatePanel2.Update();

            }
            catch (Exception)
            {
                throw;
            }
        }


        public void PopulateDropdown_BusinessProfile(){
        
            IList<BusinessProfile> businessProfileList = new List<BusinessProfile>(); 

             businessProfileList = Facade.GetAllBusinessProfile();
            if (!IsSuperAdmin && businessProfileList != null)
            {
                businessProfileList = facade.GetAllBusinessProfileByCompanyIdAndRole(LoggedInUserCompanyId, LoggedInUserCompany.IsParent);
            }      
            

            if (businessProfileList != null && businessProfileList.Count > 0)
            {
                ddlBusinessBIAProfile.DataSource = businessProfileList;
                ddlBusinessBIAProfile.DataTextField = "ProfileName";
                ddlBusinessBIAProfile.DataValueField = "Id";
                ddlBusinessBIAProfile.DataBind();
                DropDownListExtension.AddDefaultItem(ddlBusinessBIAProfile, "-Select FIA Templates-");
            }
        }


        #endregion


        #region Matrix_TemplateClasses

        private class BIAMatrixColumnTemplate : ITemplate
        {
            protected RadNumericTextBox radtxtCost;
            protected Label lblImpactCategoryID;
            protected Label lblImpactID;
            protected Label lblRTOID;
            protected CompareValidator cmpvalCost;
            protected RequiredFieldValidator revvalCost;



            string ColumnName;
            int ColumnCount;
            int IntervalID;

            public BIAMatrixColumnTemplate(string strColumnName, int iColumnCount, int iImpactID)
            {
                ColumnName = strColumnName;
                ColumnCount = iColumnCount;
                IntervalID = iImpactID;
            }

            public void InstantiateIn(System.Web.UI.Control container)
            {
                lblImpactCategoryID = new Label();
                lblImpactCategoryID.ID = "lblImpactCategoryID_" + ColumnCount;
                lblImpactCategoryID.DataBinding += new EventHandler(lblImpactCategoryID_DataBinding);
                lblImpactCategoryID.Visible = false;

                container.Controls.Add(lblImpactCategoryID);

                lblImpactID = new Label();
                lblImpactID.ID = "lblImpactID_" + ColumnCount;
                lblImpactID.DataBinding += new EventHandler(lblImpactID_DataBinding);
                lblImpactID.Visible = false;

                container.Controls.Add(lblImpactID);

                lblRTOID = new Label();
                lblRTOID.ID = "lblRTOID_" + ColumnCount.ToString();
                lblRTOID.DataBinding += new EventHandler(lblRtoID_DataBinding);
                lblRTOID.Visible = false;

                container.Controls.Add(lblRTOID);

                radtxtCost = new RadNumericTextBox();
                radtxtCost.AutoPostBack = true;
                radtxtCost.MinValue = 0;
                radtxtCost.IncrementSettings.InterceptMouseWheel = false;
                radtxtCost.NumberFormat.DecimalSeparator = ".";
                radtxtCost.NumberFormat.DecimalDigits = 2;

                radtxtCost.ID = "txt_" + ColumnCount.ToString();
                radtxtCost.TextChanged += new EventHandler(txtUpTo2hrs_TextChanged);
                //   radtxtCost.DataBinding += new EventHandler(Radtxt_DataBinding);
                container.Controls.Add(radtxtCost);

                cmpvalCost = new CompareValidator();
                cmpvalCost.Type = ValidationDataType.Double;
                cmpvalCost.ErrorMessage = "**";
                cmpvalCost.ForeColor = System.Drawing.Color.Red;
                cmpvalCost.ControlToValidate = radtxtCost.ID;
                cmpvalCost.ID = "cmpval_" + ColumnCount.ToString();
                container.Controls.Add(cmpvalCost);

                revvalCost = new RequiredFieldValidator();
                revvalCost.ErrorMessage = "*";
                revvalCost.ForeColor = System.Drawing.Color.Red;
                revvalCost.ControlToValidate = radtxtCost.ID;
                revvalCost.ID = "revval_" + ColumnCount.ToString();
                container.Controls.Add(revvalCost);


            }

            public void txtUpTo2hrs_TextChanged(object sender, EventArgs e)
            {


            }


            void Radtxt_DataBinding(object sender, EventArgs e)
            {
                RadNumericTextBox txtdata = (RadNumericTextBox)sender;
                GridDataItem container = (GridDataItem)txtdata.NamingContainer;

                Label lblImpactID = container.FindControl("lblImpactID_" + ColumnName) as Label;
                Label lblRTOID = container.FindControl("lblRTOID_" + ColumnCount.ToString()) as Label;

                if (!string.IsNullOrEmpty(lblImpactID.Text))
                {
                    //if(string.IsNullOrEmpty(txtdata.Text))
                    //    txtdata.Text = GetRTOData(lblImpactID.Text, lblRTOID.Text);
                }
            }

            void lblImpactCategoryID_DataBinding(object sender, EventArgs e)
            {
                Label lblImpactCategoryID = (Label)sender;
                GridDataItem container = (GridDataItem)lblImpactCategoryID.NamingContainer;

                object objImpactTypeID = DataBinder.Eval(container.DataItem, "ImpactTypeID");

                if (objImpactTypeID != DBNull.Value)
                {
                    lblImpactCategoryID.Text = objImpactTypeID.ToString();
                }
            }

            void lblRtoID_DataBinding(object sender, EventArgs e)
            {
                Label lblRtoID = (Label)sender;
                GridDataItem container = (GridDataItem)lblImpactCategoryID.NamingContainer;

                object objImpactTypeID = IntervalID; //DataBinder.Eval(container.DataItem, "ImpactTypeID");

                if (objImpactTypeID != DBNull.Value)
                {
                    lblRtoID.Text = objImpactTypeID.ToString();

                }
            }

            void lblImpactID_DataBinding(object sender, EventArgs e)
            {
                Label lblImpactID = (Label)sender;
                GridDataItem container = (GridDataItem)lblImpactCategoryID.NamingContainer;

                object objImpactID = DataBinder.Eval(container.DataItem, "ImpactID");

                if (objImpactID != DBNull.Value)
                {
                    lblImpactID.Text = objImpactID.ToString();

                }
            }

            private string GetRTOData(string ImpactID, string RTOID)
            {
                BussinessFunctionBIADynamic obj = new BussinessFunctionBIADynamic();

                DataTable dt = obj.GetBIAProfile();


                DataRow[] drAll = dt.Select("ImpactID = " + ImpactID + " And TimeIntervalID = " + RTOID);
                string Cost = "";
                foreach (DataRow dr in drAll)
                {
                    Cost = dr["Cost"].ToString();
                }

                return Cost;
            }
        }


        private class BIAMatrixFooterTemplate : ITemplate
        {
            protected RadNumericTextBox radtxtFooter;
            string ColumnIndex;

            public BIAMatrixFooterTemplate(string strColumnCount)
            {
                ColumnIndex = strColumnCount;
            }

            public void InstantiateIn(System.Web.UI.Control container)
            {
                radtxtFooter = new RadNumericTextBox();
                radtxtFooter.Enabled = false;
                //radtxtFooter.AutoPostBack = true;
                radtxtFooter.ID = "txtFooter_" + ColumnIndex;
                container.Controls.Add(radtxtFooter);
            }
        }


        #endregion


        #region Events

        public override void PrepareView()
        {
            _currentLoggedUserName = LoggedInUserName;
            _currentLoginUserId = LoggedInUserId;
            _companyId = LoggedInUserCompanyId;
            _isUserSuperAdmin = IsUserSuperAdmin;
            _isParent = LoggedInUserCompany.IsParent;


        }

        protected void Page_Load(object sender, EventArgs e)
        {

            if (!IsPostBack)
            {

                PopulateDropdown_BusinessProfile();
                PopulateImpactSeverityTypes();

                AssignDataToMatrix();
                btnSaveProfile.Text = "Update";

            }

            GetSumInFooterNEW();
            GetUpdateValidatorControl();


           // var BfDetails = facade.GetBusinessFunctionById(CurrentBusinessFunctionId);
         //   lblBusinessFunction.Text = BfDetails != null ? BfDetails.Name : string.Empty;


        }
        protected void Page_Init(object sender, EventArgs e)
        {

            bool ispostback = Page.IsPostBack;

            if (!IsPostBack)
            {

                string FIADefaultTemp = string.Empty;
                if (!string.IsNullOrEmpty(CurrentBusinessFunctionId.ToString()))
                {
                    _businessFunctionIDId = Convert.ToInt32(CurrentBusinessFunctionId.ToString());
                }
                if (_businessFunctionIDId == 0 || string.IsNullOrEmpty(_businessFunctionIDId.ToString()))
                {
                    var InfraObjectlist = Facade.GetAllInfraObject();
                    if (!IsSuperAdmin && InfraObjectlist != null)
                    {
                        InfraObjectlist = Facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);
                    }
                    InfraObject InfraObjectListByBsId = InfraObjectlist.FirstOrDefault();

                    _businessFunctionIDId = InfraObjectListByBsId.BusinessFunctionId;
                }
                _FIAProfileId = Helper.Url.SecureUrl[Constants.UrlConstants.Params.ProfileID].ToString();



                if (!string.IsNullOrEmpty(_FIAProfileId))
                {
                    Session["NewProfileID"] = Convert.ToInt32(_FIAProfileId);
                    ddlBusinessBIAProfile.Enabled = false;
                    ddlBusinessBIAProfile.SelectedValue = _FIAProfileId;
                }
                else
                {
                    var businessProfileList = Facade.GetAllBusinessProfile();
                    if (!IsSuperAdmin && businessProfileList != null)
                    {
                        businessProfileList = facade.GetAllBusinessProfileByCompanyIdAndRole(LoggedInUserCompanyId, LoggedInUserCompany.IsParent);
                    }
                    if (businessProfileList != null && businessProfileList.Count() > 0)
                    {
                        //var DefaultFiaTemplate = from a in businessProfileList where a.IsActive == 1 select a;
                        if (businessProfileList != null && businessProfileList.Count() > 0)
                        {
                            FIADefaultTemp = businessProfileList.FirstOrDefault().Id.ToString();

                        }

                    }
                    ddlBusinessBIAProfile.Enabled = true;

                    if (!string.IsNullOrEmpty(FIADefaultTemp))
                    {
                        Session["NewProfileID"] = Convert.ToInt32(FIADefaultTemp);
                    }

                    ddlBusinessBIAProfile.SelectedValue = FIADefaultTemp;

                }

            }
            //_businessFunctionIDId = 5;
            _bfProfileId = Session["NewProfileID"] != null ? Convert.ToInt32(Session["NewProfileID"]) : 0;


            GetBIAProfileDetails_ForMatrix(_businessFunctionIDId, _bfProfileId);

            GenerateDataGridView();
            GetUpdateValidatorControl();

            ViewState["ColumnCount"] = rgvMatrix.MasterTableView.Columns.Count;     // Count is maintain for iterate for the radgrid (temp)

        }


        protected void btnSaveProfile_Click(object sender, EventArgs e)
        {


            if (!Page.IsValid) return;
            var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
            if (returnUrl.IsNullOrEmpty())
            {
                returnUrl = ReturnUrl;
            }
            var submitButton = (Button)sender;
            var buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            try
            {
                int iSeverityID = Convert.ToInt32(ddlImactSeverityType.SelectedValue);
                var BFProfile = facade.GetBFBIAMatrixByBFId(_businessFunctionIDId);
                var BFName = facade.GetBusinessFunctionById(_businessFunctionIDId);

                var selectedProfileId = Convert.ToInt32(ddlBusinessBIAProfile.SelectedValue);

                if (BFProfile != null)
                {
                    var pId = BFProfile.FirstOrDefault().ProfileID;

                    if (pId != selectedProfileId)
                    {
                        var profileDetails = facade.GetAllBusinessProfile();
                        if (!IsSuperAdmin && profileDetails != null)
                        {
                            profileDetails = facade.GetAllBusinessProfileByCompanyIdAndRole(LoggedInUserCompanyId, LoggedInUserCompany.IsParent);
                        }
                        var SortFIAProfile = from a in profileDetails where a.Id == pId select a;
                        if (SortFIAProfile.Count() > 0)
                        {
                            if (!string.IsNullOrEmpty(SortFIAProfile.FirstOrDefault().ProfileName))
                            {
                                lblMessage1.Visible = true;
                                lblMessage1.Text = "Bussiness Function " + " " + BFName.Name + " " + "is already attached with FIA Profile :" + SortFIAProfile.FirstOrDefault().ProfileName;
                                return;
                            }
                        }

                    }

                }

                //_isTemplateEditable = true;
                //var allIncident = Facade.GetAllIncidentManagementNew();

                //if (allIncident != null)
                //{

                //    var SortIncidentByOpen = from a in allIncident where a.Status == 1 select a;

                //    if (SortIncidentByOpen != null && SortIncidentByOpen.Count() > 0)
                //    {

                //        foreach (var incSum in SortIncidentByOpen)
                //        {
                //            var inciMgtBIAProfile = Facade.GetIncidentBIAProfileByIncidentID(incSum.Id);

                //            foreach (var incBF in inciMgtBIAProfile)
                //            {

                //                if (_businessFunctionIDId == incBF.ParentBFID && iSeverityID == incBF.ParentBFImpactID)
                //                {
                //                    lblMessage1.Visible = true;
                //                    _isTemplateEditable = false;
                //                    lblMessage1.Text = "Incident is Created for this FIA Template and Business Function";
                //                    return;

                //                }
                //                if (_businessFunctionIDId == incBF.ChildBFID && iSeverityID == incBF.ChildBFImpactID)
                //                {
                //                    lblMessage1.Visible = true;
                //                    _isTemplateEditable = false;
                //                    lblMessage1.Text = "Incident is Created for this FIA Template and Business Function";
                //                    return;
                //                }

                //            }
                //        }
                //    }
                //    else
                //    {
                //        _isTemplateEditable = true;
                //    }
                //}
                //else
                //{

                //    _isTemplateEditable = true;

                //}
                //if (_isTemplateEditable)
                //{
                if (currentTransactionType != TransactionType.Undefined)
                {
                    StartTransaction();
                    GetDataFromGridToCollection();
                    EndTransaction();
                }
                //}


                //if (_isTemplateEditable)
                //{
                string message = "FIA Impact" + " ";
                ErrorSuccessNotifier.AddSuccessMessage(
                    Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                        currentTransactionType));

                //}
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                returnUrl = Request.RawUrl;

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                returnUrl = Request.RawUrl;

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                        "Unhandled exception occurred while saving data", ex);

                    ExceptionManager.Manage(customEx, this);
                }
            }
            if (returnUrl.IsNotNullOrEmpty())
            {

                //Helper.Url.Redirect(new SecureUrl(returnUrl));
                var secureBFBIAUrl = UrlHelper.BuildSecureUrl(Constants.UrlConstants.Urls.ImpactAnalysis.BusinessFunctionBIA,
                                                                                   string.Empty, Constants.UrlConstants.Params.BusinessfunctionId,
                                                                                   CurrentBusinessFunctionId.ToString(), Constants.UrlConstants.Params.ProfileID, ddlBusinessBIAProfile.SelectedValue);

                if (secureBFBIAUrl != null)
                {
                    Helper.Url.Redirect(secureBFBIAUrl);
                }
            }
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                Response.Redirect(Constants.UrlConstants.Urls.Admin.BusinessFunctionsBIA + "?Listitems=" + "Cancel");
            }
            catch (Exception)
            { }
        }



        protected void rgvMatrix_PreRender(object sender, EventArgs e)
        {

        }

        protected void rgvMatrix_ItemDataBound(object sender, GridItemEventArgs e)
        {

        }

        protected void rgvMatrix_ColumnCreated(object sender, GridColumnCreatedEventArgs e)
        {

        }

        protected void ddlImactSeverityType_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                lblMessage1.Visible = false;

                Session["NewProfileID"] = ddlBusinessBIAProfile.SelectedValue;

                Page_Init(null, null);
                AssignDataToMatrix();
                GetSumInFooterNEW();
                GetUpdateValidatorControl();
                lblMessage1.Visible = false;
                UpdatePanel1.Update();
            }
            catch (Exception)
            { }
        }


        protected void ddlBusinessBIAProfile_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                Session["NewProfileID"] = ddlBusinessBIAProfile.SelectedValue;
                lblMessage1.Visible = false;

                Session["NewProfileID"] = ddlBusinessBIAProfile.SelectedValue;

                Page_Init(null, null);
                AssignDataToMatrix();
                GetSumInFooterNEW();
                GetUpdateValidatorControl();
                lblMessage1.Visible = false;
                UpdatePanel1.Update();
            }
            catch (Exception)
            { }
        }


        protected void btnLoadMatrix_Click(object sender, EventArgs e)
        {

            Session["NewProfileID"] = ddlBusinessBIAProfile.SelectedValue;

            Page_Init(null, null);
            AssignDataToMatrix();
            GetSumInFooterNEW();
            GetUpdateValidatorControl();
            lblMessage1.Visible = false;
            UpdatePanel1.Update();
        }


        #endregion





        //#region ProcessBIAImpactMatrix

        //[Serializable()]
        //public class ProcessBIAImpactMatrix
        //{
        //    public string ID;
        //    public string BIAID;
        //    public string ImpactSeverityID;
        //    public string ImpactTypeID;
        //    public string ImpactTypeName;
        //    public string ImpactID;
        //    public string ImpactName;
        //    public string Upto2Hours;
        //    public string Upto4Hours;
        //    public string Upto8Hours;
        //    public string Upto12Hours;
        //    public string Upto24Hours;
        //    public string Upto48Hours;
        //    public string Upto72Hours;
        //    public string Upto1Week;
        //    public string Upto2Weeks;
        //    public string Upto1Month;
        //    public string IsQualitative;
        //    public string Comment;
        //    public string CreatedAt;
        //    public string CreatedBy;
        //    public string ChangedAt;
        //    public string ChangedBy;
        //    public string QuestionID;
        //    public string SectionID;
        //}

        //public class ProcessBIAImpactMatrixColl : CollectionBase
        //{
        //    public void New()
        //    {

        //    }
        //    public void Add(ProcessBIAImpactMatrix biaFacility)
        //    {
        //        base.InnerList.Add(biaFacility);

        //    }
        //    public ProcessBIAImpactMatrix this[int index]
        //    {
        //        get
        //        {
        //            return (ProcessBIAImpactMatrix)base.InnerList[index];
        //        }
        //        set
        //        {
        //            base.InnerList[index] = value;

        //        }
        //    }
        //}

        //#endregion



        public override void PrepareEditView()
        {

        }
        public override void SaveEditor()
        {

        }

        public override void BuildEntities()
        {
        }


    }
}