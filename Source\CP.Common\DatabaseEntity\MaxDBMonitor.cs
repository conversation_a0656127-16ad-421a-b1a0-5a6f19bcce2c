﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "MaxDBMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class MaxDBMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }
        
        [DataMember]
        public string PRDBIPAddress { get; set; }

        [DataMember]
        public string DRDBIPAddress { get; set; }

        [DataMember]
        public string PRDBName { get; set; }

        [DataMember]
        public string DRDBName { get; set; }

        [DataMember]
        public string PRDBState { get; set; }

        [DataMember]
        public string DRDBState { get; set; }

        [DataMember]
        public string PRDBVersion { get; set; }

        [DataMember]
        public string DRDBVersion { get; set; }

        [DataMember]
        public string PRInstanceType { get; set; }

        [DataMember]
        public string DRInstanceType { get; set; }

        [DataMember]
        public string PRInstallationPath { get; set; }

        [DataMember]
        public string DRInstallationPath { get; set; }

        [DataMember]
        public string PRClientprogPath { get; set; }

        [DataMember]
        public string DRClientProgPath { get; set; }

        [DataMember]
        public string PRGlobalProgPath { get; set; }

        [DataMember]
        public string DRGlobalProgPath { get; set; }

        [DataMember]
        public string PRGlobalDataPath { get; set; }

        [DataMember]
        public string DRGlobalDataPath { get; set; }

        [DataMember]
        public string PRDataVolumes { get; set; }

        [DataMember]
        public string DRDataVolumes { get; set; }

        [DataMember]
        public string PRMaxDataVolumes { get; set; }

        [DataMember]
        public string DRMaxDataVolumes { get; set; }

        [DataMember]
        public string PRDataSize { get; set; }

        [DataMember]
        public string DRDataSize { get; set; }

        [DataMember]
        public string PRDataSpace { get; set; }

        [DataMember]
        public string DRDataSpace { get; set; }

        [DataMember]
        public string PRLogVolumes { get; set; }

        [DataMember]
        public string DRLogVolumes { get; set; }

        [DataMember]
        public string PRMaxLogVolumes { get; set; }

        [DataMember]
        public string DRMaxLogVolumes { get; set; }

        [DataMember]
        public string PRLogSpace { get; set; }

        [DataMember]
        public string DRLogSpace { get; set; }

        [DataMember]
        public string PRLogMirrored { get; set; }

        [DataMember]
        public string DRLogMirrored { get; set; }

        [DataMember]
        public string PRLogWriting { get; set; }

        [DataMember]
        public string DRLogWriting { get; set; }

        [DataMember]
        public string PRLogAutomatic { get; set; }

        [DataMember]
        public string DRLogAutomatic { get; set; }

        #endregion Properties

    }
}
