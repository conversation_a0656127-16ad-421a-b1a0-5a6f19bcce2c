﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ODGConfig.aspx.cs" Inherits="CP.UI.Admin.ODGConfig" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
   <%-- <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>--%>
      <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <style type="text/css">
        .widget .widget-head .heading {
    /*color: #555555;
    float: left;
    font-size: 16px;
    height: 35px;
    line-height: 30px;
    margin: 0;*/
    padding: 0 10px;
}

.widget.widget-tabs.changetabsstruc> .widget-head {
     background-color:#f9f9f9;
     background-image:linear-gradient(to bottom, #fdfdfd, #f4f4f4);
     background-repeat:repeat-x;
        }

.changetabsstruc > .widget-head ul li a {
    padding:0 60px !important;
        }

.changetabsstruc > .widget-head ul li.active a { color:#4a8bc2;}
.table.table-striped.table-bordered.table-condensed.table-white.innertable-change thead tr th, .table.table-striped.table-bordered.table-condensed.table-white.innertable-change tbody tr td
{ font-size:10px !important; }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

     <div class="innerLR">
         <h3><img src="../Images/monitor.png" style="vertical-align: text-top;"> Infra Object Monitor </h3>

         <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
               <div class="widget-head">
                  <span class=" heading"> Cluster Details </span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 26%;">
                                               Cluster Details
                                            </th>
                                            <th style="width: 37%;">PR
                                            </th>
                                            <th style="width: 37%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         CLUSTER NAME
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="lable1" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label2" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         CLUSTERWARE ACTIVE VERSION
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label1" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label3" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         OHAS Status
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label4" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label5" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         CRS status
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label6" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label7" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         CSS Status
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label8" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label9" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         EVM STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label10" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label11" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         CLUSTER LISTENER
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label12" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label13" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         SCAN STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label14" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label15" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         SCAN_LISTENER STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label16" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label17" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

  
                            
                                    </tbody>
                                </table>
                    </div>
           </div>

         <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
               <div class="widget-head">
                  <span class=" heading"> Multi Tenancy </span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 26%;">
                                               Multi Tenancy
                                            </th>
                                            <th style="width: 37%;">PR
                                            </th>
                                            <th style="width: 37%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         CDB
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label18" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label19" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         Containers
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label20" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label21" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         PDBs
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label22" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label23" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                  
                            
                                    </tbody>
                                </table>
                    </div>
           </div>

         <div class="widget  widget-body-white"> 
               <div class="widget-head">
                  <span class=" heading"> Pluggable Databases </span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 19%;">
                                               PDB Name
                                            </th>
                                            <th style="width: 27%;">
                                                Pluggable databases
                                            </th>
                                            <th style="width: 27%;">PR
                                            </th>
                                            <th style="width: 27%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                <tr>
                                    <td style="width: 26%;" rowspan="8" class="text-center vertical-middle" >
                                         PDB1
                                    </td>
                                    <td style="width: 27%;" class="tdword-wrap">
                                        PDB Name
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label37" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label24" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        CONNECTION ID
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label26" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label27" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        PDB ID
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label29" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label30" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        PDB MODE
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label32" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label33" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        LOGGING
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label35" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label38" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        FORCE_LOGGING
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label25" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label41" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                 <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        RECOVERY_STATUS
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label46" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label47" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        PDB SIZE
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label43" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label44" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                <tr>
                                    <td style="width: 26%;" rowspan="8" class="text-center vertical-middle" >
                                         PDB2
                                    </td>
                                    <td style="width: 27%;" class="tdword-wrap">
                                        PDB Name
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label28" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label31" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        CONNECTION ID
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label34" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label36" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        PDB ID
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label39" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label40" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        PDB MODE
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label42" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label45" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        LOGGING
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label48" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label49" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        FORCE_LOGGING
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label50" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label51" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                 <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        RECOVERY_STATUS
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label52" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label53" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                  <tr>
                                   
                                    <td style="width: 27%;" class="tdword-wrap">
                                        PDB SIZE
                                    </td>
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label54" runat="server"> </asp:Label>
                                    </td> 
                                    <td style="width: 27%;">
                                        <asp:Label ID="Label55" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
 
                            
                                    </tbody>
                                </table>
                    </div>
           </div>

          <div class="widget widget-tabs changetabsstruc"> 
               <div class="widget-head">
                                    <ul>
                                        <li id="Li1" runat="server" class="active" >
                                            <a href="#litab1" data-toggle="tab"><i></i>Tab1</a></li>

                                        <li id="Li2" runat="server">
                                            <a href="#litab2" data-toggle="tab"><i></i>Tab2</a></li>
                                    </ul>
                </div>
               <div class="widget-body">
                   <div class="tab-content">
                          <div class="tab-pane active" id="litab1">
                              
                              <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
               <div class="widget-head">
                  <span class=" heading"> Database Details </span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 26%;">
                                               Database Details
                                            </th>
                                            <th style="width: 37%;">PR
                                            </th>
                                            <th style="width: 37%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         DATABASE NAME
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label56" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label57" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         DATABASE UNIQUE NAME
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label58" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label59" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         DATABASE ROLE
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label60" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label61" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         OPEN MODE
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label62" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label63" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         DATABASE CREATED TIME
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label64" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label65" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         CONTROLFILE TYPE
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label66" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label67" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         CURRENT SCN
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label68" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label69" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         FLASHBACK_ON
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label70" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label71" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                               
                                    </tbody>
                                </table>
                    </div>
           </div>
                              
                              <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
               <div class="widget-head">
                  <span class=" heading"> Instance Details </span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 26%;">
                                               Instance Details
                                            </th>
                                            <th style="width: 37%;">PR
                                            </th>
                                            <th style="width: 37%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         INSTANCE NAME
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label72" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label73" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         INSTANCE ID
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label74" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label75" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         INSTANCE STARTUP TIME
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label76" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label77" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         OPEN MODE
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label78" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label79" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                
                                    </tbody>
                                </table>
                    </div>
           </div>

                              <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
               <div class="widget-head">
                  <span class=" heading"> ASM Details </span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 26%;">
                                               
                                            </th>
                                            <th style="width: 37%;">PR
                                            </th>
                                            <th style="width: 37%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         ASM DG Details
                                    </td>
                                    <td style="width: 37%;" >
                                       <table style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none  innertable-change">
                                        <thead>
                                        <tr>
                                            <th style="width: 4%;" class="text-center">
                                               #
                                            </th>
                                            <th style="width: 16%;">
                                               NAME
                                            </th>
                                            <th style="width: 16%;">
                                               STATE
                                            </th>
                                            <th style="width: 16%;">
                                               TYPE
                                            </th>
                                            <th style="width: 16%;">
                                               TOTAL_MB
                                            </th>
                                            <th style="width: 16%;">FREE_MB
                                            </th>
                                            <th style="width: 16%;">USED(%)
                                            </th> 
                                        </tr>
                                    </thead> 
                                    <tbody>
                                        <tr>
                                        <td style="width: 4%;" class="text-center">
                                         1
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        REDO
                                        </td> 
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label82" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label83" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label84" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label85" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label86" runat="server"> </asp:Label>
                                        </td>
                                        </tr>
                                        <tr>
                                        <td style="width: 4%;" class="text-center">
                                         2
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        DATA
                                        </td> 
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label80" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label87" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label88" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label89" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label90" runat="server"> </asp:Label>
                                        </td>
                                        </tr>
                                        <tr>
                                        <td style="width: 4%;" class="text-center">
                                         3
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        ARCH
                                        </td> 
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label91" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label92" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label93" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label94" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label95" runat="server"> </asp:Label>
                                        </td>
                                        </tr>
                                    </tbody> 
                                       </table>
                                    </td>
                                    <td style="width: 37%;">
                                       <table style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none  innertable-change">
                                        <thead>
                                        <tr>
                                            <th style="width: 4%;" class="text-center">
                                               #
                                            </th>
                                            <th style="width: 16%;">
                                               NAME
                                            </th>
                                            <th style="width: 16%;">
                                               STATE
                                            </th>
                                            <th style="width: 16%;">
                                               TYPE
                                            </th>
                                            <th style="width: 16%;">
                                               TOTAL_MB
                                            </th>
                                            <th style="width: 16%;">FREE_MB
                                            </th>
                                            <th style="width: 16%;">USED(%)
                                            </th> 
                                        </tr>
                                    </thead> 
                                    <tbody>
                                        <tr>
                                        <td style="width: 4%;" class="text-center">
                                         1
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        REDO
                                        </td> 
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label81" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label96" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label97" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label98" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label99" runat="server"> </asp:Label>
                                        </td>
                                        </tr>
                                        <tr>
                                        <td style="width: 4%;" class="text-center">
                                         2
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        DATA
                                        </td> 
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label100" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label101" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label102" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label103" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label104" runat="server"> </asp:Label>
                                        </td>
                                        </tr>
                                        <tr>
                                        <td style="width: 4%;" class="text-center">
                                         3
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        ARCH
                                        </td> 
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label105" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label106" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label107" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label108" runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 16%;" class="tdword-wrap">
                                        <asp:Label ID="Label109" runat="server"> </asp:Label>
                                        </td>
                                        </tr>
                                    </tbody> 
                                       </table>
                                    </td> 
                                </tr>
                                        
                                    </tbody>
                                </table>
                    </div>
           </div>

                              <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
               <div class="widget-head">
                  <span class=" heading"> TNS Service Details </span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 26%;">
                                               TNS Service Details
                                            </th>
                                            <th style="width: 37%;">PR
                                            </th>
                                            <th style="width: 37%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         Services
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label110" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label111" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                 
                                    </tbody>
                                </table>
                    </div>
           </div>
                                
                          </div>
                          <div class="tab-pane" id="litab2">      
                               <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
               <div class="widget-head">
                  <span class=" heading"> Replication Details </span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 26%;">
                                               Replication Details
                                            </th>
                                            <th style="width: 37%;">PR
                                            </th>
                                            <th style="width: 37%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                             </table>
                            <div class="notifyscroll" style="height:237px !important" >
                            <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <tbody> 
                                <tr>
                                    <td style="width: 26%;">
                                         ACTIVE DG ENABLED
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label112" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label113" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         DG_BROKER STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label114" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label115" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         DATAGUARD_STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label116" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label117" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         RECOVERY STATUS (standby)
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label118" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label119" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         SWITCHOVER STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label120" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label121" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         LOG_ARCHIVE_CONFIG
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label122" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label123" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         FORCE LOGGING
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label124" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label125" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         ARCHIVE DEST LOCATIONS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label126" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label127" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                 <tr>
                                    <td style="width: 26%;">
                                         
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label128" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label129" runat="server"> </asp:Label>
                                    </td> 
                                </tr>

                                 <tr>
                                    <td style="width: 26%;">
                                         
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label130" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label131" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                               
                                    </tbody>
                                </table>
                            </div>
                    </div>
           </div>

                              <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
               <div class="widget-head">
                  <span class=" heading"> Replication Details - Non ODG</span>
               </div>
                    <div class="widget-body">
                         <table width="100%" style="table-layout:fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                    <thead>
                                        <tr>
                                            <th style="width: 26%;">
                                               Replication Details - Non ODG
                                            </th>
                                            <th style="width: 37%;">PR
                                            </th>
                                            <th style="width: 37%;">DR
                                            </th> 
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         ARCHIVE DEST LOCATIONS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label132" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label133" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         DATABASE INCARNATION#
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label134" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label135" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         LOG SEQUENCE
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label136" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label137" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         STANDBY_FILE_MANAGEMENT
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label138" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label139" runat="server"> </asp:Label>
                                    </td> 
                                </tr>
                                        
                                <tr>
                                    <td style="width: 26%;">
                                         FORCE LOGGING
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label ID="Label140" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label ID="Label141" runat="server"> </asp:Label>
                                    </td> 
                                </tr> 
                               
                                    </tbody>
                                </table>
                    </div>
           </div>

                              <div class="widget  widget-body-white"> <%--widget-heading-simple--%>
                                <div class="widget-head">
                                    <span class=" heading"> ARCHIVE LOG GENERATION </span>
                                </div>
                                <div class="widget-body" style="height:199px">
                                    <div class="col-md-4" style="border-right:1px solid #dbdbdb">
                                        <h5> Archive Log Generation Hourly (Count) </h5>
                                        <img src="../Images/chart1.png" alt="" />
                                    </div>
                                    <div class="col-md-4" style="border-right:1px solid #dbdbdb">
                                         <h5> Archive Log Generation Hourly Last 24 Hrs (Size) </h5>
                                        <img src="../Images/chart2.png" alt="" />
                                    </div>
                                    <div class="col-md-4">
                                         <h5> Archive Log Generation Past Week (Size) </h5>
                                        <img src="../Images/chart3.png" alt="" />
                                    </div>
                                </div>
                              </div>

                         </div>
                  </div>
              </div>
          </div>   

     </div>

    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script> 
    <script>
        $(document).ready(function () {
            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
               // setHeight: "200px",
            });
        });

        function pageLoad() {

            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
               // setHeight: "200px",
            });
        }


    </script>

</asp:Content>
