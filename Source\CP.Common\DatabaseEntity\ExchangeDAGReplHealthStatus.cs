﻿using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    public class ExchangeDAGReplHealthStatus : BaseEntity
    {
        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string ClusterServicePrResult { get; set; }

        [DataMember]
        public string ClusterServicePrError { get; set; }

        [DataMember]
        public string ClusterServiceDrResult { get; set; }

        [DataMember]
        public string ClusterServiceDrError { get; set; }

        [DataMember]
        public string ReplayServicePrResult { get; set; }

        [DataMember]
        public string ReplayServicePrError { get; set; }

        [DataMember]
        public string ReplayServiceDrResult { get; set; }

        [DataMember]
        public string ReplayServiceDrError { get; set; }

        [DataMember]
        public string ActiveManagerPrResult { get; set; }

        [DataMember]
        public string ActiveManagerPrError { get; set; }

        [DataMember]
        public string ActiveManagerDrResult { get; set; }

        [DataMember]
        public string ActiveManagerDrError { get; set; }

        [DataMember]
        public string TasksRpcListenerPrResult { get; set; }

        [DataMember]
        public string TasksRpcListenerPrError { get; set; }

        [DataMember]
        public string TasksRpcListenerDrResult { get; set; }

        [DataMember]
        public string TasksRpcListenerDrError { get; set; }

        [DataMember]
        public string TcpListenerPrResult { get; set; }

        [DataMember]
        public string TcpListenerPrError { get; set; }

        [DataMember]
        public string TcpListenerDrResult { get; set; }

        [DataMember]
        public string TcpListenerDrError { get; set; }

        [DataMember]
        public string DAGMembersUpPrResult { get; set; }

        [DataMember]
        public string DAGMembersUpPrError { get; set; }

        [DataMember]
        public string DAGMembersUpDrResult { get; set; }

        [DataMember]
        public string DAGMembersUpDrError { get; set; }

        [DataMember]
        public string ClusterNetworkPrResult { get; set; }

        [DataMember]
        public string ClusterNetworkPrError { get; set; }

        [DataMember]
        public string ClusterNetworkDrResult { get; set; }

        [DataMember]
        public string ClusterNetworkDrError { get; set; }

        [DataMember]
        public string QuorumGroupPrResult { get; set; }

        [DataMember]
        public string QuorumGroupPrError { get; set; }

        [DataMember]
        public string QuorumGroupDrResult { get; set; }

        [DataMember]
        public string QuorumGroupDrError { get; set; }

        [DataMember]
        public string DBCopySuspendedPrResult { get; set; }

        [DataMember]
        public string DBCopySuspendedPrError { get; set; }

        [DataMember]
        public string DBCopySuspendedDrResult { get; set; }

        [DataMember]
        public string DBCopySuspendedDrError { get; set; }

        [DataMember]
        public string DBCopyFailedPrResult { get; set; }

        [DataMember]
        public string DBCopyFailedPrError { get; set; }

        [DataMember]
        public string DBCopyFailedDrResult { get; set; }

        [DataMember]
        public string DBCopyFailedDrError { get; set; }

        [DataMember]
        public string DBInitializingPrResult { get; set; }

        [DataMember]
        public string DBInitializingPrError { get; set; }

        [DataMember]
        public string DBInitializingDrResult { get; set; }

        [DataMember]
        public string DBInitializingDrError { get; set; }

        [DataMember]
        public string DBDisconnectedPrResult { get; set; }

        [DataMember]
        public string DBDisconnectedPrError { get; set; }

        [DataMember]
        public string DBDisconnectedDrResult { get; set; }

        [DataMember]
        public string DBDisconnectedDrError { get; set; }

        [DataMember]
        public string DBLogCopyKeepingUpPrResult { get; set; }

        [DataMember]
        public string DBLogCopyKeepingUpPrError { get; set; }

        [DataMember]
        public string DBLogCopyKeepingUpDrResult { get; set; }

        [DataMember]
        public string DBLogCopyKeepingUpDrError { get; set; }



        [DataMember]
        public string ServerLocatorServicePrResult { get; set; }

        [DataMember]
        public string ServerLocatorServicePrError { get; set; }

        [DataMember]
        public string ServerLocatorServiceDrResult { get; set; }

        [DataMember]
        public string ServerLocatorServiceDrError { get; set; }

        [DataMember]
        public string DatabaseRedundancyCheckPrResult { get; set; }

        [DataMember]
        public string DatabaseRedundancyCheckPrError { get; set; }

        [DataMember]
        public string DatabaseRedundancyCheckDrResult { get; set; }

        [DataMember]
        public string DatabaseRedundancyCheckDrError { get; set; }

        [DataMember]
        public string DatabaseAvailabilityCheckPrResult { get; set; }

        [DataMember]
        public string DatabaseAvailabilityCheckPrError { get; set; }

        [DataMember]
        public string DatabaseAvailabilityCheckDrResult { get; set; }

        [DataMember]
        public string DatabaseAvailabilityCheckDrError { get; set; }


        #endregion Properties
    }
}