﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "NutanixProtectionDomainRepliMonitoring", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class NutanixProtectionDomainRepliMonitoring : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string PRAsyncDRAlert { get; set; }

        [DataMember]
        public string DRAsyncDRAlert { get; set; }

        [DataMember]
        public string PRTotalOngoingReplDir { get; set; }

        [DataMember]
        public string DRTotalOngoingReplDir { get; set; }

        [DataMember]
        public string PRTotalOngoingRepSnapId { get; set; }

        [DataMember]
        public string DRTotalOngoingRepSnapId { get; set; }

        [DataMember]
        public string PRTotalOngoingRepStartTime { get; set; }

        [DataMember]
        public string DRTotalOngoingRepStartTime { get; set; }

        [DataMember]
        public string PRTotOngoingRepDataCompl { get; set; }

        [DataMember]
        public string DRTotOngoingRepDataCompl { get; set; }

        [DataMember]
        public string PRTotalPendRepCount { get; set; }

        [DataMember]
        public string DRTotalPendRepCount { get; set; }

        [DataMember]
        public string PRTotalPendReplOldSnapId { get; set; }

        [DataMember]
        public string DRTotalPendReplOldSnapId { get; set; }

        [DataMember]
        public string PRTotPendReplOldSnapCreTime { get; set; }

        [DataMember]
        public string DRTotPendReplOldSnapCreTime { get; set; }

        [DataMember]
        public string PRTotPenReplOldSnapSize { get; set; }

        [DataMember]
        public string DRTotPenReplOldSnapSize { get; set; }

        [DataMember]
        public string PRLatestLocSnap_SnapshotId { get; set; }

        [DataMember]
        public string DRLatestLocSnap_SnapshotId { get; set; }

        [DataMember]
        public string PRLatestLocSnapCreateTime { get; set; }

        [DataMember]
        public string DRLatestLocSnapCreateTime { get; set; }

        [DataMember]
        public string PRLastSucReplDirection { get; set; }

        [DataMember]
        public string DRLastSucReplDirection { get; set; }

        [DataMember]
        public string PRLastSuccesRepSnapId { get; set; }

        [DataMember]
        public string DRLastSuccesRepSnapId { get; set; }

        [DataMember]
        public string PRLastSuccesReplStartEndTime { get; set; }

        [DataMember]
        public string DRLastSuccesReplStartEndTime { get; set; }

        [DataMember]
        public string DataLag { get; set; }

        [DataMember]
        public DateTime CreateDate { get; set; }


        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }
        #endregion Properties
    }
}
