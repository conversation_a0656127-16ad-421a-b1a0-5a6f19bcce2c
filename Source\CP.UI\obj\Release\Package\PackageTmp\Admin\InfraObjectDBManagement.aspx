﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="InfraObjectDBManagement.aspx.cs" Inherits="CP.UI.InfraObjectDBManagement" Title="Continuity Patrol :: Admin-InfraObjectDBManagement" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>


<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript">
        function CancelClick() {
            return false;
        }

    </script>

    <style type="text/css">
        #ui-datepicker-div {
            font-size: 80%;
            position: absolute !important;
            z-index: 999999 !important;
        }

        .ui-timepicker-div .ui-widget-header {
            margin-bottom: 8px;
        }

        .ui-timepicker-div dl {
            text-align: left;
        }

            .ui-timepicker-div dl dt {
                height: 25px;
            }

            .ui-timepicker-div dl dd {
                margin: -25px 10px 10px 65px;
            }

        .ui-timepicker-div td {
            font-size: 90%;
        }

        .ui-tpicker-grid-label {
            background: none;
            border: none;
            margin: 0;
            padding: 0;
        }

        .ui-datepicker {
            width: 16% !important;
        }

        #btnManageOptions {
            width: 128px;
        }

        .small-table td label {
            font-size: 12px !Important;
        }

        .small-table th {
            border-right: 0px !important;
        }

        .small-table thead > tr > th, .small-table tbody > tr > th, .small-table tfoot > tr > th, .small-table thead > tr > td, .small-table tbody > tr > td, .small-table tfoot > tr > td {
            border-right: 0px;
        }

        .small-table .form-control {
            padding: 3px !important;
            font-size: 12px !important;
            width: 74%;
        }

        #ctl00_cphBody_rdTimeIntervalPR td, #ctl00_cphBody_PanelIntervalDR td {
            border-top: 0px !important;
            padding: 0px;
            padding-right: 18px;
        }

        #ctl00_cphBody_ddlhoursPR.btn-default, #ctl00_cphBody_ddlminutesPR.btn-default, #ctl00_cphBody_ddlhoursDR.btn-default, #ctl00_cphBody_ddlminutesDR.btn-default {
            font-size: 12px !important;
        }

        #ctl00_cphBody_ddlhoursPR.btn, #ctl00_cphBody_ddlminutesPR.btn, #ctl00_cphBody_ddlhoursDR.btn, #ctl00_cphBody_ddlminutesDR.btn {
            padding: 4px 12px;
        }

        .small-table td {
            padding: 2px !important;
            font-size: 12px !important;
        }

            .small-table td input {
                padding: 2px;
                font-size: 12px !important;
                width: 80%;
                margin-top: 2px;
            }

            .small-table td label {
                padding: 2px;
                font-size: 12px !important;
            }
    </style>
    <link href="../App_Themes/CPTheme/jquery-ui-1.8.16.custom.css" rel="stylesheet"
        type="text/css" />

    <script type="text/javascript" src="../Script/jquery-ui-1.8.16.custom.min.js"></script>

    <script type="text/javascript" src="../Script/jquery-ui-timepicker-addon.js"></script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <div class="innerLR">

        <h3 id="headingdb" runat="server">
            <img src="../Images/infra-icon.png">
            InfraObject DB Management</h3>
        <h3 id="headingvirtual" runat="server" visible="false">
            <img src="../Images/infra-icon.png">
            InfraObject Virtual Management</h3>
        <div class="widget">
            <div class="widget-head">
                <asp:Label ID="lblInfraObjectName" runat="server" Text="" CssClass="heading" Style="padding-left: 5px ! important;"></asp:Label>
            </div>
            <div class="widget-body" style="padding-bottom: 0px ! important;">
                <asp:UpdatePanel ID="UpdatePanel_ProfileOverview" runat="server" UpdateMode="Conditional">
                    <ContentTemplate>

                        <asp:Label ID="lblMailboxName" runat="server" Text="MailBox Database"></asp:Label>

                        <asp:DropDownList ID="ddlMailBoxDatabase" runat="server" TabIndex="1"
                            AutoPostBack="True" OnSelectedIndexChanged="DdlMailBoxDatabaseIndexChanged">
                        </asp:DropDownList>

                        <table width="100%" id="tblMaintainence" runat="Server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component</th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tr id="ArchiveLogSequenceName" runat="server" visible="true">
                                <td>Archive Log Sequence Name
                                </td>
                                <td>
                                    <span id="Span16" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblPrseqName" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span1" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblDrseqName" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Archive Log Sequence Number
                                </td>
                                <td>
                                    <span id="Span2" class="icon-numbering" runat="server"></span>
                                    <asp:Label ID="lblPrseqNo" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span3" class="icon-numbering" runat="server"></span>
                                    <asp:Label ID="lblDrseqNo" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr id="GenerationTime1" runat="server" visible="true">
                                <td>Generation Time / Apply Time
                                </td>
                                <td>
                                    <span id="Span4" class="icon-Time" runat="server"></span>
                                    <asp:Label ID="lblPrTime" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span5" class="icon-Time" runat="server"></span>
                                    <asp:Label ID="lblDrTime" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr id="TransactionID1" runat="server" visible="true">
                                <td>Transaction ID
                                </td>
                                <td>
                                    <span id="Span6" class="id-icon" runat="server"></span>
                                    <asp:Label ID="lblPrTransID" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span7" class="id-icon" runat="server"></span>
                                    <asp:Label ID="lblDrTransID" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>

                            <tr id="idDalalag" runat="server" visible="false">
                                <td>Data Lag
                                </td>
                                <td>
                                    <span id="Span42" class="id-icon" runat="server"></span>
                                    <asp:Label ID="lblDataLagPR" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span74" class="id-icon" runat="server"></span>
                                    <asp:Label ID="lblDataLagDR" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr id="idDataguardStatus" runat="server" visible="false">
                                <td>DataguardStatus
                                </td>
                                <td>
                                    <span id="Span75" class="running-icon" runat="server"></span>
                                    <asp:Label ID="lblDataguardStatusPR" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span76" class="id-icon" runat="server"></span>
                                    <asp:Label ID="lblDataguardStatusDR" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>

                                <td visible="false">
                                    <asp:CheckBox ID="chkWorkFlow" runat="server" Text="" AutoPostBack="True" Visible="false"
                                        OnCheckedChanged="ChkWorkFlowCheckedChanged" />
                                </td>
                            </tr>

                            <tr id="trmysqlgmstatus" runat="server">
                                <td>
                                    <asp:Label ID="lblGMRepStatus" runat="server" Text="Replication Status"></asp:Label>
                                </td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr id="tr8" runat="server">
                                <td>
                                    <asp:Label ID="lblGMRepCGTIme" runat="server" Text="Last CG formation time"></asp:Label>
                                </td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr runat="server" id="trMaintainence" visible="false">
                                <td colspan="3" class="message success font_8">
                                    <asp:Label ID="lblReasonMaintenance" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </table>

                        <div id="dvCloudantDB" runat="server" visible="false">
                        </div>

                        <table id="tblVmWareApplicationTable" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Virtual Machine </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ESXI Host Name
                                    </td>
                                    <td class="text-indent">
                                        <span id="Span8" class="host-icon" runat="server"></span>
                                        <asp:Label ID="lblESXIHostNamePr" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="Span9" class="host-icon" runat="server"></span>
                                        <asp:Label ID="lblESXIHostNameDr" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Virtual Machine
                                    </td>
                                    <td>
                                        <span id="Span12" class="monitor-icon" runat="server"></span>
                                        <asp:Label ID="lblMchineNamePr" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="Span13" class="monitor-icon" runat="server"></span>
                                        <asp:Label ID="lblMchineNameDr" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Power State
                                    </td>
                                    <td>
                                        <span id="Span14" class="power-icon" runat="server"></span>
                                        <asp:Label ID="lblpowerstatePR" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="Span15" class="power-icon" runat="server"></span>
                                        <asp:Label ID="lblpowerstateDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Current Snapshot
                                    </td>
                                    <td>
                                        <span id="Span17" class="icon-snapshot" runat="server"></span>
                                        <asp:Label ID="lblcurrentsnapshotPR" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="Span18" class="icon-snapshot" runat="server"></span>
                                        <asp:Label ID="lblcurrentsnapshotDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Updated Time Stamp
                                    </td>
                                    <td class="text-indent">
                                        <span id="Span19" class="stamp-icon" runat="server"></span>
                                        <asp:Label ID="lblupdationtimestampPr" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="Span20" class="stamp-icon" runat="server"></span>
                                        <asp:Label ID="lblupdationtimestampDr" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="eBDRRepliMonitor" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Virtual Machine </th>
                                    <th class="col-md-4">Source Server
                                    </th>
                                    <th>Target Server
                                    </th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Profile Name </td>
                                    <td colspan="2">
                                        <asp:Label runat="server" CssClass="cio-profile-icon" Style="margin: 0;"></asp:Label>
                                        <asp:Label ID="lblProfileName" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Host Name
                                    </td>
                                    <td class="text-indent">
                                        <span id="Span83" class="host-icon" runat="server"></span>
                                        <asp:Label ID="lblsrchostname" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="Span84" class="host-icon" runat="server"></span>
                                        <asp:Label ID="lbltgthostname" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>VM Name </td>
                                    <td>
                                        <asp:Label runat="server" class="vm-icon">&nbsp;</asp:Label>
                                        <asp:Label ID="lblsrcvmname" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" class="vm-icon">&nbsp;</asp:Label>
                                        <asp:Label ID="lbltgtvmname" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>OS Type </td>
                                    <td>
                                        <asp:Label ID="lblsrcosiconpr" runat="server" class="icon-Windows">&nbsp;</asp:Label>
                                        <asp:Label ID="lblsrcostype" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="lblsrcosicondr" runat="server" class="icon-Windows">&nbsp;</asp:Label>
                                        <asp:Label ID="lbltgtostytpe" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Soultion Type </td>
                                    <td>
                                        <asp:Label runat="server" class="vm-icon">&nbsp;</asp:Label>
                                        <asp:Label ID="lblsrcsoln" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" class="vm-icon">&nbsp;</asp:Label>
                                        <asp:Label ID="lbltgtsoln" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>eBDR Health </td>
                                    <td colspan="2">
                                        <asp:Label ID="lbleBDRHealthIconPR" runat="server" class="health-up">&nbsp;</asp:Label>
                                        <asp:Label ID="lblprhealth" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblebdractivitymoni" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <tbody>
                                <tr>
                                    <td class="col-md-4">Manage Migration/Replication</td>
                                    <td colspan="2">
                                        <asp:Button ID="btnebdrpause" runat="server" Style="width: 15%;" CssClass="btn btn-primary" Text="Pause" OnClick="btnebdrpause_Click" />

                                        <asp:Button ID="btnebdrresume" runat="server" Style="width: 15%;" CssClass="btn btn-primary" Text="Resume" OnClick="btnebdrresume_Click" />

                                        <asp:Button ID="btnebdrstart" runat="server" Style="width: 15%;" CssClass="btn btn-primary" Text="Start" OnClick="btnebdrstart_Click" />

                                        <asp:Button ID="btnebdrstop" runat="server" Style="width: 15%;" CssClass="btn btn-primary" Text="Stop" OnClick="btnebdrstop_Click" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table width="100%" id="tbSCRMaintainece" runat="Server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component</th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tr>
                                <td>Log Sequence No
                                </td>
                                <td>
                                    <span id="Span21" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblPRLogSequence" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span22" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblDRLogSequence" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Generation Time / Apply Time
                                </td>
                                <td>
                                    <span id="Span23" class="icon-Time" runat="server"></span>
                                    <asp:Label ID="lblPRLastLogApplyTime" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span24" class="icon-Time" runat="server"></span>
                                    <asp:Label ID="lblDRLastLogApplyTime" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>Enable DataSync via WorkFlow                                
                                </td>
                                <td colspan="2">
                                    <asp:CheckBox ID="CheckBox1" runat="server" Text="" AutoPostBack="True"
                                        OnCheckedChanged="ChkWorkFlowCheckedChanged" />
                                </td>
                            </tr>

                            <tr runat="server" id="tr1" visible="false">
                                <td colspan="3" class="message success font_8">
                                    <asp:Label ID="lblReasonMaintenance2" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </table>

                        <table width="100%" id="tblSqlServerNative" runat="Server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component</th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tr>
                                <td>Log Sequence Name
                                </td>
                                <td>
                                    <span id="Span25" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblbackupfile" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span26" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblrestorefile" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Log Sequence No
                                </td>
                                <td>
                                    <span id="Span27" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblbackuplsn" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span28" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblrestorelsn" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Generation Time / Apply Time
                                </td>
                                <td>
                                    <span id="Span29" class="icon-Time" runat="server"></span>
                                    <asp:Label ID="lblbackuptime" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span30" class="icon-Time" runat="server"></span>
                                    <asp:Label ID="lblrestoretime" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>

                            <tr runat="server" id="tr2" visible="false">
                                <td colspan="3" class="message success font_8">
                                    <asp:Label ID="Label9" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </table>

                        <table id="tblPostgresComponentMonitor" class="table table-striped table-condensed table-bordered table-responsive monitortable font" runat="server" style="display: none;" width="100%">

                            <thead>
                                <tr>
                                    <th class="col-md-4">Component 
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr>
                                    <td>IP Address/HostName</td>
                                    <td>
                                        <span id="spnPCMPRIPHealth" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPCMPRIpaddress" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <span id="spnPCMDRIPHealth" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPCMdRIpaddress" runat="server" Text=""></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Database Name</td>
                                    <td>
                                        <asp:Label ID="lblPCMPRDatabaseNameIcon" runat="server" CssClass="icon-database"></asp:Label>
                                        <asp:Label ID="lblPCMPRDatabaseName" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblPCMDRDatabaseNameIcon" runat="server" CssClass="icon-database"></asp:Label>
                                        <asp:Label ID="lblPCMDRDatabaseName" runat="server" Text=""></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Database Cluster State</td>
                                    <td>
                                        <asp:Label ID="lblPCMPRdbClusterStateIcon" runat="server" CssClass="icon-dbupdate"></asp:Label>
                                        <asp:Label ID="lblPCMPRdbClusterState" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblPCMDRdbClusterStateIcon" runat="server" CssClass="icon-dbupdate"></asp:Label>
                                        <asp:Label ID="lblPCMDRdbClusterState" runat="server" Text=""></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Database Recovery State</td>
                                    <td>
                                        <asp:Label ID="lblPCMPRdbRecoveryStateIcon" runat="server" CssClass="icon-NA"></asp:Label>
                                        <asp:Label ID="lblPCMPRdbRecoveryState" runat="server" CssClass="inactive" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="spnDRDatabaseRecoveryStatus" runat="server" CssClass="Replicating"></asp:Label>
                                        <asp:Label ID="lblPCMDRdbRecoveryState" runat="server" CssClass="text-success" Text=""></asp:Label></td>
                                </tr>
                            </tbody>

                        </table>

                        <table id="tblVmWareHP3ParrWithESXIComponent" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Virtual Machine </th>
                                    <th class="col-md-4">Production Server</th>
                                    <th>DR Server</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ESXI Host Name
                                    </td>
                                    <td>
                                        <span class="vmware-icon"></span>
                                        <asp:Label ID="lblPrHNameESXI" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="vmware-icon"></span>
                                        <asp:Label ID="lblDrHNameESXI" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td>
                                        <span id="SpanVmwithHitachiPrESXI" class="health-up" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblVMPRIpESXI" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="SpanVmwithHitachiDrESXI" class="health-up" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblVmDRIpESXI" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <%--<tr>
                                    <td>Data Store Name</td>
                                    <td><span id="Span5ESXI" class="icon-database " runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRDatastoreNameESXI" runat="server"></asp:Label></td>
                                    <td><span id="Span1ESXI" class="icon-database " runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRDatastoreNameESXI" runat="server"></asp:Label></td>
                                </tr>--%>
                            </tbody>
                        </table>

                        <table id="tblEmcMirrorViewComponent" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td class="text-indent"><span id="Span105" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPrMirrorViewServerName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span106" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDrMirrorViewServerName" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td><span id="PRIPAddSpan" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPrMirrorViewIp" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="DRIPAddSpan" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDrMirrorViewIp" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <table id="tblGoldenGlobalComponent" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td class="text-indent"><span id="Span87" runat="server">&nbsp;</span>
                                        <asp:Label ID="Label32" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span88" runat="server">&nbsp;</span>
                                        <asp:Label ID="Label33" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td><span id="Span89" runat="server">&nbsp;</span>
                                        <asp:Label ID="Label34" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span90" runat="server">&nbsp;</span>
                                        <asp:Label ID="Label35" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <table id="tblHP3parpostgressFullDB" class="table table-striped table-condensed table-bordered table-responsive monitortable font" runat="server" style="display: none;" width="100%">

                            <thead>
                                <tr>
                                    <th class="col-md-4">Component 
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr>
                                    <td>IP Address/HostName</td>
                                    <td>
                                        <span id="spnPCMPRIPHealth1" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPCMPRIpaddress1" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <span id="spnPCMDRIPHealth1" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPCMdRIpaddress1" runat="server" Text=""></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Database Name</td>
                                    <td>
                                        <asp:Label ID="lblPCMPRDatabaseNameIcon1" runat="server" CssClass="icon-database"></asp:Label>
                                        <asp:Label ID="lblPCMPRDatabaseName1" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblPCMDRDatabaseNameIcon1" runat="server" CssClass="icon-database"></asp:Label>
                                        <asp:Label ID="lblPCMDRDatabaseName1" runat="server" Text=""></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Database Cluster State</td>
                                    <td>
                                        <asp:Label ID="lblPCMPRdbClusterStateIcon1" runat="server" CssClass="icon-dbupdate"></asp:Label>
                                        <asp:Label ID="lblPCMPRdbClusterState1" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblPCMDRdbClusterStateIcon1" runat="server" CssClass="icon-dbupdate"></asp:Label>
                                        <asp:Label ID="lblPCMDRdbClusterState1" runat="server" Text=""></asp:Label></td>
                                </tr>

                            </tbody>

                        </table>

                        <table id="tboraclewithfastcopy" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component</th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>

                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td>Server IP/HostName
                                    </td>
                                    <td>
                                        <asp:Label ID="healthPRIP" runat="server" class="health-up"></asp:Label>
                                        <asp:Label ID="lblOracleFastcopyPRIP" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="healthDRIP" runat="server" class="health-down"></asp:Label>
                                        <asp:Label ID="lblOracleFastcopyDRIP" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td>
                                        <span class="icon-storagePR">&nbsp;</span>&nbsp;
                               <asp:Label ID="lblPRserver" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-storageDR">&nbsp;</span>&nbsp;
                               <asp:Label ID="lblDRServer" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database Name
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblPrdb" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lbldrdb" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr id="tr5" runat="server">
                                    <td>Last Log Generated at Production
                                    </td>
                                    <td class="text-indent"><span class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblOFastcopyLastLogSeq" runat="server"></asp:Label>
                                    </td>

                                    <td>--
                                    </td>
                                </tr>

                                <tr id="tr6" runat="server">
                                    <td>Last Log Applied at DR
                                    </td>
                                    <td>--
                                    </td>

                                    <td class="text-indent"><span class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblFastcopyAppliedLog" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <table id="tblOraclewithfastcopyReplication" width="100%" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th colspan="2">Replication Monitor (DataSync)
                                    </th>


                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="col-md-4">Replication Status
                                    </td>
                                    <td>
                                        <asp:Label ID="Label19" runat="server" CssClass="Replicating float-left"></asp:Label>&nbsp;<asp:Label ID="lblRepStatus" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Source Archive Path
                                    </td>
                                    <td>
                                        <span class="icon-storageDR">&nbsp;</span>&nbsp;<asp:Label ID="lblOFastcopySourceDirectory" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Target Archive Path
                                    </td>
                                    <td>
                                        <span class="icon-storageDR">&nbsp;</span>&nbsp;<asp:Label ID="lblOFastcopyDestinationDirectory" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last Replication Count
                                    </td>
                                    <td>
                                        <span id="Span31" class="sequence-icon" runat="server"></span>
                                        <asp:Label ID="lbllastRepCount" runat="server"></asp:Label>
                                    </td>


                                </tr>
                                <tr>

                                    <td>Last Successful Repl. Time
                                    </td>
                                    <td colspan="2">
                                        <span class="icon-Time">&nbsp;</span>&nbsp;<asp:Label ID="lblLastRepTime" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Next Scheduled Repl. Time
                                    </td>
                                    <td>
                                        <span class="icon-Time">&nbsp;</span>&nbsp;<asp:Label ID="lblNextSchTime" runat="server"></asp:Label>
                                    </td>
                                </tr>


                            </tbody>
                        </table>

                        <table id="tblFastCopyJobMonitor" width="100%" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Replication Monitor (DataSync)
                                    </th>

                                    <th class="col-md-4">Production Server</th>
                                    <th>DR Server</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td>
                                        <asp:Label ID="Labelhealth_up" runat="server" CssClass="health-up" Text=""></asp:Label>
                                        <asp:Label ID="lblPRserverhealthup" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Labelhealth_down" runat="server" CssClass="health-down" Text=""></asp:Label>
                                        <asp:Label ID="lblDRserverhealthdown" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Replication Status
                                    </td>
                                    <td colspan="2">
                                        <asp:Label ID="LabelReplicating" runat="server" CssClass="Replicating"></asp:Label>
                                        <asp:Label ID="lblFastcopyRstatus" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>

                                    <td>Total Replication Jobs
                                    </td>
                                    <td colspan="2"><span class="icon-disks">&nbsp;</span>
                                        <asp:Label ID="lbltotreplicationJobPairs" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>

                                    <td>Last Successful Repl. Time
                                    </td>
                                    <td colspan="2">
                                        <span class="icon-Time">&nbsp;</span>
                                        <asp:Label ID="lbllreptime" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>

                                    <td>Next Scheduled Repl. Time
                                    </td>
                                    <td colspan="2">
                                        <span class="icon-Time">&nbsp;</span>
                                        <asp:Label ID="lblnreptime" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <table id="tblRSyncJobMonitor" width="100%" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th colspan="2">Replication Monitor (RSync)
                                    </th>


                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="col-md-4">Replication Status
                                    </td>
                                    <td>
                                        <asp:Label ID="Label36" runat="server" CssClass="Replicating float-left"></asp:Label>&nbsp;<asp:Label ID="lblRepStatus1" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Source Archive Path
                                    </td>
                                    <td>
                                        <span class="icon-storageDR">&nbsp;</span>&nbsp;<asp:Label ID="lblORSyncSourceDirectory" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Target Archive Path
                                    </td>
                                    <td>
                                        <span class="icon-storageDR">&nbsp;</span>&nbsp;<asp:Label ID="lblORSyncDestinationDirectory" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <%-- <tr>
                                    <td>Last Replication Count
                                    </td>
                                    <td>
                                        <span id="Span91" class="sequence-icon" runat="server"></span>
                                        <asp:Label ID="lbllastRepCount1" runat="server"></asp:Label>
                                    </td>


                                </tr>--%>
                                <tr>

                                    <td>Last Successful Repl. Time
                                    </td>
                                    <td colspan="2">
                                        <span class="icon-Time">&nbsp;</span>&nbsp;<asp:Label ID="lblLastRepTime1" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Next Scheduled Repl. Time
                                    </td>
                                    <td>
                                        <span class="icon-Time">&nbsp;</span>&nbsp;<asp:Label ID="lblNextSchTime1" runat="server"></asp:Label>
                                    </td>
                                </tr>


                            </tbody>
                        </table>

                        <table id="tblhadrComponentmonitor" width="100%" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Database (DB2) Monitor
                                    </th>
                                    <th class="col-md-4">Primary
                                    </th>
                                    <th>Stand By DR
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td>
                                        <asp:Label ID="lblDB2Health_PR" runat="server" CssClass="health-up"></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblPRIp" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="lblDB2Health_DR" runat="server" CssClass="health-down"></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblDRIp" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database Instance
                                    </td>
                                    <td>
                                        <asp:Label ID="Label7" runat="server" CssClass="icon-database" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblPRDatabaseInstance" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Label8" runat="server" CssClass="icon-database" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblDRDatabaseInstance" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database Status
                                    </td>
                                    <td>
                                        <asp:Label ID="lblikonPR" runat="server" CssClass="icon-standby" Text=""> </asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblPRDatabaseStatus" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="lblikonDR" runat="server" CssClass="Active float-left" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblDRDatabaseStatus" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last Log Generated / Last Log Applied
                                    </td>
                                    <td>
                                        <asp:Label ID="Label10" runat="server" CssClass="icon-log" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblPRLogFile" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Label11" runat="server" CssClass="icon-log" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblDRLogFile" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr style="display: none">
                                    <td>Current LSN
                                    </td>
                                    <td>
                                        <asp:Label ID="Label5" runat="server" CssClass="icon-currentlsn" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblPRCurrentLsn" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Label20" runat="server" CssClass="icon-currentlsn" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblDRCurrentLsn" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>LSN
                                    </td>
                                    <td>
                                        <asp:Label ID="Label15" runat="server" CssClass="icon-numbering" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblPRLsn" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Label18" runat="server" CssClass="icon-numbering" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblDRLsn" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Time Stamp
                                    </td>
                                    <td>
                                        <asp:Label ID="Label12" runat="server" CssClass="icon-Time" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblPRTimestamp" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Label13" runat="server" CssClass="icon-Time" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                            ID="lblDRTimestamp" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Datalag
                                    </td>
                                    <td colspan="2">
                                        <asp:Label ID="lblDataHadrLag" CssClass="icon-wait" runat="server" Text=""></asp:Label>&nbsp;&nbsp;
                                    <asp:Label ID="lblHadrLag" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblMSExchDAGCompSum" width="100%" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">MS Exchange DAG</th>
                                    <th class="col-md-4">Production Server</th>
                                    <th>DR Server</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td>IP Address
                                    </td>
                                    <td>

                                        <asp:Label ID="lblPRIPAddressIcon" runat="server"></asp:Label>
                                        <asp:Label ID="lblPRIPAddress" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td>

                                        <asp:Label ID="lblDRIPAddressIcon" runat="server"></asp:Label>
                                        <asp:Label ID="lblDRIPAddress" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Host Name
                                    </td>
                                    <td>
                                        <span class="msexchangedag-icon">&nbsp;&nbsp;</span>
                                        <asp:Label ID="lblPRHostName" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="msexchangedag-icon">&nbsp;&nbsp;</span>
                                        <asp:Label ID="lblDRHostName" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MailBox Database Name
                                    </td>
                                    <td class="text-indent">
                                        <span class="icon-database float-left">&nbsp;</span>
                                        <asp:Label ID="lblPRDatabase" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span class="icon-database float-left">&nbsp;</span>
                                        <asp:Label ID="lblDRDatabase" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MailBox Database Status
                                    </td>
                                    <td class="text-indent">
                                         <span class="dbmounted float-left">&nbsp;</span>
                                        <asp:Label ID="lblPRStatus" runat="server" CssClass="icon-numbering"></asp:Label>
                                        <asp:Label ID="lblPRDBState" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                          <%--<span class="health-up float-left">&nbsp;</span>--%>
                                        <asp:Label ID="lblDRState" runat="server"></asp:Label>
                                        <asp:Label ID="lblDRDBState" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last Log Generated at Production
                                    </td>
                                    <td>
                                        <asp:Label ID="Label24" CssClass="icon-numbering" runat="server"></asp:Label>&nbsp;
                                     <asp:Label ID="lblPRDBRecovery" runat="server" Text="NA"></asp:Label>
                                    </td>
                                     <td class="text-indent">
                                        <asp:Label ID="Label37" CssClass="icon-numbering" runat="server"></asp:Label>&nbsp;
                                        <asp:Label ID="lblDRDBRecovery" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last Log Replayed at DR
                                    </td>
                                       <td class="text-indent">
                                        <asp:Label ID="Label38" CssClass="icon-numbering" runat="server"></asp:Label>&nbsp;
                                        <asp:Label ID="lblPRReplyLog" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Label26" CssClass="icon-numbering" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lblDRReplyLog" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Log Generation/Replayed Time
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="Label25" CssClass="icon-Time" runat="server"></asp:Label>
                                        <asp:Label ID="lblLogGenrRepyTimePR" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="Label27" CssClass="icon-Time" runat="server"></asp:Label>
                                        <asp:Label ID="lblLogGenrRepyTimeDR" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>

                        </table>

    
                        <table id="tblPostgreSQLComponentMonitor" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">

                            <thead>
                                <tr>
                                    <th class="col-md-4">Component 
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr>
                                    <td>IP Address/HostName</td>
                                    <td>
                                        <asp:Label ID="Label6" runat="server" CssClass="health_up"></asp:Label>
                                        <asp:Label ID="lblpostPRIP" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="Label16" runat="server" CssClass="health_up"></asp:Label>
                                        <asp:Label ID="lblpostDRIP" runat="server" Text=""></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Current xlog location 
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="Label21" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblCurrentxloglocation" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td>--
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last xlog receive location  
                                    </td>
                                    <td>--
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lbldrtranslogflag" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblLastxlogreceivelocation" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last xlog replay location  
                                    </td>
                                    <td>--
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="Label22" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblLastxlogreplaylocation" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>


                            </tbody>

                        </table>

                        <table id="tblPostgres9xComponentMonitor" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">

                            <thead>
                                <tr>
                                    <th class="col-md-4">Component 
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr>
                                    <td>IP Address/HostName</td>
                                    <td>
                                        <asp:Label ID="lblpostGrePRIPIcon" runat="server" CssClass="health_up"></asp:Label>
                                        <asp:Label ID="lblpostGrePRIP" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblpostGreDRIPIcon" runat="server" CssClass="health_up"></asp:Label>
                                        <asp:Label ID="lblpostGreDRIP" runat="server" Text=""></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Current xlog location 
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblPostGreCurrentxloglocationIcon" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblPostGreCurrentxloglocation" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                    <td>--
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last xlog receive location  
                                    </td>
                                    <td>--
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblPostGreLastXlogRecLocationIcon" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblPostGreLastXlogRecLocation" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last xlog replay location  
                                    </td>
                                    <td>--
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblPostGreLastXlogReplayLocationIcon" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblPostGreLastXlogReplayLocation" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>DataLag (in MB)
                                    </td>
                                    <td class="text-indent" colspan="2">
                                        <asp:Label ID="spnPostgres9xDataLagMB" CssClass="icon-NA" runat="server"></asp:Label>
                                        <asp:Label ID="lblPostgres9xDataLagMB" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>DataLag (hh:mm:ss)
                                    </td>
                                    <td class="text-indent" colspan="2">
                                        <asp:Label ID="spnPostgres9xDataLagHR" CssClass="icon-NA" runat="server"></asp:Label>
                                        <asp:Label ID="lblPostgres9xDataLagHR" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>

                        </table>

                        <table id="tblPostgres10ComponentMonitor" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">

                            <thead>
                                <tr>
                                    <th class="col-md-4">Component 
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr>
                                    <td>IP Address/HostName</td>
                                    <td>
                                        <asp:Label ID="lblPrPostgressIpAddIcon" runat="server" CssClass="health_up"></asp:Label>
                                        <asp:Label ID="lblPrPostgressIpAdd" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblDrPostgressIpAddIcon" runat="server" CssClass="health_up"></asp:Label>
                                        <asp:Label ID="lblDrPostgressIpAdd" runat="server" Text=""></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>Current WAL log location 
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblCurrentLocationIcon" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblCurrentLocation" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                    <td>--
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last WAL log receive location  
                                    </td>
                                    <td>--
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblCurrentReciverLocationIcon" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblCurrentReciverLocation" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Last WAL log replay location  
                                    </td>
                                    <td>--
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblCurrentReplayLocationIcon" CssClass="icon-numbering" runat="server"></asp:Label>
                                        <asp:Label ID="lblCurrentReplayLocation" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>DataLag (in MB)
                                    </td>
                                    <td class="text-indent" colspan="2">
                                        <asp:Label ID="lblDataLagInMBIcon" CssClass="icon-NA" runat="server"></asp:Label>
                                        <asp:Label ID="lblDataLagInMB" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>DataLag (hh:mm:ss)
                                    </td>
                                    <td class="text-indent" colspan="2">
                                        <asp:Label ID="lblDataLagInHHMMSSIcon" CssClass="icon-NA" runat="server"></asp:Label>
                                        <asp:Label ID="lblDataLagInHHMMSS" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>

                        </table>

                        <table id="tblmariadbcomponent" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: table; table-layout: fixed;">
                            <thead>
                                <tr>
                                    <th style="width:30%">Component Monitor</th>
                                    <th style="width:70%" class="col-md-4">MariaDB Details</th>
                                    <%--  <th class="col-md-4">DR Server </th>--%>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Master Server
                                    </td>
                                    <td>
                                        <asp:DropDownList ID="ddlservercomponentlist" runat="server" AutoPostBack="true" CssClass="col-md-6" Style="margin-left: 13px;" OnSelectedIndexChanged="ddlservercomponentlist_SelectedIndexChanged">
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Server IP Address/HostName </td>
                                    <td>  <asp:Label ID="IconPRSerIP" runat="server"></asp:Label>
                                        <asp:Label ID="lblmariaIpPR" runat="server"></asp:Label>
                                    </td>
                                  
                                </tr>
                                <tr>
                                    <td>Server Hostname </td>
                                    <td><span id="Span125" class="host-icon" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblmariahostPR" runat="server"></asp:Label>
                                    </td>
                                  
                                </tr>

                                <tr>
                                    <td>Database Version </td>
                                    <td class="text-indent"><span class="icon-database"></span>
                                        <asp:Label ID="lblmariaDbverPR" runat="server"></asp:Label>
                                    </td>
                                  
                                </tr>

                                <tr>
                                    <td>Maria DB Service Status</td>
                                     <td><span id="Span99" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblmariaDbserstatPR" runat="server"></asp:Label>
                                    </td>
                                  
                                </tr>


                            </tbody>
                        </table>

                        <table id="tblMysqlGlobalMirrorRepMonitor" width="100%" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th colspan="2"></th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>

                                    <td class="col-md-4">Replication Status
                                    </td>
                                    <td class="active vertical-align">

                                        <asp:Label ID="lblmySqlGMGlobal" runat="server">&nbsp;</asp:Label>
                                        <asp:Label ID="lblMySqlGMRepGMStatus" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>

                                    <td>Last CG formation Time
                                    </td>
                                    <td class="text-indent"><span class="icon-Time">&nbsp;</span>
                                        <asp:Label ID="lblMySqlGMRepCGTIme" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblMySqlNativeMonitor" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component</th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>

                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td>Server Host Name
                                    </td>
                                    <td>
                                        <span class="icon-storagePR">&nbsp;</span>&nbsp;
                               <asp:Label ID="lblsqlnativePRserver" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-storageDR">&nbsp;</span>&nbsp;
                               <asp:Label ID="lblsqlnativeDRserver" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Server IP/HostName
                                    </td>
                                    <td>

                                        <span id="span10" class="health-up" runat="server"></span>
                                        <asp:Label ID="lblsqlnativePRIP" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="span11" class="health-up" runat="server"></span>

                                        <asp:Label ID="lblsqlnativeDRIP" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>


                                <tr>
                                    <td>Database Name
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblsqlPRdb" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblsqlDRdb" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>

                                <tr id="tr4" runat="server">
                                    <td>Last Log Genrated at Production
                                    </td>
                                    <td class="text-indent"><span class="icon-numbering">&nbsp;</span>
                                        <asp:Label ID="lbllastlogatPR" runat="server"></asp:Label>
                                    </td>

                                    <td>--
                                    </td>
                                </tr>

                                <tr id="tr7" runat="server">
                                    <td>Last Log  at DR
                                    </td>
                                    <td>--
                                    </td>

                                    <td class="text-indent"><span class="icon-numbering">&nbsp;</span>
                                        <asp:Label ID="lbllastlogatDR" runat="server" Text="N/A"></asp:Label>
                                    </td>


                                </tr>


                            </tbody>
                        </table>

                        <table id="tblDB2DataSyncComponentMonitor" width="100%" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Database(DB2) Monitor</th>
                                    <th class="col-md-4">Primary</th>
                                    <th>Stand By DR</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td>Server Host Name
                                    </td>
                                    <td class="text-indent"><span id="SpaDB2HADRPR" runat="server">&nbsp;&nbsp;</span>
                                        <asp:Label ID="lblDB2DSPRHostName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="SpaDB2HADRDR" runat="server">&nbsp;&nbsp;</span>
                                        <asp:Label ID="lblDBDS2DRHostName" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>IP Address/HostName</td>
                                    <td>
                                        <asp:Label ID="lblDB2DSHealth_PR" runat="server" CssClass="health-up"></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2PRIp" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblDB2DSHealth_DR" runat="server" CssClass="health-up"></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2DRIP" runat="server" Text=""></asp:Label></td>
                                </tr>

                                <tr>
                                    <td>Database Instance</td>
                                    <td>
                                        <asp:Label ID="lblDB2DSPRInstance" runat="server" CssClass="icon-database" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2PRDatabaseInstance" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblDB2DSDRInstance" runat="server" CssClass="icon-database" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2DRDatabaseInstance" runat="server" Text=""></asp:Label></td>
                                </tr>

                                <tr>
                                    <td>Database Status</td>
                                    <td>
                                        <asp:Label ID="lblDB2DSikonPR" runat="server" CssClass="icon-standby" Text=""> </asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2PRdbstatus" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblDB2DSikonDR" runat="server" CssClass="Active float-left" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2DRdbstatus" runat="server" Text=""></asp:Label></td>
                                </tr>


                                <tr>
                                    <td>Last Log Generated / Last Log Applied</td>
                                    <td>
                                        <asp:Label ID="lblDB2DSPRLastLog" runat="server" CssClass="icon-log" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2PRLogFile" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblDB2DSDRLastLog" runat="server" CssClass="icon-log" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2DRLogFile" runat="server" Text=""></asp:Label></td>
                                </tr>

                                <tr style="display: none">
                                    <td>Current LSN</td>
                                    <td>
                                        <asp:Label ID="lblDB2DSPRCurLsn" runat="server" CssClass="icon-currentlsn" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2PRCurrentLsn" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblDB2DSDRCurLsn" runat="server" CssClass="icon-currentlsn" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2DRCurrentLsn" runat="server" Text=""></asp:Label></td>
                                </tr>

                                <tr>
                                    <td>LSN</td>
                                    <td>
                                        <asp:Label ID="lblDB2DSPRLSN" runat="server" CssClass="icon-numbering" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2PRLsn" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblDB2DSDRLSN" runat="server" CssClass="icon-numbering" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2DRLsn" runat="server" Text=""></asp:Label></td>
                                </tr>


                                <tr style="display: none">
                                    <td>Time Stamp</td>
                                    <td>
                                        <asp:Label ID="lblDB2DSTimeStamp_Pr" runat="server" CssClass="icon-Time" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2PRTimestamp" runat="server" Text=""></asp:Label></td>
                                    <td>
                                        <asp:Label ID="lblDB2DSTimeStamp_Dr" runat="server" CssClass="icon-Time" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label ID="lbldb2DRTimestamp" runat="server" Text=""></asp:Label></td>
                                </tr>

                            </tbody>

                        </table>

                        <table class="table table-bordered table-primary" id="tblMssqlEmcsrdfFullDb" runat="server">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tr>
                                <td>MSSQL Server Instance
                                </td>
                                <td>
                                    <span id="Span38" class="sequence-icon" runat="server">&nbsp;</span>

                                    <asp:Label ID="lblPrServerInstance" runat="server" Text="" class="word-wrap"></asp:Label>
                                </td>
                                <td>
                                    <span id="Span39" class="sequence-icon" runat="server">&nbsp;</span>

                                    <asp:Label ID="lblDrServerInstance" runat="server" Text="" class="word-wrap"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Database Name
                                </td>
                                <td>
                                    <span id="Span40" class="sequence-icon" runat="server">&nbsp;</span>

                                    <asp:Label ID="lblPrDbName" runat="server" Text="" class="word-wrap"></asp:Label>
                                </td>
                                <td>
                                    <span id="Span41" class="sequence-icon" runat="server">&nbsp;</span>

                                    <asp:Label ID="lblDrDbName" runat="server" class="word-wrap" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Database State 
                                </td>
                                <td>
                                    <asp:Label ID="lblPrDbStateIcon" runat="server" Text=""></asp:Label>
                                    <span>
                                        <asp:Label ID="lblPrFullDbState" runat="server" Text=""></asp:Label></span>
                                </td>
                                <td>
                                    <span id="Span43" runat="server">&nbsp;</span>
                                    <span>
                                        <asp:Label ID="lblDrFullDbState" runat="server" Text=""></asp:Label></span>
                                </td>
                            </tr>
                            <tr id="trmssqldbmonitoring" runat="server" visible="true">
                                <td>Database Restrict Access Status
                                </td>
                                <td>
                                    <span id="Span44" class="id-icon" runat="server">&nbsp;</span>
                                    <span>
                                        <asp:Label ID="lblPrRestrictAccess" runat="server" Text=""></asp:Label></span>
                                </td>
                                <td>
                                    <span id="Span45" class="id-icon" runat="server">&nbsp;</span>
                                    <span>
                                        <asp:Label ID="lblDrRestrictAccess" runat="server" Text=""></asp:Label></span>
                                </td>
                            </tr>
                        </table>

                        <%--<table id="tblmongodbComponentMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                              <tr>
                                    <td>Server Name
                                    </td>
                                     <td><span id="Span100" runat="server" class="icon-linux">&nbsp;</span>
                                        <asp:Label ID="lblMongoPRIPAdd" runat="server"></asp:Label>
                                    </td>
                                   <td><span id="Span101" runat="server" class="icon-linux">&nbsp;</span>
                                        <asp:Label ID="lblMongoDRIPAdd" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                        <td><span id="Span85" runat="server" class="health-up"></span>
                                        <asp:Label ID="lblMongoPRServerName" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span86" runat="server" class="health-up"></span>
                                        <asp:Label ID="lblMongoDRServerName" runat="server"></asp:Label>
                                    </td>
                                </tr>
                        
                                <tr>
                                    <td>ReplicaSet Name</td>
                                    <td>
                                        <asp:Label runat="server" CssClass="health-up"></asp:Label>
                                        <asp:Label ID="lblPReplicsetname" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" CssClass="health-up"></asp:Label>
                                        <asp:Label ID="lblDReplicsetname" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>_Id </td>
                                    <td>
                                        <asp:Label runat="server" class="icon-edition"></asp:Label>
                                        <asp:Label ID="lblPRmemberID" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" class="icon-edition"></asp:Label>
                                        <asp:Label ID="lblDRmemberID" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Current Priority </td>
                                    <td>
                                        <asp:Label runat="server" class="archive-log-blue"></asp:Label>
                                        <asp:Label ID="lblPRPriority" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" class="archive-log-blue"></asp:Label>
                                        <asp:Label ID="lblDRPriority" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Time Lag </td>
                                    <td>
                                        <asp:Label runat="server" class="clock-icon-blue"></asp:Label>
                                        <asp:Label ID="lblPRTimelag" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" class="clock-icon-blue"></asp:Label>
                                        <asp:Label ID="lblDRTimelag" runat="server"></asp:Label>
                                    </td>
                                </tr>



                            </tbody>
                        </table>--%>

                        <table id="tblmongodbComponentMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                               <%-- <tr>
                                    <td>Server Name
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblMongoPRIPAdd" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblMongoDRIPAdd" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address
                                    </td>
                                    <td class="text-indent"><span id="Span85" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblMongoPRServerName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span86" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblMongoDRServerName" runat="server"></asp:Label>
                                    </td>
                                </tr>--%>

                                <tr>
                                    <td>ReplicaSet Name</td>
                                    <td>
                                        <asp:Label runat="server" CssClass="health-up"></asp:Label>
                                        <asp:Label ID="lblPReplicsetname" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" CssClass="health-up"></asp:Label>
                                        <asp:Label ID="lblDReplicsetname" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>_Id </td>
                                    <td>
                                        <asp:Label runat="server" class="icon-edition"></asp:Label>
                                        <asp:Label ID="lblPRmemberID" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" class="icon-edition"></asp:Label>
                                        <asp:Label ID="lblDRmemberID" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Current Priority </td>
                                    <td>
                                        <asp:Label runat="server" class="archive-log-blue"></asp:Label>
                                        <asp:Label ID="lblPRPriority" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" class="archive-log-blue"></asp:Label>
                                        <asp:Label ID="lblDRPriority" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Time Lag </td>
                                    <td>
                                        <asp:Label runat="server" class="clock-icon-blue"></asp:Label>
                                        <asp:Label ID="lblPRTimelag" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label runat="server" class="clock-icon-blue"></asp:Label>
                                        <asp:Label ID="lblDRTimelag" runat="server"></asp:Label>
                                    </td>
                                </tr>



                            </tbody>
                        </table>

                        <table width="100%" id="tblSqlServerNative2008" runat="Server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component</th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tr>
                                <td>Log Sequence Name
                                </td>
                                <td>
                                    <span id="Span32" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblbackupfileName" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span33" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblRestorefileName" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Log Sequence No
                                </td>
                                <td>
                                    <span id="Span34" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblbackuplsno" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span35" class="sequence-icon" runat="server"></span>
                                    <asp:Label ID="lblrestorelsno" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Generation Time / Apply Time
                                </td>
                                <td>
                                    <span id="Span36" class="icon-Time" runat="server"></span>
                                    <asp:Label ID="lblbackuptimeG" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <span id="Span37" class="icon-Time" runat="server"></span>
                                    <asp:Label ID="lblrestoretimeA" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>

                            <tr runat="server" id="tr3">
                                <td colspan="3" class="message success font_8">
                                    <asp:Label ID="Label29" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </table>

                        <table id="tblMSSqlNetAppSnapMirrorCompMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none; table-layout: fixed;">
                            <thead>
                                <tr>
                                    <th>Component Monitor </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th class="col-md-4">DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>MSSQL Server Instance </td>
                                    <td><span id="Span46" class="sequence-icon" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRMssqlServerInstance" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span47" class="sequence-icon" runat="server"></span>
                                        <asp:Label ID="lblDRMssqlServerInstance" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MSSQL IPAddress/HostName </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblMSSqlPRIPAddress" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up"></span>
                                        <asp:Label ID="lblMSSqlDRIPAddress" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MSSQL Database Name
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblMSSqlPRDBName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblMSSqlDRDBName" runat="server">N/A</asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MSSQL Database State 
                                    </td>
                                    <td>
                                        <asp:Label ID="lblMSSqlPRDBIcon" runat="server" CssClass="icon-on"></asp:Label>
                                        <asp:Label ID="lblMSSqlPRDBState" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="lblMSSqlDRDBIcon" runat="server" CssClass="icon-off"></asp:Label>
                                        <asp:Label ID="lblMSSqlDRDBState" runat="server">N/A</asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database Restrict Access Status 
                                    </td>
                                    <td class="text-indent"><span id="span49" class="users-icon">&nbsp;</span>
                                        <asp:Label ID="lblPRDBRestrictAccess" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span48" class="users-icon">&nbsp;</span>
                                        <asp:Label ID="lblDRDBRestrictAccess" runat="server">N/A</asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <table id="tblMSSqlDoubletekHealth" class="table table-bordered table-white" runat="server" width="100%">
                            <thead>
                                <tr>
                                    <th style="width: 40%">MSSQL Health
                                    </th>
                                    <th>Production
                                    </th>
                                    <th>DR
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>MSSQL Server Instance
                                    </td>
                                    <td>
                                        <span class="icon-storageDR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblPRServer_Doubletek" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-storageDR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblDRServer_Doubletek" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MSSQL Server IP Address/HostName
                                    </td>
                                    <td>
                                        <span class="health-up">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblPRIPAddress_Doubletek" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="health-up">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblDRIPAddress_Doubletek" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MSSQL Database
                                    </td>
                                    <td>
                                        <span class="icon-database float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblprdatabase_Doubletek" runat="server" Text="NA"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-database float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lbldrdatabase_Doubletek" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>MSSQL Database State
                                    </td>
                                    <td>
                                        <asp:Label ID="Label14" runat="server" CssClass="icon-NA"></asp:Label>
                                        <asp:Label ID="lblprDBState_Doubletek" Text="NA" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Label17" runat="server" CssClass="icon-NA"></asp:Label>
                                        <asp:Label ID="lbldrDBState_Doubletek" Text="NA" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <table id="tblsybasemonitorin" class="table table-bordered table-white" runat="server" width="100%">
                            <thead>
                                <tr>
                                    <th style="width: 40%">Sybase Monitor
                                    </th>
                                    <th>Production
                                    </th>
                                    <th>DR
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Sybase Server 
                                    </td>
                                    <td>
                                        <span class="icon-storagePR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblserversypr" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-storageDR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblserversydr" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Sybase Server IP Address/HostName
                                    </td>
                                    <td>
                                        <span class="health-up">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblserveripaddresspr" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <span class="health-up">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblserveripaddressdr" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblsybasewithsrsmonitorin" class="table table-bordered table-white" runat="server" width="100%">
                            <thead>
                                <tr>
                                    <th style="width: 40%">Sybase With SRS Monitor
                                    </th>
                                    <th>Production
                                    </th>
                                    <th>DR
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Sybase With SRS Server 
                                    </td>
                                    <td>
                                        <span class="icon-storagePR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblsywithsrsPr" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-storageDR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblsywithsrsDr" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Sybase With SRS Server IP Address/HostName
                                    </td>
                                    <td>
                                        <span class="health-up">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblsywithsrsSerPr" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="health-up">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblsywithsrsSerDr" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Sybase With SRS Server Database Name
                                    </td>
                                    <td>
                                        <span class="icon-database">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblsywithDBPr" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-database">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblsywithDBDr" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblSybaseWithRsHadrMonitoring" class="table table-bordered table-white" runat="server" width="100%" style="display: none">
                            <thead>
                                <tr>
                                    <th style="width: 40%">Sybase With RS HADR Monitor
                                    </th>
                                    <th>Production
                                    </th>
                                    <th>DR
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Sybase With RS HADR Server 
                                    </td>
                                    <td>
                                        <span class="icon-storageDR float-left">&nbsp;</span>&nbsp;&nbsp; <%-- icon-storagePR float-left--%>
                                        <asp:Label ID="lblPRSybaseHadrServerName" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-storageDR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblDRSybaseHadrServerName" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Sybase With RS HADR Server IP Address/HostName
                                    </td>
                                    <td>
                                        <%-- <span class="health-up">&nbsp;</span>&nbsp;&nbsp;--%>
                                        <asp:Label ID="Label30" runat="server" class="health-up"></asp:Label>
                                        <asp:Label ID="lblPRSybaseHadrServerIP" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                    <td>
                                        <%--<span class="health-up">&nbsp;</span>&nbsp;&nbsp;--%>
                                        <asp:Label ID="Label31" runat="server" class="health-up"></asp:Label>
                                        <asp:Label ID="lblDRSybaseHadrServerIP" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Sybase With RS HADR Server Database Name
                                    </td>
                                    <td>
                                        <span class="icon-database">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblPRSybaseHadrDBName" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-database">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblDRSybaseHadrDBName" runat="server" Text="N/A"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblMaxDBInfra" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>DB IP Address/HostName
                                    </td>
                                    <td>
                                        <span id="span77" class="health-up" runat="server"></span>
                                        <asp:Label ID="lblMaxDBIPAddressPR" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="span78" class="health-up" runat="server"></span>
                                        <asp:Label ID="lblMaxDBIPAddressDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database Name
                                    </td>
                                    <td>
                                        <span class="icon-database">&nbsp;</span>&nbsp;
                                                                        <asp:Label ID="lblMaxDBNamePR" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-database">&nbsp;</span>&nbsp;
                                                                        <asp:Label ID="lblMaxDBNameDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database State
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblMaxDBStatePR" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblMaxDBStateDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>DataBase Version
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblprversion" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lbldrversion" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Instance Type </td>
                                    <td class="text-indent"><span class="icon-instance">&nbsp;</span>
                                        <asp:Label ID="lblPRInsType" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-instance">&nbsp;</span>
                                        <asp:Label ID="lblDRInsType" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Installation Path </td>
                                    <td class="text-indent"><span class="icon-path">&nbsp;</span>
                                        <asp:Label ID="lblPRInspath" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-path">&nbsp;</span>
                                        <asp:Label ID="lblDRInsPath" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Data Path </td>
                                    <td class="text-indent"><span class="icon-path">&nbsp;</span>
                                        <asp:Label ID="lblPRDataPath" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-path">&nbsp;</span>
                                        <asp:Label ID="lblDRDatapath" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblHyperVMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th>Hyper-V VM Summary </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th class="col-md-4">DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Hyper-V Host IP Address/HostName
                                    </td>
                                    <td><span id="Span50" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRHyperIPAdd" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span51" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRHyperIPAdd" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>VM Name
                                    </td>
                                    <td class="text-indent"><span id="Span52" class="vmware-icon" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRHyperVMName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span53" class="vmware-icon" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRHyperVMName" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>VM State
                                    </td>
                                    <td class="text-indent"><span id="Span54" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRHyperVMState" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span55" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRHyperVMState" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>VM IP Address/HostName
                                    </td>
                                    <td class="text-indent"><span id="Span56" class="icon-NA" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRHyperVMAdd" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span57" class="icon-NA" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRHyperVMAdd" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>VM Networking Status
                                    </td>
                                    <td class="text-indent"><span id="Span58" runat="server" class="icon-NA">&nbsp;</span>
                                        <asp:Label ID="lblPRHyperNetAdd" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span59" runat="server" class="icon-NA">&nbsp;</span>
                                        <asp:Label ID="lblDRHyperNetAdd" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <%--    <table id="tblMySqlMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th>MySql Native Log Shipping Summary </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th class="col-md-4">DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>IP Address
                                    </td>
                                    <td><span id="Span60" runat="server" class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRIP" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span61" runat="server" class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRIP" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td><span id="Span62" runat="server" class="icon-storagePR">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRServerNM" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span63" runat="server" class="icon-storagePR">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRServerNM" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database Name
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRDBNM" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRDBNM" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <%--  <tr>
                                                                   <td>Database Version</td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlPRDBVersion" runat="server"></asp:Label>
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlDRDBVersion" runat="server" Text="DRDatabase"></asp:Label>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td>Database Service Status 
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlPRDBServiceStatus" runat="server" Text="PRDatabase"></asp:Label>
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlDRDBServiceStatus" runat="server" Text="DRDatabase"></asp:Label>
                                                                    </td>
                                                                </tr>

                                                                  <tr>
                                                                    <td> Database State (Read-Only)
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlPRDBState1" runat="server" Text="PRDatabase"></asp:Label>
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlDRDBState1" runat="server" Text="DRDatabase"></asp:Label>
                                                                    </td>
                                                                </tr>

                                                                 <tr>
                                                                    <td> Slave Running State
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlPRSlaveRunSate" runat="server" Text="PRDatabase"></asp:Label>
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlDRSlaveRunSate" runat="server" Text="DRDatabase"></asp:Label>
                                                                    </td>
                                                                </tr>

                                                                 <tr>
                                                                    <td> Slave IO Running Status
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlPRSlaveIO" runat="server" Text="PRDatabase"></asp:Label>
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlDRSlaveIO" runat="server" Text="DRDatabase"></asp:Label>
                                                                    </td>
                                                                </tr>

                                                                 <tr>
                                                                    <td> Slave SQL Running Status
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlPRSlaveSql" runat="server" Text="PRDatabase"></asp:Label>
                                                                    </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMySqlDRSlaveSql" runat="server" Text="DRDatabase"></asp:Label>
                                                                    </td>
                                                                </tr>--%>

                        <%-- <tr>
                                    <td>Master Log File 
                                    </td>
                                    <td class="text-indent">
                                          <span class="icon-log"></span>
                                        <asp:Label ID="lblMySqlPRMasterLogFile" runat="server" Text="PRDatabase"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                          <span class="icon-log"></span>
                                        <asp:Label ID="lblMySqlDRMasterLogFile" runat="server" Text="DRDatabase"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Relay Master Log File
                                    </td>
                                    <td class="text-indent">
                                          <span class="icon-log"></span>
                                        <asp:Label ID="lblMySqlPRRelayMasterLogF" runat="server" Text="PRDatabase"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                          <span class="icon-log"></span>
                                        <asp:Label ID="lblMySqlDRRelayMasterLogF" runat="server" Text="DRDatabase"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Master Log Position 
                                    </td>
                                    <td class="text-indent">
                                          <span class="icon-log"></span>
                                        <asp:Label ID="lblMySqlPRMasterLogPos" runat="server" Text="PRDatabase"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                          <span class="icon-log"></span>
                                        <asp:Label ID="lblMySqlDRMasterLogPos" runat="server" Text="DRDatabase"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Exec Master Log Position  
                                    </td>
                                    <td class="text-indent">
                                          <span class="icon-log"></span>
                                        <asp:Label ID="lblMySqlPRExecMaster" runat="server" Text="PRDatabase"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                          <span class="icon-log"></span>
                                        <asp:Label ID="lblMySqlDRExecMaster" runat="server" Text="DRDatabase"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>--%>

                        <table id="tblMySqlMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th id="thHeader" runat="server">MySql Native Log Shipping Summary </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th class="col-md-4">DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td><span id="Span60" runat="server" class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRIP" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span61" runat="server" class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRIP" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td><span id="Span62" runat="server" class="icon-storagePR">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRServerNM" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span63" runat="server" class="icon-storagePR">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRServerNM" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database Name
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRDBNM" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRDBNM" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr id="MasterLogfile" runat="server">
                                    <td>Master Log File 
                                    </td>
                                    <td class="text-indent">
                                        <span id="PRMasterLogFile" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRMasterLogFile" runat="server" Text="PRDatabase"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="DRMasterLogFile" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRMasterLogFile" runat="server" Text="DRDatabase"></asp:Label>
                                    </td>
                                </tr>

                                <tr id="RelayMasterLogfile" runat="server">
                                    <td>Relay Master Log File
                                    </td>
                                    <td class="text-indent">
                                        <span id="PRRelayMasterLogF" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRRelayMasterLogF" runat="server" Text="PRDatabase"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="DRRelayMasterLogF" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRRelayMasterLogF" runat="server" Text="DRDatabase"></asp:Label>
                                    </td>
                                </tr>

                                <tr id="MasterLogPosition" runat="server">
                                    <td>Master Log Position 
                                    </td>
                                    <td class="text-indent">
                                        <span id="PRMasterLogPosition" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRMasterLogPos" runat="server" Text="PRDatabase"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="DRMasterLogPosition" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRMasterLogPos" runat="server" Text="DRDatabase"></asp:Label>
                                    </td>
                                </tr>

                                <tr id="ExecMasterLogPosition" runat="server">
                                    <td>Exec Master Log Position  
                                    </td>
                                    <td class="text-indent">
                                        <span id="PRExecMasterLogPosition" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblMySqlPRExecMaster" runat="server" Text="PRDatabase"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="DRExecMasterLogPosition" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblMySqlDRExecMaster" runat="server" Text="DRDatabase"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblSVCComponentMoniotring" runat="server" class="table table-striped table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Host Name
                                    </td>
                                    <td class="text-indent"><span id="SpanSvc1" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCServerHostName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="SpanSvc2" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCServerHostName" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCIPAddress" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCIPAddress" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <table id="tblVmWareComponent" runat="server" class="table table-striped table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Virtual Machine </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ESXI Host Name
                                    </td>
                                    <td>
                                        <span class="vmware-icon"></span>
                                        <asp:Label ID="lblPrHName" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="vmware-icon"></span>
                                        <asp:Label ID="lblDrHName" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td>
                                        <span id="SpanVmwithHitachiPr" class="health-up" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblVMPRIp" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="SpanVmwithHitachiDr" class="health-up" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblVmDRIp" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Data Store Name</td>
                                    <td><span id="Span64" class="icon-database " runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRDatastoreName" runat="server"></asp:Label></td>
                                    <td><span id="Span65" class="icon-database " runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRDatastoreName" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>No. of VM Configured</td>
                                    <td><span id="Span66" class="count-icon float-left" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblNoofVmConfigured" runat="server"></asp:Label></td>
                                    <td><span id="Span67" class="count-icon float-left" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblNoofVmDRConfigured" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>No. of Power State On</td>
                                    <td><span id="Span68" class="icon-on" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblNoofPowerOn" runat="server"></asp:Label></td>
                                    <td><span id="Span69" class="icon-on" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblsNoofDRPowerOn" runat="server"></asp:Label></td>
                                </tr>

                                <tr>
                                    <td>No. of Power State Off</td>
                                    <td><span id="Span70" class="icon-off" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblNoofPowerOff" runat="server"></asp:Label></td>
                                    <td><span id="Span71" class="icon-off" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblsNoofDRPowerOff" runat="server"></asp:Label></td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblMssqldbmirrormonitoring" runat="server" class="table table-striped table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblsernamepr" runat="server"></asp:Label>
                                        <asp:Label ID="lblServerPrname" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblsernamedr" runat="server"></asp:Label>
                                        <asp:Label ID="lblServerDrname" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td><span id="Span72" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblipPraddress" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span73" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblipDraddress" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Database Name
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblDBPRName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblDBDRName" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Server Network Address
                                    </td>
                                    <td class="text-indent"><span class="icon-storagePR">&nbsp;</span>
                                        <asp:Label ID="lblPrServerNwAddress" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-storageDR">&nbsp;</span>
                                        <asp:Label ID="lblDrServerNwAddress" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Operation Mode
                                    </td>
                                    <td class="text-indent"><span class="icon-sync">&nbsp;</span>
                                        <asp:Label ID="lblPrOperationMode" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-sync">&nbsp;</span>
                                        <asp:Label ID="lblDrOperationMode" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Role Of DB
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblPRDbRole" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblDRDbRole" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                        <table id="tblMssql2014ServerComponent" runat="server" class="table table-striped table-condensed table-bordered table-responsive margin-bottom-none"
                            width="100%">
                            <thead>
                                <tr>
                                    <th class="col-md-4">MSSQL AlwaysOn Server Info
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>MSSQL Server Edition
                                    </td>
                                    <td>
                                        <span class="icon-database"></span>
                                        <asp:Label ID="lblPRServerEdition" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-database"></span>
                                        <asp:Label ID="lblDRServerEdition" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td>
                                        <span class="ip-icon"></span>
                                        <asp:Label ID="AddressIPPR" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="ip-icon"></span>                                       
                                        <asp:Label ID="AddressIPDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Host Name
                                    </td>
                                    <td>
                                        <span class="host-icon"></span>
                                        <asp:Label ID="Label23" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span class="host-icon"></span>
                                        <asp:Label ID="Label28" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Server Port
                                    </td>
                                    <td class="text-indent">
                                        <span class="port-icon"></span>
                                        <asp:Label ID="lblPRServerPort" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span class="port-icon"></span>
                                        <asp:Label ID="lblDRServerPort" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblLeapNutanixComponent" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor </th>
                                    <th class="col-md-4">Production Server </th>
                                    <th>DR Server </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name </td>
                                    <td class="text-indent"><span id="SpnPRServerLeapIcon" runat="server" class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblPRSerLeapPR" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="SpnDRServerLeapIcon" runat="server" class="icon-database">&nbsp;</span>
                                        <asp:Label ID="lblDRSerLeapPR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName </td>
                                    <td><span id="SpnPRIPAdLeapIcon" runat="server" class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblLeapPRIPAddDet" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="SpnDRIPAdLeapIcon" runat="server" class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblLeapDRIPAddDet" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblNutanixcomponent" runat="server" width="100%" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component</th>
                                    <th class="col-md-4">Production Server</th>
                                    <th class="col-md-4">DR Server</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td class="text-indent">
                                        <span id="Span95" class="icon-database" runat="server"></span>
                                        <asp:Label ID="lblNPrSer" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="Span96" class="icon-database" runat="server"></span>
                                        <asp:Label ID="lblNDrSer" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td>
                                        <span id="Span97" class="health-up" runat="server"></span>
                                        <asp:Label ID="lblNPrIp" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="Span98" class="health-up" runat="server"></span>
                                        <asp:Label ID="lblNDrIp" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table class="table table-bordered table-primary" id="tblMaxEmcsrdfFullDb" runat="server" visible="false">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tr>
                                <td>MAX Server Instance
                                </td>
                                <td>
                                    <span id="Span79" class="sequence-icon" runat="server">&nbsp;</span>

                                    <asp:Label ID="lblPRMaxServerInstance" runat="server" Text="" class="word-wrap"></asp:Label>
                                </td>
                                <td>
                                    <span id="Span80" class="sequence-icon" runat="server">&nbsp;</span>

                                    <asp:Label ID="lblDRMaxServerInstance" runat="server" Text="" class="word-wrap"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Max Database Name
                                </td>
                                <td>
                                    <span id="Span81" class="sequence-icon" runat="server">&nbsp;</span>

                                    <asp:Label ID="lblPRMaxDatabaseName" runat="server" Text="" class="word-wrap"></asp:Label>
                                </td>
                                <td>
                                    <span id="Span82" class="sequence-icon" runat="server">&nbsp;</span>

                                    <asp:Label ID="lblDRMaxDatabaseName" runat="server" class="word-wrap" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Max Database State 
                                </td>
                                <td>
                                    <asp:Label ID="lblMaxDBPRStateIcon" runat="server" Text=""></asp:Label>
                                    <span>
                                        <asp:Label ID="lblPRMaxDatabaseState" runat="server" Text=""></asp:Label></span>
                                </td>
                                <td>
                                    <asp:Label ID="lblMaxDBDRStateIcon" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblDRMaxDatabaseState" runat="server" Text=""></asp:Label></span>
                                </td>
                            </tr>

                        </table>

                        <table id="tblMSSqlComponentMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor</th>
                                    <th class="col-md-4">Production Server</th>
                                    <th>DR Server</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name</td>
                                    <td class="text-indent"><span id="Span91"  class="icon-Windows" runat="server">&nbsp;</span><asp:Label ID="lblMSSqlPRServer" runat="server"></asp:Label></td>
                                    <td class="text-indent"><span id="Span92"  class="icon-Windows" runat="server">&nbsp;</span><asp:Label ID="lblMSSqlDRServer" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName</td>
                                    <td><span id="spanMSSqlPRServerIPAddress" runat="server">&nbsp;</span><asp:Label ID="lblMSSqlPRServerIPAddress" runat="server"></asp:Label></td>
                                    <td><span id="spanMSSqlDRServerIPAddress" runat="server">&nbsp;</span><asp:Label ID="lblMSSqlDRServerIPAddress" runat="server"></asp:Label></td>
                                </tr>
                                <%--  <tr>
                                    <td>MSSQL Database Name</td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span><asp:Label ID="lblMSSQLPRSERVERDBName" runat="server"></asp:Label></td>
                                    <td class="text-indent"><span class="icon-database">&nbsp;</span><asp:Label ID="lblMSSQLDRSERVERDBName" runat="server">N/A</asp:Label></td>
                                </tr>--%>
                                <tr>
                                    <td>MSSQL Server HostName</td>
                                    <td class="text-indent"><span runat="server"  class="host-icon">&nbsp;</span><asp:Label ID="lblMSSQLPRServerHostName" runat="server"></asp:Label></td>
                                    <td class="text-indent"><span runat="server"  class="host-icon">&nbsp;</span><asp:Label ID="lblMSSQLDRServerHostName" runat="server">N/A</asp:Label></td>
                                </tr>
                            </tbody>
                        </table>
                         <table id="tblPGSqlClusterComponentMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor</th>
                                    <th class="col-md-4">Production Server</th>
                                    <th>DR Server</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                                                    <td>Server Name</td>
                                                                    <td><span id="spanPGClusterPRServer" runat="server" class="icon-Windows">&nbsp;</span>
                                                                        <asp:Label ID="lblPGClusterPRServer" runat="server" Text=""></asp:Label></td>
                                                                    <td><span id="spanPGClusterDRServer" runat="server" class="icon-Windows">&nbsp;</span>
                                                                        <asp:Label ID="lblPGClusterDRServer" runat="server" Text=""></asp:Label></td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Cluster Node–IP Address</td>
                                                                    <td><span id="spnPgsqlPRIPHealth" runat="server">&nbsp;</span><asp:Label ID="lblClusterNodePRIPAddress" runat="server" Text=""></asp:Label></td>
                                                                    <td><span id="spnPgsqlDRIPHealth" runat="server">&nbsp;</span><asp:Label ID="lblClusterNodeDRIPAddress" runat="server" Text=""></asp:Label></td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Cluster Node–Hostname</td>
                                                                    <td>
                                                                        <asp:Label ID="lblPgsqlPRDatabaseNameIcon" runat="server" CssClass="host-icon"></asp:Label><asp:Label ID="lblPgsqlPRHostName" runat="server" Text=""></asp:Label></td>
                                                                    <td>
                                                                        <asp:Label ID="lblPgsqlDRDatabaseNameIcon" runat="server" CssClass="host-icon"></asp:Label><asp:Label ID="lblPgsqlDRHostName" runat="server" Text=""></asp:Label></td>
                                                                </tr>
                            </tbody>
                        </table>


                        <asp:Panel ID="panelSwitchOver" runat="server" Width="450px" Height="520px" Style="display: none;">
                            <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                <ContentTemplate>
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 550px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">SwitchOver Workflow
                                                         <asp:Label ID="lblCreate" runat="server" Text="Workflow" Visible="false"></asp:Label></h3>
                                                    <asp:LinkButton ID="lnkbtnClose" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                                                </div>
                                                <iframe id="iframe1" src="SwitchOver.aspx" runat="server" height="435" style="width: 100%" class="border-none" />
                                            </div>
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>
                        <asp:Panel ID="panelSwitchBack" runat="server" Style="display: none" Width="450px" Height="480px">
                            <asp:UpdatePanel ID="Updatepanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                <ContentTemplate>
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 550px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">SwitchBack Workflow
                                                        <asp:Label ID="Label3" runat="server" Text="Workflow" Visible="false"></asp:Label></h3>
                                                    <asp:LinkButton ID="LinkButton1" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>

                                                </div>
                                                <iframe id="iframe2" src="SwitchBack.aspx" height="435" style="width: 100%" class="border-none" runat="server" />


                                            </div>


                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>
                        <asp:Panel ID="panelFail" runat="server" Style="display: none" Width="450px" Height="480px">
                            <asp:UpdatePanel ID="UpdatepanelFailOver" runat="server" UpdateMode="Conditional"
                                ChildrenAsTriggers="true">
                                <ContentTemplate>
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 550px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">Workflow
                                                        <asp:Label ID="Label4" runat="server" Text="Workflow" Visible="false"></asp:Label></h3>
                                                    <asp:LinkButton ID="LinkButton2" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>

                                                </div>
                                                <%--<iframe id="iframe3" src="FailOver.aspx" height="435" style="border: none; width: 100%;" runat="server" />--%>
                                            </div>


                                        </div>
                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>
                        <asp:Panel ID="panelFailBack" runat="server" Style="display: none" Width="450px" Height="480px">
                            <asp:UpdatePanel ID="Updatepanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                <ContentTemplate>
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 875px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">Workflow
                                                        <asp:Label ID="Label1" runat="server" Text="Workflow" Visible="false"></asp:Label></h3>
                                                    <asp:LinkButton ID="LinkButton3" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>
                        <asp:Panel ID="panelCustom" runat="server" Style="display: none" Width="450px" Height="480px">
                            <asp:UpdatePanel ID="Updatepanel3" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                <ContentTemplate>
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 550px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">Custom Workflow
                                                        <asp:Label ID="Label2" runat="server" Text="Workflow" Visible="false"></asp:Label></h3>
                                                    <asp:LinkButton ID="LinkButton4" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                                                </div>
                                                <iframe id="iframe4" src="CustomWorkflow.aspx" height="435" class="border-none" style="width: 100%" runat="server" />
                                            </div>
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                        <asp:Panel ID="panelScheduleworkflow" runat="server" Style="display: none" Width="550px">
                            <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                <ContentTemplate>
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 800px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">Infraobject Scheduled Workflow for
                                                    <asp:Label ID="lblName" runat="server" Text="" CssClass="h3" Style="top: 0px; font-style: normal;" ForeColor="White"></asp:Label></h3>
                                                    <asp:LinkButton ID="LinkButton6" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                                                </div>
                                                <%--<iframe id="iframe5" src="InfraobjectScheduleWorkflow.aspx" class="border-none" style="width: 100%; height: 500px;" runat="server" />--%>

                                                <asp:HtmlIframe id="iframe5" src="InfraobjectScheduleWorkflow.aspx" class="border-none" style="width: 100%; height: 500px;" runat="server">
                                                </asp:HtmlIframe>
                                            </div>
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>
                    </ContentTemplate>
                </asp:UpdatePanel>
                <table class="table table-striped table-bordered table-condensed" width="100%" id="tblMaintainenceManage" style="margin-bottom: 8px ! important;">
                    <tr>
                        <td class="col-md-4">Manage InfraObject
                        </td>
                        <td colspan="2" class="align-right">
                            <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenance" runat="server" TargetControlID="btnMaint"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                                PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="bg"
                                CancelControlID="btnCancleMaintaince">
                            </TK1:ModalPopupExtender>
                            <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenanceAll" runat="server" TargetControlID="btnMaintenanceAll"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                                PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="bg"
                                CancelControlID="btnCancleMaintaince">
                            </TK1:ModalPopupExtender>
                            <TK1:ModalPopupExtender ID="ModalPopupExtenderLock" runat="server" TargetControlID="btnLock"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                                PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="bg"
                                CancelControlID="btnCancleMaintaince">
                            </TK1:ModalPopupExtender>
                            <asp:Button ID="btnActive" runat="server" CssClass="btn btn-primary" Text="Active" Width="15%" />
                            <asp:Button ID="btnActiveAll" runat="server" CssClass="btn btn-primary" Text="ActiveAll" Width="15%" />
                            <asp:Button ID="btnLock" runat="server" CssClass="btn btn-primary" Text="Lock" OnClick="BtnLockClick" Width="15%" />
                            <asp:Button ID="btnMaint" runat="server" CssClass="btn btn-primary"
                                Text="Maintenance" Width="15%" />
                            <asp:Button ID="btnMaintenanceAll" runat="server" Text="MaintenanceAll" CssClass="btn btn-primary" Width="15%" />
                        </td>
                    </tr>
                    <tr id="TrDROperation" style="display: none">
                        <td>DR Operations
                        </td>
                        <td colspan="2" class="align-right">

                            <TK1:ModalPopupExtender ID="ModalPopupExtenderSwitchOver" runat="server" TargetControlID="btnSwitchOver"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelSwitchOver"
                                PopupDragHandleControlID="panelSwitchOver" Drag="false" BackgroundCssClass="bg">
                            </TK1:ModalPopupExtender>

                            <TK1:ModalPopupExtender ID="ModalPopupExtender1" runat="server" TargetControlID="btnSwitchBack"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelSwitchBack"
                                PopupDragHandleControlID="panelSwitchBack" Drag="false" BackgroundCssClass="bg">
                            </TK1:ModalPopupExtender>

                            <TK1:ModalPopupExtender ID="ModalPopupExtenderCustomWorkflow" runat="server" TargetControlID="btnCustom"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelCustom"
                                PopupDragHandleControlID="panelCustom" Drag="false" BackgroundCssClass="bg">
                            </TK1:ModalPopupExtender>

                            <asp:Button ID="btnSwitchOver" runat="server" CssClass="btn btn-primary" Text="SwitchOver" Width="15%" OnClick="BtnSwitchOverClick" />
                            <asp:Button ID="btnSwitchBack" runat="server" CssClass="btn btn-primary" Text="SwitchBack" Width="15%" OnClick="BtnSwitchBackClick"
                                Enabled="false" />
                            <asp:Button ID="btnFailOver" runat="server" CssClass="btn btn-primary" Text="Failover" Width="15%" OnClick="BtnFailOverClick"
                                Enabled="false" />
                            <asp:Button ID="btnFailBack" runat="server" CssClass="btn btn-primary" Text="Failback" Width="15%" OnClick="BtnFailBackClick"
                                Enabled="false" />
                            <asp:Button ID="btnCustom" runat="server" CssClass="btn btn-primary" Text="Custom" Width="15%" OnClick="BtnCustomClick"
                                Enabled="false" />
                        </td>
                    </tr>
                    <tr>
                        <td>Manage Options
                        </td>
                        <td colspan="2" class="align-right">
                            <div class="btn-group">
                                <%--  <TK1:ModalPopupExtender ID="ModalPopupExtenderInfraobjectScheduleWorkflow" runat="server" TargetControlID=""
                                    RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelScheduleworkflow"
                                    PopupDragHandleControlID="panelScheduleworkflow" Drag="false" BackgroundCssClass="bg">
                                </TK1:ModalPopupExtender>--%>
                                <button data-toggle="dropdown" id="btnManageOptions" class="btn btn-primary dropdown-toggle" type="button">
                                    Options
							    <span class="caret" style="border-width: 7px 4px 0 !important; margin-left: 45px;"></span>
                                </button>
                                <ul class="dropdown-menu pull-left">
                                    <li>
                                        <asp:LinkButton ID="btnSchduleWF" runat="server" Text="Schedule Workflow" Width="100%" OnClick="btnSchduleWFClick" /></li>
                                    <li>
                                        <asp:LinkButton ID="lnkBtnViewAlert" runat="server" Text="View Alert" Width="100%" OnClick="lnkBtnViewAlertClick" /></li>
                                    <li>
                                        <asp:LinkButton ID="lnkEnableDSMonitoring" runat="server" Text="Enable Diskspace Monitoring" Width="100%" OnClick="lnkBtnEnableDSMonitoring_Click" /></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" class="align-left" visible="false">
                            <asp:DropDownList ID="ddlWorlFlow" runat="server" Visible="false" OnSelectedIndexChanged="DdlWorlFlowSelectedIndexChanged"
                                AutoPostBack="True" CssClass="selectpicker col-md-2" data-style="btn-default">
                                <asp:ListItem Value="-1">No Workflow is defined</asp:ListItem>
                            </asp:DropDownList>&nbsp;&nbsp;
                 <asp:Button ID="btnCreateLog" runat="server" CssClass="btn btn-primary" Visible="false"
                     Text="Create Log" Enabled="True" OnClick="BtnCreateLogClick" Width="16%" Style="padding: 7px 15px !important" />&nbsp;&nbsp;&nbsp;
                    <asp:Label ID="lblInfo" runat="server"></asp:Label>
                        </td>
                        <asp:Label ID="lblErrorMessage" runat="server" Visible="false" Text=""></asp:Label>
                    </tr>
                </table>
            </div>
        </div>
        <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
        </asp:Panel>
        <asp:Panel ID="pnlEnableDSMonitoring" runat="server" Width="100%" Visible="false">
            <div class="modal" style="display: block; z-index: 9999999;">
                <div class="modal-dialog" style="width: 800px;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">Enable Diskspace Monitoring</h3>
                            <asp:LinkButton ID="LnkbtnCloseEnableDSMonitoring" runat="server" ToolTip="Close window" OnClick="LnkbtnCloseEnableDSMonitoring_Click"
                                CausesValidation="False" class="close" CommandName="Close">x</asp:LinkButton>
                        </div>
                        <asp:UpdatePanel ID="pnlUpdate_enableDSMonitoring" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <br />
                                <asp:Label ID="lblEnableDSMonitoringMsg" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>
                                <br />
                                <div class="modal-body">
                                    <table id="tblCommonComponent" class="table table-white small-table margin-bottom-none" width="100%" runat="server">
                                        <thead>
                                            <tr>
                                                <th style="width: 26%">Disk Info
                                                </th>
                                                <th style="width: 37%">Production Server
                                                </th>
                                                <th style="width: 37%">DR Server
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <label>
                                                        Volume Name / Mountpoint<span class="inactive">*</span></label>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtPRVolumeName" CssClass="form-control" runat="server" CausesValidation="true" ValidationGroup="validateEnableDSMonitoring"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvPRVolumeName" runat="server" ErrorMessage="Please Enter Volume Name(s) for PR" CssClass="error"
                                                        ControlToValidate="txtPRVolumeName" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"></asp:RequiredFieldValidator>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtDRVolumeName" CssClass="form-control" runat="server" ValidationGroup="validateEnableDSMonitoring"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvDRVolumeName" runat="server" ErrorMessage="Please Enter Volume Name(s) for DR" CssClass="error"
                                                        ControlToValidate="txtDRVolumeName" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"></asp:RequiredFieldValidator>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label>
                                                        Threshold<span class="inactive">*</span></label>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtPRThreshold" CssClass="form-control" runat="server" ValidationGroup="validateEnableDSMonitoring"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvPRThreshold" runat="server" ErrorMessage="Please Enter THreshold for PR" CssClass="error"
                                                        ControlToValidate="txtPRThreshold" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"></asp:RequiredFieldValidator>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtDRThreshold" CssClass="form-control" runat="server" ValidationGroup="validateEnableDSMonitoring"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvDRThreshold" runat="server" ErrorMessage="Please Enter THreshold for DR" CssClass="error"
                                                        ControlToValidate="txtDRThreshold" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"></asp:RequiredFieldValidator>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label>Time Interval<span class="inactive">*</span></label>
                                                </td>
                                                <td>
                                                    <asp:Panel ID="PanelIntervalPR" runat="server">
                                                        <asp:RadioButtonList ID="rdTimeIntervalPR" runat="server" RepeatDirection="Horizontal"
                                                            CssClass="dropbox text-indent" OnSelectedIndexChanged="rdTimeIntervalPR_SelectedIndexChanged"
                                                            AutoPostBack="True" ValidationGroup="validateEnableDSMonitoring">
                                                            <asp:ListItem>Minute(s)</asp:ListItem>
                                                            <asp:ListItem>Hour(s)</asp:ListItem>
                                                            <asp:ListItem>Day(s)</asp:ListItem>
                                                        </asp:RadioButtonList>
                                                        <asp:Label ID="lbltimeintervalErrormessagePR" runat="server" Visible="false" Text="Select Interval Time"></asp:Label>
                                                        <asp:RequiredFieldValidator ID="rfvTimeIntervalPR" runat="server" ControlToValidate="rdTimeIntervalPR" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"
                                                            CssClass="error" ErrorMessage="Select PR Diskspace Monitor Time Interval In Either Minutes, Hours or Days"></asp:RequiredFieldValidator>
                                                    </asp:Panel>
                                                </td>
                                                <td>
                                                    <asp:Panel ID="PanelIntervalDR" runat="server" Visible="false">
                                                        <asp:RadioButtonList ID="rdTimeIntervalDR" runat="server" RepeatDirection="Horizontal"
                                                            CssClass="dropbox text-indent" OnSelectedIndexChanged="rdTimeIntervalDR_SelectedIndexChanged"
                                                            AutoPostBack="True" ValidationGroup="validateEnableDSMonitoring">
                                                            <asp:ListItem>Minute(s)</asp:ListItem>
                                                            <asp:ListItem>Hour(s)</asp:ListItem>
                                                            <asp:ListItem>Day(s)</asp:ListItem>
                                                        </asp:RadioButtonList>
                                                        <asp:Label ID="lbltimeintervalErrormessageDR" runat="server" Visible="false" Text="Select Interval Time"></asp:Label>
                                                        <asp:RequiredFieldValidator ID="rfvTimeIntervalDR" runat="server" ControlToValidate="rdTimeIntervalDR" Display="Dynamic" ValidationGroup=""
                                                            CssClass="error" ErrorMessage="Select DR Diskspace Monitor Time Interval In Either Minutes, Hours or Days"></asp:RequiredFieldValidator>
                                                    </asp:Panel>
                                                </td>
                                            </tr>
                                            <tr id="setTimeTR" runat="server" visible="false">
                                                <td>
                                                    <label>Set Time<span class="inactive">*</span></label>
                                                </td>
                                                <td>
                                                    <asp:Panel ID="Panel_MinuitePR" runat="server" Visible="False">
                                                        <div>
                                                            <asp:Label ID="lblEveryMinuitePR" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                            <asp:TextBox ID="txteveryminuitePR" runat="server" CssClass="form-control" Style="width: 15%;"
                                                                MaxLength="4" CausesValidation="True"></asp:TextBox>
                                                            <asp:Label ID="lblminutesPR" runat="server" Text="Minute(s)" CssClass="margin-right"></asp:Label>
                                                            <asp:RegularExpressionValidator ID="REVEveryminuitePR" runat="server" CssClass="error"
                                                                ControlToValidate="txteveryminuitePR" ErrorMessage="Please Enter only Numbers" Display="Dynamic"
                                                                ValidationExpression="\d+" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                            <asp:RequiredFieldValidator ID="rfvtxteveryminuitePR" runat="server" Enabled="true"
                                                                ControlToValidate="txteveryminuitePR" Display="Dynamic" ForeColor="Red"
                                                                CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                        </div>
                                                    </asp:Panel>
                                                    <asp:Panel ID="Panel_HourlyPR" runat="server" Visible="false">
                                                        <div>
                                                            <asp:Label ID="lblEveryHourlyPR" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                            <asp:TextBox ID="txteveryhourPR" runat="server" CssClass="form-control" Style="width: 15%;"
                                                                MaxLength="2"></asp:TextBox>
                                                            <asp:Label ID="lblhoursPR" runat="server" Text="Hour(s)" CssClass="margin-right"></asp:Label>
                                                            <asp:TextBox ID="txteveryhourlyminuitePR" runat="server" CssClass="form-control" Style="width: 15%;" AutoPostBack="true"
                                                                MaxLength="2" OnTextChanged="txteveryhourlyminuitePR_TextChanged"></asp:TextBox>
                                                            <asp:Label ID="lblhourlyminutesPR" runat="server" Text="Minute(s)"></asp:Label>
                                                            <asp:RequiredFieldValidator ID="rfvtxteveryhourPR" runat="server" Enabled="true" ControlToValidate="txteveryhourPR"
                                                                Display="Dynamic" Font-Size="8" ForeColor="Red" CssClass="error" ErrorMessage="Enter hours and  minutes"></asp:RequiredFieldValidator>
                                                            <asp:RequiredFieldValidator ID="rfvtxteveryhourlyminuitePR" runat="server" Enabled="true"
                                                                ControlToValidate="txteveryhourlyminuitePR" Display="Dynamic" ForeColor="Red"
                                                                CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                            <asp:RegularExpressionValidator ID="revtxteveryhourPR" runat="server" CssClass="error" Display="Dynamic"
                                                                ControlToValidate="txteveryhourPR" ErrorMessage="Enter only Numbers" ValidationExpression="\d+"
                                                                Font-Size="9pt"></asp:RegularExpressionValidator>
                                                            <asp:RangeValidator ID="rngPR" runat="server" CssClass="error" ControlToValidate="txteveryhourlyminuitePR" Display="Dynamic"
                                                                ErrorMessage="Minutes Should Be Less Than 60" Type="Integer" Enabled="false" MaximumValue="60" MinimumValue="0"></asp:RangeValidator>
                                                            <asp:RegularExpressionValidator ID="regexpfornumericPR" runat="server" CssClass="error" Enabled="false"
                                                                ControlToValidate="txteveryhourlyminuitePR" ErrorMessage="Enter Numeric Only" Display="Dynamic"
                                                                ValidationExpression="^[0-99]*$" SetFocusOnError="True" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                        </div>
                                                    </asp:Panel>
                                                    <asp:Panel ID="Panel_DailyPR" runat="server" Visible="false">
                                                        <div>
                                                            <asp:Label ID="lblEverydailyPR" runat="server" Text="Every" CssClass="margin-right"></asp:Label>
                                                            <asp:TextBox ID="txteverydailyPR" runat="server" CssClass="form-control" Style="width: 18%; margin-left: 17px;"></asp:TextBox>
                                                            <asp:Label ID="lbldaysPR" runat="server" Text="Day(s)" CssClass="margin-right"></asp:Label>
                                                            <br />
                                                            <br />
                                                            <asp:Label ID="lblstartTimePR" runat="server" CssClass="pull-left margin-right margin-top" Text="StartTime "></asp:Label>
                                                            <asp:DropDownList ID="ddlhoursPR" runat="server" CssClass="selectpicker col-xs-3" data-style="btn-default" Style="width: 16%">
                                                                <asp:ListItem>00</asp:ListItem>
                                                                <asp:ListItem>01</asp:ListItem>
                                                                <asp:ListItem>02</asp:ListItem>
                                                                <asp:ListItem>03</asp:ListItem>
                                                                <asp:ListItem>04</asp:ListItem>
                                                                <asp:ListItem>05</asp:ListItem>
                                                                <asp:ListItem>06</asp:ListItem>
                                                                <asp:ListItem>07</asp:ListItem>
                                                                <asp:ListItem>08</asp:ListItem>
                                                                <asp:ListItem>09</asp:ListItem>
                                                                <asp:ListItem>10</asp:ListItem>
                                                                <asp:ListItem>11</asp:ListItem>
                                                                <asp:ListItem>12</asp:ListItem>
                                                                <asp:ListItem>13</asp:ListItem>
                                                                <asp:ListItem>14</asp:ListItem>
                                                                <asp:ListItem>15</asp:ListItem>
                                                                <asp:ListItem>16</asp:ListItem>
                                                                <asp:ListItem>17</asp:ListItem>
                                                                <asp:ListItem>18</asp:ListItem>
                                                                <asp:ListItem>19</asp:ListItem>
                                                                <asp:ListItem>20</asp:ListItem>
                                                                <asp:ListItem>21</asp:ListItem>
                                                                <asp:ListItem>22</asp:ListItem>
                                                                <asp:ListItem>23</asp:ListItem>
                                                            </asp:DropDownList>
                                                            <asp:Label ID="lblhrPR" runat="server" CssClass="pull-left margin-right margin-top" Text="Hr"></asp:Label>
                                                            <asp:DropDownList ID="ddlminutesPR" runat="server" CssClass="selectpicker col-xs-3"
                                                                data-style="btn-default" Style="width: 16%">
                                                                <asp:ListItem>00</asp:ListItem>
                                                                <asp:ListItem>01</asp:ListItem>
                                                                <asp:ListItem>02</asp:ListItem>
                                                                <asp:ListItem>03</asp:ListItem>
                                                                <asp:ListItem>04</asp:ListItem>
                                                                <asp:ListItem>05</asp:ListItem>
                                                                <asp:ListItem>06</asp:ListItem>
                                                                <asp:ListItem>07</asp:ListItem>
                                                                <asp:ListItem>08</asp:ListItem>
                                                                <asp:ListItem>09</asp:ListItem>
                                                                <asp:ListItem>10</asp:ListItem>
                                                                <asp:ListItem>11</asp:ListItem>
                                                                <asp:ListItem>12</asp:ListItem>
                                                                <asp:ListItem>13</asp:ListItem>
                                                                <asp:ListItem>14</asp:ListItem>
                                                                <asp:ListItem>15</asp:ListItem>
                                                                <asp:ListItem>16</asp:ListItem>
                                                                <asp:ListItem>17</asp:ListItem>
                                                                <asp:ListItem>18</asp:ListItem>
                                                                <asp:ListItem>19</asp:ListItem>
                                                                <asp:ListItem>20</asp:ListItem>
                                                                <asp:ListItem>21</asp:ListItem>
                                                                <asp:ListItem>22</asp:ListItem>
                                                                <asp:ListItem>23</asp:ListItem>
                                                                <asp:ListItem>24</asp:ListItem>
                                                                <asp:ListItem>25</asp:ListItem>
                                                                <asp:ListItem>26</asp:ListItem>
                                                                <asp:ListItem>27</asp:ListItem>
                                                                <asp:ListItem>28</asp:ListItem>
                                                                <asp:ListItem>29</asp:ListItem>
                                                                <asp:ListItem>30</asp:ListItem>
                                                                <asp:ListItem>31</asp:ListItem>
                                                                <asp:ListItem>32</asp:ListItem>
                                                                <asp:ListItem>33</asp:ListItem>
                                                                <asp:ListItem>34</asp:ListItem>
                                                                <asp:ListItem>35</asp:ListItem>
                                                                <asp:ListItem>36</asp:ListItem>
                                                                <asp:ListItem>37</asp:ListItem>
                                                                <asp:ListItem>38</asp:ListItem>
                                                                <asp:ListItem>39</asp:ListItem>
                                                                <asp:ListItem>40</asp:ListItem>
                                                                <asp:ListItem>41</asp:ListItem>
                                                                <asp:ListItem>42</asp:ListItem>
                                                                <asp:ListItem>43</asp:ListItem>
                                                                <asp:ListItem>44</asp:ListItem>
                                                                <asp:ListItem>45</asp:ListItem>
                                                                <asp:ListItem>46</asp:ListItem>
                                                                <asp:ListItem>47</asp:ListItem>
                                                                <asp:ListItem>48</asp:ListItem>
                                                                <asp:ListItem>49</asp:ListItem>
                                                                <asp:ListItem>50</asp:ListItem>
                                                                <asp:ListItem>51</asp:ListItem>
                                                                <asp:ListItem>52</asp:ListItem>
                                                                <asp:ListItem>53</asp:ListItem>
                                                                <asp:ListItem>54</asp:ListItem>
                                                                <asp:ListItem>55</asp:ListItem>
                                                                <asp:ListItem>56</asp:ListItem>
                                                                <asp:ListItem>57</asp:ListItem>
                                                                <asp:ListItem>58</asp:ListItem>
                                                                <asp:ListItem>59</asp:ListItem>
                                                            </asp:DropDownList>
                                                            <asp:Label ID="lblminPR" runat="server" CssClass="pull-left margin-right margin-top" Text="Min "></asp:Label>
                                                            <asp:RegularExpressionValidator ID="revdaysPR" runat="server" CssClass="error" ControlToValidate="txteverydailyPR" Display="Dynamic"
                                                                ErrorMessage="Please Enter only Numbers" ValidationExpression="\d+"></asp:RegularExpressionValidator>
                                                            <asp:RequiredFieldValidator ID="rfveverydailyPR" runat="server" ControlToValidate="txteverydailyPR"
                                                                CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days" ForeColor="Red"></asp:RequiredFieldValidator>
                                                        </div>
                                                    </asp:Panel>

                                                    <asp:CustomValidator runat="server" Display="Dynamic" ID="cvSetTimePR" ValidationGroup="validateEnableDSMonitoring"
                                                        CssClass="error" OnServerValidate="cvSetTimePRValidator_ServerValidate"></asp:CustomValidator>

                                                </td>
                                                <td></td>
                                                <td id="tdSetTImeDR" class="col-md-5" runat="server" visible="false">
                                                    <asp:Panel ID="Panel_MinuiteDR" runat="server" Visible="false">
                                                        <div>
                                                            <asp:Label ID="lblEveryMinuiteDR" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                            <asp:TextBox ID="txteveryminuiteDR" runat="server" CssClass="form-control" Style="width: 15%;"
                                                                MaxLength="4" CausesValidation="True"></asp:TextBox>
                                                            <asp:Label ID="lblminutesDR" runat="server" Text="Minute(s)" CssClass="margin-right"></asp:Label>
                                                            <asp:RegularExpressionValidator ID="REVEveryminuiteDR" runat="server" CssClass="error"
                                                                ControlToValidate="txteveryminuiteDR" ErrorMessage="Please Enter only Numbers" Display="Dynamic"
                                                                ValidationExpression="\d+" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                            <asp:RequiredFieldValidator ID="rfvtxteveryminuiteDR" runat="server" Enabled="true"
                                                                ControlToValidate="txteveryminuiteDR" Display="Dynamic" ForeColor="Red"
                                                                CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                        </div>
                                                    </asp:Panel>
                                                    <asp:Panel ID="Panel_HourlyDR" runat="server" Visible="false">
                                                        <div>
                                                            <asp:Label ID="lblEveryHourlyDR" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                            <asp:TextBox ID="txteveryhourDR" runat="server" CssClass="form-control" Style="width: 15%;"
                                                                MaxLength="2"></asp:TextBox>
                                                            <asp:Label ID="lblhoursDR" runat="server" Text="Hour(s)" CssClass="margin-right"></asp:Label>
                                                            <asp:TextBox ID="txteveryhourlyminuiteDR" runat="server" CssClass="form-control" Style="width: 15%;" AutoPostBack="true"
                                                                MaxLength="2" OnTextChanged="txteveryhourlyminuiteDR_TextChanged"></asp:TextBox>
                                                            <asp:Label ID="lblhourlyminutesDR" runat="server" Text="Minute(s)"></asp:Label>
                                                            <asp:RequiredFieldValidator ID="rfvtxteveryhourDR" runat="server" Enabled="true" ControlToValidate="txteveryhourDR"
                                                                Display="Dynamic" Font-Size="8" ForeColor="Red" CssClass="error" ErrorMessage="Enter hours and  minutes"></asp:RequiredFieldValidator>
                                                            <asp:RequiredFieldValidator ID="rfvtxteveryhourlyminuiteDR" runat="server" Enabled="true"
                                                                ControlToValidate="txteveryhourlyminuiteDR" Display="Dynamic" ForeColor="Red"
                                                                CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                            <asp:RegularExpressionValidator ID="revtxteveryhourDR" runat="server" CssClass="error" Display="Dynamic"
                                                                ControlToValidate="txteveryhourDR" ErrorMessage="Enter only Numbers" ValidationExpression="\d+"
                                                                Font-Size="9pt"></asp:RegularExpressionValidator>
                                                            <asp:RangeValidator ID="rngDR" runat="server" CssClass="error" ControlToValidate="txteveryhourlyminuiteDR" Display="Dynamic"
                                                                ErrorMessage="Minutes Should Be Less Than 60" Type="Integer" Enabled="false" MaximumValue="60" MinimumValue="0"></asp:RangeValidator>
                                                            <asp:RegularExpressionValidator ID="regexpfornumericDR" runat="server" CssClass="error" Enabled="false"
                                                                ControlToValidate="txteveryhourlyminuiteDR" ErrorMessage="Enter Numeric Only" Display="Dynamic"
                                                                ValidationExpression="^[0-99]*$" SetFocusOnError="True" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                        </div>
                                                    </asp:Panel>
                                                    <asp:Panel ID="Panel_DailyDR" runat="server" Visible="false">
                                                        <div>
                                                            <asp:Label ID="lblEverydailyDR" runat="server" Text="Every" CssClass="margin-right"></asp:Label>
                                                            <asp:TextBox ID="txteverydailyDR" runat="server" CssClass="form-control" Style="width: 16%; margin-left: 17px;"></asp:TextBox>
                                                            <asp:Label ID="lbldaysDR" runat="server" Text="Day(s)" CssClass="margin-right"></asp:Label>
                                                            <br />
                                                            <br />
                                                            <asp:Label ID="lblstartTimeDR" runat="server" CssClass="pull-left margin-right margin-top" Text="StartTime "></asp:Label>
                                                            <asp:DropDownList ID="ddlhoursDR" runat="server" CssClass="selectpicker col-xs-3" data-style="btn-default" Style="width: 16%">
                                                                <asp:ListItem>00</asp:ListItem>
                                                                <asp:ListItem>01</asp:ListItem>
                                                                <asp:ListItem>02</asp:ListItem>
                                                                <asp:ListItem>03</asp:ListItem>
                                                                <asp:ListItem>04</asp:ListItem>
                                                                <asp:ListItem>05</asp:ListItem>
                                                                <asp:ListItem>06</asp:ListItem>
                                                                <asp:ListItem>07</asp:ListItem>
                                                                <asp:ListItem>08</asp:ListItem>
                                                                <asp:ListItem>09</asp:ListItem>
                                                                <asp:ListItem>10</asp:ListItem>
                                                                <asp:ListItem>11</asp:ListItem>
                                                                <asp:ListItem>12</asp:ListItem>
                                                                <asp:ListItem>13</asp:ListItem>
                                                                <asp:ListItem>14</asp:ListItem>
                                                                <asp:ListItem>15</asp:ListItem>
                                                                <asp:ListItem>16</asp:ListItem>
                                                                <asp:ListItem>17</asp:ListItem>
                                                                <asp:ListItem>18</asp:ListItem>
                                                                <asp:ListItem>19</asp:ListItem>
                                                                <asp:ListItem>20</asp:ListItem>
                                                                <asp:ListItem>21</asp:ListItem>
                                                                <asp:ListItem>22</asp:ListItem>
                                                                <asp:ListItem>23</asp:ListItem>
                                                            </asp:DropDownList>
                                                            <asp:Label ID="lblhrDR" runat="server" CssClass="pull-left margin-right margin-top" Text="Hr"></asp:Label>
                                                            <asp:DropDownList ID="ddlminutesDR" runat="server" CssClass="selectpicker col-xs-3"
                                                                data-style="btn-default" Style="width: 16%">
                                                                <asp:ListItem>00</asp:ListItem>
                                                                <asp:ListItem>01</asp:ListItem>
                                                                <asp:ListItem>02</asp:ListItem>
                                                                <asp:ListItem>03</asp:ListItem>
                                                                <asp:ListItem>04</asp:ListItem>
                                                                <asp:ListItem>05</asp:ListItem>
                                                                <asp:ListItem>06</asp:ListItem>
                                                                <asp:ListItem>07</asp:ListItem>
                                                                <asp:ListItem>08</asp:ListItem>
                                                                <asp:ListItem>09</asp:ListItem>
                                                                <asp:ListItem>10</asp:ListItem>
                                                                <asp:ListItem>11</asp:ListItem>
                                                                <asp:ListItem>12</asp:ListItem>
                                                                <asp:ListItem>13</asp:ListItem>
                                                                <asp:ListItem>14</asp:ListItem>
                                                                <asp:ListItem>15</asp:ListItem>
                                                                <asp:ListItem>16</asp:ListItem>
                                                                <asp:ListItem>17</asp:ListItem>
                                                                <asp:ListItem>18</asp:ListItem>
                                                                <asp:ListItem>19</asp:ListItem>
                                                                <asp:ListItem>20</asp:ListItem>
                                                                <asp:ListItem>21</asp:ListItem>
                                                                <asp:ListItem>22</asp:ListItem>
                                                                <asp:ListItem>23</asp:ListItem>
                                                                <asp:ListItem>24</asp:ListItem>
                                                                <asp:ListItem>25</asp:ListItem>
                                                                <asp:ListItem>26</asp:ListItem>
                                                                <asp:ListItem>27</asp:ListItem>
                                                                <asp:ListItem>28</asp:ListItem>
                                                                <asp:ListItem>29</asp:ListItem>
                                                                <asp:ListItem>30</asp:ListItem>
                                                                <asp:ListItem>31</asp:ListItem>
                                                                <asp:ListItem>32</asp:ListItem>
                                                                <asp:ListItem>33</asp:ListItem>
                                                                <asp:ListItem>34</asp:ListItem>
                                                                <asp:ListItem>35</asp:ListItem>
                                                                <asp:ListItem>36</asp:ListItem>
                                                                <asp:ListItem>37</asp:ListItem>
                                                                <asp:ListItem>38</asp:ListItem>
                                                                <asp:ListItem>39</asp:ListItem>
                                                                <asp:ListItem>40</asp:ListItem>
                                                                <asp:ListItem>41</asp:ListItem>
                                                                <asp:ListItem>42</asp:ListItem>
                                                                <asp:ListItem>43</asp:ListItem>
                                                                <asp:ListItem>44</asp:ListItem>
                                                                <asp:ListItem>45</asp:ListItem>
                                                                <asp:ListItem>46</asp:ListItem>
                                                                <asp:ListItem>47</asp:ListItem>
                                                                <asp:ListItem>48</asp:ListItem>
                                                                <asp:ListItem>49</asp:ListItem>
                                                                <asp:ListItem>50</asp:ListItem>
                                                                <asp:ListItem>51</asp:ListItem>
                                                                <asp:ListItem>52</asp:ListItem>
                                                                <asp:ListItem>53</asp:ListItem>
                                                                <asp:ListItem>54</asp:ListItem>
                                                                <asp:ListItem>55</asp:ListItem>
                                                                <asp:ListItem>56</asp:ListItem>
                                                                <asp:ListItem>57</asp:ListItem>
                                                                <asp:ListItem>58</asp:ListItem>
                                                                <asp:ListItem>59</asp:ListItem>
                                                            </asp:DropDownList>
                                                            <asp:Label ID="lblminDR" runat="server" CssClass="pull-left margin-right margin-top" Text="Min "></asp:Label>
                                                            <asp:RegularExpressionValidator ID="revdaysDR" runat="server" CssClass="error" ControlToValidate="txteverydailyDR" Display="Dynamic"
                                                                ErrorMessage="Please Enter only Numbers" ValidationExpression="\d+"></asp:RegularExpressionValidator>
                                                            <asp:RequiredFieldValidator ID="rfveverydailyDR" runat="server" ControlToValidate="txteverydailyDR"
                                                                CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days" ForeColor="Red"></asp:RequiredFieldValidator>
                                                        </div>
                                                    </asp:Panel>

                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                                <div class="modal-footer" style="padding: 5px;">
                                    <div class="text-right">
                                        <asp:Button ID="btnSubmit" runat="server" Text="Submit" CssClass="btn btn-primary" Width="12%" Style="font-size: 12px"
                                            OnClick="BtnSubmit_Click" ValidationGroup="validateEnableDSMonitoring" Visible="true" />
                                        <asp:Button ID="btnDelete" runat="server" Text="Delete" CssClass="btn btn-primary" Width="12%" Style="font-size: 12px"
                                            OnClick="BtnDelete_Click" ValidationGroup="validateEnableDSMonitoring" Visible="false" />
                                        <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="btnDelete"
                                            ConfirmText="" OnClientCancel="CancelClick">
                                        </TK1:ConfirmButtonExtender>
                                        <asp:Button ID="btnClose" runat="server" Text="Close" CssClass="btn btn-default" Style="font-size: 12px"
                                            Width="12%" CausesValidation="false" OnClick="btnClose_Click" />
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <asp:Panel ID="panelMaintenance" runat="server" Style="display: none" Width="500" Height="480">
            <div class="modal" style="display: block;">
                <div class="modal-dialog" style="width: 500px;">
                    <div class="modal-content  widget-body-white">
                        <div class="modal-header">
                            <asp:Label ID="lblmaintenance" runat="server"></asp:Label>
                            <asp:LinkButton ID="LinkButton5" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                        </div>
                        <div class="modal-body">
                            <div class="">
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label ">Reason <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <textarea id="txtReason" rows="2" cols="40" class="form-control" style="width: 85%"></textarea>
                                            <span class="inactive">*</span>
                                            <asp:Label ID="lblReason" runat="server"></asp:Label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label ">Mode <span class="inactive">*</span></label>

                                        <div class="col-md-9">
                                            <select id="ddlMode" class="col-md-8" style="width: 85% !important; padding-right: 5px; padding-left: 5px;">
                                                <option value="0">-Select Mode-</option>
                                                <%--<option value="1">Auto</option>--%>
                                                <option value="2">Manual</option>
                                            </select>
                                            <span class="inactive">*</span>
                                            <asp:Label ID="lblMode" runat="server"></asp:Label>
                                        </div>
                                    </div>
                                    <div class="form-group" id="idUnClock" style="display: none">
                                        <label class="col-md-3 control-label ">Unlock Time</label>
                                        <div class="col-md-9">
                                            <input type="text" name="txtTime" id="txtTime" readonly="true" class="form-control" style="width: 85%;" />

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="col-md-6">
                            </div>
                            <div class="col-md-11">
                                <input id="btnActionSetSave" type="button" value="OK" class="btn btn-primary" style="width: 20%" />
                                <input id="btnCancleMaintaince" type="button" value="Cancel" class="btn btn-default" style="width: 20%; margin-right: 10px;" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </asp:Panel>
    </div>


    <script src="../Script/infraobjectdbMaintenance.js" type="text/javascript"></script>
    <script src="../Script/validation.js"></script>
    <script src="../Script/Helper.js"></script>
</asp:Content>

