﻿using CP.Common.Base;
using CP.Common.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;


namespace CP.Common
{
    public class eBDRProfileMonitoring : BaseEntity
    {
        [DataMember]
        public string Profile_Name { get; set; }

        [DataMember]
        public int ProfileId { get; set; }

        [DataMember]
        public int InfraobjectId { get; set; }

        [DataMember]
        public string Source_Host { get; set; }

        [DataMember]
        public string Target_Host { get; set; }

        [DataMember]
        public string SourceVM_Name { get; set; }

        [DataMember]
        public string TargetVM_Name { get; set; }

        [DataMember]
        public string OsType { get; set; }

        [DataMember]
        public int Health { get; set; }

        [DataMember]
        public string RPO { get; set; }

        [DataMember]
        public string Datalag { get; set; }

        [DataMember]
        public string Source_Type { get; set; }

        [DataMember]
        public string Target_Type { get; set; }

        [DataMember]
        public int Repli_Status { get; set; }

        [DataMember]
        public string Repli_Estimation { get; set; }

        [DataMember]
        public int Status { get; set; }

        [DataMember]
        public bool IsMigration { get; set; }

        [DataMember]
        public int currentReplicationCount { get; set; }

    }
}
