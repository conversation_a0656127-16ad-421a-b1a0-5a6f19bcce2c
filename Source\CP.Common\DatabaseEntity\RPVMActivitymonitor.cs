﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    public class RPVMActivitymonitor : BaseEntity
    {

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string RecoveryActivityName { get; set; }

        [DataMember]
        public string Copy { get; set; }
        [DataMember]
        public string Snapshot { get; set; }
        [DataMember]
        public string ActivityStart { get; set; }
        [DataMember]
        public string ActivityStatus { get; set; }
        [DataMember]
        public string Progress { get; set; }

        [DataMember]
        public string CreateDate { get; set; }

        [DataMember]
        public string UpdateDate { get; set; }
    }
}
