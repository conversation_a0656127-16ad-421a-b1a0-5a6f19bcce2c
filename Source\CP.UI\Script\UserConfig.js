﻿$(document).ready(function () {


    $('[id$=dvgrp2]').hide();
    $('[id$=domainUserDiv]').hide();
    $('[id$=lblerrorMsg]').hide();
    $('[id$=errordomainsearch]').show();
    //jQuery(function () {
    //    jQuery('[id$=combobox1]').combobox([
    //    ]);
    //    jQuery('[id$=txtDomainUserNew]').combobox([
    //    ]);

    //});

    $('[id$=combobox1]').parent().find(".combobox_button").remove();
    $('[id$=combobox1]').parent().find(".combobox_selector").remove();
    $('[id$=combobox1]').unwrap();
    $('[id$=combobox1]').combobox([]);

    $('[id$=txtDomainUserNew]').parent().find(".combobox_button").remove();
    $('[id$=txtDomainUserNew]').parent().find(".combobox_selector").remove();
    $('[id$=txtDomainUserNew]').unwrap();
    $('[id$=txtDomainUserNew]').combobox([]);

    $(document).on("click", "#btndomainname", function () {
        $('[id$=dvgrp3]').hide();
        $('[id$=dvgrp2]').show();
        binddropdown();

    });

    $(document).on("click", "[id$=btnsearch]", function () {
        var type = $('input[name$=rdoLogin]:checked').val();

        if ($('[id$=txtDomainUserName]').val() == "" && type == "Group") {
            $('[id$=errordomainsearch]').show();
            $('[id$=errordomainsearch]').html("Enter Group Name");
            return false;
        }
        if ($('[id$=txtDomainUserName]').val() == "" && type == "Individual") {
            $('[id$=errordomainsearch]').show();
            $('[id$=errordomainsearch]').html("Enter User Name");
            return false;
        }
        $('[id$=errordomainsearch]').hide();
        $('[id$=divDomainUser]').hide();
        $('[id$=domainUserDiv]').show();
        $('[id$=lblerrorMsg]').hide();

          if (type == "Group") {
            $('[id$=txtDomainUser]').attr('placeholder', "Enter Group Name");
            $('[id$=lblDomainUserName]').html("Enter Group Name");

            bindGroupUser();
        } else {
            $('[id$=txtDomainUser]').attr('placeholder', "Enter User Name");
            $('[id$=lblDomainUserName]').html("Enter User Name");
            bindDomainUser();
        }
       
        var myCookie = getCookie('myCookie');
        $.removeCookie('myCookie');
    });


});
$(document).on("change", "[id$=txtDomainUser]", function () {
    var DomainName = "";
    var UserName = "";
    var slash = "\\";

    if ($('[id$=combobox2]').val() != "")
        DomainName = $('[id$=combobox2]').val();
    else if ($('[id$=combobox1]').val() != "")
        DomainName = $('[id$=combobox1]').val();
    if ($('[id$=txtDomainUser]').val() != "")
        UserName = $('[id$=txtDomainUser]').val();
    else if ($('[id$=txtDomainUserNew]').val() != "")
        UserName = $('[id$=txtDomainUserNew]').val();
    $('[id$=txtLogin]').val(DomainName + slash + UserName);
    $('[id$=txtUserName]').val(UserName);
   var getfun = $('[id$=txtLogin]').attr("onblur");
    eval(getfun);
    getAdIndividualUsers();

});

$(document).on("change", "[id$=txtDomainUserNew]", function () {
    var DomainName = "";
    var UserName = "";
    var slash = "\\";

    if ($('[id$=combobox2]').val() != "")
        DomainName = $('[id$=combobox2]').val();
    else if ($('[id$=combobox1]').val() != "")
        DomainName = $('[id$=combobox1]').val();
    if ($('[id$=txtDomainUser]').val() != "")
        UserName = $('[id$=txtDomainUser]').val();
    else if ($('[id$=txtDomainUserNew]').val() != "")
        UserName = $('[id$=txtDomainUserNew]').val();
    $('[id$=txtLogin]').val(DomainName + slash + UserName);
    $('[id$=txtUserName]').val(UserName);
    var getfun = $('[id$=txtLogin]').attr("onblur");
    eval(getfun);
    getAdIndividualUsers();

});
function getAdIndividualUsers() {

    if ($('[id$=txtDomainUser]').val() != "")
        values = $('[id$=txtDomainUser]').val();
    else if ($('[id$=txtDomainUserNew]').val() != "")
        values = $('[id$=txtDomainUserNew]').val();

    $.ajax({
        type: "POST",
        url: "UserConfiguration.aspx/AdIndividualUsers",
        data: "{'values':'" + values + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function Success(data) {
            getIndividualValue(data.d);
        },
        cache: false

    });
}
function getIndividualValue(value) {

    var users = value;
    var type = $('input[name$=rdoLogin]:checked').val();

    if (type == "Individual") {
        $('[id$=txtUserName]').val(value);
    }
}

function binddropdown() {
    $.ajax({
        type: "POST",
        url: "UserConfiguration.aspx/DiscoverDomains",
        data: "{}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function Success(data) {
            getBindValue(data.d);
        }

    });
}
function getDomainUser(value) {

    var text = [];
    if (value != null) {
        var data = value.split(",");
        for (var i = 0; i < data.length; i++) {
            text.push(data[i]);

        }
        setCookie('myCookie', text, 7)
        var myCookie = getCookie('myCookie');

        //jQuery(function () {
        //    jQuery('[id$=txtDomainUser]').combobox(
        //       text);
        //});


        $('[id$=txtDomainUserNew]').parent().find(".combobox_button").remove();
        $('[id$=txtDomainUserNew]').parent().find(".combobox_selector").remove();
        $('[id$=txtDomainUserNew]').unwrap();
        $('[id$=txtDomainUserNew]').combobox(text);

        $('[id$=btnok]').attr('disabled', false);
    }
    else if (('[id$=txtDomainUserNew]').length != 0) {
        $('[id$=btnok]').attr('disabled', false);
    }

    else {
        if (text.length == 0)
            $('[id$=btnok]').attr('disabled', true);
        else
            $('[id$=btnok]').attr('disabled', false);
    }

}

//function getBindValue(value) {
//    var text = [];
//    if (value != null) {
//        var data = value.split(",");
//        for (var i = 0; i < data.length; i++) {
//            text.push(data[i]);
//        }
//        jQuery(function () {
//            jQuery('[id$=combobox2]').combobox(
//                text);
//        });

//    }
//}


//function getBindValue(value) {
//    if (value != null) {
//        var data = value.split(":");
//        for (var i = 0; i < data.length; i++) {
//            var volume = data[i].split(",");
//            var text = volume[0];
//            if (value == "") {
//                $('[id$=lblerrorMsg]').show();
//            }
//            else {
//                $('[id$=lblerrorMsg]').hide();
//            }

//            jQuery(function () {
//                jQuery('[id$=combobox2]').combobox([
//                        text
//                ]);
//                $('[id$=combobox2]').val(volume[0]);
//            });
//        }
//    }
//}

function getBindValue(value) {
    var domain = [];
    if (value != null) {
        var data = value.split(",");
        for (var i = 0; i < data.length; i++) {
            var volume = data[i].split(",");
            var text = volume[0];
            domain.push(text)

            if (value == "") {
                $('[id$=lblerrorMsg]').show();
            }
            else {
                $('[id$=lblerrorMsg]').hide();
            }

            //jQuery(function () {
            //    jQuery('[id$=combobox2]').combobox([
            //            text
            //    ]);
            //    $('[id$=combobox2]').val(domain);
            //});
            //jQuery(function () {
            //    jQuery('[id$=combobox2]').combobox(
            //       domain);
            //});

            $('[id$=combobox2]').parent().find(".combobox_button").remove();
            $('[id$=combobox2]').parent().find(".combobox_selector").remove();
            $('[id$=combobox2]').unwrap();
            $('[id$=combobox2]').combobox(domain);

        }
    }
}

function check() {
    if (('[id$=txtDomainUserNew]').length != 0) {
        $('[id$=btnok]').attr('disabled', false);
    }
    else
        $('[id$=btnok]').attr('disabled', true);
}

function HideErrorMsg() {
    if ($('[id$=combobox1]').val() != "") {
        $('[id$=lblerrorMsg]').hide();
    }
    else if ($('[id$=combobox2]').val() != "") {
        $('[id$=lblerrorMsg]').hide();
    }
    else
        $('[id$=lblerrorMsg]').show();
}
function bindGroupUser() {

    var values = $('[id$=txtDomainUserName]').val();

    $.ajax({
        type: "POST",
        url: "UserConfiguration.aspx/GetGroupUsers",
        data: "{'values':'" + values + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function Success(data) {

            bindgroupUser1(data.d)
        },
        cache: false

    });
}
function bindgroupUser1(value) {
    // $('[id$=errordomainsearch]').hide();

    // var DomainUser = document.getElementById("txtDomainUser") = 
    //  document.getElementById("txtDomainUserNew").value = "";
    $('[id$=txtDomainUser]').parent().find(".combobox_button").remove();
    $('[id$=txtDomainUser]').parent().find(".combobox_selector").remove();
    $('[id$=txtDomainUser]').unwrap();
    $('[id$=txtDomainUser]').val('');
    $('[id$=txtLogin]').val('');
    $('[id$=txtUserName]').val('');
    if (value != null) {

        var data = value;

        if (data.length > 0) {
            $('[id$=txtDomainUser]').combobox(data);
        }
        else {
            $('[id$=errordomainsearch]').show();
            $('[id$=errordomainsearch]').html("Invalid Group Name");
            $('[id$=txtDomainUser]').combobox([]);

        }


    }



}

function bindDomainUser() {
    var values = $('[id$=txtDomainUserName]').val();
    if ($('[id$=combobox2]').val() != null || $('[id$=combobox2]').val() != "")
        values = values + "," + $('[id$=combobox2]').val();
    else if ($('[id$=combobox1]').val() != null || $('[id$=combobox1]').val() != "")
        values = values + "," + $('[id$=combobox1]').val();


    $.ajax({
        type: "POST",
        url: "UserConfiguration.aspx/GetDomainUsers",
        data: "{'values':'" + values + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function Success(data) {
            getDomainUser(data.d);
        },
        cache: false

    });

}

function getDomainUser(value) {

    var text = [];
    if ((value != null)&&(value !="")) {
        var data = value.split(",");
        for (var i = 0; i < data.length; i++) {
            text.push(data[i]);

        }
        setCookie('myCookie', text, 7)
        var myCookie = getCookie('myCookie');

        //jQuery(function () {
        //    jQuery('[id$=txtDomainUser]').combobox(
        //       text);
        //});

        //$(".combobox_button, .combobox_selector").remove();
        //$('[id$=txtDomainUser]').unwrap();
        //$('[id$=txtDomainUser]').combobox(text);
        $('[id$=txtDomainUser]').parent().find(".combobox_button").remove();
        $('[id$=txtDomainUser]').parent().find(".combobox_selector").remove();
        $('[id$=txtDomainUser]').unwrap();
        $('[id$=txtDomainUser]').val('');
        $('[id$=txtLogin]').val('');
        $('[id$=txtUserName]').val('');
        $('[id$=txtDomainUser]').combobox(text);

    }
    else if ((value == null) || (value == "")) {
        $('[id$=txtDomainUser]').parent().find(".combobox_button").remove();
        $('[id$=txtDomainUser]').parent().find(".combobox_selector").remove();
        $('[id$=txtDomainUser]').unwrap();
        $('[id$=txtDomainUser]').val('');
        $('[id$=txtLogin]').val('');
        $('[id$=txtUserName]').val('');
        $('[id$=errordomainsearch]').show();
        $('[id$=errordomainsearch]').html("Invalid User Name");
        $('[id$=txtDomainUser]').combobox([]);

    }
}
function setCookie(c_name, value, exdays) {
    var exdate = new Date();
    exdate.setDate(exdate.getDate() + exdays);
    var c_value = escape(value) + ((exdays == null) ? "" : "; expires=" + exdate.toUTCString());
    document.cookie = c_name + "=" + c_value;
}


function getCookie(c_name) {
    var i, x, y, ARRcookies = document.cookie.split(";");
    for (i = 0; i < ARRcookies.length; i++) {
        x = ARRcookies[i].substr(0, ARRcookies[i].indexOf("="));
        y = ARRcookies[i].substr(ARRcookies[i].indexOf("=") + 1);
        x = x.replace(/^\s+|\s+$/g, "");
        if (x == c_name) {
            return unescape(y);
        }
    }
}


function clearControl(control) {
    if (control.id == "ctl00_cphBody_txtLogin") {
        if (control.value != "") {
            control.value = getOrignalData(control, $('#ctl00_cphBody_hdfStaticGuid').val());
        }
    }
    $('[id$=txtPwd]').val("");
    $('[id$=txtConPwd]').val("");
}


function getHashData(control) {
    control.value = genrateUserNameHash(control, $('#ctl00_cphBody_hdfStaticGuid').val());
}
