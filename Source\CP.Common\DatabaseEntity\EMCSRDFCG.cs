﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "EMCSRDFCG", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class EMCSRDFCG : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _replicationBase = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string SYMRDFBinPath { get; set; }

        [DataMember]
        public string GroupName { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }
        }

        #endregion Properties
    }
}
