﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CustomWorkflow.aspx.cs"
    Inherits="CP.UI.Admin.CustomWorkflow" %>

<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<!DOCTYPE html >
<html>
<head id="Head1" runat="server">
    <title>Continuity Patrol :: Custom Workflow</title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script type="text/javascript" src="../Script/jquery-ui.min.js "></script>
    <script src="../Script/less.min.js"></script>

</head>
<body>
    <form id="form1" runat="server">
        <div>
            <asp:ScriptManager ID="ScriptManager1" runat="server" EnablePartialRendering="True">
            </asp:ScriptManager>

            <script type="text/javascript">
                var xPos, yPos;
                Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
                Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
                function BeginRequestHandler(sender, args) {
                    if ($("#SwitchScrollbar").length > 0) {
                        xPos = window.$get('SwitchScrollbar').scrollLeft;
                        yPos = window.$get('SwitchScrollbar').scrollTop;
                    }
                }
                function EndRequestHandler(sender, args) {
                    if ($("#SwitchScrollbar").length > 0) {
                        window.$get('SwitchScrollbar').scrollLeft = xPos;
                        window.$get('SwitchScrollbar').scrollTop = yPos;
                    }
                }
            </script>

            <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
                <Triggers>
                    <asp:AsyncPostBackTrigger ControlID="Timerworkflow" EventName="Tick" />
                </Triggers>
                <ContentTemplate>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div id="popupmessage" class="success message" runat="server" visible="false">
                                    <asp:Label ID="lblMsg" runat="server" Text=""></asp:Label>
                                </div>
                                <div id="errorMsgabort" class="error message" runat="server" visible="false">
                                    <asp:Label ID="lblabort" runat="server" Text=""></asp:Label>
                                </div>
                                <asp:Timer runat="server" ID="Timerworkflow" Interval="5000" OnTick="UpdateTimerTick"
                                    Enabled="false" />
                                <asp:Panel ID="PanelExceptionCondition" runat="server" Style="display: none">
                                    <asp:UpdatePanel ID="Updatepanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                        <ContentTemplate>
                                            <div class="modal" style="display: block;">
                                                <div class="modal-dialog" style="width: 450px;">
                                                    <div class="modal-content  widget-body-white">
                                                        <div class="modal-header">
                                                            <h3 class="modal-title">Alert</h3>
                                                        </div>
                                                        <div class="modal-body">

                                                            <div class="row">
                                                                <div class="col-xs-12 form-horizontal uniformjs">
                                                                    <div id="popuperrormessage" class="error message" runat="server">
                                                                        <asp:Label ID="lblerrorMsg" runat="server" Text=""></asp:Label>
                                                                    </div>
                                                                    <asp:Button ID="BtnSkip" runat="server" Text="Skip" CssClass="btn btn-primary" Width="15%" OnClick="BtnSkipClick"
                                                                        ValidationGroup="vgexception" />
                                                                    <asp:Button ID="BtnRetry" runat="server" Text="Retry" CssClass="btn btn-primary" Width="15%" OnClick="BtnRetryClick"
                                                                        ValidationGroup="vgexception" />
                                                                    <asp:Button ID="BtnAbort" runat="server" Text="Abort" CssClass="btn btn-default" Width="15%" OnClick="BtnAbortClick"
                                                                        ValidationGroup="vgexception" />
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </asp:Panel>
                                <%--<asp:Panel runat="server" ID="PopAuthenticationFailed">
               <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                        <ContentTemplate>
                         <div id="Div1">
              <div class="modal-window " style="width:280px; top:10px; left:-130px;">
                               <div class="block-content margin-right red">
                               <div class="block-header">
                               <h1>Authentication Failed</h1>
                               </div>
                               <div id="Div2" class="error message" runat="server">
                             <asp:Label ID="Label1" runat="server" Text="Password is incorrect, Try again"></asp:Label>
                             </div>
                           <div class="block-footer align-right">
                               <asp:Button ID="BtnAuthenticationFail" CssClass="buttonblue" runat="server" Text="Ok" OnClick="BtnAuthenticationFailClick" />
                           </div>
        </div></div></div>
                                 </ContentTemplate>
                    </asp:UpdatePanel>
          </asp:Panel>
          <asp:Panel runat="server" ID="PanelMode">
               <asp:UpdatePanel ID="Updatepanel5" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                        <ContentTemplate>
                         <div id="Div3">
              <div class="modal-window " style="width:280px; top:10px; left:-130px;">
                               <div class="block-content margin-right red">
                               <div class="block-header">
                               <h1>Alert</h1>
                               </div>
                               <div id="Div4" class="error message" runat="server">
                             <asp:Label ID="Label2" runat="server" Text="Please choose atleast one Mode"></asp:Label>
                             </div>
                           <div class="block-footer align-right">
                               <asp:Button ID="BtnMode" CssClass="buttonblue" runat="server" Text="Ok" OnClick="BtnModeClick" />
                           </div>
        </div></div></div>
                                 </ContentTemplate>
                    </asp:UpdatePanel>
          </asp:Panel>--%>
                                <asp:Panel ID="PanelAuthenticate" runat="server" Style="display: none">
                                    <asp:UpdatePanel ID="Updatepanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                        <ContentTemplate>
                                            <div class="modal" style="display: block;">
                                                <div class="modal-dialog" style="width: 450px;">
                                                    <div class="modal-content  widget-body-white">
                                                        <div class="modal-header">
                                                            <h3 class="modal-title">Authenticate</h3>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="row">
                                                                <div class="col-xs-12 form-horizontal uniformjs">
                                                                    <div runat="server" id="divPsdErrMsg" class="message error" visible="false">
                                                                        <asp:Label ID="lblPsdErrorMsg" Visible="False" runat="server" ForeColor="Red"></asp:Label>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label class="col-xs-3 control-label ">
                                                                            <asp:Label ID="lblpassword" runat="server">Password</asp:Label></label>
                                                                        <div class="col-xs-9">
                                                                            <asp:TextBox ID="txtpassword" Text="" autocomplete="off" TextMode="Password" runat="server" CssClass="form-control"></asp:TextBox>
                                                                        </div>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <div class="col-xs-3">
                                                                        </div>
                                                                        <div class="col-xs-9">
                                                                            <asp:RadioButtonList ID="RadioWorkflowExecution" runat="server" RepeatDirection="Horizontal">
                                                                                <asp:ListItem Selected="True" Value="1">Auto</asp:ListItem>
                                                                                <asp:ListItem Value="2">Steps</asp:ListItem>
                                                                            </asp:RadioButtonList>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <asp:Button ID="btnPasswordOk" CssClass="btn btn-primary" runat="server" Width="15%" Text="Ok" OnClick="BtnPasswordOkClick" />
                                                            <asp:Button ID="btnClose" CssClass="btn btn-default" runat="server" Width="15%" Text="Close" OnClick="BtnCloseClick" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ContentTemplate>
                                        <Triggers>
                                            <asp:AsyncPostBackTrigger ControlID="BtnStartCustom" EventName="Click" />
                                        </Triggers>
                                    </asp:UpdatePanel>
                                </asp:Panel>
                                <asp:Panel ID="PanelNextpopup" runat="server" Style="display: none">
                                    <asp:UpdatePanel ID="Updatepanel3" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                        <ContentTemplate>
                                            <div id="nextpop">
                                                <div class="modal-window " style="width: 300px; top: -50px; left: -140px;">
                                                    <div class="block-content margin-right">
                                                        <div class="block-header">
                                                            <h1>Action Completed Successfully</h1>
                                                        </div>
                                                        <center>
                                                            <div class="bold align-left  padding-5">
                                                                Current Action:
                                                            </div>
                                                            <div class="align-left padding-5">
                                                                <asp:Label ID="lblCurrent" runat="server"></asp:Label>
                                                            </div>
                                                            <hr />
                                                            <div class="clear">
                                                                &nbsp;
                                                            </div>
                                                            <div class="grid-18">
                                                                <asp:Button ID="BtnNext" runat="server" Text="Next" CssClass="buttonblue" OnClick="BtnNextClick"
                                                                    ValidationGroup="vlaNext" />                                                                
                                                                <asp:Button ID="BtnAbortAction" runat="server" Text="Abort" CssClass="btnabort" OnClick="BtnAbortClick"
                                                                    ValidationGroup="vgexception" />
                                                            </div>
                                                            <hr />
                                                            <div class="bold align-left">
                                                                Next Action:
                                                            </div>
                                                            <div class="align-left padding-5">
                                                                <asp:Label ID="lblNext" runat="server"></asp:Label>
                                                            </div>
                                                            <div class="clear">
                                                                &nbsp;
                                                            </div>
                                                        </center>
                                                    </div>
                                                </div>
                                            </div>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </asp:Panel>
                                <div class="form-group">
                                    <label class="col-md-3 control-label ">Custom workflow </label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="dllCustomWorkflow" runat="server" AutoPostBack="true" OnSelectedIndexChanged="DllCustomWorkflowSelectedIndexChanged"
                                            CssClass="selectpicker col-md-6" data-style="btn-default">
                                        </asp:DropDownList>
                                    </div>
                                </div>

                                <asp:Panel ID="PanelCustomWorkflow" runat="server">
                                    <asp:ListView ID="lvworkflow" runat="server" OnItemCommand="LvcustomItemCommand">
                                        <LayoutTemplate>
                                            <div class="widget widget-scroll" data-scroll-height="280px">
                                                <div class="widget-body">
                                                    <div class="row">
                                                        <div class="col-xs-2">&nbsp;</div>
                                                        <div class="col-xs-8">
                                                            <asp:Label ID="lblWorkflowName" runat="server" Visible="false">Custom Workflow</asp:Label>
                                                            <div class="widget-timeline">
                                                                <ul class="list-timeline">
                                                                    <asp:PlaceHolder ID="itemPlaceholder" runat="server"></asp:PlaceHolder>
                                                                </ul>

                                                            </div>
                                                        </div>
                                                    </div>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <li id="bgAction" class='<%# BgActions(Eval("Description")) %>' runat="server"><%--<span class="date"></span>--%>
                                                <asp:CheckBox ID="ChkRunAction" runat="server" AutoPostBack="true" CommandName="ChkRunAction"
                                                    Visible='<%#ModeChaging(Eval("IsActive")) %>' OnCheckedChanged="ChkRunActionCheckedChanged" />
                                                <span class="glyphicons activity-icon wf-icon-os">
                                                    <i></i>

                                                </span>
                                                <span id="Span1" class='<%#ClassChaging(Eval("IsActive"))%>' runat="server"></span>
                                                <asp:Label ID="lblDescription" runat="server" Text='<%# Eval("Description")%>'></asp:Label>&nbsp;
                                                    <asp:Label ID="actionName" runat="server" CssClass="text-primary" Text='<%# Eval("Name") %>'></asp:Label>

                                                <%--<a href="">Martin Glades's</a>--%>
                                                <asp:Label ID="lblActionId" Visible="false" runat="server" Text='<%# Eval("Id")%>'></asp:Label>
                                                <asp:Button ID="ibtnStartAction" runat="server" Text="Run" Enabled='<%#ActiveButtonIcon(Eval("Description")) %>'
                                                    Visible='<%#ModeChaging(Eval("IsActive")) %>' CssClass="buttonblue font_8"
                                                    CommandName="StartAction" AlternateText="StartAction" />
                                                <asp:Image ID="imgload" Visible="false" CssClass="pull-right margin-top" ImageUrl='<%# ActiveProcess(Eval("Description")) %>'
                                                    runat="server" class="btnworkflowclose" />

                                                <div class="clearfix"></div>
                                            </li>
                                        </ItemTemplate>
                                        <EmptyDataTemplate>
                                            <asp:Label ID="lblempty" Text="No Record Found" runat="server"> </asp:Label>
                                        </EmptyDataTemplate>
                                        <EditItemTemplate>
                                            <tr>
                                                <td colspan="3">
                                                    <asp:Label ID="ActionNAME" runat="server" Text='<%# Eval("Name") %>' />
                                                </td>
                                            </tr>
                                        </EditItemTemplate>
                                    </asp:ListView>
                                </asp:Panel>
                                <div id="divmessage" class="message" runat="server">
                                    <asp:Label ID="lblMessage" runat="server" Text=""></asp:Label>
                                </div>
                                <div class="moda-footer">
                                    <div id="divbtn" class="col-xs-12 center margin-top" runat="server">
                                        <asp:Button ID="BtnStartCustom" runat="server" Text="Start" Visible="false" OnClick="BtnStartClick"
                                            CssClass="btn btn-primary" Width="20%" />
                                        <asp:Button ID="BtnAutoAbort" runat="server" Text="Abort" CssClass="btn btn-default" Width="20%" Visible="False"
                                            OnClick="BtnAutoAbortClick" />
                                        <asp:Button ID="BtnCompleted" runat="server" CssClass="btn btn-primary" Width="20%" Visible="False"
                                            Text="Completed" OnClick="BtnCompletedClick" />
                                        <asp:Button runat="server" ID="HiddenForModal" Style="display: none" />

                                        <cc1:ModalPopupExtender ID="ModalPopupExtenderCustom" runat="server" TargetControlID="HiddenForModal"
                                            PopupControlID="PanelExceptionCondition" Drag="false" PopupDragHandleControlID="PanelExceptionCondition"
                                            BackgroundCssClass="bg">
                                        </cc1:ModalPopupExtender>
                                        <cc1:ConfirmButtonExtender ID="ConfirmAbort" runat="server" TargetControlID="BtnAutoAbort"
                                            ConfirmText="Are you sure want to abort Workflow ?" OnClientCancel="CancelClick">
                                        </cc1:ConfirmButtonExtender>
                                        <cc1:ModalPopupExtender ID="ModalPopupExtenderPassword" runat="server" TargetControlID="BtnStartCustom"
                                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="PanelAuthenticate"
                                            PopupDragHandleControlID="PanelAuthenticate" Drag="true" BackgroundCssClass="bg">
                                        </cc1:ModalPopupExtender>

                                    </div>
                                </div>
                                <div class="col-xs-12 text-small">
                                    <div class="pull-left margin-top">
                                        Note: 
                                    </div>
                                    <div class="pull-left innerLR">
                                        <span class="legend-warning"></span>Running
                                    </div>
                                    <div class="pull-left innerR">
                                        <span class="legend-info"></span>Skip
                                    </div>
                                    <div class="pull-left innerR">
                                        <span class="legend-success"></span>Success
                                    </div>
                                    <div class="pull-left innerR">
                                        <span class="legend-warning"></span>Error
                                    </div>
                                    <div class="pull-left">
                                        <span class="legend-default"></span>Pending
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>

        </div>
    </form>
    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/jquery.slimscroll.min.js"></script>
    <script src="../Script/core.init.js"></script>
</body>
</html>
