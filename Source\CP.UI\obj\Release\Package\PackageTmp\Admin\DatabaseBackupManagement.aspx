﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="DatabaseBackupManagement.aspx.cs" Inherits="CP.UI.Admin.DatabaseBackupManagement"
    Title="Continuity Patrol :: DatabaseBackup Management" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script language="javascript" type="text/javascript">
        function ValidateWeek(source, args) {
            var chkListModules = document.getElementById('<%= chklstWeekley.ClientID %>');
            var chkListinputs = chkListModules.getElementsByTagName("input");
            for (var i = 0; i < chkListinputs.length; i++) {
                if (chkListinputs[i].checked) {
                    args.IsValid = true;
                    return;
                }
            }
            args.IsValid = false;
        }
    </script>
    <script src="../Script/EncryptDecrypt.js"></script>
    <script src="../Script/MaskedPassword.js"></script>
    <style type="text/css">
        .bootstrap-select:not([class*="col"]), .bootstrap-select:not([class*="col"]) .btn {
        width:182px !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />

    <asp:Timer runat="server" ID="UpdateTimer" Interval="30000" OnTick="UpdateTimerTick" Enabled="false" />
    <div class="innerLR">
        <div id="ulMessage" runat="server" visible="false">
            <span class="close-bt"></span>
        </div>

      
        <h3>
            <img src="../Images/db-icon-conf.png">
            Backup Configuration</h3>
     
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-md-12 form-horizontal uniformjs">

                        <div class="form-group">
                            <label class="col-md-3 control-label" >
                                Server Name / IP Address<span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <asp:TextBox ID="txtServer" runat="server" class="form-control" TabIndex="5"></asp:TextBox>

                                <asp:RequiredFieldValidator ID="rfvIPAddress" runat="server" ControlToValidate="txtServer"
                                    ErrorMessage="Enter Server Name" Display="Dynamic" ValidationGroup="main"></asp:RequiredFieldValidator>

                                <asp:Button ID="btnDiscover" runat="server" CssClass="btn btn-primary" Width="15%"
                                    Text="Discover" OnClick="btnDiscover_Click" CausesValidation="false" />
                            </div>
                        </div>
                      
                        <div class="form-group">
                            <label class="col-md-3 control-label" >
                                <asp:Label ID="lbldbUser" runat="server" Text="User Name"></asp:Label><span class="inactive">
                        *</span></label>

                            <div class="col-md-9">
                                <asp:TextBox ID="txtUserName" runat="server" class="form-control" TabIndex="7"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvSSHUser" runat="server" ControlToValidate="txtUserName"
                                    ErrorMessage="Enter User Name" Display="Dynamic" ValidationGroup="main"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label" >
                                <asp:Label ID="lblPassword" runat="server" Text="Password"></asp:Label><span class="inactive">
                        *</span></label>

                            <div class="col-md-9">
                                <asp:TextBox ID="txtPassword" runat="server" class="form-control" autocomplete="off" TextMode="Password" TabIndex="8"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtPassword"
                                    ErrorMessage="Enter User Password" Display="Dynamic" ValidationGroup="main"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <asp:Panel ID="pnlSudo" runat="server">
                            <div class="form-group">
                                <label class="col-md-3 control-label" >
                                    <asp:Label ID="lbldbname" runat="server" Text="Database Name"></asp:Label><span class="inactive">
                            *</span></label>

                                <div class="col-md-9">
                                    <asp:TextBox ID="txtDatabaseName" runat="server" class="form-control" TabIndex="9"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="txtDatabaseName"
                                        ErrorMessage="Enter Database Name" Display="Dynamic" ValidationGroup="main"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </asp:Panel>

                        <div class="relativeWrap">
                            <span id="grmonitor" onclick="javascript:togglediv('grcontent');toggleclass('grmonitor');">&nbsp;</span><h3>Metadata Management
                                <asp:Label ID="lblGroupNameHeader" runat="server"></asp:Label></h3>
                            <div class="widget widget-tabs">
                               
                                <div class="widget-head">
                                    <ul>
                                        <li id="Li1" runat="server" class="active" >
                                            <a class="glyphicons display ui-tabs-anchor" href="#litab1" data-toggle="tab"><i></i>Backup Data</a></li>

                                        <li id="Li2" runat="server">
                                            <a class="glyphicons share_alt ui-tabs-anchor" href="#litab2" data-toggle="tab"><i></i>Scheduled Data</a></li>
                                    </ul>
                                    
                                </div>

                                <div class="widget-body">
                                    <div class="tab-content">
                                      
                                        <div class="tab-pane active" id="litab1">

                                            <div class="row">
                                                <div class="col-md-12 form-horizontal uniformjs">

                                                    <div class="form-group">
                                                        <label class="col-md-3 control-label" >
                                                            Backup Path <span class="inactive">*</span></label>&nbsp;&nbsp;
                                                        <div class="col-md-8">
                                                            <div class="form-group">
                                                                <div class="pull-left">

                                                                    <asp:TextBox ID="txtBackupPath" runat="server" class="form-control" Width="200px"></asp:TextBox>

                                                                    <asp:RequiredFieldValidator ID="rfvPath" runat="server" ErrorMessage="Enter Backup Path"
                                                                        ControlToValidate="txtBackupPath" Display="Dynamic" ValidationGroup="main"></asp:RequiredFieldValidator>&nbsp;
                                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ValidationGroup="main" Display="Dynamic" ControlToValidate="txtBackupPath" ErrorMessage="Enter correct path" ValidationExpression="^(([a-zA-Z]:|\\\\\w[ \w\.]*)(\\\w[ \w\.]*|\\%[ \w\.]+%+)+|%[ \w\.]+%(\\\w[ \w\.]*|\\%[ \w\.]+%+)*)"></asp:RegularExpressionValidator>
                                                                    <asp:CheckBox ID="chkedit" runat="server" OnCheckedChanged="ChkeditCheckedChanged" Text="Edit" AutoPostBack="true" />&nbsp;&nbsp;&nbsp;
                                                                </div>
                                                                <asp:DropDownList ID="ddlServerList" runat="server" CssClass="selectpicker col-md-3" data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="DdlServerListSelectedIndexChanged">
                                                                </asp:DropDownList>
                                                            </div>
                                                        </div>
                                                    </div>

                                                   
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane" id="litab2">

                                            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                                <ContentTemplate>
                                                    <TK1:TabContainer ID="tabMysqlBack" runat="server" ActiveTabIndex="2"
                                                        AutoPostBack="true" OnActiveTabChanged="tabMysqlBack_ActiveTabChanged"
                                                        Style="border-color: rgb(219, 219, 219); background: none repeat scroll 0% 0% rgb(255, 255, 255); margin: 0px auto 15px; position: relative; box-shadow: 0px 3px 0px 0px rgb(219, 219, 219); border-radius: 5px; border: 1px solid rgb(219, 219, 219);">

                                                        <TK1:TabPanel ID="tabMinute" runat="server" HeaderText="Minutes" data-toggle="tab">
                                                            <HeaderTemplate>
                                                                Minutes
                                                            </HeaderTemplate>
                                                            <ContentTemplate>
                                                                <div class="form-group">
                                                                    <label class="col-md-3 control-label" >Every<span class="inactive">*</span></label>

                                                                    <div class="col-md-6">

                                                                        <asp:TextBox ID="txtMinute" runat="server" class="form-control"></asp:TextBox>
                                                                        minute(s)
                        <asp:RequiredFieldValidator ID="rfvminute" runat="server" CssClass="error" ErrorMessage="Enter minute"
                            ControlToValidate="txtMinute" Display="Dynamic" ValidationGroup="minute"></asp:RequiredFieldValidator>
                                                                        <asp:RangeValidator ID="rngminute" runat="server" ErrorMessage="only 60 minutes allowed"
                                                                            ControlToValidate="txtMinute" MaximumValue="60" MinimumValue="1" ValidationGroup="rngmin"></asp:RangeValidator>
                                                                        
                                                                    </div>
                                                                </div>
                                                            </ContentTemplate>
                                                        </TK1:TabPanel>

                                                        <TK1:TabPanel ID="tabHours" runat="server" HeaderText="Hourly" data-toggle="tab">
                                                            <HeaderTemplate>
                                                                Hourly
                                                            </HeaderTemplate>
                                                            <ContentTemplate>
                                                                <div class="form-group">
                                                                    <label class="col-md-3 control-label" >Every<span class="inactive">*</span></label>

                                                                    <div class="col-md-6">

                                                                        <asp:TextBox ID="txtHour" runat="server" class="form-control"></asp:TextBox>
                                                                        hour(s)
                        <asp:RequiredFieldValidator ID="rfvHour" runat="server" ErrorMessage="Enter hour"
                            ControlToValidate="txtHour" CssClass="error" Display="Dynamic" ValidationGroup="hour"></asp:RequiredFieldValidator>
                                                                        <asp:RangeValidator ID="rnghour" runat="server" ErrorMessage="only 24 hr. allowed"
                                                                            ControlToValidate="txtHour" MaximumValue="24" Display="Dynamic" MinimumValue="1" ValidationGroup="rnghr"></asp:RangeValidator>
                                                                    </div>
                                                                </div>

                                                                <div class="clear"></div>
                                                            </ContentTemplate>
                                                        </TK1:TabPanel>

                                                        <TK1:TabPanel ID="tabDaily" runat="server" HeaderText="Daily">
                                                            <HeaderTemplate>
                                                                Daily
                                                            </HeaderTemplate>
                                                            <ContentTemplate>

                                                                <div class="row">
                                                                    <div class="col-md-12 form-horizontal uniformjs">
                                                                        <div class="form-group">
                                                                            <label class="col-md-3 control-label">
                                                                                Select Day Type<span class="inactive">*</span>
                                                                            </label>
                                                                          
                                                                            <div class="col-md-9">
                                                                                <asp:RadioButton ID="rddaily" GroupName="rb" runat="server" Checked="True" AutoPostBack="true" OnCheckedChanged="rddaily_CheckedChanged" />
                                                                                Every
                                                                                <asp:RadioButton ID="RadioButton1" GroupName="rb" AutoPostBack="true" runat="server"
                                                                                    OnCheckedChanged="RadioButton1_CheckedChanged" />
                                                                                Every Week day
                                                                            </div>
                                                                        </div>

                                                                        <div class="form-group">
                                                                            <label class="col-md-3 control-label">
                                                                            </label>
                                                                            <div class="col-md-9">
                                                                                <asp:TextBox ID="txtday" runat="server" CssClass="form-control"></asp:TextBox>
                                                                                day(s)
                                                                                <asp:RequiredFieldValidator ID="rfvDay" runat="server" CssClass="error" ErrorMessage="Enter day"
                                                                                    ControlToValidate="txtday" Display="Dynamic" ValidationGroup="day"></asp:RequiredFieldValidator>
                                                                                <asp:RegularExpressionValidator ID="rgexp" runat="server" ControlToValidate="txtday" Display="Dynamic"
                                                                                    ErrorMessage="Enter Only Numbers" ValidationExpression="\d+" ValidationGroup="check"></asp:RegularExpressionValidator>
                                                                            </div>
                                                                        </div>

                                                                        <div class="form-group">
                                                                            <label class="col-md-3 control-label" >
                                                                                Start Time<span class="inactive">*</span></label>
                                                                            <div class="col-md-9">

                                                                                <asp:DropDownList ID="ddltime" runat="server" CssClass="selectpicker" data-style="btn-default" Width="165px"></asp:DropDownList>
                                                                                Hour

                                                                                <asp:DropDownList ID="ddlMin" runat="server" CssClass="selectpicker" data-style="btn-default" Width="165px" />
                                                                                Minutes
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ContentTemplate>
                                                        </TK1:TabPanel>

                                                        <TK1:TabPanel ID="tabWeek" runat="server" HeaderText="Weekly">
                                                            <ContentTemplate>
                                                                <div class="row">
                                                                    <div class="col-md-12 form-horizontal uniformjs">
                                                                        <div class="form-group">
                                                                            <label class="col-md-3 control-label" >
                                                                                Select Day <span class="inactive">*</span></label>
                                                                            <div class="col-md-9">
                                                                                <asp:CheckBoxList ID="chklstWeekley"   runat="server" RepeatDirection="Horizontal" RepeatLayout="Table" CssClass="pull-left padding"
                                                                                    Width="48%" ScrollBars="Vertical" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px"
                                                                                    RepeatColumns="4">
                                                                                    <asp:ListItem Value="MON">Monday</asp:ListItem>
                                                                                    <asp:ListItem Value="TUE">Tuesday</asp:ListItem>
                                                                                    <asp:ListItem Value="WED">Wednesday</asp:ListItem>
                                                                                    <asp:ListItem Value="THU">Thursday</asp:ListItem>
                                                                                    <asp:ListItem Value="FRI">Friday</asp:ListItem>
                                                                                    <asp:ListItem Value="SAT">Saturday</asp:ListItem>
                                                                                    <asp:ListItem Value="SUN">Sunday</asp:ListItem>
                                                                                </asp:CheckBoxList>&nbsp;
                                                                                <asp:CustomValidator runat="server" ID="chkWeek" Display="Dynamic"
                                                                                    ClientValidationFunction="ValidateWeek" ValidationGroup="week" CssClass="error"
                                                                                    ErrorMessage="Select week day"></asp:CustomValidator>
                                                                            </div>
                                                                        </div>

                                                                        <div class="form-group">
                                                                            <label class="col-md-3 control-label" >
                                                                                Start Time<span class="inactive">*</span></label>
                                                                            <div class="col-md-9">
                                                                                <asp:DropDownList ID="ddltime1" runat="server" CssClass="selectpicker" data-style="btn-default" Width="165px"></asp:DropDownList>
                                                                                Hour
                                                                                <asp:RequiredFieldValidator ID="rfvTime1" runat="server" ControlToValidate="ddltime1"
                                                                                    InitialValue="0" Display="Dynamic" ErrorMessage="Select Hour"></asp:RequiredFieldValidator>
                                                                                <asp:DropDownList ID="ddlMin1" runat="server" CssClass="selectpicker" data-style="btn-default" Width="165px" />
                                                                                Minutes
                                                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator21" CssClass="error" runat="server" ControlToValidate="ddlMin1"
                                                                                    InitialValue="0" Display="Dynamic" ErrorMessage="Select Minutes"></asp:RequiredFieldValidator>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ContentTemplate>
                                                        </TK1:TabPanel>

                                                        <TK1:TabPanel ID="tabMonth" runat="server" HeaderText="Monthly" >
                                                            <ContentTemplate>
                                                                <div class="form-group">
                                                                    <label class="col-md-3 control-label" >
                                                                        Select Day <span class="inactive">*</span></label>
                                                                    <div class="col-md-9">
                                                                        <TK1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtstart"
                                                                            PopupButtonID="imgFromDate" Format="yyyy-MM-dd">
                                                                        </TK1:CalendarExtender>
                                                                        <asp:TextBox ID="txtstart" runat="server" class="form-control" Width="30%"></asp:TextBox>
                                                                        <img src="../images/icons/calendar-month.png" width="16" id="imgFromDate" />
                                                                        <asp:RequiredFieldValidator ID="rfvSic" runat="server" CssClass="error" ErrorMessage="Select date" Display="Dynamic" ValidationGroup="month" ControlToValidate="txtstart"></asp:RequiredFieldValidator>
                                                                    </div>
                                                                </div>

                                                                <div class="form-group">
                                                                    <label class="col-md-3 control-label" >
                                                                        Start Time<span class="inactive">*</span></label>
                                                                    <div class="col-md-9">
                                                                        <asp:DropDownList ID="ddltime2" runat="server" CssClass="selectpicker" data-style="btn-default" Width="165px"></asp:DropDownList>
                                                                        Hour
                                                                        <asp:RequiredFieldValidator ID="rfvTime2" runat="server" ControlToValidate="ddltime2"
                                                                            InitialValue="0" Display="Dynamic" ErrorMessage="Select Hour"></asp:RequiredFieldValidator>
                                                                        <asp:DropDownList ID="ddlMin2" runat="server" CssClass="selectpicker" data-style="btn-default" Width="165px" />
                                                                        Minutes
                                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator51" CssClass="error" runat="server" ControlToValidate="ddlMin2"
                                                                            InitialValue="0" Display="Dynamic" ErrorMessage="Select Minutes"></asp:RequiredFieldValidator>
                                                                    </div>
                                                                </div>
                                                              
                                                            </ContentTemplate>
                                                        </TK1:TabPanel>
                                                        
                                                    </TK1:TabContainer>
                                                </ContentTemplate>
                                               
                                            </asp:UpdatePanel>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                       
                        <asp:UpdatePanel ID="update" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <hr class="separator" />
                                <div class="form-actions row">
                                    <div class="col-lg-5">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span class="black">Required Fields</span> &nbsp;&nbsp; <span id="iconexcuting" class="Replicating" runat="server"
                                            visible="False">&nbsp;</span>
                                        <asp:Label ID="lblBackupResult" runat="server"></asp:Label>
                                    </div>
                                    <div class="col-lg-7" >

                                        <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="15%" runat="server" Text="Save" TabIndex="9"
                                            OnClick="BtnSaveClick" ValidationGroup="main" />&nbsp;&nbsp;
                            <asp:Button ID="btnExecuteNow" CssClass="btn btn-primary" Width="15%" runat="server" Text="Execute"
                                CausesValidation="False" TabIndex="9" OnClick="BtnExecuteNowClick" />
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <asp:UpdateProgress ID="UpdateProgress1" runat="server">
        <ProgressTemplate>
            <div id="imgLoading" class="loading-mask">
                <span>Loading...</span>
            </div>
        </ProgressTemplate>
    </asp:UpdateProgress>
</asp:Content>