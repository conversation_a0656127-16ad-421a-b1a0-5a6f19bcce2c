﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class AzureKubernetesConfig : ReplicationControl
    {
        #region Variable

        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        public string sitetype;

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;

        private AzureKubernetes _Azurekubernetes;

        #endregion Variable

        public AzureKubernetes CurrentEntity
        {
            get { return _Azurekubernetes ?? (_Azurekubernetes = new AzureKubernetes()); }
            set
            {
                _Azurekubernetes = value;
            }
        }

        public string MessageInitials
        {
            get { return "Azure kubernetes"; }
        }
        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }
        public override void PrepareView()
        {
            //txtResGrpnNme.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtResGrpnNme.ClientID + ")");
            //txtVirMacName.Attributes.Add("onblur", "ValidatorValidate(" + rcvtxtVirMacName.ClientID + ")");
            //txtPublicIPAdd.Attributes.Add("onblur", "ValidatorValidate(" + rcvtxtPublicIPAdd.ClientID + ")");
            //txtRevVaultName.Attributes.Add("onblur", "ValidatorValidate(" + rcvtxtRevVaultName.ClientID + ")");
            LoadData();
            PrepareEditView();

        }

        private void PrepareEditView()
        {
            
            Response.Write(Session["AzureKubernetes"]);
            CurrentAzureKubernetes.Id = Convert.ToInt32(Session["AzureKubernetes"]);
            if (CurrentAzureKubernetes != null)// && CurrentAzure.Id > 0)
            {
                CurrentEntity = CurrentAzureKubernetes;
                txtResourceGroup.Text = CurrentEntity.AKS_ResourceGroupName;
                txtazureSubName.Text = CurrentEntity.Azure_SubscriptionName;
                txtClusterName.Text = CurrentEntity.AKS_ClusterName;
                txtClusterLocation.Text = CurrentEntity.AkS_ClusterLocation;
                txtVirtualMachineScaleSetName.Text = CurrentEntity.Aks_VirtualMachineScaleSetName;

                btnSave.Text = "Update";
            }
        }
        private void LoadData()
        {
            if (CurrentMimix != null)
            {
                CurrentEntity = CurrentAzureKubernetes;

                Session["AzureKubernetes"] = CurrentEntity;
               // CurrentEntity = CurrentAzureKubernetes;
                txtResourceGroup.Text = CurrentEntity.AKS_ResourceGroupName;
                txtazureSubName.Text = CurrentEntity.Azure_SubscriptionName;
                txtClusterName.Text = CurrentEntity.AKS_ClusterName;
                txtClusterLocation.Text = CurrentEntity.AkS_ClusterLocation;
                txtVirtualMachineScaleSetName.Text = CurrentEntity.Aks_VirtualMachineScaleSetName;
                btnSave.Text = "Update";
            }
        }
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void btnSave_Click(object sender, EventArgs e)
        {

            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }

            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;


            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            if (Page.IsValid)
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;

                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }


                try
                {
                    if (currentTransactionType != TransactionType.Undefined)
                    {
                        StartTransaction();
                        BuildEntities();
                        SaveEditor();
                        ReplicationName.Text = string.Empty;
                        EndTransaction();


                        CurrentEntity.ReplicationBase.Name = Session["RepliName"].ToString();
                        string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                currentTransactionType));
                        btnSave.Enabled = false;
                    }
                }

                catch (CpException ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                    {
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);
                    }
                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, Page);
                    }
                }
                if (returnUrl.IsNotNullOrEmpty())
                {
                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                    Helper.Url.Redirect(secureUrl);
                }
            }
        }

        private void BuildEntities()
        {
            if (Session["AzureKubernetes"] != null)
            {
                CurrentEntity = (AzureKubernetes)Session["AzureKubernetes"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            Session["RepliName"] = CurrentEntity.ReplicationBase.Name;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);

            CurrentEntity.ReplicationId = CurrentReplicationId;
            CurrentEntity.Azure_SubscriptionName = txtazureSubName.Text;
            CurrentEntity.AKS_ClusterName = txtClusterName.Text;
            CurrentEntity.AkS_ClusterLocation = txtClusterLocation.Text;
            CurrentEntity.AKS_ResourceGroupName = txtResourceGroup.Text;
            CurrentEntity.Aks_VirtualMachineScaleSetName = txtVirtualMachineScaleSetName.Text;

            
        }

        private void SaveEditor()
        {
            if (btnSave.Text == "Save")
            {
                try
                {
                    CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                    CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.AddAzureKubernete(CurrentEntity);
                    ActivityLogger.AddLog(LoggedInUserName, "Mimix", UserActionType.CreateReplicationComponent, "The AzureKubernetes Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);

                }
                catch (Exception exc)
                {

                    throw;
                }

            }
            else
            {
                CurrentEntity.Id = CurrentAzureKubernetes.Id;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateAzureKubernete(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "Mimix", UserActionType.UpdateReplicationComponent, "The AzureKubernetes Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }





        private bool CheckReplicationNameExist()
        {

            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }
            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);

        }
    }
}