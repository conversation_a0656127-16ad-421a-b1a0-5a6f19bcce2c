﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using log4net;
using System.Net;

namespace CP.UI.Controls
{
    
    public partial class AzureCosmoDbReplicationList : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;
        private readonly ILog _logger = LogManager.GetLogger(typeof(OracleDataGuardList));
        public static string IPAddress = string.Empty;
        DropDownList _ddlReplicationType = new DropDownList();
        ReplicationType type;

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            Binddata();

        }


        protected void Page_Load(object sender, EventArgs e)
        {

        }

        private void Binddata()
        {

            type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            var result = GetlvAzureListByReplicationType(type.ToString());
            lvAzure.DataSource = result;
            lvAzure.DataBind();
            UpdatePanel_Azure.Update();
        }

        public IList<ReplicationAzureCosmosDB> GetlvAzureListByReplicationType(string iType)
        {
            //var replicationlist = Facade.GetAzureByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
            var replicationlist = Facade.GetAllReplicationAzureCosmosDB();
            if (replicationlist != null)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Type.ToString() == iType
                              select replication).ToList();

                return replicationlist;
            }
            return null;
        }

        public IList<ReplicationAzureCosmosDB> GetAzureList(string searchvalue)
        {
            type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            var replicationlist = GetlvAzureListByReplicationType(type.ToString());
            // var replicationlist = GetlvAzureListByReplicationType(_ddlReplicationType.SelectedValue);
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) || replicationlist != null)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvAzure.Items.Clear();
                lvAzure.DataSource = GetAzureList(txtsearchvalue.Text);
                lvAzure.DataBind();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void lvAzure_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPagelvAzureList"] = dataPager1.StartRowIndex;
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvAzure.Items[e.NewEditIndex].FindControl("Id")) as Label;
            Session["AzureCosmoId"] = lbl1.Text;
            var lblName = (lvAzure.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;
            if (lbl1 != null && lblName != null) //&& ValidateRequest("MimixReplicationList  Edit", UserActionType.ReplicationList))
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvAzure_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPagelvAzureList"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCountlvAzureList"] = dataPager1.TotalRowCount;
                var lblId = lvAzure.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvAzure.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
                var InfraObjects = Facade.GetReplicationAzureCosmosDBById(Convert.ToInt32(lblId.Text));
                //if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("AzureSiteRecovery Delete", UserActionType.ReplicationList)) // && ValidateRequest("MimixReplicationList Delete", UserActionType.ReplicationList))
                if (lblId != null && lblId.Text != null && ValidateRequest("AzureSiteRecovery Delete", UserActionType.ReplicationList))
                {
                    //if (InfraObjects != null)
                    //{
                    //    ErrorSuccessNotifier.AddSuccessMessage("The Azure Replication component is in use.");
                    //    ActivityLogger.AddLog1(LoggedInUserName, "Azure", UserActionType.DeleteReplicationComponent, "The Azure Replication component  '" + lblName.Text + "' is in use", LoggedInUserId, IPAddress);

                    //    _logger.Info("Replication component " + "'" + lblName.Text + "'" + "is in use.  User IP Address " + "'" + IPAddress + "'");

                    //}
                    //else
                    //{
                        Facade.DeleteReplicationAzureCosmosDBById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(LoggedInUserName, "Azure", UserActionType.DeleteReplicationComponent,
                                              "The Aure Replication component '" + lblName.Text +
                                              "' was deleted from the replication component", LoggedInUserId);
                        _logger.Info("DataGuard" + " " + '"' + lblName.Text + '"' + "deleted successfully. With User IP Address " + "'" + IPAddress + "'");
                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "Azure Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));

                    //}
                }

            }
            catch (CpException ex)
            {
                if (ex != null)
                {
                    _logger.Error("CP exception while loading RecoveryAzureSiteList in  lvAzure_ItemDeleting method on RecoveryAzureSiteList page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        _logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);

            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);

                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            //   var lblRepType = (lvAzure.Items[e.ItemIndex].FindControl("Rep_Type")) as Label;

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "137");

                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvAzure_PreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }

        protected void lvAzure_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;
            if (IsUserCustom)
            {
                edit.Enabled = false;
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.Where(x => x.AccessMenuType == AccessManagerType.View.ToString()).ToList();
                    if (ObjAccess == null)
                    {
                        edit.Enabled = false;
                        edit.ImageUrl = "../images/icons/pencil_disable.png";
                        delete.Enabled = false;
                        delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                    }
                    else if (ObjAccess != null)
                    {
                        foreach (var Submenu in ObjAccess)
                        {
                            var Edit = Submenu.AccessSubMenuType.ToString();
                            var deleted = Submenu.AccessSubMenuType.ToString();
                            if (Edit == "4")
                            {
                                edit.Enabled = true;
                                edit.ImageUrl = "../images/icons/pencil.png";
                            }
                            else if (deleted == "5")
                            {
                                delete.Enabled = true;
                                delete.ImageUrl = "../images/icons/cross-circle.png";
                            }
                            else
                            {
                                edit.Enabled = false;
                                edit.ImageUrl = "../images/icons/pencil_disable.png";
                                delete.Enabled = false;
                                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                            }
                        }
                    }
                }
                else
                {
                    edit.Enabled = true;
                    edit.ImageUrl = "../images/icons/pencil.png";
                    delete.Enabled = true;
                    delete.ImageUrl = "../images/icons/cross-circle.png";
                }
            }

            if (IsUserOperator || IsUserManager)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }


        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void lvAzure_SelectedIndexChanged(object sender, EventArgs e)
        {

        }
    }
}