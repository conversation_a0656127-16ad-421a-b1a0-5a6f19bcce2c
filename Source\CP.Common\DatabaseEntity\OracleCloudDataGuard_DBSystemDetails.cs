﻿using CP.Common.Base;
using CP.Common.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "AccessManagerCustom", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class OracleCloudDataGuard_DBSystemDetails : BaseEntity
    {

        #region Properties

        public int InfraObjectId { get; set; }
        public string PRCompartmentName { get; set; }
        public string DRCompartmentName { get; set; }
        public string PRDBSystemDisplayName { get; set; }
        public string DRDBSystemDisplayName { get; set; }
        public string PRDBSystemOCID { get; set; }
        public string DRDBSystemOCID { get; set; }
        public string PRDBSystemState { get; set; }
        public string DRDBSystemState { get; set; }
        public string PRDBSystemAvailabilityDomain { get; set; }
        public string DRDBSystemAvailabilityDomain { get; set; }
        public string PRDBSystemSoftwareEdition { get; set; }
        public string DRDBSystemSoftwareEdition { get; set; }
        public string PRDBSystemVersion { get; set; }
        public string DRDBSystemVersion { get; set; }
        public string PRDBSystemPORT { get; set; }
        public string DRDBSystemPORT { get; set; }
        public string PRDBHomePath { get; set; }
        public string DRDBHomePath { get; set; }
        public string PRDBHomeOCID { get; set; }
        public string DRDBHomeOCID { get; set; }



        public string PRDataGuardAssociationOCID { get; set; }
        public string DRDataGuardAssociationOCID { get; set; }
        public string PRDataGuardAssociationStatus { get; set; }
        public string DRDataGuardAssociationStatus { get; set; }
        public string PRDatabaseName { get; set; }
        public string DRDatabaseName { get; set; }
        public string PRDatabaseOCID { get; set; }
        public string DRDatabaseOCID { get; set; }
        public string PRDatabaseRole { get; set; }
        public string DRDatabaseRole { get; set; }
        public string PRDatabaseUniqueName { get; set; }
        public string DRDatabaseUniqueName { get; set; }
        public string ApplyLag { get; set; }



        #endregion Properties


    }
}
