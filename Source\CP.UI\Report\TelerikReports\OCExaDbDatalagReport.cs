namespace CP.UI.Report.TelerikReports
{
    using CP.Common.DatabaseEntity;
    using log4net;
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Data;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;
    using CP.Common.Shared;
    /// <summary>
    /// Summary description for OracleCloudDataGuardDatalagReport.
    /// </summary>
    public partial class OCExaDbDatalagReport : Telerik.Reporting.Report
    {

        private readonly ILog _logger = LogManager.GetLogger(typeof(OCExaDbDatalagReport));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();
        public OCExaDbDatalagReport()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();
            this.DataSource = null;
            this.NeedDataSource += new System.EventHandler(this.OCExaDbDatalagReport_NeedDataSource);

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }
        private void showtable()
        {
            try
            {
                var dataTable = new DataTable();

                string strDate = Convert.ToDateTime(this.ReportParameters["iStartDate"].Value).ToString("yyyy-MM-dd");
                string endDate = Convert.ToDateTime(this.ReportParameters["iEndDate"].Value).ToString("yyyy-MM-dd");
                string infraObjectName = this.ReportParameters["iInfraName"].Value.ToString();

                int infraObjectId = Facade.GetInfraObjectByName(infraObjectName).Id;

                IList<OC_ExaDb_Monitor> Oraclecloud_list = new List<OC_ExaDb_Monitor>();
                Oraclecloud_list = Facade.Get_OCExaDBMonitor_ByDate(infraObjectId, strDate, endDate);

                if (Oraclecloud_list != null && Oraclecloud_list.Count > 0)
                {
                    dataTable.Columns.Add("Sr.No.");
                    dataTable.Columns.Add("PRCompartmentName");
                    dataTable.Columns.Add("DRCompartmentName");
                    dataTable.Columns.Add("DatabaseName");
                    dataTable.Columns.Add("PRDBClusterDisplayName");
                    dataTable.Columns.Add("DRDBClusterDisplayName");
                    dataTable.Columns.Add("PRDBClusterState");
                    dataTable.Columns.Add("DRDBClusterState");
                    dataTable.Columns.Add("PRClusterAvailibilityDomain");
                    dataTable.Columns.Add("DRClusterAvailibilityDomain");
                    dataTable.Columns.Add("ApplyLag");
                    dataTable.Columns.Add("CreateDate");
                    _logger.Info("Data Mapping Start For Report.");





                    int i = 1;
                    foreach (OC_ExaDb_Monitor ocdg in Oraclecloud_list)
                    {
                        DataRow dr = dataTable.NewRow();
                        dr["Sr.No."] = i.ToString();
                        dr["PRCompartmentName"] = ocdg.PRCompartmentName != null ? ocdg.PRCompartmentName : "N/A";
                        dr["DRCompartmentName"] = ocdg.DRCompartmentName != null ? ocdg.DRCompartmentName : "N/A";
                        dr["DatabaseName"] = ocdg.PRDatabaseName != null ? ocdg.PRDatabaseName : "N/A";

                          InfraObject infradetails = Facade.GetInfraObjectById(ocdg.InfraObjectId);


                          //if (infradetails.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted || infradetails.DROperationStatus == (int)InfraWorkflowOperation.FailOverCompleted)
                          //{

                          //    dr["PRDBSystemDisplayName"] = ocdg.DRDBSystemDisplayName != null ? ocdg.DRDBSystemDisplayName : "N/A";
                          //    dr["DRDBSystemDisplayName"] = ocdg.PRDBSystemDisplayName != null ? ocdg.PRDBSystemDisplayName : "N/A";
                          //}
                          //else
                          //{
                          dr["PRDBClusterDisplayName"] = !string.IsNullOrEmpty(ocdg.VMClusterDisplayNamePR) && ocdg.VMClusterDisplayNamePR != null ? ocdg.VMClusterDisplayNamePR : "N/A";
                          dr["DRDBClusterDisplayName"] = !string.IsNullOrEmpty(ocdg.VMClusterDisplayNameDR) && ocdg.VMClusterDisplayNameDR != null ? ocdg.VMClusterDisplayNameDR : "N/A";

                          //}
                          dr["PRDBClusterState"] = !string.IsNullOrEmpty(ocdg.VMClusterStatePR) && ocdg.VMClusterStatePR != null ? ocdg.VMClusterStatePR : "N/A";
                          dr["DRDBClusterState"] = !string.IsNullOrEmpty(ocdg.VMClusterStateDR) && ocdg.VMClusterStateDR != null ? ocdg.VMClusterStateDR : "N/A";
                        dr["PRClusterAvailibilityDomain"] = !string.IsNullOrEmpty(ocdg.VMClusterAvailabilityDomainsPR) && ocdg.VMClusterAvailabilityDomainsPR != null ? ocdg.VMClusterAvailabilityDomainsPR : "N/A";
                        dr["DRClusterAvailibilityDomain"] = !string.IsNullOrEmpty(ocdg.VMClusterAvailabilityDomainsDR) && ocdg.VMClusterAvailabilityDomainsDR != null ? ocdg.VMClusterAvailabilityDomainsDR : "N/A";
                        dr["ApplyLag"] = ocdg.ApplyLag != null ? Utility.ConvertSecondsToHHMMSS(ocdg.ApplyLag) : "N/A";



                        dr["CreateDate"] = Utility.Formatdate(Convert.ToDateTime(ocdg.CreateDate).ToString("MM-dd-yyyy HH:mm:ss"));
                        //   dr["DataLag"] = vsmoni.CreateDate != null ? vsmoni.CreateDate : "N/A";
                        i++;
                        dataTable.Rows.Add(dr);

                    }
                }
                this.DataSource = dataTable;
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method, InnerException Message " + ex.InnerException.Message);
            }
        }





        private void OCExaDbDatalagReport_NeedDataSource(object sender, EventArgs e)
        {
            showtable();
        }
    }
}