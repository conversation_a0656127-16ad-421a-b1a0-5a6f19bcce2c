
(function (t, e, i) { function o(i, o, n) { var r = e.createElement(i); return o && (r.id = te + o), n && (r.style.cssText = n), t(r) } function n() { return i.innerHeight ? i.innerHeight : t(i).height() } function r(t) { var e = H.length, i = (j + t) % e; return 0 > i ? e + i : i } function h(t, e) { return Math.round((/%/.test(t) ? ("x" === e ? E.width() : n()) / 100 : 1) * parseInt(t, 10)) } function l(t, e) { return t.photo || t.photoRegex.test(e) } function s(t, e) { return t.retinaUrl && i.devicePixelRatio > 1 ? e.replace(t.photoRegex, t.retinaSuffix) : e } function a(t) { "contains" in x[0] && !x[0].contains(t.target) && (t.stopPropagation(), x.focus()) } function d() { var e, i = t.data(A, Z); null == i ? (_ = t.extend({}, Y), console && console.log && console.log("Error: cboxElement missing settings object")) : _ = t.extend({}, i); for (e in _) t.isFunction(_[e]) && "on" !== e.slice(0, 2) && (_[e] = _[e].call(A)); _.rel = _.rel || A.rel || t(A).data("rel") || "nofollow", _.href = _.href || t(A).attr("href"), _.title = _.title || A.title, "string" == typeof _.href && (_.href = t.trim(_.href)) } function c(i, o) { t(e).trigger(i), se.trigger(i), t.isFunction(o) && o.call(A) } function u() { var t, e, i, o, n, r = te + "Slideshow_", h = "click." + te; _.slideshow && H[1] ? (e = function () { clearTimeout(t) }, i = function () { (_.loop || H[j + 1]) && (t = setTimeout(J.next, _.slideshowSpeed)) }, o = function () { M.html(_.slideshowStop).unbind(h).one(h, n), se.bind(ne, i).bind(oe, e).bind(re, n), x.removeClass(r + "off").addClass(r + "on") }, n = function () { e(), se.unbind(ne, i).unbind(oe, e).unbind(re, n), M.html(_.slideshowStart).unbind(h).one(h, function () { J.next(), o() }), x.removeClass(r + "on").addClass(r + "off") }, _.slideshowAuto ? o() : n()) : x.removeClass(r + "off " + r + "on") } function f(i) { G || (A = i, d(), H = t(A), j = 0, "nofollow" !== _.rel && (H = t("." + ee).filter(function () { var e, i = t.data(this, Z); return i && (e = t(this).data("rel") || i.rel || this.rel), e === _.rel }), j = H.index(A), -1 === j && (H = H.add(A), j = H.length - 1)), g.css({ opacity: parseFloat(_.opacity), cursor: _.overlayClose ? "pointer" : "auto", visibility: "visible" }).show(), V && x.add(g).removeClass(V), _.className && x.add(g).addClass(_.className), V = _.className, K.html(_.close).show(), $ || ($ = q = !0, x.css({ visibility: "hidden", display: "block" }), W = o(ae, "LoadedContent", "width:0; height:0; overflow:hidden").appendTo(v), D = b.height() + k.height() + v.outerHeight(!0) - v.height(), B = C.width() + T.width() + v.outerWidth(!0) - v.width(), N = W.outerHeight(!0), z = W.outerWidth(!0), _.w = h(_.initialWidth, "x"), _.h = h(_.initialHeight, "y"), J.position(), u(), c(ie, _.onOpen), O.add(F).hide(), x.focus(), e.addEventListener && (e.addEventListener("focus", a, !0), se.one(he, function () { e.removeEventListener("focus", a, !0) })), _.returnFocus && se.one(he, function () { t(A).focus() })), w()) } function p() { !x && e.body && (X = !1, E = t(i), x = o(ae).attr({ id: Z, "class": t.support.opacity === !1 ? te + "IE" : "", role: "dialog", tabindex: "-1" }).hide(), g = o(ae, "Overlay").hide(), S = o(ae, "LoadingOverlay").add(o(ae, "LoadingGraphic")), y = o(ae, "Wrapper"), v = o(ae, "Content").append(F = o(ae, "Title"), I = o(ae, "Current"), P = t('<button type="button"/>').attr({ id: te + "Previous" }), R = t('<button type="button"/>').attr({ id: te + "Next" }), M = o("button", "Slideshow"), S, K = t('<button type="button"/>').attr({ id: te + "Close" })), y.append(o(ae).append(o(ae, "TopLeft"), b = o(ae, "TopCenter"), o(ae, "TopRight")), o(ae, !1, "clear:left").append(C = o(ae, "MiddleLeft"), v, T = o(ae, "MiddleRight")), o(ae, !1, "clear:left").append(o(ae, "BottomLeft"), k = o(ae, "BottomCenter"), o(ae, "BottomRight"))).find("div div").css({ "float": "left" }), L = o(ae, !1, "position:absolute; width:9999px; visibility:hidden; display:none"), O = R.add(P).add(I).add(M), t(e.body).append(g, x.append(y, L))) } function m() { function i(t) { t.which > 1 || t.shiftKey || t.altKey || t.metaKey || t.control || (t.preventDefault(), f(this)) } return x ? (X || (X = !0, R.click(function () { J.next() }), P.click(function () { J.prev() }), K.click(function () { J.close() }), g.click(function () { _.overlayClose && J.close() }), t(e).bind("keydown." + te, function (t) { var e = t.keyCode; $ && _.escKey && 27 === e && (t.preventDefault(), J.close()), $ && _.arrowKey && H[1] && !t.altKey && (37 === e ? (t.preventDefault(), P.click()) : 39 === e && (t.preventDefault(), R.click())) }), t.isFunction(t.fn.on) ? t(e).on("click." + te, "." + ee, i) : t("." + ee).live("click." + te, i)), !0) : !1 } function w() { var e, n, r, a = J.prep, u = ++de; q = !0, U = !1, A = H[j], d(), c(le), c(oe, _.onLoad), _.h = _.height ? h(_.height, "y") - N - D : _.innerHeight && h(_.innerHeight, "y"), _.w = _.width ? h(_.width, "x") - z - B : _.innerWidth && h(_.innerWidth, "x"), _.mw = _.w, _.mh = _.h, _.maxWidth && (_.mw = h(_.maxWidth, "x") - z - B, _.mw = _.w && _.w < _.mw ? _.w : _.mw), _.maxHeight && (_.mh = h(_.maxHeight, "y") - N - D, _.mh = _.h && _.h < _.mh ? _.h : _.mh), e = _.href, Q = setTimeout(function () { S.show() }, 100), _.inline ? (r = o(ae).hide().insertBefore(t(e)[0]), se.one(le, function () { r.replaceWith(W.children()) }), a(t(e))) : _.iframe ? a(" ") : _.html ? a(_.html) : l(_, e) ? (e = s(_, e), t(U = new Image).addClass(te + "Photo").bind("error", function () { _.title = !1, a(o(ae, "Error").html(_.imgError)) }).one("load", function () { var e; u === de && (U.alt = t(A).attr("alt") || t(A).attr("data-alt") || "", _.retinaImage && i.devicePixelRatio > 1 && (U.height = U.height / i.devicePixelRatio, U.width = U.width / i.devicePixelRatio), _.scalePhotos && (n = function () { U.height -= U.height * e, U.width -= U.width * e }, _.mw && U.width > _.mw && (e = (U.width - _.mw) / U.width, n()), _.mh && U.height > _.mh && (e = (U.height - _.mh) / U.height, n())), _.h && (U.style.marginTop = Math.max(_.mh - U.height, 0) / 2 + "px"), H[1] && (_.loop || H[j + 1]) && (U.style.cursor = "pointer", U.onclick = function () { J.next() }), U.style.width = U.width + "px", U.style.height = U.height + "px", setTimeout(function () { a(U) }, 1)) }), setTimeout(function () { U.src = e }, 1)) : e && L.load(e, _.data, function (e, i) { u === de && a("error" === i ? o(ae, "Error").html(_.xhrError) : t(this).contents()) }) } var g, x, y, v, b, C, T, k, H, E, W, L, S, F, I, M, R, P, K, O, _, D, B, N, z, A, j, U, $, q, G, Q, J, V, X, Y = { transition: "elastic", speed: 300, fadeOut: 300, width: !1, initialWidth: "600", innerWidth: !1, maxWidth: !1, height: !1, initialHeight: "450", innerHeight: !1, maxHeight: !1, scalePhotos: !0, scrolling: !0, inline: !1, html: !1, iframe: !1, fastIframe: !0, photo: !1, href: !1, title: !1, rel: !1, opacity: .9, preloading: !0, className: !1, retinaImage: !1, retinaUrl: !1, retinaSuffix: "@2x.$1", current: "image {current} of {total}", previous: "previous", next: "next", close: "close", xhrError: "This content failed to load.", imgError: "This image failed to load.", open: !1, returnFocus: !0, reposition: !0, loop: !0, slideshow: !1, slideshowAuto: !0, slideshowSpeed: 2500, slideshowStart: "start slideshow", slideshowStop: "stop slideshow", photoRegex: /\.(gif|png|jp(e|g|eg)|bmp|ico|webp)((#|\?).*)?$/i, onOpen: !1, onLoad: !1, onComplete: !1, onCleanup: !1, onClosed: !1, overlayClose: !0, escKey: !0, arrowKey: !0, top: !1, bottom: !1, left: !1, right: !1, fixed: !1, data: void 0 }, Z = "colorbox", te = "cbox", ee = te + "Element", ie = te + "_open", oe = te + "_load", ne = te + "_complete", re = te + "_cleanup", he = te + "_closed", le = te + "_purge", se = t("<a/>"), ae = "div", de = 0; t.colorbox || (t(p), J = t.fn[Z] = t[Z] = function (e, i) { var o = this; if (e = e || {}, p(), m()) { if (t.isFunction(o)) o = t("<a/>"), e.open = !0; else if (!o[0]) return o; i && (e.onComplete = i), o.each(function () { t.data(this, Z, t.extend({}, t.data(this, Z) || Y, e)) }).addClass(ee), (t.isFunction(e.open) && e.open.call(o) || e.open) && f(o[0]) } return o }, J.position = function (t, e) { function i(t) { b[0].style.width = k[0].style.width = v[0].style.width = parseInt(t.style.width, 10) - B + "px", v[0].style.height = C[0].style.height = T[0].style.height = parseInt(t.style.height, 10) - D + "px" } var o, r, l, s = 0, a = 0, d = x.offset(); E.unbind("resize." + te), x.css({ top: -9e4, left: -9e4 }), r = E.scrollTop(), l = E.scrollLeft(), _.fixed ? (d.top -= r, d.left -= l, x.css({ position: "fixed" })) : (s = r, a = l, x.css({ position: "absolute" })), a += _.right !== !1 ? Math.max(E.width() - _.w - z - B - h(_.right, "x"), 0) : _.left !== !1 ? h(_.left, "x") : Math.round(Math.max(E.width() - _.w - z - B, 0) / 2), s += _.bottom !== !1 ? Math.max(n() - _.h - N - D - h(_.bottom, "y"), 0) : _.top !== !1 ? h(_.top, "y") : Math.round(Math.max(n() - _.h - N - D, 0) / 2), x.css({ top: d.top, left: d.left, visibility: "visible" }), t = x.width() === _.w + z && x.height() === _.h + N ? 0 : t || 0, y[0].style.width = y[0].style.height = "9999px", o = { width: _.w + z + B, height: _.h + N + D, top: s, left: a }, 0 === t && x.css(o), x.dequeue().animate(o, { duration: t, complete: function () { i(this), q = !1, y[0].style.width = _.w + z + B + "px", y[0].style.height = _.h + N + D + "px", _.reposition && setTimeout(function () { E.bind("resize." + te, J.position) }, 1), e && e() }, step: function () { i(this) } }) }, J.resize = function (t) { $ && (t = t || {}, t.width && (_.w = h(t.width, "x") - z - B), t.innerWidth && (_.w = h(t.innerWidth, "x")), W.css({ width: _.w }), t.height && (_.h = h(t.height, "y") - N - D), t.innerHeight && (_.h = h(t.innerHeight, "y")), t.innerHeight || t.height || (W.css({ height: "auto" }), _.h = W.height()), W.css({ height: _.h }), J.position("none" === _.transition ? 0 : _.speed)) }, J.prep = function (e) { function i() { return _.w = _.w || W.width(), _.w = _.mw && _.mw < _.w ? _.mw : _.w, _.w } function n() { return _.h = _.h || W.height(), _.h = _.mh && _.mh < _.h ? _.mh : _.h, _.h } if ($) { var h, a = "none" === _.transition ? 0 : _.speed; W.empty().remove(), W = o(ae, "LoadedContent").append(e), W.hide().appendTo(L.show()).css({ width: i(), overflow: _.scrolling ? "auto" : "hidden" }).css({ height: n() }).prependTo(v), L.hide(), t(U).css({ "float": "none" }), h = function () { function e() { t.support.opacity === !1 && x[0].style.removeAttribute("filter") } var i, n, h = H.length, d = "frameBorder", u = "allowTransparency"; $ && (n = function () { clearTimeout(Q), S.hide(), c(ne, _.onComplete) }, F.html(_.title).add(W).show(), h > 1 ? ("string" == typeof _.current && I.html(_.current.replace("{current}", j + 1).replace("{total}", h)).show(), R[_.loop || h - 1 > j ? "show" : "hide"]().html(_.next), P[_.loop || j ? "show" : "hide"]().html(_.previous), _.slideshow && M.show(), _.preloading && t.each([r(-1), r(1)], function () { var e, i, o = H[this], n = t.data(o, Z); n && n.href ? (e = n.href, t.isFunction(e) && (e = e.call(o))) : e = t(o).attr("href"), e && l(n, e) && (e = s(n, e), i = new Image, i.src = e) })) : O.hide(), _.iframe ? (i = o("iframe")[0], d in i && (i[d] = 0), u in i && (i[u] = "true"), _.scrolling || (i.scrolling = "no"), t(i).attr({ src: _.href, name: (new Date).getTime(), "class": te + "Iframe", allowFullScreen: !0, webkitAllowFullScreen: !0, mozallowfullscreen: !0 }).one("load", n).appendTo(W), se.one(le, function () { i.src = "//about:blank" }), _.fastIframe && t(i).trigger("load")) : n(), "fade" === _.transition ? x.fadeTo(a, 1, e) : e()) }, "fade" === _.transition ? x.fadeTo(a, 0, function () { J.position(0, h) }) : J.position(a, h) } }, J.next = function () { !q && H[1] && (_.loop || H[j + 1]) && (j = r(1), f(H[j])) }, J.prev = function () { !q && H[1] && (_.loop || j) && (j = r(-1), f(H[j])) }, J.close = function () { $ && !G && (G = !0, $ = !1, c(re, _.onCleanup), E.unbind("." + te), g.fadeTo(_.fadeOut || 0, 0), x.stop().fadeTo(_.fadeOut || 0, 0, function () { x.add(g).css({ opacity: 1, cursor: "auto" }).hide(), c(le), W.empty().remove(), setTimeout(function () { G = !1, c(he, _.onClosed) }, 1) })) }, J.remove = function () { x && (x.stop(), t.colorbox.close(), x.stop().remove(), g.remove(), G = !1, x = null, t("." + ee).removeData(Z).removeClass(ee), t(e).unbind("click." + te)) }, J.element = function () { return t(A) }, J.settings = Y) })(jQuery, document, window);