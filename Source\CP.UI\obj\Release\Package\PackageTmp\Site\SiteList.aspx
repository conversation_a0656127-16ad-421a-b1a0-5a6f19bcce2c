﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="SiteList.aspx.cs" Inherits="CP.UI.SiteList" Title="Continuity Patrol :: Site List" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <asp:HiddenField ID="hdGuid" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="UpSite_Overview" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <h3><span class="site-icon"></span>
                    Site List</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-xs-5 col-md-push-7 text-right">
                                <asp:TextBox ID="txtsearchvalue" placeholder="Site Name" CssClass="form-control"
                                    runat="server"></asp:TextBox>
                                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%"
                                    Text="Search" OnClick="BtnSearchClick" />
                            </div>
                        </div>
                        <hr />
                        <asp:ListView ID="lvSite" runat="server" OnItemEditing="LvSiteItemEditing" OnItemDeleting="LvSiteItemDeleting"
                            DataKeyNames="Id" OnPreRender="LvSitePreRender" OnItemDataBound="LvSiteItemDataBound"
                            OnPagePropertiesChanging="LvSitePagePropertiesChanging">
                            <LayoutTemplate>
                                <table class="table table-striped table-bordered table-condensed table-white" width="100%" style="table-layout: fixed">
                                    <thead>
                                        <tr>
                                            <th style="width: 4%;" class="text-center">
                                                <span>
                                                    <img src="../Images/icons/sites_list.png" /></span>
                                            </th>
                                            <th style="width: 45%;">Name
                                            </th>
                                            <th style="width: 10%;">Type
                                            </th>
                                            <th style="width: 10%;">Status
                                            </th>
                                            <th style="width: 23%;">Location
                                            </th>
                                            <th style="width: 8%;" class="text-center">Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td style="width: 4%;" class="text-center">
                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                        <%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td class="tdword-wrap" style="width: 45%;">
                                        <asp:Label ID="SITE_NAME" runat="server" Text='<%# Eval("Name") %>' />
                                    </td>
                                    <td style="width: 10%;">
                                        <asp:Label ID="TYPE" runat="server" Text='<%# Eval("Type") %>' />
                                    </td>
                                    <td style="width: 10%;">
                                        <asp:Label ID="SITE_STATUS" runat="server" Text='<%# Eval("Status") %>' />
                                    </td>
                                    <td style="width: 23%;">
                                        <asp:Label ID="SITELIST" runat="server" Text='<%# Eval("Location") %>' />
                                    </td>
                                    <td style="width: 8%;" class="text-center">
                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                            ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                    </td>
                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                        ConfirmText='<%# CheckWithServer(Eval("Name"),Eval("Id")) %>' OnClientCancel="CancelClick">
                                    </TK1:ConfirmButtonExtender>
                                </tr>
                            </ItemTemplate>
                            <EmptyDataTemplate>
                                <div class="message warning align-center bold no-bottom-margin">
                                    <asp:Label ID="lblError" Text="No Record Found" ForeColor="Red" runat="server" Visible="true"></asp:Label>
                                </div>
                            </EmptyDataTemplate>
                        </asp:ListView>
                        <div class="row">
                            <div class="col-xs-6">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvSite">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                                <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                Out Of
                                                <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-xs-6 text-right">
                                <asp:DataPager ID="dataPager3" runat="server" PagedControlID="lvSite" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button"
                                            ShowFirstPageButton="false" ShowLastPageButton="false" ShowNextPageButton="false"
                                            PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10" NextPreviousButtonCssClass="btn-pagination"
                                            CurrentPageLabelCssClass="currentlabel" NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button"
                                            ShowFirstPageButton="false" ShowLastPageButton="false" ShowPreviousPageButton="false"
                                            ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>
                    </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
