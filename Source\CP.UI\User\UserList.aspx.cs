﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Controls;
using log4net;
using CP.BusinessFacade;
using System.Net;
using System.Web;
using System.Globalization;
using CP.ExceptionHandler;

namespace CP.UI
{
    public partial class UserList : UserBasePage
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(UserList));
        public static string RedirectURL = Constants.UrlConstants.Urls.User.UserConfiguration;
        public static string RedirectURL1 = Constants.UrlConstants.Urls.Admin.ChangePassword;
        public static string IPAddress = string.Empty;

        #region Public Properties

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.User.UserList;
                }
                return string.Empty;
            }
        }

        #endregion Public Properties

        #region method

        protected string CommMode(object commid)
        {
            string commMode = string.Empty;
            switch (Convert.ToInt32(commid))
            {
                case 1:
                    commMode = "Application";
                    break;

                case 2:
                    commMode = "Email and Application";
                    break;

                case 3:
                    commMode = "Mobile and Application";
                    break;

                case 4:
                    commMode = "Email,Mobile and Application";
                    break;
            }
            return commMode;
        }

        protected string GetRoleType(object roleId)
        {
            string roleName = string.Empty;
            switch (Convert.ToInt32(roleId))
            {
                case 1:
                    roleName = "SuperAdmin";
                    break;

                case 2:
                    roleName = "Admin";
                    break;

                case 3:
                    roleName = "Operator";
                    break;

                case 4:
                    roleName = "Manager";
                    break;
            }
            return roleName;
        }

        private IList<User> GetUserList()
        {
            //  return Facade.GetUsersByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, LoggedInUserCompany.IsParent);

            var result = Facade.GetUsersByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, LoggedInUserCompany.IsParent);

            if (!IsUserSuperAdmin)
                result = (from users in result
                          where users.CreatorId == LoggedInUserId
                          select users).ToList();


            return result;
        }

        // added by kiran
        private IList<User> GetUserList(IList<User> userlist, string value)
        {
            if (value != null || value == "" || userlist != null)
            {
                var result = (from users in userlist
                              where users.LoginName.ToLower().Contains(value.ToLower())
                              select users).ToList();

                return result;
            }
            else
                return userlist;
        }

        #endregion method

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();
            //if (IsUserManager)
            //{
            //    Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);
            //}
            if (IsUserOperator || IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            if (IsUserCustom)
            {
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.FirstOrDefault(x => x.AccessMenuType == AccessManagerType.Admin.ToString());
                    if (ObjAccess == null)
                    {
                        Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                        return;
                    }
                }
                else
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }

            }
            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }


            Utility.SelectMenu(Master, "Module8");
            lvUser.DataSource = GetUserList();
            lvUser.DataBind();
            setListViewPage();
        }

        protected void LvUserItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (ValidateRequest("User Edit", UserActionType.UpdateUserAccount))
            {
                if (e.CommandName == "ChangePassword")
                {
                    var lblId = e.Item.FindControl("Id") as Label;

                    if (lblId != null)
                    {
                        Session["Id"] = lblId.Text;
                    }

                    var dataItem = (ListViewDataItem)e.Item;
                    var dataKey = lvUser.DataKeys[dataItem.DisplayIndex];
                    if (dataKey != null)
                    {
                        string userId = dataKey.Value.ToString();
                        int id = Convert.ToInt32(userId);
                        Session["Id"] = id;
                        var userpassword = Facade.GetUserById(id);

                        if (LoggedInUser.Role == (UserRole)1 && userpassword.Role == (UserRole)1 && LoggedInUser.Id != userpassword.Id)
                        {
                            Response.Redirect("~/Logout.aspx");
                        }


                        SecureUrl secureUrl = UrlHelper.BuildSecureUrl(RedirectURL1, string.Empty, Constants.UrlConstants.Params.UserId, userId);
                        if (secureUrl != null)
                        {
                            Helper.Url.Redirect(secureUrl);
                        }

                        bool isChange = Facade.ResetUserPasswordById(id, userpassword.LoginPassword);
                        if (isChange)
                        {
                            lvUser.EditIndex = -1;
                            lvUser.DataSource = GetUserList();
                            lvUser.DataBind();
                            setListViewPage();
                        }
                        else
                        {
                            lvUser.DataSource = GetUserList();
                            lvUser.DataBind();
                            setListViewPage();
                        }
                    }
                }
                if (e.CommandName == "ActiveState")
                {
                    HiddenField hdnIsActive = (HiddenField)e.Item.FindControl("hdnIsActive"); //(lvUser.Items[e.NewEditIndex].FindControl("hdnIsActive")) as HiddenField;

                    if (hdnIsActive != null)
                    {
                        var edit = e.Item.FindControl("ibtnEdit") as ImageButton;
                        var delete = e.Item.FindControl("ibtnDelete") as ImageButton;
                        var changepwd = e.Item.FindControl("ibtnChangePassword") as ImageButton;
                        Label lbText = (Label)e.Item.FindControl("Id");
                        var lblloginname = e.Item.FindControl("Name") as Label;

                        string label = lbText.Text;

                        int a = Convert.ToInt32(label.ToString());

                        int isactive = Convert.ToInt32(hdnIsActive.Value);
                        if (isactive == 0)
                        {
                            //Session["isactive"] = isactive;
                            UnLockUser(a);
                            edit.Enabled = true;
                            delete.Enabled = true;
                            changepwd.Enabled = true;

                            var imageactive = e.Item.FindControl("imgActive") as ImageButton;

                            imageactive.ImageUrl = "../Images/icons/tick-circle.png";

                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("User" + " " + '"' + lblloginname.Text + '"', TransactionType.Active));

                            if (ReturnUrl.IsNotNullOrEmpty())
                            {
                                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
                            }
                        }
                    }
                }
            }
        }

        private bool UnLockUser(int a)
        {
            return Facade.UnLockUserAccount(a);
        }

        protected string GetUserType(string usertype, string isGroup)
        {
            //return usertype == LoginType.FormAuthentication.ToString() ? "Local Authentication" : "Active Directory";
            string groupString = isGroup == "1" ? "Group" : "Individual";


            return usertype == LoginType.FormAuthentication.ToString() ? "Local Authentication" : "AD(" + groupString + ")";
        }

        protected string GetCompanyname(string companyid)
        {
            string companyname = "";
            try
            {


                var cdetails = Facade.GetCompanyProfileById(Convert.ToInt32(companyid));
                if (cdetails != null)
                {
                    companyname = cdetails.DisplayName.ToString();
                }


            }
            catch (Exception ex)
            {
                _logger.Error("Exception in GetCompanyname method:" + ex.Message);
            }
            return companyname;

        }

        protected void LvUserItemDataBound(object sender, ListViewItemEventArgs e)
        {

            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                Label lblCompanyName = (Label)e.Item.FindControl("lblcompany");

                ListViewDataItem dataItem = (ListViewDataItem)e.Item;
                User rowView = dataItem.DataItem as User;

                int _companyid = rowView.CompanyId;


                var companyname = Facade.GetCompanyProfileById(_companyid);
                if (companyname != null)
                {

                    lblCompanyName.Text = companyname.DisplayName.ToString();
                }
                else
                {
                    lblCompanyName.Visible = false;
                }
            }

            var edit = e.Item.FindControl("ibtnEdit") as ImageButton;
            var delete = e.Item.FindControl("ibtnDelete") as ImageButton;
            var changepwd = e.Item.FindControl("ibtnChangePassword") as ImageButton;
            var lockmode = e.Item.FindControl("imgActive") as ImageButton;
            var role = e.Item.FindControl("lblCategory") as Label;
            var lbl = e.Item.FindControl("Label3") as Label;
            var lblId = e.Item.FindControl("Id") as Label;
            var lblLoginType = e.Item.FindControl("LoginType") as Label;
            var lblLoginname = e.Item.FindControl("Name") as Label;

            if (lblLoginname.Text == "cpadmin")
            {
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
            }
            if (LoggedInUserName == lblLoginname.Text)
            {
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
            }

            HiddenField hdnIsActive = (HiddenField)e.Item.FindControl("hdnIsActive");

            if (hdnIsActive != null)
            {
                string isactive = hdnIsActive.Value.ToString();

                if (isactive == "0")
                {
                    edit.Enabled = false;
                    delete.Enabled = false;
                    changepwd.Enabled = false;
                }
                else if (isactive == "1")
                {
                    lockmode.Enabled = false;
                }
            }

            if (lblId != null) Session["Id"] = lblId.Text;

            if (lbl != null && lbl.Text == "Undefined")
                lbl.Text = "_";

            if (IsSuperAdmin)
            {
                //if (role != null)
                //{
                //    if (role.Text == "SuperAdmin")
                //    {
                //        if (edit != null)
                //        {
                //            edit.Enabled = false;
                //            edit.ImageUrl = "../images/icons/pencil_disable.png";
                //        }
                //        if (delete != null)
                //        {
                //            delete.Enabled = false;
                //            delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                //        }
                //    }
                //}

                if (role != null)
                {

                    // if (role.Text == "SuperAdmin")
                    //{
                    //    if (edit != null)
                    //    {
                    //        edit.Enabled = false;
                    //        edit.ImageUrl = "../images/icons/pencil_disable.png";
                    //    }
                    //    if (delete != null)
                    //    {
                    //        delete.Enabled = false;
                    //        delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                    //    }
                    //}
                    int _userid = Convert.ToInt32(lblId.Text);
                    var userDetail = Facade.GetUserById(LoggedInUser.Id);
                    if (userDetail != null)
                    {
                        if (role.Text == "SuperAdmin" && userDetail.Accessrole == "root")
                        {


                            if (edit != null)
                            {
                                edit.Enabled = true;
                                edit.ImageUrl = "../images/icons/pencil.png";
                            }
                            if (delete != null)
                            {
                                delete.Enabled = false;
                                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                            }

                        }
                        else if (role.Text == "SuperAdmin")
                        {


                            if (edit != null)
                            {
                                edit.Enabled = false;
                                edit.ImageUrl = "../images/icons/pencil_disable.png";
                            }
                            if (delete != null)
                            {
                                delete.Enabled = false;
                                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                            }
                        }

                    }

                }
            }

            if ((lblId.Text == Convert.ToString(LoggedInUser.Id)) && IsSuperAdmin)
            {
                if (edit != null)
                {
                    edit.Enabled = true;
                    edit.ImageUrl = "../images/icons/pencil.png";
                }
            }
            if (IsUserOperator || IsUserManager)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (lockmode != null)
                {
                    lockmode.Enabled = false;
                    lockmode.ImageUrl = "../Images/icons/tick-circle.png";
                }
            }

            if (lblLoginType.Text != "")
            {
                if (lblLoginType.Text == LoginType.AD.ToString())
                {
                    changepwd.Enabled = false;
                    changepwd.ImageUrl = "../Images/icons/key_disable.png";
                }
            }
            var _userDetail = Facade.GetUserById(LoggedInUser.Id);
            var _userDetails1 = Facade.GetUserById(Convert.ToInt32(lblId.Text));
            if (_userDetail != null && _userDetails1 != null)
            {

                //if (_userDetail.Accessrole == "root")
                //{

                //    if (changepwd != null)
                //    {
                //        changepwd.Enabled = true;
                //        changepwd.ImageUrl = "../Images/icons/key_disable.png";
                //    }
                //}

                //if (_userDetail.Role.ToString() == "SuperAdmin" && _userDetail.Accessrole != "root")
                //{

                //    if ((lblId.Text == Convert.ToString(LoggedInUser.Id)) && IsSuperAdmin) {

                //        if (changepwd != null)
                //        {
                //            changepwd.Enabled = true;
                //            changepwd.ImageUrl = "../Images/icons/key_disable.png";
                //        }
                //    }

                //}

                if (_userDetail.Role.ToString() == "SuperAdmin" && _userDetail.Accessrole != "root" && _userDetails1.Accessrole == "root")
                {


                    if (changepwd != null)
                    {
                        changepwd.Enabled = false;
                        changepwd.ImageUrl = "../Images/icons/key_disable.png";
                    }
                }

                if (_userDetail.Role.ToString() == "SuperAdmin" && _userDetail.Accessrole != "root" && _userDetails1.Role.ToString() == "SuperAdmin")
                {
                    if ((lblId.Text != Convert.ToString(LoggedInUser.Id)))
                    {

                        if (changepwd != null)
                        {
                            changepwd.Enabled = false;
                            changepwd.ImageUrl = "../Images/icons/key_disable.png";
                        }
                    }
                }
            }
        }
        protected void LvUserItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageUsersList"] = (dataPager1.StartRowIndex);
                var lbl = (lvUser.Items[e.ItemIndex].FindControl("ID")) as Label;
                var lblname = (lvUser.Items[e.ItemIndex].FindControl("Label2")) as Label;
                var lblloginname = (lvUser.Items[e.ItemIndex].FindControl("Name")) as Label;
                var lblRole = (lvUser.Items[e.ItemIndex].FindControl("lblCategory")) as Label;
                if (lbl != null && ValidateRequest("User Delelte", UserActionType.UpdateUserAccount))
                {
                    int id = Convert.ToInt32(lbl.Text);
                    var userinfo = Facade.GetUserById(Convert.ToInt32(id));
                    if (userinfo != null)
                    {
                        if (LoggedInUser.Role == (UserRole)1 && (userinfo.Role == (UserRole)1 || userinfo.Id == LoggedInUser.Id))
                        {
                            Response.Redirect("~/Logout.aspx");
                        }
                        bool isDeleteUserInfo = Facade.DeleteUserInfoById(id);
                        bool isDelete = Facade.DeleteUserById(id);

                        ActivityLogger.AddLog1(LoggedInUserName, "User", UserActionType.UpdateUserAccount,
                                 "User " + lblloginname.Text + " deleted by  " + LoggedInUserName,
                                 LoggedInUserId, IPAddress);
                        _logger.Info("User Deleted successfully " + "'" + lblloginname.Text + "'" + " was Deleted by User " + "'" + LoggedInUserId + "'" + "and IP Address " + "'" + IPAddress + "'");
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("User" + " " + '"' + lblloginname.Text + '"', TransactionType.Delete));

                        _logger.DebugFormat("{0} - Delete '<{1}>' User <({2})> - '<UserId-{3}>'", HostAddress, lblloginname.Text, lblRole.Text, LoggedInUserName);
                    }
                }
                if (ReturnUrl.IsNotNullOrEmpty())
                {
                    Helper.Url.Redirect(new SecureUrl(ReturnUrl));
                }
            }
            catch (CpException ex)
            {
                if (ex != null)
                {

                    _logger.Error("CP exception while Deleting in  LvUserItemDeleting method on UserList page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        _logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                ExceptionManager.Manage(ex);
            }
        }

        protected void LvUserItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageUsersList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(RedirectURL);

            var lblUserId = (lvUser.Items[e.NewEditIndex].FindControl("ID")) as Label;
            var lblname = (lvUser.Items[e.NewEditIndex].FindControl("Name")) as Label;

            ActivityLogger.AddLog(LoggedInUserName, "User Account", UserActionType.UpdateUserAccount,
                                    "The User Account '" + lblname.Text +
                                    "' Opened as Editing Mode ", LoggedInUserId);

            if (lblUserId != null && ValidateRequest("User Edit", UserActionType.UpdateUserAccount))
            {
                //secureUrl = UrlHelper.BuildSecureUrl(RedirectURL, string.Empty, Constants.UrlConstants.Params.UserId,
                //                                     lblUserId.Text);
                secureUrl = UrlHelper.BuildSecureUrl(RedirectURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.UserId, lblUserId.Text);
                Helper.Url.Redirect(secureUrl);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        // modify by kiran
        protected void LvUserPreRender(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lvUser.DataSource = GetUserList();
                lvUser.DataBind();
                setListViewPage();
            }
            else
            {
                if (txtsearchvalue.Text == "" || string.IsNullOrEmpty(txtsearchvalue.Text))
                {
                    lvUser.DataSource = GetUserList();
                    lvUser.DataBind();
                    setListViewPage();
                }
                else
                {
                    lvUser.DataSource = GetUserList(GetUserList(), txtsearchvalue.Text);
                    lvUser.DataBind();
                }
            }
        }
        /// <summary>
        /// if deleting or updating the List of Users the page will get postback and then Listview is display the same page
        /// where it is deleted or updated. - Goraknath Khule.
        /// </summary>
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageUsersList"]) != -1) && Session["CurrentPageUsersList"] != null)
            {
                if (Convert.ToInt32(Session["CurrentPageUsersList"]) == dataPager1.TotalRowCount)
                {
                    Session["CurrentPageUsersList"] = Convert.ToInt32(Session["CurrentPageUsersList"]) - dataPager1.MaximumRows;
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageUsersList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageUsersList"] = -1;

            }
        }

        protected void LkbtnclosesmtphostClick(object sender, EventArgs e)
        {
            //  txtNewpassword.Text = string.Empty;
            //   txtConPwd.Text = string.Empty;
            Response.Redirect(Constants.UrlConstants.Urls.User.UserList);
        }

        protected string CheckPassword(object act, object username)
        {
            int val = Convert.ToInt32(act);
            string strActive = string.Empty;
            //if (LoggedInUser.Role == (UserRole)1)
            //{
            //    strActive = "../Images/icons/key.png";
            //}
            string loginname = Convert.ToString(username);
            var userinfo = Facade.GetUserByLoginName(loginname);

            if (LoggedInUser.Id == userinfo.Id)
                strActive = "../Images/icons/key.png";
            else
                if (LoggedInUser.Role == (UserRole)1 && userinfo.Role != (UserRole)1)
                {
                    strActive = "../Images/icons/key.png";
                }
                else
                {
                    switch (val)
                    {
                        case 0:
                            strActive = "../Images/icons/key.png";
                            break;

                        case 1:
                            strActive = "../Images/icons/key_disable.png";
                            break;
                    }
                }
            return strActive;
        }

        protected string CheckToolTip(object tool)
        {
            int toolid = Convert.ToInt32(tool);
            string strtool = string.Empty;
            switch (toolid)
            {
                case 0:
                    strtool = "De-activated, click to activate";
                    break;

                case 1:
                    strtool = "Active";
                    break;
            }
            return strtool;
        }

        protected bool ChangePassword(object psd)
        {
            Session.Remove("LoginName");
            string loginname = Convert.ToString(psd);
            Session["LoginName"] = loginname;
            var userinfo = Facade.GetUserByLoginName(loginname);
            int isActive = Convert.ToInt32(userinfo.IsActive == 1);

            //if (LoggedInUser.Role == (UserRole)1)
            //{
            //    return true;
            //}

            if (LoggedInUser.Id == userinfo.Id)
                return true;

            if (LoggedInUser.Role == (UserRole)1 && userinfo.Role != (UserRole)1)
            {
                return true;
            }
            switch (isActive)
            {
                case 0:
                    return true;

                case 1:
                    return false;
            }
            return false;
        }

        protected string CheckActive(object act)
        {
            int val = Convert.ToInt32(act);
            string strActive = string.Empty;
            switch (val)
            {
                case 0:
                    strActive = "../Images/icons/Lock.png";
                    break;

                case 1:
                    strActive = "../Images/icons/tick-circle.png";
                    break;
            }
            return strActive;
        }

        protected bool CheckEnabled()
        {
            return !IsUserOperator;
        }

        protected void LvUserPagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected void BtnSaveClick(object sender, EventArgs e)
        {
            var userPassword = new User();

            userPassword.LoginName = Convert.ToString(Session["LoginName"]);
        }

        protected void GetLoginIdClick(object sender, ListViewItemEventArgs e)
        {
            var dataItem = (ListViewDataItem)e.Item;
            var dataKey = lvUser.DataKeys[dataItem.DisplayIndex];
            if (dataKey != null)
            {
                string userId = dataKey.Value.ToString();
                Session["userId"] = userId;
            }
        }

        protected void BtnCloseClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.User.UserList);
        }

        protected void BtnSearchClick(object sender, EventArgs e)
        {
            if (txtsearchvalue.Text == "" || string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvUser.DataSource = GetUserList();
                lvUser.DataBind();
                setListViewPage();
            }
            else
            {
                lvUser.DataSource = GetUserList(GetUserList(), txtsearchvalue.Text);
                lvUser.DataBind();
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {

                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId,IPAddress);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId,IPAddress);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
    }
}