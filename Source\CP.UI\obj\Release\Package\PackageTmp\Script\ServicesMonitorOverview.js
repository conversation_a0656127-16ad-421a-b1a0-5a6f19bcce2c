﻿

$(".lcs_switch").live("click", function () {
    if ($(".lcs_switch").hasClass("lcs_off")) {


        //$("#accordion h3").removeClass("activeService");
        //globalIndex = 0;        
        globalTime = setInterval(function () { ProfileListRotate() }, refreshTimer);
        $("#accordion").accordion({
            header: "h3",
            collapsible: true,
            active: false
        });
    }

    else {

        abortTimer();
    }
})


function abortTimer() {
    clearInterval(globalTime);
}



function RenderServiceProfile() {
    $('.loadingmessage').show();
    $.ajax({

        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify(),
        url: "ServicesMonitorOverview.aspx/ProfileServiceHtml",
        success: function (msg) {
            $('.loadingmessage').hide();
            if (msg.d != null) {

                $(".outerservicediv").empty();
                $(".totalservice").empty();
                $("#accordion").empty();
                var value = msg.d.split('^');
                $("#accordion").append(value[0]);
                $(".outerservicediv").append(value[1]);
                $(".totalservice").append(value[2]);

                $("#accordion").accordion({
                    header: "h3",
                    collapsible: true,
                    active: false
                });

            }

            ProfileList = $("#accordion").children("h3");
            var profile = $(ProfileList)[0];
            $(profile).addClass("activeService");
            $(".lcs_switch").removeClass("lcs_off").addClass("lcs_on");
            $(".lcs_cursor").attr("title", "On");
            $("[id$='timeInter1']").prop('disabled', false);
            $("#accordion h3").live("click", function () {
                //debugger;               
                if ($(this).siblings("div").hasClass("ui-accordion-content-active")) {
                    abortTimer();
                    $(".lcs_switch").removeClass("lcs_on").addClass("lcs_off");
                    $(".lcs_cursor").attr("title", "Off");
                    $("[id$='timeInter1']").prop('disabled', true);
                    $("#accordion h3").removeClass("activeService");
                    $(this).addClass("activeService");
                }
                //else {                       
                //    $(".lcs_switch").removeClass("lcs_off").addClass("lcs_on");
                //    $("[id$='timeInter1']").prop('disabled', false);
                //    globalTime = setInterval(function () { ProfileListRotate() }, refreshTimer);                        
                //}                
            })
        }
    });
}

function RebindProfileAccordian(PRID) {

    if (PRID != null) {
        $.ajax({
            type: "POST",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            data: JSON.stringify({ 'ProfileId': PRID }),
            url: "ServicesMonitorOverview.aspx/SetProfileList",
            success: function (msg) {
                if (msg.d != null) {


                    $("#accordion").empty();
                    var value = msg.d;
                    $("#accordion").append(value);
                    $("#accordion").accordion("refresh");
                    ProfileList = $("#accordion").children("h3");

                    //Set slider to  Active Service
                    var profile = $(ProfileList)[globalIndex];
                    $(profile).addClass("activeService");

                    $('#accordion').accordion({
                        collapsible: true,
                        active: false
                    });

                }
            }
        });
    }
}

function renderProfile(Id) {
    $('.loadingmessage').show();
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        data: JSON.stringify({ 'Id': Id }),
        url: "ServicesMonitorOverview.aspx/ProfileService",
        success: function (msg) {
            if (msg.d != null) {
                $('.loadingmessage').hide();
                $(".outerservicediv").empty();
                $(".totalservice").empty();
                var value = msg.d.split('^');
                $(".outerservicediv").append(value[0]);
                $(".totalservice").append(value[1]);

            }

            $(".outerservicedivnew").mCustomScrollbar({
                axis: "y"
            });

        }

    });


}

function renderProfileAccordionList() {

    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        data: JSON.stringify({ 'Id': Id }),
        url: "ServicesMonitorOverview.aspx/ProfileService",
        success: function (msg) {
            if (msg.d != null) {

                $(".outerservicediv").empty();
                $(".totalservice").empty();
                var value = msg.d.split('^');
                $(".outerservicediv").append(value[0]);
                $(".totalservice").append(value[1]);

            }

            $(".outerservicedivnew").mCustomScrollbar({
                axis: "y"
            });

        }

    });


}

$('.modal-header a.close').live("click", function () {
    //$("[id$=divServiceModal] tr").remove();
    globalTime = setInterval(function () { ProfileListRotate() }, refreshTimer);

    $("[id$=divServiceModal]").hide();
    $("#modelbg").hide();
    localStorage.setItem("mridulpopup", "false");
});

function CallToServicepopup(ProfileId, ServiceId) {

    if (ProfileId != "" && ServiceId != "") {
        abortTimer();
        $('.loadingmessage').show();
        $.ajax({
            type: "POST",
            url: "ServicesMonitorOverview.aspx/GetServiceDetils",
            data: JSON.stringify({ 'ProfileId': ProfileId, 'ServiceId': ServiceId }),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                $('.loadingmessage').hide();
                $("[id$=divServiceModal]").empty();
                $("[id$=divServiceModal]").html(data.d);
                $("[id$=divServiceModal]").show();
                $("#modelbg").show();
                $(".notifyscrolltable").mCustomScrollbar({
                    axis: "y"
                });
            },
            error: function (data) {
                OnError(data);
            }
        });
    }

}



var globalTime = null;
var globalIndex = 1;
var ProfileList = $("#accordion").children("h3");
var refreshTimer = 60000;

//set interval
var globalTime = setInterval(function () { ProfileListRotate() }, refreshTimer);



//set latest seleced time from dropdown
function getrefreshval(sel) {

    abortTimer();
    refreshTimer = sel.value;
    globalTime = setInterval(function () { ProfileListRotate() }, refreshTimer);


}


function ProfileListRotate() {
    // if  the service is only one then set golbalIndex = 0; 
    if ($("#accordion").children("h3").length == 1) {
        globalIndex = 0;
    }


    ProfileList = $("#accordion").children("h3");

    if (ProfileList.length > 0) {
        var pId = ProfileList[globalIndex].attributes[0].value;
        RebindProfileAccordian(pId);
    }

    if (ProfileList.length > 0) {

        //Render profiled
        var profileId = ProfileList[globalIndex].attributes[0].value;

        //remove the slider of last active service
        if (globalIndex > 0) {
            var lastprofile = ProfileList[globalIndex - 1];
            $(lastprofile).removeClass("activeService");
        }
        else {

            var lastprofile = ProfileList[ProfileList.length - 1];
            $(lastprofile).removeClass("activeService");

        }



        renderProfile(profileId);

        //Set slider to  Active Service
        var profile = $(ProfileList)[globalIndex];
        $(profile).addClass("activeService");

        globalIndex++;

        if (globalIndex == ProfileList.length) {
            globalIndex = 0;
        }

    }

}


function LoadServiceFlow(ServiceId, ProfileId) {

    if (ServiceId != "" && ProfileId != "") {
        $.ajax({
            type: "POST",
            url: "ServicesMonitorOverview.aspx/LoadProfileService",
            data: JSON.stringify({ 'ProfileId': ProfileId, 'ServiceId': ServiceId }),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: true,
            success: function (data) {
                if (data.d != null) {

                    window.location = data.d;
                }
            },
            error: function (data) {
                OnError(data);
            }
        });
    }

}