namespace CP.UI.Report.TelerikReports
{
    using System;
    using System.ComponentModel;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;
    using BusinessFacade;
    using DataAccess;
    using Common.DatabaseEntity;
    using System.Collections.Generic;
    using System.Collections;
    using System.Linq;
    using System.Configuration;
    using System.Data;
    using System.Globalization;
    using System.IO;
    using System.Web;
    using System.Web.UI;
    using System.Web.UI.WebControls;
    using CP.ExceptionHandler;
    using CP.UI.Controls.ReportClients;
    using Gios.Pdf;
    using log4net;
    using SpreadsheetGear;
    using CP.Common.BusinessEntity;

    /// <summary>
    /// Summary description for MimixDatalg.
    /// </summary>
    public partial class DoubleTakeRPT : Telerik.Reporting.Report
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(DoubleTakeRPT));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();
        public DoubleTakeRPT()
        {

            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }


        private void ShowTable()
        {
            try
            {
                _logger.Info("ShowTable Method Execution Start.");
                int iInfraObjId = Convert.ToInt32(this.ReportParameters["iInfraObjId"].Value);
                string strDate = (this.ReportParameters["iStartDate"].Value).ToString();
                string endDate = (this.ReportParameters["iEndDate"].Value).ToString();

                var table = new DataTable();
                table.Columns.Add("SrNo");
                table.Columns.Add("SourceServer");
                table.Columns.Add("TargetServer");
                table.Columns.Add("MirrorStatus");
                table.Columns.Add("MirrorPercentComplete");
                table.Columns.Add("MirrorBytesRemaining");
                table.Columns.Add("TimeStamp");

                IList<MSSqlDoubletek> hyperv = Facade.GetMSSQLDoubleTakeFullDBByDate(iInfraObjId, strDate, endDate);

                if (hyperv != null && hyperv.Count > 0)
                {
                    int i = 1;
                    foreach (var emc in hyperv)
                    {
                        DataRow dr = table.NewRow();

                        dr["SrNo"] = i.ToString();
                        dr["SourceServer"] = !string.IsNullOrEmpty(emc.SourceServer) ? emc.SourceServer : "NA";
                        dr["TargetServer"] = !string.IsNullOrEmpty(emc.TargetServer) ? emc.TargetServer : "NA";
                        dr["MirrorStatus"] = !string.IsNullOrEmpty(emc.MirrorStatus) ? emc.MirrorStatus : "NA";

                        dr["MirrorPercentComplete"] = !string.IsNullOrEmpty(emc.MirrorPercentComplete) ? emc.MirrorPercentComplete : "NA";

                        var Business_Fun = Facade.GetBusinessFunctionByInfraObjectId(Convert.ToInt32(iInfraObjId));
                        double DataLagInBye = Convert.ToDouble(Business_Fun.DataLagInByte);

                        double MirrorByteRemain = 0;
                        double DataLagInBytemb = (DataLagInBye / 1024) / 1024;
                        double MirrorByteRemainMB = 0;//-1;

                        if (!string.IsNullOrEmpty(emc.MirrorBytesRemaining) && emc.MirrorBytesRemaining != "NA")//if (sql.MirrorBytesRemaining != null && sql.MirrorBytesRemaining != "NA")
                        {
                            MirrorByteRemain = Convert.ToDouble(emc.MirrorBytesRemaining);
                            MirrorByteRemainMB = (MirrorByteRemain / 1024) / 1024;
                        }
                        dr["MirrorBytesRemaining"] = MirrorByteRemainMB.ToString();

                        dr["TimeStamp"] = emc.CreateDate != DateTime.MinValue ? Convert.ToString(emc.CreateDate) : "NA";

                        table.Rows.Add(dr);
                        i++;

                    }
                }
                this.DataSource = table;
                _logger.Info("ShowTable Method Execution Completed.");
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method InnerException Message " + ex.InnerException.Message);
            }
            
        }

        private void HyperVRPT_NeedDataSource(object sender, EventArgs e)
        {
            ShowTable();
        }



    }
}