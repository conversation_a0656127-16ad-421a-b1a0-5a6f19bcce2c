﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ParallelProfile", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ParallelProfile : BaseEntity
    {
        [DataMember]
        public string ProfileName { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public string Password { get; set; }
    }
}