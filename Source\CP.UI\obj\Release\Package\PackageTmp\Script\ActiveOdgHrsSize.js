﻿function ActiveOdgHrsSize(effData) {
    debugger;
    var datajson = effData.split(",");
    var dateMonth1 = datajson[0];
    var dateMonth2 = datajson[1];
    var dateMonth3 = datajson[2];
    var dateMonth4 = datajson[3];
    var dateMonth5 = datajson[4];
    var dateMonth6 = datajson[5];
    var dateMonth7 = datajson[6];
    var dateMonth8 = datajson[7];
    var dateMonth9 = datajson[8];
    var dateMonth10 = datajson[9];
    var dateMonth11 = datajson[10];
    var dateMonth12 = datajson[11];
    var dateMonth13 = datajson[12];
    var dateMonth14 = datajson[13];
    var dateMonth15 = datajson[14];
    var dateMonth16 = datajson[15];
    var dateMonth17 = datajson[16];
    var dateMonth18 = datajson[17];
    var dateMonth19 = datajson[18];
    var dateMonth20 = datajson[19];
    var dateMonth21 = datajson[20];
    var dateMonth22 = datajson[21];
    var dateMonth23 = datajson[22];
    var dateMonth24 = datajson[23];
    //Better to construct options first and then pass it as a parameter
    var options = {
        axisX: {
            gridThickness: 0,
            //lineColor: "#f2f2f2",
            //tickColor: "#f2f2f2",
            title: "Hours",
            lineThickness: 1,
            labelFontColor: "#777777",
        },
        axisY: {
            gridThickness: 0,
            minimum: 0,
            title: "Size in MB",
            //maximum: 1000,
            //interval:100,
            //lineColor: "#f2f2f2",
            //tickColor: "#f2f2f2",
            lineThickness: 1,
            labelFontColor: "#777777",
        },
        dataPointMaxWidth: 12,
        animationEnabled: true,
        animationEnabled: true,
        backgroundColor: "#ffffff",
        height: 165,
        width: 390,
        data: [
        {
            color: "#2073ba",
            type: "column",
            dataPoints: [
                { y: parseFloat(dateMonth1), label: "0" },
                { y: parseFloat(dateMonth2), label: "1" },
                { y: parseFloat(dateMonth3), label: "2" },
                { y: parseFloat(dateMonth4), label: "3" },
                { y: parseFloat(dateMonth5), label: "4" },
                { y: parseFloat(dateMonth6), label: "5" },
                { y: parseFloat(dateMonth7), label: "6" },
                { y: parseFloat(dateMonth8), label: "7" },
                { y: parseFloat(dateMonth9), label: "8" },
                { y: parseFloat(dateMonth10), label: "9" },
                { y: parseFloat(dateMonth11), label: "10" },
                { y: parseFloat(dateMonth12), label: "11" },
                { y: parseFloat(dateMonth13), label: "12" },
                { y: parseFloat(dateMonth14), label: "13" },
                { y: parseFloat(dateMonth15), label: "14" },
                { y: parseFloat(dateMonth16), label: "15" },
                { y: parseFloat(dateMonth17), label: "16" },
                { y: parseFloat(dateMonth18), label: "17" },
                { y: parseFloat(dateMonth19), label: "18" },
                { y: parseFloat(dateMonth20), label: "19" },
                { y: parseFloat(dateMonth21), label: "20" },
                { y: parseFloat(dateMonth22), label: "21" },
                { y: parseFloat(dateMonth23), label: "22" },
                { y: parseFloat(dateMonth24), label: "23" }
            ]
        }
        ]
    };

    $("#ODGSizeHrsChart").CanvasJSChart(options);
}