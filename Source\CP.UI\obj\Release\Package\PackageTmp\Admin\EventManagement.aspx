﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="EventManagement.aspx.cs" Inherits="CP.UI.Admin.EventManagement" Title="Continuity Patrol :: Events" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
     <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
     <script type="text/javascript">
         function CancelClick() {
             return false;
         }
    </script>
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="updtpnl" UpdateMode="Conditional" runat="server">
            <ContentTemplate>

               
                <h3>
                    <img src="../Images/event-icon.png">
                    Event Management</h3>
            

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">

                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        InfraObject
                            <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlgroup" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default"
                                            AutoPostBack="True" OnSelectedIndexChanged="ddlgroup_SelectedIndexChanged1">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator2" CssClass="error" runat="server" ErrorMessage="Select Infra object" ControlToValidate="ddlgroup" InitialValue="0"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Manage Events
                            <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlevent" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default"
                                            AutoPostBack="True">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" CssClass="error" runat="server" ErrorMessage="Select Event" ControlToValidate="ddlevent" InitialValue="0"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtName">
                                        Workflow <span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlworkflow" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default"
                                            AutoPostBack="True">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" CssClass="error" runat="server" ErrorMessage="Select WorkFlow" ControlToValidate="ddlworkflow" InitialValue="0"></asp:RequiredFieldValidator>
                                        <asp:Label ID="lblGroup" runat="server" CssClass="error" Visible="False" ForeColor="Red">Please
                Select Group</asp:Label>
                                    </div>
                                </div>

                                <hr class="separator" />
                                <div class="form-actions row">

                                    <div class="col-xs-6">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
                                    </div>


                                    <div class="col-xs-6">
                                        <asp:Button CssClass="btn btn-primary" style="width: 20%; margin-left: 5px;" runat="server" Text="Save" TabIndex="15" CausesValidation="True" ID="btnAdd" OnClick="btnAdd_Click" />&nbsp;&nbsp;
                     <asp:HiddenField ID="hdeventid" runat="server" />
                                    </div>
                                </div>
                <hr class="separator" />
                                <asp:ListView ID="lstevent" runat="server" Visible="true" OnItemDeleting="lstevent_ItemDeleting1" OnItemEditing="lstevent_ItemEditing1">
                                    <LayoutTemplate>
                                        <table class="table table-striped table-bordered table-condensed" width="100%" id="tblevent"
                                            runat="server" style="margin-bottom: 0; margin-top: 10px;">
                                            <thead>
                                                <tr>
                                                
                                                    <th style="width: 5%;">ID.
                                                    </th>
                                                    <th style="width: 23.75%;">InfraObject Name
                                                    </th>
                                                    <th style="width: 23.75%;">Event
                                                    </th>
                                                    <th style="width: 23.75%;">Trigged Workflow
                                                    </th>
                                                    <th style="width: 23.75%;">Action
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="slim-scroll chat-items" data-scroll-height="190px" data-scroll-size="0">
                                            <table class="table table-striped table-bordered table-condensed" width="100%">
                                                <tbody>
                                                    <asp:PlaceHolder ID="itemplaceholder" runat="server" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td style="width: 5%;">
                                                <asp:Label ID="lblid" runat="server" Text='<%#Eval("Id") %>' Visible="False"></asp:Label>
                                                <%#Container.DataItemIndex+1 %>
                                            </td>
                                            <td style="width: 23.75%;">
                                                <asp:Label ID="lblgroupid" runat="server" Text='<%# Eval("InfraobjectId") %>' Visible="False"></asp:Label>
                                                <asp:Label ID="lblgroup" Text='<%# GroupName(Eval("InfraobjectId")) %>' runat="server"></asp:Label>
                                            </td>
                                            <td style="width: 23.75%;">
                                                <asp:Label ID="lblevent" runat="server" Text='<%#Eval("Event") %>'></asp:Label>
                                            </td>
                                            <td style="width: 23.75%;">
                                                <asp:Label ID="lblworkflow" runat="server" Text='<%# WorkflowName(Eval("WorkFlowId")) %>'></asp:Label>
                                                <asp:Label ID="lblworkflowid" runat="server" Text='<%# Eval("WorkFlowId") %>' Visible="False"></asp:Label>
                                            </td>
                                            <td style="width: 23.75%;">
                                                <asp:ImageButton ID="ImageButton1" runat="server" CommandName="Edit" AlternateText="Edit"
                                                    ToolTip="Edit" ImageUrl="../images/icons/pencil.png" CausesValidation="False" />
                                                <asp:ImageButton ID="ImageButton2" runat="server" CommandName="Delete" AlternateText="Delete" CausesValidation="False"
                                                    ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                                            </td>
                                             <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImageButton2"
                                            ConfirmText='<%# "Are you sure want to delete " + Eval("Event") + " ? " %>' OnClientCancel="CancelClick">
                                        </TK1:ConfirmButtonExtender>
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>
                               
                                  </div>
                            </div>
                        </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
