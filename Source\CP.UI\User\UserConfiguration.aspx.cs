﻿using System;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Collections.Generic;
using System.Linq;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Web.Services;
using log4net;
using System.Text.RegularExpressions;
using System.Collections;
using System.DirectoryServices.AccountManagement;
using System.DirectoryServices;
using System.DirectoryServices.ActiveDirectory;
using System.Text;
using Microsoft.Security.Application;
using System.Web;
using System.Net;
using System.Globalization;
using System.Security.Principal;

namespace CP.UI
{
    public partial class UserConfiguration : UserBasePageEditor
    {
        UserControl _userControl = new UserControl();
        readonly IList<ListItem> _previousSelectedItems = new List<ListItem>();
        readonly IList<ListItem> _previousApplicationSelectedItems = new List<ListItem>();
        private static readonly ILog _logger = LogManager.GetLogger(typeof(UserConfiguration));

        public static string IPAddress = string.Empty;

        public override string MessageInitials
        {
            get { return "User"; }
        }

        public override void SaveEditor()
        {
            try
            {
                if (CurrentEntity.IsNew)
                {
                    if (LoggedInUser != null)
                    {
                        CurrentEntity.LastAlertId = LoggedInUser.LastAlertId;
                    }
                    //if (CurrentEntity.LoginType == LoginType.AD)
                    //    CurrentEntity.LoginName = txtLogin.Text;
                    //else
                    //    CurrentEntity.LoginName = Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value);

                    if (CurrentUser.LoginType.ToDescription() == "AD")

                        CurrentEntity.LoginType = LoginType.AD;
                    if (txtLogin.Text.Contains('-'))
                        CurrentEntity.LoginName = Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value);
                    else
                        CurrentEntity.LoginName = txtLogin.Text;

                    if (rdoLogin.SelectedItem.Value == "Group")
                    {
                        _logger.Info("Selected Login Type Value" + rdoLogin.SelectedItem.Value);
                        CurrentEntity.IsGroupDomain = 1;
                    }
                    else
                    {
                        _logger.Info("Selected Login Type Value" + rdoLogin.SelectedItem.Value);
                        CurrentEntity.IsGroupDomain = 0;
                    }


                    txtLogin.Enabled = true;
                    int userinfraloggercnt = 1;
                    CurrentEntity.LastLoginIP = HostAddress;
                    CurrentEntity.LoginPassword = "";
                    CurrentEntity.LoginPassword = Utility.getOriginalEncryData(txtPwd.Text, hdfStaticGuid.Value);
                    CurrentEntity.CreatorId = LoggedInUserId;
                    CurrentEntity.UserInformation.CreatorId = LoggedInUserId;
                    CurrentEntity.UpdatorId = LoggedInUserId;
                   
                    if (CurrentEntity.Role == UserRole.SuperAdmin)
                        CurrentEntity.IsReset = false;
                    else
                       CurrentEntity.IsReset = true;

                    CurrentEntity = Facade.AddUser(CurrentEntity);
                    _logger.DebugFormat("{0} - Created user '<{1}>' with user role '<{2}>' in continuity patrol database ", HostAddress, CurrentEntity.LoginName, CurrentEntity.Role);
                    ActivityLogger.AddLog1(LoggedInUserName, "User List ", UserActionType.CreateUserAccount, "The User Account '" + CurrentEntity.LoginName + "' was added to the user table", LoggedInUserId, IPAddress);
                    _logger.Info("User Account Created successfully " + "'" + CurrentEntity.LoginName + "'" + " was Added to the user table With User " + "'" + LoggedInUserId + "'" + "and IP Address " + "'" + IPAddress + "'" + "Login Type: " + CurrentEntity.IsGroupDomain);
                    if (CurrentEntity.Id > 0)
                    {
                        var userInfraObject = new UserInfraObject { UserId = CurrentEntity.Id };
                        if (CurrentEntity.Role == UserRole.Custom)
                        {
                            CurrentEntity.CustomSubRolType.UserId = CurrentEntity.Id;
                            CurrentEntity.CustomSubRolType.RoleSubtype = txtCustomSubRoleType.Text;
                            var testrolesubtype = Facade.AddCustomRoleSubtype(CurrentEntity.CustomSubRolType);
                        }
                        var selectedItem = Utility.GetSelectedItem(cblstGroup);
                        if (selectedItem != null)
                        {
                            foreach (var listItem in selectedItem)
                            {
                                if (listItem.Text == "ALL")
                                {
                                    continue;
                                }
                                userInfraObject.InfraObjectId = Convert.ToInt32(listItem.Value);
                                userInfraObject.CreatorId = LoggedInUserId;
                                Facade.AddUseInfraObject(userInfraObject);

                                if (userinfraloggercnt == 1)
                                {
                                    ActivityLogger.AddLog1(LoggedInUserName, "User InfraObject", UserActionType.CreateApplicationUserGroup, "The User InfraObject '" + userInfraObject.UserId + "' was added to the user_InfraObject table", LoggedInUserId, IPAddress);
                                    _logger.Info("User Group Created successfully " + "'" + userInfraObject.UserId + "'" + " was Added to the user_InfraObject table With User " + "'" + LoggedInUserId + "'" + "and IP Address " + "'" + IPAddress + "'");
                                }
                                userinfraloggercnt++;
                            }
                            if (ddlAuthenticatinType.SelectedItem.Text == "ActiveDirectory")
                            {
                                if (ckbEmail.Checked == true)
                                {
                                    var smtpInfo = Facade.GetSmtpConfigurations();
                                    if (smtpInfo != null)
                                    {
                                        var userInfo = Facade.GetUserInfoByUserId(userInfraObject.UserId);
                                        var localUser = Facade.GetUserById(userInfraObject.UserId);
                                        var superUser = Facade.GetSuperUserId(CurrentEntity.CompanyId);
                                        foreach (var smtpDetails in smtpInfo)
                                        {
                                            CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();

                                            emailManager.From = CryptographyHelper.Md5Decrypt(smtpDetails.UserName);

                                            //emailManager.Body = string.Concat("Domain user: ", localUser.LoginName, " is successfully Regsitered into Continuity Patrol  with ", "'", CurrentEntity.Role, "'", " Role  ", " at ", "'", userInfo.CreateDate, "'", "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                            //emailManager.Subject = "Active Directory Registration";

                                            emailManager.Body = string.Concat("Domain User: ", localUser.LoginName, " is successfully Regsitered into Continuity Patrol  with ", "'", CurrentEntity.Role, "'", " Role  ", " at ", "'", userInfo.CreateDate, "'", "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                            emailManager.Subject = "Active Directory Registration";

                                            SmtpConfiguration smtpConfig = new SmtpConfiguration();
                                            smtpConfig.SmtpHost = CryptographyHelper.Md5Decrypt(smtpDetails.SmtpHost);
                                            smtpConfig.Port = smtpDetails.Port;

                                            smtpConfig.Password = CryptographyHelper.Md5Decrypt(smtpDetails.Password);

                                            smtpConfig.EnableSSL = true;
                                            smtpConfig.IsBodyHTML = true;

                                            if (LoggedInUserRole.ToString() == "Administrator")
                                            {
                                                foreach (var info in superUser)
                                                {
                                                    smtpConfig.UserName = info.UserInformation.Email;
                                                    // smtpConfig.UserName = userInfo.Email;
                                                    string output = emailManager.SendTestMail(smtpConfig);
                                                }
                                                smtpConfig.UserName = userInfo.Email;
                                                string output1 = emailManager.SendTestMail(smtpConfig);
                                                lblMsg.Text = output1;
                                                lblMsg.Visible = true;

                                                //var alerts = Facade.GetUserInfraObjectByUserId(userInfraObject.UserId);
                                                //foreach (var InfraValue in alerts)
                                                //{
                                                //    var alert = new Alert
                                                //    {
                                                //        Type = "User " + CurrentEntity.LoginName + " created",
                                                //        Severity = "Information",
                                                //        UserDefinedMessage = "AdminUser",
                                                //        SystemMessage = CurrentEntity.LoginName + " created",
                                                //        JobName = "Domain Authentication",
                                                //        InfraObjectId = InfraValue.InfraObjectId
                                                //    };
                                                //    Facade.AddAlert(alert);
                                                //}

                                            }
                                            else
                                            {
                                                smtpConfig.UserName = userInfo.Email;

                                                string output = emailManager.SendTestMail(smtpConfig);
                                                lblMsg.Text = output;
                                                lblMsg.Visible = true;

                                                //var alerts = Facade.GetUserInfraObjectByUserId(userInfraObject.UserId);

                                                //foreach (var InfraValue in alerts)
                                                //{
                                                //    var alert = new Alert
                                                //    {
                                                //        Type = "User " + CurrentEntity.LoginName + " created",
                                                //        Severity = "Information",
                                                //        UserDefinedMessage = "SuperUser",
                                                //        SystemMessage = CurrentEntity.LoginName + " created",
                                                //        JobName = "Domain Authentication",
                                                //        InfraObjectId = InfraValue.InfraObjectId
                                                //    };
                                                //    Facade.AddAlert(alert);
                                                //}
                                            }
                                        }
                                    }
                                    else
                                    {
                                        lblMsg.Visible = true;
                                        lblMsg.Text = "SMTP not configured";
                                    }
                                }
                            }
                            if (ddlAuthenticatinType.SelectedItem.Text == "Local Authentication")
                            {
                                if (ckbEmail.Checked == true)
                                {
                                    var smtpInfo = Facade.GetSmtpConfigurations();
                                    if (smtpInfo != null)
                                    {
                                        var userInfo = Facade.GetUserInfoByUserId(userInfraObject.UserId);
                                        var localUser = Facade.GetUserById(userInfraObject.UserId);
                                        var superUser = Facade.GetSuperUserId(CurrentEntity.CompanyId);
                                        foreach (var smtpDetails in smtpInfo)
                                        {
                                            CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();

                                            emailManager.From = CryptographyHelper.Md5Decrypt(smtpDetails.UserName);

                                            //emailManager.Body = string.Concat("User: ", localUser.LoginName, " is successfully Regsitered into Continuity Patrol  with ", "'", CurrentEntity.Role, "'", " Role  ", " at ", "'", userInfo.CreateDate, "'", "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                            //emailManager.Subject = "User Registration";

                                            emailManager.Body = string.Concat("user: ", localUser.LoginName, " is successfully Regsitered into Continuity Patrol  with ", "'", CurrentEntity.Role, "'", " Role  ", " at ", "'", userInfo.CreateDate, "'", "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                            emailManager.Subject = "User Registration";

                                            SmtpConfiguration smtpConfig = new SmtpConfiguration();
                                            smtpConfig.SmtpHost = CryptographyHelper.Md5Decrypt(smtpDetails.SmtpHost);
                                            smtpConfig.Port = smtpDetails.Port;

                                            smtpConfig.Password = CryptographyHelper.Md5Decrypt(smtpDetails.Password);

                                            smtpConfig.EnableSSL = true;
                                            smtpConfig.IsBodyHTML = true;

                                            if (LoggedInUserRole.ToString() == "Administrator")
                                            {
                                                foreach (var info in superUser)
                                                {
                                                    smtpConfig.UserName = info.UserInformation.Email;
                                                    // smtpConfig.UserName = userInfo.Email;
                                                    string output = emailManager.SendTestMail(smtpConfig);
                                                }
                                                smtpConfig.UserName = userInfo.Email;
                                                string output1 = emailManager.SendTestMail(smtpConfig);
                                                lblMsg.Text = output1;
                                                lblMsg.Visible = true;
                                            }
                                            else
                                            {
                                                smtpConfig.UserName = userInfo.Email;

                                                string output = emailManager.SendTestMail(smtpConfig);
                                                lblMsg.Text = output;
                                                lblMsg.Visible = true;
                                            }

                                            var alerts = Facade.GetUserInfraObjectByUserId(userInfraObject.UserId);

                                            //foreach (var InfraValue in alerts)
                                            //{
                                            //    var alert = new Alert
                                            //    {
                                            //        Type = "Domain User " + CurrentEntity.LoginName + " created",
                                            //        Severity = "Information",
                                            //        UserDefinedMessage = "SuperUser",
                                            //        SystemMessage = CurrentEntity.LoginName + " created",
                                            //        JobName = "Domain Authentication",
                                            //        InfraObjectId = InfraValue.InfraObjectId
                                            //    };
                                            //    Facade.AddAlert(alert);
                                            //}

                                            var InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);
                                            StringBuilder infraCon = new StringBuilder();

                                            foreach (var Infraval in alerts)
                                            {
                                                foreach (InfraObject @infra in InfraList.Where(@infra => Infraval.InfraObjectId == @infra.Id))
                                                {
                                                    if (infraCon.Length > 0)
                                                    {
                                                        infraCon.Append(", ");
                                                    }
                                                    infraCon.Append(@infra.Name);
                                                }
                                            }
                                            var alert = new Alert
                                            {
                                                Type = "Domain User " + CurrentEntity.LoginName + " created",
                                                Severity = "Information",
                                                UserDefinedMessage = infraCon.ToString(),
                                                SystemMessage = CurrentEntity.LoginName + " created",
                                                JobName = "Domain Authentication",
                                                InfraObjectId = alerts.FirstOrDefault().InfraObjectId
                                            };
                                            Facade.AddAlert(alert);

                                        }
                                    }
                                }
                                else
                                {
                                    lblMsg.Visible = true;
                                    lblMsg.Text = "SMTP not configured";
                                }
                            }
                        }

                    }

                }
                else
                {
                    //  int id = CurrentEntity.Id;
                    ////  Session["Id"] = id;
                    //  var userpassword = Facade.GetUserById(id);
                    //  Session["password"] = userpassword.ToString();
                    //  CurrentEntity.LoginPassword = userpassword.LoginPassword;

                    CurrentEntity.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.UpdateUser(CurrentEntity);
                    _logger.DebugFormat("{0} - Updated user '<{1}>' by loggedIn User - {2}", HostAddress, CurrentEntity.LoginName, LoggedInUserName);
                    ActivityLogger.AddLog1(LoggedInUserName, "User List", UserActionType.UpdateUserAccount, "The Users Account '" + CurrentEntity.LoginName + "' was added to the user table", LoggedInUserId, IPAddress);
                    _logger.Info("User Account Updated successfully " + "'" + CurrentEntity.LoginName + "'" + " was Added to the user table With User " + "'" + LoggedInUserId + "'" + "and IP Address " + "'" + IPAddress + "'");
                    if (CurrentEntity != null)
                    {
                        if (CurrentEntity.Role == UserRole.Custom)
                        {
                            CurrentEntity.CustomSubRolType.UserId = CurrentEntity.Id;
                            CurrentEntity.CustomSubRolType.RoleSubtype = txtCustomSubRoleType.Text;
                            var testrolesubtype = Facade.UpdateCustomSubRoleTypebyUserId(CurrentEntity.CustomSubRolType);
                        }
                        var previousItems = Session["PreviousItem"] as IList<ListItem>;
                        var previousapplicationItems = Session["ApplicationPreviousItem"] as IList<ListItem>;

                        var currentSelectedItems = Utility.GetSelectedItem(cblstGroup);
                        //Added by Hanumant -for remove created infra from list
                        //currentSelectedItems = (from itm in currentSelectedItems where itm.Enabled == true select itm).ToList();
                        if (previousItems != null && currentSelectedItems != null)
                        {
                            CompareToCollection(previousItems, currentSelectedItems, 0);
                        }

                        if (ddlAuthenticatinType.SelectedItem.Text == "ActiveDirectory")
                        {
                            if (ckbEmail.Checked == true)
                            {
                                var smtpInfo = Facade.GetSmtpConfigurations();
                                if (smtpInfo != null)
                                {
                                    var userInfo = Facade.GetUserInfoByUserId(CurrentEntity.UserInformation.UserId);
                                    var localUser = Facade.GetUserById(CurrentEntity.UserInformation.UserId);
                                    var superUser = Facade.GetSuperUserId(CurrentEntity.CompanyId);
                                    foreach (var smtpDetails in smtpInfo)
                                    {
                                        CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();

                                        emailManager.From = CryptographyHelper.Md5Decrypt(smtpDetails.UserName);
                                        emailManager.Body = string.Concat("Domain User: ", localUser.LoginName, " is successfully updated into Continuity Patrol  with ", "'", CurrentEntity.Role, "'", " Role  ", " at ", "'", userInfo.UpdateDate, "'", "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                        emailManager.Subject = "Domain User update";
                                        SmtpConfiguration smtpConfig = new SmtpConfiguration();
                                        smtpConfig.SmtpHost = CryptographyHelper.Md5Decrypt(smtpDetails.SmtpHost);
                                        smtpConfig.Port = smtpDetails.Port;

                                        smtpConfig.Password = CryptographyHelper.Md5Decrypt(smtpDetails.Password);

                                        smtpConfig.EnableSSL = true;
                                        smtpConfig.IsBodyHTML = true;

                                        if (LoggedInUserRole.ToString() == "Administrator")
                                        {
                                            foreach (var info in superUser)
                                            {
                                                smtpConfig.UserName = info.UserInformation.Email;
                                                // smtpConfig.UserName = userInfo.Email;
                                                string output = emailManager.SendTestMail(smtpConfig);
                                            }
                                            smtpConfig.UserName = userInfo.Email;
                                            string output1 = emailManager.SendTestMail(smtpConfig);
                                            lblMsg.Text = output1;
                                            lblMsg.Visible = true;

                                        }
                                        else
                                        {
                                            smtpConfig.UserName = userInfo.Email;

                                            string output = emailManager.SendTestMail(smtpConfig);
                                            lblMsg.Text = output;
                                            lblMsg.Visible = true;
                                        }
                                    }
                                }
                                else
                                {
                                    lblMsg.Visible = true;
                                    lblMsg.Text = "SMTP not configured";
                                }
                            }
                        }

                        if (ddlAuthenticatinType.SelectedItem.Text == "Local Authentication")
                        {
                            if (ckbEmail.Checked == true)
                            {
                                var smtpInfo = Facade.GetSmtpConfigurations();
                                if (smtpInfo != null)
                                {
                                    var userInfo = Facade.GetUserInfoByUserId(CurrentEntity.UserInformation.UserId);
                                    var localUser = Facade.GetUserById(CurrentEntity.UserInformation.UserId);
                                    var superUser = Facade.GetSuperUserId(CurrentEntity.CompanyId);
                                    foreach (var smtpDetails in smtpInfo)
                                    {
                                        CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();

                                        emailManager.From = CryptographyHelper.Md5Decrypt(smtpDetails.UserName);
                                        emailManager.Body = string.Concat("User: ", localUser.LoginName, " is successfully updated into Continuity Patrol  with ", "'", CurrentEntity.Role, "'", " Role  ", " at ", "'", userInfo.UpdateDate, "'", "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                        emailManager.Subject = "User update";
                                        SmtpConfiguration smtpConfig = new SmtpConfiguration();
                                        smtpConfig.SmtpHost = CryptographyHelper.Md5Decrypt(smtpDetails.SmtpHost);
                                        smtpConfig.Port = smtpDetails.Port;

                                        smtpConfig.Password = CryptographyHelper.Md5Decrypt(smtpDetails.Password);

                                        smtpConfig.EnableSSL = true;
                                        smtpConfig.IsBodyHTML = true;

                                        if (LoggedInUserRole.ToString() == "Administrator")
                                        {
                                            foreach (var info in superUser)
                                            {
                                                smtpConfig.UserName = info.UserInformation.Email;
                                                // smtpConfig.UserName = userInfo.Email;
                                                string output = emailManager.SendTestMail(smtpConfig);
                                            }
                                            smtpConfig.UserName = userInfo.Email;
                                            string output1 = emailManager.SendTestMail(smtpConfig);
                                            lblMsg.Text = output1;
                                            lblMsg.Visible = true;

                                        }
                                        else
                                        {
                                            smtpConfig.UserName = userInfo.Email;

                                            string output = emailManager.SendTestMail(smtpConfig);
                                            lblMsg.Text = output;
                                            lblMsg.Visible = true;
                                        }
                                    }
                                }
                                else
                                {
                                    lblMsg.Visible = true;
                                    lblMsg.Text = "SMTP not configured";
                                }
                            }
                        }
                    }
                }

                #region commented
                /*  if (CurrentEntity.IsNew)
              {

                  if (LoggedInUser != null)
                  {
                      CurrentEntity.LastAlertId = LoggedInUser.LastAlertId;
                  }
                  CurrentEntity.LastLoginIP = HostAddress;
                  CurrentEntity.CreatorId = LoggedInUserId;
                  CurrentEntity.UserInformation.CreatorId = LoggedInUserId;
                  CurrentEntity.UpdatorId = LoggedInUserId;
                  CurrentEntity.IsReset = true;
                  CurrentEntity = Facade.AddUser(CurrentEntity);
                  _logger.DebugFormat("{0} - Create '<{1}>' User <({2})> - '<UserId-{3}>'", HostAddress,  CurrentEntity.LoginName ,  CurrentEntity.Role , LoggedInUserName);
                  ActivityLogger.AddLog(LoggedInUserName, "User List ", UserActionType.CreateUserAccount, "The User Account '" + CurrentEntity.LoginName + "' was added to the user table", LoggedInUserId);
                  if (CurrentEntity.Id > 0)
                  {
                      var userInfraObject = new UserInfraObject { UserId = CurrentEntity.Id };



                      var selectedItem = Utility.GetSelectedItem(cblstGroup);
                      if (selectedItem != null)
                      {
                          foreach (var listItem in selectedItem)
                          {
                              if (listItem.Text == "ALL")
                              {
                                  continue;
                              }
                              userInfraObject.InfraObjectId = Convert.ToInt32(listItem.Value);                          
                              userInfraObject.CreatorId = LoggedInUserId;
                              Facade.AddUseInfraObject(userInfraObject);
                              ActivityLogger.AddLog(LoggedInUserName, "User InfraObject", UserActionType.CreateApplicationUserGroup, "The User InfraObject '" + userInfraObject.UserId + "' was added to the user_InfraObject table", LoggedInUserId);
                          }
                      }

                      //var appselectedItem = Utility.GetSelectedItem(cblstApplication);
                      //if (appselectedItem != null)
                      //{
                      //    foreach (var listItem in appselectedItem)
                      //    {
                      //        if (listItem.Text == "ALL")
                      //        {
                      //            continue;
                      //        }
                      //        userInfraObject.InfraObjectId = Convert.ToInt32(listItem.Value);
                      //        userInfraObject.IsApplication = 1;
                      //        userInfraObject.CreatorId = LoggedInUserId;
                      //        Facade.AddUseInfraObject(userInfraObject);
                      //        ActivityLogger.AddLog(LoggedInUserName, "User Group", UserActionType.CreateApplicationUserGroup, "The User Group '" + userInfraObject.UserId + "' was added to the user group table", LoggedInUserId);
                      //    }
                      //}
                  }

              }
              else
              {
                //  int id = CurrentEntity.Id;
                ////  Session["Id"] = id;
                //  var userpassword = Facade.GetUserById(id);
                //  Session["password"] = userpassword.ToString();
                //  CurrentEntity.LoginPassword = userpassword.LoginPassword;

                  CurrentEntity.UpdatorId = LoggedInUserId;
                  CurrentEntity = Facade.UpdateUser(CurrentEntity);
                  _logger.DebugFormat("{0} - Update {1} User - {2}", HostAddress, CurrentEntity.LoginName, LoggedInUserName);
                  ActivityLogger.AddLog(LoggedInUserName, "User List", UserActionType.UpdateUserAccount, "The Users Account '" + CurrentEntity.LoginName + "' was added to the user table", LoggedInUserId);
                  if (CurrentEntity != null)
                  {
                      var previousItems = Session["PreviousItem"] as IList<ListItem>;
                      var previousapplicationItems = Session["ApplicationPreviousItem"] as IList<ListItem>;

                      var currentSelectedItems = Utility.GetSelectedItem(cblstGroup);
                      if (previousItems != null && currentSelectedItems != null)
                      {
                          CompareToCollection(previousItems, currentSelectedItems, 0);
                      }

                      //var currentApplicationSelectedItems = Utility.GetSelectedItem(cblstApplication);
                      //if (previousapplicationItems != null && currentApplicationSelectedItems != null)
                      //{
                      //    CompareToCollection(previousapplicationItems, currentApplicationSelectedItems, 1);
                      //}
                  }
              }*/
                #endregion commented

            }
            catch (CpException ex)
            {
                if (ex != null)
                {
                    _logger.Error("CP exception while Creating Updating  in  SaveEditor method on UserConfiguration page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        _logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                ExceptionManager.Manage(ex);
            }


        }

        public override void BuildEntities()
        {

            //  CurrentEntity.LoginName = Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value); //txtLogin.Text.ToLower();
            string strPass = Utility.getOriginalEncryData(txtPwd.Text, hdfStaticGuid.Value);
            if (strPass != string.Empty)
            {
                CurrentEntity.LoginPassword = txtPwd.Text;
                // CurrentEntity.LoginType = LoginType.FormAuthentication;

                if (ddlAuthenticatinType.SelectedIndex == 1)// ****************************************
                {

                    CurrentEntity.LoginType = LoginType.FormAuthentication;
                    //CurrentEntity.LastLoginDate = DateTime.Now;
                    DateTime emptyDateTime = new DateTime();
                    CurrentEntity.LastLoginDate = emptyDateTime;

                    // CurrentEntity.LastLoginDate =  DateTime.MaxValue;
                }
                else
                {
                    CurrentEntity.LoginType = LoginType.AD;
                }
            }
            else
            {
                int id = CurrentEntity.Id;
                CurrentEntity.LoginPassword = string.Empty;
                var userpassword = Facade.GetUserById(id);

                if (userpassword == null)
                {
                    CurrentEntity.LoginPassword = "";
                }
                else if (userpassword.LoginPassword != "")
                {
                    if (CurrentUser.LoginType.ToDescription() == "AD")
                    {
                        CurrentEntity.LoginPassword = "";
                    }
                    else
                    {
                        CurrentEntity.LoginPassword = userpassword.LoginPassword;
                    }
                    if (CurrentEntity.Role.ToString() == "SuperAdmin")
                    {

                        if (CurrentEntity.Accessrole == "root")
                        {

                            CurrentEntity.Accessrole = "root";
                        }
                        else
                        {

                            CurrentEntity.Accessrole = "";
                        }
                    }
                }
                //if (RadioButtonList1.SelectedIndex == 1)
                //{
                //    CurrentEntity.LoginPassword = userpassword.LoginPassword;
                //}
                //else
                //{
                //    CurrentEntity.LoginPassword = "";
                //}


                if (CurrentUser.LoginType.ToDescription() == "AD" || CurrentUser.LoginType.ToDescription() == "Undefined")
                {
                    CurrentEntity.LoginType = LoginType.AD;
                    if (txtLogin.Text.Contains('-'))
                        CurrentEntity.LoginName = Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value);
                    else
                        CurrentEntity.LoginName = txtLogin.Text;

                    //  CurrentEntity.LastLoginDate = DateTime.MaxValue;   
                    DateTime emptyDateTime = new DateTime();

                    CurrentEntity.LastLoginDate = emptyDateTime;
                    if (txtLogin.Text == string.Empty || txtUserName.Text == string.Empty)
                    {
                        string domainUser = string.Empty;
                        string domainName = string.Empty;
                        domainUser = ((txtDomainUserNew.Value != string.Empty) ? txtDomainUserNew.Value : txtDomainUser.Value);
                        domainName = ((combobox1.Value != string.Empty) ? combobox1.Value : combobox2.Value);
                        txtLogin.Text = domainName + @"\" + domainUser;

                        txtUserName.Text = domainUser;
                    }
                }
                else
                {
                    CurrentEntity.LoginType = LoginType.FormAuthentication;
                    // CurrentEntity.LastLoginDate = DateTime.Now;
                    DateTime emptyDateTime = new DateTime();
                    CurrentEntity.LastLoginDate = emptyDateTime;
                }
                //if (RadioButtonList1.SelectedIndex == 0)
                //{
                //    CurrentEntity.LoginType = LoginType.AD;
                //  //  CurrentEntity.LastLoginDate = DateTime.MaxValue;   
                //    DateTime emptyDateTime = new DateTime();

                //    CurrentEntity.LastLoginDate = emptyDateTime;
                //}
                //else
                //{
                //    CurrentEntity.LoginType = LoginType.FormAuthentication;
                //    CurrentEntity.LastLoginDate = DateTime.Now;
                //}





                ////if (CurrentEntity.LoginType == LoginType.FormAuthentication)
                ////{
                //    CurrentEntity.LoginType = LoginType.FormAuthentication;
                //}
                //else
                //{
                //    CurrentEntity.LoginType = LoginType.AD;
                //}
            }
            CurrentEntity.UserInformation.UserName = txtUserName.Text;
            CurrentEntity.CompanyId = Convert.ToInt32(ddlCompany.SelectedValue);


            if (LoggedInUserRole.ToString() == "SuperAdmin")
            {
                if (ddlRole.SelectedItem.Text == "SuperAdmin")//|| ddlRole.SelectedIndex == 3 || ddlRole.SelectedIndex == 4
                {
                    CurrentEntity.InfraObjectAllFlag = true;
                    CurrentEntity.ApplicationAllFlag = true;
                    Session["InfraObject"] = "T";
                }
                else if (cblstGroup.SelectedItem == null)
                {
                    lblMessage.Text = "Please Select the Application";
                    Session["InfraObject"] = "F";

                }
                else
                {
                    if (cblstGroup.SelectedItem != null)
                    {
                        CurrentEntity.InfraObjectAllFlag = cblstGroup.SelectedItem.Text == "ALL";
                        Session["InfraObject"] = "T";
                    }
                    else
                    {
                        Session["InfraObject"] = "F";
                        CurrentEntity.InfraObjectAllFlag = false;
                    }
                }
            }
            if (LoggedInUserRole.ToString() == "Administrator")
            {
                if (ddlRole.SelectedIndex == 0)//|| ddlRole.SelectedIndex == 3 || ddlRole.SelectedIndex == 4
                {
                    CurrentEntity.InfraObjectAllFlag = true;
                    CurrentEntity.ApplicationAllFlag = true;
                    Session["InfraObject"] = "T";
                }
                else if (cblstGroup.SelectedItem == null)
                {
                    lblMessage.Text = "Please Select the Application";
                    Session["InfraObject"] = "F";

                }
                else
                {
                    if (cblstGroup.SelectedItem != null)
                    {
                        CurrentEntity.InfraObjectAllFlag = cblstGroup.SelectedItem.Text == "ALL";
                        Session["InfraObject"] = "T";
                    }
                    else
                    {
                        Session["InfraObject"] = "F";
                        CurrentEntity.InfraObjectAllFlag = false;
                    }
                }
            }

            CurrentEntity.Role = (UserRole)Enum.Parse(typeof(UserRole), ddlRole.SelectedValue, true);
            CurrentEntity.UserInformation.Address = txtAddress.Text;
            CurrentEntity.UserInformation.Phone = txtCountryCode.Text + "-" + txtStateCode.Text + "-" + txtPhone.Text;
            if (CurrentEntity.Role == UserRole.Custom)
            {
                // DivCustomSubRoleType.Visible = true;
                // CurrentEntity.CustomSubRolType.UserId = CurrentEntity.Id;
                // CurrentEntity.CustomSubRolType.RoleSubtype = txtCustomSubRoleType.Text;
                //// var testrolesubtype = Facade.AddCustomRoleSubtype(CurrentEntity.CustomSubRolType);
            }
            else
            {
                DivCustomSubRoleType.Visible = false;
            }
            //    CurrentEntity.LastLoginDate = DateTime.Now;
            CurrentEntity.LastPasswordChanged = DateTime.Now;
            if (ChkEmail.Checked && ChkMobile.Checked)
            {
                CurrentEntity.UserInformation.AlertMode = AlertModeType.EmailandSMS;
                CurrentEntity.UserInformation.Mobile = txtMCountryCode.Text + "-" + txtMobile.Text;
                CurrentEntity.UserInformation.Email = txtEmail.Text;
            }
            else if (ChkMobile.Checked)
            {
                CurrentEntity.UserInformation.AlertMode = AlertModeType.SMS;
                CurrentEntity.UserInformation.Mobile = txtMCountryCode.Text + "-" + txtMobile.Text;
                CurrentEntity.UserInformation.Email = "NA";
            }
            else if (ChkEmail.Checked)
            {
                CurrentEntity.UserInformation.AlertMode = AlertModeType.Email;
                CurrentEntity.UserInformation.Email = txtEmail.Text;
                CurrentEntity.UserInformation.Mobile = "NA";
            }
            else
            {
                CurrentEntity.UserInformation.AlertMode = AlertModeType.Undefined;
                CurrentEntity.UserInformation.Email = "NA";
                CurrentEntity.UserInformation.Mobile = "NA";
            }
        }

        #region populate password policy expression from setting table from setting page
        private void populatepasswordValue()
        {
            try
            {
                IList<Settings> settingInfo = Facade.GetAllSetting();
                if (settingInfo == null)
                {
                    passExp.Title = "Password must have minimum (8,16) characters. At least 1 upper-case alphabetic, 4 lower-case, 3 numeric character and 1 special characters.";
                }
                else
                {
                    foreach (var settingValue in settingInfo)
                    {
                        if (settingValue.Key == "PwdKey")
                        {
                            var getValue = CryptographyHelper.Md5Decrypt(settingValue.Value);

                            string[] expPwd = getValue.Split(')');

                            if (expPwd[0] == "")
                            {
                                img123.Visible = false;
                            }
                            else
                            {
                                #region Regularexpression string
                                string min = expPwd[0];
                                string[] minchar = min.Split('{');
                                string[] minchar1 = min.Split(',');
                                string lastchars = minchar1[1];
                                string[] maxcharc = lastchars.Split('}');
                                string maxchar = maxcharc[0];
                                string minchars = minchar[1];
                                string[] minch = minchars.Split(',');
                                string minnum = minch[0];
                                string number = expPwd[2];
                                string characters = expPwd[5];
                                string lower = expPwd[3];
                                string[] lowertext = lower.Split(']');
                                string smallchar = lowertext[1];
                                string specchar = expPwd[8];
                                passExp.Title = "*password must have minimum {" + minnum + "," + maxchar + "} characters with atleast " + characters.Trim(new Char[] { ' ', '{', '}' }) + " upper-case alphabet, " + smallchar.Trim(new Char[] { ' ', '{', '}' }) + " lower-case alphabet, " + number.Trim(new Char[] { ' ', '{', '}' }) + " numeric character, " + specchar.Trim(new Char[] { ' ', '{', '}' }) + " special character.";
                                break;
                                #endregion
                            }
                        }
                        else if (settingValue.Key != "PwdKey")
                        {
                            passExp.Title = "Password must have minimum (8,16) characters. At least 1 upper-case alphabetic, 4 lower-case, 3 numeric character and 1 special characters.";
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        #endregion

        public override void PrepareView()
        {
            try
            {
                hdfStaticGuid.Value = Guid.NewGuid().ToString();
                //ViewState["_token"] = UrlHelper.AddTokenToRequest();
                //if (ViewState["_token"] != null)
                //{
                //    hdtokenKey.Value = ViewState["_token"].ToString();
                //}

                //BOC Validate Request
                ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
                if (ViewState["_token"] != null)
                {
                    hdtokenKey.Value = ViewState["_token"].ToString();
                }
                //EOC 


                if (IsUserOperator || IsUserManager)
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }
                if (IsUserCustom)
                {
                    IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                    if (lstAccess != null)
                    {
                        var ObjAccess = lstAccess.FirstOrDefault(x => x.AccessMenuType == AccessManagerType.Admin.ToString());
                        if (ObjAccess == null)
                        {
                            Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                            return;
                        }
                    }
                    else
                    {
                        Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                        return;
                    }

                }
                DivCustomSubRoleType.Visible = false;
                divAD.Visible = false;
                txtLogin.Text = string.Empty;
                txtLogin.Enabled = true;

                //txtLogin.Attributes.Add("onblur", "ValidatorValidate(" + rqdUsrID.ClientID + ")");
                //txtPwd.Attributes.Add("onblur", "ValidatorValidate(" + rfvPwd.ClientID + ")");
                //txtConPwd.Attributes.Add("onblur", "ValidatorValidate(" + rfvConPwd.ClientID + ")");
                txtLogin.Attributes.Add("onblur", "ValidatorValidate(" + rqdUsrID.ClientID + "),getHashData(" + txtLogin.ClientID + ")");
                txtPwd.Attributes.Add("onblur", "ValidatorValidate(" + rfvPwd.ClientID + "),getHashData(" + txtPwd.ClientID + ")");
                txtConPwd.Attributes.Add("onblur", "ValidatorValidate(" + rfvConPwd.ClientID + "),getHashData(" + txtConPwd.ClientID + ")");
                txtUserName.Attributes.Add("onblur", "ValidatorValidate(" + rfvUserName.ClientID + ")");
                ddlCompany.Attributes.Add("onblur", "ValidatorValidate(" + rfvcompamny.ClientID + ")");
                ddlRole.Attributes.Add("onblur", "ValidatorValidate(" + rfvRole.ClientID + ")");
                txtAddress.Attributes.Add("onblur", "ValidatorValidate(" + rfvAddress.ClientID + ")");
                txtCountryCode.Attributes.Add("onblur", "ValidatorValidate(" + rfvphone2.ClientID + ")");
                txtStateCode.Attributes.Add("onblur", "ValidatorValidate(" + rfvphone1.ClientID + ")");
                txtPhone.Attributes.Add("onblur", "ValidatorValidate(" + rfvphone.ClientID + ")");
                txtMobile.Attributes.Add("onblur", "ValidatorValidate(" + rfvmobile.ClientID + ")");
                txtEmail.Attributes.Add("onblur", "ValidatorValidate(" + rfvEmail.ClientID + ")");
                ;
                //if (IsUserOperator || IsUserManager || IsUserSuperAdmin)
                //{
                //    Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);
                //}

                Utility.SelectMenu(Master, "Module8");
                Utility.PopulateAuthenticationType(ddlAuthenticatinType);
                ////var companylist12 = Facade.GetCompanyProfileByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, LoggedInUserCompany.IsParent);
                //var companylist = Facade.GetCompanyProfileByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
                //if (companylist != null)
                //{
                //    var companylist1 = (from n in companylist where n.CompanyInformation.CreatorId == Convert.ToInt32(LoggedInUserId) || Convert.ToInt32(n.CompanyInformation.CompanyId) == Convert.ToInt32(LoggedInUserCompanyId) select n).ToList();
                //    //var companylist1 = from n in companylist where n.CreatorId == Convert.ToInt32(LoggedInUserId) || Convert.ToInt32(n.CompanyId) == Convert.ToInt32(LoggedInUserCompanyId) select n;
                //    if (companylist1 != null)
                //    {
                //        ddlCompany.DataSource = companylist1;
                //        ddlCompany.DataTextField = "DisplayName";
                //        ddlCompany.DataValueField = "Id";
                //        ddlCompany.DataBind();
                //        ddlCompany.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectCompanyName, "000"));
                //    }
                //}

                if (IsSuperAdmin)
                {

                    var companylist = Facade.GetCompanyProfileByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
                    //if (companylist.Count>0 && companylist!=null)
                    //{
                    //    ddlCompany.DataSource = companylist;
                    //    ddlCompany.DataTextField = "Name";
                    //    ddlCompany.DataValueField = "Id";
                    //    ddlCompany.DataBind();
                    //}
                    ddlCompany.DataSource = companylist;
                    ddlCompany.DataTextField = "Name";
                    ddlCompany.DataValueField = "Id";
                    ddlCompany.DataBind();
                    ddlCompany.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectCompanyName, "000"));
                }
                else
                {

                    IList<CompanyProfile> companylist = Facade.GetAllCompanyProfiles();
                    IList<CompanyProfile> companylistfinal = null;
                    var companylist1 = from n in companylist
                                       where n.CompanyInformation.CreatorId == Convert.ToInt32(Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"])) || Convert.ToInt32(n.CompanyInformation.CompanyId) == Convert.ToInt32(LoggedInUserCompanyId)
                                       select n;
                    companylistfinal = companylist1.ToList();

                    var sites = Facade.GetSiteByUserId(Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]));
                    if (sites != null && sites.Count != 0)
                    {
                        var ids = sites.Select(x => x.CompanyId).Distinct();
                        foreach (var c in ids)
                        {
                            foreach (var cmp in companylist)
                            {
                                if (cmp.Id == c && !companylistfinal.Any(f => f.Id == c))
                                {
                                    companylistfinal.Add(cmp);
                                }
                            }
                        }
                    }

                    if (companylistfinal != null)
                    {
                        ddlCompany.DataSource = companylistfinal;
                        ddlCompany.DataTextField = "Name";
                        ddlCompany.DataValueField = "Id";
                        ddlCompany.DataBind();
                        ddlCompany.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectCompanyName, "000"));
                    }

                }


                if (LoggedInUserCompany.IsParent)
                {
                    //cblstGroup.Items.Insert(0, new ListItem("ALL", "0"));
                    ddlRole.ClearItems();
                    if (LoggedInUserRole.ToString() == "SuperAdmin")
                    {
                        ddlRole.AddItem("Select Role", "0000");
                        ddlRole.AddItem("SuperAdmin", "SuperAdmin");
                        ddlRole.AddItem("Administrator", "Administrator");
                        ddlRole.AddItem("Operator", "Operator");
                        ddlRole.AddItem("Manager", "Manager");
                        ddlRole.AddItem("Custom", "Custom");
                    }
                    else
                    {
                        ddlRole.AddItem("Select Role", "0000");
                        // ddlRole.AddItem("Administrator", "Administrator");
                        ddlRole.AddItem("Operator", "Operator");
                        ddlRole.AddItem("Manager", "Manager");
                        //ddlRole.AddItem("Custom", "Custom");
                    }

                }
                else
                {
                    ddlRole.ClearItems();
                    ddlRole.AddItem("Select Role", "0000");
                    ddlRole.AddItem("Administrator", "Administrator");
                    ddlRole.AddItem("Operator", "Operator");
                    ddlRole.AddItem("Manager", "Manager");
                    // ddlRole.AddItem("Custom", "Custom");
                }
                populatepasswordValue();
                PrepareEditView();

            }

            catch (Exception ex)
            {
                _logger.Error("Exception occurred in PrepareView error message " + ex.Message);
                if (ex.InnerException != null)
                {
                    _logger.Error("Exception occurred in PrepareView InnerException " + ex.InnerException.Message);
                    _logger.Error("Exception occurred in PrepareView  StackTrace" + ex.InnerException.StackTrace);
                }
            }
            //_userControl = (UserControl)Page.LoadControl("../Controls/CaptchaImage.ascx");
        }

        /* public override void PrepareEditView()
        {            
            if (CurrentEntityId <= 0) return;
            btnSave.Text = "Update";
            lblUserName.Visible = false;
           // revLoginName.Visible = false;
            txtLogin.Enabled = false;
            txtLogin.CssClass = "form-control";
            txtPwd.Visible = false;
            txtConPwd.Visible = false;
            //lblPassword.Visible = false;
            //lblCPassword.Visible = false;
            //div_hr1.Visible = false;
            //div_hr2.Visible = false;
            rfvPwd.Visible = false;
            rfvPwd.Enabled = false;
            rfvConPwd.Visible = false;
            rfvConPwd.Enabled = false;
            rqdUsrID.Visible = false;
            rqdUsrID.Enabled = false;
            ucpwd.Visible = false;

            txtLogin.Text = CurrentUser.LoginName;
            txtPwd.Text = CurrentUser.LoginPassword;
            txtUserName.Text = CurrentUser.UserInformation.UserName;
            ddlCompany.SelectedValue = Convert.ToString(CurrentUser.CompanyId);
            DdlCompanySelectedIndexChanged(null, null);
            //ddlCompany.Enabled = false;
            ddlRole.SelectedValue = Convert.ToString(CurrentUser.Role);
            txtAddress.Text = CurrentUser.UserInformation.Address;
            txtPhone.Text = CurrentUser.UserInformation.Phone;
            var splitphone = txtPhone.Text.Split('-');
            txtPhone.Text = splitphone[2];
            txtStateCode.Text = splitphone[1];
            txtCountryCode.Text = splitphone[0];
            txtMobile.Text = CurrentUser.UserInformation.Mobile;
            if (txtMobile.Text != "NA")
            {
                var splitmobile = txtMobile.Text.Split('-');
                txtMCountryCode.Text = splitmobile[0];
                txtMobile.Text = splitmobile[1];
            }
            txtEmail.Text = CurrentUser.UserInformation.Email;
            CheckAlertMode(CurrentUser.UserInformation.AlertMode.ToString());
            CheckGroupBoxList();
        }
        */

        public override void PrepareEditView()
        {
            try
            {

                if (CurrentEntityId <= 0) return;

                DivCustomSubRoleType.Visible = false;
                btnSave.Text = "Update";
                lblUserName.Visible = false;
                //  revLoginName.Visible = false;
                txtLogin.Enabled = false;
                txtLogin.CssClass = "form-control";
                txtPwd.Visible = false;
                // txtConPwd11.Visible = false;
                //lblPassword.Visible = false;
                //lblCPassword.Visible = false;
                //div_hr1.Visible = false;
                //div_hr2.Visible = false;
                // rfvPwd11.Visible = false;
                //rfvPwd11.Enabled = false;
                // rfvConPwd11.Visible = false;
                // rfvConPwd11.Enabled = false;
                //  rqdUsrID.Visible = false;
                // rqdUsrID.Enabled = false;          
                // Updatepanel4.Update();
                ucpwd.Visible = false;
                txtLogin.Text = CurrentUser.LoginName;
                txtPwd.Text = CurrentUser.LoginPassword;
                txtUserName.Text = CurrentUser.UserInformation.UserName;
                ddlCompany.SelectedValue = Convert.ToString(CurrentUser.CompanyId);
                DdlCompanySelectedIndexChanged(null, null);
                //ddlCompany.Enabled = false;
                ddlRole.SelectedValue = Convert.ToString(CurrentUser.Role);
                Utility.PopulateAuthenticationType(ddlAuthenticatinType);

                if (CurrentUser.Role == UserRole.Custom)
                {
                    var SubRoleType = Facade.GetCustomSubRoleTypebyUserId(CurrentUser.Id);
                    DivCustomSubRoleType.Visible = true;
                    txtCustomSubRoleType.Text = SubRoleType.RoleSubtype;
                }

                txtAddress.Text = CurrentUser.UserInformation.Address;
                txtPhone.Text = CurrentUser.UserInformation.Phone;
                var splitphone = txtPhone.Text.Split('-');
                txtPhone.Text = splitphone[2];
                txtStateCode.Text = splitphone[1];
                txtCountryCode.Text = splitphone[0];
                txtMobile.Text = CurrentUser.UserInformation.Mobile;
                if (txtMobile.Text != "NA")
                {
                    var splitmobile = txtMobile.Text.Split('-');
                    txtMCountryCode.Text = splitmobile[0];
                    txtMobile.Text = splitmobile[1];
                }
                txtEmail.Text = CurrentUser.UserInformation.Email;

                if (CurrentUser.LoginType.ToDescription() == "AD")
                {
                    divAD.Visible = true;
                    divUserLogin.Visible = true;
                    rdoLogin.Enabled = false;


                    rdoLogin.SelectedIndex = CurrentUser.IsGroupDomain;
                    ddlAuthenticatinType.SelectedIndex = 2; // ****************************************
                    ListItem listItem = ddlAuthenticatinType.Items.FindByValue("Form Authentication");
                    ddlAuthenticatinType.Items.Remove(listItem);
                    //ddlAuthenticatinType.Enabled = false;

                    //txtLogin.Enabled = false;
                }
                else
                {
                    divAD.Visible = false;
                    //RadioButtonList1.SelectItemByText("Form Authentication");
                    ddlAuthenticatinType.SelectedIndex = 1;// ****************************************
                    ListItem listItem = ddlAuthenticatinType.Items.FindByValue("AD");
                    ddlAuthenticatinType.Items.Remove(listItem);

                    //ddlAuthenticatinType.Enabled = true;
                    //txtLogin.Enabled = true;
                }

                CheckAlertMode(CurrentUser.UserInformation.AlertMode.ToString());
                CheckGroupBoxList();
                divAD.Visible = false;


            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in PrepareEditView error message " + ex.Message);
                if (ex.InnerException != null)
                {
                    _logger.Error("Exception occurred in PrepareEditView InnerException " + ex.InnerException.Message);
                    _logger.Error("Exception occurred inPrepareEditView  StackTrace" + ex.InnerException.StackTrace);
                }
            }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.User.UserList;
                }
                return string.Empty;
            }
        }

        private void CheckGroupBoxList()
        {
            Session.Remove("PreviousItem");
            Session.Remove("ApplicationPreviousItem");
            Facade.GetUserById(CurrentUserId);
            var userInfraList = Facade.GetUserInfraObjectByUserId(CurrentUserId);

            if (userInfraList != null)
            {
                foreach (var listItem in from ListItem listItem in cblstGroup.Items from userInfraItem in userInfraList where listItem.Value == userInfraItem.InfraObjectId.ToString() && userInfraItem.IsApplication == 0 select listItem)
                {
                    listItem.Selected = true;
                    _previousSelectedItems.Add(listItem);
                }

            }


            CheckBoxListAllSelected(cblstGroup);
            //Added by hanumant 05.07.2018
            if (CurrentUserId != 0)
            {
                int i = 0;
                foreach (ListItem listItem in cblstGroup.Items)
                {
                    if (Convert.ToInt32(listItem.Value) != 0)
                    {
                        var infra = Facade.GetInfraObjectById(Convert.ToInt32(listItem.Value));
                        if (infra.CreatorId == CurrentUserId)
                        {
                            listItem.Selected = true;
                            listItem.Enabled = false;
                            cblstGroup.Items[i].Enabled = false;
                        }
                    }
                    i++;
                }
            }
            Session["PreviousItem"] = _previousSelectedItems;
            Session["ApplicationPreviousItem"] = _previousApplicationSelectedItems;
        }

        private void CheckAlertMode(string alertMode)
        {
            switch (alertMode)
            {
                case "Email":
                    ChkEmail.Checked = true;
                    divEmail.Visible = true;
                    break;
                case "SMS":
                    ChkMobile.Checked = true;
                    divMobile.Visible = true;
                    break;
                case "EmailandSMS":
                    ChkEmail.Checked = true;
                    ChkMobile.Checked = true;
                    divEmail.Visible = true;
                    divMobile.Visible = true;
                    break;
                case "Undefined":
                    ChkEmail.Checked = true;
                    ChkMobile.Checked = false;
                    break;

            }
        }

        private bool CheckUserNameExist()
        {
            // return Facade.IsExistUserByLoginName(txtLogin.Text);
            return Facade.IsExistUserByLoginName(Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value));
        }

        private bool CheckADUserNameExist()
        {
            string loginNM = txtLogin.Text;
            string finalNM = string.Empty;
            if (loginNM != string.Empty)
            {
                String[] breakApart = loginNM.Split('\\');
                if (breakApart.Length == 1)
                {
                    loginNM = Utility.getOriginalEncryData(loginNM, hdfStaticGuid.Value);
                    String[] breakApart1 = loginNM.Split('\\');
                    finalNM = breakApart1[0] + "\\" + breakApart1[1];

                }
                else
                    finalNM = breakApart[0] + "\\" + breakApart[1];
            }

            return Facade.IsExistUserByLoginName(finalNM);
            //return Facade.IsExistUserByLoginName(Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value));
        }

        private bool CheckUserNameExist1()
        {

            // return Facade.IsExistUserByLoginName(txtLogin.Text);
            return Facade.IsExistUserByLoginName(Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value));

        }
        private bool CheckValidation1()
        {
            var isValid = true;

            if (btnSave.Text.Contains("Update"))
            {
                isValid = true;
            }
            else
            {
                lblUserName.Text = CheckUserNameExist1() ? "Login Name is Not Available" : "";

                if (lblUserName.Text.Contains("Login Name is Not Available"))
                {
                    isValid = false;
                    lblUserName.Text = "Login Name is Not Available";
                    lblUserName.Visible = true;
                }
            }

            return isValid;
        }

        private bool CheckValidation()
        {
            var isValid = true;
            captcha1.ValidateCaptcha(txtcaptcha.Text.Trim());

            if (txtLogin.Text == string.Empty || txtUserName.Text == string.Empty || ddlCompany.SelectedValue == "0" ||
                 ddlRole.SelectedValue == "0" || txtAddress.Text == string.Empty || txtStateCode.Text == string.Empty ||
                 txtPhone.Text == string.Empty || txtMobile.Text == string.Empty || lblUserName.Text != string.Empty)
            {
                if (btnSave.Text.Contains("Update"))
                {
                    isValid = false;

                }
                else if (txtPwd.Text == string.Empty)
                {
                    isValid = false;
                }
            }

            if (!captcha1.UserValidated)
            {
                lblerror.Visible = true;
                lblerror.Text = "You have Entered Invalid Text";
                isValid = false;
                _logger.Info("You have Entered Invalid Captcha Text ");

            }

            else
            {
                lblerror.Visible = true;
                lblerror.Text = "";
                isValid = true;
            }

            return isValid;
        }

        private void CompareToCollection(IEnumerable<ListItem> previousItems, IList<ListItem> currentSelectedItems, int isApplication)
        {
            var deleteListInfra = new List<ListItem>();
            var addListGroups = currentSelectedItems;
            foreach (var prItem in previousItems)
            {
                var itemFind = false;

                foreach (var crItem in currentSelectedItems)
                {
                    if (prItem.Text != crItem.Text) continue;
                    if (addListGroups.Contains(crItem))
                    {
                        addListGroups.Remove(crItem);
                    }
                    itemFind = true;
                    break;
                }
                if (!itemFind)
                {
                    deleteListInfra.Add(prItem);
                }
            }

            try
            {
                if (deleteListInfra.Count > 0)
                {
                    foreach (var userInfra in deleteListInfra.Select(deleteItem => new UserInfraObject
                    {
                        UserId = Convert.ToInt32(CurrentEntity.Id),
                        InfraObjectId = Convert.ToInt32(deleteItem.Value),
                    }))
                    {
                        if (userInfra.InfraObjectId != 0 && userInfra.UserId != 0)
                            Facade.DeleteUserInfraObjectByUserId(userInfra);
                    }
                }

                if (addListGroups.Count > 0)
                {

                    foreach (var userInfraObject in addListGroups.Select(addItem => new UserInfraObject
                    {
                        UserId = Convert.ToInt32(CurrentEntity.Id),
                        InfraObjectId = Convert.ToInt32(addItem.Value),
                        IsApplication = Convert.ToInt32(isApplication),
                        CreatorId = Convert.ToInt32(LoggedInUserId)
                    }))
                    {
                        if (userInfraObject.InfraObjectId != 0 && userInfraObject.UserId != 0)
                            Facade.AddUseInfraObject(userInfraObject);
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occurred while comparing user group list", ex);
                ExceptionManager.Manage(cpException);
            }
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.User.UserList + "?Listitems=" + "Cancel");
        }

        protected void TxtLoginTextChanged(object sender, EventArgs e)
        {
            // btnSave.Enabled = true;
            //if (txtLogin.Text == string.Empty)
            //{
            //    btnSave.Enabled = true;
            //    btnSave.CssClass = "btn btn-primary";
            //}
            //else
            //{

            //lblUserName.Text = CheckUserNameExist() ? "Login Name is Not Available" : "";

            //if (RadioButtonList1.SelectedItem.Text == "AD")
            //{
            //    string _loginName = txtLogin.Text;
            //    string[] parts = Regex.Split(_loginName, @"\\");
            //    bool isExpired = IsUserExpired(parts[0], parts[1]);

            //    lblUserName.Text = isExpired ? "Selected AD user is Expired" : string.Empty;
            //}
            //if (lblUserName.Text != string.Empty)
            //{
            //    btnSave.Enabled = false;
            //}
            //else
            //{
            //    btnSave.Enabled = true;
            //    btnSave.CssClass = "btn btn-primary";
            //}

            btnSave.Enabled = false;


            if (CheckUserNameExist() && txtLogin.Text != "")
            {
                lblUserName.Text = "Login Name is Not Available";
                lblUserName.Visible = true;
            }
            else
            {
                lblUserName.Text = "";
                lblUserName.Visible = false;
            }

            string checkvalidationAD = "^[a-zA-Z0-9\\\\,.'@_-]+$";
            string checkvalidationFA = "^[a-zA-Z0-9 ,.'@_-]+$";
            txtLogin.Enabled = true;
            string strLoginName = Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value);
            if (strLoginName.Length > 30)
            {
                lblUserName.Text = "Allowed Max Length 30";
                lblUserName.Enabled = true;
                lblUserName.Visible = true;
                return;
            }
            if (ddlAuthenticatinType.SelectedIndex == 1)// ****************************************
            {
                if (Regex.IsMatch(strLoginName, checkvalidationAD))
                {
                    string loginNm = txtLogin.Text;
                    // txtLogin.Attributes.Add("value", loginNm);
                    // txtLogin.Enabled = false;
                    txtLogin.Enabled = true;
                    txtLogin.CssClass = "form-control";
                }
                else if (txtLogin.Text == "")
                {
                    //txtLogin.Text = "";
                    //txtLogin.Enabled = false;
                    txtLogin.CssClass = "form-control";
                }
                else
                {
                    lblUserName.Text = "Invalid Login Name";
                    lblUserName.Visible = true;

                }
            }
            else if (ddlAuthenticatinType.SelectedIndex == 2)// ****************************************
            {
                if (Regex.IsMatch(txtLogin.Text, checkvalidationFA))
                {
                    string loginNm = txtLogin.Text;
                    //txtLogin.Attributes.Add("value", loginNm);
                    txtLogin.Enabled = true;
                }
                else if (txtLogin.Text == "")
                {
                    txtLogin.Text = "";
                }
                else
                {
                    //lblUserName.Text = "Invalid Login Name";
                }
            }

            if (lblUserName.Text != "")
            {
                lblUserName.Visible = true;
                btnSave.Enabled = false;
            }
            else
            {
                _logger.Info("Selected Login Name  is " + txtLogin.Text);
                lblUserName.Visible = false;
                btnSave.Enabled = true;
            }



        }

        protected void ValidationCheck(object sender, EventArgs e)
        {
            try
            {
                //Modified By Priyanka
                IList<Settings> settingInfo = Facade.GetAllSetting();
                string password = Utility.getOriginalEncryData(txtPwd.Text, hdfStaticGuid.Value);
                string strLoginName = Utility.getOriginalEncryData(txtLogin.Text, hdfStaticGuid.Value);

                if (!string.IsNullOrEmpty(strLoginName))
                {
                    if (password.Contains(strLoginName))
                    {
                        lblMatchwithUname.Visible = true;
                        lblErr.Visible = false;
                        lblMatchwithUname.Text = "Password can not contain username";
                        //txtPwd.Focus();
                        return;
                    }
                }

                if (settingInfo == null)
                {
                    string checkvalidation = "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\da-zA-Z]).{8,16}$";
                    if (Regex.IsMatch(password, checkvalidation))
                    {
                        lblErr.Visible = false;
                        lblMatchwithUname.Visible = false;
                        string pwdValue1 = txtPwd.Text;
                        txtPwd.Attributes.Add("value", pwdValue1);
                        //txtPwd.Focus();
                    }
                    else
                    {
                        lblErr.Visible = true;
                        lblMatchwithUname.Visible = false;
                        txtPwd.Text = "";
                        txtPwd.Attributes.Add("value", string.Empty);
                        lblErr.Text = "Invalid Password";
                        //txtConPwd.Focus();
                    }
                }
                else
                {
                    var pwdKeyRecord = (from setting in settingInfo
                                        where setting.Key == "PwdKey"
                                        select setting).FirstOrDefault();
                    if (pwdKeyRecord != null)
                    {
                        var getValue = CryptographyHelper.Md5Decrypt(pwdKeyRecord.Value);
                        if (Regex.IsMatch(password, getValue))
                        {
                            lblErr.Visible = false;
                            lblMatchwithUname.Visible = false;
                            string pwdValue = txtPwd.Text;
                            txtPwd.Attributes.Add("value", pwdValue);
                            //txtConPwd.Focus();
                            //txtPwd.Focus();
                        }
                        else
                        {
                            lblErr.Visible = true;
                            lblErr.Text = "Invalid password";
                            //txtPwd.Focus();
                            //txtConPwd.Focus();
                        }
                    }
                    else
                    {
                        string checkvalidation = "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\da-zA-Z]).{8,16}$";
                        if (Regex.IsMatch(password, checkvalidation))
                        {
                            lblErr.Visible = false;
                            lblMatchwithUname.Visible = false;
                            string pwdValue1 = txtPwd.Text;
                            txtPwd.Attributes.Add("value", pwdValue1);
                            //txtConPwd.Focus();
                            //txtPwd.Focus();
                        }
                        else
                        {
                            lblErr.Visible = true;
                            lblMatchwithUname.Visible = false;
                            txtPwd.Text = "";
                            txtPwd.Attributes.Add("value", string.Empty);
                            lblErr.Text = "Invalid Password";
                            //txtConPwd.Focus();
                        }

                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        protected void BtnSaveClick(object sender, EventArgs e)
        {

            var NAME = "";

            NAME = txtLogin.Text;
            var jai = txtDomainUserName.Text;
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("UserConfiguration", UserActionType.CreateUserAccount))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                }
                else
                {
                    string confPass = Utility.getOriginalEncryData(txtConPwd.Text, hdfStaticGuid.Value);
                    string pass = Utility.getOriginalEncryData(txtPwd.Text, hdfStaticGuid.Value);
                    if (!pass.Equals(confPass))
                    {
                        lblerrorMessage.Visible = true;
                        return;
                    }
                    else
                        lblerrorMessage.Visible = false;

                    if (!CheckValidation()) return;
                    if (!CheckValidation1()) return;


                    if (ddlCompany.SelectedIndex == 0)
                    {
                        lblcompany.Text = "Select Company Name";
                        return;
                    }
                    if (ddlAuthenticatinType.SelectedItem.Text == "Active Directory")
                    {
                        if (btnSave.Text == "Save")
                        {
                            lblUserName.Text = CheckADUserNameExist() ? "Login Name is Not Available" : "";
                            if (lblUserName.Text != "")
                            {
                                lblUserName.Visible = true;
                                return;
                            }
                        }
                    }
                    var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }
                    var submitButton = (Button)sender;
                    var buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }


                    try
                    {
                        if (currentTransactionType != TransactionType.Undefined)
                        {
                            BuildEntities();


                            try
                            {
                                if (Session["InfraObject"].ToString() == "F")
                                {
                                    lblMessage.Text = "Please Select the InfraObject";
                                }
                            }
                            catch (Exception ex)
                            {
                                lblMessage.Text = "Please Select the InfraObject";
                            }

                            if (Session["InfraObject"].ToString() != "F")
                            {
                                StartTransaction();
                                SaveEditor();
                                EndTransaction();
                                string message = MessageInitials + " " + '"' + CurrentUser.UserInformation.UserName + '"';
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));
                            }
                            else
                            {
                                labelSDErrormessage.Visible = true;
                                labelSDErrormessage.Text = "Please Select at least one InfraObject";
                                returnUrl = string.Empty;
                            }
                            // }
                        }
                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, this);
                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, this);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, this);
                        }
                    }

                    //if (returnUrl.IsNotNullOrEmpty())
                    //{
                    //    Helper.Url.Redirect(new SecureUrl(returnUrl));
                    //}
                    if (returnUrl.IsNotNullOrEmpty())
                    {

                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.UserId, CurrentEntity.Id);

                        Helper.Url.Redirect(returnUrl);
                    }
                }
            }
        }

        protected void lnkbtnCaptcha_Click(object sender, EventArgs e)
        {


            rfvCaptcha.Visible = false;

            captcha1.ValidateCaptcha(txtcaptcha.Text.Trim());
        }

        protected void DdlCompanySelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlCompany.SelectedIndex > 0)
            {
                UpdatePanel2.Update();
                lblcompany.Text = "";
                ddlRole.Enabled = true;
                IList<InfraObject> infraObjectList = new List<InfraObject>();
                IList<InfraObject> infrafilterdetails = new List<InfraObject>();

                var companydetails = Facade.GetCompanyProfileById(Convert.ToInt32(ddlCompany.SelectedValue));
                if (companydetails != null)
                {
                    infraObjectList = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

                    if (LoggedInUser.Role == UserRole.SuperAdmin && companydetails.IsParent == false)
                    {
                        foreach (InfraObject infradetails in infraObjectList)
                        {
                            var businessdetails = Facade.GetBusinessServiceById(infradetails.BusinessServiceId);

                            if (businessdetails.CompanyId == Convert.ToInt32(ddlCompany.SelectedValue))
                            {
                                infrafilterdetails.Add(infradetails);
                            }
                        }
                        infraObjectList = infrafilterdetails;
                    }

                    cblstGroup.ClearItems();

                    if (infraObjectList != null)
                    {
                        cblstGroup.DataSource = infraObjectList;
                        cblstGroup.DataTextField = "Name";
                        cblstGroup.DataValueField = "Id";
                        cblstGroup.DataBind();
                    }



                    if (companydetails.IsParent)
                    {
                        if (infraObjectList != null)
                        {
                            cblstGroup.Items.Insert(0, new ListItem("ALL", "0"));
                        }



                        ddlRole.ClearItems();

                        if (LoggedInUserRole.ToString() == "SuperAdmin")
                        {
                            ddlRole.AddItem("Select Role", "0000");
                            ddlRole.AddItem("SuperAdmin", "SuperAdmin");
                            ddlRole.AddItem("Administrator", "Administrator");
                            ddlRole.AddItem("Operator", "Operator");
                            ddlRole.AddItem("Manager", "Manager");
                            ddlRole.AddItem("Custom", "Custom");
                        }
                        else
                        {
                            ddlRole.AddItem("Select Role", "0000");
                            ddlRole.AddItem("Operator", "Operator");
                            ddlRole.AddItem("Manager", "Manager");
                            //ddlRole.AddItem("Custom", "Custom");ddlCompany
                        }
                    }
                    else
                    {
                        if (infraObjectList != null)
                        {
                            cblstGroup.Items.Insert(0, new ListItem("ALL", "0"));
                        }

                        ddlRole.ClearItems();
                        ddlRole.AddItem("Select Role", "0000");
                        ddlRole.AddItem("Administrator", "Administrator");
                        ddlRole.AddItem("Operator", "Operator");
                        ddlRole.AddItem("Manager", "Manager");
                        //ddlRole.AddItem("Custom", "Custom");
                    }
                }

            }
            else
            {
                cblstGroup.ClearItems();
            }
            //Added by hanumant 05.07.2018
            if (CurrentUserId != 0)
            {
                int i = 0;
                foreach (ListItem listItem in cblstGroup.Items)
                {
                    if (Convert.ToInt32(listItem.Value) != 0)
                    {
                        var infra = Facade.GetInfraObjectById(Convert.ToInt32(listItem.Value));
                        if (infra.CreatorId == CurrentUserId)
                        {
                            listItem.Selected = true;
                            listItem.Enabled = false;
                            cblstGroup.Items[i].Enabled = false;
                        }
                    }
                    i++;
                }
            }

        }

        //protected void CblstGroupSelectedIndexChanged(object sender, EventArgs e)
        //{
        //    // Updatepanel4.Update();
        //    btnSave.Enabled = true;
        //    labelSDErrormessage.Visible = false;


        //    string result = Request.Form["__EVENTTARGET"];
        //    string[] checkedBox = result.Split('$');
        //    int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);

        //    if (checkedIndex == 0)
        //    {
        //        if (cblstGroup.Items[0].Selected)
        //        {
        //            for (int i = 1; i < cblstGroup.Items.Count; i++)
        //            {
        //                cblstGroup.Items[i].Selected = true;
        //                cblstGroup.Items[i].Enabled = true;
        //            }
        //        }
        //        else
        //        {
        //            for (int i = 1; i < cblstGroup.Items.Count; i++)
        //            {
        //                cblstGroup.Items[i].Selected = false;
        //                cblstGroup.Items[i].Enabled = true;
        //            }
        //        }
        //    }
        //    else
        //    {
        //        CheckBoxListAllSelected(cblstGroup);
        //    }

        //}

        protected void CblstGroupSelectedIndexChanged(object sender, EventArgs e)
        {
            string result = Request.Form["__EVENTTARGET"];
            string[] checkedBox = result.Split('$');
            int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);
            ListItem testChkBx = cblstGroup.Items[checkedIndex];

            //if (cblstGroup.SelectedItem != null)
            //{
            if (checkedIndex > 0)  //&& testChkBx.Selected
            {
                if (!testChkBx.Selected && CurrentEntity.Id > 0)
                {
                    int userid = CurrentEntity.Id;  // CurrentEntity.UserInformation.UserId;

                    //var workflowinfo = Facade.GetUserInfraobjectName(Convert.ToInt32(testChkBx.Value), userid);

                    var workflowinfo = Facade.GetParallelProfileByInfraUserId(Convert.ToInt32(testChkBx.Value), userid);

                    string profileNamesList = string.Join(", ", workflowinfo.Select(p => p.ProfileName));

                    if (workflowinfo != null)
                        if (workflowinfo.Count > 0)
                        {
                            testChkBx.Selected = true;
                            lblInfraUserErrMsg.Visible = true;
                            lblInfraUserErrMsg.Text = "User created  " + profileNamesList + "  profile for this Infraobject";
                        }
                        else
                        {
                            btnSave.Enabled = true;
                            labelSDErrormessage.Visible = false;
                            lblInfraUserErrMsg.Visible = false;

                            //string result = Request.Form["__EVENTTARGET"];
                            //string[] checkedBox = result.Split('$');
                            //int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);

                            if (checkedIndex == 0)
                            {
                                if (cblstGroup.Items[0].Selected)
                                {
                                    for (int i = 1; i < cblstGroup.Items.Count; i++)
                                    {
                                        cblstGroup.Items[i].Selected = true;
                                        cblstGroup.Items[i].Enabled = true;
                                    }
                                }
                                else
                                {
                                    for (int i = 1; i < cblstGroup.Items.Count; i++)
                                    {
                                        cblstGroup.Items[i].Selected = false;
                                        cblstGroup.Items[i].Enabled = true;
                                    }
                                }
                            }
                            else
                            {
                                CheckBoxListAllSelected(cblstGroup);
                            }
                        }
                }
                else
                {
                    btnSave.Enabled = true;
                    labelSDErrormessage.Visible = false;
                    lblInfraUserErrMsg.Visible = false;

                    //string result = Request.Form["__EVENTTARGET"];
                    //string[] checkedBox = result.Split('$');
                    //int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);

                    if (checkedIndex == 0)
                    {
                        if (cblstGroup.Items[0].Selected)
                        {
                            for (int i = 1; i < cblstGroup.Items.Count; i++)
                            {
                                cblstGroup.Items[i].Selected = true;
                                cblstGroup.Items[i].Enabled = true;
                            }
                        }
                        else
                        {
                            for (int i = 1; i < cblstGroup.Items.Count; i++)
                            {
                                cblstGroup.Items[i].Selected = false;
                                cblstGroup.Items[i].Enabled = true;
                            }
                        }
                    }
                    else
                    {
                        CheckBoxListAllSelected(cblstGroup);


                    }
                }
            }
            else
            {
                btnSave.Enabled = true;
                labelSDErrormessage.Visible = false;
                lblInfraUserErrMsg.Visible = false;

                //string result = Request.Form["__EVENTTARGET"];
                //string[] checkedBox = result.Split('$');
                //int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);

                if (checkedIndex == 0)
                {
                    if (cblstGroup.Items[0].Selected)
                    {
                        for (int i = 1; i < cblstGroup.Items.Count; i++)
                        {
                            cblstGroup.Items[i].Selected = true;
                            cblstGroup.Items[i].Enabled = true;
                        }
                    }
                    else
                    {
                        if (CurrentEntity.Id > 0)
                        {
                            for (int i = 1; i < cblstGroup.Items.Count; i++)
                            {
                                int userid = CurrentEntity.Id;    //CurrentEntity.UserInformation.UserId;

                                //var workflowinfo = Facade.GetUserInfraobjectName(Convert.ToInt32(cblstGroup.Items[i].Value), userid);

                                var workflowinfo = Facade.GetParallelProfileByInfraUserId(Convert.ToInt32(cblstGroup.Items[i].Value), userid);

                                string profileNamesList = string.Join(", ", workflowinfo.Select(p => p.ProfileName));

                                if (workflowinfo != null)
                                {
                                    if (workflowinfo.Count > 0)
                                    {
                                        cblstGroup.Items[i].Selected = true;
                                        cblstGroup.Items[i].Enabled = true;

                                        lblInfraUserErrMsg.Visible = true;
                                        lblInfraUserErrMsg.Text = "This User created " + profileNamesList + " profiles for " + cblstGroup.Items[i].Text + " Infraobject";
                                    }
                                    else
                                    {
                                        btnSave.Enabled = true;
                                        labelSDErrormessage.Visible = false;
                                        //lblInfraUserErrMsg.Visible = false;
                                        cblstGroup.Items[i].Selected = false;
                                        cblstGroup.Items[i].Enabled = true;
                                    }
                                }
                                else
                                {
                                    btnSave.Enabled = true;
                                    labelSDErrormessage.Visible = false;
                                    //lblInfraUserErrMsg.Visible = false;
                                    cblstGroup.Items[i].Selected = false;
                                    cblstGroup.Items[i].Enabled = true;
                                }

                                //if (workflowinfo == null)
                                //{
                                //    btnSave.Enabled = true;
                                //    labelSDErrormessage.Visible = false;
                                //    //lblInfraUserErrMsg.Visible = false;


                                //    cblstGroup.Items[i].Selected = false;
                                //    cblstGroup.Items[i].Enabled = true;
                                //}
                                ////if (workflowinfo.Count > 0)
                                //else if (workflowinfo.Count > 0)
                                //{
                                //    cblstGroup.Items[i].Selected = true;
                                //    cblstGroup.Items[i].Enabled = true;

                                //    lblInfraUserErrMsg.Visible = true;
                                //    lblInfraUserErrMsg.Text = "This User created profile for " + cblstGroup.Items[i].Text + " Infraobject";
                                //}
                            }
                        }
                        else
                        {
                            for (int i = 1; i < cblstGroup.Items.Count; i++)
                            {
                                cblstGroup.Items[i].Selected = false;
                                cblstGroup.Items[i].Enabled = true;
                            }
                        }
                    }
                }
                else
                {
                    CheckBoxListAllSelected(cblstGroup);
                }
            }
            //  teststr = "True";
            //}

            //Added by hanumant 05.07.2018
            if (CurrentUserId != 0)
            {
                int i = 0;
                foreach (ListItem listItem in cblstGroup.Items)
                {
                    if (Convert.ToInt32(listItem.Value) != 0)
                    {
                        var infra = Facade.GetInfraObjectById(Convert.ToInt32(listItem.Value));
                        if (infra.CreatorId == CurrentUserId)
                        {
                            listItem.Selected = true;
                            listItem.Enabled = false;
                            cblstGroup.Items[i].Enabled = false;
                        }
                    }
                    i++;
                }
            }

        }

        private void CheckBoxListAllSelected(CheckBoxList grpOrAppList)
        {
            int selectedKwt = 0;

            for (int i = 1; i < grpOrAppList.Items.Count; i++)
            {
                if (grpOrAppList.Items[i].Selected)
                {
                    selectedKwt++;
                }
                grpOrAppList.Items[i].Enabled = true;
            }

            if (selectedKwt == grpOrAppList.Items.Count - 1)
            {
                grpOrAppList.Items[0].Selected = true;
                if (ddlRole.SelectedValue == "SuperAdmin")
                {
                    Panel1.Enabled = false;
                }
                else
                {
                    Panel1.Enabled = true;
                }
            }
            else
            {
                grpOrAppList.Items[0].Selected = false;
                //NO need to add this code 

            }
        }

        protected void ChkEmailChanged(object sender, EventArgs e)
        {
            divEmail.Visible = ChkEmail.Checked;
        }

        protected void ChkMobileChanged(object sender, EventArgs e)
        {
            divMobile.Visible = ChkMobile.Checked;
        }

        protected void groupname(object sender, EventArgs e)
        {
            //divDomainUser.Visible = false;
            //divgroupname.Visible = true;
        }


        protected void DdlRoleSelectedIndexChanged(object sender, EventArgs e)
        {
            UpdatePanel2.Update();
            if (ddlRole.SelectedValue == "SuperAdmin")
            {
                DivCustomSubRoleType.Visible = false;
                for (int i = 0; i < cblstGroup.Items.Count; i++)
                {
                    cblstGroup.Items[i].Selected = true;
                }
                Panel1.Enabled = false;
                //   Updatepanel4.Update();
            }
            else if (ddlRole.SelectedValue == "Custom")
            {
                DivCustomSubRoleType.Visible = true;
                for (int i = 0; i < cblstGroup.Items.Count; i++)
                {
                    cblstGroup.Items[i].Enabled = true;
                    cblstGroup.Items[i].Selected = false;
                }
                Panel1.Enabled = true;
            }
            else
            {
                DivCustomSubRoleType.Visible = false;
                for (int i = 0; i < cblstGroup.Items.Count; i++)
                {
                    cblstGroup.Items[i].Enabled = true;
                    cblstGroup.Items[i].Selected = false;
                }
                Panel1.Enabled = true;


            }
            //Added by hanumant 05.07.2018
            if (CurrentUserId != 0)
            {
                int i = 0;
                foreach (ListItem listItem in cblstGroup.Items)
                {
                    if (Convert.ToInt32(listItem.Value) != 0)
                    {
                        var infra = Facade.GetInfraObjectById(Convert.ToInt32(listItem.Value));
                        if (infra.CreatorId == CurrentUserId)
                        {
                            listItem.Selected = true;
                            listItem.Enabled = false;
                            cblstGroup.Items[i].Enabled = false;
                        }
                    }
                    i++;
                }
            }
        }

        protected void ddlAuthenticatinType_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            //UpdatePanel3.Update();
            txtLogin.Text = string.Empty;
            txtLogin.Enabled = true;
            divUserLogin.Visible = false;
            if (ddlAuthenticatinType.SelectedIndex == 2)
            {
                //   ckbEmail.Visible = true;
                ucpwd.Visible = false;
                divAD.Visible = true;
                divUserLogin.Visible = true;
                //  Updatepanel4.Update();
                //txtLogin.Enabled = false;
                txtLogin.Attributes.Add("ReadOnly", "true");
            }

            if (ddlAuthenticatinType.SelectedIndex == 1)
            {
                // upEmail.Update();
                // ckbEmail.Visible = false;

                divAD.Visible = false;
                ucpwd.Visible = true;
                divUserLogin.Visible = false;
                txtDomainUserName.Text = "";

                txtUserName.Text = string.Empty;
                lblUserName.Visible = false;
                txtDomainUserName.Text = string.Empty;
                txtLogin.Text = string.Empty;
                txtLogin.Attributes.Remove("ReadOnly");
                TxtLoginTextChanged(null, null);
                //txtLogin.Enabled = true;
            }

        }

        protected void userlogintype_selectedindexchanged(object sender, EventArgs e)
        {
            string message = "Value: " + rdoLogin.SelectedItem.Value;
            if (rdoLogin.SelectedItem.Value == "Group")
            {
                txtDomainUserName.Text = string.Empty;
                txtLogin.Text = string.Empty;
                txtUserName.Text = string.Empty;
                lblDomainUserName.Text = "Enter Group Name";
                //  lblDomainUserName.Attributes.Add("text", "Enter Group Name");
                txtDomainUser.Attributes.Add("placeholder", "Enter Group Name");
                txtDomainUserNew.Attributes.Add("placeholder", "Enter Group Name");

            }
            if (rdoLogin.SelectedItem.Value == "Individual")
            {
                txtDomainUserName.Text = string.Empty;
                txtLogin.Text = string.Empty;
                txtUserName.Text = string.Empty;
                lblDomainUserName.Text = "Enter User Name";
                txtDomainUser.Attributes.Add("placeholder", "Enter User Name");
                txtDomainUserNew.Attributes.Add("placeholder", "Enter User Name");

            }

        }
        [WebMethod]

        public static ArrayList GetGroupUsers(string values)
        {
            //Forest currentForest = Forest.GetCurrentForest();
            //DomainCollection domains = currentForest.Domains;
            //GroupPrincipal
            // create your domain context

            //String currentDomain = Domain.GetCurrentDomain().ToString();

            //string DomainContainer = "DC=ptsch,DC=COM";

            //PrincipalContext ctx = new PrincipalContext(ContextType.Domain, currentDomain, DomainContainer, ContextOptions.Negotiate);                 

            // GroupPrincipal grp = new GroupPrincipal(ctx,);



            // GroupPrincipal grp1 = GroupPrincipal.FindByIdentity(ctx, IdentityType.Name, values);


            // define a "query-by-example" principal - here, we search for a GroupPrincipal 
            // GroupPrincipal qbeGroup = new GroupPrincipal(ctx);

            // create your principal searcher passing in the QBE principal    
            //PrincipalSearcher srch = new PrincipalSearcher(qbeGroup);

            // find all matches
            //foreach (var found in srch.FindAll())
            //{
            //    // do whatever here - "found" is of type "Principal" - it could be user, group, computer.....          
            //}

            ArrayList groups = new ArrayList();
            if (!string.IsNullOrEmpty(values))
            {
                foreach (IdentityReference group1 in System.Web.HttpContext.Current.Request.LogonUserIdentity.Groups)
                {
                    //if (group1.Translate(typeof(NTAccount)).ToString().ToLower().Contains(values))
                    try
                    {
                        if (group1.Translate(typeof(NTAccount)).ToString().ToLower().Contains(values))
                            groups.Add(group1.Translate(typeof(NTAccount)).ToString().ToLower().Split('\\')[1]);
                    }
                    catch (Exception exc)
                    {


                    }

                }
            }
            if (groups.Count <= 0)
            {
                _logger.Error(" Invalid  Group Name " + values);
            }


            return groups;
        }
        [WebMethod]

        public static string AdIndividualUsers(string values)
        {
            string name = "";
            using (var context = new PrincipalContext(ContextType.Domain))
            {
                var usr = UserPrincipal.FindByIdentity(context, values);
                if (usr != null)
                    name = usr.DisplayName;
            }
            return name;
        }
        [WebMethod]

        public static string GetDomainUsers(string values)
        {
            var _domainName = values.Split(',');

            var returnValue = "";
            // ArrayList _domainsusers = new ArrayList();
            StringBuilder _domainsusers = new StringBuilder();
            try
            {
                List<string> allUsers = new List<string>();

                PrincipalContext domainContext = new PrincipalContext(ContextType.Domain, _domainName[1]);

                try
                {
                    if (domainContext != null)
                    {

                    }
                }
                catch (Exception ex)
                {
                    throw (ex);
                }
                //Create a "user object" in the context
                UserPrincipal user = new UserPrincipal(domainContext);

                user = null;
                user = new UserPrincipal(domainContext);

                user.Surname = _domainName[0] + "*";


                PrincipalSearcher srch1 = new PrincipalSearcher(user);
                foreach (var found in srch1.FindAll())
                {
                    DirectoryEntry de = found.GetUnderlyingObject() as DirectoryEntry;
                    _domainsusers.Append(de.Properties["samAccountName"].Value);
                    _domainsusers.Append(",");
                    //allUsers.Add(found.DisplayName + "(" + found.SamAccountName + ")");
                }

                user = null;
                user = new UserPrincipal(domainContext);
                user.SamAccountName = _domainName[0] + "*";

                PrincipalSearcher srch2 = new PrincipalSearcher(user);

                foreach (var found in srch2.FindAll())
                {
                    DirectoryEntry de = found.GetUnderlyingObject() as DirectoryEntry;
                    _domainsusers.Append(de.Properties["samAccountName"].Value);
                    _domainsusers.Append(",");
                    //allUsers.Add(found.DisplayName + "(" + found.SamAccountName + ")");
                }


                returnValue = string.Format("{0}{1}", returnValue, _domainsusers.ToString());
                //returnValue.Remove(returnValue.Length - 1, 1);\
                returnValue = returnValue.Substring(0, returnValue.Length - 1);
                _logger.Info(" Discovered AD users are " + _domainsusers);
            }
            catch (Exception ex)
            {
                //_logger.Info("AD users does not exist " + _domainsusers);
                _logger.Error(" Invalid  user Name " + _domainName[0]);
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Domain Name does not exist", ex);
                ExceptionManager.Manage(cpException);
            }
            return returnValue;
        }
        //public static string GetDomainUsers(string values)
        //{
        //    var _domainName = values.Split(',');

        //    var returnValue = "";
        //    // ArrayList _domainsusers = new ArrayList();
        //    StringBuilder _domainsusers = new StringBuilder();
        //    try
        //    {
        //        List<string> allUsers = new List<string>();

        //        PrincipalContext domainContext = new PrincipalContext(ContextType.Domain, _domainName[1]);

        //        //Create a "user object" in the context
        //        UserPrincipal user = new UserPrincipal(domainContext);

        //        //Specify the search parameters
        //       // user.SamAccountName =_domainName[0] + "*";     

        //       // user.GivenName = _domainName[0] + "*";   

        //        //pass (our) user object
        //      //  PrincipalSearcher pS = new PrincipalSearcher(user);
        //        //foreach (var found in pS.FindAll())
        //        //{
        //        //    DirectoryEntry de = found.GetUnderlyingObject() as DirectoryEntry;
        //        //    _domainsusers.Append(de.Properties["samAccountName"].Value);
        //        //    _domainsusers.Append(",");
        //        //   // allUsers.Add(found.DisplayName + "(" + found.SamAccountName + ")");
        //        //}


        //        user = null;
        //        user = new UserPrincipal(domainContext);

        //        user.Surname = _domainName[0] + "*";


        //        PrincipalSearcher srch1 = new PrincipalSearcher(user);
        //        foreach (var found in srch1.FindAll())
        //        {
        //            DirectoryEntry de = found.GetUnderlyingObject() as DirectoryEntry;
        //             _domainsusers.Append(de.Properties["samAccountName"].Value);
        //            _domainsusers.Append(",");
        //            //allUsers.Add(found.DisplayName + "(" + found.SamAccountName + ")");
        //        }

        //        user = null;
        //        user = new UserPrincipal(domainContext);
        //        user.SamAccountName = _domainName[0] + "*";

        //        PrincipalSearcher srch2 = new PrincipalSearcher(user);

        //        foreach (var found in srch2.FindAll())
        //        {
        //            DirectoryEntry de = found.GetUnderlyingObject() as DirectoryEntry;
        //            _domainsusers.Append(de.Properties["samAccountName"].Value);
        //            _domainsusers.Append(",");
        //            //allUsers.Add(found.DisplayName + "(" + found.SamAccountName + ")");
        //        }


        //        returnValue = string.Format("{0}{1}", returnValue, _domainsusers.ToString());
        //        returnValue.Remove(returnValue.Length - 1, 1);


        //        //pS.QueryFilter = user;

        //        //using (var context = new PrincipalContext(ContextType.Domain, _domainName[1]))
        //        //{
        //        //    using (var searcher = new PrincipalSearcher(new UserPrincipal(context)))
        //        //    {                        
        //        //        foreach (var result in pS.FindAll())
        //        //        {
        //        //            DirectoryEntry de = result.GetUnderlyingObject() as DirectoryEntry;
        //        //            _domainsusers.Append(de.Properties["samAccountName"].Value);
        //        //            _domainsusers.Append(",");

        //        //        }
        //        //        returnValue = string.Format("{0}{1}", returnValue, _domainsusers.ToString());
        //        //        returnValue.Remove(returnValue.Length - 1, 1);

        //        //    }
        //        //}
        //        _logger.Info(" Discovered AD users are " + _domainsusers);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Info("AD users does not exist " + _domainsusers);
        //        var cpException = new CpException(CpExceptionType.CommonUnhandled, "Domain Name does not exist", ex);
        //        ExceptionManager.Manage(cpException);
        //    }
        //    return returnValue;
        //}

        [WebMethod]
        public static string DiscoverDomains()
        {
            StringBuilder _domain = new StringBuilder();
            var returnValue = "";
            try
            {
                List<Domain> _domains = new List<Domain>();
                Forest currentForest = Forest.GetCurrentForest();
                //Forest currentForest = null;
                if (currentForest != null && currentForest.Domains.Count > 0)
                {
                    DomainCollection domains = currentForest.Domains;
                    foreach (Domain objDomain in domains)
                    {
                        _domains.Add(objDomain);
                        _domain.Append(objDomain.Name);
                        _domain.Append(",");
                        //_domain.Append("ptech.com");
                        //_domain.Append(",");
                        // returnValue = string.Format("{0}{1}", returnValue, objDomain.Name);


                    }
                    returnValue = string.Format("{0}{1}", returnValue, _domain.ToString());
                    _logger.Info("discover domains " + returnValue);

                    _logger.Info("Trim comma at end, discover domains info " + returnValue);
                    returnValue.Remove(returnValue.Length - 1, 1);
                    var getLastIndex = returnValue.Length - 1;
                    returnValue.Remove(getLastIndex, 1);
                    return returnValue;
                }
                //_logger.Info("returnValue of discover domains " + returnValue);
                _logger.Info("returnValue of discover domains is empty");
                return returnValue;
            }
            catch (Exception ex)
            {
                //_logger.Info("discover domains return empty domains");
                _logger.Error("DiscoverDomains method exception : " + ex.Message);

                return returnValue;
            }
        }

        public UserPrincipal GetUser(string sUserName, string _domainName)
        {
            UserPrincipal oUserPrincipal = null;
            try
            {
                PrincipalContext oPrincipalContext = new PrincipalContext(ContextType.Domain, _domainName);
                oUserPrincipal = UserPrincipal.FindByIdentity(oPrincipalContext, sUserName);


            }
            catch (Exception ex)
            {
                _logger.Info("Get User failed to find AD users " + oUserPrincipal + " for user name " + sUserName + " with the domain name " + _domainName);
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Domain Name does not exist", ex);
                ExceptionManager.Manage(cpException);
            }
            return oUserPrincipal;
        }

        //protected void btnsearch_Click(object sender, ImageClickEventArgs e)
        //{
        //    txtDomainUserNew.Value = string.Empty;
        //    txtDomainUser.Value = string.Empty;
        //}

        protected void btnsearch_click(object sender, EventArgs e)
        {
            var domainname = txtDomainUserName.Text;
            if (string.IsNullOrEmpty(domainname))
            {
                if (rdoLogin.SelectedItem.Value == "Group")
                {
                    // rqdsearch.Attributes.Add("ErrorMessage", "Enter User Name");
                    // errordomainsearch.Visible = true;
                    //errordomainsearch.Text = "Enter Group Name";
                    // txtPwd.Attributes.Add("onblur", "ValidatorValidate(" + rfvPwd.ClientID + "),getHashData(" + txtPwd.ClientID + ")");
                }
                else
                {
                    //errordomainsearch.Visible = true;
                    // errordomainsearch.Text = "Enter User Name";

                }
            }
            else
            {
                //errordomainsearch.Text = "";
            }
        }

        /*  protected void BtnOk_Click(object sender, EventArgs e)
        {
            UserPrincipal oUserPrincipal = null;
            string domainUser = string.Empty;
            string domainName = string.Empty;
            domainUser = ((txtDomainUserNew.Value != string.Empty) ? txtDomainUserNew.Value : txtDomainUser.Value);
            domainName = ((combobox1.Value != string.Empty) ? combobox1.Value : combobox2.Value);
           
            if (domainName != string.Empty && domainUser != string.Empty)
                oUserPrincipal = GetUser(domainUser, domainName);
                                      

            string fullname;
            if (oUserPrincipal != null)
            {
                lblMessage.Text = string.Empty;
                if (oUserPrincipal.DisplayName!=null)
                {
                    fullname = oUserPrincipal.DisplayName;
                   // Session["FullName"] = fullname;
                }
                else if (oUserPrincipal.Name != null)
                {
                    fullname = oUserPrincipal.Name;
                   // Session["FullName"] = fullname;
                }

            }
            //else
            //{
            //    domainUser;
            //}
           
            string username = domainUser;
          
           // Session["DomainName"] = domainName + @"\" + username;

            txtLogin.Text = domainName + @"\" + username;

            txtUserName.Text = domainUser; 
        }
        */

        protected void OnCheckSendMail(object sender, EventArgs e)
        {
            if (ckbEmail.Checked == true)
            {

                //   upEmail.Update();
                UpdatePanel1.Update();
                UpdatePanel1.Update();
                var smtpInfo = Facade.GetSmtpConfigurations();
                if (smtpInfo == null)
                {
                    //  ckbEmail.Checked = true;
                    lblMsg.Visible = true;
                    lblMsg.Text = "SMTP not configured";
                    //tm = new System.Timers.Timer(5000);
                    //tm.Elapsed += tm_Tick;
                    //tm.Enabled = true;
                    Timer1.Enabled = true;

                    //Thread.Sleep(10000);
                    //ckbEmail.Checked = true;
                    // upEmail.Update();
                }
            }
            else
            {
                ckbEmail.Enabled = true;
                lblMsg.Visible = false;
                // upEmail.Update();
            }
        }

        protected void tm_Tick(object sender, EventArgs e)
        {
            ckbEmail.Checked = false;
            lblMsg.Text = string.Empty;
            Timer1.Enabled = false;
            UpdatePanel1.Update();
            UpdatePanel2.Update();
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId, IPAddress);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId, IPAddress);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request


        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                IgnoreIDs.Add("txtCountryCode");
                IgnoreIDs.Add("txtCountryCode2");

                if (ChkEmail.Checked)
                {
                    IgnoreIDs.Add("txtEmail");
                }

                if (ChkMobile.Checked)
                {
                    IgnoreIDs.Add("txtMCountryCode");
                }


                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }
    }

}
