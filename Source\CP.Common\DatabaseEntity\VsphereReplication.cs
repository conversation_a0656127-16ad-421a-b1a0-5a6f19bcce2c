﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = " VsphereReplication", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class VsphereReplication : BaseEntity
    {
        #region Member Variable

        private ReplicationBase _replicationBase = new ReplicationBase();

        #endregion Member Variable
        #region Properties


        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int SiteId { get; set; }
        [DataMember]
        public int PRServerId { get; set; }
        [DataMember]
        public int DRServerId { get; set; }

        [DataMember]
        public string PRIPADDRESS { get; set; }

        [DataMember]
        public string DRIPADDRESS { get; set; }

        [DataMember]
        public string PRServerName { get; set; }

        [DataMember]
        public string DRServerName { get; set; }


        [DataMember]
        public string RecoveryPlanName { get; set; }

        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }

        }
        #endregion Properties

    }
}
