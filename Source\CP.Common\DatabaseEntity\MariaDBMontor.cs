﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "MariaDBMontor", Namespace = "http://www.ContinuityPlatform.com/types")]
   public class MariaDBMontor :BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int DatabaseId { get; set; }

        [DataMember]
        public string DatabaseVersion { get; set; }

        [DataMember]
        public string MariaDBServiceStatus { get; set; }

        [DataMember]
        public string DatabaseList { get; set; }

        [DataMember]
        public string IncomingIPAddresses { get; set; }

        [DataMember]
        public string ClusterStateUUID { get; set; }

        [DataMember]
        public string ConfigurationID { get; set; }

        [DataMember]
        public string ConnectedNodes { get; set; }

        [DataMember]
        public string ClusterReadyState { get; set; }

        [DataMember]
        public string ConnectedToWsrepProvider { get; set; }

        [DataMember]
        public string ConnectedNodesStatus { get; set; }

        [DataMember]
        public string LastCommittedTransaction { get; set; }

        [DataMember]
        public string LocalStateComment { get; set; }

        [DataMember]
        public string DataRcvddFromOtherNode { get; set; }

        [DataMember]
        public string DataRcvdFromOtherNodeinBytes { get; set; }

        [DataMember]
        public string DataReplicatedToOtherNode { get; set; }

        [DataMember]
        public string DataReplicatedOtherinBytes { get; set; }

        [DataMember]
        public string ReceivedQueueLengthMax { get; set; }

        [DataMember]
        public string SendQueueLengthMax { get; set; }
        [DataMember]
        public string StartPositon { get; set; }

        [DataMember]
        public DateTime CreateDate { get; set; }

        [DataMember]
        public DateTime UpdateDate { get; set; }

        #endregion
    }
}
