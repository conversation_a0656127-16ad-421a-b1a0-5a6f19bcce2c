﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "HuaweiStorage", Namespace = "http://www.ContinuityPlatform.com/types")]


    public class HuaweiStorage : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int StorageServerId { get; set; }

        [DataMember]
        public string ConGroupId { get; set; }

        [DataMember]
        public string StorageReplicationMethod { get; set; }

        [DataMember]
        public string CREATEDATE { get; set; }


        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }



        #endregion Properties
    }
}


