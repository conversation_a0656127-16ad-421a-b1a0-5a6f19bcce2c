using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class AzureGatewayReplicationConfiguration : ReplicationControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        #region Variable

        TextBox _txtReplicationName = new TextBox();
        DropDownList _ddlReplicationType = new DropDownList();
        DropDownList _ddlSiteId = new DropDownList();
        private AzureGatewayReplication _azureGatewayReplication;

        #endregion

        #region Properties

        public AzureGatewayReplication CurrentEntity
		{
            get { return _azureGatewayReplication ?? (_azureGatewayReplication = new AzureGatewayReplication()); }
            set
            {
                _azureGatewayReplication = value;
            }
        }


        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "AzureGatewayReplication"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion

        public override void PrepareView()
        {
            PrepareEditView();
        }

        public void PrepareEditView()
        { 
            if (CurrentAzureGatewayReplication != null)
            {
                CurrentEntity = CurrentAzureGatewayReplication;

                Session["AzureGatewayReplication"] = CurrentEntity;

            txtSubscriptionName.Text = CurrentEntity.SubscriptionName;
			txtGatewayName.Text = CurrentEntity.GatewayName;
			txtResourceGroupName.Text = CurrentEntity.ResourceGroupName;
			txtLocation.Text = CurrentEntity.Location;
			txtPublicIPName.Text = CurrentEntity.PublicIPName;
			txtPublicIPRGName.Text = CurrentEntity.PublicIPRGName;
			txtPublicIPLocation.Text = CurrentEntity.PublicIPLocation;
			

                btnSave.Text = "Update";
            }
        }

        public void BuildEntities()
        {
            if (Session["AzureGatewayReplication"] != null)
            {
                CurrentEntity = (AzureGatewayReplication)Session["AzureGatewayReplication"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.SubscriptionName = txtSubscriptionName.Text;
			CurrentEntity.GatewayName = txtGatewayName.Text;
			CurrentEntity.ResourceGroupName = txtResourceGroupName.Text;
			CurrentEntity.Location = txtLocation.Text;
			CurrentEntity.PublicIPName = txtPublicIPName.Text;
			CurrentEntity.PublicIPRGName = txtPublicIPRGName.Text;
			CurrentEntity.PublicIPLocation = txtPublicIPLocation.Text;
			

        }

        public void SaveEditor()
        {
            // if (CurrentEntity.IsNew)
            if (btnSave.Text == "Save")
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;

                CurrentEntity = Facade.AddAzureGatewayReplication(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "AzureGatewayReplication", UserActionType.CreateReplicationComponent, "The AzureGateway Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);

            }
            else
            {
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateAzureGatewayReplication(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "AzureGatewayReplication", UserActionType.UpdateReplicationComponent, "The AzureGateway Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }
            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            if (Page.IsValid)
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }

                try
                {
                    if (currentTransactionType != TransactionType.Undefined)
                    {
                        StartTransaction();
                        BuildEntities();
                        SaveEditor();
                        var replicationName = ReplicationName.Text;
                        ReplicationName.Text = string.Empty;
                        EndTransaction();

                        string message = MessageInitials + " " + '"' + replicationName + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                currentTransactionType));
                        btnSave.Enabled = false;
                    }
                }
                catch (CpException ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                    {
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);
                    }
                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, Page);
                    }
                }
                if (returnUrl.IsNotNullOrEmpty())
                {
                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                    Helper.Url.Redirect(secureUrl);
                }
            }

        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }
    }
}
