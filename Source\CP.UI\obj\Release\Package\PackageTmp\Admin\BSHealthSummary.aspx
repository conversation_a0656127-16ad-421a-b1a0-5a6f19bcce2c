﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="BSHealthSummary.aspx.cs" Inherits="CP.UI.BSHealthSummary" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <script src="../Script/BSHealthSummary.js"></script>
    <script src="../Script/jquery.canvasjs.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
            BSHealthdash();
        }

    </script>
    <style type="text/css">
        .canvasjs-chart-credit {
            display: none;
        }

        .chosen-container {
            width: 147px !important;
        }

        #ctl00_cphBody_ddlbsservices_chosen {
            width: 218px !important;
        }
    </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <asp:UpdatePanel ID="upnlProfileOverview" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

                <!--breadcrumb -->
                <%-- <ul class="breadcrumb show">
                    <li>You are here</li>
                    <li><a href="#" class="glyphicons list"><i></i>Overview</a></li>
                    <li class="divider"></li>
                    <li>Profile List</li>
                </ul>--%>
                <!--end here-->
                <h3>
                    <img src="../Images/BSDashboard/Business Services Health Summary.png" />
                    Business Service Health Summary
                     <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false"></asp:Label>
                    <asp:Button ID="btnexport" runat="server" Text="Export" CssClass="btn btn-primary pull-right" Style="margin-top: 10px;" OnClick="btnexport_Click" />
                </h3>
                <!--List start here -->
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body" style="padding-top: 20px;">
                        <div class="col-md-4 border_right">

                            <div class="col-md-4 pd_ln col-md-push-4">
                                <div class="i_bx">
                                    <img src="../Images/BSDashboard/BS_health_total.png">
                                    <div>
                                        <h3>
                                            <asp:Label ID="ctl00_cphBody_lblTotalHealthSummary" runat="server"></asp:Label></h3>
                                        <span>Total </span>
                                    </div>
                                </div>
                            </div>
                            <div class="clearfix" style="margin-bottom: 20px;"></div>
                            <div class="col-md-4 pd_ln">
                                <div class="i_bx">
                                    <img src="../Images/BSDashboard/BS_health_pr.png">
                                    <div>
                                        <h3>
                                            <asp:Label ID="lblProductionCnt" runat="server"></asp:Label></h3>
                                        <span>Production </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 pd_ln">
                                <div class="i_bx">
                                    <img src="../Images/BSDashboard/BS_health_dr.png">
                                    <div>
                                        <h3>
                                            <asp:Label ID="lblDRCount" runat="server"></asp:Label></h3>
                                        <span>DR </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4" style="padding: 0px">
                                <div class="i_bx">
                                    <img src="../Images/BSDashboard/BS_underconfig.png">
                                    <div style="width: 76%;">
                                        <h3>
                                            <asp:Label ID="lblunderCount" runat="server"></asp:Label></h3>
                                        <span>Under Configuration </span>
                                    </div>
                                </div>
                            </div>

                            <div class="clearfix"></div>
                            <div id="bsHealthdash" style="width: 350px; height: 450px;">
                            </div>


                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-5 pd_rn">
                                    <div class="form-group" style="margin: 0;">
                                        <label style="margin-right: 10px">Business Services </label>
                                        <asp:DropDownList ID="ddlbsservices" runat="server" CssClass="chosen-select" OnSelectedIndexChanged="ddlbsservices_SelectedIndexChanged" AutoPostBack="true"></asp:DropDownList>
                                    </div>
                                </div>
                                <div class="col-md-3 pd_rn">
                                    <div class="form-group" style="margin: 0;">
                                        <label style="margin-right: 10px">Status </label>
                                        <asp:DropDownList ID="ddlServiceStatus" runat="server" CssClass="chosen-select" OnSelectedIndexChanged="ddlServiceStatus_SelectedIndexChanged" AutoPostBack="true">
                                            <asp:ListItem Selected="True" Text="- Select Status -" Value="0"></asp:ListItem>
                                            <asp:ListItem Text="Production" Value="1"></asp:ListItem>
                                            <asp:ListItem Text="DR" Value="2"></asp:ListItem>
                                            <asp:ListItem Text="Under Configured" Value="3"></asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                                <div class="col-md-4 pd_rn">
                                    <div class="form-group" style="margin: 0;">
                                        <label style="margin-right: 10px">Location </label>
                                        <asp:DropDownList ID="ddlLocation" runat="server" CssClass="chosen-select" OnSelectedIndexChanged="ddlLocation_SelectedIndexChanged" AutoPostBack="true"></asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <hr />

                            <asp:ListView ID="lvBSList" runat="server" OnItemDataBound="lvBSList_ItemDataBound">
                                <LayoutTemplate>
                                    <table class="d_table" style="table-layout: fixed; margin-bottom: 0;">
                                        <thead>
                                            <tr>
                                                <th style="width: 4%;" class="text-center">
                                                    <span>
                                                        <img src="../Images/profile-company-icon-white.png" /></span>
                                                </th>
                                                <th style="width: 56%;">Business Services
                                                </th>
                                                <th style="width: 20%;">Status
                                                </th>
                                                <th style="width: 20%">Location
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                    <div class="notifyscroll" style="height: 110px">
                                        <table class="d_table" style="table-layout: fixed; margin-bottom: 0;">
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                            </tbody>
                                        </table>
                                    </div>
                                </LayoutTemplate>
                                <EmptyDataTemplate>
                                    <div class="message warning align-center bold no-bottom-margin">
                                        <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                                    </div>
                                </EmptyDataTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td style="width: 4%;" class="text-center">
                                            <asp:Label ID="Id" runat="server" Text='<%# Eval("serviceId") %>' Visible="false" /><%--<%#Container.DataItemIndex+1 %>--%>
                                        </td>
                                        <td class="tdword-wrap" style="width: 56%;">
                                            <asp:Label ID="lblBusinessServiceName" runat="server" Text='<%# Eval("BusinessServiceName") %>' />
                                        </td>
                                        <td class="tdword-wrap" style="width: 20%;">
                                            <asp:Label ID="StateusImgIcon" runat="server"></asp:Label>
                                            <asp:Label ID="lblBSStatus" runat="server" Text='<%#Eval("Status") %>' />
                                        </td>
                                        <td class="tdword-wrap" style="width: 20%;">
                                            <asp:Label ID="Label1" runat="server" CssClass="bs_location"></asp:Label>
                                            <asp:Label ID="lblSiteId" runat="server" Text='<%# Eval("Location") %>' />
                                        </td>
                                    </tr>
                                </ItemTemplate>

                            </asp:ListView>

                        </div>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
