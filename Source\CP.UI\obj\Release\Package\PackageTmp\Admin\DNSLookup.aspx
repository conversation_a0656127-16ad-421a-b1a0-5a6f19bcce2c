﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="DNSLookup.aspx.cs" Inherits="CP.UI.Admin.DNSLookup" Title="Continuity Patrol :: DNS Loookup" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript" language="javascript">
        function Validate() {
            var oDomainText = document.getElementById('txtDomainName');
            if (oDomainText == "") {
                alert("Domain Text Cannot be blank");
                return false;
            }
            return false;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <ul id="ulMessage" runat="server" visible="false">
        <li>
            <asp:Label ID="lblMessage" runat="server" Text="ddddd"></asp:Label></li>
        <li class="close-bt"></li>
    </ul>
    <div class="innerLR">
        
        <ul class="breadcrumb show">
            <li>You are here</li>
            <li><a href="#" class="glyphicons settings"><i></i>Configuration</a></li>
            <li class="divider"></li>
            <li>DNS Lookup</li>
        </ul>
       
        <h3>DNS Lookup</h3>
        
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-md-12 form-horizontal uniformjs">
                        <div class="form-group">
                            <label class="col-md-3 control-label">
                                BCMS Server <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <asp:DropDownList ID="ddlSite" runat="server" TabIndex="1" CssClass="selectpicker col-md-6"
                                    data-style="btn-default">
                                    <asp:ListItem Value="0">Select Server</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvSite" runat="server" ControlToValidate="ddlSite"
                                    Display="Dynamic" InitialValue="0" ErrorMessage="Select Server"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div style="display: none;">
                            <label class="col-md-3 control-label">
                                Forward Reverse</label>
                        </div>
                        <div style="display: none;">
                            <asp:DropDownList ID="ddlLookUp" runat="server" TabIndex="2" Width="155px">
                                <asp:ListItem Value="0">Forward</asp:ListItem>
                                <asp:ListItem Value="1">Reverse</asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">
                                DNS Domain Name <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <asp:TextBox ID="txtDomainName" runat="server" class="form-control"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvDomainName" runat="server" Display="Dynamic" ErrorMessage="Please Enter Domain Name"
                                    ControlToValidate="txtDomainName"></asp:RequiredFieldValidator>
                                <asp:Button ID="BtnDiscover" runat="server" CssClass="btn btn-primary" Text="Discover"
                                    OnClick="BtnDiscoverClick" />
                                <asp:Button ID="btnSaveDetails" runat="server" CssClass="buttonblue" Text="Save"
                                    Visible="false" OnClick="BtnSaveClick" OnClientClick="javascript:return Validate();" />
                            </div>
                        </div>
                        <hr />
                        <div class="block-content no-padding margin-bottom8" runat="server" id="divListComponants"
                            visible="false">
                            <asp:ListView ID="lvComponent" runat="server" DataKeyNames="Id" OnPreRender="LvComponentPreRender">
                                <LayoutTemplate>
                                    <table class="table font no-bottom-margin" width="100%">
                                        <thead>
                                            <tr>
                                                <th style="width: 4%;">
                                                    <span>
                                                        <img src="../Images/icons/server.png" /></span>
                                                </th>
                                                <th>SERVER TYPE
                                                </th>
                                                <th>HOSTNAME
                                                </th>
                                                <th>IP
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                        </tbody>
                                    </table>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td>
                                            <%#Container.DataItemIndex+1 %>
                                        </td>
                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                        <td>
                                            <asp:Label ID="lblHost" runat="server" Text='<%# Eval("Host") %>' />
                                        </td>
                                        <td>&nbsp;&nbsp;
                                            <asp:Label ID="lblHostname" runat="server" Text='<%# Eval("Name") %>' />
                                        </td>
                                        <td>
                                            <asp:Label ID="lblIPAddress" runat="server" Text='<%# Eval("IPAddress")%>' />
                                        </td>
                                    </tr>
                                </ItemTemplate>
                            </asp:ListView>
                            <div class="message no-bottom-margin">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvComponent" PageSize="4">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                                <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                Out Of
                                                <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="block-footer no-margin">
                                &nbsp;
                                <div class="float-right margin-right">
                                    <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvComponent" PageSize="10">
                                        <Fields>
                                            <asp:NextPreviousPagerField ButtonType="Button" ButtonCssClass="buttonblue" ShowFirstPageButton="false"
                                                ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="Prev" />
                                            <asp:NumericPagerField NextPageText="Next" PreviousPageText="Prev" ButtonCount="10"
                                                NextPreviousButtonCssClass="buttonblue" CurrentPageLabelCssClass="buttonblue"
                                                NumericButtonCssClass="button" />
                                            <asp:NextPreviousPagerField ButtonType="Button" ButtonCssClass="buttonblue" ShowFirstPageButton="false"
                                                ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" />
                                        </Fields>
                                    </asp:DataPager>
                                </div>
                            </div>
                        </div>
                        <div class="message no-bottom-margin no-margin">
                            <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                Fields</span>
                            <asp:Label ID="lblNoRecords" Visible="false" runat="server" Text="No Records Found"></asp:Label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>