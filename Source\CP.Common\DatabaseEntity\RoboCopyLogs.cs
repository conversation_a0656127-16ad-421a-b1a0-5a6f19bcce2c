﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RoboCopyLogs", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class RoboCopyLogs : BaseEntity
    {
        #region Properties

        //[DataMember]
        //public int Id { get; set; }

        [DataMember]
        public int RoboCopyJobId { get; set; }

        [DataMember]
        public string SourceIP { get; set; }

        [DataMember]
        public string DestinationIP { get; set; }

        [DataMember]
        public string SourcePath { get; set; }

        [DataMember]
        public string DestinationPath { get; set; }


        [DataMember]
        public string RepStartTime { get; set; }

        [DataMember]
        public string RepEndTime { get; set; }



        [DataMember]
        public int TotalDirCount { get; set; }
        [DataMember]
        public int TotalDirCopiedCount { get; set; }
        [DataMember]
        public int TotalSkippedDirCount { get; set; }
        [DataMember]
        public int TotalMisMatchedDirCount { get; set; }
        [DataMember]
        public int TotalFailedDirCount { get; set; }
        [DataMember]
        public int TotalExtrasDirCount { get; set; }

        [DataMember]
        public int TotalFilesCount { get; set; }
        [DataMember]
        public int TotalFilesCopiedCount { get; set; }
        [DataMember]
        public int TotalSkippedFilesCount { get; set; }
        [DataMember]
        public int TotalMisMatchedFilesCount { get; set; }
        [DataMember]
        public int TotalFailedFilesCount { get; set; }
        [DataMember]
        public int TotalExtrasFilesCount { get; set; }

        [DataMember]
        public string TotalBytesCount { get; set; }
        [DataMember]
        public string TotalBytesCopiedCount { get; set; }
        [DataMember]
        public string TotalSkippedBytesCount { get; set; }
        [DataMember]
        public string TotalMisMatchedBytesCount { get; set; }
        [DataMember]
        public string TotalFailedBytesCount { get; set; }
        [DataMember]
        public string TotalExtrasBytesCount { get; set; }

        [DataMember]
        public string TotalTimesCount { get; set; }
        [DataMember]
        public string TotalTimesCopiedCount { get; set; }
        [DataMember]
        public string TotalSkippedTimesCount { get; set; }
        [DataMember]
        public string TotalMisMatchedTimesCount { get; set; }
        [DataMember]
        public string TotalFailedTimesCount { get; set; }
        [DataMember]
        public string TotalExtrasTimesCount { get; set; }


        [DataMember]
        public string SpeedBytesPerSeconds { get; set; }
        [DataMember]
        public string SpeedMBPerMinute { get; set; }

        [DataMember]
        public string SelectedOptions { get; set; }

         [DataMember]
        public int InfraObjectId { get; set; }

         [DataMember]
         public string Datalag
         {
             get;
             set;
         }

        #endregion Properties
    }
}
