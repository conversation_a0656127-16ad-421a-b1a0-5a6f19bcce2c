﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="BusinessServiceOverview.aspx.cs" Inherits="CP.UI.Admin.BusinessServiceOverview" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
     <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <%--  <asp:Timer runat="server" ID="UpdateTimer" Interval="60000" Enabled="false" />--%> <%--OnTick="UpdateTimerTick"--%>
    <style>
        .margin-new {
            padding: 0;
            margin: 0 8px 8px 0 !important;
            border: 1px solid #ccc;
        }

        #inner-content-div_new .row .BShead {
            box-shadow: none !important;
        }

        .margin-new.col-md-3 {
            width: 24%;
        }

        #inner-content-div_new .row.margin-new:nth-child(4n) {
            margin-right: 0 !important;
        }

        #inner-content-div_new .row:nth-child(5n+2) .BShead {
            background-color: #ffb404;
            box-shadow: -5px 0px 0px #ffb404;
        }

        #inner-content-div_new .row:nth-child(5n+3) .BShead {
            background-color: #fa590c;
            box-shadow: -5px 0px 0px #fa590c;
        }

        #inner-content-div_new .row:nth-child(5n+4) .BShead {
            background-color: #ce315d;
            box-shadow: -5px 0px 0px #ce315d;
        }

        #inner-content-div_new .row:nth-child(5n+5) .BShead {
            background-color: #ce315d;
            box-shadow: -5px 0px 0px #ce315d;
        }

        .margin-new .innerB {
            margin-top: 5px;
        }

        #inner-content-div_new {
            border-bottom: none;
        }

        .bsdash-header {
            /*background-color: #cecece;
            padding: 8px;*/
            margin-bottom: 20px;
        }

        .border-rgt {
            padding: 0px;
            border: 1px solid #dcdcdc;
            margin-right: 8px;
            border-radius: 4px;
            background: #fff;
            width: 18%;
            box-shadow: 1px 2px 1px rgba(0,0,0,0.2);
        }

        .grpicon {
            position: absolute;
            top: 32px;
            left: 15px;
            height: 34px;
        }

        .txt-white {
            padding: 3px 8px 3px 5px;
            background: #1a6cb1;
            text-align: left !important;
            color: #fff;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        .text-ServerColor {
            padding: 8px;
        }
        /*#inner-content-div h4 a {
            cursor:default;
        }*/
        #inner-content-div_new .row:nth-child(5n+1) .BShead {
            background-color: #78b732;
            box-shadow: -5px 0px 0px #78b732;
        }


        #inner-content-div_new .row .BShead {
            box-shadow: none !important;
        }

        #inner-content-div_new h4 a {
            text-decoration: none;
        }

        #inner-content-div_new span[id$=lblrpo], #inner-content-div span[id$=lblrto] {
            color: #346D9D;
        }


        #inner-content-div_new span[id$=spnrpo], #inner-content-div_new span[id$=spnrto] {
            padding-top: 5px;
            position: absolute;
            margin-top: 5px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">

        <h3><span class="business-setting-icon vertical-sub"></span>
            <b>Business Service Overview</b></h3>


        <asp:UpdatePanel ID="updmain" runat="server" UpdateMode="Conditional">
            <Triggers>
                <asp:AsyncPostBackTrigger ControlID="UpdateTimer" EventName="Tick" />
            </Triggers>
            <ContentTemplate>
                <div class="bsdash-header">
                    <div class="col-md-2 border-rgt col-md-offset-2" style="margin-left: 11%;">
                        <div class="text-right txt-white">Service Availability</div>
                        <div class="text-right text-ServerColor text-medium greenborder" id="divSerAvail">
                            <asp:Label ID="lblserviceavailabilitycount" runat="server" Text=""></asp:Label>
                            <i runat="server" id="arrowClass" class="up-arrow-icon"></i>
                            <asp:Label ID="lblserviceavailability" runat="server" Style="font-size: 21px;" Text=""></asp:Label>
                            <i runat="server" id="downServiceArrow" class="down-arrow-icon"></i>
                            <asp:HiddenField ID="hdnbsList" runat="server"></asp:HiddenField>
                        </div>
                        <img src="../Images/tab-availability.png" class="grpicon" />
                    </div>
                    <div class="col-md-2 border-rgt">
                        <div class="text-right txt-white">Business Functions</div>
                        <div class="text-right text-ServerColor yellowborder text-medium" id="divBFAvail">
                            <asp:Label ID="lblupbfCount" runat="server" Text=""></asp:Label>
                            <i class="up-arrow-icon"></i>
                            <asp:Label ID="lbldownbfCount" runat="server" Text="" Style="font-size: 21px;"></asp:Label>
                            <i class="down-arrow-icon"></i>
                            <asp:HiddenField ID="hdnbflist" runat="server" />
                        </div>
                        <img src="../Images/serv-ovr-ico.png" class="grpicon" />
                    </div>
                    <div class="col-md-2 border-rgt">
                        <div class="txt-white text-right">DR Ready</div>
                        <div class="text-right text-ServerColor redborder text-medium" id="divDrReadyCountNew">
                            <asp:Label ID="lbldrReadycount" runat="server" Text=""></asp:Label>
                            <i id="arrowDrR" runat="server" class="up-arrow-icon" style="margin-left: 5px;"></i><%--style="margin: 0px -7px 0px 0px;"--%>
                            <asp:Label ID="lbldrReady" runat="server" Text="" Style="font-size: 21px;">
                            </asp:Label><i id="arrowDrRDown" class="down-arrow-icon" runat="server" style="margin-left: 5px;"></i>
                            <asp:HiddenField ID="hdndrReadycountHtml" runat="server"></asp:HiddenField>
                        </div>
                        <img src="../Images/dr-ready.png" class="grpicon" />
                    </div>
                    <div class="col-md-2 border-rgt">
                        <div class="txt-white text-right">Business Services Count</div>
                        <div class="text-right text-ServerColor blueborder text-medium" id="divBSCount">
                            <asp:Label ID="lblBsCount" runat="server" Text="" Style="cursor: pointer; line-height: 34px;"></asp:Label>
                        </div>
                        <img src="../Images/total-services.png" class="grpicon" />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="notifyscroll_1" id="inner-content-div_new" style="overflow: auto">
                            <%-- <asp:UpdatePanel ID="updmain" runat="server" UpdateMode="Conditional">
                                <Triggers>
                                    <asp:AsyncPostBackTrigger ControlID="UpdateTimer" EventName="Tick" />
                                </Triggers>
                                <ContentTemplate>--%>
                            <asp:Repeater runat="server" ID="rptServices" OnItemDataBound="rptServices_ItemDataBound">
                                <HeaderTemplate>
                                </HeaderTemplate>
                                <ItemTemplate>
                                    <div class="row col-md-3 margin-new">
                                        <div class="col-md-12 padding-none-LR margin-none BShead">
                                            <div class="col-md-7 padding-none margin-none">
                                                <h4 class="padding padding-top-none padding-bottom-none">
                                                    <i class="BS-white-icon  text-primary"></i>
                                                    <asp:LinkButton runat="server" ID="lnkbtnBusinessServiceName" OnClick="lnkbtnBusinessServiceName_Click"></asp:LinkButton>
                                                    <asp:Label runat="server" ID="lblbusinessID" Text='<%#GetHashID(Eval("ID")) %>' Visible="false"></asp:Label>
                                                    <asp:HiddenField runat="server" ID="hdnPriority" Value='<%#Eval("Priority") %>' />

                                                </h4>

                                            </div>
                                            <div class="col-md-5 text-right">
                                                <asp:LinkButton ID="lnkbtnBSimpact" CssClass="btnreddot" runat="server" Visible="false" ToolTip="" OnClientClick="javascript:void(0)"></asp:LinkButton>
                                            </div>
                                        </div>
                                        <%--   <div class="clearfix"></div>--%>
                                        <div class="col-md-12 padding">
                                            <div class="col-md-4 text-right padding-none">
                                                <span class="bs-rpo-icon">&nbsp;</span>
                                                <asp:Label ID="lblrpo" CssClass="text-medium" runat="server" Text='NA'></asp:Label>
                                            </div>
                                            <div class="col-md-2 padding-none">
                                                <span id="spnrpo" runat="server">RPO</span>
                                                <br />
                                                <asp:Label ID="lblHrs" runat="server" CssClass="text-HighPriorityColor"></asp:Label>

                                            </div>

                                            <div class="col-md-4 text-right padding-none">
                                                <span class="bs-rpo-icon">&nbsp;</span>
                                                <asp:Label ID="lblrto" CssClass="text-medium" runat="server" Text="NA"></asp:Label>
                                            </div>
                                            <div class="col-md-2 padding-none">
                                                <span id="spnrto" runat="server">RTO </span>
                                                <br />
                                                <span id="spnRTO1" runat="server" class="text-HighPriorityColor">
                                                    <asp:Label runat="server" ID="lblRTOUnit" Text="NA"></asp:Label></span>
                                            </div>
                                            <div class="col-md-6 padding-none innerB text-small">
                                                Configured :
                               <asp:Label ID="lblConfiguredRpo" runat="server" Text='<%#GetConfiguredRpo(Eval("ID") )%>'></asp:Label>
                                                mins
                                            </div>
                                            <div class="col-md-6 padding-none innerB text-small">
                                                Configured :
                                                        <asp:Label ID="lblConfiguredRto" runat="server" Text='<%#GetConfiguredRto(Eval("ID") )%>'></asp:Label>
                                                mins
                                            </div>
                                        </div>
                                        <div class="col-md-12 padding-none">
                                            <div class="col-md-6 padding padding-top-none">
                                                <div class="text-left">
                                                    <asp:LinkButton ID="lnkimgimpact" Text="What-If Analysis" runat="server" Visible="false"></asp:LinkButton>
                                                </div>
                                            </div>
                                            <div class="col-md-6 padding padding-none-LR padding-top-none">
                                                <div id="divpriority" runat="server" class="text-left">
                                                    Priority :
                                                            <asp:Label ID="lblpriority" runat="server" CssClass="text-NetworkColor text-small"> </asp:Label>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>

            </ContentTemplate>
        </asp:UpdatePanel>
        <asp:Timer runat="server" ID="UpdateTimer" Interval="300000" OnTick="UpdateTimer_Tick" />
    </div>
    <script>
        $(function () {
            $("[id$=lnkbtnBusinessServiceName]").on("click", function () {
                localStorage.setItem("servName", $(this).text());
            });

            var st = window.outerHeight - 300;
            $('#inner-content-div_new, #inner-content-div').css("height", st + "px") +

            $("#inner-content-div").mCustomScrollbar({
                axis: "y",
                setHeight: "295px",
            });
        })
    </script>
</asp:Content>
