﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Net;
using System.Globalization;
using log4net;

namespace CP.UI.SNMP
{
    public partial class DiscoveryConfigurationForm : BasePage// System.Web.UI.Page
    {
        static IFacade _facade = new Facade();

        public static string IPAddress = string.Empty;
        private readonly ILog _logger = LogManager.GetLogger(typeof(DiscoveryConfigurationForm));

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
        }

        protected void Page_Load(object sender, EventArgs e)
        {

            if (!Page.IsPostBack)
            {
                PopulateDiscoveryConfiguration();
            }
        }

        private void PopulateDiscoveryConfiguration()
        {
            DiscoveryConfiguration objDiscoveryConfig = _facade.GetDiscoveryConfigurationById(1);
            if (objDiscoveryConfig != null)
            {
                txtIPAddressFrom.Text = objDiscoveryConfig.IPRangeFrom;
                txtIPAddressTo.Text = objDiscoveryConfig.IPRangeTo;
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateRequest("DiscoveryConfiguration", UserActionType.CreateDiscoveryConfiguration))
                {
                    DiscoveryConfiguration objDiscoveryConfig = new DiscoveryConfiguration();
                    objDiscoveryConfig.IPRangeFrom = txtIPAddressFrom.Text;
                    objDiscoveryConfig.IPRangeTo = txtIPAddressTo.Text;
                    objDiscoveryConfig.UpdatorId = 1;
                    objDiscoveryConfig.Id = 1;
                    _facade.UpdateDiscoveryConfiguration(objDiscoveryConfig);
                }
            }
            catch (Exception ex)
            {

            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId,IPAddress);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId,IPAddress);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
    }
}