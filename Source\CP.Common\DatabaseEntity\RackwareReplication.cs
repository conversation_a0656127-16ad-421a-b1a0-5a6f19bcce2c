﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RackwareRepli", Namespace = "http://www.ContinuityPlatform.com/types")]

   public class RackwareReplication : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables


        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string WaveName { get; set; }

        [DataMember]
        public string DRPolicyName { get; set; }

      

        [DataMember]
        public string CreateDate { get; set; }



        [DataMember]
        public string UpdateDate { get; set; }



        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }



        #endregion Properties

    }
}
