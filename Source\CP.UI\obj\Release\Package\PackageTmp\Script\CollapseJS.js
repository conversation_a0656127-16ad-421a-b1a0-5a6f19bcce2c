﻿

var CollapsJS = function () {

$('.accordion-toggle[data-parent=#tabAccountAccordion]').click(function() {
    sessionStorage.CollapseChildCSS = "collapse";
    var href = $(this).attr("href");
    sessionStorage.setItem("CollapseMainID", href);
if($(href).hasClass("in")){
    sessionStorage.CollapseMainCSS = "collapse";
    }
else{
    sessionStorage.CollapseMainCSS = "in";
     }
});

 if(sessionStorage.CollapseMainCSS=='collapse') {
        var Ghref = sessionStorage.getItem("CollapseMainID");
          $(Ghref).addClass("collapse");
    }
 else if(sessionStorage.CollapseMainCSS=='in'){
          var Ghref = sessionStorage.getItem("CollapseMainID");
         $(Ghref).addClass("in");
    }

$('.accordion-toggle[data-parent=#accordion]').click(function() {
 var childhref = $(this).attr("href");
 sessionStorage.setItem("CollapseChildID", childhref);
 if($(childhref).hasClass("in")){
    sessionStorage.CollapseChildCSS = "collapse";
    }
else{
    sessionStorage.CollapseChildCSS = "in";
    };
});

if(sessionStorage.CollapseChildCSS=='collapse') {
    var childGhref = sessionStorage.getItem("CollapseChildID");
    $(childGhref).addClass("collapse");
   }
else if(sessionStorage.CollapseChildCSS=='in'){
    var childGhref = sessionStorage.getItem("CollapseChildID");
    $(childGhref).addClass("in");
}
};