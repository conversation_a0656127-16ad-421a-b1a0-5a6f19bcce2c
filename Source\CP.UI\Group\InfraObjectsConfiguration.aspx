﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="InfraObjectsConfiguration.aspx.cs" Inherits="CP.UI.InfraObjectsConfiguration" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });

        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
            var submit = 0;
            $("#ctl00_cphBody_Wizard1_FinishNavigationTemplateContainerID_FinishButton").click(function () {

                if (++submit > 1) {
                    return false;
                }
            });
        }

    </script>
    <script type="text/javascript">
         

    </script>

    <style>
        .chosen-select + .chosen-container {
            width: 48% !important;
            opacity: 1 !important;
        }

        .btn-nxt {
            width: 111px !important;
        }

        tr.widget-body {
            border-bottom: none !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <script src="../Script/InfraObjectsConfiguration.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {


            $("table[id$=Wizard1] > tbody").addClass("widget widget-tabs widget-tabs-double");
            $("table[id$=Wizard1] > tbody > tr:first-child ").addClass("widget-head");
            $("table[id$=Wizard1] > tbody > tr:nth-child(2)").addClass("widget-body");
            $("#li0").addClass("no-padding");
            $('input:radio').click(function () {
                $('label:has(input:radio:checked)').addClass('active');
                $('label:has(input:radio:not(:checked))').removeClass('active');
            });
        });

        Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
        Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
        function BeginRequestHandler(sender, args) {
        }
        function EndRequestHandler(sender, args) {
            $("table[id$=Wizard1] > tbody").addClass("widget widget-tabs widget-tabs-double");
            $("table[id$=Wizard1] > tbody > tr:first-child ").addClass("widget-head");
            $("table[id$=Wizard1] > tbody > tr:nth-child(2)").addClass("widget-body");
            $("#li0").addClass("no-padding");
        }
    </script>


    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

                <h3>
                    <img src="../Images/bus-func-icon.png">
                    InfraObject Configuration
                </h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="wizard">
                        <asp:Wizard ID="Wizard1" runat="server" Width="100%" CssClass="wizard" DisplaySideBar="false"
                            NavigationStyle-HorizontalAlign="Center" StepPreviousButtonStyle-CssClass="btn btn-primary previous btn-nxt"
                            StartNextButtonStyle-CssClass="btn btn-primary previous btn-nxt" FinishPreviousButtonStyle-CssClass="wizardbutton prev"
                            OnFinishButtonClick="Wizard1FinishButtonClick"
                            OnNextButtonClick="Wizard1NextButtonClick"
                            OnPreviousButtonClick="Wizard1PreviousButtonClick">
                            <NavigationStyle CssClass="pagination margin-bottom-none pull-right" HorizontalAlign="center" VerticalAlign="Top" Width="86%" />
                            <FinishPreviousButtonStyle CssClass="btn btn-primary btn-nxt" />
                            <StepStyle CssClass="tab-content padding"></StepStyle>
                            <NavigationButtonStyle CssClass="btn btn-primary btn-nxt" />
                            <StartNextButtonStyle CssClass="btn btn-primary next btn-nxt"></StartNextButtonStyle>
                            <HeaderTemplate>
                                <div class="widget-head" style="height: 40px;">
                                    <ul>

                                        <li class="active" id="li0" runat="server"><a class="glyphicons circle_ok"><i id="iIcon1" color="" runat="server"></i><span
                                            class="strong">Step 1</span><span> Create InfraObject </span></a></li>
                                        <li id="li1" runat="server"><a class="glyphicons circle_ok"><i></i><span class="strong">Step 2</span>
                                            <span>Establish Relation</span> </a></li>
                                        <li id="li2" runat="server"><a class="glyphicons circle_ok"><i></i><span class="strong">Step 3</span>
                                            <span>Summary</span> </a></li>
                                        <li id="li3" runat="server" visible="False"><a class="glyphicons circle_ok"><i></i><span class="strong">Step 3</span>
                                            <span>Luns Summary</span> </a></li>
                                        <li id="li4" runat="server" visible="False"><a class="glyphicons circle_ok"><i></i><span class="strong">Step 3</span>
                                            <span>Native Log Shipping</span> </a></li>
                                        <li id="li5" runat="server" visible="False"><a class="glyphicons circle_ok"><i></i><span class="strong">Step 3</span>
                                            <span>Archive Redo</span> </a></li>
                                    </ul>
                                </div>
                            </HeaderTemplate>
                            <WizardSteps>
                                <asp:WizardStep ID="WizardStep1" runat="server" Title="Step 1" StepType="Auto">

                                    <div class="task padding-5">
                                        <h4>Create InfraObject</h4>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    InfraObject Name <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtName" runat="server" CssClass="form-control"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" CssClass="error" ErrorMessage="Enter InfraObject Name" Display="Dynamic" ControlToValidate="txtName"></asp:RequiredFieldValidator>
                                                    <asp:CustomValidator ID="CustomValidator1" runat="server" ControlToValidate="txtName" Display="Dynamic" OnServerValidate="ValidationFunctionName" CssClass="error" ErrorMessage="Infra Object Aleady Exist" ValidationGroup="Step1"></asp:CustomValidator>

                                                    <span class="hide">Enter InfraObject Name</span>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Description <span class="inactive">*</span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtDescription" runat="server" cols="20" Rows="2" class="form-control"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" CssClass="error" ErrorMessage="Enter InfraObject Description" ControlToValidate="txtDescription" Display="Dynamic"></asp:RequiredFieldValidator>
                                                    <%--ValidationGroup="Step1"--%>
                                                    <span class="hide">Enter InfraObject Description</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Business Service <span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlBusinessService" runat="server" AutoPostBack="True" CssClass="chosen-select col-md-6"
                                                        data-style="btn-default" OnSelectedIndexChanged="OnSelectedIndexChangedBindFunctions">
                                                        <asp:ListItem Value="000">- Select Business Service -</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <span class="hide">Select Business Service</span>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator3" CssClass="error" runat="server" ErrorMessage="Select Business Service" ControlToValidate="ddlBusinessService" InitialValue="000"></asp:RequiredFieldValidator>
                                                    <%--ValidationGroup="Step1"--%>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Business Function <span class="inactive">*</span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlFunction" runat="server" CssClass="chosen-select col-md-6"
                                                        data-style="btn-default">
                                                        <asp:ListItem Value="000">- Select Business Function -</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <span class="hide">Select Business Function</span>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator4" CssClass="error" runat="server" ErrorMessage="Select Business Function" ControlToValidate="ddlFunction" InitialValue="000"></asp:RequiredFieldValidator>
                                                    <%--ValidationGroup="Step1"--%>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    DR Ready<span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:CheckBox ID="chkDRReady" runat="server" AutoPostBack="True" CssClass="vertical-align" OnCheckedChanged="OnCheckedChangedEnableControls" />
                                                    <asp:CustomValidator ID="CustomValidator2" runat="server" Display="Dynamic" OnServerValidate="ValidateCheckBox" CssClass="error" ErrorMessage="Check Required"></asp:CustomValidator>
                                                    <%--ValidationGroup="Step1"--%>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Type<span class="inactive">*</span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlBusinessType" runat="server" AutoPostBack="True" CssClass="chosen-select col-md-6" data-style="btn-default" OnSelectedIndexChanged="BusinessType_IndexChanged">
                                                        <asp:ListItem Value="0">- Select Type -</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <span class="hide">Select Type</span>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator5" CssClass="error" runat="server" ErrorMessage="Select Type"
                                                        ControlToValidate="ddlBusinessType" InitialValue="0" Display="Dynamic"></asp:RequiredFieldValidator>
                                                    <%--ValidationGroup="Step1"--%>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Sub Type<span class="inactive">*</span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlSubBusinessType" runat="server" AutoPostBack="True" CssClass="chosen-select col-md-6" data-style="btn-default" OnSelectedIndexChanged="BusinessSubType_IndexChanged">
                                                        <asp:ListItem Value="000">- Select Type -</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <span class="hide">Select Type</span>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator21" CssClass="error" runat="server" ErrorMessage="Select Sub Type"
                                                        ControlToValidate="ddlSubBusinessType" InitialValue="000" Display="Dynamic"></asp:RequiredFieldValidator>
                                                    <%--ValidationGroup="Step1"--%>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label" id="lblrecoverytype" runat="server">
                                                    Recovery Solution Type<span class="inactive">*</span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlReplicationType" runat="server" Enabled="False" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ReplicationType_IndexChanged">
                                                        <asp:ListItem Value="000">- Select Solution Type -</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <span class="hide">Select Solution Type </span>

                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator6" CssClass="error" runat="server" ErrorMessage="Select Solution Type"
                                                        ControlToValidate="ddlReplicationType" InitialValue="000"></asp:RequiredFieldValidator>
                                                    <%--ValidationGroup="Step1"--%>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3">
                                                    IsQueueMonitor<span class="inactive"></span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:CheckBox ID="ChkIsQueueMonitor" runat="server" AutoPostBack="True" CssClass="vertical-align" />
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Near Site<span class="inactive"></span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:CheckBox ID="chkNearSite" runat="server" AutoPostBack="True" CssClass="vertical-align" OnCheckedChanged="OnCheckedChangedEnableNearSite" />
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    &nbsp;</label>

                                                <div class="col-md-1">
                                                    <asp:CheckBox ID="ChkIspair" runat="server" AutoPostBack="True" CssClass="vertical-align" Text="IsPair" Checked="false" OnCheckedChanged="ChkIspair_CheckedChanged" />
                                                </div>
                                                <label class="col-md-1 control-label">
                                                    &nbsp;</label>
                                                <div class="col-md-2">
                                                    <asp:CheckBox ID="ChkIsAssociate" runat="server" AutoPostBack="True" CssClass="vertical-align" Text="IsAssociate" Checked="false" OnCheckedChanged="ChkIsAssociate_CheckedChanged" />
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label id="lblPairInfra" class="col-md-3 control-label" runat="server">
                                                    Pair InfraObject ID<span class="inactive">*</span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlPairinfraobjectid" runat="server" AutoPostBack="True" CssClass="chosen-select col-md-6" data-style="btn-default" Enabled="false">
                                                        <asp:ListItem Value="000">- Select InfraObject ID -</asp:ListItem>
                                                    </asp:DropDownList>

                                                    <span class="hide">Select Type</span>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator26" CssClass="error" Enabled="false" runat="server" ErrorMessage="Select Sub Type"
                                                        ControlToValidate="ddlPairinfraobjectid" InitialValue="000" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>

                                            <div id="divGroupDetail" style="display: none">

                                                <div class="float-left side1">
                                                    <label>
                                                        InfraObject</label><span class="inactive">*</span>
                                                </div>
                                                <div>
                                                    <asp:DropDownList ID="ddlGroupList" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default">
                                                    </asp:DropDownList>

                                                    <span>Select Site Solution Type</span>
                                                </div>
                                                <hr />
                                            </div>


                                            <div runat="server" id="divCommand" visible="False">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        PR Monitoring Workflow<span class="inactive"></span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlMonitorWorkflow" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default">
                                                            <asp:ListItem Value="000">- Select Workflow Name -</asp:ListItem>
                                                        </asp:DropDownList>

                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Monitoring DR Application<span class="inactive"></span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:CheckBox ID="chkDrMonitorApplication" runat="server" AutoPostBack="True" OnCheckedChanged="OnDrMonitorCheckChange"></asp:CheckBox>

                                                    </div>
                                                </div>
                                            </div>
                                            <div id="divDrMonitorWorkflow" class="form-group" runat="server">
                                                <label class="col-md-3 control-label" id="lblDrMonitorAppWorkflow" runat="server">
                                                    DR Monitoring Workflow<span class="inactive"></span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlDRMonitorWorkflow" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default">
                                                        <asp:ListItem Value="000">- Select Workflow Name -</asp:ListItem>
                                                    </asp:DropDownList>


                                                </div>
                                            </div>

                                            <div class="form-group" runat="server" id="divOutPut" visible="False">
                                                <label class="col-md-3 control-label">
                                                    Output<span class="inactive">*</span>
                                                </label>

                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtOutput" type="text" class="form-control"
                                                        runat="server" />
                                                    <span class="hide">Enter Output</span>
                                                </div>
                                            </div>


                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Priority<span class="inactive">*</span>
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlPriority" runat="server" class="chosen-select col-md-6" data-style="btn-default">
                                                        <asp:ListItem Value="000">- Select Priority -</asp:ListItem>
                                                        <asp:ListItem Value="1">High </asp:ListItem>
                                                        <asp:ListItem Value="2">Medium </asp:ListItem>
                                                        <asp:ListItem Value="3">Low </asp:ListItem>
                                                    </asp:DropDownList>
                                                    <span class="hide">Select Priority</span>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator7" CssClass="error" runat="server" ErrorMessage="Select Priority"
                                                        ControlToValidate="ddlPriority" InitialValue="000" Display="Dynamic"></asp:RequiredFieldValidator>
                                                    <%--ValidationGroup="Step1"--%>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr />

                                    <div class="col-xs-4 row">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
                                        <asp:Label ID="lblProfileResult" runat="server" Text="&nbsp;"></asp:Label>
                                    </div>


                                </asp:WizardStep>

                                <asp:WizardStep ID="WizardStep2" runat="server" StepType="Auto">
                                    <div class="task padding-5">
                                        <h4>Establish Relation</h4>
                                    </div>
                                    <div class="divtbl">
                                        <table class="tblestblishrel dynamicTable colVis table table-striped table-bordered table-condensed table-white dataTablezz" style="width: 100%">
                                            <tbody>
                                                <tr>
                                                    <th><b>Components</b></th>
                                                    <%--  <td>Server<span class="inactive">*</span></td>--%>
                                                    <td id="tdServerComponant" runat="server">
                                                        <asp:Label ID="lblServerName" runat="server" Text="Server"></asp:Label><span class="inactive">*</span></td>
                                                    <td id="SRMserver" runat="server" visible="false">
                                                        <asp:Label ID="lblServerSRMName" runat="server" Text="SRMServer"></asp:Label><span class="inactive">*</span></td>
                                                    <%-- <td id="tdServerComponant" runat="server">Server<span class="inactive">*</span></td>--%>
                                                    <td id="tdDataBaseComponant" runat="server">DataBase<span class="inactive">*</span> </td>
                                                    <td id="tdMailboxComponent" runat="server" style="height: 70px" visible="false">Mailbox Database<span class="inactive">*</span></td>
                                                    <td id="tdReplicationComponant" runat="server">Replication<span class="inactive">*</span></td>
                                                    <td id="tdClusterServer" runat="server">Cluster<span class="inactive"></span>
                                                        <asp:CheckBox ID="chkIsCluster" runat="server" AutoPostBack="True" CssClass="vertical-align" Checked="false" OnCheckedChanged="chkIsCluster_CheckedChanged" />
                                                    </td>
                                                </tr>

                                                <tr id="trPr" runat="server">
                                                    <th id="serverId"><b>Production </b></th>
                                                    <td>
                                                        <asp:DropDownList ID="ddlServerPr" runat="server" AutoPostBack="True" class="chosen-select col-md-6" data-style="btn-default" OnSelectedIndexChanged="PrServer_SelectedIndexChanged">
                                                            <asp:ListItem Value="000">- Select Server Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator8" runat="server" ErrorMessage="Select Server Name"
                                                            ControlToValidate="ddlServerPr" InitialValue="000" CssClass="error"></asp:RequiredFieldValidator>
                                                    </td>
                                                    <td id="tdProductionDataBase" runat="server">
                                                        <asp:DropDownList ID="ddlDatabasePr" runat="server" class="chosen-select col-md-6" AutoPostBack="True" data-style="btn-default" OnSelectedIndexChanged="DatabasePrSelectedIndexChanged">
                                                            <asp:ListItem Value="000">- Select Database Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator9" runat="server" ErrorMessage="Select Database Name"
                                                            ControlToValidate="ddlDatabasePr" CssClass="error" InitialValue="000"></asp:RequiredFieldValidator>

                                                        <div id="divPRhostname" runat="server" visible="false" style="display: inline-block; float: right; margin-top: 6px; margin-right: 24px;">
                                                            <asp:CheckBox ID="chkPRhostname" runat="server" AutoPostBack="True" CssClass="vertical-align" OnCheckedChanged="chkPRhostname_CheckedChanged" />
                                                            <label>PRHostName</label>
                                                        </div>

                                                    </td>
                                                    <td id="tdProductionExchaneDAGMailBox" runat="server" style="height: 70px" visible="false">
                                                        <asp:ListBox ID="lstExcDAGPR" runat="server" Height="50px" Enabled="false"></asp:ListBox>
                                                        <%--  <asp:RequiredFieldValidator ID="RequiredFieldValidator24" runat="server" ErrorMessage="Select Mailbox Database Name"
                                                            ControlToValidate="lstExcDAGPR" CssClass="error"></asp:RequiredFieldValidator>--%>
                                                    </td>

                                                    <td id="tdProductionSecondServer" runat="server" visible="false">
                                                        <asp:DropDownList ID="ddlServerPr2" runat="server" AutoPostBack="True" class="chosen-select col-md-6" data-style="btn-default">
                                                            <asp:ListItem Value="000">- Select Server Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator22" runat="server" ErrorMessage="Select Server Name"
                                                            ControlToValidate="ddlServerPr2" InitialValue="000" CssClass="error"></asp:RequiredFieldValidator>
                                                    </td>

                                                    <td id="tdProductionReplication" runat="server">
                                                        <asp:DropDownList ID="ddlReplicationPr" runat="server" class="chosen-select col-md-6" data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ReplicationPr_SelectedIndexChanged">
                                                            <asp:ListItem Value="000">- Select Replication Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator10" runat="server" CssClass="error" ErrorMessage="Select Replication Name"
                                                            ControlToValidate="ddlReplicationPr" InitialValue="000"></asp:RequiredFieldValidator>
                                                    </td>

                                                    <td id="tdIsPrCloud" runat="server" visible="false">Azure Cloud
                                                        <asp:CheckBox ID="chkPrCloud" runat="server" OnCheckedChanged="chkPrCloud_CheckedChanged" AutoPostBack="true" /></td>
                                                    <%-- <td id="tdClusterlist" runat="server">
                                                        <asp:DropDownList ID="ddlClusterProfileName" runat="server" class="selectpicker col-md-6" data-style="btn-default" AutoPostBack="True" Visible="false"></asp:DropDownList>
                                                        <%--  <asp:RequiredFieldValidator ID="rfvCluster" runat="server" CssClass="error" ErrorMessage="Select Cluster Profile Name"
                                                            ControlToValidate="ddlClusterProfileName" InitialValue="0"></asp:RequiredFieldValidator>--%>
                                                    <%--  </td>--%>


                                                    <td id="tdCluster" runat="server" visible="false">
                                                        <asp:DropDownList ID="ddlCluster" runat="server" AutoPostBack="True" class="selectpicker col-md-6" data-style="btn-default" Visible="true" OnSelectedIndexChanged="ddlCluster_SelectedIndexChanged">
                                                            <asp:ListItem Value="000">- Select Server Name -</asp:ListItem>
                                                            <asp:ListItem Value="1">Veritas</asp:ListItem>
                                                            <asp:ListItem Value="2">HACMP</asp:ListItem>
                                                        </asp:DropDownList>
                                                    </td>
                                                    <td id="tdClusterPr" runat="server" visible="false">

                                                        <asp:DropDownList ID="ddlClusterPR" runat="server" AutoPostBack="True" CssClass="new-select" Style="width: 60%" class="selectpicker col-md-6" data-style="btn-default" Visible="true">
                                                            <%--<asp:ListItem Value="000">- Select Server Name -</asp:ListItem>--%>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator27" runat="server" CssClass="error" ErrorMessage="Select Server Name" ControlToValidate="ddlClusterPR" InitialValue="000"></asp:RequiredFieldValidator>

                                                    </td>
                                                </tr>
                                                <tr id="trNearDr" runat="server" visible="False">
                                                    <th><b>Near DR </b></th>
                                                    <td>
                                                        <select id="ddlNearDrServer" class="selectpicker col-md-6" data-style="btn-default"></select>

                                                    </td>
                                                    <td>
                                                        <select id="ddlNearDrDatabase" class="selectpicker col-md-6" data-style="btn-default"></select>

                                                    </td>
                                                    <td>
                                                        <select id="ddlNearDrReplication" class="selectpicker col-md-6" data-style="btn-default"></select>

                                                    </td>
                                                </tr>
                                                <tr id="trDr" runat="server">
                                                    <th id="drserverId"><b>DR</b></th>
                                                    <td id="trDrDR" runat="server">
                                                        <asp:DropDownList ID="ddlServerDr" runat="server" AutoPostBack="True" class="chosen-select col-md-6" data-style="btn-default" OnSelectedIndexChanged="DrServer_SelectedIndexChanged">
                                                            <asp:ListItem Value="000">- Select Server Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator11" runat="server" ErrorMessage="Select Server Name"
                                                            ControlToValidate="ddlServerDr" InitialValue="000" CssClass="error"></asp:RequiredFieldValidator>

                                                    </td>
                                                    <td id="tdDrDatabase" runat="server">
                                                        <asp:DropDownList ID="ddlDatabaseDr" runat="server" class="chosen-select col-md-6" data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="DatabaseDrSelectedIndexChanged">
                                                            <asp:ListItem Value="000">- Select Database Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator12" runat="server" ErrorMessage="Select Database Name"
                                                            ControlToValidate="ddlDatabaseDr" InitialValue="000" CssClass="error"></asp:RequiredFieldValidator>

                                                        <input id="btnRelateNodes" runat="server" visible="False" value="Relate Nodes" type="button" class="btn btn-primary" title="Relate Nodes" />

                                                        <div id="divDRhostname" runat="server" visible="false" style="display: inline-block; float: right; margin-top: 6px; margin-right: 24px;">
                                                            <asp:CheckBox ID="chkDRhostname" runat="server" AutoPostBack="True" CssClass="vertical-align" OnCheckedChanged="chkDRhostname_CheckedChanged" />
                                                            <label>DRHostName</label>
                                                        </div>
                                                    </td>
                                                    <td id="tdDrExchaneDAGMailBox" runat="server" style="height: 70px" visible="false">
                                                        <asp:ListBox ID="lstExcDAGDR" runat="server" Height="50px" Enabled="false"></asp:ListBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator25" runat="server" ErrorMessage="Select Mailbox Database Name"
                                                            ControlToValidate="lstExcDAGDR" InitialValue="0" CssClass="error"></asp:RequiredFieldValidator>
                                                    </td>

                                                    <td id="TdServerDRSecondServer" runat="server" visible="false">
                                                        <asp:DropDownList ID="ddlServerDr2" runat="server" AutoPostBack="True" class="chosen-select col-md-6" data-style="btn-default">
                                                            <asp:ListItem Value="000">- Select Server Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator23" runat="server" ErrorMessage="Select Server Name"
                                                            ControlToValidate="ddlServerDr2" InitialValue="000" CssClass="error"></asp:RequiredFieldValidator>
                                                    </td>

                                                    <td id="tdDrReplication" runat="server">
                                                        <asp:DropDownList ID="ddlReplicationDr" runat="server" class="chosen-select col-md-6" data-style="btn-default">
                                                            <asp:ListItem Value="000">- Select Replication Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator13" runat="server" CssClass="error" ErrorMessage="Select Replication Name"
                                                            ControlToValidate="ddlReplicationDr" InitialValue="000"></asp:RequiredFieldValidator>

                                                    </td>

                                                    <%--<td></td>--%>
                                                    <td id="tdClusterDr" runat="server" visible="false">
                                                        <asp:DropDownList ID="ddlClusterDR" runat="server" AutoPostBack="True" CssClass="new-select" Style="width: 60%" class="selectpicker col-md-6" Visible="true" data-style="btn-default">
                                                            <asp:ListItem Value="000">- Select Server Name -</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator28" runat="server" CssClass="error" ErrorMessage="Select Server Name" ControlToValidate="ddlClusterDR" InitialValue="000"></asp:RequiredFieldValidator>--%>
                                                    </td>

                                                    <td id="tdDRCloud" runat="server" visible="false">Azure Cloud
                                                        <asp:CheckBox ID="chkDrCloud" runat="server" OnCheckedChanged="chkDrCloud_CheckedChanged" AutoPostBack="true" /></td>
                                                </tr>
                                            </tbody>
                                        </table>

                                        <div id="test"></div>
                                    </div>

                                    <div id="divNodes" runat="server" class="modal bg" style="display: none;">
                                        <div class="modal-dialog" style="display: block; left: 383px; top: 194px; width: 50%;">
                                            <div class="modal-content ">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">Relate Nodes</h3>
                                                    <a id="aClosePopup" class="close">ˣ</a>
                                                </div>
                                                <div class="modal-body">
                                                    <table class="table table-bordered table-condensed margin-bottom-none" width="100%">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 5%">#
                                                                </th>
                                                                <th style="width: 40%">Production Nodes
                                                                    <label id="prHeadDb"></label>
                                                                </th>
                                                                <th style="width: 40%;">DR Nodes<label id="drHeadDb"></label>
                                                                </th>
                                                                <th style="width: 15%">Action</th>
                                                            </tr>
                                                        </thead>
                                                    </table>
                                                    <div class="scrollL">
                                                        <table class="table table-bordered table-condensed" width="100%">
                                                            <tbody id="tbNodes" runat="server">
                                                                <tr>
                                                                    <td id="tdNodes" style="width: 5%;">#
                                                                    </td>
                                                                    <td id="tdNodePr" style="width: 40%;">

                                                                        <asp:DropDownList ID="ddlNodePr" runat="server" class="selectpicker col-md-9" data-style="btn-default">
                                                                            <asp:ListItem Value="000">- Select PR Node-</asp:ListItem>
                                                                        </asp:DropDownList>
                                                                        <span class="hide">Select Node</span>
                                                                    </td>
                                                                    <td id="tdNodeDr" style="width: 40%;">

                                                                        <asp:DropDownList ID="ddlNodeDr" runat="server" class="selectpicker col-md-9" data-style="btn-default">
                                                                            <asp:ListItem Value="000">- Select DR Node-</asp:ListItem>
                                                                        </asp:DropDownList>
                                                                        <span class="hide">Select Node</span> </td>
                                                                    <td style="width: 15%;" class="center">
                                                                        <img alt="Add" id="imgAddNodes" src="../Images/icons/plus-circle.png" />
                                                                        <div id="nodepallet">
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div class="modal-footer align-right">
                                                    <input id="btnSave" runat="server" type="button" value="Save" class="btn btn-primary" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <asp:HiddenField runat="server" ID="hdnCurrentObjID" Value="" />
                                </asp:WizardStep>
                                <asp:WizardStep ID="WizardStep3" runat="server" StepType="Auto">
                                    <asp:Panel runat="server" ID="pnlSummary" Visible="False">
                                        <div class="task">

                                            <h4>Summary</h4>
                                        </div>
                                        <table class="table table-striped table-bordered table-condensed" width="100%">
                                            <thead>
                                                <tr>
                                                    <th>InfraObject Summary</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td style="width: 34%;">InfraObject Name</td>
                                                    <td>
                                                        <asp:Label ID="SGroupName" runat="server" Text=""></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Description</td>
                                                    <td>
                                                        <asp:Label ID="SGroupDesc" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>Business Service</td>
                                                    <td>
                                                        <asp:Label ID="SGroupService" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>Business Function</td>
                                                    <td>
                                                        <asp:Label ID="SGroupFunction" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>Type</td>
                                                    <td>
                                                        <asp:Label ID="SGroupRepType" runat="server" Text=""></asp:Label></td>
                                                </tr>

                                                <tr>
                                                    <td>DR Ready</td>
                                                    <td>
                                                        <asp:Label ID="SGroupDrReady" runat="server" Text=""></asp:Label></td>
                                                </tr>

                                                <tr id="trRecoveryType" runat="server">
                                                    <td>Recovery Solution Type</td>
                                                    <td>
                                                        <asp:Label ID="SGroupRecoveryType" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>Near Site</td>
                                                    <td>
                                                        <asp:Label ID="SGroupNearSite" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr id="trSummaryCommand" runat="server" visible="False">
                                                    <td>PR Monitoring Workflow</td>
                                                    <td>
                                                        <asp:Label ID="SGroupCommand" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr id="trDrApplicationCheck" runat="server" visible="False">
                                                    <td>Dr Monitoring Application</td>

                                                    <td>
                                                        <asp:Label ID="lblCheckDrMonitor" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr id="trDRMonitoringWorkflow" runat="server" visible="False">
                                                    <td>DR Monitoring Workflow</td>
                                                    <td>
                                                        <asp:Label ID="lblDrMonitorWorkflow" runat="server" Text=""></asp:Label></td>
                                                </tr>

                                                <tr>
                                                    <td>IsQueueMonitor</td>
                                                    <td>
                                                        <asp:Label ID="SGroupIsQueueMonitor" runat="server" Text=""></asp:Label></td>
                                                </tr>



                                                <tr>
                                                    <td>Priority</td>
                                                    <td>
                                                        <asp:Label ID="SGroupPriority" runat="server" Text=""></asp:Label></td>
                                                </tr>


                                                <tr>
                                                    <td>IsPair</td>
                                                    <td>
                                                        <asp:Label ID="SGroupIsPair" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>IsAssociate</td>
                                                    <td>
                                                        <asp:Label ID="SGroupIsAssociate" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>Pair InfraObject ID</td>
                                                    <td>
                                                        <asp:Label ID="SGroupInfraID" runat="server" Text=""></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>IsAssociate InfraObject ID</td>
                                                    <td>
                                                        <asp:Label ID="SGroupAssociateID" runat="server" Text=""></asp:Label></td>
                                                </tr>

                                            </tbody>
                                        </table>
                                        <div id="divSummaryNearDr" class="divtbl">
                                            <table class="table table-striped table-bordered table-white table-responsive table-primary" width="100%">
                                                <tbody>

                                                    <tr id="trComponents">
                                                        <td id="tdPR" runat="server" style="width: 34% !important; background-color: #4A8BC2 !important; color: #FFFFFF ! important;"><b>#</b></td>
                                                        <td id="tdPrComponent" style="background-color: #4A8BC2 !important; color: #FFFFFF ! important;">
                                                            <label id="lblSummaryNearPr" style="font-weight: bold ! important;" runat="server">Production</label>
                                                        </td>
                                                        <td id="Td1" style="background-color: #4A8BC2 !important; color: #FFFFFF ! important;" runat="server" visible="False"><b>Near DR </b></td>
                                                        <td id="tdDRComponent" style="background-color: #4A8BC2 !important; color: #FFFFFF ! important;" runat="server">
                                                            <%--<label id="lblSummaryNearDr" style="/*font-weight: bold ! important;*/">DR</label>--%>
                                                            <label id="lblSummaryNearDr" style="font-weight: bold ! important;" runat="server">DR</label>
                                                        </td>

                                                    </tr>

                                                    <tr id="trServer">
                                                        <td id="tdPRServer" runat="server">
                                                            <%--<label style="font-weight: bold ! important;">Server</label>--%>
                                                            <asp:Label Style="font-weight: bold ! important;" ID="lblPRServer" runat="server" Text="Server"></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="SGrServerPr" runat="server" Text=""></asp:Label></td>

                                                        <td id="SGrTdServerDr" runat="server">
                                                            <asp:Label ID="SGrServerDr" runat="server" Text=""></asp:Label></td>

                                                    </tr>

                                                    <tr id="trSummaryDataBase" runat="server">
                                                        <td id="tdPrDatabase" runat="server">
                                                            <label style="font-weight: bold ! important;">DataBase</label></td>
                                                        <td>
                                                            <asp:Label ID="SGrDatabaseNearPr" runat="server" Text=""></asp:Label></td>
                                                        <td runat="server" id="SGrTDServereDrDatabase">
                                                            <asp:Label ID="SGrDatabaseNearDr" runat="server" Text=""></asp:Label><a id="btnRacNodeSummary" runat="server" visible="false" href="javascript:void(0);" title="Relate Nodes">(Nodes Relation Summary)</a>
                                                        </td>

                                                    </tr>
                                                    <tr id="trSummaryExchangeMailBoxDAG" runat="server" visible="false">
                                                        <td>
                                                            <label style="font-weight: bold ! important;">Mailbox database</label></td>
                                                        <td id="tdProductionSummaryExchaneDAGMailBox" runat="server" style="height: 70px">
                                                            <asp:ListBox ID="lstSummaryExcDAGPR" runat="server" Height="50px" Enabled="false"></asp:ListBox>
                                                        </td>
                                                        <td id="tdDRSummaryExchaneDAGMailBox" runat="server" style="height: 70px">
                                                            <asp:ListBox ID="lstSummaryExcDAGDR" runat="server" Height="50px" Enabled="false"></asp:ListBox>
                                                        </td>
                                                    </tr>
                                                    <tr id="trSummaryReplication" runat="server">
                                                        <td>
                                                            <label style="font-weight: bold ! important;">Replication</label></td>
                                                        <td id="tdReplicationPr">
                                                            <asp:Label ID="SGrReplicationPr" runat="server" Text=""></asp:Label></td>

                                                        <td id="tdReplicationD" runat="server">
                                                            <asp:Label ID="SGrReplicationDr" runat="server" Text=""></asp:Label>
                                                        </td>

                                                    </tr>
                                                    <tr id="trSecondServer" runat="server" visible="false">
                                                        <td id="td3" runat="server">
                                                            <label style="font-weight: bold ! important;">Server</label></td>
                                                        <td>
                                                            <asp:Label ID="lblPR2" runat="server" Text=""></asp:Label></td>

                                                        <td id="Td4" runat="server">
                                                            <asp:Label ID="lblDr2" runat="server" Text=""></asp:Label></td>

                                                    </tr>

                                                    <tr id="trClusterServer" runat="server">
                                                        <td id="tdPRCluster" runat="server">
                                                            <label style="font-weight: bold ! important;">Cluster</label></td>
                                                        <td>
                                                            <asp:Label ID="SGClusterPR" runat="server" Text=""></asp:Label></td>

                                                        <td id="tdDRCluster" runat="server">
                                                            <asp:Label ID="SGClusterDR" runat="server" Text=""></asp:Label></td>

                                                    </tr>

                                                </tbody>
                                            </table>
                                        </div>
                                        <div id="divnodesum" style="display: none;" class="modal bg">
                                            <div class="modal-dialog" style="display: block; left: 383px; top: 194px; width: 50%;">
                                                <div class="modal-content ">
                                                    <div class="modal-header">
                                                        <h3 class="modal-title">Relate Nodes</h3>
                                                        <a id="a1" class="close">ˣ</a>
                                                    </div>
                                                    <div class="modal-body">
                                                        <table class="table table-bordered table-condensed margin-bottom-none" width="100%">
                                                            <thead>
                                                                <tr>
                                                                    <th style="width: 5%">#
                                                                    </th>
                                                                    <th style="width: 35%">Production Nodes
                                                                    <label id="Label1"></label>
                                                                    </th>
                                                                    <th style="width: 35%">DR Nodes<label id="Label2"></label>
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                        </table>
                                                        <div class="scrollL">
                                                            <table class="table table-bordered table-condensed" width="100%">
                                                                <tbody id="tNodeSummaryBody"></tbody>
                                                            </table>
                                                        </div>
                                                        <div class="modal-footer text-right">
                                                            <input id="btnCloseSummary" type="button" value="OK" class="btn btn-primary" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </asp:Panel>
                                    <asp:Panel runat="server" ID="pnlLunsSummary" Visible="False">
                                        <div class="task padding-5">
                                            <%--<span class="number">3</span>--%>
                                            <h4>Production Server Filesystem Information for AIX Operating System</h4>
                                        </div>
                                        <asp:ListView ID="lvLunsList" runat="server" OnItemDataBound="BindLunsListItemDataBound" OnItemCreated="lvLunsList_ItemCreated" DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed">
                                                    <thead>
                                                        <tr>
                                                            <th style="width: 3%;">Sr. No.
                                                            </th>
                                                            <th style="width: 12%;">Filesystem Type
                                                            </th>
                                                            <th style="width: 12%;">Mount Point Name
                                                            </th>
                                                            <th style="width: 11%;">LV Name
                                                            </th>
                                                            <th style="width: 11%;">VG Name
                                                            </th>
                                                            <th style="width: 10%;">HDisk Name</th>
                                                            <th style="width: 10%;">PV ID</th>
                                                            <th>LUN ID</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>

                                                    <asp:Label runat="server" Visible="False" ID="lblLunId" Text='<%#Eval("Id")%>'></asp:Label>

                                                    <td style="width: 5.8%;" class="luns"><%#Container.DataItemIndex+1 %></td>

                                                    <td style="width: 9.8%;">
                                                        <%--<select id="ddl" runat="server" cssclass="selectpicker"
                                                            data-style="btn-default" style="width: 100%;">
                                                            <option value='1'>Redo</option>
                                                            <option value='2'>Archive</option>
                                                            <option value='3'>Datafile</option>
                                                            <option value='4'>Controlfile</option>
                                                            <option value='5'>Application</option>
                                                            <option value='6'>Other</option>

                                                        </select>--%>
                                                        <asp:DropDownList ID="ddlFileType" runat="server" Width="125"></asp:DropDownList>
                                                    </td>
                                                    <td style="width: 19.9%;"><span class="float-left">
                                                        <asp:TextBox ID="txtMountPointName" MaxLength="20" CssClass="form-control" Width="60%" runat="server"> </asp:TextBox>
                                                        <span></span><span></span></span>
                                                        <input type="button" value="Discover" class="btn btn-primary" />

                                                        <%--id='<%#GetItemId("groupA", Container.DataItemIndex)%>'--%><br />
                                                        <span class="float-left hide"></td>
                                                    <%-- id='<%#GetItemId("mountA", Container.DataItemIndex)%>' --%>
                                                    <td style='width: 13.2%;'>
                                                        <asp:TextBox ID="txtLVName" MaxLength="20" CssClass="form-control" Width="90%" runat="server"> </asp:TextBox>
                                                        <div class="spGroupA margin5" id="groupB" runat="server"></div>
                                                        <div class='spArchiveA margin-top15 hide' id="mountB" runat="server"></div>
                                                    </td>
                                                    <td style='width: 13.1%;'>
                                                        <asp:TextBox ID="txtVGName" MaxLength="20" CssClass="form-control" Width="90%" runat="server"> </asp:TextBox>
                                                        <div class="spGroupA margin5" id="groupC" runat="server"></div>
                                                        <div class='spArchiveA margin-top15 hide' id="mountC" runat="server"></div>
                                                    </td>
                                                    <td id="Td2" colspan="3" class="padding-none" runat="server" visible="true">
                                                        <asp:ListView ID="lvmanual" runat="server" InsertItemPosition="LastItem" OnItemInserting="lvmanual_ItemInserting"
                                                            OnItemEditing="lvmanual_ItemEditing" OnItemCanceling="lvmanual_ItemCanceling" OnItemUpdating="lvmanual_ItemUpdating" OnItemDeleting="lvmanual_ItemDeleting">
                                                            <LayoutTemplate>
                                                                <div class="luns-scroll ">
                                                                    <table class="table table-bordered small-controls margin-bottom-none">
                                                                        <tbody>
                                                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </LayoutTemplate>

                                                            <ItemTemplate>
                                                                <tr>
                                                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                    <td style="width: 28%;">
                                                                        <asp:Label ID="HDiskName" Text='<%# Eval("Hdisk") %>' runat="server"> </asp:Label>

                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <asp:Label ID="PVID" Text='<%# Eval("PVID") %>' runat="server"> </asp:Label>
                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <asp:Label ID="LUNID" Text='<%# Eval("LUNID") %>' runat="server"> </asp:Label>
                                                                    </td>

                                                                    <td>
                                                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                            ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                                    </td>
                                                                </tr>
                                                            </ItemTemplate>

                                                            <EditItemTemplate>
                                                                <tr>
                                                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                    <td style="width: 28%;">
                                                                        <%-- <asp:Label ID="HDiskName" Text='<%# Eval("Hdisk") %>' runat="server"> </asp:Label>
                                                                         <asp:TextBox ID="TextBox3" Text='<%# Eval("Hdisk") %>' MaxLength="4" Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>--%>
                                                                        <asp:TextBox ID="HDiskName" Text='<%# Eval("Hdisk") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <%--<asp:Label ID="PVID" Text='<%# Eval("PVID") %>' runat="server"> </asp:Label>--%>
                                                                        <asp:TextBox ID="PVID" Text='<%# Eval("PVID") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <%--<asp:Label ID="LUNID" Text='<%# Eval("LUNID") %>' runat="server"> </asp:Label>--%>
                                                                        <asp:TextBox ID="LUNID" Text='<%# Eval("LUNID") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>

                                                                    <td>

                                                                        <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                                                            ToolTip="Update" ImageUrl="../Images/icons/navigation-090.png" />
                                                                        <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                                            ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />

                                                                    </td>
                                                                </tr>
                                                            </EditItemTemplate>
                                                            <InsertItemTemplate>

                                                                <tr>
                                                                    <td style="width: 28%;">
                                                                        <asp:TextBox ID="HDiskName" Text='<%# Eval("Hdisk") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <asp:TextBox ID="PVID" Text='<%# Eval("PVID") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <asp:TextBox ID="LUNID" Text='<%# Eval("LUNID") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>

                                                                    <td>
                                                                        <asp:ImageButton ID="imgInsert" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                            ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                                    </td>
                                                                </tr>
                                                            </InsertItemTemplate>
                                                        </asp:ListView>

                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                        </asp:ListView>
                                    </asp:Panel>
                                    <asp:Panel runat="server" ID="pnlNativeLogsSummary" Visible="False">

                                        <div class="task padding-5">
                                            <h2 style="margin-bottom: 10px;">Native Log Shipping</h2>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 form-horizontal uniformjs">
                                                <div class="col-md-6">

                                                    <b style="font-size: 15px;">Production Server</b>
                                                </div>
                                                <div class="col-md-6">
                                                    <b style="font-size: 15px;">DR Server</b>
                                                </div>
                                            </div>
                                        </div>
                                        <hr />
                                        <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                                            <ContentTemplate>
                                                <div class="row">
                                                    <div class="col-md-12 form-horizontal uniformjs">
                                                        <div class="col-md-6">

                                                            <div class="form-group">
                                                                <label class="col-md-4 control-label">
                                                                    Backup Folder Name
                                                                </label>
                                                                <div class="col-md-8">
                                                                    <asp:TextBox ID="txtBackup" runat="server" Width="55%" Enabled="false" CssClass="form-control"></asp:TextBox><span>*</span><span></span>
                                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator17" runat="server" CssClass="error" ErrorMessage="Enter Backup" ControlToValidate="txtBackup"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </div>
                                                            <div class="form-group">
                                                                <label class="col-md-4 control-label">
                                                                    Backup Network Path
                                                                </label>
                                                                <div class="col-md-8">
                                                                    <asp:TextBox ID="txtBackupNetwork" runat="server" Width="55%" Enabled="false" CssClass="form-control"></asp:TextBox><span>*</span><span></span>
                                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator19" runat="server" CssClass="error" ErrorMessage="Enter BackupNetwork" ControlToValidate="txtBackupNetwork"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="col-md-4 control-label">
                                                                    Restore Folder
                                                                </label>
                                                                <div class="col-md-8">
                                                                    <asp:TextBox ID="txtRestore" runat="server" Width="55%" Enabled="false" CssClass="form-control"></asp:TextBox><span>*</span><span></span>
                                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator18" runat="server" CssClass="error" ErrorMessage="Enter Restore" ControlToValidate="txtRestore"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </div>
                                                            <div class="form-group">
                                                                <label class="col-md-4 control-label">
                                                                    Restore Network Path
                                                                </label>
                                                                <div class="col-md-8">
                                                                    <asp:TextBox ID="txtRestoreNetwork" runat="server" Width="55%" Enabled="false" CssClass="form-control"></asp:TextBox><span>*</span><span></span>
                                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator20" runat="server" CssClass="error" ErrorMessage="Enter RestoreNetwork" ControlToValidate="txtRestoreNetwork"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </ContentTemplate>
                                        </asp:UpdatePanel>

                                        <hr />
                                        <table class="table table-striped table-bordered table-condensed" width="100%">
                                            <thead>
                                                <tr>
                                                    <th colspan="2">Job Interval Time
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td style="width: 17.5%">Backup
                                                    </td>
                                                    <td>
                                                        <asp:TextBox ID="txtbackuptime" runat="server" CssClass="form-control"></asp:TextBox>
                                                        Min
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator14" ControlToValidate="txtbackuptime"
                                                            Display="Dynamic" runat="server" CssClass="error" ErrorMessage="Insert BackUp Interval Time"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="revPhone1" runat="server" ControlToValidate="txtbackuptime"
                                                            ValidationGroup="dbConfig" ErrorMessage="Numbers only" ValidationExpression="[0-9]{1,4}"></asp:RegularExpressionValidator>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 17.5%">Copy
                                                    </td>
                                                    <td>
                                                        <asp:TextBox ID="txtcopytime" runat="server" CssClass="form-control"></asp:TextBox>
                                                        Min
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator15" ControlToValidate="txtcopytime" CssClass="error"
                                                            Display="Dynamic" runat="server" ErrorMessage="Insert Copy Interval Time"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ControlToValidate="txtcopytime"
                                                            CssClass="error" ErrorMessage="Max 4 digit allowed" ValidationExpression="[0-9]{1,4}"></asp:RegularExpressionValidator>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 17.5%">Restore
                                                    </td>
                                                    <td>
                                                        <asp:TextBox ID="txtrestoretime" runat="server" CssClass="form-control"></asp:TextBox>
                                                        Min
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator16" ControlToValidate="txtrestoretime"
                                                            Display="Dynamic" runat="server" CssClass="error" ErrorMessage="Insert Restore Interval Time"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator2" runat="server" ControlToValidate="txtrestoretime"
                                                            ErrorMessage="Max 4 digit allowed" ValidationExpression="[0-9]{1,4}" CssClass="error"></asp:RegularExpressionValidator>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <span id="divsplchar" class="error" style="display: none;">* No Special Character</span>
                                    </asp:Panel>
                                    <asp:Panel runat="server" ID="pnlArchiveRedo" Visible="False">
                                        <div class="task padding-5">

                                            <h4>Archive Redo</h4>
                                        </div>
                                        <div class="form paddingLR margin-top25">
                                            <fieldset class="float-left grid-23 margin-right">
                                                <div class="row">
                                                    <div class="col-md-12 form-horizontal uniformjs">

                                                        <div class="col-md-6">
                                                            <div class="widget">
                                                                <div class="widget-head" style="background-color: #357EBD ! important; background-image: none ! important; color: #FFFFFF ! important;">
                                                                    <span style="padding-left: 10px;"><b>Archive Info</b></span>
                                                                </div>
                                                                <div class="widget-body">
                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">VG Name <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtArchVGName" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator245" runat="server" CssClass="error" ErrorMessage="Enter VG Name" ControlToValidate="txtArchVGName" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter VG Name</span>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">Mount Point(s) <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtArchMountPoint" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator246" runat="server" CssClass="error" ErrorMessage="Enter Mount Point" ControlToValidate="txtArchMountPoint" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter VG Name</span>
                                                                            <a title="Mount Point(s)">
                                                                                <img src="../Images/icons/icon-tooltipQ.png" class="vertical-align" /></a>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">Device Type <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:DropDownList ID="ddlarchdevicetype" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default">
                                                                                <asp:ListItem runat="server">Lun Numbers</asp:ListItem>
                                                                                <asp:ListItem runat="server">Disks</asp:ListItem>
                                                                            </asp:DropDownList>
                                                                            <span class="hide">Select Lun Numbers</span>
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator247" CssClass="error" runat="server" ErrorMessage="Select Lun Numbers"
                                                                                ControlToValidate="ddlarchdevicetype" InitialValue="000" Display="Dynamic"></asp:RequiredFieldValidator>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">HUR/ True Copy Source <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtArchHurTrueCopysource" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            P-VOL
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator248" runat="server" CssClass="error" ErrorMessage="Enter source" ControlToValidate="txtArchHurTrueCopysource" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter  source</span>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">Shadow Image Production <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtArchShadowImagePR" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator289" runat="server" CssClass="error" ErrorMessage="Enter Shadow ImagePR" ControlToValidate="txtArchShadowImagePR" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter Shadow ImagePR</span>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">Target <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtArchTarget" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            S-VOL
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator244" runat="server" CssClass="error" ErrorMessage="Enter  Target" ControlToValidate="txtArchTarget" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter  Target</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">

                                                            <div class="widget">
                                                                <div class="widget-head" style="background-color: #357EBD ! important; background-image: none ! important; color: #FFFFFF ! important;">
                                                                    <span style="padding-left: 10px;"><b>Redo Info</b></span>
                                                                </div>
                                                                <div class="widget-body">
                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">VG Name <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtRedoVGName" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator24" runat="server" CssClass="error" ErrorMessage="Enter VG Name" ControlToValidate="txtRedoVGName" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter VG Name</span>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">Mount Point(s) <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtRedoMountPoint" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            <a title="Mount Point(s)">
                                                                                <img src="../Images/icons/icon-tooltipQ.png" class="vertical-align" /></a>
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator28" runat="server" CssClass="error" ErrorMessage="Enter Mount Point" ControlToValidate="txtRedoMountPoint" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter Mount Point</span>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">Device Type <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:DropDownList ID="ddlRedoDevicetype" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default">
                                                                                <asp:ListItem runat="server">Lun Numbers</asp:ListItem>
                                                                                <asp:ListItem runat="server">Disks</asp:ListItem>
                                                                            </asp:DropDownList>
                                                                            <span class="hide">Select Lun Numbers</span>
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator29" CssClass="error" runat="server" ErrorMessage="Select Lun Numbers"
                                                                                ControlToValidate="ddlRedoDevicetype" InitialValue="000" Display="Dynamic"></asp:RequiredFieldValidator>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">HUR/ True Copy Source <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtRedohurtruecopysource" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            P-VOL
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator32" runat="server" CssClass="error" ErrorMessage="Enter source" ControlToValidate="txtRedohurtruecopysource" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter source</span>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">Shadow Image Production <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtRedoshadowimagePR" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator30" runat="server" CssClass="error" ErrorMessage="Enter shadow imagePR" ControlToValidate="txtRedoshadowimagePR" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter  shadow imagePR</span>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label class="col-md-3 control-label">Target <span class="inactive">*</span></label>
                                                                        <div class="col-md-9">
                                                                            <asp:TextBox ID="txtRedoTarget" runat="server" CssClass="form-control"></asp:TextBox>
                                                                            S-VOL
                                                                             <asp:RequiredFieldValidator ID="RequiredFieldValidator31" runat="server" CssClass="error" ErrorMessage="Enter Target" ControlToValidate="txtRedoTarget" Display="Dynamic" EnableClientScript="false"></asp:RequiredFieldValidator>
                                                                            <span id="clientValidationMessage" style="color: red; display: none;">Enter Target</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                        </div>
                                        <div class="clear"></div>
                                        <hr class="separator" />

                                    </asp:Panel>
                                    <asp:Panel runat="server" ID="pnlCgSummary" Visible="False">
                                        <div class="task padding-5">
                                            <%--<span class="number">3</span>--%>
                                            <h4>Production Server Filesystem Information for AIX Operating System</h4>
                                        </div>
                                        <asp:ListView ID="lvCGlist" runat="server" OnItemDataBound="BindCGListItemDataBound" OnItemCreated="lvlvCGlist_ItemCreated" DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed">
                                                    <thead>
                                                        <tr>
                                                            <th style="width: 3%;">Sr. No.
                                                            </th>
                                                            <th style="width: 12%;">CG Name
                                                            </th>
                                                            <%-- <th style="width: 12%;">Mount Point Name
                                                            </th>--%>
                                                            <th style="width: 11%;">PRVolume Name
                                                            </th>
                                                            <th style="width: 11%;">DRVolume Name
                                                            </th>
                                                            <%-- <th style="width: 10%;">HDisk Name</th>
                                                            <th style="width: 10%;">PV ID</th>--%>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>

                                                    <asp:Label runat="server" Visible="False" ID="lblCGId" Text='<%#Eval("Id")%>'></asp:Label>

                                                    <td style="width: 5.8%;" class="luns"><%#Container.DataItemIndex+1 %></td>

                                                    <td style="width: 9.8%;">
                                                        <%--<select id="ddl" runat="server" cssclass="selectpicker"
                                                            data-style="btn-default" style="width: 100%;">
                                                            <option value='1'>Redo</option>
                                                            <option value='2'>Archive</option>
                                                            <option value='3'>Datafile</option>
                                                            <option value='4'>Controlfile</option>
                                                            <option value='5'>Application</option>
                                                            <option value='6'>Other</option>

                                                        </select>--%>
                                                        <asp:DropDownList ID="ddlCGName" runat="server" Width="125"></asp:DropDownList>
                                                    </td>

                                                    <%-- id='<%#GetItemId("mountA", Container.DataItemIndex)%>' --%>
                                                    <%--<td style='width: 13.2%;'>
                                                        <asp:TextBox ID="txtLVName" MaxLength="20" CssClass="form-control" Width="90%" runat="server"> </asp:TextBox>
                                                        <div class="spGroupA margin5" id="groupB" runat="server"></div>
                                                        <div class='spArchiveA margin-top15 hide' id="mountB" runat="server"></div>
                                                    </td>--%>
                                                    <%--<td style='width: 13.1%;'>
                                                        <asp:TextBox ID="txtVGName" MaxLength="20" CssClass="form-control" Width="90%" runat="server"> </asp:TextBox>
                                                        <div class="spGroupA margin5" id="groupC" runat="server"></div>
                                                        <div class='spArchiveA margin-top15 hide' id="mountC" runat="server"></div>
                                                    </td>--%>
                                                    <td id="Td2" colspan="3" class="padding-none" runat="server" visible="true">
                                                        <asp:ListView ID="lvCGmanual" runat="server" InsertItemPosition="LastItem" OnItemInserting="lvCGmanual_ItemInserting" OnItemEditing="lvCGmanual_ItemEditing"
                                                            OnItemCanceling="lvCGmanual_ItemCanceling" OnItemUpdating="lvCGmanual_ItemUpdating" OnItemDeleting="lvCGmanual_ItemDeleting">
                                                            <LayoutTemplate>
                                                                <div class="luns-scroll ">
                                                                    <table class="table table-bordered small-controls margin-bottom-none">
                                                                        <tbody>
                                                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </LayoutTemplate>

                                                            <ItemTemplate>
                                                                <tr>
                                                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                    <td style="width: 28%;">
                                                                        <asp:Label ID="PRVolumeName" Text='<%# Eval("PRVolumeName") %>' runat="server"> </asp:Label>

                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <asp:Label ID="DRVolumeName" Text='<%# Eval("DRVolumeName") %>' runat="server"> </asp:Label>
                                                                    </td>
                                                                    <%--  <td style="width: 28%;">
                                                                        <asp:Label ID="CGID" Text='<%# Eval("CGID") %>' runat="server"> </asp:Label>
                                                                    </td>--%>

                                                                    <td>
                                                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                            ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                                    </td>
                                                                </tr>
                                                            </ItemTemplate>

                                                            <EditItemTemplate>
                                                                <tr>
                                                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                    <td style="width: 28%;">
                                                                        <%-- <asp:Label ID="HDiskName" Text='<%# Eval("Hdisk") %>' runat="server"> </asp:Label>
                                                                         <asp:TextBox ID="TextBox3" Text='<%# Eval("Hdisk") %>' MaxLength="4" Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>--%>
                                                                        <asp:TextBox ID="PRVolumeName" Text='<%# Eval("PRVolumeName") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <%--<asp:Label ID="PVID" Text='<%# Eval("PVID") %>' runat="server"> </asp:Label>--%>
                                                                        <asp:TextBox ID="DRVolumeName" Text='<%# Eval("DRVolumeName") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>


                                                                    <td>

                                                                        <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                                                            ToolTip="Update" ImageUrl="../Images/icons/navigation-090.png" />
                                                                        <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                                            ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />

                                                                    </td>
                                                                </tr>
                                                            </EditItemTemplate>
                                                            <InsertItemTemplate>

                                                                <tr>
                                                                    <td style="width: 28%;">
                                                                        <asp:TextBox ID="PRVolumeName" Text='<%# Eval("PRVolumeName") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>
                                                                    <td style="width: 28%;">
                                                                        <asp:TextBox ID="DRVolumeName" Text='<%# Eval("DRVolumeName") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>
                                                                    <%-- <td style="width: 28%;">
                                                                        <asp:TextBox ID="LUNID" Text='<%# Eval("LUNID") %>' Width="90%" CssClass="form-control" runat="server"> </asp:TextBox>
                                                                    </td>--%>

                                                                    <td>
                                                                        <asp:ImageButton ID="imgInsert" runat="server" CommandName="Insert" AlternateText="Insert"
                                                                            ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                                    </td>
                                                                </tr>
                                                            </InsertItemTemplate>
                                                        </asp:ListView>

                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                        </asp:ListView>
                                    </asp:Panel>

                                </asp:WizardStep>
                            </WizardSteps>
                        </asp:Wizard>
                    </div>
                </div>

            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
