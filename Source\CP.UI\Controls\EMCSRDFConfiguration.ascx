﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EMCSRDFConfiguration.ascx.cs"
    Inherits="CP.UI.EMCSRDFConfiguration" %>
<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>


<asp:UpdatePanel ID="upnlGlobalMirror" runat="server" UpdateMode="Conditional">
    <ContentTemplate>
        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-head">
                    <h4 class="heading">EMC Management Console</h4>
                </div>
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-replication">
                            Select Server (SymCLI Server)<span class="inactive">*</span>
                        </label>
                        <div class="col-md-9">
                            <asp:DropDownList ID="ddlServer" runat="server" AutoPostBack="true"
                                OnSelectedIndexChanged="DdlServerSelectedIndexChanged" CssClass="selectpicker col-replication-dropdown" data-style="btn-default">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvddlServer" runat="server" ControlToValidate="ddlServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select Server"></asp:RequiredFieldValidator>
                            <asp:Label ID="lblServer" runat="server" ForeColor="Red"></asp:Label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            Server Hostname
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDSCLIHostname" CssClass="form-control" Style="width: 49% !important;" Enabled="False" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            Server IP
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDSCLIServerIP" CssClass="form-control" Style="width: 49% !important;" Enabled="False" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            SSH User ID
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSSHUserID" Enabled="False" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            SSH Password
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSSHPassword" CssClass="form-control" Enabled="False" Style="width: 49% !important;" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-head">
                    <h4 class="heading">Discovery info</h4>
                </div>
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-replication">
                            Group Name <span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDGName" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtDGName" CssClass="error"
                                Display="Dynamic" ErrorMessage="Please Enter Group Name"></asp:RequiredFieldValidator>
                            <asp:Button ID="btnDiscover" CssClass="btn btn-primary" Width="20%" runat="server" CausesValidation="true"
                                Text="Discover & Save" OnClick="BtnDiscoverClick" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            DG's Type</label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDGType" CssClass="form-control" Style="width: 49% !important;" Enabled="True" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            DG's Symmetrix ID
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDGSymmetrixID" CssClass="form-control" Style="width: 49% !important;" Enabled="True" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            Remote Symmetrix ID</label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtRemoteSym" CssClass="form-control" Style="width: 49% !important;" Enabled="True" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            RDF (RA) Group Number</label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtRDFGroupNumber" CssClass="form-control" Style="width: 49% !important;" Enabled="True" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div id="grdWithScroll" style="">
                        <asp:ListView ID="lvdevicelist" runat="server">
                            <LayoutTemplate>
                                <table class="table font no-bottom-margin" width="100%">
                                    <thead>
                                        <tr>
                                            <th style="width: 30%;">Device Name
                                            </th>
                                            <th style="width: 30%;">Source (R1)
                                            </th>
                                            <th>Target (R2)
                                            </th>
                                        </tr>
                                    </thead>
                                </table>
                                <div class="parallelScroll" id="jScrollbar">
                                    <table class="table font" width="100%">
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </table>
                                </div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td style="width: 31%;">
                                        <asp:Label ID="lblId" runat="server" Visible="false" Text='<%# Eval("Id") %>'></asp:Label>
                                        <asp:Label ID="DeviceName" runat="server" Text='<%# Eval("LogicalDevice") %>'>
                                        </asp:Label>
                                    </td>
                                    <td style="width: 31%;">
                                        <asp:Label ID="Source" runat="server" Text='<%# Eval("SourceR1Invtracks") %>'> </asp:Label>
                                    </td>
                                    <td>
                                        <asp:Label ID="Target" runat="server" Text='<%# Eval("TargetR2Invtracks") %>'></asp:Label>
                                    </td>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions row">

            <div class="col-lg-7" style="margin-left: 37%;">
                <asp:Button ID="btnSave" runat="server" CssClass="btn btn-primary" Width="20%" OnClick="BtnSaveClick"
                    Text="Save" CausesValidation="true" />
                <asp:Button ID="btnCancel" runat="server" CssClass="btn btn-default" Width="20%"
                    OnClick="BtnCancelClick" Text="Cancel" CausesValidation="false" />
            </div>
        </div>
    </ContentTemplate>
    <Triggers>
        <asp:AsyncPostBackTrigger ControlID="ddlServer" EventName="SelectedIndexChanged" />
        <asp:AsyncPostBackTrigger ControlID="btnDiscover" EventName="Click" />
    </Triggers>
</asp:UpdatePanel>