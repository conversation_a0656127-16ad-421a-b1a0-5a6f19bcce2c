﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
//using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RPForVMMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class RPForVMMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int Id { get; set; }
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string ConsistencyGroupId { get; set; }
        [DataMember]
        public string ConsistencyGroupName { get; set; }
        [DataMember]
        public String PRClusterIP { get; set; }
        [DataMember]
        public string PRClusterName { get; set; }
        [DataMember]
        public string PRClusterId { get; set; }
        [DataMember]
        public string PRReplicaCopyName { get; set; }
        [DataMember]
        public string DRClusterIP { get; set; }
        [DataMember]
        public string DRClusterName { get; set; }
        [DataMember]
        public string DRClusterId { get; set; }
        [DataMember]
        public string DRReplicaCopyName { get; set; }
        [DataMember]
        public string CGReplnTransferStatus { get; set; }

        [DataMember]
        public string DataLag { get; set; }

        [DataMember]
        public string CreateDate { get; set; }

        [DataMember]
        public string UpdateDate { get; set; }


        public string InfraObjectName
        {
            get;
            set;
        }
        public string Server
        {
            get;
            set;
        }
        #endregion Properties

    }
}
