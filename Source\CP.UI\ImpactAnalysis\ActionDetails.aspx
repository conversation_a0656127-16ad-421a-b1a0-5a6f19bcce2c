﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ActionDetails.aspx.cs" Inherits="CP.UI.ImpactAnalysis.ActionDetails" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title runat="server" id="usefortitle"></title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
     <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/modernizr.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/ie.prototype.polyfill.js"></script>
    <script src="../Script/html5shiv.js"></script>
    <script type="text/javascript" src="../Script/jquery-ui.min.js "></script>
    <style type="text/css">
        h4 span {
            font-style: normal;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <telerik:RadScriptManager runat="server" ID="RadScriptManager1" />
        <div class="innerLR">

            <asp:Panel ID="pnl_1_FailActionDetails" runat="server">
                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none">
                    <div class="widget-body">

                        <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <%--<h4>Action Failure Statistics -
                                    <asp:Label ID="lblComponent" runat="server" Text=""></asp:Label></h4>--%>

                                <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <asp:ListView ID="lvBIAallhumanintervensionAction" runat="server" OnPreRender="lvBIAallhumanintervensionAction_PreRender"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="b-12" style="width: 27%;">Action Name
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">Run
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">Fail
                                                            </th>
                                                            <th style="width: 10%;">Skip
                                                            </th>
                                                            <th style="width: 10%;">Abort
                                                            </th>
                                                            <th style="width: 10%;">Retry
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 15%;">% Fail
                                                            </th>
                                                            <%-- <th style="width: 10%;">Skip Percentage
                                                                                            </th>
                                                                                            <th style="width: 10%;">Abort Percentage
                                                                                            </th>
                                                                                            <th style="width: 10%;">Retry Percentage
                                                                                            </th>--%>
                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="bl_12" runat="server" Text='<%# Eval("Actionname") %>' ToolTip='<%# Eval("Actionname") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="b_12" runat="server" Text='<%# Eval("Totalrun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="b_12" runat="server" Text='<%# Eval("AllFailedCount") %>' />
                                                    </td>
                                                    <td style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Skip") %>' />
                                                    </td>
                                                    <td style="width: 10%;">
                                                        <asp:Label ID="Label1" runat="server" Text='<%# Eval("Abort") %>' />
                                                    </td>
                                                    <td style="width: 10%;">
                                                        <asp:Label ID="Label6" runat="server" Text='<%# Eval("Retry") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 15%;">
                                                        <asp:Label ID="Label13" CssClass="b_12" runat="server" Text='<%# Eval("AllPercentage") %>' />
                                                    </td>

                                                    <%-- <td style="width: 10%;">
                                                                                        <asp:Label ID="Label9" runat="server" Text='<%# Eval("Skippercentage") %>' />
                                                                                    </td>
                                                                                    <td style="width: 10%;">
                                                                                        <asp:Label ID="Label10" runat="server" Text='<%# Eval("Totalrun") %>' />
                                                                                    </td>--%>
                                                    <%-- <td style="width: 10%;">
                                                                                        <asp:Label ID="Label11" runat="server" Text='<%# Eval("Abortpercentage") %>' />
                                                                                    </td>
                                                                                    <td style="width: 10%;">
                                                                                        <asp:Label ID="Label12" runat="server" Text='<%# Eval("Retrypercentage") %>' />
                                                                                    </td>--%>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin text-center" style="height: 127px;">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="nrf" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager5" runat="server" PagedControlID="lvBIAallhumanintervensionAction">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager6" runat="server" PagedControlID="lvBIAallhumanintervensionAction" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                    </div>

                </div>
            </asp:Panel>
            <asp:Panel ID="pnl_2_ActionFailuareStatsDetails" runat="server">
                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none">
                    <div class="widget-body">

                        <asp:Label ID="lblMessageActionFailuareStats" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <%--<h4>Action Failuare Stats-Human Interventions -
                                    <asp:Label ID="lblComponentActionFailuareStats" runat="server" Text=""></asp:Label></h4>--%>

                                <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <asp:ListView ID="lvActionFailureStatByStatus" runat="server" OnPagePropertiesChanged="lvActionFailureStatByStatus_PagePropertiesChanged" DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="b-12" style="width: 27%;">Action Name
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">Run
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">Fail
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">% Fail
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">Success
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">% Success
                                                            </th>
                                                        </tr>
                                                    </thead>




                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="bl_12" runat="server" Text='<%# Eval("Actionname") %>' ToolTip='<%# Eval("Actionname") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label6" runat="server" Text='<%# Eval("Total") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="b_12" runat="server" Text='<%# Eval("Failed") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="b_12" runat="server" Text='<%# Eval("FailePercentage") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label16" CssClass="b_12" runat="server" Text='<%# Eval("Success") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label17" CssClass="b_12" runat="server" Text='<%# Eval("SuccessPercentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin text-center" style="height: 127px;">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="nrf" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager3" runat="server" PagedControlID="lvActionFailureStatByStatus">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager4" runat="server" PagedControlID="lvActionFailureStatByStatus" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                    </div>

                </div>
            </asp:Panel>
            <asp:Panel ID="pnl_3_ActionFailureStatsWorkflowCategory" runat="server">
                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none">
                    <div class="widget-body">

                        <asp:Label ID="lblMessageActionFailureStatsWorkflowCategory" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <%--  <h4>Action Failure Stats Workflow Category - 
                                    <asp:Label ID="lblComponentActionFailureStatsWorkflowCategory" runat="server" Text=""></asp:Label></h4>--%>

                                <asp:UpdatePanel ID="UpdatePanel5" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <asp:ListView ID="lvBIAFailurebySOSB" runat="server" OnPagePropertiesChanged="lvBIAFailurebySOSB_PagePropertiesChanged" DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="b-12" style="width: 27%;">Action Name
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">Fail
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">% Fail
                                                            </th>

                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="bl_12" runat="server" Text='<%# Eval("Actionname") %>' ToolTip='<%# Eval("Actionname") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="b_12" runat="server" Text='<%# Eval("AllFailedCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="b_12" runat="server" Text='<%# Eval("AllPercentage") %>' />
                                                    </td>

                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin text-center" style="height: 127px;">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="nrf" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager9" runat="server" PagedControlID="lvBIAFailurebySOSB">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager10" runat="server" PagedControlID="lvBIAFailurebySOSB" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                    </div>

                </div>
            </asp:Panel>
            <asp:Panel ID="pnl_2_ActiExecutionStatisticsDetails" runat="server">
                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none">
                    <div class="widget-body">

                        <asp:Label ID="Label3" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <%-- <h4>Action Execution Statastics -
                                    <asp:Label ID="Label4" runat="server" Text=""></asp:Label></h4>--%>

                                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <asp:ListView ID="lvBIAActiexecutionstatistics" runat="server" OnPagePropertiesChanged="lvBIAActiexecutionstatistics_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="b-12" style="width: 27%;">Action Name
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 10%;">Run
                                                            </th>
                                                            <th class="b-12 text-center" style="width: 15%;">Success
                                                            </th>
                                                            <th style="width: 10%;">Skip
                                                            </th>
                                                            <th style="width: 10%;">Abort
                                                            </th>
                                                            <th style="width: 10%;">Retry
                                                            </th>
                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="bl_12" runat="server" Text='<%# Eval("Actionname") %>' ToolTip='<%# Eval("Actionname") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="b_12" runat="server" Text='<%# Eval("Total") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 15%;">
                                                        <asp:Label ID="Label13" CssClass="b_12" runat="server" Text='<%# Eval("Success") %>' />
                                                    </td>
                                                    <td style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Skip") %>' />
                                                    </td>
                                                    <td style="width: 10%;">
                                                        <asp:Label ID="Label1" runat="server" Text='<%# Eval("Abort") %>' />
                                                    </td>
                                                    <td style="width: 10%;">
                                                        <asp:Label ID="Label6" runat="server" Text='<%# Eval("Retry") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin text-center" style="height: 127px;">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="nrf" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvBIAActiexecutionstatistics">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvBIAActiexecutionstatistics" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl123" runat="server">
                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none">
                    <div class="widget-body">

                        <asp:Label ID="Label2" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                            </div>
                        </div>
                    </div>

                </div>
            </asp:Panel>
        </div>
    </form>
</body>
</html>
