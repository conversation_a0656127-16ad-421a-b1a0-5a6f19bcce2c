﻿<%@ Page Title="Continuity Patrol-Welcome Wizard" Language="C#" MasterPageFile="~/Master/BcmsLogin.Master" AutoEventWireup="true" CodeBehind="CompanyInfoPage.aspx.cs" Inherits="CP.UI.CompanyInfoPage" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="Script/jquery-migrate.min.js"></script>
    <script src="Script/less.min.js"></script>
    <link href="App_Themes/CPTheme/core/font-icon/font-awesome.min.css" rel="stylesheet" />
    <script type="text/javascript">
        function Check(textBox, maxLength) {
            if (textBox.value.length > maxLength) {
                alert("Max characters allowed are " + maxLength);
                textBox.value = textBox.value.substr(0, maxLength);
            }
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="container-fluid loginPageBg">
        <ul id="divClass" runat="server" visible="False" class="notyfy_container i-am-new">
            <li class="notyfy_wrapper notyfy_success">
                <div id="notyfy_1155564924086075600" class="notyfy_bar">
                    <div class="notyfy_message">
                        <span class="notyfy_text">
                            <strong>
                                <asp:Literal ID="FailureText" Text="Error message" runat="server" EnableViewState="False"></asp:Literal></strong>
                        </span>
                    </div>
                </div>
            </li>
        </ul>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-8">
                    <div id="login" class="innerLR">
                        <div class="row margin-none center">
                            <div class="col-md-12 col-md-push-2 margin-top90  ">
                                <img src="Images/ContinuityPatrol_logo_black1.png" class="center" alt="Continuity Patrol" />
                                <asp:Label ID="lblVersion" runat="server" Text=""></asp:Label>
                            </div>
                            <div class="clearfix">&nbsp;</div>
                            <div class="col-md-10 col-md-push-3 margin-top">

                                <asp:Panel ID="pnlCompanyInfo" runat="server" Visible="True" class="widget box-shadow-none">

                                    <div class="widget-head">
                                        <h3 class="heading">

                                            <span class="fa fa-building-o text-primary text-medium"></span>
                                            <asp:Label ID="lblheader" CssClass="text-bold" runat="server" Text="Company Information"></asp:Label>
                                        </h3>
                                    </div>
                                    <div class="widget-body text-left">
                                        <div class="row">
                                            <div class="col-md-12 form-horizontal uniformjs">
                                                <div class="form-group margin-top">
                                                    <label class="col-md-3">Company Name <span class="inactive">*</span></label>
                                                    <div>
                                                        <asp:TextBox ID="txtcompanyName" runat="server" CssClass="form-control" onChange="javascript:Check(this, 100);" />
                                                        <a title=" a-z A-Z 0-9 * Only & _ @ .-special characters allowed."
                                                            class="error">
                                                            <img class="vertical-align" src="Images/icons/icon-tooltipQ.png"></a>
                                                        <asp:RequiredFieldValidator ID="rfvName" CssClass="error" runat="server" ErrorMessage="Enter Company Name"
                                                            Display="Dynamic" ControlToValidate="txtcompanyName"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="regexp" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtcompanyName" ErrorMessage="Invalid Characters"
                                                            ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3">Display Name <span class="inactive">*</span></label><div>
                                                        <asp:TextBox ID="txtdisName" runat="server" Placeholder="Like Ptechno,IBM,..etc" CssClass="form-control" />
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator4" CssClass="error" runat="server" ErrorMessage="Enter Display Name"
                                                            Display="Dynamic" ControlToValidate="txtdisName"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="revDisplayName" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtdisName" ErrorMessage="Invalid Characters"
                                                            ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>

                                                <div class="form-group" runat="server" id="divcomvalue">
                                                    <label class="col-md-3">Company Id<span class="inactive">*</span></label><div>
                                                        <asp:TextBox ID="txtCompanyId" runat="server" Placeholder="Company Id" CssClass="form-control" />
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" CssClass="error" runat="server" ErrorMessage="Enter Company Id"
                                                            Display="Dynamic" ControlToValidate="txtCompanyId"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <hr class="separator" />
                                        <div class="row">
                                            <div class="col-md-4">

                                                <span class="strong">Note:</span>&nbsp;<span class="inactive">*</span><span> Required
                            Fields</span>
                                            </div>
                                            <div class="col-md-8 text-right ">
                                                <asp:Button ID="btnNext" CssClass="btn btn-primary" runat="server" Text="Next" Width="32%" OnClick="btnSave_Click" />

                                            </div>
                                        </div>

                                    </div>
                                </asp:Panel>

                                <asp:Panel ID="pnlUserInfo" runat="server" Visible="False" class="widget box-shadow-none fadeInLeft animated">
                                    <div class="widget-head">
                                        <h3 class="heading">
                                            <img src="Images/user-icon.png">
                                            <asp:Label ID="Label1" CssClass="text-bold" runat="server" Text="User Information"></asp:Label>
                                        </h3>
                                    </div>
                                    <div class="widget-body text-left">
                                        <div class="row">
                                            <div class="col-md-12 form-horizontal uniformjs">
                                                <div class="form-group margin-top">
                                                    <label class="col-md-3">Login Name <span class="inactive">*</span></label>
                                                    <div>
                                                        <asp:TextBox ID="txtLoginName" runat="server" CssClass="form-control" />
                                                        <a title=" * Only  & _ @ . - special characters, Numeric & alpha numeric allowed."
                                                            class="error">
                                                            <img class="vertical-align" src="Images/icons/icon-tooltipQ.png"></a>
                                                        <asp:RequiredFieldValidator ID="rqdUsrID" runat="server" ControlToValidate="txtLoginName" CssClass="error"
                                                            ErrorMessage="Enter Login Name" Display="Dynamic"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="revLoginName" runat="server" ControlToValidate="txtLoginName" CssClass="error"
                                                            Display="Dynamic" ErrorMessage="Enter Correct Login Name" ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3">Password <span class="inactive">*</span></label><div>
                                                        <asp:TextBox ID="txtPassword" runat="server" autocomplete="off" TextMode="Password" CssClass="form-control" />
                                                        <a title="" id="passExp" runat="server"
                                                            class="error">
                                                            <asp:Image ID="img123" CssClass="vertical-align" runat="server" src="Images/icons/icon-tooltipQ.png" />
                                                        </a>
                                                        <asp:RequiredFieldValidator ID="rfvPwd" runat="server" ControlToValidate="txtPassword" CssClass="error"
                                                            ErrorMessage="Enter Password" Visible="true" Display="Dynamic"></asp:RequiredFieldValidator>
                                                        <asp:Label ID="lblErr" CssClass="error" runat="server" Visible="false"></asp:Label>
                                                        <asp:RegularExpressionValidator ID="revPwd" runat="server" ControlToValidate="txtPassword" CssClass="error"
                                                            ErrorMessage="Password isn't proper format" ValidationExpression="(?=^.{8,16}$)(?=(?:.*?\d){3})(?=.*[a-z]{4})(?=(?:.*?[A-Z]){1})(?=(?:.*?[!@#$%*()_+^&amp;}{:;?.]){1})(?!.*\s)[0-9a-zA-Z!@#$%*()_+^&amp;]*$"
                                                            Display="Dynamic"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <hr class="separator" />
                                        <div class="row">
                                            <div class="col-md-4">

                                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                            Fields</span>
                                            </div>
                                            <div class="col-md-8 text-right ">
                                                <asp:Button ID="btnSave" CssClass="btn btn-primary" runat="server" Text="Save" Width="32%" OnClick="btnSave_Click1" />

                                            </div>
                                        </div>

                                    </div>
                                </asp:Panel>
                            </div>

                        </div>
                    </div>
                </div>

            </div>
        </div>

    </div>

    <script type="text/javascript">
        $('[id$=divClass]').click(function () {
            $(this).fadeOut("slow");
        });

    </script>
</asp:Content>
