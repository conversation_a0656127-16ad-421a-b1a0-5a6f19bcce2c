﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="AccessManager.aspx.cs" Inherits="CP.UI.AccessManager" %>

<%@ Register Src="~/Controls/CustomRoleAccessManager.ascx" TagName="AccessmangerConfig" TagPrefix="uc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/jquery.combobox/style.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/jquery.cookie.js"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script src="../Script/jquery.combobox.js" type="text/javascript"></script>
    <script src="../Script/UserConfig.js" type="text/javascript"></script>
    <%--<script src="../Script/MaskedPassword.js"></script>--%>
    <script src="../Script/EncryptDecrypt.js"></script>
    <%--<script src="../Script/Login.js" type="text/javascript"></script>--%>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <style type="text/css">
        .RadTreeView_Outlook .rtChecked, .RadTreeView_Outlook .rtUnchecked, .RadTreeView_Outlook .rtIndeterminate {
            background-image: url(../Images/custom-chkbox-rdbtn-small-new.png) !important;
        }

        .RadTreeView .rtUnchecked {
            background-position: -15px -12px !important;
        }

        .RadTreeView .rtChecked {
            background-position: 1px 0px !important;
        }

        .RadTreeView .rtIndeterminate {
            background-position: -15px 0px !important;
        }

        .RadTreeView_Outlook {
            font: normal 13px/16px Segoe UI !important;
        }
    </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <h3>
                    <img src="../Images/icons/server.png" />
                    Access Manager</h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtHostName">
                                        User <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlusers" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlusers_SelectedIndexChanged"></asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvusers" runat="server" ControlToValidate="ddlusers" CssClass="error"
                                            InitialValue="0" Display="Dynamic" ErrorMessage="Please Select User"></asp:RequiredFieldValidator>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtHostName">
                                        Role <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlroles" runat="server" CssClass="chosen-select col-md-6" Enabled="true" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlroles_SelectedIndexChanged">
                                            <asp:ListItem Value="-- Select Role --"></asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvroles" runat="server" ControlToValidate="ddlroles" CssClass="error"
                                            InitialValue="0" Display="Dynamic" ErrorMessage="Please Select Role"></asp:RequiredFieldValidator>
                                    </div>
                                </div>

                                <div id="DivRoleSubType" class="form-group" runat="server" visible="false">
                                    <label class="col-md-3 control-label" for="txtHostName">
                                        Role SubType <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtRolesubtype" runat="server" class="form-control" AutoPostBack="true" OnTextChanged="txtRolesubtype_TextChanged"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvrolesubtype" runat="server" ControlToValidate="txtRolesubtype" CssClass="error" Display="Dynamic" ErrorMessage="Please Enter Role Sub Type">
                                        </asp:RequiredFieldValidator>
                                    </div>

                                </div>


                                <div class="form-group" style="display: none">
                                    <label class="col-md-3 control-label" for="cskgoup">
                                        Infraobject <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:Panel ID="Panel1" runat="server" ScrollBars="Vertical" Height="101px" class="padding pull-left"
                                            Width="48%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px" TabIndex="7">
                                            <asp:CheckBoxList ID="cskgoup" runat="server" AutoPostBack="true" OnSelectedIndexChanged="cskgoup_SelectedIndexChanged"></asp:CheckBoxList>
                                            <asp:Label ID="labelSDErrormessage" runat="server" CssClass="error" Visible="false"></asp:Label>
                                        </asp:Panel>

                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="cskgoup">
                                        Infraobject <span class="inactive"></span>
                                    </label>
                                    <div class="col-md-9">
                                        <div class="notifycheckscroll" style="padding: 5px; border: 1px solid #ccc; width: 48%; max-height: 120px;">
                                            <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional">
                                                <ContentTemplate>
                                                    <telerik:RadTreeView RenderMode="Lightweight" CausesValidation="false" ID="RadTreeView1" runat="server" CheckBoxes="True"
                                                        Skin="Outlook" TriStateCheckBoxes="true" CheckChildNodes="true" IsOptionElementsEnabled="True">
                                                        <%--TriStateCheckBoxes="true" CheckChildNodes="true"--%>
                                                        <Nodes>
                                                            <telerik:RadTreeNode Text="Assigned InfraObjects....." Expanded="true" Checkable="false" Enabled="false"></telerik:RadTreeNode>
                                                        </Nodes>
                                                        <%--<Nodes>
                                                <telerik:RadTreeNode Text="Software" Expanded="true">
                                                    <Nodes>
                                                        <telerik:RadTreeNode Text="Business &amp;amp; Office">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Database">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Networking">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Presentation">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Project Management">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Reports">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Spreadsheet">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Word Processing">
                                                        </telerik:RadTreeNode>
                                                    </Nodes>
                                                </telerik:RadTreeNode>
                                                <telerik:RadTreeNode Text="Books">
                                                    <Nodes>
                                                        <telerik:RadTreeNode Text="Arts">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Biographies">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Children's Books">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Computers &amp;amp; Internet">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Cooking">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="History">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Fiction">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Mystery">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Nonfiction">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Romance">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Science Fiction ">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Travel">
                                                        </telerik:RadTreeNode>
                                                    </Nodes>
                                                </telerik:RadTreeNode>
                                                <telerik:RadTreeNode Text="Music">
                                                    <Nodes>
                                                        <telerik:RadTreeNode Text="Alternative">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Blues">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Children's Music">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Classical">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Country">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Dance">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Folk ">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Hard Rock">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Jazz">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Soundtracks">
                                                        </telerik:RadTreeNode>
                                                    </Nodes>
                                                </telerik:RadTreeNode>
                                                <telerik:RadTreeNode Text="Movies">
                                                    <Nodes>
                                                        <telerik:RadTreeNode Text="Action">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Animation">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Classics">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Comedy">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Documentary">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Drama">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Horror">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Musicals">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Mystery">
                                                        </telerik:RadTreeNode>
                                                        <telerik:RadTreeNode Text="Westerns">
                                                        </telerik:RadTreeNode>
                                                    </Nodes>
                                                </telerik:RadTreeNode>
                                            </Nodes>--%>
                                                    </telerik:RadTreeView>
                                                </ContentTemplate>
                                            </asp:UpdatePanel>
                                        </div>
                                    </div>

                                </div>
                                <uc1:AccessmangerConfig ID="ucnAccessmangerConfiguration" runat="server" Visible="false" />
                            </div>
                        </div>
                    </div>
            </ContentTemplate>
        </asp:UpdatePanel>
        <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>


    </div>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }

        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
            $(".notifycheckscroll").mCustomScrollbar({
                axis: "y",

            });
        }
    </script>
</asp:Content>
