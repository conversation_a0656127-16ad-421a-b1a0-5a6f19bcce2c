﻿using System;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;

namespace CP.UI
{
    public partial class ReportManagement : BasePage
    {
        public override void PrepareView()
        {
            Utility.SelectMenu(Master, "Module7");
            ShowPage();
            if (!IsSuperAdmin)
            {
                ddlReport.Items.FindByValue("26").Enabled = false;
            }
            if (!LoggedInUser.InfraObjectAllFlag)
            {
                //ddlReport.Items.FindByValue("2").Enabled = false;
                // ddlReport.Items.FindByValue("3").Enabled = false;
                //ddlReport.Items.FindByValue("5").Enabled = false;
                // ddlReport.Items.FindByValue("6").Enabled = false;
                //ddlReport.Items.FindByValue("7").Enabled = false;
                //ddlReport.Items.FindByValue("8").Enabled = false;
                //ddlReport.Items.FindByValue("9").Enabled = false;
                //ddlReport.Items.FindByValue("12").Enabled = false;
                // ddlReport.Items.FindByValue("13").Enabled = false;
            }
        }

        protected void DdlReportSelectedIndexChanged(object sender, EventArgs e)
        {
            //Session["SReport"] = ddlReport.SelectedItem.ToString();
            ShowPage();
            ClearControl();
        }

        public void ClearControl()
        {
            TextBox textBox1 = (TextBox)ucDataLagReport.FindControl("txtstart");
            textBox1.Text = "";
            TextBox textBox2 = (TextBox)ucDataLagReport.FindControl("txtend");
            textBox2.Text = "";
            DropDownList ddl1 = (DropDownList)ucDataLagReport.FindControl("ddlGroup");
            if (ddl1 != null)
                ddl1.SelectedIndex = -1;

            TextBox textBox3 = (TextBox)ucGlobalMirrorReplication.FindControl("txtstart");
            textBox3.Text = "";
            TextBox textBox4 = (TextBox)ucGlobalMirrorReplication.FindControl("txtend");
            textBox4.Text = "";
            DropDownList ddl2 = (DropDownList)ucGlobalMirrorReplication.FindControl("ddlselectTime");
            if (ddl2 != null)
                ddl2.SelectedIndex = -1;

            TextBox textBox5 = (TextBox)FastcopyMonitorReport.FindControl("txtstart");
            textBox5.Text = "";
            TextBox textBox6 = (TextBox)FastcopyMonitorReport.FindControl("txtend");
            textBox6.Text = "";
            DropDownList ddl3 = (DropDownList)ucDataLagReport.FindControl("ddlAppnme");
            if (ddl3 != null)
                ddl3.SelectedIndex = -1;

            TextBox textBox7 = (TextBox)UserActivityDetailReport.FindControl("txtstart");
            textBox7.Text = "";
            TextBox textBox8 = (TextBox)UserActivityDetailReport.FindControl("txtend");
            textBox8.Text = "";

            TextBox textBox9 = (TextBox)ucSMSReport.FindControl("txtstart");
            textBox9.Text = "";
            TextBox textBox10 = (TextBox)ucSMSReport.FindControl("txtend");
            textBox10.Text = "";

            TextBox textBox11 = (TextBox)ucEmailReport.FindControl("txtstart");
            textBox11.Text = "";
            TextBox textBox12 = (TextBox)ucEmailReport.FindControl("txtend");
            textBox12.Text = "";

            DropDownList ddl4 = (DropDownList)ucParallelDROperationReport.FindControl("ddlParallelDrOperation");
            if (ddl4 != null)
                ddl4.SelectedIndex = -1;

            TextBox textBox13 = (TextBox)ucRPMonitor.FindControl("txtstart");
            textBox13.Text = "";
            TextBox textBox14 = (TextBox)ucRPMonitor.FindControl("txtend");
            textBox14.Text = "";

            DropDownList ddl5 = (DropDownList)ucGroupConfiguration.FindControl("ddlGroup");
            if (ddl5 != null)
                ddl5.SelectedIndex = -1;

            Label lbl24hrs = (Label)ucNewDataLag24Hours.FindControl("lblMsg");
            if (lbl24hrs != null)
            {
                lbl24hrs.Text = string.Empty;
                lbl24hrs.Visible = false;
            }

        }

        private void ShowPage()
        {
            ucDRReddynesslog.Visible = false;
            ucDRReaddynesssummary.Visible = false;

            ucImportCMDBReport.Visible = false;
            ucMultiserverprofilemoniReport.Visible = false;
            ucVMCenterReport.Visible = false;
            // ucMonitoringSerivesReport.Visible = false;
            ucApplicationDiscovery.Visible = false;
            ucDataLagReport.Visible = false;
            ucGroupConfiguration.Visible = false;
            ucGlobalMirrorReplication.Visible = false;
            ucMediationGroup.Visible = false;
            ucApplicationGroup.Visible = false;
            // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
            //globalmirrorFmation.Visible = false;
            ucGlobalMirrorCGFormation.Visible = false;
            ucDatalagReport24Hrs.Visible = false;
            ucNewDataLag24Hours.Visible = false;
            ucStatus24.Visible = false;
            //ucBusinessImpactAnalysis.Visible = false;
            //ucDRDrillReport.Visible = false;
            //ucNewStatus24.Visible = false;
            //ucDatalagChartForAllGroup.Visible = false;
            ucDatalagstatus.Visible = false;
            //ucApplication24Hrs.Visible = false;
            ucParallelDROperationReport.Visible = false;
            ucDRDrillExcelReport.Visible = false;
            //ApplicationSyncstatus.Visible = false;
            FastcopyMonitorReport.Visible = false;
            ucDRHealthReport.Visible = false;
            UserActivityDetailReport.Visible = false;
            AuditReport.Visible = false;
            SLAReport.Visible = false;
            ucApplicationdependancyReport.Visible = false;
            ucRPMonitor.Visible = false;
            ucGGReplicationReportHourly.Visible = false;
            ucDRReadyRPT.Visible = false;
            RTOReport.Visible = false;
            ucRPOSLADeviationReports.Visible = false;

            AlertReport.Visible = false;
            unLicenseUtilizationRPT.Visible = false;

            unPPDMRestoreExecution.Visible = false;
            unCMDBCompareReport.Visible = false;



            switch (ddlReport.SelectedValue)
            {
                case "16":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    var datalagRpt = (Panel)ucDataLagReport.FindControl("Pan1");
                    datalagRpt.Visible = false;
                    ucDataLagReport.Visible = true;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "13":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    var ucGrpconfig = (Panel)ucGroupConfiguration.FindControl("Pan1");
                    ucGrpconfig.Visible = false;
                    ucGroupConfiguration.Visible = true;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;
                   
                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "12":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    var globalmirorReprpt = (Panel)ucGlobalMirrorReplication.FindControl("Pan1");
                    globalmirorReprpt.Visible = false;
                    ucGlobalMirrorReplication.Visible = true;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;
                    ucRPOSLADeviationReports.Visible = false;
                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "14":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    var ucGrpsummary = (Panel)ucMediationGroup.FindControl("Pan1");
                    ucGrpsummary.Visible = false;
                    ucMediationGroup.Visible = true;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "3":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    var ucApplicgrp = (Panel)ucApplicationGroup.FindControl("Pan1");
                    ucApplicgrp.Visible = false;
                    ucApplicationGroup.Visible = true;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "11":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = true;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                //case "7":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    var ucDlg24Hrs = (Panel)ucDatalagReport24Hrs.FindControl("Pan1");
                //    ucDlg24Hrs.Visible = false;
                //    ucDatalagReport24Hrs.Visible = true;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    //ucDatalagChartForAllGroup.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucApplicationDiscovery.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;
                //    break;

                case "5":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    var newdatalag24Hrs = (Panel)ucNewDataLag24Hours.FindControl("Pan1");
                    newdatalag24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = true;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;
                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                //case "9":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    var ucText = (Panel)ucStatus24.FindControl("Pan1");
                //    ucText.Visible = false;
                //    ucStatus24.Visible = true;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    //ucDatalagChartForAllGroup.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    ucDRDrillExcelReport.Visible = false;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucApplicationDiscovery.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;
                //    break;

                //case "10":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = true;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    //ucDatalagChartForAllGroup.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    ucDRDrillExcelReport.Visible = false;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucApplicationDiscovery.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;
                //    break;

                //case "11":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = true;
                //    //ucNewStatus24.Visible = false;
                //    //ucDatalagChartForAllGroup.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    ucDRDrillExcelReport.Visible = false;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucApplicationDiscovery.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;
                //    break;

                ////case "12":
                ////    ucDataLagReport.Visible = false;
                ////    ucGroupConfiguration.Visible = false;
                ////    ucGlobalMirrorReplication.Visible = false;
                ////    ucMediationGroup.Visible = false;
                ////    ucApplicationGroup.Visible = false;
                ////    ucGlobalMirrorCGFormation.Visible = false;
                ////    ucDatalagReport24Hrs.Visible = false;
                ////    ucNewDataLag24Hours.Visible = false;
                ////    ucStatus24.Visible = false;
                ////    //ucBusinessImpactAnalysis.Visible = false;
                ////    //ucDRDrillReport.Visible = false;
                ////    //ucNewStatus24.Visible = true;
                ////    //var new24HrsStatus = (Panel)ucNewStatus24.FindControl("Pan1");
                ////    //new24HrsStatus.Visible = false;
                ////    //ucDatalagChartForAllGroup.Visible = false;
                ////    ucDatalagstatus.Visible = false;
                ////    ucApplication24Hrs.Visible = false;
                ////    ucParallelDROperationReport.Visible = false;
                ////    ucDRDrillExcelReport.Visible = false;
                ////    //ApplicationSyncstatus.Visible = false;
                ////    FastcopyMonitorReport.Visible = false;
                ////    ucDRHealthReport.Visible = false;
                ////   ucApplicationDiscovery.Visible = false;
                ////ucVMCenterReport.Visible = false;
                ////ucMultiserverprofilemoniReport.Visible = false;
                ////    break;

                ////case "13":
                ////ucDataLagReport.Visible = false;
                ////ucGroupConfiguration.Visible = false;
                ////ucGlobalMirrorReplication.Visible = false;
                ////ucMediationGroup.Visible = false;
                ////ucApplicationGroup.Visible = false;
                ////ucGlobalMirrorCGFormation.Visible = false;
                ////ucDatalagReport24Hrs.Visible = false;
                ////ucNewDataLag24Hours.Visible = false;
                ////ucStatus24.Visible = false;
                ////ucBusinessImpactAnalysis.Visible = false;
                ////ucDRDrillReport.Visible = false;
                ////ucNewStatus24.Visible = false;
                ////ucDatalagChartForAllGroup.Visible = true;
                //// ucApplicationDiscovery.Visible = false;
                ////ucVMCenterReport.Visible = false;
                ////ucMultiserverprofilemoniReport.Visible = false;
                ////break;

                //case "13":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    var datalagStatus = (Panel)ucDatalagstatus.FindControl("Pan1");
                //    datalagStatus.Visible = false;
                //    ucDatalagstatus.Visible = true;
                //    ucDRDrillExcelReport.Visible = false;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucApplicationDiscovery.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;
                //    break;

                ////case "14":
                ////    ucDataLagReport.Visible = false;
                ////    ucGroupConfiguration.Visible = false;
                ////    ucGlobalMirrorReplication.Visible = false;
                ////    ucMediationGroup.Visible = false;
                ////    ucApplicationGroup.Visible = false;
                ////    ucGlobalMirrorCGFormation.Visible = false;
                ////    ucDatalagReport24Hrs.Visible = false;
                ////    ucNewDataLag24Hours.Visible = false;
                ////    ucStatus24.Visible = false;
                ////    //ucBusinessImpactAnalysis.Visible = false;
                ////    //ucDRDrillReport.Visible = false;
                ////    //ucNewStatus24.Visible = false;
                ////    ucDatalagstatus.Visible = false;
                ////    ucParallelDROperationReport.Visible = false;
                ////    //var appstatusrpt = (Panel)ucApplication24Hrs.FindControl("Pan1");
                ////    //appstatusrpt.Visible = false;
                ////    //ucApplication24Hrs.Visible = true;
                ////    ucDRDrillExcelReport.Visible = false;
                ////    //ApplicationSyncstatus.Visible = false;
                ////    FastcopyMonitorReport.Visible = false;
                ////    ucDRHealthReport.Visible = false;
                //// ucApplicationDiscovery.Visible = false;
                //// ucVMCenterReport.Visible = false;
                //// ucMultiserverprofilemoniReport.Visible = false;
                ////    break;

                case "7":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    //var paralleldroperation = (Panel)ucParallelDROperationReport.FindControl("Pan1");
                    //paralleldroperation.Visible = false;
                    ucParallelDROperationReport.Visible = true;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;
                    ucRPOSLADeviationReports.Visible = false;
                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                //case "16":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    var drdrillrptExcel = (Panel)ucDRDrillExcelReport.FindControl("Pan1");
                //    drdrillrptExcel.Visible = false;
                //    ucDRDrillExcelReport.Visible = true;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;

                //    // ucApplicationDiscovery.Visible = false;
                //    break;

                //case "17":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    ucDRDrillExcelReport.Visible = false;
                //    //var applicationsync = (Panel)ApplicationSyncstatus.FindControl("Pan1");
                //    //applicationsync.Visible = false;
                //    //ApplicationSyncstatus.Visible = true;
                //    FastcopyMonitorReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //   ucApplicationDiscovery.Visible = false;
                //  ucVMCenterReport.Visible = false;
                //  ucMultiserverprofilemoniReport.Visible = false;
                //    break;
                case "6":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    var fastcopymonitor = (Panel)FastcopyMonitorReport.FindControl("Pan1");
                    fastcopymonitor.Visible = false;
                    FastcopyMonitorReport.Visible = true;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                //case "19":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    ucDRDrillExcelReport.Visible = false;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    //Panel DRHealthReport = (Panel)ucDRHealthReport.FindControl("Pan1");
                //    //DRHealthReport.Visible = false;
                //    ucDRHealthReport.Visible = true;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucApplicationDiscovery.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;
                //    break;


                case "18":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    Panel uUserActivityDetailReport = (Panel)UserActivityDetailReport.FindControl("Pan1");
                    uUserActivityDetailReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = true;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                //case "21":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    ucDRDrillExcelReport.Visible = false;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    // Panel uUserActivityDetailReport = (Panel)UserActivityDetailReport.FindControl("Pan1");
                //    //uUserActivityDetailReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = true;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucApplicationDiscovery.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;
                //    break;

                //case "22":
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    //ucBusinessImpactAnalysis.Visible = false;
                //    //ucDRDrillReport.Visible = false;
                //    //ucNewStatus24.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    //ucApplication24Hrs.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    ucDRDrillExcelReport.Visible = false;
                //    //ApplicationSyncstatus.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    // Panel uUserActivityDetailReport = (Panel)UserActivityDetailReport.FindControl("Pan1");
                //    //uUserActivityDetailReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = true;
                //    MontlySummaryReport.Visible = false;
                //    ucEmailReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationDiscovery.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;
                //    break;

                case "2":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucApplicationDiscovery.Visible = true;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                //case "24":
                //    ucApplicationDiscovery.Visible = false;
                //    ucDataLagReport.Visible = false;
                //    ucGroupConfiguration.Visible = false;
                //    ucGlobalMirrorReplication.Visible = false;
                //    ucMediationGroup.Visible = false;
                //    ucApplicationGroup.Visible = false;
                //    ucGlobalMirrorCGFormation.Visible = false;
                //    ucDatalagReport24Hrs.Visible = false;
                //    ucNewDataLag24Hours.Visible = false;
                //    ucStatus24.Visible = false;
                //    ucDatalagstatus.Visible = false;
                //    ucParallelDROperationReport.Visible = false;
                //    ucDRDrillExcelReport.Visible = false;
                //    FastcopyMonitorReport.Visible = false;
                //    ucDRHealthReport.Visible = false;
                //    UserActivityDetailReport.Visible = false;
                //    AuditReport.Visible = false;
                //    SLAReport.Visible = false;
                //    MontlySummaryReport.Visible = true;
                //    ucEmailReport.Visible = false;
                //    ucSMSReport.Visible = false;
                //    ucApplicationdependancyReport.Visible = false;
                //    ucVMCenterReport.Visible = false;
                //    ucMultiserverprofilemoniReport.Visible = false;
                //    ucImportCMDBReport.Visible = false;

                //    break;
                case "17":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = true;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    var ucGrpsummary2 = (Panel)ucMediationGroup.FindControl("Pan1");
                    ucGrpsummary2.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;
                case "10":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucEmailReport.Visible = true;
                    ucSMSReport.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    var ucGrpsummary1 = (Panel)ucMediationGroup.FindControl("Pan1");
                    ucGrpsummary1.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "1":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucApplicationdependancyReport.Visible = true;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    var ucGrpsummary3 = (Panel)ucMediationGroup.FindControl("Pan1");
                    ucGrpsummary3.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;
                case "19":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucVMCenterReport.Visible = true;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    //  ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "15":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = true;
                    ucVMCenterReport.Visible = false;
                    // ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;
                case "4":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucImportCMDBReport.Visible = true;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucVMCenterReport.Visible = false;
                    // ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "8":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDRReddynesslog.Visible = true;
                    ucDRReaddynesssummary.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucVMCenterReport.Visible = false;
                    // ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;
                case "9":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDRReddynesslog.Visible = false;
                    ucDRReaddynesssummary.Visible = true;

                    ucImportCMDBReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucVMCenterReport.Visible = false;
                    // ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "20":
                    ucSMSReport.Visible = false;
                    ucDRReadyRPT.Visible = false;
                    ucRPMonitor.Visible = true;
                    ucDRReddynesslog.Visible = false;
                    ucDRReaddynesssummary.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucVMCenterReport.Visible = false;
                    // ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;

                    break;

                case "21":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = true;
                    ucRPMonitor.Visible = false;
                    ucDRReddynesslog.Visible = false;
                    ucDRReaddynesssummary.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    RTOReport.Visible = false;
                    ucEmailReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;


                case "22":
                    ucDRReadyRPT.Visible = true;
                    ucGGReplicationReportHourly.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucVMCenterReport.Visible = false;
                    // ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    RTOReport.Visible = false;

                    ucRPOSLADeviationReports.Visible = false;

                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "23":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucVMCenterReport.Visible = false;
                    // ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    RTOReport.Visible = true;

                    ucRPOSLADeviationReports.Visible = false;
                    AlertReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;



                case "25":
                     AlertReport.Visible = false;
                    ucRPOSLADeviationReports.Visible = true;
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    var datalagRptb = (Panel)ucDataLagReport.FindControl("Pan1");
                    datalagRptb.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = false;
                    break;

                case "24":
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucVMCenterReport.Visible = false;
                    // ucMonitoringSerivesReport.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    // var globalmirrorFmation = (Panel)ucGlobalMirrorCGFormation.FindControl("Pan1");
                    //globalmirrorFmation.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    //ucBusinessImpactAnalysis.Visible = false;
                    //ucDRDrillReport.Visible = false;
                    //ucNewStatus24.Visible = false;
                    //ucDatalagChartForAllGroup.Visible = false;
                    ucDatalagstatus.Visible = false;
                    //ucApplication24Hrs.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    //ApplicationSyncstatus.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    RTOReport.Visible = false;
                    AlertReport.Visible = true;
                     ucRPOSLADeviationReports.Visible = false;
                     unLicenseUtilizationRPT.Visible = false;
                     unCMDBCompareReport.Visible = false;
                    break;


                case "26":
                    AlertReport.Visible = false;
                    ucRPOSLADeviationReports.Visible = false;
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = true;
                    unCMDBCompareReport.Visible = false;
                    break;





                case "28":
                    AlertReport.Visible = false;
                    ucRPOSLADeviationReports.Visible = false;
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unPPDMRestoreExecution.Visible = true;
                    break;


                case "27":
                    AlertReport.Visible = false;
                    ucRPOSLADeviationReports.Visible = false;
                    ucDRReadyRPT.Visible = false;
                    ucGGReplicationReportHourly.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucDataLagReport.Visible = false;
                    ucGroupConfiguration.Visible = false;
                    ucGlobalMirrorReplication.Visible = false;
                    ucMediationGroup.Visible = false;
                    ucApplicationGroup.Visible = false;
                    ucGlobalMirrorCGFormation.Visible = false;
                    ucDatalagReport24Hrs.Visible = false;
                    ucNewDataLag24Hours.Visible = false;
                    ucStatus24.Visible = false;
                    ucDatalagstatus.Visible = false;
                    ucParallelDROperationReport.Visible = false;
                    ucDRDrillExcelReport.Visible = false;
                    FastcopyMonitorReport.Visible = false;
                    ucDRHealthReport.Visible = false;
                    UserActivityDetailReport.Visible = false;
                    AuditReport.Visible = false;
                    SLAReport.Visible = false;
                    MontlySummaryReport.Visible = false;
                    ucEmailReport.Visible = false;
                    ucSMSReport.Visible = false;
                    ucApplicationdependancyReport.Visible = false;
                    ucRPMonitor.Visible = false;
                    ucApplicationDiscovery.Visible = false;
                    ucVMCenterReport.Visible = false;
                    ucMultiserverprofilemoniReport.Visible = false;
                    ucImportCMDBReport.Visible = false;
                    RTOReport.Visible = false;
                    unLicenseUtilizationRPT.Visible = false;
                    unCMDBCompareReport.Visible = true;
                    break;


            }
        }

        protected void btnSchReport_Click(object sender, EventArgs e)
        {
            Session["getval"] = 0;
            ModalPopupExtenderCustom.Show();
        }

        protected void CloseGroupWorkflowClick(object sender, EventArgs e)
        {
            Session["getval"] = 0;
            ModalPopupExtenderCustom.Hide();
        }
    }
}