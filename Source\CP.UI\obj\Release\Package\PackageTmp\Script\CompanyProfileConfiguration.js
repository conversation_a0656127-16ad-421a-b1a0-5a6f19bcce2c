﻿var ready = $(document).ready(
function(){
	var click = $("input[id$=btnCustomerSave]").click(function(){
		$(this).prev().hide();
		var content ="<div class='float-left side1'>Customer Name<span class='error'>*</span> </div><div> <input type='text' id ='txtCustomerName' runat='server' maxlength='10' /><span></span><span id='spCustomerdbnum' style='display:none;' class='error'>Enter AlphaNumeric value only</span><span style='display:none;' class='error' id='spCustomerdbname'>BCMS Customer Name not Available</span></div> <hr/>" +
			            '<div class="float-left side1">Upload Company Logo </div><div><input type="file" id ="uploadImg" name="" size=""> </input></div>';
		var hideButtonIds = "Reset:Update:Close";
		openModal("Create Customer", content, hideButtonIds,saveCustomer, null);
	});

	function saveCustomer(win){
		var bcmsCustomerName = jQuery.trim($('#txtCustomerName').val());

		var valueToPass = "";
		valueToPass = bcmsCustomerName;
		if (bcmsCustomerName == "" ){
			alert("Required fields must be filled");
			return false;
		}
		$('#txtCustomerName').trigger("blur");
		if ($("#spCustomerdbname").is(":visible")) {
			return false;
		}

		var ajaxUrl = "CompanyProfileConfiguration.aspx/SaveDatabase";

		var ajaxData = "{'Details':'" + valueToPass +"'}";
		AjaxFunction(ajaxUrl, ajaxData, CustomerSaved, OnError);
		CloseModel(win);
		return false;
	}
	function CustomerSaved(){
		window.location.reload();
	}
	});