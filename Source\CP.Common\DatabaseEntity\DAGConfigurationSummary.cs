﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DAGConfigurationSummary", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DAGConfigurationSummary : BaseEntity
    {
        #region Properties

        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int InfraobjectId { get; set; }

        [DataMember]
        public int DatabaseId { get; set; }

        [DataMember]
        public string DAGName { get; set; }

        [DataMember]
        public string DAGIP { get; set; }
        
        [DataMember]
        public string MemberServers { get; set; }

        [DataMember]
        public string WitnessServer { get; set; }

        [DataMember]
        public string WitnessDirectory { get; set; }

        #endregion Properties
    }
}
