﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;

namespace CP.UI.Controls
{
    public partial class ActiveDirectoryConfigList : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;
        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }
        public override void PrepareView()
        {
            //     Binddata();
        }

        private void Binddata()
        {
            setListViewPage();
            IList<ActiveDirectory> result = Facade.GetActiveDirectoryByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, ReplicationType.ActiveDirectory.ToString());

            if (result == null)
            {
                IList<ActiveDirectory> val = new List<ActiveDirectory>();
                lvActiveDirectory.DataSource = val;
                lvActiveDirectory.DataBind();
            }
            else
            {
                lvActiveDirectory.DataSource = result;
                lvActiveDirectory.DataBind();

            }

        }
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPagelvActiveDirectoryList"]) != -1) && Session["CurrentPagelvActiveDirectoryList"] != null && (Convert.ToInt32(Session["CurrentPagelvActiveDirectoryList"]) > 0))
            {
                if (Session["TotalPageRowsCountlvActiveDirectoryList"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPagelvActiveDirectoryList"]) == Convert.ToInt32(Session["TotalPageRowsCountlvActiveDirectoryList"]) - 1)
                    {
                        Session["CurrentPagelvActiveDirectoryList"] = Convert.ToInt32(Session["CurrentPagelvActiveDirectoryList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCountlvActiveDirectoryList"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPagelvActiveDirectoryList"]), dataPager1.MaximumRows, true);
                dataPager1.DataBind();
                Session["CurrentPagelvActiveDirectoryList"] = -1;
            }
        }
        private IList<ActiveDirectory> GetlvActiveDirectoryList()
        {
            return Facade.GetActiveDirectoryByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, ReplicationType.ActiveDirectory.ToString());
        }
        public IList<ActiveDirectory> GetlvActiveDirectoryList(string searchvalue)
        {
            var replicationlist = GetlvActiveDirectoryList();

            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count>0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().StartsWith(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvActiveDirectory.Items.Clear();
                lvActiveDirectory.DataSource = GetlvActiveDirectoryList(txtsearchvalue.Text);
                lvActiveDirectory.DataBind();
            }
        }
        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByReplicationId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Replication " + name + " attaching with group " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void lvActiveDirectory_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPagelvActiveDirectoryList"] = dataPager1.StartRowIndex;
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvActiveDirectory.Items[e.NewEditIndex].FindControl("Id")) as Label;

            var lblName = (lvActiveDirectory.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;
            if (lbl1 != null && lblName != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, "127");
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }

        }

        protected void lvActiveDirectory_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPagelvActiveDirectoryList"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCountlvActiveDirectoryList"] = dataPager1.TotalRowCount;
                var lblId = lvActiveDirectory.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvActiveDirectory.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
                //var ReplicationDetail = Facade.GetReplicationBaseById(Convert.ToInt32(lblId.Text));
                //var SiteDetail = Facade.GetSiteById(ReplicationDetail.SiteId);
                var InfraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));
                if (lblId != null && lblId.Text != null && lblName != null)
                {
                    //var applicationDetailByReplicationId =
                    //    Facade.GetApplicationGroupsByReplicationId(Convert.ToInt32(lblId.Text));
                    //if (applicationDetailByReplicationId != null)
                    //{
                    //    ErrorSuccessNotifier.AddSuccessMessage("The SnapMirror Replication component is in use");
                    //}
                    //else
                    //{
                    if (InfraObjects != null && InfraObjects.Count > 0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The lvActiveDirectory Replication component is in use.");

                    }
                    else
                    {
                        Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(LoggedInUserName, "lvActiveDirectory", UserActionType.DeleteReplicationComponent,
                                              "The lvActiveDirectory Replication component '" + lblName.Text +
                                              "' was deleted from the replication component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "ActiveDirectory Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));

                    }
                }

            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);

            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);

                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "127");

                Helper.Url.Redirect(secureUrl);
            }

        }

        protected void lvActiveDirectory_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;
            if (IsUserCustom)
            {
                edit.Enabled = false;
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.Where(x => x.AccessMenuType == AccessManagerType.View.ToString()).ToList();
                    if (ObjAccess == null)
                    {
                        edit.Enabled = false;
                        edit.ImageUrl = "../images/icons/pencil_disable.png";
                        delete.Enabled = false;
                        delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                    }
                    else if (ObjAccess != null)
                    {
                        foreach (var Submenu in ObjAccess)
                        {
                            var Edit = Submenu.AccessSubMenuType.ToString();
                            var deleted = Submenu.AccessSubMenuType.ToString();
                            if (Edit == "4")
                            {
                                edit.Enabled = true;
                                edit.ImageUrl = "../images/icons/pencil.png";
                            }
                            else if (deleted == "5")
                            {
                                delete.Enabled = true;
                                delete.ImageUrl = "../images/icons/cross-circle.png";
                            }
                            else
                            {
                                edit.Enabled = false;
                                edit.ImageUrl = "../images/icons/pencil_disable.png";
                                delete.Enabled = false;
                                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                            }
                        }
                    }
                }
                else
                {
                    edit.Enabled = true;
                    edit.ImageUrl = "../images/icons/pencil.png";
                    delete.Enabled = true;
                    delete.ImageUrl = "../images/icons/cross-circle.png";
                }
            }

            if (IsUserOperator || IsUserManager)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }

        protected void lvActiveDirectory_PreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }
    }
}
