﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DownBusinessServices.ascx.cs" Inherits="CP.UI.DownBusinessServices" %>
<link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
<style type="text/css">
    .myButton {
        display: inline-block;
        background-color: transparent;
        border: 0 solid #4a8bc2;
        color: #4a8bc2;
        font-size: 23px !important;
        margin-left: 7px;
        margin-top: -12px;
        padding: 0px;
        text-shadow: 0 0 0 rgba(0, 0, 0, 0.5);
        vertical-align: text-top;
    }

    #ciodrready .popover {
        width: 400px !important;
        max-width: 400px !important;
        top: -73px !important;
    }

    .containertable {
        display: none;
    }
</style>
<%--<asp:UpdatePanel runat="server" UpdateMode="Conditional" ID="uppnlDownService">
    <ContentTemplate>--%>
<div class="widget widget-heading-simple widget-body-white">
    <asp:HiddenField runat="server" ID="hdnCompLogo" />
    <asp:HiddenField runat="server" ID="hdnServiceId" />

    <div class="widget-body">
        <div class="cioheader">

            <div class="title">
                Currently Not DR Ready
                
                        <asp:ImageButton ID="imgBtnReport" runat="server" ImageUrl="../../Images/CIO/report-icon.png" Style="float: right;" OnClick="imgBtnReport_Click" Visible="false" />
                <asp:ImageButton ID="imgbtnDRReady" runat="server" ImageUrl="../../Images/CIO/report-icon.png" OnClick="imgbtnDRReady_Click" Style="float: right;" />
            </div>

            <asp:Label ID="lblCureentMonth" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
            <span class="subhead">(Current Month)</span>


        </div>
        <div class="ciocontent">

            <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 0px;">
                <div class="col-md-6 padding-none-LR">
                    <div>
                        <asp:Label ID="lblBusinessServiceCount" runat="server" CssClass="count" Text="0"></asp:Label>
                    </div>
                    <asp:Label ID="Label44" runat="server" CssClass="subhead" Text="DR Not Ready"></asp:Label>
                </div>
                <div class="col-md-6 padding-none-LR text-right">
                    <div>
                        <asp:Label ID="lblBusinessFunctionCount" runat="server" CssClass="count" Text="0"></asp:Label>
                    </div>
                    <asp:Label ID="Label46" runat="server" CssClass="subhead" Text="DR Ready"></asp:Label>
                    <asp:HiddenField ID="hdndrReadycountHtml" runat="server"></asp:HiddenField>
                    <%--<asp:HiddenField ID="hdnDrillInfra" runat="server"></asp:HiddenField>--%>

                </div>
            </div>
            <div class="text-center">
                <%--  <img src="../Images/CIO/pie-chart.png" />--%>
                <%-- <div id="chart" style="height: 180px;">
                </div>--%>
                <telerik:RadHtmlChart runat="server" ID="RadPieChart" Height="120px" Width="190px" CssClass="fb-sized" Legend-Appearance-Visible="false">
                    <PlotArea>
                        <Series>
                            <telerik:PieSeries DataFieldY="Share" NameField="Name">
                                <%--<LabelsAppearance Visible="false" Position="InsideEnd" DataFormatString="{0}">
                                </LabelsAppearance>--%>
                                <TooltipsAppearance Color="White" DataFormatString="{0}"></TooltipsAppearance>

                            </telerik:PieSeries>
                        </Series>
                        <YAxis Color="#95c761">
                        </YAxis>
                    </PlotArea>
                </telerik:RadHtmlChart>
                <%-- <ChartTitle Text="Dr Not Ready">
                    </ChartTitle>--%>
                <div>
                    <asp:Label ID="Label57" runat="server" CssClass="leg1"></asp:Label>
                    <span class="legtext">DR Ready</span>
                    <asp:Label ID="Label56" runat="server" CssClass="leg2"></asp:Label>
                    <span class="legtext">DR Not Ready</span>

                    <asp:Label ID="Label1" runat="server" CssClass="leg3"></asp:Label>
                    <span class="legtext">NA</span>
                </div>
            </div>
        </div>
        <div class="ciofooter">
            <div class="col-md-9 padding-none-LR">
                <img src="../Images/CIO/not-ready.png" />
            </div>
            <div class="col-md-3 padding-none-LR text-right">
                <asp:ImageButton ID="ImageButton14" runat="server" Enabled="false" ImageUrl="~/Images/CIO/i_icon.png" />
            </div>
        </div>
    </div>
</div>

<div id="divDRModel" runat="server" class="bg" style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 100%; height: 100%; display: none;"></div>
<div id="divDRPopup" class="modal" style="display: none;" runat="server"></div>
<asp:Button ID="Button1" runat="server" Text="Button" OnClick="Button1_Click" Style="display: none;" />
<asp:Button ID="hdnClose" runat="server" Text="Button" OnClick="hdnClose_Click" Style="display: none;" />
<%--      <asp:UpdateProgress ID="UpdateProgress1" runat="server" AssociatedUpdatePanelID="uppnlDownService">
            <ProgressTemplate>
                <div id="imgLoading" class="loading-mask">
                    <span>Report Generating...</span>
                </div>
            </ProgressTemplate>
        </asp:UpdateProgress>
    </ContentTemplate>
</asp:UpdatePanel>--%>
<script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
<script type="text/javascript">

    function pageLoad() {
        $(".divServerDetScroll").mCustomScrollbar({
            axis: "y"
        });
        $('.myButton').click(function () {
            if (this.value === '-') {
                open = false;
                this.value = '+';
                $(this).next("div.containertable").hide();
            }
            else {

                open = true;
                this.value = '-';
                $(this).siblings("[value='-']").click();
                $(this).next("div.containertable").show();
            }
        });

    }

    $(document).ready(function () {

        $(".divServerDetScroll").mCustomScrollbar({
            axis: "y"
        });
        //$('.myButton').value = '+';
        // $('.myButton').next("div.containertable").hide();

    });

    $(".close").live("click", function () {

        document.getElementById('<%= hdnClose.ClientID %>').click();
        //$('[id$=divDRPopup]').hide();
        //$('[id$=divDRModel]').hide();
    });


        function ShowReport(Sid, fId, IId) {
            document.getElementById('<%= hdnServiceId.ClientID%>').value = Sid + "," + fId + "," + IId;
            document.getElementById('<%= Button1.ClientID %>').click();
        }



</script>
