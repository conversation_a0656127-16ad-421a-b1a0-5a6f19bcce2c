(function(c){var i={init:function(a){var b={color:c(this).css("background-color"),reach:20,speed:1E3,pause:0,glow:!0,repeat:!0,onHover:!1};c(this).css({"-moz-outline-radius":c(this).css("border-top-left-radius"),"-webkit-outline-radius":c(this).css("border-top-left-radius"),"outline-radius":c(this).css("border-top-left-radius")});a&&c.extend(b,a);b.color=c("<div style='background:"+b.color+"'></div>").css("background-color");b.repeat!==!0&&!isNaN(b.repeat)&&b.repeat>0&&(b.repeat-=1);return this.each(function(){b.onHover?
c(this).bind("mouseover",function(){g(b,this,0)}).bind("mouseout",function(){c(this).pulsate("destroy")}):g(b,this,0)})},destroy:function(){return this.each(function(){clearTimeout(this.timer);c(this).css("outline",0)})}},g=function(a,b,d){var f=a.reach;d=d>f?0:d;var h=(f-d)/f,e=a.color.split(",");h="rgba("+e[0].split("(")[1]+","+e[1]+","+e[2].split(")")[0]+","+h+")";e={outline:"2px solid "+h};a.glow?(e["box-shadow"]="0px 0px "+parseInt(d/1.5)+"px "+h,c.browser.webkit&&(e["outline-offset"]=d+"px",
e["outline-radius"]="100 px")):e["outline-offset"]=d+"px";c(b).css(e);b.timer=setTimeout(function(){if(d>=f&&!a.repeat)return c(b).pulsate("destroy"),!1;else if(d>=f&&a.repeat!==!0&&!isNaN(a.repeat)&&a.repeat>0)a.repeat-=1;else if(a.pause&&d>=f)return j(a,b,d+1),!1;g(a,b,d+1)},a.speed/f)},j=function(a,b,c){innerfunc=function(){g(a,b,c)};setTimeout(innerfunc,a.pause)};c.fn.pulsate=function(a){if(i[a])return i[a].apply(this,Array.prototype.slice.call(arguments,1));else if(typeof a==="object"||!a)return i.init.apply(this,
arguments);else c.error("Method "+a+" does not exist on jQuery.pulsate")}})(jQuery);