﻿using System;
using System.Collections.Generic;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Linq;
using System.Web;
using System.Configuration;
using log4net;
using System.ServiceProcess;
//System.Web.UI.Page

namespace CP.UI
{
    public partial class ReportScheduler : ReportScheduleBasePageEditor
    {
        #region Variables
        private static readonly ILog _logger = LogManager.GetLogger(typeof(ReportScheduler));
        public static int ReportScheduleId = 0;
        public int ReportCount = 0;
        public static int CurrentSelectedIndex = 0;
        private bool _reportValidator = true;
        readonly IList<ListItem> _previousSelectedItems = new List<ListItem>();
        #endregion

        public override string MessageInitials
        {
            get { return "ReportScheduler"; }
        }

        public static string RedirectURL = Constants.UrlConstants.Urls.Reports.ReportScheduler;
        public override string ReturnUrl
        {
            get
            {
                //if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                //{
                //    return Constants.UrlConstants.Urls.Component.SereverList;
                //}
                return string.Empty;
            }
        }

        public void BindListView_ByReportSchedulerId(int ReportSchedulerId)
        {
            List<ReportSchedule> lst = new List<ReportSchedule>();

            ReportSchedule ReportScheduler = Facade.GetReportSchedulerById(ReportSchedulerId);
            if (ReportScheduler == null)
                return;

            lst.Insert(0, ReportScheduler);

            lvReportScheduler.DataSource = lst;
            lvReportScheduler.DataBind();
        }

        //protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    if (RadioButtonList1.SelectedItem.Text == "Daily")
        //    {
        //        DWM_Basis.Visible = true;
        //        Panel_Daily.Visible = true;
        //        Panel_Wekly.Visible = false;
        //        Panel_Monthly.Visible = false;

        //    }
        //    else if (RadioButtonList1.SelectedItem.Text == "Weekly")
        //    {
        //        DWM_Basis.Visible = true;
        //        Panel_Daily.Visible = false;
        //        Panel_Wekly.Visible = true;
        //        Panel_Monthly.Visible = false;
        //    }
        //    else if (RadioButtonList1.SelectedItem.Text == "Monthly")
        //    {
        //        DWM_Basis.Visible = true;
        //        Panel_Daily.Visible = false;
        //        Panel_Wekly.Visible = false;
        //        Panel_Monthly.Visible = true;
        //    }
        //}
        //protected void rdbtn_Daytype_SelectedIndexChanged(object sender, EventArgs e)
        //{

        //    if (rdbtn_Daytype.SelectedItem.Text == "Day of Month")
        //    {
        //        pnl_daysofmonth.Visible = true;
        //        pnl_weekdays.Visible = false;
        //    }
        //    else if (rdbtn_Daytype.SelectedItem.Text == "Weekdays")
        //    {
        //        pnl_daysofmonth.Visible = false;
        //        pnl_weekdays.Visible = true;
        //    }

        //}
        //protected void cbl_weekdays_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //}


        public void AddReportscheduler()
        {
            CurrentEntity = Facade.AddReportScheduler(CurrentEntity);
            lblAdded.Visible = true;
            lblAdded.Text = "Report has been successfully Added";
            BindReportName(ddlReport.SelectedItem.Text);
            // ActivityLogger.AddLog(LoggedInUserName, "ReportScheduler", UserActionType.CreateServerComponent, "The ReportScheduler '" + CurrentEntity.ReportName + "' was added to the ReportScheduler component table");
        }

        public void ResetControlsValue()
        {

            switch (ddlReport.SelectedItem.Text)
            {
                case "RPO SLA Report":
                    ddlInfra.SelectedIndex = 0;
                    ResetCommonFields();
                    break;

                case "Datalag Status Report":
                    ResetCommonFields();
                    break;

                case "DRDRill Report":
                    ddlInfraObj.SelectedIndex = 0;
                    ResetCommonFields();
                    break;

                case "DataSync Monitoring Report":
                    // ddlApp.SelectedIndex = 0;
                    // ddlJobs.SelectedIndex = 0;
                    ResetCommonFields();
                    break;

                case "Weekly Summary Report":
                    ResetCommonFields();
                    break;

                case "Group Summary":
                    ResetCommonFields();
                    break;

                case "Monthly Summary Report":
                    ResetCommonFields();
                    break;

                case "DR Readiness Execution Log":
                    ResetCommonFields();
                    break;

                case "DR Ready Report":
                    ResetCommonFields();
                    break;
            }
        }
        public void ResetControls()
        {

            switch (ddlReport.SelectedItem.Text)
            {
                case "RPO SLA Report":
                    ddlInfra.SelectedIndex = 0;
                    CommonFields();
                    PanelInterval.Visible = true;
                    break;

                case "Datalag Status Report":
                    CommonFields();
                    break;

                case "DRDRill Report":
                    ddlInfraObj.SelectedIndex = 0;
                    CommonFields();
                    break;

                case "DataSync Monitoring Report":
                    ddlApp.SelectedIndex = 0;
                    ddlJobs.SelectedIndex = 0;
                    CommonFields();
                    break;

                case "Weekly Summary Report":
                    CommonFields();
                    break;

                case "Group Summary":
                    CommonFields();
                    break;

                case "Monthly Summary Report":
                    CommonFields();
                    break;

                case "InfraObject Summary":
                    ddlBussServ.SelectedIndex = 0;
                    CommonFields();
                    break;

                case "DR Readiness Execution Log":
                    ddlBussService.SelectedIndex = 0;
                    CommonFields();
                    break;

                case "DR Ready Report":
                    CommonFields();
                    break;

            }
        }
        public void ResetCommonFields()
        {
            txtend.Text = string.Empty;
            txtstart.Text = string.Empty;
            //txtDays.Text = string.Empty;
            txtEndDSM.Text = string.Empty;
            txtStartDSM.Text = string.Empty;
            ddlHrs.SelectedIndex = -1;
            ddlMins.SelectedIndex = -1;
            btnSave.Text = "Save";
            //  ddlReport.SelectedValue = "0";
            //ddlCheckEmailRec.Items.Clear();
            // ddlCheckEmailRec.SelectedIndex = -1;
            // ddlCheckEmailRec.Items.Clear();

            RadioButtonList1.SelectedIndex = 0;
            ddlhours.SelectedIndex = 00;
            ddlminutes.SelectedIndex = 00;
            ddlWeklyhours.SelectedIndex = 00;
            ddlWeklyminutes.SelectedIndex = 00;
            ddlDays.SelectedIndex = 0;
            ddl_month.SelectedValue = "All";
            rdbtn_Daytype.SelectedValue = "DOM";
            txtday.Text = string.Empty;
            cbl_weekdays.ClearSelection();

            ddl_Hours1.SelectedIndex = 00;
            ddl_minutes1.SelectedIndex = 00;
            cblstGroup.ClearItems();

        }
        public void CommonFields()
        {
            txtend.Text = string.Empty;
            txtstart.Text = string.Empty;
            //  txtDays.Text = string.Empty;
            txtEndDSM.Text = string.Empty;
            txtStartDSM.Text = string.Empty;
            ddlHrs.SelectedIndex = -1;
            ddlMins.SelectedIndex = -1;
            btnSave.Text = "Save";
            ddlReport.SelectedValue = "0";
            //ddlCheckEmailRec.Items.Clear();
            // ddlCheckEmailRec.Items.Clear();
            cblstGroup.ClearItems();
            RadioButtonList1.SelectedIndex = 0;
            ddlhours.SelectedIndex = 00;
            ddlminutes.SelectedIndex = 00;
            ddlWeklyhours.SelectedIndex = 00;
            ddlWeklyminutes.SelectedIndex = 00;
            ddlDays.SelectedIndex = 0;
            ddl_month.SelectedValue = "All";
            rdbtn_Daytype.SelectedValue = "DOM";
            txtday.Text = string.Empty;
            cbl_weekdays.ClearSelection();

            ddl_Hours1.SelectedIndex = 00;
            ddl_minutes1.SelectedIndex = 00;

        }
        public void UpdateReportScheduler()
        {
            CurrentEntity.UpdatorId = LoggedInUserId;
            CurrentEntity.Id = Convert.ToInt32(Session["Id"]);
            CurrentEntity = Facade.UpdateReportScheduler(CurrentEntity);

            lblAdded.Visible = true;
            lblAdded.Text = "Report has been successfully Updated";
            BindReportName(ddlReport.SelectedItem.Text);

            //  ActivityLogger.AddLog(LoggedInUserName, "ReportScheduler", UserActionType.UpdateServerComponent, "The ReportScheduler '" + CurrentEntity.ReportName + "' was updated to the ReportScheduler component table");
        }

        public bool ValidateReportNameList()
        {
            bool rbchecked = false;
            for (int i = 1; i < ddlReport.Items.Count; i++)
            {
                if (ddlReport.Items[i].Selected == true)
                {
                    rbchecked = true;
                    _reportValidator = true;
                }
            }
            if (rbchecked == true)
            {
                lblReportlist.Text = string.Empty;
                _reportValidator = true;
                return true;
            }
            else
            {
                // lblTIme.Text = "Please select Time Interval";
                _reportValidator = false;
                return false;
            }
        }

        public override void SaveEditor()
        {

            bool validate = false;
            validate = ValidateReceiverList();

            if (validate)
            {
                var Allsheduledreports = Facade.GetAllReportScheduler();
                #region Save/Update
                if (btnSave.Text.Equals("Save"))
                {
                    CurrentEntity.CreatorId = LoggedInUserId;
                    CurrentEntity.UpdatorId = LoggedInUserId;

                    if (Allsheduledreports != null)
                    {
                        foreach (var rpt in Allsheduledreports)
                        {
                            switch (ddlReport.SelectedItem.Text)
                            {
                                //case "RPO SLA Report":
                                //    if (rpt.ReportName == CurrentEntity.ReportName && rpt.InfraObjectId == Convert.ToInt32(ddlInfra.SelectedValue))
                                //    {
                                //        ReportCount = ReportCount + 1;
                                //        ReportScheduleId = rpt.Id;
                                //        goto ExitFromLoop;
                                //    }

                                //    break;

                                case "InfraObject Summary Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName && rpt.AppId == Convert.ToInt32(ddlBussServ.SelectedValue))
                                    {


                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;

                                    }
                                    break;

                                case "Group Summary Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName && rpt.AppId == Convert.ToInt32(ddlBussServ.SelectedValue))
                                    {


                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;

                                    }
                                    break;

                                case "DRDRill Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName && rpt.InfraObjectId == Convert.ToInt32(ddlInfraObj.SelectedValue))
                                    {
                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;
                                    }
                                    break;

                                //case "DataSync Monitoring Report":
                                //    if (rpt.ReportName == CurrentEntity.ReportName && rpt.InfraObjectId == Convert.ToInt32(ddlApp.SelectedValue))
                                //    {
                                //        ReportCount = ReportCount + 1;
                                //        ReportScheduleId = rpt.Id;
                                //        goto ExitFromLoop;
                                //    }
                                //    break;
                                case "Weekly Summary Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName)
                                    {
                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;
                                    }
                                    break;
                                case "Datalag Status Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName)
                                    {
                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;
                                    }
                                    break;

                                case "Monthly Summary Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName)
                                    {
                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;
                                    }
                                    break;
                            }

                        }
                    ExitFromLoop:
                        if (ReportCount > 0)
                        {
                            switch (ddlReport.SelectedItem.Text)
                            {
                                case "RPO SLA Report":
                                    lblDatemsg.Text = ddlReport.SelectedItem.Text + " report for " + ddlInfra.SelectedItem + "  is already scheduled.";
                                    BindListView_ByReportSchedulerId(ReportScheduleId);
                                    break;

                                case "Group Summary Report":
                                    lblDatemsg.Text = ddlReport.SelectedItem.Text + " report for " + ddlBussServ.SelectedItem + "  is already scheduled.";

                                    BindListView_ByReportSchedulerId(ReportScheduleId);
                                    break;

                                case "DRDRill Report":
                                    lblDatemsg.Text = ddlReport.SelectedItem.Text + " report for " + ddlInfraObj.SelectedItem + "  is already scheduled.";
                                    BindListView_ByReportSchedulerId(ReportScheduleId);
                                    break;

                                //case "DataSync Monitoring Report":
                                //    lblDatemsg.Text = ddlReport.SelectedItem.Text + " report for " + ddlApp.SelectedItem + "  is already scheduled.";
                                //    BindListView_ByReportSchedulerId(ReportScheduleId);
                                //    break;

                                case "Weekly Summary Report":
                                    lblDatemsg.Text = ddlReport.SelectedItem.Text + "  is already scheduled.";
                                    BindListView_ByReportSchedulerId(ReportScheduleId);
                                    break;

                                case "Datalag Status Report":
                                    lblDatemsg.Text = ddlReport.SelectedItem.Text + "  is already scheduled.";
                                    BindListView_ByReportSchedulerId(ReportScheduleId);
                                    break;

                                case "Monthly Summary Report":
                                    lblDatemsg.Text = ddlReport.SelectedItem.Text + "  is already scheduled.";
                                    BindListView_ByReportSchedulerId(ReportScheduleId);
                                    break;

                            }
                        }
                        else
                        {
                            AddReportscheduler();
                            BindReportName(ddlReport.SelectedItem.Text.ToString());
                            ResetControls();

                        }
                    }
                    else
                    {
                        AddReportscheduler();
                        BindReportName(ddlReport.SelectedItem.Text.ToString());
                        ResetControls();

                    }

                    var sc = new ServiceController("CPMonitorService");
                    if (sc.Status.ToString() != "Stopped")
                    {
                        sc.ExecuteCommand(150);
                    }
                }
                else//if btn text ='Update'
                {
                    if (Allsheduledreports != null)
                    {
                        foreach (var rpt in Allsheduledreports)
                        {
                            switch (ddlReport.SelectedItem.Text)
                            {
                                case "RPO SLA Report":
                                    if (CurrentSelectedIndex != ddlInfra.SelectedIndex)
                                    {
                                        if (rpt.ReportName == CurrentEntity.ReportName && rpt.InfraObjectId == Convert.ToInt32(ddlInfra.SelectedValue))
                                        {
                                            ReportCount = ReportCount + 1;
                                            ReportScheduleId = rpt.Id;
                                            goto ExitFromLoop;
                                        }
                                    }
                                    else
                                    {
                                        UpdateReportScheduler();
                                        BindReportName(ddlReport.SelectedItem.Text.ToString());
                                        ResetControls();
                                        goto ExitFromswitch;
                                    }
                                    break;

                                case "InfraObject Summary Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName && rpt.AppId == Convert.ToInt32(ddlBussServ.SelectedValue))
                                    {


                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;

                                    }
                                    break;

                                case "Group Summary Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName && rpt.AppId == Convert.ToInt32(ddlBussServ.SelectedValue))
                                    {


                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;

                                    }
                                    break;

                                case "DRDRill Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName && rpt.InfraObjectId == Convert.ToInt32(ddlInfraObj.SelectedValue))
                                    {
                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;
                                    }
                                    break;

                                case "DataSync Monitoring Report":
                                    if (CurrentSelectedIndex != ddlApp.SelectedIndex)
                                    {
                                        if (rpt.ReportName == CurrentEntity.ReportName && rpt.InfraObjectId == Convert.ToInt32(ddlApp.SelectedValue))
                                        {
                                            ReportCount = ReportCount + 1;
                                            ReportScheduleId = rpt.Id;
                                            goto ExitFromLoop;
                                        }
                                    }
                                    else
                                    {
                                        UpdateReportScheduler();
                                        BindReportName(ddlReport.SelectedItem.Text.ToString());
                                        ResetControls();
                                        goto ExitFromswitch;
                                    }
                                    break;
                                case "Weekly Summary Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName)
                                    {
                                        //ReportCount = ReportCount + 1;
                                        //ReportScheduleId = rpt.Id;
                                        //goto ExitFromLoop;
                                        UpdateReportScheduler();
                                        BindReportName(ddlReport.SelectedItem.Text.ToString());
                                        ResetControls();
                                        goto ExitFromswitch;
                                    }
                                    break;
                                case "Datalag Status Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName)
                                    {
                                        //ReportCount = ReportCount + 1;
                                        //ReportScheduleId = rpt.Id;
                                        //goto ExitFromLoop;
                                        UpdateReportScheduler();
                                        BindReportName(ddlReport.SelectedItem.Text.ToString());
                                        ResetControls();
                                        goto ExitFromswitch;
                                    }
                                    break;

                                case "Monthly Summary Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName)
                                    {

                                        UpdateReportScheduler();
                                        BindReportName(ddlReport.SelectedItem.Text.ToString());
                                        ResetControls();
                                        goto ExitFromswitch;
                                    }
                                    break;


                                case "DR Readiness Execution Log":
                                    if (CurrentSelectedIndex != ddlBussService.SelectedIndex)
                                    {
                                        if (rpt.ReportName == CurrentEntity.ReportName && rpt.BusinessServiceId == Convert.ToInt32(ddlBussService.SelectedValue))
                                        {
                                            ReportCount = ReportCount + 1;
                                            ReportScheduleId = rpt.Id;
                                            goto ExitFromLoop;
                                        }
                                    }
                                    else if (CurrentSelectedIndex != ddlfn.SelectedIndex)
                                    {

                                        if (rpt.ReportName == CurrentEntity.ReportName && rpt.BusinessServiceId == Convert.ToInt32(ddlBussService.SelectedValue) && rpt.BusinessFunctionId == Convert.ToInt32(ddlfn.SelectedValue))
                                            ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;
                                    }

                                    else if (CurrentSelectedIndex != ddlinfra1.SelectedIndex)
                                    {
                                        if (rpt.ReportName == CurrentEntity.ReportName && rpt.BusinessServiceId == Convert.ToInt32(ddlBussService.SelectedValue) && rpt.BusinessFunctionId == Convert.ToInt32(ddlfn.SelectedValue) && rpt.InfraObjectId == Convert.ToInt32(ddlinfra1.SelectedValue))
                                            ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;
                                    }
                                    else
                                    {
                                        UpdateReportScheduler();
                                        BindReportName(ddlReport.SelectedItem.Text.ToString());
                                        ResetControls();
                                        goto ExitFromswitch;
                                    }
                                    break;

                                case "DR Ready Report":
                                    if (rpt.ReportName == CurrentEntity.ReportName && rpt.AppId == Convert.ToInt32(ddlbusser.SelectedValue))
                                    {
                                        ReportCount = ReportCount + 1;
                                        ReportScheduleId = rpt.Id;
                                        goto ExitFromLoop;

                                    }
                                    break;
                            }

                        }
                    ExitFromLoop:
                        //if (ReportCount > 0)
                        //{
                        //    switch (ddlReport.SelectedItem.Text)
                        //    {
                        //        case "RPO SLA Report":
                        //            lblDatemsg.Text = ddlInfra.SelectedItem + "  is already scheduled.Try with another Group Name. ";
                        //            //BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "InfraObject Summary Report":
                        //            lblDatemsg.Text = ddlBussServ.SelectedItem + "  is already scheduled.Try with another Group Name.";

                        //            //BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "Group Summary Report":
                        //            lblDatemsg.Text = ddlBussServ.SelectedItem + "  is already scheduled.Try with another Group Name.";

                        //            //BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "DRDRill Report":
                        //            lblDatemsg.Text = ddlInfraObj.SelectedItem + "  is already scheduled. Try with another Group Name.";
                        //            BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "DataSync Monitoring Report":
                        //            lblDatemsg.Text = ddlApp.SelectedItem + "  is already scheduled.Try with another Group Name.";
                        //            BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "Weekly Summary Report":
                        //            lblDatemsg.Text = ddlReport.SelectedItem.Text + "  is already scheduled.";
                        //            //BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "Datalag Status Report":
                        //            lblDatemsg.Text = ddlReport.SelectedItem.Text + "  is already scheduled.";
                        //            //BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "Monthly Summary Report":
                        //            lblDatemsg.Text = ddlReport.SelectedItem.Text + "  is already scheduled.";
                        //            //BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "DR Readiness Execution Log":
                        //            if (ddlBussServ.SelectedValue == "All")
                        //            {
                        //                lblDatemsg.Text = ddlBussServ.SelectedItem + "  is already scheduled.Try with another Group Name.";
                        //            }
                        //            else if (ddlBussServ.SelectedValue != "All" && ddlfn.SelectedValue == "ALL")
                        //            {
                        //                lblDatemsg.Text = ddlBussServ.SelectedItem + "  with BF Name. " + ddlfn.SelectedItem + "  is already scheduled.Try with another Group Name.";
                        //            }
                        //            else if (ddlBussServ.SelectedValue != "All" && ddlfn.SelectedValue != "ALL" && ddlinfra1.SelectedValue == "ALL")
                        //            {
                        //                lblDatemsg.Text = ddlBussServ.SelectedItem + "  with BF Name. " + ddlfn.SelectedItem + " with infra Name." + ddlinfra1.SelectedItem + "  is already scheduled.Try with another Group Name.";
                        //            }
                        //            else if (ddlBussServ.SelectedValue != "All" && ddlfn.SelectedValue != "ALL" && ddlinfra1.SelectedValue != "ALL")
                        //            {
                        //                lblDatemsg.Text = ddlBussServ.SelectedItem + "  with BF Name. " + ddlfn.SelectedItem + " with infra Name." + ddlinfra1.SelectedItem + "  is already scheduled.Try with another Group Name.";
                        //            }

                        //            //BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;

                        //        case "DR Ready Report":
                        //            lblDatemsg.Text = ddlbusser.SelectedItem + "  is already scheduled.Try with another Group Name.";

                        //            //BindListView_ByReportSchedulerId(ReportScheduleId);
                        //            break;
                        //    }
                        //}
                        //else
                        //{
                        //    //AddReportscheduler();

                        UpdateReportScheduler();
                        BindReportName(ddlReport.SelectedItem.Text.ToString());
                        ResetControls();

                        // }

                    }
                    else
                    {
                        //AddReportscheduler();
                        //ResetControls();
                        //BindReportName(ddlReport.SelectedItem.Text.ToString());
                    }
                    var sc = new ServiceController("CPMonitorService");
                    if (sc.Status.ToString() != "Stopped")
                    {
                        sc.ExecuteCommand(151);
                    }


                }
            }
            else
            {
                lblEmailErr.Visible = true;
                lblEmailErr.Text = "Select Email";
            }
                #endregion

        ExitFromswitch: { }
        }

        public DateTime FirstDayOfMonthFromDateTime(int Year, int Month)
        {
            return new DateTime(Year, Month, 1);
        }
        public DateTime LastDayOfMonthFromDateTime(int Year, int Month)
        {
            DateTime firstDayOfTheMonth = new DateTime(Year, Month, 1);
            return firstDayOfTheMonth.AddMonths(1).AddDays(-1);
        }
        protected void ddlYear_SelectedIndexChanged(object sender, EventArgs e)
        {
            //lblMsg.Text = string.Empty;
        }
        protected void ddlMonth_SelectedIndexChanged(object sender, EventArgs e)
        {
            //lblMsg.Text = string.Empty;
        }

        public override void BuildEntities()
        {
            CurrentEntity.ReportName = ddlReport.SelectedItem.ToString();
            CurrentEntity.CompanyId = LoggedInUserCompanyId;
            if (ddlInfra.SelectedIndex > 0)
            {
                CurrentEntity.InfraObjectId = Convert.ToInt32(ddlInfra.SelectedValue);
            }
            CurrentEntity.FromDate = txtstart.Text;
            CurrentEntity.ToDate = txtend.Text;

            if (ddlinfra1.SelectedIndex > 0)
            {
                CurrentEntity.InfraObjectId = Convert.ToInt32(ddlinfra1.SelectedValue);

                CurrentEntity.FromDate = txtstart1.Text;
                CurrentEntity.ToDate = txtend1.Text;

            }

            if (ddlbusser.SelectedIndex > 0)
            {
                CurrentEntity.BusinessServiceId = Convert.ToInt32(ddlbusser.SelectedValue);
            }

            if (CurrentEntity.ReportName.Contains("DR Readiness Execution Log"))
            {
                if (ddlBussService.SelectedIndex > 0)
                {
                    CurrentEntity.BusinessServiceId = Convert.ToInt32(ddlBussService.SelectedValue);
                }
                else
                    CurrentEntity.BusinessServiceId = 0;

                if (ddlfn.SelectedIndex > 0)
                {
                    CurrentEntity.BusinessFunctionId = Convert.ToInt32(ddlfn.SelectedValue);
                }
                else
                    CurrentEntity.BusinessFunctionId = 0;


                if (ddlinfra1.SelectedIndex > 0)
                {
                    CurrentEntity.InfraObjectId = Convert.ToInt32(ddlinfra1.SelectedValue);

                    //CurrentEntity.FromDate = txtstart1.Text;
                    //CurrentEntity.ToDate = txtend1.Text;
                }
                else
                    CurrentEntity.InfraObjectId = 0;

                CurrentEntity.FromDate = txtstart1.Text;
                CurrentEntity.ToDate = txtend1.Text;
            }

            if (ddlApp.SelectedIndex > 0)
            {
                CurrentEntity.InfraObjectId = Convert.ToInt32(ddlApp.SelectedValue);
                var groupinfo = Facade.GetInfraObjectById(Convert.ToInt32(ddlApp.SelectedValue));
                CurrentEntity.AppId = groupinfo.PRReplicationId;

                if (ddlJobs.SelectedIndex > 0)
                {
                    CurrentEntity.JobId = Convert.ToInt32(ddlJobs.SelectedValue);
                }

                //  CurrentEntity.FromDate = txtStartDSM.Text;
                //  CurrentEntity.ToDate = txtEndDSM.Text;
            }

            if (ddlBussServ.SelectedIndex > 0)
            {
                CurrentEntity.BusinessServiceId = Convert.ToInt32(ddlBussServ.SelectedValue);
            }
            if (ddlParDrOpr.SelectedIndex > 0)
            {
                CurrentEntity.ParallelDrOpId = Convert.ToInt32(ddlParDrOpr.SelectedValue);
            }

            if (CurrentEntity.ReportName.Contains("DRDRill Report"))
            {
                CurrentEntity.InfraObjectId = Convert.ToInt32(ddlInfraObj.SelectedValue);
            }

            if (ddlBussService.SelectedIndex > 0)
            {
                CurrentEntity.BusinessServiceId = Convert.ToInt32(ddlBussService.SelectedValue);
            }


            if (ddlfn.SelectedIndex > 0)
            {
                CurrentEntity.BusinessFunctionId = Convert.ToInt32(ddlfn.SelectedValue);
            }
            if (CurrentEntity.ReportName.Contains("DR Ready Report"))
            {

                if (ddlbusser.SelectedIndex > 0)
                {
                    CurrentEntity.BusinessServiceId = Convert.ToInt32(ddlbusser.SelectedValue);
                }
                else
                    CurrentEntity.BusinessServiceId = 0;
            }





            //foreach (ListItem item in ddlCheckEmailRec.Items)
            //{
            //    if (item.Selected)
            //        CurrentEntity.ReceiverId += item.Value + ";";
            //    // ids=ids+item.Value + ";";
            //}

            var receiverItems = Utility.GetSelectedItem(cblstGroup);

            string emails = string.Empty;

            if (receiverItems != null && receiverItems.Count > 0)
            {
                foreach (var listItem in receiverItems)
                {
                    if (listItem.Text != "All" && listItem.Value != Convert.ToString(0))
                    {

                        if (listItem.Text.Contains("("))
                        {
                            string[] val = listItem.Text.Split('(');

                            if (val.Length > 1)
                            {
                                string name = val[1];
                                if (!string.IsNullOrEmpty(name) && name.Contains(")"))
                                {
                                    string[] valone = name.Split(')');

                                    if (valone.Length > 0)
                                    {
                                        emails += valone[0] + ";";
                                    }
                                }
                            }

                        }
                         //emails += listItem.Text + ";" ;
                    }
                    //emails += listItem.Text + ";";
                }

            }

            CurrentEntity.ReceiverId = emails;




            //  if (divDaily.Visible == false
            if (timediv.Visible == false) //if (divDaily.Visible == true)
            {

                if (string.IsNullOrEmpty(txtDays.Text) && ddlHrs.SelectedItem.Text != "0" && ddlMins.SelectedItem.Text == "0")
                {
                    CurrentEntity.ScheduleTime = string.Format("0 {0} {1} 1/1 * ? *", ddlMins.SelectedItem.Text, ddlHrs.SelectedItem.Text);
                    CurrentEntity.Time = "0" + ":" + ddlHrs.SelectedItem + ":" + ddlMins.SelectedItem;
                }
                // Day=0; hrs=somevalue; mins=somevalue;
                else if (string.IsNullOrEmpty(txtDays.Text) && ddlHrs.SelectedItem.Text != "0" && ddlMins.SelectedItem.Text != "0")
                {
                    CurrentEntity.ScheduleTime = string.Format("0 {0} {1} 1/1 * ? *", ddlMins.SelectedItem.Text, ddlHrs.SelectedItem.Text);
                    CurrentEntity.Time = "0" + ":" + ddlHrs.SelectedItem + ":" + ddlMins.SelectedItem;
                }
                // Day=0; hrs=0; mins=somevalue;
                else if (string.IsNullOrEmpty(txtDays.Text) && ddlHrs.SelectedItem.Text == "0" && ddlMins.SelectedItem.Text != "0")
                {
                    CurrentEntity.ScheduleTime = string.Format("0 0/{0} * 1/1 * ? *", ddlMins.SelectedItem.Text);
                    CurrentEntity.Time = "0" + ":" + ddlHrs.SelectedItem + ":" + ddlMins.SelectedItem;
                }
                //Day=somevalue; hrs=0; mins=0;
                else if (!string.IsNullOrEmpty(txtDays.Text) && ddlHrs.SelectedItem.Text == "0" && ddlMins.SelectedItem.Text == "0")
                {
                    CurrentEntity.ScheduleTime = string.Format("0 {0} {1} 1/{2} * ? *", ddlMins.SelectedItem.Text, ddlHrs.SelectedItem.Text, txtDays.Text);
                    CurrentEntity.Time = txtDays.Text + ":" + ddlHrs.SelectedItem + ":" + ddlMins.SelectedItem;
                }
                //Day=somevalue; hrs=somevalue; mins=0;
                else if (!string.IsNullOrEmpty(txtDays.Text) && ddlHrs.SelectedItem.Text != "0" && ddlMins.SelectedItem.Text == "0")
                {
                    CurrentEntity.ScheduleTime = string.Format("0 {0} {1} 1/{2} * ? *", ddlMins.SelectedItem.Text, ddlHrs.SelectedItem.Text, txtDays.Text);
                    CurrentEntity.Time = txtDays.Text + ":" + ddlHrs.SelectedItem + ":" + ddlMins.SelectedItem;
                }
                //Day=somevalue; hrs=0; mins=somevalue;
                else if (!string.IsNullOrEmpty(txtDays.Text) && ddlHrs.SelectedItem.Text == "0" && ddlMins.SelectedItem.Text != "0")
                {
                    CurrentEntity.ScheduleTime = string.Format("0 {0} {1} 1/{2} * ? *", ddlMins.SelectedItem.Text, ddlHrs.SelectedItem.Text, txtDays.Text);
                    CurrentEntity.Time = txtDays.Text + ":" + ddlHrs.SelectedItem + ":" + ddlMins.SelectedItem;
                }
                //Day=somevalue; hrs=somevalue; mins=somevalue;
                else if (!string.IsNullOrEmpty(txtDays.Text) && ddlHrs.SelectedItem.Text != "0" && ddlMins.SelectedItem.Text != "0")
                {
                    CurrentEntity.ScheduleTime = string.Format("0 {0} {1} 1/{2} * ? *", ddlMins.SelectedItem.Text, ddlHrs.SelectedItem.Text, txtDays.Text);
                    CurrentEntity.Time = txtDays.Text + ":" + ddlHrs.SelectedItem + ":" + ddlMins.SelectedItem;
                }
            }

            String rpt_type = string.Empty;
            //if (CurrentEntity.ReportName.Contains("RPO SLA Report") || CurrentEntity.ReportName.Contains("DataSync Monitoring Report"))
            //{
            rpt_type = RadioButtonList1.SelectedItem.Text;
            CurrentEntity.Frequency = rpt_type;
            string cronstring = string.Empty;
            if (RadioButtonList1.SelectedValue == "Daily")
            {
                //cronstring = string.Format("0 {0} {1} 1/{2} * ? *", ddlminutes.SelectedValue, ddlhours.SelectedValue, txteverydaily.Text);
                cronstring = string.Format("0 {0} {1} 1/{2} * ? *", ddlminutes.SelectedValue, ddlhours.SelectedValue, "1");
                CurrentEntity.Time = "0" + ":" + ddlhours.SelectedValue + ":" + ddlminutes.SelectedValue;

            }
            else if (RadioButtonList1.SelectedValue == "Weekly")
            {
                cronstring = string.Format("00 {0} {1} ? * {2} *", ddlWeklyminutes.SelectedValue, ddlWeklyhours.SelectedValue, ddlDays.SelectedValue.Substring(0, 3));
                CurrentEntity.Time = "0" + ":" + ddlWeklyhours.SelectedValue + ":" + ddlWeklyminutes.SelectedValue;
            }
            else if (RadioButtonList1.SelectedValue == "Monthly")
            {
                if (rdbtn_Daytype.SelectedValue == "DOM")
                {
                    if (ddl_month.SelectedValue == "All")
                    {
                        if (Convert.ToInt32(txtday.Text) > 0 && Convert.ToInt32(txtday.Text) < 31)
                        {
                            cronstring = string.Format("0 {0} {1} {2} * ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                            CurrentEntity.Time = "0" + ":" + ddl_Hours1.SelectedValue + ":" + ddl_minutes1.SelectedValue;
                        }
                        else
                        { return; }
                    }
                    else
                    {
                        if (Convert.ToInt32(txtday.Text) > 0 && Convert.ToInt32(txtday.Text) < 31)
                        {
                            cronstring = GetCronstring(ddl_month.SelectedValue, txtday.Text, ddl_Hours1.SelectedValue, ddl_minutes1.SelectedValue);
                            CurrentEntity.Time = "0" + ":" + ddl_Hours1.SelectedValue + ":" + ddl_minutes1.SelectedValue;
                        }
                        else
                        {
                            return;
                        }
                    }
                }
                else if (rdbtn_Daytype.SelectedValue == "WKD")
                {
                    String sb = string.Empty;
                    var selectedItem = Utility.GetSelectedItem(cbl_weekdays);
                    foreach (var item in selectedItem)
                    {
                        sb = sb + item.Value + ",";
                    }

                    if (ddl_month.SelectedValue == "All" && sb.Contains("All"))
                    {
                        cronstring = string.Format("0 {0} {1} ? * *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        CurrentEntity.Time = "0" + ":" + ddl_Hours1.SelectedValue + ":" + ddl_minutes1.SelectedValue;
                    }
                    else if (ddl_month.SelectedValue == "All" && !sb.Contains("All"))
                    {
                        cronstring = string.Format("0 {0} {1} ? * {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb.Remove(sb.Length - 1, 1));
                        CurrentEntity.Time = "0" + ":" + ddl_Hours1.SelectedValue + ":" + ddl_minutes1.SelectedValue;
                    }
                    else if (ddl_month.SelectedValue != "All" && sb.Contains("All"))
                    {
                        cronstring = GetCronstring_wkdays(ddl_month.SelectedValue);
                        CurrentEntity.Time = "0" + ":" + ddl_Hours1.SelectedValue + ":" + ddl_minutes1.SelectedValue;
                    }
                    else if (ddl_month.SelectedValue != "All" && !sb.Contains("All"))
                    {
                        cronstring = GetCronstring_wkdays(ddl_month.SelectedValue, sb.Remove(sb.Length - 1, 1));
                        CurrentEntity.Time = "0" + ":" + ddl_Hours1.SelectedValue + ":" + ddl_minutes1.SelectedValue;
                    }
                }
            }
            else
            {
                //cronstring = string.Format("0 {0} {1} {3} ? * {2} *", ddlWeklyminutes.SelectedValue, ddlWeklyhours.SelectedValue, ddlDays.SelectedValue.Substring(0, 3), ddlDays.SelectedValue.Substring(0, 2));
                //cronstring = cronstring.Remove(0, 1);
                cronstring = string.Format("0 {0} {1} 1/{2} * ? *", ddlminutes.SelectedValue, ddlhours.SelectedValue, "1");
                CurrentEntity.Time = "0" + ":" + ddlhours.SelectedValue + ":" + ddlminutes.SelectedValue;
            }

            CurrentEntity.ScheduleTime = cronstring;
            //  }

            #region CronTime

            #endregion CronTime

        }
        protected string GetCronstring(string month, string day, string hour, string minutes)
        {
            try
            {
                string cronstring = string.Empty;

                switch (month)
                {
                    case "All":
                        cronstring = string.Format("0 {0} {1} {2} * ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "JAN":
                        cronstring = string.Format("0 {0} {1} {2} 1 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "FEB":
                        cronstring = string.Format("0 {0} {1} {2} 2 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "MAR":
                        cronstring = string.Format("0 {0} {1} {2} 3 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "APR":
                        cronstring = string.Format("0 {0} {1} {2} 4 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "MAY":
                        cronstring = string.Format("0 {0} {1} {2} 5 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "JUN":
                        cronstring = string.Format("0 {0} {1} {2} 6 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "JUL":
                        cronstring = string.Format("0 {0} {1} {2} 7 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "AUG":
                        cronstring = string.Format("0 {0} {1} {2} 8 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "SEP":
                        cronstring = string.Format("0 {0} {1} {2} 9 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "OCT":
                        cronstring = string.Format("0 {0} {1} {2} 10 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "NOV":
                        cronstring = string.Format("0 {0} {1} {2} 11 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    case "DEC":
                        cronstring = string.Format("0 {0} {1} {2} 12 ?", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, txtday.Text);
                        break;
                    default:
                        break;
                }

                return cronstring;

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        protected string GetCronstring_wkdays(string month)
        {
            try
            {
                string cronstring = string.Empty;

                switch (month)
                {
                    case "All":
                        //                          Sec	Min	Hr DOM Mon	DOW Year
                        cronstring = string.Format("0 {0} {1} ? * *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "JAN":
                        cronstring = string.Format("0 {0} {1} ? 1 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "FEB":
                        cronstring = string.Format("0 {0} {1} ? 2 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "MAR":
                        cronstring = string.Format("0 {0} {1} ? 3 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "APR":
                        cronstring = string.Format("0 {0} {1} ? 4 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "MAY":
                        cronstring = string.Format("0 {0} {1} ? 5 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "JUN":
                        cronstring = string.Format("0 {0} {1} ? 6 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "JUL":
                        cronstring = string.Format("0 {0} {1} ? 7 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "AUG":
                        cronstring = string.Format("0 {0} {1} ? 8 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "SEP":
                        cronstring = string.Format("0 {0} {1} ? 9 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "OCT":
                        cronstring = string.Format("0 {0} {1} ? 10 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "NOV":
                        cronstring = string.Format("0 {0} {1} ? 11 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    case "DEC":
                        cronstring = string.Format("0 {0} {1} ? 12 *", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue);
                        break;
                    default:
                        break;
                }

                return cronstring;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        protected string GetCronstring_wkdays(string month, string sb)
        {
            try
            {
                string cronstring = string.Empty;
                switch (month)
                {
                    case "All":
                        cronstring = string.Format("0 {0} {1} ? * {2} ", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "JAN":
                        cronstring = string.Format("0 {0} {1} ? 1 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "FEB":
                        cronstring = string.Format("0 {0} {1} ? 2 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "MAR":
                        cronstring = string.Format("0 {0} {1} ? 3 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "APR":
                        cronstring = string.Format("0 {0} {1} ? 4 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "MAY":
                        cronstring = string.Format("0 {0} {1} ? 5 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "JUN":
                        cronstring = string.Format("0 {0} {1} ? 6 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "JUL":
                        cronstring = string.Format("0 {0} {1} ? 7 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "AUG":
                        cronstring = string.Format("0 {0} {1} ? 8 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "SEP":
                        cronstring = string.Format("0 {0} {1} ? 9 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "OCT":
                        cronstring = string.Format("0 {0} {1} ? 10 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "NOV":
                        cronstring = string.Format("0 {0} {1} ? 11 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    case "DEC":
                        cronstring = string.Format("0 {0} {1} ? 12 {2}", ddl_minutes1.SelectedValue, ddl_Hours1.SelectedValue, sb);
                        break;
                    default:
                        break;
                }

                return cronstring;

            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public void BindListView()
        {
            //lvReportScheduler.DataSource = null;
            //IList<ReportSchedule> rptSch = Facade.GetAllReportScheduler();
            //if (rptSch == null)
            //    return;

            //lvReportScheduler.DataSource = rptSch;
            //lvReportScheduler.DataBind();



            IList<ReportSchedule> rptSch = Facade.GetAllReportScheduler();

            IList<ReportSchedule> ReportSchedulerfinal = new List<ReportSchedule>();
            if (rptSch != null && rptSch.Count > 0)
            {
                ReportSchedulerfinal = (from grp in rptSch where grp.CompanyId == LoggedInUserCompanyId select grp).ToList();
            }
            if (ReportSchedulerfinal == null && ReportSchedulerfinal.Count == 0)
            {
                return;

            }
            else
            {
                lvReportScheduler.DataSource = ReportSchedulerfinal;
                lvReportScheduler.DataBind();
            }


        }

        public void BindReportName(string name)
        {
            string reportname = ddlReport.SelectedItem.Text;

            IList<ReportSchedule> rptname = Facade.GetReportSchedulerByName(reportname);

            if (rptname == null)
            {


                //lvReportScheduler.DataSource = rptname;
                //lvReportScheduler.DataBind();
                ResetControlsValue();
                return;

            }
            else
            {
                lvReportScheduler.DataSource = rptname;
                lvReportScheduler.DataBind();
            }
        }

        public override void PrepareView()
        {
            // Validation for RPO SLA Calender Controls

            CalendarExtender1.EndDate = Convert.ToDateTime(DateTime.Now.Date).AddDays(-1);
            CalendarExtender2.EndDate = Convert.ToDateTime(DateTime.Now.Date).AddDays(-1);

            // Validation for DataSync Monitoring Report

            CalendarExtender3.EndDate = Convert.ToDateTime(DateTime.Now.Date).AddDays(-1);
            CalendarExtender4.EndDate = Convert.ToDateTime(DateTime.Now.Date).AddDays(-1);


            CalendarExtender5.EndDate = Convert.ToDateTime(DateTime.Now.Date).AddDays(-1);
            CalendarExtender6.EndDate = Convert.ToDateTime(DateTime.Now.Date).AddDays(-1);

            BindListView();

            //lvReportScheduler.Visible = false;
            lvReportScheduler.Visible = true;
            pnlAppName.Visible = false;
            pnlBussService.Visible = false;
            pnlDatalag.Visible = false;
            pnlInfrObj.Visible = false;
            pnlParllDROpr.Visible = false;
            pnlMonthlyRpt.Visible = false;
            divMontly.Visible = false;
            divstrtdt.Visible = false;
            pnlDrReadness.Visible = false;
            pnlDrReady.Visible = false;
            upnlTime.Visible = false;
            //  ddlCheckEmailRec.Items.Clear();
            cblstGroup.ClearItems();
            lblMsg.Text = "";
            BindReceiversList();
            if (IsUserOperator)
                divButtons.Visible = false;

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 
            rdbtn_Daytype.SelectedValue = "DOM";
        }

        public override void PrepareEditView()
        {

            timediv.Visible = false;
            if (Session["Id"] != null)
            {
                CurrentReportScheduleId = Convert.ToInt32(Session["Id"]);
                if (CurrentReportScheduleId > 0)
                {

                    BindControlsValue();
                    btnSave.Text = "Update";
                }
            }
        }

        protected void ddlType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlType.SelectedItem.Text == "Monthly")
            {
                divstrtdt.Visible = false;
                divMontly.Visible = true;
            }
            else
            {
                divstrtdt.Visible = true;
                divMontly.Visible = false;
            }
        }
        private void BindReceiversList()
        {
            var recList = Facade.GetAllAlertReceivers();

            //bind Receivers List check box list
            cblstGroup.ClearItems();
            if (recList != null && recList.Count > 0)
            {
                List<AlertReceiver> receiverList = new List<AlertReceiver>();
                foreach (var item in recList)
                {
                    AlertReceiver alr = new AlertReceiver();

                    string name = item.Name + "(" + item.EmailAddress + ")";
                    alr.Id = item.Id;
                    alr.EmailAddress = name;
                    receiverList.Add(alr);
                }

                if (receiverList != null && receiverList.Count > 0)
                {
                    cblstGroup.DataSource = receiverList;
                    cblstGroup.DataTextField = "EmailAddress";
                    cblstGroup.DataValueField = "Id";
                    cblstGroup.DataBind();
                    cblstGroup.Items.Insert(0, new ListItem("ALL", "0"));
                }
                else
                {
                    cblstGroup.Items.Insert(0, new ListItem("ALL", "0"));
                }
            }
            else
            {
                cblstGroup.Items.Insert(0, new ListItem("ALL", "0"));
            }
        }



        //private void BindControlsValue()
        //{
        //    // txtRpt.Text = CurrentEntity.ReportName;
        //    ddlReport.SelectItemByText(CurrentEntity.ReportName);
        //}

        private void BindControlsValue()
        {
            // txtRpt.Text = CurrentEntity.ReportName;
            //cblstGroup.Items.Insert(0, new ListItem("ALL", "0"));
            ddlReport.SelectItemByText(CurrentEntity.ReportName);

            ddlInfra.SelectedValue = Convert.ToString(CurrentEntity.InfraObjectId);

            var userGroupList = Facade.GetReportSchedulerById(CurrentEntity.Id);
            //cblstGroup.ClearItems();
            if (userGroupList != null)
            {
                var test = userGroupList.ReceiverId.Split(';');
                foreach (var listItem in from ListItem listItem in cblstGroup.Items from userGroupItem in test where listItem.Value == userGroupItem select listItem)
                {
                    listItem.Selected = false;
                    //_previousSelectedItems.Add(listItem);
                }
            }


            DWM_Div.Visible = true;
            DWM_Basis.Visible = true;
            timediv.Visible = false;
            RadioButtonList1.SelectedValue = CurrentEntity.Frequency;
            if (CurrentEntity.Frequency == "Daily")
            {
                Panel_Daily.Visible = true;
                Panel_Wekly.Visible = false;
                Panel_Monthly.Visible = false;
                if (CurrentEntity.ScheduleTime != null)
                {
                    //0 11 15 1/1 * ? *
                    string[] _lststr = (CurrentEntity.ScheduleTime).Split(' ');
                    if (_lststr != null)
                    {
                        if (_lststr[1] != null && Convert.ToInt32(_lststr[1]) > 0)
                        {
                            ddlminutes.SelectedValue = _lststr[1];
                        }
                        if (_lststr[2] != null && Convert.ToInt32(_lststr[2]) > 0)
                        {
                            ddlhours.SelectedValue = _lststr[2];
                        }
                    }
                }
            }
            else if (CurrentEntity.Frequency == "Weekly")
            {
                //DWM_Basis.Visible = true;
                Panel_Wekly.Visible = true;
                Panel_Daily.Visible = false;
                Panel_Monthly.Visible = false;

                //00 30 11 ? * MON *
                if (CurrentEntity.ScheduleTime != null)
                {
                    string[] _lststr = (CurrentEntity.ScheduleTime).Split(' ');
                    if (_lststr != null)
                    {
                        if (_lststr[1] != null && Convert.ToInt32(_lststr[1]) > 0)
                        {
                            ddlWeklyminutes.SelectedValue = _lststr[1];
                        }
                        if (_lststr[2] != null && Convert.ToInt32(_lststr[2]) > 0)
                        {
                            ddlWeklyhours.SelectedValue = _lststr[2];
                        }
                        if (_lststr[5] != null)
                        {
                            ddlDays.SelectedItem.Text = _lststr[5];
                        }
                    }
                }
            }
            else if (CurrentEntity.Frequency == "Monthly")
            {
                //DWM_Basis.Visible = true;
                Panel_Monthly.Visible = true;
                Panel_Daily.Visible = false;
                Panel_Wekly.Visible = false;

                //0 01 04 ? 2 MON ?
                //Sec-Min-Hr-DOM-Mon-DOW-Year
                if (!string.IsNullOrEmpty(CurrentEntity.ScheduleTime))
                {
                    string[] _lststr = (CurrentEntity.ScheduleTime).Split(' ');
                    if (_lststr != null)
                    {
                        if (_lststr[1] != null && Convert.ToInt32(_lststr[1]) > 0)
                        {
                            ddl_minutes1.SelectedValue = _lststr[1];
                        }
                        if (_lststr[2] != null && Convert.ToInt32(_lststr[2]) > 0)
                        {
                            ddl_Hours1.SelectedValue = _lststr[2];
                        }
                        if (_lststr[3] != null && _lststr[3] != "?")
                        {
                            rdbtn_Daytype.SelectedValue = "DOM";
                            if (_lststr[3] == "1/1")
                            {
                                txtday.Text = "0";
                            }
                            else
                            {
                                txtday.Text = _lststr[3];
                            }
                            pnl_daysofmonth.Visible = true;
                            pnl_weekdays.Visible = false;
                        }
                        else
                        {

                        }

                        if (_lststr[5] != null && _lststr[5] != "?")
                        {
                            rdbtn_Daytype.SelectedValue = "WKD";
                            string[] values = _lststr[5].Split(',');

                            for (int i = 0; i < values.Length; i++)
                            {
                                foreach (ListItem item in cbl_weekdays.Items)
                                {
                                    if (values[i] == item.Value)
                                    {
                                        item.Selected = true;
                                    }
                                    //item.Selected = values[i].Contains(item.Value);
                                }
                            }
                            pnl_daysofmonth.Visible = false;
                            pnl_weekdays.Visible = true;
                        }
                        else if (_lststr[5] != null && _lststr[5] == "*")
                        {
                            rdbtn_Daytype.SelectedValue = "WKD";
                            cbl_weekdays.SelectedValue = "All";
                            pnl_daysofmonth.Visible = false;
                            pnl_weekdays.Visible = true;
                        }
                    }
                }
            }
        }





        protected void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                //if (Page.IsValid && (ViewState["_token"] != null) && ValidateRequest("CreateReportScheduler", UserActionType.CreateReportScheduler))
                //{
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {
                    if (ddlReport.SelectedItem.Value == "0")
                    {
                        lblReportlist.Visible = true;
                        lblReportlist.Text = "Please Select Report";
                        return;
                    }

                    ValidateReportNameList();

                    if (_reportValidator == false)
                    {
                        lblReportlist.Visible = true;
                        lblReportlist.Text = "Select Report";
                    }
                    else
                    {
                        lblAdded.Text = string.Empty;
                        lblDatemsg.Text = string.Empty;
                        lblReportlist.Visible = false;

                        string dat = DateTime.Now.ToString("yyyy-MM-dd");
                        var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                        if (returnUrl.IsNullOrEmpty())
                        {
                            returnUrl = ReturnUrl;
                        }
                        var submitButton = (Button)sender;
                        var buttionText = " " + submitButton.Text.ToLower() + " ";
                        var currentTransactionType = TransactionType.Undefined;
                        if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                        {
                            currentTransactionType = TransactionType.Save;
                        }
                        else if (buttionText.Contains(" update "))
                        {
                            currentTransactionType = TransactionType.Update;
                        }
                        if (currentTransactionType == TransactionType.Update)
                        {
                            if (ddlReport.SelectedItem.Text == "Monthly Summary Report")
                            {
                            }
                        }

                        try
                        {
                            if (currentTransactionType != TransactionType.Undefined)
                            {
                                if (ddlReport.SelectedItem.Text == "Monthly Summary Report")
                                {
                                    if (ddlType.SelectedValue == "2")
                                    {
                                        DateTime dstartdate = FirstDayOfMonthFromDateTime(Convert.ToInt32(ddlYear.SelectedValue), Convert.ToInt32(ddlMonth.SelectedValue));
                                        DateTime dsEnddate = LastDayOfMonthFromDateTime(Convert.ToInt32(ddlYear.SelectedValue), Convert.ToInt32(ddlMonth.SelectedValue));

                                        if (dsEnddate.Year > DateTime.Now.Year)
                                        {
                                            lblMsg.Visible = true;
                                            lblMsg.Text = "Selected Year is Greater than Current Year";
                                            return;

                                        }
                                        if (dsEnddate.Year == DateTime.Now.Year)
                                        {
                                            if (dsEnddate.Month > DateTime.Now.Month)
                                            {
                                                lblMsg.Visible = true;
                                                lblMsg.Text = "Selected Month is Greater than Current Month";
                                                return;
                                            }


                                        }

                                    }
                                }
                                lblMsg.Text = "";

                                BuildEntities();
                                StartTransaction();
                                SaveEditor();
                                EndTransaction();
                                PrepareView();
                                upnlTime.Update();
                            }
                            //ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials,
                            //                                                                            currentTransactionType));
                        }
                        catch (CpException ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            ExceptionManager.Manage(ex, this);
                        }
                        catch (Exception ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                            {
                                ExceptionManager.Manage((CpException)ex.InnerException, this);
                            }
                            else
                            {
                                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                                ExceptionManager.Manage(customEx, this);
                            }
                        }

                        // Response.Redirect("../Report/ReportScheduler.aspx");
                        if (returnUrl.IsNotNullOrEmpty())
                        {
                            Helper.Url.Redirect(new SecureUrl(returnUrl));
                        }
                    }
                }
                //}
                //ClientScript.RegisterClientScriptBlock(Page.GetType(), "script", "window.close();", true);
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occured In btnSave_Click Event, Error Message " + ex.Message);
                if (ex.InnerException.Message != null)
                    _logger.Error("Exception Occured In btnSave_Click Event, InnerException Message " + ex.InnerException.Message);
            }

        }

        protected void ddlReport_SelectedIndexChanged(object sender, EventArgs e)
        {
            lvReportScheduler.Visible = true;
            lblDatemsg.Text = string.Empty;
            lblAdded.Text = string.Empty;
            lblReportlist.Visible = false;
            BindListView();
            Panel_Daily.Visible = true;
            Panel_Wekly.Visible = false;
            Panel_Monthly.Visible = false;
            switch (ddlReport.SelectedItem.Text)
            {
                case "- Select Report -":
                    BindListView();
                    break;

                case "RPO SLA Report":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = true;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;

                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;

                    // timediv.Visible = false;
                    BindRPOSLARptInafra();
                    //Utility.PopulateInfraObject(ddlInfra, true, LoggedInUserId, true);

                    BindReportName(ddlReport.SelectedItem.Text);
                    var recList = Facade.GetAllAlertReceivers();
                    //if (recList != null && recList.Count > 0)
                    //{
                    //    foreach (var item in recList)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}

                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = true;
                    RadioButtonList1.Items[2].Enabled = true;
                    break;

                case "InfraObject Summary":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = true;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    lblReportlist.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;
                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = false;
                    RadioButtonList1.Items[2].Enabled = false;
                    Utility.PopulateBusinessServiceReportSch(ddlBussServ, true);
                    // ddlBussServ.Items.Insert(0, new ListItem(" All ", "0", true));
                    BindReportName(ddlReport.SelectedItem.Text);
                    //ddlCheckEmailRec.DataSource = null;
                    //ddlCheckEmailRec.Items.Clear();
                    //var recList12 = Facade.GetAllAlertReceivers();
                    //if (recList12 != null && recList12.Count > 0)
                    //{
                    //    foreach (var item in recList12)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}

                    break;

                case "Group Summary":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = true;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = true;
                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;
                    upnlTime.Visible = false;

                    Utility.PopulateBusinessService(ddlBussServ, true);
                    BindReportName(ddlReport.SelectedItem.Text);
                    //var recList2 = Facade.GetAllAlertReceivers();
                    //if (recList2 != null && recList2.Count > 0)
                    //{
                    //    foreach (var item in recList2)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}
                    break;

                case "Parallel DROperation Report":
                    pnlParllDROpr.Visible = true;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    BindReportName(ddlReport.SelectedItem.Text);
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = true;
                    upnlTime.Visible = false;
                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;

                    //var recList3 = Facade.GetAllAlertReceivers();
                    //if (recList3 != null && recList3.Count > 0)
                    //{
                    //    foreach (var item in recList3)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}

                    break;

                case "DRDRill Report":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = true;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = true;
                    upnlTime.Visible = false;
                    Utility.PopulateInfraObject(ddlInfraObj, true, LoggedInUserId, true);
                    BindReportName(ddlReport.SelectedItem.Text);
                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;

                    ////var recList4 = Facade.GetAllAlertReceivers();
                    ////if (recList4 != null && recList4.Count > 0)
                    ////{
                    ////    foreach (var item in recList4)
                    ////    {
                    ////        string name = item.Name + "(" + item.EmailAddress + ")";
                    ////        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    ////    }
                    ////}

                    break;

                case "DataSync Monitoring Report":

                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = true;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;

                    divtxtStartDSM.Visible = false;
                    divtxtEndDSM.Visible = false;

                    timediv.Visible = false;
                    upnlTime.Visible = true;

                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;


                    upnlTime.Update();

                    //Pratik now PRReplicationId will go like a ReplicationId for service

                    //Utility.PopulateApplicationByIsReplication(ddlApp, true, LoggedInUserId, true);
                    IList<InfraObject> infraObject = new List<InfraObject>();
                    infraObject = Facade.GetAllInfraObject();

                    var InfraDB = (from infra in infraObject where infra.RecoveryType == 3 select infra).ToList();

                    if (InfraDB != null)
                    {
                        ddlApp.DataSource = InfraDB;
                        ddlApp.DataTextField = "Name";
                        ddlApp.DataValueField = "Id";
                        ddlApp.DataBind();
                        ddlApp.Items.Insert(0, new ListItem("Select InfraObject", "0"));
                    }

                    BindReportName(ddlReport.SelectedItem.Text);
                    // DWM_Div.Visible = false;
                    //  DWM_Basis.Visible = false;

                    //var recList5 = Facade.GetAllAlertReceivers();
                    //if (recList5 != null && recList5.Count > 0)
                    //{
                    //    foreach (var item in recList5)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = true;
                    RadioButtonList1.Items[2].Enabled = true;
                    break;

                case "Weekly Summary Report":

                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = true;

                    BindReportName(ddlReport.SelectedItem.Text);
                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;

                    //var recList6 = Facade.GetAllAlertReceivers();
                    //if (recList6 != null && recList6.Count > 0)
                    //{
                    //    foreach (var item in recList6)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}

                    break;

                case "Datalag Status Report":

                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    lblReportlist.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;
                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = false;
                    RadioButtonList1.Items[2].Enabled = false;

                    BindReportName(ddlReport.SelectedItem.Text);

                    //var recList7 = Facade.GetAllAlertReceivers();
                    //if (recList7 != null && recList7.Count > 0)
                    //{
                    //    foreach (var item in recList7)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}


                    break;

                case "Monthly Summary Report":

                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = true;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = true;
                    BindReportName(ddlReport.SelectedItem.Text);

                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;

                    //var recList8 = Facade.GetAllAlertReceivers();
                    //if (recList8 != null && recList8.Count > 0)
                    //{
                    //    foreach (var item in recList8)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}
                    break;

                case "DR Readiness Execution Log":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    pnlDrReadness.Visible = true;
                    lblAdded.Visible = false;
                    lblReportlist.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;
                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = false;
                    RadioButtonList1.Items[2].Enabled = false;

                    IList<BusinessService> businessServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);
                    if (businessServiceList != null)
                    {
                        ddlBussService.DataSource = businessServiceList;
                        ddlBussService.DataTextField = "Name";
                        ddlBussService.DataValueField = "Id";
                        ddlBussService.DataBind();
                        ddlBussService.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlBussService.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }

                    IList<BusinessFunction> businessFunctionList = new List<BusinessFunction>();
                    businessFunctionList = Facade.GetBusinessFunctionsByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);

                    if (businessFunctionList != null)
                    {
                        ddlfn.DataSource = businessFunctionList;
                        ddlfn.DataTextField = "Name";
                        ddlfn.DataValueField = "Id";
                        ddlfn.DataBind();
                        ddlfn.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlfn.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }

                    IList<InfraObject> Infralist = new List<InfraObject>();
                    Infralist = Facade.GetAllInfraObjectId();


                    if (Infralist != null)
                    {

                        ddlinfra1.DataSource = Infralist;
                        ddlinfra1.DataTextField = "Name";
                        ddlinfra1.DataValueField = "Id";
                        ddlinfra1.DataBind();
                        ddlinfra1.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlinfra1.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }

                    BindReportName(ddlReport.SelectedItem.Text);

                    // DWM_Div.Visible = false;
                    // DWM_Basis.Visible = false;

                    //var recList9 = Facade.GetAllAlertReceivers();
                    //if (recList9 != null && recList9.Count > 0)
                    //{
                    //    foreach (var item in recList9)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}
                    break;

                case "DR Ready Report":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = true;
                    lblAdded.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;
                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = false;
                    RadioButtonList1.Items[2].Enabled = false;
                   // IList<BusinessService> busiServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);
                   IList<BusinessService> busiServiceList = Facade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

                    if (busiServiceList != null)
                    {
                        busiServiceList = busiServiceList.OrderBy(a => a.Name).ToList();
                        ddlbusser.DataSource = busiServiceList;
                        ddlbusser.DataTextField = "Name";
                        ddlbusser.DataValueField = "Id";
                        ddlbusser.DataBind();
                        //ddlbusser.Items.Insert(0, new ListItem("- Select Application -", "0"));
                        ddlbusser.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlbusser.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }

                    BindReportName(ddlReport.SelectedItem.Text);
                    //  DWM_Div.Visible = false;
                    //  DWM_Basis.Visible = false;

                    //var recList10 = Facade.GetAllAlertReceivers();
                    //if (recList10 != null && recList10.Count > 0)
                    //{
                    //    foreach (var item in recList10)
                    //    {
                    //        string name = item.Name + "(" + item.EmailAddress + ")";
                    //        ddlCheckEmailRec.Items.Add(new ListItem(name, item.EmailAddress));
                    //    }
                    //}
                    break;



            }
            BindReceiversList();
        }

        private void BindRPOSLARptInafra()
        {
            try
            {
                IList<InfraObject> Infra = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

                if (Infra != null && Infra.Count > 0)
                {
                    /// Added by Vishal as per discussed with Kiran Sir for IDEA.
                    var InfraDB = (from infra in Infra where infra.RecoveryType != 0 && infra.RecoveryType != 3 && infra.RecoveryType != 1 select infra).ToList();

                    ddlInfra.DataSource = InfraDB;
                    ddlInfra.DataTextField = "Name";
                    ddlInfra.DataValueField = "Id";
                    ddlInfra.DataBind();
                    ddlInfra.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
                }
                else
                    ddlInfra.Items.Insert(0, new ListItem("No Infra Object Assigned", "0"));

            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In BindRPOSLARptInafra, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In BindRPOSLARptInafra, InnerException Message " + ex.InnerException.Message);
            }
        }

        public string PutName(object item)
        {
            string returnarray = string.Empty;

            if (item.ToString() != "")
            {
                string[] name = item.ToString().Split(';');
                foreach (string word in name)
                {
                    if (word != "")
                        //returnarray = returnarray + "," + word.ToString().Remove(item.ToString().IndexOf('@'));
                        returnarray = returnarray + "," + word.ToString().Remove(word.ToString().IndexOf('@'));
                }
                return returnarray.Remove(0, 1);

                //name = item.ToString().Remove(item.ToString().IndexOf('@'));
                //return name;
            }

            return "";
        }

        protected void lvReportScheduler_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            try
            {
                var lblId = (Label)e.Item.FindControl("Id");
                //var lbl= (Label)e.Item.FindControl("actionName");
                //var btnRun = (Button)e.Item.FindControl("ibtnStartAction");
                //var check = e.Item.FindControl("ChkRunAction") as CheckBox;
                switch (e.CommandName)
                {
                    case "Edit":

                        Session["Id"] = lblId.Text.ToInteger();

                        break;

                    case "Delete":
                        Session["Id"] = lblId.Text.ToInteger();

                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred In lvReportScheduler_ItemCommand, Error Messsage " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception occurred In lvReportScheduler_ItemCommand, Error Messsage " + ex.InnerException.Message);
            }
        }

        public void BindDailyWeeklyMonthlyValues(ReportSchedule rps)
        {
            if (rps.Frequency == "Daily")
            {
                Panel_Daily.Visible = true;
                Panel_Wekly.Visible = false;
                Panel_Monthly.Visible = false;
                RadioButtonList1.SelectedValue = "Daily";
                if (rps.Time.Contains(":"))
                {
                    string[] time = rps.Time.ToString().Split(':');
                    ddlhours.SelectedValue = time[1];
                    ddlminutes.SelectedValue = time[2];

                }



            }
            else if (rps.Frequency == "Weekly")
            {

                Panel_Daily.Visible = false;
                Panel_Wekly.Visible = true;
                Panel_Monthly.Visible = false;

                RadioButtonList1.SelectedValue = "Weekly";

                // ddlWeklyhours.SelectedIndex = 00;
                // ddlWeklyminutes.SelectedIndex = 00;
                ddlDays.SelectedIndex = 0;
                if (rps.Time.Contains(":"))
                {
                    string[] time = rps.Time.ToString().Split(':');
                    // ddlWeklyhours.SelectedIndex = Convert.ToInt32(time[1]);
                    // ddlWeklyminutes.SelectedIndex = Convert.ToInt32(time[2]);

                    ddlWeklyhours.SelectedValue = time[1];
                    ddlWeklyminutes.SelectedValue = time[2];

                }//ddlDays
                if (rps.ScheduleTime.Contains("SUN"))
                {
                    ddlDays.SelectedValue = "SUNDAY";
                }
                else if (rps.ScheduleTime.Contains("MON"))
                {
                    ddlDays.SelectedValue = "MONDAY";
                }
                else if (rps.ScheduleTime.Contains("TUE"))
                {
                    ddlDays.SelectedValue = "TUESDAY";
                }
                else if (rps.ScheduleTime.Contains("WED"))
                {
                    ddlDays.SelectedValue = "WEDNESDAY";
                }
                else if (rps.ScheduleTime.Contains("THU"))
                {
                    ddlDays.SelectedValue = "THURSDAY";
                }
                else if (rps.ScheduleTime.Contains("FRI"))
                {
                    ddlDays.SelectedValue = "FRIDAY";
                }
                else if (rps.ScheduleTime.Contains("SAT"))
                {
                    ddlDays.SelectedValue = "SATURDAY";
                }
            }
            else if (rps.Frequency == "Monthly")
            {
                Panel_Daily.Visible = false;
                Panel_Wekly.Visible = false;
                Panel_Monthly.Visible = true;
                RadioButtonList1.SelectedValue = "Monthly";

                ddlhours.SelectedIndex = 0;
                ddlminutes.SelectedIndex = 0;
                ddl_month.SelectedValue = "All";
                rdbtn_Daytype.SelectedValue = "DOM";
                pnl_daysofmonth.Visible = true;
                pnl_weekdays.Visible = false;
                txtday.Text = "0";
                cbl_weekdays.ClearSelection();

                //  ddl_Hours1.SelectedValue = "00";
                //  ddl_minutes1.SelectedValue = "00";

                if (rps.Time.Contains(":"))
                {
                    string[] time = rps.Time.ToString().Split(':');
                    ddl_Hours1.SelectedValue = time[1];
                    ddl_minutes1.SelectedValue = time[2];

                }

            }
            else
            {
                Panel_Daily.Visible = true;
                Panel_Wekly.Visible = false;
                Panel_Monthly.Visible = false;

                ddlhours.SelectedIndex = 00;
                ddlminutes.SelectedIndex = 00;

            }

        }

        public void BindDataToControlsById(int id)
        {
            ReportSchedule rps = Facade.GetReportSchedulerById(id);

            switch (rps.ReportName)
            {
                case "RPO SLA Report":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = true;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;

                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;


                    upnlTime.Update();
                    //  BindDailyWeeklyMonthlyValues(rps);


                    Utility.PopulateInfraObject(ddlInfra, true, LoggedInUserId, true);

                    //ddlReport.SelectedIndex = ddlReport.Items.IndexOf(new ListItem(rps.ReportName));
                    ddlReport.SelectItemByText(rps.ReportName);
                    ddlInfra.SelectedIndex = ddlInfra.Items.IndexOf(new ListItem(Facade.GetInfraObjectById(rps.InfraObjectId).Name, rps.InfraObjectId.ToString()));
                    //  txtstart.Text = rps.FromDate;
                    //  txtend.Text = rps.ToDate;

                    SelectEmailReceiversandTime(rps);

                    break;

                case "InfraObject Summary":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = true;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    lblReportlist.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    //timediv.Visible = true;
                    //upnlTime.Visible = false;
                    //DWM_Div.Visible = false;
                    //DWM_Basis.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;
                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = false;
                    RadioButtonList1.Items[2].Enabled = false;

                    //   Utility.PopulateBusinessService(ddlBussServ, true);

                    Utility.PopulateBusinessServiceReportSch(ddlBussServ, true);

                    //ddlReport.SelectedIndex = ddlReport.Items.IndexOf(new ListItem(rps.ReportName));
                    ddlReport.SelectItemByText(rps.ReportName);

                    if (rps.BusinessServiceId != 0)
                    {
                        ddlBussServ.SelectedIndex = ddlBussServ.Items.IndexOf(new ListItem(Facade.GetBusinessServiceById(rps.BusinessServiceId).Name, rps.BusinessServiceId.ToString()));
                    }
                    else
                    {
                        ddlBussServ.SelectedIndex = 0;

                    }
                    SelectEmailReceiversandTime(rps);

                    break;

                case "Group Summary":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = true;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = true;
                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;
                    upnlTime.Visible = false;

                    Utility.PopulateBusinessService(ddlBussServ, true);

                    //ddlReport.SelectedIndex = ddlReport.Items.IndexOf(new ListItem(rps.ReportName));
                    ddlReport.SelectItemByText(rps.ReportName);
                    ddlBussServ.SelectedIndex = ddlBussServ.Items.IndexOf(new ListItem(Facade.GetBusinessServiceById(rps.BusinessServiceId).Name, rps.BusinessServiceId.ToString()));
                    SelectEmailReceiversandTime(rps);

                    break;

                case "Datalag Status Report":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;
                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = false;
                    RadioButtonList1.Items[2].Enabled = false;
                    //Utility.PopulateGroup(ddlInfraObj, true, LoggedInUserId, true);                    
                    ddlReport.SelectItemByText(rps.ReportName);
                    //ddlInfraObj.SelectedIndex = ddlInfraObj.Items.IndexOf(new ListItem(Facade.GetGroupById(rps.GroupId).Name, rps.GroupId.ToString()));
                    //CurrentSelectedIndex = ddlInfraObj.Items.IndexOf(new ListItem(Facade.GetGroupById(rps.GroupId).Name, rps.GroupId.ToString()));
                    SelectEmailReceiversandTime(rps);
                    break;

                case "Parallel DROperation Report":
                    pnlParllDROpr.Visible = true;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = true;
                    upnlTime.Visible = false;
                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;

                    SelectEmailReceiversandTime(rps);
                    break;

                case "DRDRill Report":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = true;
                    pnlDatalag.Visible = false;
                    lblAdded.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    timediv.Visible = true;
                    upnlTime.Visible = false;
                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;

                    Utility.PopulateInfraObject(ddlInfraObj, true, LoggedInUserId, true);

                    //ddlReport.SelectedIndex = ddlInfra.Items.IndexOf(new ListItem(rps.ReportName));
                    ddlReport.SelectItemByText(rps.ReportName);
                    ddlInfraObj.SelectedIndex = ddlInfraObj.Items.IndexOf(new ListItem(Facade.GetInfraObjectById(rps.InfraObjectId).Name, rps.InfraObjectId.ToString()));

                    SelectEmailReceiversandTime(rps);
                    break;

                case "DataSync Monitoring Report":

                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = true;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    lblAdded.Visible = false;


                    divtxtStartDSM.Visible = false;
                    divtxtEndDSM.Visible = false;

                    timediv.Visible = false;
                    upnlTime.Visible = true;

                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;


                    upnlTime.Update();

                    ddlReport.SelectItemByText(rps.ReportName);
                    Utility.PopulateInfraObject(ddlApp, true, LoggedInUserId, true);

                    // var infraObject = Facade.GetInfraobjectByReplicationId(rps.InfraObjectId);
                    var infraObject = Facade.GetInfraObjectById(rps.InfraObjectId);

                    //   if (infraObject != null)
                    //   {
                    //       ddlApp.DataSource = infraObject;
                    //       ddlApp.DataTextField = "Name";
                    //       ddlApp.DataValueField = "Id";
                    //       ddlApp.DataBind();
                    //       ddlApp.Items.Insert(0, new ListItem("Select InfraObject", "0"));

                    //       //  ddlApp.SelectedIndex = ddlApp.Items.IndexOf(ddlApp.Items.FindByText("Name"));
                    //   }

                    ddlApp.SelectedIndex = ddlApp.Items.IndexOf(new ListItem(Facade.GetInfraObjectById(rps.InfraObjectId).Name, rps.InfraObjectId.ToString()));

                    IList<FastCopyJob> fcopyJobs = new List<FastCopyJob>();

                    var infraobj = Facade.GetInfraObjectById(Convert.ToInt32(rps.InfraObjectId));

                    fcopyJobs = Facade.GetFastcopyJobsByApplicationReplicationId(infraobj.PRReplicationId);

                    if (fcopyJobs != null)
                    {
                        ddlJobs.DataSource = fcopyJobs;
                        ddlJobs.DataTextField = "SourceDirectory";
                        ddlJobs.DataValueField = "Id";
                        ddlJobs.DataBind();
                        ddlJobs.Items.Insert(0, new ListItem("Select Job", "0"));
                    }
                    ddlJobs.SelectedValue = Convert.ToInt32(rps.JobId).ToString();
                    //  ddlJobs.SelectedIndex = ddlJobs.Items.IndexOf(new ListItem(Facade.GetFastCopyJobByAppId(rps.InfraObjectId).SourceDirectory, rps.Id.ToString()));




                    //IList<FastCopyJob> fcopyJobs = new List<FastCopyJob>();
                    //fcopyJobs.Add(Facade.GetFastCopyJobByAppId(rps.InfraObjectId));
                    //if (fcopyJobs != null)
                    //{
                    //       ddlJobs.DataSource = fcopyJobs;
                    //       ddlJobs.DataTextField = "SourceDirectory";
                    //       ddlJobs.DataValueField = "Id";
                    //       ddlJobs.DataBind();
                    //       ddlJobs.Items.Insert(0, new ListItem("Select Job", "0"));
                    //   }
                    //Utility.PopulateFastCopy(ddlJobs, true, LoggedInUserId, true);

                    //ddlJobs.SelectedIndex = ddlJobs.Items.IndexOf(new ListItem(Facade.GetFastCopyJobByAppId(rps.InfraObjectId).SourceDirectory, rps.Id.ToString()));

                    //IList<FastCopyJob> fastCopy = new List<FastCopyJob>();
                    //fastCopy = fa

                    //Utility.PopulateInfraObject(ddlApp, true, LoggedInUserId, true);
                    //Utility.PopulateApplicationByIsReplication(ddlApp, true, LoggedInUserId, true);
                    //ddlApp.SelectedIndex = ddlApp.Items.IndexOf(new ListItem(Facade.GetApplicationGroupsByReplicationId(rps.AppId)[0].Name, rps.AppId.ToString()));

                    //  BindJobs();

                    //  ddlJobs.SelectedIndex = ddlJobs.Items.IndexOf(new ListItem(Facade.GetFastCopyJobById(rps.InfraObjectId).SourceDirectory, rps.JobId.ToString()));

                    // txtStartDSM.Text = rps.FromDate;
                    // txtEndDSM.Text = rps.ToDate;
                    SelectEmailReceiversandTime(rps);

                    break;

                case "Weekly Summary Report":

                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;

                    ddlReport.SelectItemByText(rps.ReportName);
                    SelectEmailReceiversandTime(rps);
                    break;

                case "Monthly Summary Report":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    pnlMonthlyRpt.Visible = true;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = false;
                    lblAdded.Visible = false;

                    timediv.Visible = true;

                    DWM_Div.Visible = false;
                    DWM_Basis.Visible = false;
                    ddlReport.SelectItemByText(rps.ReportName);

                    if (rps.ReportType == "CustomPeriod")
                    {
                        ddlType.SelectedValue = "1";
                        //  txtMstart.Text = rps.FromDate;
                        //  txtMend.Text = rps.ToDate;
                        divstrtdt.Visible = true;
                        divMontly.Visible = false;
                    }
                    else
                    {
                        // DateTime dstartdate = Convert.ToDateTime(rps.FromDate);
                        // DateTime dsEnddate = Convert.ToDateTime(rps.ToDate);

                        //  ddlMonth.SelectedValue = dstartdate.Month.ToString();
                        //  ddlYear.SelectedValue = dstartdate.Year.ToString();


                        divstrtdt.Visible = false;
                        divMontly.Visible = true;
                        ddlType.SelectedValue = "2";

                    }


                    SelectEmailReceiversandTime(rps);

                    break;

                case "DR Readiness Execution Log":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = true;
                    pnlDrReady.Visible = false;
                    lblAdded.Visible = false;
                    lblReportlist.Visible = false;

                    timediv.Visible = false;
                    upnlTime.Visible = true;
                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = false;
                    RadioButtonList1.Items[2].Enabled = false;

                    ddlReport.SelectItemByText(rps.ReportName);

                    IList<BusinessService> businessServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);
                    if (businessServiceList != null)
                    {
                        ddlBussService.DataSource = businessServiceList;
                        ddlBussService.DataTextField = "Name";
                        ddlBussService.DataValueField = "Id";
                        ddlBussService.DataBind();
                        ddlBussService.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlBussService.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }

                    IList<BusinessFunction> businessFunctionList = new List<BusinessFunction>();
                    businessFunctionList = Facade.GetBusinessFunctionsByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);

                    if (businessFunctionList != null)
                    {
                        ddlfn.DataSource = businessFunctionList;
                        ddlfn.DataTextField = "Name";
                        ddlfn.DataValueField = "Id";
                        ddlfn.DataBind();
                        ddlfn.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlfn.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }

                    IList<InfraObject> Infralist = new List<InfraObject>();
                    Infralist = Facade.GetAllInfraObjectId();


                    if (Infralist != null)
                    {

                        ddlinfra1.DataSource = Infralist;
                        ddlinfra1.DataTextField = "Name";
                        ddlinfra1.DataValueField = "Id";
                        ddlinfra1.DataBind();
                        ddlinfra1.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlinfra1.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }


                    SelectEmailReceiversandTime(rps);

                    break;

                case "DR Ready Report":
                    pnlParllDROpr.Visible = false;
                    pnlAppName.Visible = false;
                    pnlBussService.Visible = false;
                    pnlInfrObj.Visible = false;
                    pnlDatalag.Visible = false;
                    pnlMonthlyRpt.Visible = false;
                    pnlDrReadness.Visible = false;
                    pnlDrReady.Visible = true;
                    lblAdded.Visible = false;
                    timediv.Visible = false;
                    upnlTime.Visible = true;
                    DWM_Div.Visible = true;
                    DWM_Basis.Visible = true;
                    RadioButtonList1.Items[0].Enabled = true;
                    RadioButtonList1.Items[1].Enabled = false;
                    RadioButtonList1.Items[2].Enabled = false;
                    IList<BusinessService> busiServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);

                    if (busiServiceList != null)
                    {
                        busiServiceList = busiServiceList.OrderBy(a => a.Name).ToList();
                        ddlbusser.DataSource = busiServiceList;
                        ddlbusser.DataTextField = "Name";
                        ddlbusser.DataValueField = "Id";
                        ddlbusser.DataBind();
                        ddlbusser.Items.Insert(0, "ALL");
                        //ddlbusser.Items.Insert(0, new ListItem("- Select Application -", "0"));
                        //ddlBussService.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlbusser.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }

                    SelectEmailReceiversandTime(rps);

                    break;

            }
        }

        protected void lvReportScheduler_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            // btnSave.Text = "Update";
            // lblDatemsg.Text = string.Empty;
            // lblAdded.Text = string.Empty;

            //for (int i = 0; i < ddlCheckEmailRec.Items.Count; i++)
            //{
            //    ddlCheckEmailRec.Items[i].Selected = false;
            //}
            //if (Session["Id"] != null)
            //{
            //    PrepareEditView();
            //    BindDataToControlsById(Convert.ToInt32(Session["Id"]));
            //}
            //// BindListView();
            //// BindReportName(ddlReport.SelectedItem.Text);

            try
            {
                // var secureUrl = new SecureUrl(RedirectURL);

                // var lblreportScheduledId = (lvReportScheduler.Items[e.NewEditIndex].FindControl("Id")) as Label;
                // var lblname = (lvReportScheduler.Items[e.NewEditIndex].FindControl("ReportName")) as Label;

                //ActivityLogger.AddLog(LoggedInUserName, "ReportScheduler", UserActionType.UpdateReportScheduler, "The NotificationManager '" + lblname.Text +
                //                          "' Opened as Editing Mode ", LoggedInUserId);
                //    if (lblreportScheduledId != null && ValidateRequest("ReportScheduler Edit", UserActionType.UpdateReportScheduler))
                //    {
                //        secureUrl = UrlHelper.BuildSecureUrl(RedirectURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                //        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReportScheduleId, lblreportScheduledId.Text);
                //        Helper.Url.Redirect(secureUrl);
                //    }

                btnSave.Text = "Update";
                lblDatemsg.Text = string.Empty;
                lblAdded.Text = string.Empty;

                //for (int i = 0; i < ddlCheckEmailRec.Items.Count; i++)
                //{
                //    ddlCheckEmailRec.Items[i].Selected = false;
                //}

                for (int i = 0; i < cblstGroup.Items.Count; i++)
                {
                    cblstGroup.Items[i].Selected = false;
                }
                if (Session["Id"] != null)
                {
                    PrepareEditView();
                    BindDataToControlsById(Convert.ToInt32(Session["Id"]));
                }
            }
            catch (Exception ex)
            {


                _logger.Error("Exception occurred In lvReportScheduler_ItemEditing, Error Messsage " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception occurred In lvReportScheduler_ItemEditing, Error Messsage " + ex.InnerException.Message);


            }

            // BindListView();
            // BindReportName(ddlReport.SelectedItem.Text);
        }



        public void SelectEmailReceiversandTime(ReportSchedule rps)
        {
            string[] receivers = rps.ReceiverId.Split(';');
            if (receivers.Count() == cblstGroup.Items.Count)
            {
                if (string.IsNullOrEmpty(receivers[receivers.Length - 1]))
                {
                    receivers[receivers.Length - 1] = "ALL";
                }
            }
            for (int i = 0; i < receivers.Length; i++)
            {
                if (receivers[i] != "")
                {
                    for (int j = 0; j < cblstGroup.Items.Count; j++)
                    {
                        if (cblstGroup.Items[j].Text.Contains(receivers[i]))
                            cblstGroup.Items[j].Selected = true;
                    }
                }

            }

            string[] time = rps.Time.Split(':');
            // txtDays.Text = time[0];
            //  txtDays.Text = string.Empty;// provide Empty becuase cron issue while update for daily basis.
            //  ddlHrs.SelectedIndex = ddlHrs.Items.IndexOf(new ListItem(time[1]));
            //  ddlMins.SelectedIndex = ddlMins.Items.IndexOf(new ListItem(time[2]));
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            // ddlCheckEmailRec.Items.Clear();
            cblstGroup.ClearItems();
            txtDays.Text = "";
            ddlHrs.SelectedValue = "0";
            ddlMins.SelectedValue = "0";
            PrepareView();
            btnSave.Text = "Save";
            lblAdded.Visible = false;
            ddlReport.SelectedValue = "0";

            //Response.Redirect("../Report/ReportManagement.aspx");

            //Response.Write("<script language='javascript'>window.opener='Self'; window.close();</script>");



        }

        protected void lvReportScheduler_ItemDeleting1(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                //   if (Session["Id"] != null && ValidateRequest("DeleteReportScheduler", UserActionType.DeleteReportScheduler))
                if (Session["Id"] != null)
                {
                    _logger.Info("Report Scheduler Id - " + Convert.ToInt32(Session["Id"]));
                    Facade.DeleteReportSchedulerById(Convert.ToInt32(Session["Id"]));

                    BindListView();
                    // Session["getval"] = 0;

                    Response.Redirect("../Report/ReportScheduler.aspx", false);
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occured In lvReportScheduler_ItemDeleting1 Event, Error Message " + ex.Message);
                if (ex.InnerException.Message != null)
                    _logger.Error("Exception Occured In lvReportScheduler_ItemDeleting1 Event, InnerException Message " + ex.InnerException.Message);
            }
        }

        protected void ddlApp_SelectedIndexChanged(object sender, EventArgs e)
        {
            //ApplicationGroup appGrp= Facade.GetApplcationGroupById(Convert.ToInt32(ddlApp.SelectedValue));
            ////appGrp.ReplicationId

            //FastCopy fc= Facade.GetFastCopyByReplicationId(appGrp.ReplicationId);

            //FastCopyJob fcJob =  Facade.GetFastCopyJobById(fc.Id);

            ////get app id
            ////get rep id
            ////get fascopyid
            ////get fastcopyjobid and populate ddl job

            // if (Convert.ToInt32(ddlApp.SelectedItem.Value) > 0)
            // {
            BindJobs();
            //}
        }


        protected void DdlCompanySelectedIndexChanged(object sender, EventArgs e)
        {
            if (Convert.ToInt32(ddlApp.SelectedItem.Value) > 0)
            {
                BindJobs();
            }
        }

        public void BusinessSerDrReady()
        {
            CalendarExtender5.StartDate = DateTime.Now.Date;
            CalendarExtender6.EndDate = DateTime.Now.Date;
            IList<BusinessService> businessServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);
            if (businessServiceList != null)
            {
                ddlBussService.DataSource = businessServiceList;
                ddlBussService.DataTextField = "Name";
                ddlBussService.DataValueField = "Id";
                ddlBussService.DataBind();
                ddlBussService.Items.Insert(0, "ALL");
            }
            else
            {
                ddlBussService.Items.Insert(0, new ListItem("No Data Found", "0"));
            }
        }


        public void BindJobs()
        {
            IList<FastCopyJob> fcopyJobs = new List<FastCopyJob>();

            var infraobj = Facade.GetInfraObjectById(Convert.ToInt32(ddlApp.SelectedItem.Value));

            fcopyJobs = Facade.GetFastcopyJobsByApplicationReplicationId(infraobj.PRReplicationId);

            if (fcopyJobs != null)
            {
                ddlJobs.DataSource = fcopyJobs;
                ddlJobs.DataTextField = "SourceDirectory";
                ddlJobs.DataValueField = "Id";
                ddlJobs.DataBind();

                ListItem list = ddlJobs.Items.FindByValue("0");
                if (list == null)
                    ddlJobs.Items.Insert(0, new ListItem("- Select Job -", "0"));
            }
        }
        //protected void ddlCheckEmailRec_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    foreach (ListItem item in ddlCheckEmailRec.Items)
        //    {
        //        if (item.Selected)
        //        {
        //            lblEmailErr.Visible = false;
        //        }
        //        // ids=ids+item.Value + ";";
        //    }
        //}



        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            try
            {
                int i = Convert.ToInt32(Session["getval"]);
                if (i == 0)
                {
                    ViewState["_token1"] = ViewState["_token"];

                }
                if (i >= 1)
                {

                    ViewState["_token"] = ViewState["_token1"];

                }
                i++;
                Session["getval"] = i;

                if ((ViewState["_token"] != null))
                {
                    _logger.Info("Token Is Not Null.");
                    if (!IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                    {
                        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                        Logout _Logout = new Logout();
                        _Logout.PrepareView();
                        return false;
                    }
                }
                else
                {
                    _logger.Info("Token Getting Null.");
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occured In ValidateRequest Event, Error Message " + ex.Message);
                if (ex.InnerException.Message != null)
                    _logger.Error("Exception Occured In ValidateRequest Event, InnerException Message " + ex.InnerException.Message);

                return false;
            }
        }

        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        public static bool IsTokenValidated(string tokenKey)
        {
            string tokenvalue = string.Empty;
            bool isValidToken = false;

            if (ConfigurationManager.AppSettings["IsSecurityTokenEnabled"].Equals("true", StringComparison.OrdinalIgnoreCase))
            {
                if (tokenKey != null)
                {
                    foreach (string key in HttpContext.Current.Request.Form.AllKeys)
                    {
                        if (key.Equals("ctl00$cphBody$hdtokenKey", StringComparison.OrdinalIgnoreCase))
                        {
                            tokenvalue = HttpContext.Current.Request.Form[key].ToString();
                            break;
                        }
                        if (key.Equals("hdtokenKey", StringComparison.OrdinalIgnoreCase))
                        {
                            tokenvalue = HttpContext.Current.Request.Form[key].ToString();
                            break;
                        }
                    }
                }


                if (!string.IsNullOrEmpty(tokenvalue))
                {
                    if (!string.IsNullOrEmpty(tokenKey))
                    {
                        if (tokenKey.Equals(tokenvalue))
                        {
                            isValidToken = true;
                        }

                    }
                }
            }
            else
            {
                isValidToken = true;
            }
            return isValidToken;
        }


        protected void ddlBussService_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                lblMsg.Text = string.Empty;
                lblMsg.Visible = false;
                txtstart1.Text = string.Empty;
                txtend1.Text = string.Empty;


                ddlfn.DataSource = null;
                ddlfn.DataBind();

                ddlinfra1.DataSource = null;
                ddlinfra1.DataBind();

                if ((ddlBussService.SelectedItem.Text).ToLower() != "all")
                {

                    dvbf.Visible = true;
                    IList<BusinessFunction> businessFunctionList = new List<BusinessFunction>();

                    businessFunctionList = Facade.GetBusinessFunctionsByBusinessServiceId(Convert.ToInt32(ddlBussService.SelectedValue.ToString()));

                    if (businessFunctionList != null)
                    {
                        ddlfn.DataSource = businessFunctionList;
                        ddlfn.DataTextField = "Name";
                        ddlfn.DataValueField = "Id";
                        ddlfn.DataBind();
                        ddlfn.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlfn.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }

                    var InfraList = Facade.GetAllInfraObject();

                    if (InfraList != null && InfraList.Count > 0)
                    {
                        if (ddlBussService.SelectedValue != "0")
                        {
                            var infralist_1 = (from infra in InfraList
                                               where infra.BusinessServiceId == Convert.ToInt32(ddlBussService.SelectedValue)
                                               select infra).ToList();
                            if (infralist_1 != null && infralist_1.Count > 0)
                            {
                                ddlinfra1.DataSource = infralist_1;
                                ddlinfra1.DataTextField = "Name";
                                ddlinfra1.DataValueField = "Id";
                                ddlinfra1.DataBind();
                                ddlinfra1.Items.Insert(0, "ALL");
                            }
                            else
                            {
                                ddlinfra1.Items.Insert(0, new ListItem("No Infraobjects Found", "0"));
                            }
                        }
                        else
                        {
                            ddlinfra1.DataSource = InfraList;
                            ddlinfra1.DataTextField = "Name";
                            ddlinfra1.DataValueField = "Id";
                            ddlinfra1.DataBind();
                            ddlinfra1.Items.Insert(0, "ALL");
                        }
                    }

                }
                else
                {
                    dvbf.Visible = false;
                    dvinfra.Visible = false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ddlBussService_SelectedIndexChanged Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ddlBussService_SelectedIndexChanged Event, InnerException Message " + ex.InnerException.Message);


            }

        }

        protected void ddlfn_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {

                lblMsg.Text = string.Empty;
                lblMsg.Visible = false;
                txtstart1.Text = string.Empty;
                txtend1.Text = string.Empty;

                ddlinfra1.DataSource = null;
                ddlinfra1.DataBind();

                if ((ddlfn.SelectedItem.Text).ToLower() != "all")
                {

                    dvinfra.Visible = true;
                    IList<InfraObject> Infralist = new List<InfraObject>();

                    Infralist = Facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(Convert.ToInt32(ddlBussService.SelectedValue.ToString()), (Convert.ToInt32(ddlfn.SelectedValue.ToString())));

                    if (Infralist != null)
                    {

                        ddlinfra1.DataSource = Infralist;
                        ddlinfra1.DataTextField = "Name";
                        ddlinfra1.DataValueField = "Id";
                        ddlinfra1.DataBind();
                        ddlinfra1.Items.Insert(0, "ALL");
                    }
                    else
                    {
                        ddlinfra1.Items.Insert(0, new ListItem("No Data Found", "0"));
                    }
                }

                else
                {
                    dvinfra.Visible = false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ddlfn_SelectedIndexChanged Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ddlfn_SelectedIndexChanged Event, InnerException Message " + ex.InnerException.Message);

            }
        }

        protected void ddlinfra1_SelectedIndexChanged(object sender, EventArgs e)
        {
            txtstart1.Text = string.Empty;
            txtend1.Text = string.Empty;
        }


        protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
        {
            upnlTime.Update();

            if (RadioButtonList1.SelectedValue == "Daily")
            {
                Panel_Daily.Visible = true;
                Panel_Wekly.Visible = false;
                Panel_Monthly.Visible = false;

                ddlhours.SelectedIndex = 00;
                ddlminutes.SelectedIndex = 00;


            }
            else if (RadioButtonList1.SelectedValue == "Weekly")
            {

                Panel_Daily.Visible = false;
                Panel_Wekly.Visible = true;
                Panel_Monthly.Visible = false;

                ddlWeklyhours.SelectedIndex = 00;
                ddlWeklyminutes.SelectedIndex = 00;
                ddlDays.SelectedIndex = 0;
            }
            else if (RadioButtonList1.SelectedValue == "Monthly")
            {
                Panel_Daily.Visible = false;
                Panel_Wekly.Visible = false;
                Panel_Monthly.Visible = true;


                ddlhours.SelectedIndex = 0;
                ddlminutes.SelectedIndex = 0;
                ddl_month.SelectedValue = "All";
                rdbtn_Daytype.SelectedValue = "DOM";
                pnl_daysofmonth.Visible = true;
                pnl_weekdays.Visible = false;
                txtday.Text = "0";
                cbl_weekdays.ClearSelection();

                ddl_Hours1.SelectedValue = "00";
                ddl_minutes1.SelectedValue = "00";

            }
            else
            {
                Panel_Daily.Visible = true;
                Panel_Wekly.Visible = false;
                Panel_Monthly.Visible = false;

                ddlhours.SelectedIndex = 00;
                ddlminutes.SelectedIndex = 00;

            }
        }


        protected void rdbtn_Daytype_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (rdbtn_Daytype.SelectedValue == "DOM")
                {
                    pnl_daysofmonth.Visible = true;
                    pnl_weekdays.Visible = false;
                }
                else if (rdbtn_Daytype.SelectedValue == "WKD")
                {
                    pnl_daysofmonth.Visible = false;
                    pnl_weekdays.Visible = true;
                }
                else
                {
                    rdbtn_Daytype.SelectedValue = "DOM";
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }
        protected void cbl_weekdays_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                string result = Request.Form["__EVENTTARGET"];
                string[] checkedBox = result.Split('$');
                int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);
                ListItem testChkBx = cbl_weekdays.Items[checkedIndex];
                if (checkedIndex > 0)
                {
                    if (!testChkBx.Selected)
                    {

                        if (checkedIndex == 0)
                        {
                            if (cbl_weekdays.Items[0].Selected)
                            {
                                for (int i = 1; i < cbl_weekdays.Items.Count; i++)
                                {
                                    cbl_weekdays.Items[i].Selected = true;
                                    cbl_weekdays.Items[i].Enabled = true;
                                }
                            }
                            else
                            {
                                for (int i = 1; i < cbl_weekdays.Items.Count; i++)
                                {
                                    cbl_weekdays.Items[i].Selected = false;
                                    cbl_weekdays.Items[i].Enabled = true;
                                }
                            }
                        }
                        else
                        {
                            CheckBoxListAllSelected(cbl_weekdays);
                        }

                    }
                    else
                    {
                        btnSave.Enabled = true;

                        if (checkedIndex == 0)
                        {
                            if (cbl_weekdays.Items[0].Selected)
                            {
                                for (int i = 1; i < cbl_weekdays.Items.Count; i++)
                                {
                                    cbl_weekdays.Items[i].Selected = true;
                                    cbl_weekdays.Items[i].Enabled = true;
                                }
                            }
                            else
                            {
                                for (int i = 1; i < cbl_weekdays.Items.Count; i++)
                                {


                                    cbl_weekdays.Items[i].Selected = false;
                                    cbl_weekdays.Items[i].Enabled = true;
                                }
                            }
                        }
                        else
                        {
                            CheckBoxListAllSelected(cbl_weekdays);


                        }
                    }
                }
                else
                {
                    btnSave.Enabled = true;

                    if (checkedIndex == 0)
                    {
                        if (cbl_weekdays.Items[0].Selected)
                        {
                            for (int i = 1; i < cbl_weekdays.Items.Count; i++)
                            {
                                cbl_weekdays.Items[i].Selected = true;
                                cbl_weekdays.Items[i].Enabled = true;
                            }
                        }
                        else
                        {
                            for (int i = 1; i < cbl_weekdays.Items.Count; i++)
                            {

                                btnSave.Enabled = true;
                                cbl_weekdays.Items[i].Selected = false;
                                cbl_weekdays.Items[i].Enabled = true;

                            }
                        }
                    }
                    else
                    {
                        CheckBoxListAllSelected(cbl_weekdays);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        private void CheckBoxListAllSelected(CheckBoxList grpOrAppList)
        {
            try
            {
                int selectedKwt = 0;
                for (int i = 1; i < grpOrAppList.Items.Count; i++)
                {
                    if (grpOrAppList.Items[i].Selected)
                    {
                        selectedKwt++;
                    }
                    grpOrAppList.Items[i].Enabled = true;
                }

                if (selectedKwt == grpOrAppList.Items.Count - 1)
                {
                    grpOrAppList.Items[0].Selected = true;
                }
                else
                {
                    grpOrAppList.Items[0].Selected = false;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        protected void txtday_TextChanged(object sender, EventArgs e)
        {
            try
            {
                //if (Convert.ToInt32(txtday.Text) != 0 && Convert.ToInt32(txtday.Text) <= 31)
                //{
                //    lblerrormsg.Visible = false;
                //    btnSave.Enabled = true;
                //}
                //else
                //{
                //    lblerrormsg.Visible = true;
                //    lblerrormsg.Text = "Please Enter Days between 1 to 31";
                //    return;
                //}
            }
            catch (Exception ex)
            {
                throw (ex);
            }
        }

        protected void cblstGroup_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                //lbldrodata.Visible = false;
                //lbldroperation.Visible = false;
                //upnldroperdata.Update();
                string doString = string.Empty;

                string result = Request.Form["__EVENTTARGET"];
                string[] checkedBox = result.Split('$');
                int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);
                ListItem testChkBx = cblstGroup.Items[checkedIndex];


                if (checkedIndex > 0)
                {
                    if (!testChkBx.Selected)
                    {

                        if (checkedIndex == 0)
                        {
                            if (cblstGroup.Items[0].Selected)
                            {
                                for (int i = 1; i < cblstGroup.Items.Count; i++)
                                {
                                    cblstGroup.Items[i].Selected = true;
                                    cblstGroup.Items[i].Enabled = true;
                                }
                            }
                            else
                            {
                                for (int i = 1; i < cblstGroup.Items.Count; i++)
                                {
                                    cblstGroup.Items[i].Selected = false;
                                    cblstGroup.Items[i].Enabled = true;
                                }
                            }
                        }
                        else
                        {
                            CheckBoxListAllSelected2(cblstGroup);
                        }

                    }
                    else
                    {

                        if (checkedIndex == 0)
                        {
                            if (cblstGroup.Items[0].Selected)
                            {
                                for (int i = 1; i < cblstGroup.Items.Count; i++)
                                {
                                    cblstGroup.Items[i].Selected = true;
                                    cblstGroup.Items[i].Enabled = true;
                                }
                            }
                            else
                            {
                                for (int i = 1; i < cblstGroup.Items.Count; i++)
                                {


                                    cblstGroup.Items[i].Selected = false;
                                    cblstGroup.Items[i].Enabled = true;
                                }
                            }
                        }
                        else
                        {
                            CheckBoxListAllSelected2(cblstGroup);


                        }
                    }
                }
                else
                {


                    if (checkedIndex == 0)
                    {
                        if (cblstGroup.Items[0].Selected)
                        {
                            for (int i = 1; i < cblstGroup.Items.Count; i++)
                            {
                                cblstGroup.Items[i].Selected = true;
                                cblstGroup.Items[i].Enabled = true;
                            }
                        }
                        else
                        {
                            for (int i = 1; i < cblstGroup.Items.Count; i++)
                            {

                                cblstGroup.Items[i].Selected = false;
                                cblstGroup.Items[i].Enabled = true;

                            }
                        }
                    }
                    else
                    {
                        CheckBoxListAllSelected2(cblstGroup);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Error Occured While Selecting Receivers List On Report Scheduler Page " + ex.Message);
            }
        }

        private void CheckBoxListAllSelected2(CheckBoxList grpOrAppList)
        {
            try
            {
                int selectedKwt = 0;

                for (int i = 1; i < grpOrAppList.Items.Count; i++)
                {
                    if (grpOrAppList.Items[i].Selected)
                    {
                        selectedKwt++;
                    }
                    grpOrAppList.Items[i].Enabled = true;
                }

                if (selectedKwt == grpOrAppList.Items.Count - 1)
                {
                    grpOrAppList.Items[0].Selected = true;
                }
                else
                {
                    grpOrAppList.Items[0].Selected = false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Error occured while checkbox list selecting all item on Report Scgeduler page " + ex.Message);
            }

        }

        private bool ValidateReceiverList()
        {
            if (cblstGroup.SelectedItem == null)
            {
                return false;
            }
            else { lblEmailErr.Visible = false; }
            return true;
        }
    }
}








