﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="AdminSetting.aspx.cs" Inherits="CP.UI.Admin.AdminSetting" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style type="text/css">
        .strong {
            padding-top: 5px;
        }

        .widget-tabs-vertical ul li.active a {
            background-color: white;
            margin-right: -2px;
            z-index: 9;
        }

        .widget.widget-tabs-double > .widget-head ul li a, .widget.widget-tabs-double > .widget-head ul li.active a {
            height: 39px;
        }

        #tab-password {
            background-color: #4a8bc2;
        }

        .glyphicons.keys i:before {
            color: #ffffff !important;
        }

        .strongsub {
            padding-left: 38px;
        }

        .badge {
            background: none repeat scroll 0 0 #fff;
            margin-top: 3px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

      <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
            <img src="../Images/icons/icon-settings.png" height="25" width="25" />
            Settings</h3>
        <div class="relativeWrap">
            <div class="widget widget-tabs widget-tabs-double widget-tabs-vertical row row-merge">


                <div class="widget-head col-md-3">
                    <ul>
                        <li class="active"><a data-toggle="tab" class="glyphicons notes" href="#tab1-1"><i></i><span class="strong">Report</span></a></li>
                        <li class=""><a data-toggle="tab" class="glyphicons notes" href="#tab7-1"><i></i><span class="strong">Log</span></a></li>
                        <li class=""><a data-toggle="tab" class="glyphicons database_lock" href="#tab2-1"><i></i><span class="strong">Database</span></a></li>
                        <li class=""><a data-toggle="tab" class="glyphicons database_lock" href="#tab6-1"><i></i><span class="strong">Session Time Out</span></a></li>
                        <li class="" id="tab-password"><a data-toggle="tab" style="color: #ffffff !important; background-color: #4a8bc2 !important;" href="#tab4-1" class="glyphicons keys"><i></i><span class="strong" style="font-weight: bold; display: inline-block">Password</span><span class="badge badge-default pull-right">2</span></a></li>
                        <li class="tab-psd-child"><a data-toggle="tab" href="#tab4-1"><span class="strong strongsub">Password Policy</span></a></li>
                        <li class="tab-psd-child"><a data-toggle="tab" href="#tab5-1"><span class="strong strongsub">Password Age</span></a></li>
                        <li id="servdetails" runat="server" class=""><a data-toggle="tab" class="glyphicons notes" href="#tab8-1"><i></i><span class="strong">Server Utilization Count</span></a></li>
                    </ul>
                </div>


                <div class="widget-body col-md-9">
                    <div class="tab-content">


                        <div id="tab1-1" class="tab-pane active">
                            <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <asp:Panel ID="SuccessMsg" class="PanelNotificationBox PanelSuccess AutoHide" runat="server" Visible="false">
                                        <asp:Label ID="lblpath" Visible="false" runat="server"></asp:Label>

                                        <span class="close">×</span>
                                    </asp:Panel>

                                    <h3>Report</h3>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Key</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtReportPathKey" Text="ReportPath" runat="server" CssClass="form-control"></asp:TextBox>
                                                </div>
                                            </div>
                                            <hr class="separator" />
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Report Path</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtReportPath" runat="server" CssClass="form-control"></asp:TextBox>
                                                </div>
                                            </div>


                                            <hr class="separator" />

                                            <div class="form-actions row">
                                                <div class="col-md-3">
                                                </div>
                                                <div class="col-md-9">
                                                    <asp:Button ID="btnPath" runat="server" Text="Save" style="margin-left: 9%;width:10.6%" CssClass="btn btn-primary" CausesValidation="false" OnClick="btnGetPathClick" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>

                        <div id="tab2-1" class="tab-pane">
                            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <asp:Panel ID="Panel1" class="PanelNotificationBox PanelSuccess AutoHide" runat="server" Visible="false">
                                        <asp:Label ID="lblSucess" runat="server" Visible="false" ForeColor="Green"></asp:Label>

                                        <span class="close">×</span>
                                    </asp:Panel>


                                    <h3>Database</h3>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Key</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtDBRoleNameKey" Text="DBRoleName" runat="server" CssClass="form-control"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator7" runat="server" ControlToValidate="txtDBRoleNameKey" CssClass="error"
                                                        ErrorMessage="Enter database name" ValidationGroup="db" Display="Dynamic"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <hr class="separator" />
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">DB Role Name</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtDBRoleName" runat="server" CssClass="form-control"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvDbase" runat="server" ControlToValidate="txtDBRoleName" CssClass="error"
                                                        ErrorMessage="Enter database name" ValidationGroup="db" Display="Dynamic"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <hr class="separator" />
                                            <div class="form-actions row">
                                                <div class="col-md-3">
                                                </div>
                                                <div class="col-md-9">
                                                    <asp:Button ID="btnDBRole" runat="server" style="margin-left: 9%;width:10.6%" CssClass="btn btn-primary" Text="Save" CausesValidation="true" ValidationGroup="db" OnClick="btnDBRoleClick" />
                                                </div>
                                            </div>


                                        </div>

                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>

                        <div id="tab4-1" class="tab-pane">
                            <asp:UpdatePanel ID="update" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <asp:Panel ID="Panel2" class="PanelNotificationBox PanelSuccess AutoHide" runat="server" Visible="false">
                                        <asp:Label ID="lblErr" runat="server" CssClass="error" Visible="false"></asp:Label>
                                        <asp:Label ID="lblpwdPolicay" runat="server" Visible="false"></asp:Label>

                                        <span class="close">×</span>
                                    </asp:Panel>
                                    <h3>Password Policy</h3>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <div class="col-md-6 padding-none-LR">
                                                    <label class="col-md-3 control-label">Key</label>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="txtPwdKey" runat="server" Text="PwdKey" CssClass="form-control"></asp:TextBox>
                                                    </div>
                                                </div>

                                            </div>
                                            <hr class="separator" />
                                            <div class="form-group">

                                                <label class="col-md-3 control-label" style="width: 12.5%">Min length<span class="inactive">*</span></label>
                                                <div class="col-md-9" style="width: 28%">


                                                    <asp:TextBox ID="txtMin" CssClass="form-control" runat="server" style="width: 66%;"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator6" runat="server" ValidationGroup="pp" ControlToValidate="txtMin" CssClass="error"
                                                        ErrorMessage="Enter number" Display="Dynamic"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator5" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtMin"
                                                        ErrorMessage="Enter number" ValidationGroup="pp" ValidationExpression="^\d+$"></asp:RegularExpressionValidator>
                                                </div>

                                                <label class="col-md-3 control-label" style="width: 12.5%">Max length<span class="inactive">*</span></label>
                                                <div class="col-md-9" style="width: 28%">
                                                    <asp:TextBox ID="txtMax" CssClass="form-control" runat="server" style="width: 66%;"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rqdUsrID" runat="server" ValidationGroup="pp" ControlToValidate="txtMax" CssClass="error"
                                                        ErrorMessage="Enter number" Display="Dynamic"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator6" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtMax"
                                                        ErrorMessage="Enter number" ValidationGroup="pp" ValidationExpression="^\d+$"></asp:RegularExpressionValidator>
                                                </div>

                                            </div>

                                            <div class="form-group">
                                               
                                                    <label class="col-md-3 control-label" style="width: 12.5%">Upper length<span class="inactive">*</span></label>
                                                    <div class="col-md-9" style="width: 28%">
                                                        <asp:TextBox ID="txtUpper" CssClass="form-control" runat="server" style="width: 66%;"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ValidationGroup="pp" ControlToValidate="txtUpper" CssClass="error"
                                                            ErrorMessage="Enter number" Display="Dynamic"></asp:RequiredFieldValidator>
                                                        <%--  <asp:RangeValidator ID="rangeValidator1" runat="server" ControlToValidate="txtUpper" MaximumValue="5" MinimumValue="1"
                                                            ForeColor="Red" ErrorMessage="minumum one value"  ValidationGroup="pp" CssClass="error" Display="Dynamic" />--%>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator2" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtUpper"
                                                            ErrorMessage="Enter number" ValidationGroup="pp" ValidationExpression="^\d+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                
                                                    <label class="col-md-3 control-label" style="width: 12.5%">Small length<span class="inactive">*</span></label>
                                                    <div class="col-md-9" style="width: 28%">
                                                        <asp:TextBox ID="txtSmall" CssClass="form-control" runat="server" style="width: 66%;"></asp:TextBox>

                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ValidationGroup="pp" ControlToValidate="txtSmall" CssClass="error"
                                                            ErrorMessage="Enter number" Display="Dynamic"></asp:RequiredFieldValidator>
                                                        <%-- <asp:RangeValidator ID="rangeValidator2" runat="server" ControlToValidate="txtSmall" MaximumValue="5" MinimumValue="1"
                                                            ForeColor="Red" ErrorMessage="minumum one value" CssClass="error" Display="Dynamic" />--%>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtSmall"
                                                            ErrorMessage="Enter number" ValidationGroup="pp" ValidationExpression="^\d+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                
                                            </div>
                                            <div class="form-group">
                                                
                                                    <label class="col-md-3 control-label" style="width: 12.5%">Num<span class="inactive">*</span></label>
                                                    <div class="col-md-9" style="width: 28%">
                                                        <asp:TextBox ID="txtNum" CssClass="form-control" runat="server" style="width: 66%;"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator4" ValidationGroup="pp" runat="server" ControlToValidate="txtNum" CssClass="error"
                                                            ErrorMessage="Enter number" Display="Dynamic"></asp:RequiredFieldValidator>
                                                        <%--   <asp:RangeValidator ID="rangeValidator3" runat="server" ControlToValidate="txtNum" MaximumValue="5" MinimumValue="1"
                                                            ForeColor="Red" ErrorMessage="minumum one value" />--%>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator3" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtNum"
                                                            ErrorMessage="Enter number" ValidationGroup="pp" ValidationExpression="^\d+$"></asp:RegularExpressionValidator>
                                                    </div>
                                               
                                                    <label class="col-md-3 control-label" style="width: 12.5%">Special Char<span class="inactive">*</span></label>
                                                    <div class="col-md-9" style="width: 28%">
                                                        <asp:TextBox ID="txtSpecial" CssClass="form-control" runat="server" style="width: 66%;"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator5" ValidationGroup="pp" runat="server" ControlToValidate="txtSpecial" CssClass="error"
                                                            ErrorMessage="Enter number" Display="Dynamic"></asp:RequiredFieldValidator>
                                                        <%--   <asp:RangeValidator ID="rangeValidator4" runat="server" ControlToValidate="txtSpecial" MaximumValue="5" MinimumValue="1"
                                                            ForeColor="Red" ErrorMessage="minumum one value" />--%>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator4" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtSpecial"
                                                            ErrorMessage="Enter number" ValidationGroup="pp" ValidationExpression="^\d+$"></asp:RegularExpressionValidator>
                                                    </div>
                                                
                                            </div>
                                            <asp:Label ID="lblerrmsg" CssClass="error" runat="server" Visible="false"></asp:Label>
                                            <hr class="separator" />
                                            <div class="form-actions row">
                                                <div class="col-md-7">
                                                </div>
                                                <div class="col-md-5">
                                                    <asp:Button ID="btnGenrateKey" Text="Save" style="margin-left: 32px;width:20%" CssClass="btn btn-primary" ValidationGroup="pp" CausesValidation="true" runat="server" OnClick="btnGenrateKeyClick" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>



                        <div id="tab5-1" class="tab-pane">
                            <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <asp:Panel ID="Panel3" class="PanelNotificationBox PanelSuccess AutoHide" runat="server" Visible="false">
                                        <asp:Label ID="lblpwdAge" Visible="false" runat="server"></asp:Label>

                                        <span class="close">×</span>
                                    </asp:Panel>
                                    <h3>Password Policy</h3>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Key</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtPasswordAgeKey" Text="PwdAge" runat="server" CssClass="form-control"></asp:TextBox>

                                                </div>
                                            </div>
                                            <hr class="separator" />
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Password Age</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtPasswordAge" runat="server" CssClass="form-control"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="tfvPwdAge" runat="server" ControlToValidate="txtPasswordAge" CssClass="error"
                                                        ErrorMessage="Enter TimeOut" ValidationGroup="pwdAge" Display="Dynamic"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>

                                            <hr class="separator" />
                                            <div class="form-actions row">
                                                <div class="col-md-3">
                                                </div>
                                                <div class="col-md-9">
                                                    <asp:Button ID="btnPasswordAge" runat="server" style="margin-left: 9%;width:10.6%" Text="Save" ValidationGroup="pwdAge" CssClass="btn btn-primary" CausesValidation="true" OnClick="btnPasswordAgeClick" />
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>

                        <div id="tab6-1" class="tab-pane">
                            <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <asp:Panel ID="Panel4" class="PanelNotificationBox PanelSuccess AutoHide" runat="server" Visible="false">
                                        <asp:Label ID="lblTime" Visible="false" runat="server"></asp:Label>

                                        <span class="close">×</span>
                                    </asp:Panel>
                                    <h3>Session Time Out</h3>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Session Time Out</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtTimeOut" runat="server" CssClass="form-control" Style="margin-right:4px;"></asp:TextBox>minutes
                                                    <asp:RequiredFieldValidator ID="rfvTime" runat="server" ControlToValidate="txtTimeOut" CssClass="error"
                                                        ErrorMessage="Enter TimeOut" ValidationGroup="timeout" Display="Dynamic"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator runat="server" ID="rexNumber" ControlToValidate="txtTimeOut" Display="Dynamic" ValidationGroup="timeout" ValidationExpression="^([0-9])+$" ErrorMessage="Enter number" />
                                                </div>
                                            </div>

                                            <hr class="separator" />
                                            <div class="form-actions row">
                                                <div class="col-md-3">
                                                </div>
                                                <div class="col-md-9">
                                                    <asp:Button ID="btnTimeOut" Text="Save" style="margin-left: 9%;width:10.6%" CssClass="btn btn-primary" ValidationGroup="timeout" CausesValidation="true" runat="server" OnClick="btnTimeOutClick" />
                                                </div>
                                            </div>


                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>

                        <div id="tab7-1" class="tab-pane">
                            <asp:UpdatePanel ID="UpdatePanel5" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <asp:Panel ID="logsuccess" class="PanelNotificationBox PanelSuccess AutoHide" runat="server" Visible="false">
                                        <asp:Label ID="lbllogFile" Visible="false" runat="server"></asp:Label>

                                        <span class="close">×</span>
                                    </asp:Panel>

                                    <h3>Log</h3>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Log File Path</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtLogFile" runat="server" CssClass="form-control" ValidationGroup="logfile"></asp:TextBox>
                                                </div>
                                            </div>

                                            <hr class="separator" />

                                            <div class="form-actions row">
                                                <div class="col-md-3">
                                                </div>
                                                <div class="col-md-9">
                                                    <asp:Button ID="btnLogFile" runat="server" Text="Save" style="margin-left: 9%;width:10.6%" CssClass="btn btn-primary" ValidationGroup="logfile" CausesValidation="true" OnClick="btnLogPathClick" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>


                           <div id="tab8-1" class="tab-pane" visible="false">
                            <asp:UpdatePanel ID="UpdatePanel6" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <asp:Panel ID="Panel5" class="PanelNotificationBox PanelSuccess AutoHide" runat="server" Visible="false">
                                        <asp:Label ID="Label1" runat="server" Visible="false" ForeColor="Green"></asp:Label>

                                        <span class="close">×</span>
                                    </asp:Panel>


                                    <h3>License Utilization Count</h3>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Total Server Count</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txttotalsercount" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txttotalsercount" CssClass="error"
                                                        ErrorMessage="Enter database name" ValidationGroup="db" Display="Dynamic"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <hr class="separator" />
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Total Application Count</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtappcount" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator8" runat="server" ControlToValidate="txtappcount" CssClass="error"
                                                        ErrorMessage="Enter database name" ValidationGroup="db" Display="Dynamic"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <hr class="separator" />
                                             <div class="form-group">
                                                <label class="col-md-2 control-label">Total DataBase Count</label>
                                                <div class="col-md-6">
                                                    <asp:TextBox ID="txtdbcount" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator9" runat="server" ControlToValidate="txtdbcount" CssClass="error"
                                                        ErrorMessage="Enter database name" ValidationGroup="db" Display="Dynamic"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                               <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false"></asp:Label>
                        </div>
                        

                    </div>

                </div>
            </div>
        </div>

    </div>


    <script type="text/javascript">
        $(document).ready(function () {

            $('li.tab-psd-child').hide();
            $('.widget-tabs-vertical ul li').click(function () {

                if (!$(this).is("#tab-password") == false || $(this).hasClass('tab-psd-child')) {

                    $(this).parent().children('li.tab-psd-child').show();

                }
                else {
                    $(this).parent().children('li.tab-psd-child').hide();
                }
            });
        });

    </script>

</asp:Content>
