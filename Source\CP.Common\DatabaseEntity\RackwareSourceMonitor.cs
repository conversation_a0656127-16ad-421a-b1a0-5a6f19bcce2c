﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
namespace CP.Common.DatabaseEntity
{

    [Serializable]
    [DataContract(Name = "RackwareSourceMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]

   public class RackwareSourceMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int id { get; set; }
        [DataMember]
        public int InfraObjectId { get; set; }
        [DataMember]
        public string Source { get; set; }
        [DataMember]
        public String Target { get; set; }
        [DataMember]
        public string Goal { get; set; }
        [DataMember]
        public string JobId { get; set; }
        [DataMember]
        public string Status { get; set; }
        [DataMember]
        public string CreateDate { get; set; }
        [DataMember]
        public string UpdateDate { get; set; }



        public string InfraObjectName
        {
            get;
            set;
        }
        public string Server
        {
            get;
            set;
        }
        #endregion Properties

    }
}
