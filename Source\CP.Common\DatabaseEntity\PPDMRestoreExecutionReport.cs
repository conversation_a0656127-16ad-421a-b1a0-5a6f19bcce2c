﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{


    [Serializable]
    [DataContract(Name = "PPDMRestoreExecutionReport", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class PPDMRestoreExecutionReport : BaseEntity
    {
        #region Properties

        [DataMember]
        public string PPDMServerIP { get; set; }

        [DataMember]
        public int WorkflowActionId { get; set; }

        [DataMember]
        public string ProtectionPolicyName { get; set; }

        [DataMember]
        public string VMName { get; set; }

        [DataMember]
        public string TargetvCenterName { get; set; }

        [DataMember]
        public string ProgressPercentage { get; set; }

        [DataMember]
        public string ProgressState { get; set; }


        [DataMember]
        public string ProgressStatus { get; set; }

        [DataMember]
        public string JobId { get; set; }



        public string WorkflowName { get; set; }

        [DataMember]
        public string WorkflowActionName { get; set; }

        [DataMember]
        public string ActivityId { get; set; }


        [DataMember]
        public string StartTime { get; set; }

        [DataMember]
        public string EndTime { get; set; }

        [DataMember]
        public string TotalExecutionTime { get; set; }

        #endregion Properties
    }




    
}
