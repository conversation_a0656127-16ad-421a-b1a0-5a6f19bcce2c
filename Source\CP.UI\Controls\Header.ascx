﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="Header.ascx.cs" Inherits="CP.UI.Controls.Header" %>
<ul id="notifications" runat="server" visible="false">
    <li class="">
        <asp:Label ID="lblNotification" runat="server" Text="Password is Expired"></asp:Label>
        <i>X</i>
    </li>
</ul>

<div class="navbar main">
    <div class="logodiv pull-left padding">
        <a href="../Admin/CommandCenter.aspx" class="appbrand">
            <img src="../images/ContinuityPatrol_logo.png" width="180" alt="Continuity Patrol" /></a>
    </div>
    <div class="menu-items pull-left">
        <ul class="topnav" id="abc" runat="server">
            <li>
                <a id="Module1" runat="server" href="../Admin/CommandCenter.aspx" title="Home" class="active glyphicons dashboard"><i></i>Command Center</a>
            </li>
            <li id="liConfigure" runat="server" class="dropdown dd-1">
                <a id="Module2" runat="server" title="Configure" data-toggle="dropdown">Configure <span class="caret"></span></a>
                <ul class="dropdown-menu pull-left" style="width: 250px">
                    <li><a id="createcomp" runat="server" href="../CompanyProfile/CompanyProfileConfiguration.aspx">Create Company Profile</a></li>
                    <li><a id="createcomp1" runat="server" href="../Site/SiteConfiguration.aspx">Create Site</a></li>
                    <li><a id="createcomp2" runat="server" href="../BusinessServices/BusinessServiceConfiguration.aspx">Create Business Service</a></li>
                    <li><a id="businessfun" runat="server" href="../BusinessFunction/BusinessFunctionConfig.aspx">Create Business Function</a></li>
                    <li class="dropdown submenu "><a href="#" class="glyphicons play"><i></i>Create Infra Component</a>
                        <ul class="dropdown-menu submenu-show pull-right submenu-hide" style="width: 250px">
                            <li><a id="serverid" runat="server" href="../Component/ServerConfiguration.aspx">Server Infra Component</a></li>
                            <li><a id="databaseid" runat="server" href="../Component/DatabaseConfiguration.aspx">Database Infra Component</a></li>
                            <li><a id="replid" runat="server" href="../Component/ReplicationConfiguration.aspx">Replication Infra Component</a></li>
                            <li><a id="datasyncid" runat="server" href="../Component/DataSyncPropertiesConfig.aspx">DataSync Properties</a></li>
                            <li><a id="nodeid" runat="server" href="../Component/NodesConfiguration.aspx">Nodes Configuration</a></li>
                            <li><a id="gridid" runat="server" href="../Component/GridConfiguration.aspx">ASM Grid Configuration</a></li>
                            <li><a id="homeregioncon" runat="server" href="../Component/OracleCloudHomeRegionDetails.aspx">Home Region Configuration</a></li>
                            <li class="dropdown submenu "><a href="#" class="glyphicons play"><i></i>Import Servers From CMDB</a>
                                <ul class="dropdown-menu submenu-show pull-right submenu-hide" style="width: 250px; max-height: 250px !important;">
                                    <li><a id="imprtCMDB" runat="server" href="../Component/ImportCMDB.aspx">Import Excel/CSV File</a></li>
                                </ul>
                            </li>
                    </li>
                    <li><a id="vCenterMonitorProfile" runat="server" href="../Component/vCenterMonitorProfileConfig.aspx">Profile Monitoring Configuration</a></li>

                    <li><a id="veritascluserConfig" runat="server" href="../Component/VeritasClusterConfiguration.aspx">Veritas Cluster Configuration</a></li>
                    <li><a href="../Component/HACMPClusterConfiguration.aspx" id="HACMPConfig" runat="server">HACMP Cluster </a></li>
                    <li><a id="robocopyoptionsid" runat="server" href="../Component/Robocopyoptionsconfig.aspx">RoboCopyOptions</a></li>
                    <li><a id="RSyncOptionsConfig" runat="server" href="../Component/Rsyncoptionconfiguration.aspx">RSyncOptions Configuration</a></li>
                     <li><a id="LoadBalancerConfig" runat="server" href="../Component/LoadBalancerconfiguration.aspx">Load Balancer Configuration</a></li>
                      <li><a id="ODGOptionsConfig" runat="server" href="../Component/DiffModuleITOPS.aspx">Diff Module Configuration</a></li>
                </ul>
            </li>
            <li><a id="infraid" runat="server" href="../Group/InfraObjectsConfiguration.aspx">Create InfraObject </a></li>

            <li><a href="../Component/SingleSign-OnConfiguration.aspx">Create Single Sign-On </a></li>
            <li><a href="../Admin/BulkCredentialChange.aspx">BulkCredential</a></li>
              <li><a href="../Admin/BulkCredentialDatabase.aspx">Bulk Database Credential</a></li>
            <li class="dropdown submenu ">
                <a href="#" class="glyphicons play"><i></i>Create FIA/BIA</a>
                <ul class="dropdown-menu submenu-show pull-right submenu-hide" style="width: 250px">
                    <li><a id="A3" runat="server" href="../Admin/BIAProfileForm.aspx">Configure FIA Templates</a></li>
                    <li><a id="A4" runat="server" href="../Admin/BusinessFunctionsBIA.aspx">Configure FIA Costs</a></li>
                    <li><a id="A5" runat="server" href="../ImpactAnalysis/ConfigureBIARule.aspx">Configure BIA Rules</a></li>
                </ul>
            </li>

            <%--     <li><a href="../Admin/BIAProfileForm.aspx">Configure BIA Profile</a></li>--%>
           <%-- <li><a id="BIA" runat="server" href="~/ImpactAnalysis/BussinessFunctionBIADynamic.aspx">Configure Business Function BIA</a></li> --%>
            <li><a id="A1" runat="server" href="../SNMP/ADMSetting.aspx">ADMSetting</a></li>
            <li><a id="A2" runat="server" href="../SNMP/ScheduleDiscovery.aspx">Schedule Discovery</a></li>
            <%--     <li class="dropdown submenu "><a href="#" class="glyphicons play"><i></i>Create BIA Impact</a>
                <ul class="dropdown-menu submenu-show pull-right submenu-hide" style="width: 250px">
                    <li><a id="impacttypemasterid" runat="server" href="../ImpactAnalysis/ImpactTypeMaster.aspx">Create Impact Type </a></li>
                    <li><a id="impactmasterid" runat="server" href="../ImpactAnalysis/ImpactMaster.aspx">Create Impact Master</a></li>
                </ul>
            </li>--%>
            <%--    <li><a id="RuleConfiguration" runat="server" href="../ImpactAnalysis/EntityImpactRelationshipForm.aspx">Configure BIA Rule</a></li>--%>

            <li><a id="A12" runat="server" visible="false" href="../Admin/DesignProcessMoniter.aspx">Design Process Monitor</a></li>
            <%--<li><a id="motque" runat="server" href="../Admin/MonitoringQueue.aspx">Monitoring Queues</a></li>--%>
        </ul>
        </li>

            <li id="liView" runat="server" class="dropdown dd-1">
                <a id="Module3" runat="server" title="View" data-toggle="dropdown">View <span class="caret"></span></a>
                <ul class="dropdown-menu pull-left" style="width: 225px">
                    <li><a id="profile" runat="server" href="../CompanyProfile/CompanyProfileList.aspx">Profiles</a></li>
                    <li><a id="sitelist" runat="server" href="../Site/SiteList.aspx">Sites</a></li>
                    <li><a id="busnlist" runat="server" href="../BusinessServices/BusinessServiceList.aspx">Business Services</a></li>
                    <li><a id="busfunlist" runat="server" href="../BusinessFunction/BusinessFunctionList.aspx">Business Functions</a></li>

                    <li class="dropdown submenu "><a href="#" class="glyphicons play"><i></i>Infra Components</a>
                        <ul class="dropdown-menu submenu-show pull-right submenu-hide">
                            <li><a id="servlist" runat="server" href="../Component/ServerList.aspx">Servers</a></li>
                            <li><a id="dblist" runat="server" href="../Component/DatabaseList.aspx">Databases</a></li>
                            <li><a id="repllist" runat="server" href="../Component/ReplicationList.aspx">Replications</a></li>
                            <li><a id="datasynlist" runat="server" href="../Component/DataSyncPropertiesList.aspx">DataSync Properties</a></li>
                            <li><a id="nodelist" runat="server" href="../Component/NodesList.aspx">Nodes</a></li>
                            <li><a id="vCenterMonitor" runat="server" href="../Component/VCenterProfile.aspx">Infra Monitoring</a></li>
                            <li><a id="veritasclusterlist" runat="server" href="../Component/VeritasClusterList.aspx">Veritas Cluster List</a></li>
                            <li><a href="../Component/HACMPClusterConfigurationList.aspx" id="HACMPList" runat="server">HACMP Clusters</a></li>
                            <li><a id="RoboCopyOptionsList" runat="server" href="../Component/Robocopyoptionslist.aspx">RoboCopyOptions</a></li>
                            <li><a id="RSyncOptionList" runat="server" href="../Component/Rsyncoptionslist.aspx">RSync Options</a></li>
                            <li><a id="LoadBalancerList" runat="server" href="../Component/LoadBalancerList.aspx">Load Balancer List</a></li>
                            <li><a id="homeregionlis" runat="server" href="../Component/OracleCloudHomeRegionList.aspx">Home Region List</a></li>
                            <li><a id="DiffModule" runat="server" href="../Component/DiffModuleList.aspx">Diff Module List</a></li>


                        </ul>
                    </li>

                    <li><a id="infralist" runat="server" href="../Group/InfraObjectsList.aspx">InfraObjects</a></li>
                    <li><a href="../Component/SingleSignOnList.aspx">Single SignOns</a></li>


                    <%--  <li><a id="eventlist" runat="server" href="../Admin/EventListing.aspx">Events</a></li>
                    <li><a id="infrmonlist" runat="server" href="../Component/InfrastructureMonitorList.aspx">Infrastructures Monitor</a></li>--%>
                    <li><a id="appdilist" runat="server" href="../SNMP/ViewApplicationDiscovery.aspx">Application Discovery</a></li>
                    <li><a id="appdelist" runat="server" href="../SNMP/ViewApplicationDependency.aspx">Application Dependency</a></li>
                    <li><a href="../SNMP/ViewApplicationDependencyMapping.aspx">App Dependency Mapping</a></li>
                    <li><a href="../ImpactAnalysis/Statistics.aspx" runat="server" visible="false">Analytics</a></li>
                    <li class="dropdown submenu"><a class="glyphicons play" runat="server"><i></i>View Incident Details</a>
                        <ul class="dropdown-menu submenu-show pull-right submenu-hide" style="width: 250px">
                            <%--      <li><asp:LinkButton ID="lnkopenInc" runat="server" Text="Open Incidents" OnClick="lnkopenInc_Click"></asp:LinkButton></li>
                            <li><asp:LinkButton ID="lnk7DaysInc" runat="server" Text="Last 7 Days" OnClick="lnk7DaysInc_Click"></asp:LinkButton></li>--%>
                            <%--    <li><asp:LinkButton ID="lnkAllInc" runat="server" Text="All Close Incidents" OnClick="lnkAllInc_Click"></asp:LinkButton></li>--%>
                            <li><a id="OpenInc" runat="server">Open Incidents</a></li>
                            <li><a id="WdaysInc" runat="server">Last 7 Days  (Open And Closed)</a></li>
                            <li><a id="AllInc" runat="server">All Closed Incidents  (30 Days)</a></li>
                            <li><a id="A6" runat="server" href="../ImpactAnalysis/ImpactSummary.aspx"></a></li>
                            <li><a id="A8" runat="server" href="../ImpactAnalysis/ImpactSummary.aspx"></a></li>
                            <li><a id="A9" runat="server" href="../ImpactAnalysis/ImpactSummary.aspx"></a></li>

                        </ul>
                    </li>
                    <li><a href="../ImpactAnalysis/AllComponentWhatIfAnalysis.aspx">Component What If Analysis</a></li>
                    <li><a id="A10" runat="server" visible="false" href="../Admin/ServicesMonitor.aspx">Services Monitor</a></li>
                    <li><a id="A11" runat="server" visible="false" href="../Admin/ServicesMonitorOverview.aspx">Services Monitor Overview</a></li>
                    <li><a id="workflowlst" runat="server" href="../Workflow/WorkflowList.aspx">Workflow List</a></li>
                    <li><a id="A14" runat="server" href="../Admin/BusinessServiceDashboard.aspx">Business Service Dashboard</a></li>
                    <li><a id="A13" runat="server" href="../Admin/DellEMC_Dashboard.aspx">Dell EMC Dashboard</a></li>
                    <li><a id="A15" runat="server" href="../Admin/DiffModuleDashBoard.aspx">Diff Module Dashboard</a></li>
                </ul>
            </li>
        <li id="liInfraObject" runat="server" class="dropdown dd-1">
            <a id="Module4" runat="server" title="Manage" data-toggle="dropdown">Manage <span class="caret"></span></a>
            <ul class="dropdown-menu pull-left" style="width: 225px">
                <%--<li><a id="excmgmt" runat="server" href="../Alert/CustomExceptionManagement.aspx">Exception Management</a></li>--%>
                <%--<li><a id="evnmgmt" runat="server" href="../Admin/EventManagement.aspx">Event Management</a></li>--%>
                <li><a id="jobmgmt" runat="server" href="../Admin/JobManagement.aspx">Job Management</a></li>
                <%--<li><a id="inmgmt" runat="server" href="../Component/InfrastructureMonitor.aspx">Infrastructure Monitor</a></li>--%>
                <li><a id="momgmt" runat="server" href="../Admin/MonitoringServices.aspx">Monitoring Services</a></li>
                <li><a id="notmgmt" runat="server" href="../Alert/NotificationManager.aspx">Notification Manager</a></li>
                <%--<li><a id="netmgmt" runat="server" href="../SNMP/SNMPDiscovery.aspx">Network Discovery</a></li>
                <li><a id="appmgmt" runat="server" href="../SNMP/NetworkDiscoveryForm.aspx">Application Discovery</a></li>
                <li><a id="dismgmt" runat="server" href="../SNMP/DiscoveryConfiguration.aspx">Discovery Configuration</a></li>
                <li><a id="dissmgmt" runat="server" href="../SNMP/DiscoverySummary.aspx">Discovery Summary</a></li>
                <li><a id="biamgmt" runat="server" href="../Admin/BIAFunctionsReport.aspx">BIA Functions</a></li>--%>
                <li><a id="imtmgmt" runat="server" href="../Admin/ImportCMDBForm.aspx">Import CMDB</a></li>
                <li><a id="moniterqueuelist" runat="server" visible="false" href="../Component/QueueMonitorList.aspx">Queue Monitor</a></li>
                <li><a id="A7" runat="server" href="../Admin/ManageBusinessService.aspx">Manage Business Service</a></li>
                <li><a id="logviewer" runat="server" href="../Admin/LogViewer.aspx">Log Viewer</a></li>

            </ul>
        </li>
        <li id="liAlert" runat="server" class="dropdown dd-1">
            <a id="Module5" runat="server" title="Alerts" data-toggle="dropdown">Alerts <span class="caret"></span></a>
            <ul class="dropdown-menu pull-left">
                <li><a id="alert" runat="server" href="../Alert/AlertManagement.aspx">Alerts</a></li>
                <li><a id="alertdashboard" runat="server" href="../Alert/AlertDashBoard.aspx">Alert DashBoard</a></li>
                <%--  <li><a id="alertmanag" runat="server" href="../Alert/AlertManager.aspx">Alert Manager</a></li>
                    <li><a id="incident" runat="server" href="../Alert/IncidentManager.aspx">Incident Management</a></li>--%>
            </ul>
        </li>

        <li id="liWorkflow" runat="server" class="dropdown dd-1">
            <a id="Module6" runat="server" title="IT Orchestration" data-toggle="dropdown" href="">IT Orchestration <span class="caret"></span></a>
            <ul class="dropdown-menu pull-left" style="width: 225px">
                <%--  <li><a id="bpa" runat="server" href="../Admin/BusinessProcessAuto.aspx">Business Process Automation</a></li>--%>
                <li><a id="wrflow" runat="server" href="../Workflow/WorkflowConfiguration.aspx">Workflow Configuration</a></li>
                <li><a id="wrkprofile" runat="server" href="../Admin/WorkflowProfileManagement.aspx">Workflow Profile Management</a></li>
                <li><a id="parallelwrkfl" runat="server" href="../Admin/ParallelWorkflow.aspx">Parallel Workflow</a></li>
            </ul>
        </li>



        <li id="liReport" runat="server" class="dropdown dd-1">
            <a id="Module7" runat="server" title="Reports" data-toggle="dropdown">Reports <span class="caret"></span></a>
            <ul class="dropdown-menu pull-left" style="width: 225px">
                <li><a id="NewReport" runat="server" href="~/Report/TelerikReportManagement.aspx">Reports</a></li>
                <li><a id="ExcelReports" runat="server" href="~/Report/ReportManagement.aspx">Excel Reports</a></li>

            </ul>
        </li>

        <li id="liAdmin" runat="server" class="dropdown dd-1">
            <a id="Module8" runat="server" title="Admin" data-toggle="dropdown">Admin <span class="caret"></span></a>
            <ul class="dropdown-menu pull-left">
                <li><a id="archive" runat="server" href="../Admin/Archive.aspx">Archive</a></li>
                <li><a id="back" runat="server" href="../Admin/DatabaseBackupManagement.aspx">Backup Data</a></li>

                <li class="dropdown submenu "><a href="#" class="glyphicons play"><i></i>User Management</a>
                    <ul class="dropdown-menu pull-left">
                        <li><a id="usermg" runat="server" href="../User/UserConfiguration.aspx">Create User</a></li>
                        <li><a id="userlist" runat="server" href="../User/UserList.aspx">Users</a></li>
                        <li><a id="AccessManager" runat="server" href="../User/AccessManager.aspx">AccessManager</a></li>
                    </ul>
                </li>

                <li><a id="setting" runat="server" href="../Admin/AdminSetting.aspx">Settings</a></li>
                <%--<li><a href="../Report/TelerikReportManagement.aspx"> Reports</a></li>--%>
                <%--<li><a href="../User/AccessManager.aspx">Access Manager</a></li>--%>
                <%--<li><a id="alertSetting" runat="server" href="../Alert/AlertSetting.aspx">Alert Setting</a></li>--%>
                <li><a href="../Component/ConfigureDefaultMonitoringServices.aspx" runat="server" visible="false">Default Monitoring Services</a></li>
                <%--<li><a href="../Admin/CIODashboard.aspx">CIO Dashboard</a></li>--%>
            </ul>
        </li>
        <li style="float: right;" runat="server" id="libssummary"><a id="bs" runat="server" title="Business Service Summary" href="../Admin/BusinessService_DashboardDetails.aspx" onclick="lblIcon_Click">
            <img src="../Images/bs-summery2.png" height="18px">
        </a>
        </li>
        </ul>
    </div>

    <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
        <ContentTemplate>
            <asp:Timer runat="server" ID="UpdateTimer" Interval="300000" OnTick="UpdateTimerTick"
                Enabled="false" />
            <ul class="topnav pull-right">
                <%--pull-right--%>

                <div class="date-time">
                    <span id="day"></span>
                    <span id="time"></span>
                </div>

                <li class="hidden-xs hidden-md dropdown dd-1 dd-flags" id="lstAlert" runat="server"
                    value="false" visible="false" ><a href="../Alert/AlertManagement.aspx" title="Alert" id="alertsblink">
                        <img src="../Images/icons/alert.gif" alt="Alert" width="19px" height="19px">
                        <asp:Label ID="lblAlertCount" runat="server" ToolTip="Alert"></asp:Label></a></li>

                <li class="account dropdown dd-1"><a data-toggle="dropdown" href="../User/UserConfiguration.aspx"
                    class="glyphicons logout user"><%--<span>--%>
                    <asp:Label ID="lblLoggedUser" Visible="false" runat="server" Text="Label" Style="display: inline-block; max-width: 140px; text-align: right; text-overflow: ellipsis; white-space: nowrap; overflow: hidden"></asp:Label><%--</span>--%><i></i>
                </a>
                    <ul class="dropdown-menu pull-right">
                        <li id="chngPass" runat="server"><a href="../Admin/ChangePassword.aspx" title="Change Password" class="glyphicons keys single-icon">
                            <i></i>Change Password</a></li>
                        <li class="profile"><span><span class="heading">Profile </span>
                            <span class="details">User : 
                                 <asp:Label ID="lblUserName" runat="server" CssClass="bold" Text=""></asp:Label>
                                <br />
                                Last Login  :
                                <asp:Label ID="lblLastLogin" runat="server" CssClass="bold" Text=""></asp:Label>
                            </span><span class="clearfix"></span></span></li>

                        <li><span>
                            <asp:LoginStatus ID="lgsLogin" CssClass="btnlogout btn btn-default btn-mini pull-right"
                                ToolTip="Logout" LogoutText="Logout" LoginText="Login" runat="server" LogoutAction="RedirectToLoginPage"
                                OnLoggedOut="LgsLoginLoggedOut" Width="79px" Height="33px" />
                        </span></li>
                    </ul>
                </li>

            </ul>

            <div class="clearfix">
            </div>
        </ContentTemplate>
        <Triggers>
            <asp:AsyncPostBackTrigger ControlID="UpdateTimer" EventName="Tick" />
        </Triggers>
    </asp:UpdatePanel>

</div>
