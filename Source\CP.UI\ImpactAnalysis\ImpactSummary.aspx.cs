﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.UI.Controls;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI;
using Telerik.Web.UI.HtmlChart;
using CP.Helper;
using CP.BusinessFacade;
using System.Text;
using CP.Common.BusinessEntity;
using System.Web.Services;
using System.ComponentModel;

namespace CP.UI.ImpactAnalysis
{
    public partial class ImpactSummary : BasePage
    {
        #region Variables

        #region Commom variables
        public static string WhatIfTabCurrentUrl = Constants.UrlConstants.Urls.ImpactAnalysis.InidentWhatIf;

        private static IFacade facade = new Facade();
        private static int _currentLoginUserId;
        private static string _currentLoggedUserName;
        private static int _companyId;
        private static bool _isUserSuperAdmin;
        private static bool _isParent;
        private static String _IncidentRecord = string.Empty;
        private static int _IncidentRecordId = 0;
        private DataTable table;
        private static string _IncidentType;
        private static int TotallyImpactId, MajorlyImpactId, PartiallyImpactId;
        #endregion

        #region Business-IT relationShip tree variables

        private static IList<BusinessFunction> businessFunctionList = null;
        private static IList<InfraObject> infraObjects = null;

        private static StringBuilder buildNodeRelSb = null;
        private static StringBuilder jsonNodeRelSb = null;
        private static List<string> BFIsRedOrYellow = null;

        private static BusinessFunction businessFunctionObj = null;
        private static BusinessService businessService = null;

        private static DataTable businessFunctionsTable = null;
        private static DataTable infraObjectTable = null;

        public static int currentNode = 0;
        public static string nameStr = "name", levelStr = "level", colorRedStr = "rgb(189, 54, 47)",
                                        colorYellowStr = "rgb(236, 180, 4)", colorOrange = "rgb(255,168,0)", colorGreen = "rgb(64, 208, 167)", childStr = "children",
                                        size = "size", hide = "hide", logo = "logo", RepKey = "Replication",
                                        RepValue = "Native Replication", ImpactType = "ImpactType",
                                        PartiallyImpacted = "Partial Impact", TotallyImpacted = "Total Impact", NoImpact = "None",
                                        MajorImpacted = "Major Impact";

        public static string imagePath = "../Images/icons/close_icon.png";
        public static string imagePath1 = "../Images/Test/cross-circle-icon.png";

        public static string imagePathInc = "../Images/icons/incident-icon.png";

        public static string ProcessimagePath = "../Images/Test/service-icon.png";

        public static string imagePathOrange = "../Images/icons/orange-dot.png";
        public static string imagePathOrangeBF = "../Images/Test/BF-org-icon.png";//"../Images/icons/orange-bf-icon.jpg";
        public static string imagePathOrangeBS = "../Images/Test/BS-org-icon.png";//"../Images/icons/orange-bs-icon.jpg";

        public static string imagePathRed = "../Images/icons/red-dot.png";
        public static string imagePathRedBF = "../Images/Test/BF-red-icon.png";//"../Images/icons/red-bf-icon.jpg";
        public static string imagePathRedBS = "../Images/Test/BS-red-icon.png";//"../Images/icons/red-bs-icon.jpg";

        public static string imagePathGreen = "../Images/icons/green-dot.png";
        public static string imagePathGreenBF = "../Images/Test/BF-green-icon.png";//"../Images/icons/green-bf-icon.jpg";
        public static string imagePathGreenBS = "../Images/Test/BS-green-icon.png";//"../Images/icons/green-bs-icon.jpg";

        public static string imagePathYellow = "../Images/icons/yellow-dot.png";
        public static string imagePathYellowBF = "../Images/Test/BF-yellow-icon.png";// "../Images/icons/yellow-bf-icon.jpg";
        public static string imagePathYellowBS = "../Images/Test/BS-yellow-icon.png";//"../Images/icons/yellow-bS-icon.jpg";
        private static bool hasChild = false;
        private static bool first = true;
        private static int firstSplitCount = 0;
        public static string inputString = string.Empty, iObjInputString = string.Empty, IncidentString = string.Empty;
        public static string jsonString = string.Empty;
        public static string infraObjectJsonString = string.Empty;

        #endregion Business-IT relationShip tree variables

        #endregion

        #region Properties


        /// <summary>
        /// Property to hold list of BusinessFunctions
        /// </summary>
        public static IList<BusinessFunction> BusinessFunctionList
        {
            get { return businessFunctionList ?? (businessFunctionList = new List<BusinessFunction>()); }
            set { businessFunctionList = value; }
        }

        /// <summary>
        /// Property to hold list of InfraObject
        /// </summary>
        public static IList<InfraObject> infraObjectList
        {
            get { return infraObjects ?? (infraObjects = new List<InfraObject>()); }
            set { infraObjects = value; }
        }

        #endregion

        #region Constructor
        #endregion

        #region Events

        /// <summary>
        /// Page load event
        /// </summary>
        /// <author>Ram Mahajan-24/02/2015</author>
        public override void PrepareView()
        {
            _currentLoggedUserName = LoggedInUserName;
            _currentLoginUserId = LoggedInUserId;
            _companyId = LoggedInUserCompanyId;
            _isUserSuperAdmin = IsUserSuperAdmin;
            _isParent = LoggedInUserCompany.IsParent;

            setImpactType();

            GetAllBusinessServices();

            _IncidentType = Helper.Url.SecureUrl[Constants.UrlConstants.Params.IncidentType].ToString();

            //GetAllIncidents(_IncidentType);

            table = BuildTableStructure();
            SetDynamicTabUrlAndDetails(_IncidentType);

            RdTreeListIncidentRecord.DataSource = table;
            updPnlIncidentList.Update();
            updIcidentDetailsTabButton.Update();

            RdTreeListIncidentRecord.ExpandAllItems();


        }

        int Id = 1;
        /// <summary>
        /// Fill datatable for TreeList 
        /// </summary>
        private void FillDataTable(String IncidentRecodr, int Id)
        {
            IEnumerable<IncidentManagementNew> inciMgtDetails = null;
            IEnumerable<IncidentManagementNew> incidentMgtDetails = null;
            IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>();
             incidentMgtDetails = facade.GetAllIncidentManagementNew();
           // var details = facade.GetAllIncidentManagementNew();
            if (!IsSuperAdmin && incidentMgtDetails != null)
            {
                IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                if (infralst != null)
                {
                    foreach (var infra in infralst)
                    {

                        var getdetails = from a in incidentMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                        foreach (var data in getdetails)
                        {
                            incidentMgtDetailsUserBased.Add(data);

                        }
                    }
                }
                incidentMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
            }


            _IncidentRecord = IncidentRecodr;
            _IncidentRecordId = Id;
            if (incidentMgtDetails == null)
            {
                //Display error message
                lblError.Text = "INCIDENT IS NOT CREATED";
                lblError.Visible = true;
                return;
            }
            if (IncidentRecodr == "7Days")
                inciMgtDetails = from a in incidentMgtDetails where a.CreateDate >= DateTime.Now.AddDays(-7) select a;
            else if (IncidentRecodr == "Open")
                inciMgtDetails = from a in incidentMgtDetails where a.Status == 1 select a;
            else if (IncidentRecodr == "Close")
                inciMgtDetails = from a in incidentMgtDetails where a.CreateDate >= DateTime.Now.AddDays(-30) && a.Status == 0 select a;
            else if (IncidentRecodr == "ById" && Id > 0)
                inciMgtDetails = from a in incidentMgtDetails where a.Id == Id select a;
            else
                inciMgtDetails = incidentMgtDetails;

            var incMgtSummary = facade.GetAllIncidentManagementSummary();


            if (IncidentRecodr == "Open")
            {
                if (inciMgtDetails != null)
                {
                    lnkOpen.Text = inciMgtDetails.Count() + " " + "Open Incidents";
                }
                else
                { lnkOpen.Text = "0 Open Incidents"; }
            }

            DataTable dtCost = null;
            DataTable dtBSCost = null;

            string BSName = "";
            string BFName = "";
            String InfraComponentName = "";
            if (inciMgtDetails == null) return;
            foreach (var inDet in inciMgtDetails)
            {
                if (inDet.Id.Equals(_IncidentRecordId) || _IncidentRecordId.Equals(0))
                {

                    List<string> lstParentBFid = new List<string>();
                    IEnumerable<IncidentManagementSummary> inciMgtSumByIncidentID = null;
                    if (incMgtSummary != null)
                    {
                        inciMgtSumByIncidentID = from a in incMgtSummary where a.IncidentID == inDet.Id select a;
                        if (inciMgtSumByIncidentID != null)
                        {
                            // Get Cost tabels for Business service and Business Functions;
                            GetCostTable(inciMgtSumByIncidentID, ref  dtCost, ref  dtBSCost);
                            //(Id,Item,ItemId,ItemType,ParentID,ImpactType,ImpactEntity,IncidentName,ClientSystemTicketID,ImpactStartEndDateTime,ImpactReason,ImpactCost)  
                            InfraComponentName = GetInfraComponentNameByIdAndType(inDet.InfraComponentID, inDet.InfraComponentType,inDet.JOBNAME,inDet.Id);
                            Double FinancialCost = 0;

                            FinancialCost = GetFinancialCost(dtBSCost);
                            int InciIDTree = Id;

                            string IncidentRecoveryTime = string.Empty;

                            if (inDet.Status == 1)
                            {
                                IncidentRecoveryTime = "N/A";
                            }
                            else
                            {
                                IncidentRecoveryTime = inDet.IncidentRecoveryTime.ToString().TrimEnd();
                            }


                            table.Rows.Add(Id++.ToString(), inDet.incidentCode, inDet.Id.ToString(), "Incident", null,
                                  null, InfraComponentName, inDet.IncidentName, "", "Start Date: " + inDet.IncidentTime.ToString().TrimEnd() + "<br/>", "End Date: " + IncidentRecoveryTime, inDet.incidentDetails, FinancialCost, inDet.incidentCode);

                            int compoID = Id;
                            //table.Rows.Add(Id++.ToString(), InfraComponentName, inDet.InfraComponentID.ToString(), "InfraComponent", InciIDTree.ToString(),
                            //     null, InfraComponentName, inDet.IncidentName, "", "Start Date: " + inDet.IncidentTime.ToString().TrimEnd() + "<br/>", "End Date: " + IncidentRecoveryTime, inDet.incidentDetails, "-", inDet.incidentCode);

                            table.Rows.Add(Id++.ToString(), InfraComponentName, inDet.InfraComponentID.ToString(), "InfraComponent", InciIDTree.ToString(), null);

                            foreach (var inSum in inciMgtSumByIncidentID)
                            {

                                bool IsBSpresent = false;
                                Double BfCost = 0;
                                Double BSCost = 0;
                                int ParentBFTree = 0;
                                IsBSpresent = lstParentBFid.Contains(inSum.ParentBFID.ToString().ToLower());
                                //if (!IsBSpresent)
                                //{
                                if (inSum.ParentBFID > 0)
                                {
                                    BFName = facade.GetBusinessFunctionById(inSum.ParentBFID).Name;

                                    BfCost = GetBusinessFunctionCost(inSum.ParentBFID, dtCost);
                                    ParentBFTree = Id;
                                    lstParentBFid.Add(inSum.ParentBFID.ToString().ToLower());
                                    //lstParentBFid.Add("ParentBFtreeID:" + Id.ToString());


                                    //table.Rows.Add(Id++.ToString(), BFName, inSum.ParentBFID, "BF", compoID.ToString(),
                                    //  ((BIImpactType)(inSum.ParentBFImpactID)).ToDescription(), InfraComponentName, inDet.IncidentName, "", "Start Date: " + inDet.IncidentTime.ToString().TrimEnd() + "<br/>", "End Date: " + inciMgtSumBy, inDet.incidentDetails, BfCost, inDet.incidentCode);

                                    table.Rows.Add(Id++.ToString(), BFName, inSum.ParentBFID, "BF", compoID.ToString(), ((BIImpactType)(inSum.ParentBFImpactID)).ToDescription());
                                }



                                if (inSum.ParentBSID > 0)
                                {
                                    var bsdetails = facade.GetBusinessServiceById(inSum.ParentBSID);
                                    if (bsdetails != null)
                                    {
                                        BSName = bsdetails.Name;

                                        BSCost = GetBusinessServiceCost(inSum.ParentBSID, dtBSCost);

                                        //table.Rows.Add(Id++.ToString(), BSName, inSum.ParentBSID.ToString(), "BS", ParentBFTree.ToString(),
                                        //        ((BIImpactType)(inSum.ParentBSImpactID)).ToDescription(), InfraComponentName, inDet.IncidentName, "", "Start Date: " + inDet.IncidentTime.ToString().TrimEnd() + "<br/>", "End Date: " + IncidentRectime, inDet.incidentDetails, BSCost, inDet.incidentCode);

                                        table.Rows.Add(Id++.ToString(), BSName, inSum.ParentBSID.ToString(), "BS", ParentBFTree.ToString(),
                                                ((BIImpactType)(inSum.ParentBSImpactID)).ToDescription());
                                    }
                                }
                                //}
                                int childBFTree = 0;
                                if (inSum.ChildBFID > 0)
                                {
                                    var ChildBFDetails = facade.GetBusinessFunctionById(inSum.ChildBFID);
                                    if (ChildBFDetails != null)
                                    {
                                        BFName = ChildBFDetails.Name;
                                        int index = lstParentBFid.IndexOf(inSum.ParentBFID.ToString());
                                        BfCost = GetBusinessFunctionCost(inSum.ChildBFID, dtCost);
                                        childBFTree = Id;


                                        //table.Rows.Add(Id++.ToString(), BFName, inSum.ChildBFID, "BF", ParentBFTree.ToString(),
                                        //    ((BIImpactType)(inSum.ChildBFImpactID)).ToDescription(), InfraComponentName, inDet.IncidentName, "", "Start Date: " + inDet.IncidentTime.ToString() + "<br/>", "End Date: " + IncidentRectime, inDet.incidentDetails, BfCost, inDet.incidentCode);

                                        table.Rows.Add(Id++.ToString(), BFName, inSum.ChildBFID, "BF", ParentBFTree.ToString(),
                                            ((BIImpactType)(inSum.ChildBFImpactID)).ToDescription());
                                    }
                                }
                                if (inSum.ChildBSID > 0)
                                {
                                    var ChisldBSdetails = facade.GetBusinessServiceById(inSum.ChildBSID);
                                    if (ChisldBSdetails != null)
                                    {
                                        BSName = ChisldBSdetails.Name;

                                        BSCost = GetBusinessServiceCost(inSum.ChildBSID, dtBSCost);


                                        string IncidentRectime = string.Empty;

                                        if (inDet.Status == 1)
                                        {
                                            IncidentRectime = "N/A";
                                        }
                                        else
                                        {
                                            IncidentRectime = inDet.IncidentRecoveryTime.ToString().TrimEnd();
                                        }

                                        table.Rows.Add(Id++.ToString(), BSName, inSum.ChildBSID.ToString(), "BS", childBFTree.ToString(),
                                                ((BIImpactType)(inSum.ParentBSImpactID)).ToDescription(), InfraComponentName, inDet.IncidentName, "", "Start Date: " + inDet.IncidentTime.ToString().TrimEnd() + "<br/>", "End Date: " + IncidentRectime, inDet.incidentDetails, BSCost, inDet.incidentCode);
                                    }
                                }

                            }
                        }
                    }
                }
            }

            }

        private Double GetFinancialCost(DataTable dtBSCost)
        {
            Double financialCost = 0;
            for (int i = 0; i < dtBSCost.Rows.Count; i++)
            {
                financialCost += Convert.ToDouble(dtBSCost.Rows[i]["Cost"].ToString());
            }
            return financialCost;
        }

        private Double GetBusinessServiceCost(int BSID, DataTable dtBSCost)
        {
            try
            {
                DataView dv = (dtBSCost.AsDataView());
                Double cost = 0;
                dv.RowFilter = "[BSID]=" + BSID;
                if (dv != null && dv.Count > 0)
                {
                    cost = Convert.ToDouble(dv[0][3]);
                }
                return cost;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private Double GetBusinessFunctionCost(int BFId, DataTable dtCost)
        {
            try
            {
                DataView dv = (dtCost.AsDataView());
                Double cost = 0;
                dv.RowFilter = "[BFID]=" + BFId;
                if (dv != null && dv.Count > 0)
                {
                    cost = Convert.ToDouble(dv[0][7]);
                }
                return cost;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// Get Infracomponenet name by its id and type ex type server or database
        /// </summary>
        /// <param name="p1"></param>
        /// <param name="p2"></param>
        private String GetInfraComponentNameByIdAndType(int InfracomponentId, string InfraComponentType, string JOBNAME, int IncidentID)
        {

            #region previuos code with Queue and Process changes


            //String componentName = string.Empty;
            //string DBcomponentName = string.Empty;
            //if (InfraComponentType == InfraobjectComponentType.Server.ToDescription())
            //{
            //    var serverDetails = Facade.GetServerById(InfracomponentId);
            //    componentName = CryptographyHelper.Md5Decrypt(serverDetails.IPAddress) + "(Server)";
            //    DBcomponentName = serverDetails.Name;
            //}
            //else if (InfraComponentType == InfraobjectComponentType.Database.ToDescription())
            //{
            //    var DBDetails = Facade.GetDatabaseBaseById(InfracomponentId);
            //    String DatabaseName = getDataBaseName(DBDetails.DatabaseType, DBDetails.Id);
            //    componentName = DatabaseName + "(DB)";
            //}
            //return componentName;

            #endregion previuos code with Queue and Process changes

            String componentName = string.Empty;
            string DBcomponentName = string.Empty;
            IEnumerable<IncidentManagementNew> incidentMgtDetails = null;
            IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>(); 

            if (InfraComponentType == InfraobjectComponentType.Server.ToDescription() && JOBNAME.Trim().ToLower().Equals("monitorprserverstatus"))
            {
                var serverDetails = Facade.GetServerById(InfracomponentId);
                componentName = CryptographyHelper.Md5Decrypt(serverDetails.IPAddress) + "(Server)";
                DBcomponentName = serverDetails.Name;
            }
            else if (InfraComponentType == InfraobjectComponentType.Database.ToDescription() && JOBNAME.Trim().ToLower().Equals("monitorprdatabasestatus"))
            {
                var DBDetails = Facade.GetDatabaseBaseById(InfracomponentId);
                String DatabaseName = getDataBaseName(DBDetails.DatabaseType, DBDetails.Id);
                componentName = DatabaseName + "(DB)";
            }
            else if (JOBNAME.Trim().ToLower().Equals("monitorqueueprocess"))
            {
                 incidentMgtDetails = facade.GetAllIncidentManagementNew();
              //  var details = facade.GetAllIncidentManagementNew();
                if (!IsSuperAdmin && incidentMgtDetails != null)
                {
                    IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                    if (infralst != null)
                    {
                        foreach (var infra in infralst)
                        {

                            var getdetails = from a in incidentMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                            foreach (var data in getdetails)
                            {
                                incidentMgtDetailsUserBased.Add(data);

                            }
                        }
                    }
                    incidentMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
                }


                var QueueDetails = from a in incidentMgtDetails where a.Id == IncidentID select a;
                if (QueueDetails != null)
                {
                    var QueueList = facade.QueueMoniterGetAll();
                    if (QueueList != null)
                    {
                        var sortbyQ = from a in QueueList where a.Id == Convert.ToInt32(QueueDetails.FirstOrDefault().APPPROCESS) select a;
                        if (sortbyQ != null)
                            componentName = sortbyQ.FirstOrDefault().QUEUENAME + "(Queue)";
                    }
                }

            }
            else if (JOBNAME.Trim().ToLower().Equals("monitorapplicationprocess"))
            {
                 incidentMgtDetails = facade.GetAllIncidentManagementNew();
               // var details = facade.GetAllIncidentManagementNew();
                if (!IsSuperAdmin && incidentMgtDetails != null)
                {
                    IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                    if (infralst != null)
                    {
                        foreach (var infra in infralst)
                        {

                            var getdetails = from a in incidentMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                            foreach (var data in getdetails)
                            {
                                incidentMgtDetailsUserBased.Add(data);

                            }
                        }
                    }
                    incidentMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
                }
                var QueueDetails = from a in incidentMgtDetails where a.Id == IncidentID select a;
                if (QueueDetails != null)
                {
                    var AppList = facade.GetMonitorServicesById(Convert.ToInt32(QueueDetails.FirstOrDefault().APPPROCESS));
                    if (AppList != null)
                        componentName = AppList.ServicePath + "(Process)";
                }

            }
            //else if (JOBNAME.Trim().ToLower().Equals("monitorworkflowprocess"))
            //{
            //    var incidentMgtDetails = facade.GetAllIncidentManagementNew();
            //    var QueueDetails = from a in incidentMgtDetails where a.Id == IncidentID select a;
            //    if (QueueDetails != null)
            //    {
            //        var AppList = facade.GetWorkflowActionNewById(Convert.ToInt32(QueueDetails.FirstOrDefault().APPPROCESS));
            //        if (AppList != null)
            //            componentName = AppList.Name + "(Process)";
            //    }

            //}
            return componentName;



        }

        private String GetInfraDBComponentNameByIdAndType(int InfracomponentId, string InfraComponentType)
        {
            String componentName = string.Empty;
            if (InfraComponentType == InfraobjectComponentType.Server.ToDescription())
            {
                var serverDetails = Facade.GetServerById(InfracomponentId);
                componentName = serverDetails.Name;
            }
            else if (InfraComponentType == InfraobjectComponentType.Database.ToDescription())
            {
                var DBDetails = Facade.GetDatabaseBaseById(InfracomponentId);
                String DatabaseName = getDataBaseNameByType(DBDetails.DatabaseType, DBDetails.Id);
                componentName = DatabaseName + "(DB)";
            }
            return componentName;
        }

        /// <summary>
        /// treeview select node changed event
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-24/02/2015</author>
        protected void tvBSHierarchy_SelectedNodeChanged(object sender, EventArgs e)
        {
            int level1 = 0;
            int level2 = 1;
            TreeView tv = (TreeView)sender;

            if (tv.SelectedNode.Parent == null)
            {
                if (tv.SelectedNode.ChildNodes.Count > 0)
                {
                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "renderAjaxData('" + Convert.ToInt32(tv.SelectedNode.Value.Split('/')[0]) + "','" + level1 + "','" + tv.SelectedNode.Value.Split('/')[3].ToString() + "');", true);
                    udpgraph.Update();
                    ShowBSDetails(Convert.ToInt32(tv.SelectedNode.Value.Split('/')[0]));
                }
                else
                {
                    //Need to configure business functions
                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "clearGraph('" + tv.SelectedValue.Split('/')[2].ToString() + "','" + tv.SelectedNode.Value.Split('/')[3].ToString() + "');", true);
                }
            }
            else
            {
                if (tv.SelectedNode.ChildNodes.Count > 0)
                {
                    string nodeTextToRender = tv.SelectedValue.Split('/')[1];
                    string type = tv.SelectedValue.Split('/')[2];
                    string nodeRelation = string.Empty;

                    nodeRelation = GetJsonData(tv.SelectedValue.Split('/')[0].ToInteger());

                    if (!string.IsNullOrEmpty(nodeRelation))
                    {
                        int index = nodeRelation.IndexOf(string.Concat(nodeTextToRender, ":"));
                        if (index == -1)
                        {
                            index = nodeRelation.IndexOf(string.Concat(nodeTextToRender, "/yellow:"));
                            if (index == -1)
                            {
                                index = nodeRelation.IndexOf(string.Concat(nodeTextToRender, "/red:"));
                                if (index == -1)
                                {
                                    index = nodeRelation.IndexOf(string.Concat(nodeTextToRender, "/Hide:"));
                                    if (index == -1)
                                    {
                                        index = nodeRelation.IndexOf(string.Concat(nodeTextToRender, "/Hide/logo/red:"));

                                        if (index == -1)
                                        {
                                            //TO DO-If infraObject component details added to treeview,then need to check conditions for it here.
                                        }
                                        else
                                        {
                                            AjaxCallAndRenderGraph(index, nodeRelation, type, level1, level2, tv.SelectedValue.Split('/')[3].ToString(), Convert.ToInt32(tv.SelectedValue.Split('/')[4].ToString()));
                                        }

                                    }
                                    else
                                    {
                                        AjaxCallAndRenderGraph(index, nodeRelation, type, level1, level2, tv.SelectedValue.Split('/')[3].ToString(), Convert.ToInt32(tv.SelectedValue.Split('/')[4].ToString()));
                                    }
                                }
                                else
                                {
                                    AjaxCallAndRenderGraph(index, nodeRelation, type, level1, level2, tv.SelectedValue.Split('/')[3].ToString(), Convert.ToInt32(tv.SelectedValue.Split('/')[4].ToString()));
                                }

                            }
                            else
                            {
                                AjaxCallAndRenderGraph(index, nodeRelation, type, level1, level2, tv.SelectedValue.Split('/')[3].ToString(), Convert.ToInt32(tv.SelectedValue.Split('/')[4].ToString()));
                            }
                        }
                        else
                        {
                            AjaxCallAndRenderGraph(index, nodeRelation, type, level1, level2, tv.SelectedValue.Split('/')[3].ToString(), Convert.ToInt32(tv.SelectedValue.Split('/')[4].ToString()));
                        }
                    }
                }
                else
                {
                    // No child nodes
                    if (tv.SelectedValue.Split('/')[2].Equals("BF", StringComparison.OrdinalIgnoreCase))
                    {
                        //Need to show configure infraObject message
                        System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "clearGraph('" + tv.SelectedValue.Split('/')[2].ToString() + "','" + tv.SelectedValue.Split('/')[3].ToString() + "');", true);
                    }
                    else if (tv.SelectedValue.Split('/')[2].Equals("IO", StringComparison.OrdinalIgnoreCase))
                    {
                        System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "clearGraph('" + tv.SelectedValue.Split('/')[2].ToString() + "','" + tv.SelectedValue.Split('/')[3].ToString() + "');", true);
                    }
                }
            }
        }

        /// <summary>
        /// treeview select node change event
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-05/03/2015</author>
        protected void tv7DaysIncident_SelectedNodeChanged(object sender, EventArgs e)
        {
            IEnumerable<IncidentManagementNew> allIncident = null;
            IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>(); 
            RadTab rootTab = RadTabStrip1.Tabs[1];
            String[] parentNodeValue;
            int incidentId = 0;
            TreeView tv = (TreeView)sender;
            DrawDiagram(tv, null);
            updIcidentDetailsTabButton.Update();
            if (tv.SelectedNode.Parent == null)
            {
                if (tv.SelectedNode != null)
                {
                    parentNodeValue = (tv.SelectedNode.Value.Split(','));
                    incidentId = Convert.ToInt32(parentNodeValue[0]);
                     allIncident = facade.GetAllIncidentManagementNew();
                   // var details = facade.GetAllIncidentManagementNew();
                    if (!IsSuperAdmin && allIncident != null)
                    {
                        IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                        if (infralst != null)
                        {
                            foreach (var infra in infralst)
                            {

                                var getdetails = from a in allIncident where a.InfraID == Convert.ToInt32(infra.Id) select a;
                                foreach (var data in getdetails)
                                {
                                    incidentMgtDetailsUserBased.Add(data);

                                }
                            }
                        }
                        allIncident = incidentMgtDetailsUserBased.AsEnumerable();
                    }

                    if (allIncident != null && allIncident.Count() > 0)
                    {
                        var incidentById = from a in allIncident where a.Id == incidentId select a;
                        if (incidentById != null)
                        {
                            rootTab.Visible = incidentById.First().Status.Equals(0) ? false : true;

                            updRadTab.Update();

                        }
                    }
                }
            }
        }

        /// <summary>
        /// Current Incident treeview select node change event
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Goraknath Date: 11-Mar-2015</author>
        protected void tvOpenIncident_SelectedNodeChanged(object sender, EventArgs e)
        {
            TreeView tv = (TreeView)sender;
            DrawDiagram(tv, null);
            updIcidentDetailsTabButton.Update();
        }

        /// <summary>
        /// 24HR Incident treeview select node change event
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Goraknath Date: 11-Mar-2015</author>
        protected void tvAllCloseIncident_SelectedNodeChanged(object sender, EventArgs e)
        {
            TreeView tv = (TreeView)sender;
            DrawDiagram(tv, null);
            updIcidentDetailsTabButton.Update();
        }
        #endregion

        //protected void btnTest_Click(object sender, EventArgs e)
        //{
        //    GetImpactedNodes(262, "Database", 0);

        //}

        /*
                private void GetImpactedNodes(int iInfraComponentID, string iInfraComponentTypeID, int iInfraObject)
                {
                    IncidentString = "";
                    DataTable dtInfraImpactRelation = new DataTable();

                    dtInfraImpactRelation.Columns.Add("BSID");
                    dtInfraImpactRelation.Columns.Add("BSName");
                    dtInfraImpactRelation.Columns.Add("BSImpactID");

                    dtInfraImpactRelation.Columns.Add("BFID");
                    dtInfraImpactRelation.Columns.Add("BFName");
                    dtInfraImpactRelation.Columns.Add("BFImpactID");

                    dtInfraImpactRelation.Columns.Add("InfraID");
                    dtInfraImpactRelation.Columns.Add("InfraName");
                    // dtInfraImpactRelation.Columns.Add("InfraImpactID");

                    dtInfraImpactRelation.Columns.Add("InfraCompID");
                    dtInfraImpactRelation.Columns.Add("InfraCompName");
                    dtInfraImpactRelation.Columns.Add("InfraCompType");

                    dtInfraImpactRelation.Columns.Add("ParentBFID");


                    if (iInfraObject == 0)
                    {
                        var objInfraColl = Facade.GetInfraObjectByInfraComponentID(iInfraComponentID, iInfraComponentTypeID);

                        if (objInfraColl == null) return;

                        //  foreach (var objInfra in objInfraColl)
                        //  {
                        //      var objBFColl = Facade.GetBFByInfraobjectID(objInfra.InfraID, objInfra.BFImpactID);

                        //       if (objBFColl == null) return;

                        foreach (var objBF in objInfraColl)
                        {
                            var objImpactedBSColl1 = Facade.GetBSByImpactBF(objBF.BFId, objBF.BFImpactID);

                            if (objImpactedBSColl1 == null) return;

                            foreach (var objImpactedBS1 in objImpactedBSColl1)
                            {

                                dtInfraImpactRelation.Rows.Add(objImpactedBS1.BSId, objImpactedBS1.BSName, objImpactedBS1.BSImpactID,
                                  objBF.BFId, objBF.BFName, objBF.BFImpactID,
                                  objBF.InfraID, objBF.InfraObjectName,
                                  objBF.InfraComponentId, "CName" + objBF.InfraComponentId, objBF.InfraComponentType, 0);
                            }


                            var objImpactedBFColl = Facade.GetBFByImpactedBF(objBF.BFId, objBF.BFImpactID);

                            if (objImpactedBFColl != null)
                            {
                                foreach (var objImpactedBF in objImpactedBFColl)
                                {
                                    if (objImpactedBF.ParentBFId == 0) break;

                                    var objImpactedBSColl2 = Facade.GetBSByImpactBF(objImpactedBF.ParentBFId, objImpactedBF.ParentBFImpactID);

                                    if (objImpactedBSColl2 == null) return;

                                    foreach (var objImpactedBS2 in objImpactedBSColl2)
                                    {
                                        dtInfraImpactRelation.Rows.Add(objImpactedBS2.BSId, objImpactedBS2.BSName, objImpactedBS2.BSImpactID,
                                          objImpactedBF.ChildBFId, objImpactedBF.ChildBFName, objImpactedBF.ChildBFImpactID,
                                              objBF.InfraID, objBF.InfraObjectName,
                                              objBF.InfraComponentId, "CName" + objBF.InfraComponentId, objBF.InfraComponentType, objBF.BFId);
                                    }

                                }
                            }



                            IncidentString = CreateDiagramString(dtInfraImpactRelation);

                        }


                    }
                }

                private string CreateDiagramString(DataTable dtInfraImpactRelation)
                {
                    StringBuilder sb = new StringBuilder();
                    StringBuilder sbChildBFBS = new StringBuilder();

                    if (dtInfraImpactRelation.Rows.Count > 0)
                    {
                        string strIncident = dtInfraImpactRelation.Rows[0]["InfraCompName"].ToString();
                        sb.Append("IncidentName:" + strIncident + "/red");
                        sb.Append(";");
                        sb.Append(strIncident + "/red:");
                        string strInfraCompType = dtInfraImpactRelation.Rows[0]["InfraCompType"].ToString();

                        if (dtInfraImpactRelation.Rows.Count > 0)
                        {
                            foreach (DataRow dr in dtInfraImpactRelation.Rows)
                            {
                                string BusinessFunctionName = dr["BFName"].ToString();
                                string BusinessImpact = GetImpactTpye(dr["BFImpactID"].ToString());
                                sb.Append(BusinessFunctionName + "/" + BusinessImpact + ",");

                            }
                            sb.Remove(sb.Length - 1, 1);
                            sb.Append(";");
                            foreach (DataRow drInnerData in dtInfraImpactRelation.Rows)
                            {

                                if (Convert.ToInt16(drInnerData["ParentBFID"]) > 0) continue;


                                string BusinessFunctionName = drInnerData["BFName"].ToString();
                                string BusinessFunctionImpact = GetImpactTpye(drInnerData["BFImpactID"].ToString());
                                string BusinessServiceName = drInnerData["BSName"].ToString();
                                string BusinessServiceImpact = GetImpactTpye(drInnerData["BSImpactID"].ToString());

                                sb.Append(BusinessFunctionName + "/" + BusinessFunctionImpact + ":" + BusinessServiceName + "/" + BusinessServiceImpact);


                                if (Convert.ToInt16(drInnerData["ParentBFID"]) == 0)
                                {
                                    DataTable dtChildBF = dtInfraImpactRelation.Clone();
                                    DataTable dtTemp = dtInfraImpactRelation.Clone();

                                    DataRow[] drAll = dtInfraImpactRelation.Select("ParentBFID =" + drInnerData["BFID"].ToString());

                                    if (drAll != null) { foreach (DataRow drSelected in drAll) { dtChildBF.ImportRow(drSelected); } }

                                    foreach (DataRow drBF in dtChildBF.Rows)
                                    {
                                        sb.Append(",");
                                        string BusinessFunctionNameChild = drBF["BFName"].ToString();
                                        string BusinessFunctionImpactChild = GetImpactTpye(drBF["BFImpactID"].ToString());
                                        sb.Append(BusinessFunctionNameChild + "/" + BusinessFunctionImpactChild);



                                        string BusinessServiceNameChild = drBF["BSName"].ToString();
                                        string BusinessServiceImpactChild = GetImpactTpye(drBF["BSImpactID"].ToString());

                                        sbChildBFBS.Append(BusinessFunctionNameChild + "/" + BusinessFunctionImpactChild + ":" + BusinessServiceNameChild + "/" + BusinessServiceImpactChild + ",");
                                    }

                                    sb.Append(";");
                                    sb.Append(sbChildBFBS.ToString());
                                }
                            }
                        }
                    }

                    string str = sb.ToString().Remove(sb.ToString().Length - 1, 1);


                    return str;

                }

                private string GetImpactTpye(string iImpact)
                {
                    string ReturnString = string.Empty;
                    switch (iImpact)
                    {
                        case "1":
                            {
                                ReturnString = "PI";
                                break;
                            }
                        case "2":
                            {
                                ReturnString = "MI";
                                break;
                            }
                        case "3":
                            {
                                ReturnString = "TI";
                                break;
                            }
                    }

                    return ReturnString;

                }*/

        #region Methods added by Neeraj


        private void GetImpactedNodes(int iInfraComponentID, string iInfraComponentTypeID, int iInfraObject, String IncidentName, int IncidentID, string JOBNAME)
        {

            String InfraComponenetName = "";

            if (iInfraObject == 0)
            {


                var inciMgtSumDetails = facade.GetAllIncidentManagementSummary();
                if (inciMgtSumDetails != null)
                {
                    var inciMgtSumDetailsById = from inSum in inciMgtSumDetails where inSum.IncidentID == IncidentID orderby inSum.ParentBSID, inSum.ChildBSID select inSum;

                    if (inciMgtSumDetailsById == null) return;

                    InfraComponenetName = GetInfraComponentNameByIdAndType(iInfraComponentID, iInfraComponentTypeID, JOBNAME, IncidentID);
                    lblImpactedComp.Text = InfraComponenetName;
                    lblImpactedComp.ToolTip = InfraComponenetName;
                    lblDBCompName.Text = GetInfraDBComponentNameByIdAndType(iInfraComponentID, iInfraComponentTypeID);
                    lblDBCompName.ToolTip = lblDBCompName.Text;

                 
                    // bind cost details with bf and BS 
                    var inciMgtSumDetailsForCost = facade.GetIncidentManagementSummaryByIncidentID(IncidentID);
                    if (inciMgtSumDetailsForCost != null && inciMgtSumDetailsForCost.Count() > 0)
                    {
                        if (inciMgtSumDetailsForCost.FirstOrDefault().ParentBFCost.Equals(0.0) && inciMgtSumDetailsForCost.FirstOrDefault().ChildBFCost.Equals(0.0))
                        {

                            var ParentBFid = inciMgtSumDetailsForCost.FirstOrDefault().ParentBFID;

                            string Name = string.Empty;
                            int ProfileID = 0;

                            IList<BFBIAMatrix> objBFBIAMatrix;

                            if (Convert.ToInt32(ParentBFid) > 0)
                            {
                                objBFBIAMatrix = Facade.GetBFBIAMatrixByBFId(Convert.ToInt32(ParentBFid.ToString()));
                                if (objBFBIAMatrix != null && objBFBIAMatrix.Count() > 0)
                                {
                                    ProfileID = objBFBIAMatrix != null ? objBFBIAMatrix.FirstOrDefault().ProfileID : 0;
                                }
                                else
                                { Name = "NA"; }
                                if (ProfileID > 0)
                                {


                                }
                            }

                        }
                    }

                    BindCostDetails(inciMgtSumDetailsForCost);
                    if (iInfraComponentTypeID.Equals(InfraobjectComponentType.Server.ToDescription()))
                        lblIconComponent.CssClass = "server-icon vertical-sub";

                    else
                        lblIconComponent.CssClass = "icon-database vertical-sub";

                    lblComponentName.Text = InfraComponenetName;
                    /* foreach (var inciMgtSum in inciMgtSumDetailsById)
                     {


                         GetBIACost(Convert.ToString(inciMgtSum.ParentBFID), Convert.ToString(inciMgtSum.ParentBFImpactID), ref iUpto2Hours, ref iUpto4Hours, ref iUpto8Hours, ref iUpto12Hours, ref iUpto24Hours, ref iUpto48Hours, ref iUpto72Hours, ref iUpto1Week, ref iUpto2Weeks, ref iUpto1Month);

                         var BSdetails = facade.GetBusinessServiceById(inciMgtSum.ParentBSID);
                         BSName = BSdetails != null ? BSdetails.Name : string.Empty;
                         var BFdetails = facade.GetBusinessFunctionById(inciMgtSum.ParentBFID);
                         ParentBFName = BFdetails != null ? BFdetails.Name : string.Empty;

                         dtInfraImpactRelation.Rows.Add(dtInfraImpactRelation.Rows.Count + 1, IncidentName, inciMgtSum.ParentBSID, BSName, inciMgtSum.ParentBSImpactID,
                               inciMgtSum.ParentBFID, ParentBFName, inciMgtSum.ParentBFImpactID,
                               "", "",
                               iInfraComponentID, InfraComponenetName, iInfraComponentTypeID, 0, iUpto2Hours, iUpto4Hours, iUpto8Hours, iUpto12Hours, iUpto24Hours, iUpto48Hours, iUpto72Hours, iUpto1Week, iUpto2Weeks, iUpto1Month);


                         var ChildBFdetails = facade.GetBusinessFunctionById(inciMgtSum.ChildBFID);
                         ChildBFName = ChildBFdetails != null ? ChildBFdetails.Name : string.Empty;
                         GetBIACost(Convert.ToString(inciMgtSum.ChildBFID), Convert.ToString(inciMgtSum.ChildBFImpactID), ref iUpto2Hours, ref iUpto4Hours, ref iUpto8Hours, ref iUpto12Hours, ref iUpto24Hours, ref iUpto48Hours, ref iUpto72Hours, ref iUpto1Week, ref iUpto2Weeks, ref iUpto1Month);

                         dtInfraImpactRelation.Rows.Add(dtInfraImpactRelation.Rows.Count + 1, IncidentName, inciMgtSum.ParentBSID, BSName, inciMgtSum.ParentBSImpactID,
                           inciMgtSum.ChildBFID, ChildBFName, inciMgtSum.ChildBFImpactID,
                               "", "",
                               iInfraComponentID, InfraComponenetName, iInfraComponentTypeID, inciMgtSum.ParentBFID, iUpto2Hours, iUpto4Hours, iUpto8Hours, iUpto12Hours, iUpto24Hours, iUpto48Hours, iUpto72Hours, iUpto1Week, iUpto2Weeks, iUpto1Month);


                     }




                     ViewState["dtImpact"] = dtInfraImpactRelation;*/

                    IncidentString = CreateDiagramString(IncidentID, InfraComponenetName, IncidentName, JOBNAME);
                }

                //  CalculateBIACost(dtInfraImpactRelation);

            }
        }
        /// <summary>
        /// added by GSK date: 23-Mar-2015 
        /// Bind cost details with BF and BS on selected Incident.
        /// </summary>
        /// <param name="IncidentID"></param>
        private void BindCostDetails(IEnumerable<IncidentManagementSummary> inciMgtSumDetailsById)
        {
            DataTable dtCost = new DataTable();

            dtCost.Columns.Add("ID", typeof(Int32));
            dtCost.Columns.Add("BSID", typeof(Int32));
            dtCost.Columns.Add("BSImpactID", typeof(Int32));
            dtCost.Columns.Add("BFID", typeof(Int32));
            dtCost.Columns.Add("BFImpactID", typeof(Int32));
            dtCost.Columns.Add("BFName");
            dtCost.Columns.Add("BSName");
            dtCost.Columns.Add("BFCost", typeof(Double));
            dtCost.Columns.Add("BSCost", typeof(Double));

            List<string> addedParentBf = new List<string>();


            foreach (var incSum in inciMgtSumDetailsById)
            {

                #region Totally Impacted
                var TIParentBF = from TIBF in inciMgtSumDetailsById
                                 where TIBF.ParentBFImpactID == TotallyImpactId // Convert.ToInt32(BIImpactType.Totally)
                                     && TIBF.ParentBFID == incSum.ParentBFID
                                 select TIBF;
                if (TIParentBF.Count() > 0 && TIParentBF != null)
                {
                    var TIparentBFFirst = TIParentBF.First();

                    BusinessFunction ParentBfdetails = new BusinessFunction();
                    BusinessService ParentBsdetails = new BusinessService();
                    if (TIparentBFFirst.ParentBFID > 0)
                    {
                        ParentBfdetails = facade.GetBusinessFunctionById(TIparentBFFirst.ParentBFID);
                    }
                    if (TIparentBFFirst.ParentBSID > 0)
                    {
                        ParentBsdetails = facade.GetBusinessServiceById(TIparentBFFirst.ParentBSID);
                    }


                    if (!addedParentBf.Contains(TIparentBFFirst.ParentBFID.ToString()))
                    {
                        addedParentBf.Add(TIparentBFFirst.ParentBFID.ToString());
                        dtCost.Rows.Add(
                       TIparentBFFirst.Id,
                       TIparentBFFirst.ParentBSID,
                       TIparentBFFirst.ParentBSImpactID,
                       TIparentBFFirst.ParentBFID,
                       TIparentBFFirst.ParentBFImpactID,
                       ParentBfdetails != null ? ParentBfdetails.Name : string.Empty,
                       ParentBsdetails != null ? ParentBsdetails.Name : string.Empty,
                       TIparentBFFirst.ParentBFCost,
                       0
                       );
                    }
                }
                //child BF TI
                var TIChildBFIMpacted = from TIBFHild in inciMgtSumDetailsById
                                        where TIBFHild.ChildBFImpactID ==  TotallyImpactId  // Convert.ToInt32(BIImpactType.Totally)
                                            && TIBFHild.ChildBFID == incSum.ChildBFID
                                        select TIBFHild;
                if (TIChildBFIMpacted.Count() > 0 && TIChildBFIMpacted != null)
                {
                    var TIChildBF = TIChildBFIMpacted.First();

                    BusinessFunction ChildBfdetailsCH = new BusinessFunction();
                    BusinessService ChildBsdetailsCH = new BusinessService();
                    if (TIChildBF.ChildBFID > 0)
                    {
                        ChildBfdetailsCH = facade.GetBusinessFunctionById(TIChildBF.ChildBFID);
                    }
                    if (TIChildBF.ChildBSID > 0)
                    {
                        ChildBsdetailsCH = facade.GetBusinessServiceById(TIChildBF.ChildBSID);
                    }


                    if (!addedParentBf.Contains(TIChildBF.ChildBFID.ToString()))
                    {
                        addedParentBf.Add(TIChildBF.ChildBFID.ToString());
                        dtCost.Rows.Add(
                       TIChildBF.Id,
                       TIChildBF.ChildBSID,
                       TIChildBF.ChildBSImpactID,
                       TIChildBF.ChildBFID,
                       TIChildBF.ChildBFImpactID,
                       ChildBfdetailsCH != null ? ChildBfdetailsCH.Name : string.Empty,
                       ChildBsdetailsCH != null ? ChildBsdetailsCH.Name : string.Empty,
                       TIChildBF.ChildBFCost,
                       0
                       );
                    }
                }
                #endregion
                #region Majorly Impacted

                var MajorlyParentBFIMpacted = from MIBF in inciMgtSumDetailsById
                                              where MIBF.ParentBFImpactID == MajorlyImpactId  // Convert.ToInt32(BIImpactType.Majorly)
                                                  && MIBF.ParentBFID == incSum.ParentBFID
                                              select MIBF;
                if (MajorlyParentBFIMpacted.Count() > 0 && MajorlyParentBFIMpacted != null)
                {
                    var MIparentBF = MajorlyParentBFIMpacted.First();


                    BusinessFunction ParentBfdetailsPA = new BusinessFunction();
                    BusinessService ParentBsdetailsPA = new BusinessService();
                    if (MIparentBF.ParentBFID > 0)
                    {
                        ParentBfdetailsPA = facade.GetBusinessFunctionById(MIparentBF.ParentBFID);
                    }
                    if (MIparentBF.ParentBSID > 0)
                    {
                        ParentBsdetailsPA = facade.GetBusinessServiceById(MIparentBF.ParentBSID);
                    }
                    if (!addedParentBf.Contains(MIparentBF.ParentBFID.ToString()))
                    {
                        addedParentBf.Add(MIparentBF.ParentBFID.ToString());
                        dtCost.Rows.Add(
                       MIparentBF.Id,
                       MIparentBF.ParentBSID,
                       MIparentBF.ParentBSImpactID,
                       MIparentBF.ParentBFID,
                       MIparentBF.ParentBFImpactID,
                       ParentBfdetailsPA != null ? ParentBfdetailsPA.Name : string.Empty,
                       ParentBsdetailsPA != null ? ParentBsdetailsPA.Name : string.Empty,
                       MIparentBF.ParentBFCost,
                       0
                       );
                    }
                }
                //child BF MI
                var MajorlyChildBFIMpactedCH = from MIBFHild in inciMgtSumDetailsById
                                               where MIBFHild.ChildBFImpactID == MajorlyImpactId // Convert.ToInt32(BIImpactType.Majorly)
                                                   && MIBFHild.ChildBFID == incSum.ChildBFID
                                               select MIBFHild;
                if (MajorlyChildBFIMpactedCH.Count() > 0 && MajorlyChildBFIMpactedCH != null)
                {
                    var MIChildBF = MajorlyChildBFIMpactedCH.First();


                    BusinessFunction ChildBfdetailsCHMI = new BusinessFunction();
                    BusinessService ChildBsdetailsCHMI = new BusinessService();
                    if (MIChildBF.ChildBFID > 0)
                    {
                        ChildBfdetailsCHMI = facade.GetBusinessFunctionById(MIChildBF.ChildBFID);
                    }
                    if (MIChildBF.ChildBSID > 0)
                    {
                        ChildBsdetailsCHMI = facade.GetBusinessServiceById(MIChildBF.ChildBSID);
                    }

                    if (!addedParentBf.Contains(MIChildBF.ChildBFID.ToString()))
                    {
                        addedParentBf.Add(MIChildBF.ChildBFID.ToString());
                        dtCost.Rows.Add(
                       MIChildBF.Id,
                       MIChildBF.ChildBSID,
                       MIChildBF.ChildBSImpactID,
                       MIChildBF.ChildBFID,
                       MIChildBF.ChildBFImpactID,
                       ChildBfdetailsCHMI != null ? ChildBfdetailsCHMI.Name : string.Empty,
                       ChildBfdetailsCHMI != null ? ChildBsdetailsCHMI.Name : string.Empty,
                       MIChildBF.ChildBFCost,
                       0
                       );
                    }
                }
                #endregion Majorly impacted

                #region Partially Impacted

                var PartiallyParentBFIMpacted = from PIBF in inciMgtSumDetailsById
                                                where PIBF.ParentBFImpactID == PartiallyImpactId // Convert.ToInt32(BIImpactType.Partially)
                                                    && PIBF.ParentBFID == incSum.ParentBFID
                                                select PIBF;
                if (PartiallyParentBFIMpacted.Count() > 0 && PartiallyParentBFIMpacted != null)
                {
                    var PIparentBF = PartiallyParentBFIMpacted.First();



                    BusinessFunction ParentBfdetailsPI = new BusinessFunction();
                    BusinessService ParentBsdetailsPI = new BusinessService();
                    if (PIparentBF.ParentBFID > 0)
                    {
                        ParentBfdetailsPI = facade.GetBusinessFunctionById(PIparentBF.ParentBFID);
                    }
                    if (PIparentBF.ParentBSID > 0)
                    {
                        ParentBsdetailsPI = facade.GetBusinessServiceById(PIparentBF.ParentBSID);
                    }
                    if (!addedParentBf.Contains(PIparentBF.ParentBFID.ToString()))
                    {
                        addedParentBf.Add(PIparentBF.ParentBFID.ToString());
                        dtCost.Rows.Add(
                       PIparentBF.Id,
                       PIparentBF.ParentBSID,
                       PIparentBF.ParentBSImpactID,
                       PIparentBF.ParentBFID,
                       PIparentBF.ParentBFImpactID,
                       ParentBfdetailsPI != null ? ParentBfdetailsPI.Name : string.Empty,
                       ParentBsdetailsPI != null ? ParentBsdetailsPI.Name : string.Empty,
                       PIparentBF.ParentBFCost,
                       0
                       );
                    }
                }
                //child BF MI
                var PartiallyChildBFIMpactedCH = from PIBFHild in inciMgtSumDetailsById
                                                 where PIBFHild.ChildBFImpactID == PartiallyImpactId  //Convert.ToInt32(BIImpactType.Partially)
                                                     && PIBFHild.ChildBFID == incSum.ChildBFID
                                                 select PIBFHild;
                if (PartiallyChildBFIMpactedCH.Count() > 0 && PartiallyChildBFIMpactedCH != null)
                {
                    var PIChildBF = PartiallyChildBFIMpactedCH.First();

                    BusinessFunction ChildBfdetailsCHPI = new BusinessFunction();
                    BusinessService ChildBsdetailsCHPI = new BusinessService();
                    if (PIChildBF.ChildBFID > 0)
                    {
                        ChildBfdetailsCHPI = facade.GetBusinessFunctionById(PIChildBF.ChildBFID);
                    }
                    if (PIChildBF.ChildBSID > 0)
                    {
                        ChildBsdetailsCHPI = facade.GetBusinessServiceById(PIChildBF.ChildBSID);
                    }

                    if (!addedParentBf.Contains(PIChildBF.ChildBFID.ToString()))
                    {
                        addedParentBf.Add(PIChildBF.ChildBFID.ToString());
                        dtCost.Rows.Add(
                       PIChildBF.Id,
                       PIChildBF.ChildBSID,
                       PIChildBF.ChildBSImpactID,
                       PIChildBF.ChildBFID,
                       PIChildBF.ChildBFImpactID,
                       ChildBfdetailsCHPI != null ? ChildBfdetailsCHPI.Name : string.Empty,
                       ChildBsdetailsCHPI != null ? ChildBsdetailsCHPI.Name : string.Empty,
                       PIChildBF.ChildBFCost,
                       0
                       );
                    }
                }
                #endregion Partially impacted
            }

            DataTable dtBSCost = new DataTable();

            dtBSCost.Columns.Add("ID", typeof(Int32));
            dtBSCost.Columns.Add("BSID", typeof(Int32));
            dtBSCost.Columns.Add("BSName");
            dtBSCost.Columns.Add("Cost", typeof(Double));
            dtBSCost.Columns.Add("BSImpactID", typeof(Int32));
            List<string> BSList = new List<string>();
            int BSImpactID = 0;
            for (int j = 0; j < dtCost.Rows.Count; j++)
            {
                int BSID = 0;
                Double totalBSCost = 0;
                BSID = Convert.ToInt32(dtCost.Rows[j]["BSID"].ToString());

                if (!BSList.Contains(BSID.ToString()))
                {
                    BSList.Add(BSID.ToString());
                    for (int i = 0; i < dtCost.Rows.Count; i++)
                    {
                        if (BSID == Convert.ToInt32(dtCost.Rows[i]["BSID"].ToString()))
                        {
                            totalBSCost += Convert.ToDouble(dtCost.Rows[i]["BFCost"].ToString());
                        }
                    }


                    var TIBSParent = from TIBSldParent in inciMgtSumDetailsById
                                     where TIBSldParent.ParentBSImpactID == TotallyImpactId // Convert.ToInt32(BIImpactType.Totally)
                                                         && TIBSldParent.ParentBSID == BSID
                                     select TIBSldParent;
                    if (TIBSParent != null && TIBSParent.Count() > 0)
                    {
                        var BSIDFirst = TIBSParent.First();
                        BSImpactID = BSIDFirst.ParentBSImpactID;

                    }
                    else
                    {
                        var TIBSChild = from TIBSldChild in inciMgtSumDetailsById
                                        where TIBSldChild.ChildBSImpactID == TotallyImpactId // Convert.ToInt32(BIImpactType.Totally)
                                                            && TIBSldChild.ChildBSID == BSID
                                        select TIBSldChild;
                        if (TIBSChild != null && TIBSChild.Count() > 0)
                        {
                            var BSIDFirst = TIBSChild.First();
                            BSImpactID = BSIDFirst.ChildBSImpactID;

                        }
                        else
                        {

                            var MIBSParent = from MIBSldParent in inciMgtSumDetailsById
                                             where MIBSldParent.ParentBSImpactID == MajorlyImpactId  // Convert.ToInt32(BIImpactType.Majorly)
                                                                 && MIBSldParent.ParentBSID == BSID
                                             select MIBSldParent;

                            if (MIBSParent != null && MIBSParent.Count() > 0)
                            {
                                var BSIDFirst = MIBSParent.First();
                                BSImpactID = BSIDFirst.ParentBSImpactID;

                            }
                            else
                            {
                                var MIBSChild = from MIBSldChild in inciMgtSumDetailsById
                                                where MIBSldChild.ChildBSImpactID == MajorlyImpactId  // Convert.ToInt32(BIImpactType.Majorly)
                                                                    && MIBSldChild.ChildBSID == BSID
                                                select MIBSldChild;
                                if (MIBSChild != null && MIBSChild.Count() > 0)
                                {
                                    var BSIDFirst = MIBSChild.First();

                                    BSImpactID = BSIDFirst.ChildBSImpactID;

                                }
                                else
                                {
                                    var PIBSParent = from PIBSldParent in inciMgtSumDetailsById
                                                     where PIBSldParent.ParentBSImpactID == PartiallyImpactId // Convert.ToInt32(BIImpactType.Partially)
                                                                         && PIBSldParent.ParentBSID == BSID
                                                     select PIBSldParent;

                                    if (PIBSParent != null && PIBSParent.Count() > 0)
                                    {
                                        var BSIDFirst = PIBSParent.First();
                                        BSImpactID = BSIDFirst.ParentBSImpactID;

                                    }
                                    else
                                    {
                                        var PIBSChild = from PIBSldChild in inciMgtSumDetailsById
                                                        where PIBSldChild.ChildBSImpactID == PartiallyImpactId // Convert.ToInt32(BIImpactType.Partially)
                                                                            && PIBSldChild.ChildBSID == BSID
                                                        select PIBSldChild;

                                        if (PIBSChild != null && PIBSChild.Count() > 0)
                                        {
                                            var BSIDFirst = PIBSChild.First();
                                            BSImpactID = BSIDFirst.ChildBSImpactID;

                                        }
                                    }
                                }
                            }


                        }
                    }
                    dtBSCost.Rows.Add(dtBSCost.Rows.Count + 1, Convert.ToInt32(dtCost.Rows[j]["BSID"].ToString()), dtCost.Rows[j]["BSName"].ToString(), totalBSCost, BSImpactID);
                }
            }

            //Showing Business function cost with BS and BF name
            for (int j = 0; j < dtCost.Rows.Count; j++)
            {
                var CostPanel = new Panel();
                var IconLabel = new Label();
                var lblBFName = new Label();
                string BFName = "";
                string BSName = "";
                double BFPecentage = 0.0;

                BSName = dtCost.Rows[j]["BSName"].ToString();
                BFName = dtCost.Rows[j]["BFName"].ToString();

                DataRow[] rows = dtBSCost.Select("BSID=" + dtCost.Rows[j]["BSID"]);
                if (rows.Count() > 0)
                {
                    if (Convert.ToDouble(dtCost.Rows[j]["BFCost"].ToString()) != 0)
                    {
                        BFPecentage = Math.Round((Convert.ToDouble(dtCost.Rows[j]["BFCost"].ToString()) / Convert.ToDouble(rows[0]["Cost"].ToString())) * 100, 2);
                    }
                    else
                    {
                        BFPecentage = 0;
                    }
                }
                CostPanel.ID = "pnlCost" + j;
                lblBFName.Text = GetLogo(Convert.ToInt32(dtCost.Rows[j]["BFImpactID"].ToString())) + BFName;
                lblBFName.Text += " -> " + "$" + dtCost.Rows[j]["BFCost"].ToString();
                if (!string.IsNullOrEmpty(BSName) && !string.IsNullOrWhiteSpace(BSName))
                {
                    lblBFName.Text += "(" + BSName + ")" + "(" + BFPecentage + "%)";
                }
                CostPanel.CssClass = "border-margin-bottom";
                CostPanel.Controls.Add(lblBFName);
                pnlappend.Controls.Add(CostPanel);

            }

            double FinancialImpactCost = 0;
            for (int k = 0; k < dtBSCost.Rows.Count; k++)
            {
                FinancialImpactCost += Convert.ToDouble(dtBSCost.Rows[k]["Cost"].ToString());
            }
            for (int j = 0; j < dtBSCost.Rows.Count; j++)
            {
                var BSCostPanel = new Panel();
                var IconLabel = new Label();
                var lblBSName1 = new Label();
                string BSName = "";
                double BSPercentage = 0.0;
                BSCostPanel.CssClass = "border-margin-bottom";

                if (dtBSCost.Rows[j]["Cost"].ToString().IsNotNullOrEmpty())
                {
                    if (Convert.ToDouble(dtBSCost.Rows[j]["Cost"].ToString()) != 0)
                    {
                        BSPercentage = Math.Round((Convert.ToDouble(dtBSCost.Rows[j]["Cost"].ToString()) / FinancialImpactCost) * 100, 2);
                    }
                    else
                    {
                        BSPercentage = 0;
                    }
                    BSName = dtBSCost.Rows[j]["BSName"].ToString();
                    if (!string.IsNullOrEmpty(BSName) && !string.IsNullOrWhiteSpace(BSName))
                    {
                        BSCostPanel.ID = "BSpnlCost" + j;
                        lblBSName1.Text = GetLogo(Convert.ToInt32(dtBSCost.Rows[j]["BSImpactID"].ToString())) + BSName;
                        lblBSName1.Text += " -> " + "$" + dtBSCost.Rows[j]["Cost"].ToString() + "(" + BSPercentage + "%)";
                        BSCostPanel.Controls.Add(lblBSName1);
                        pnlBSCostAppend.Controls.Add(BSCostPanel);
                    }


                }



            }
            // lblFinancialImpact.Text = "$" + FinancialImpactCost.ToString();
            //lblFinancialImpact.ToolTip = "$" + FinancialImpactCost.ToString();
            lblFinancialImapactTotalCost.Text = "$" + FinancialImpactCost.ToString();
        }

        private void GetCostTable(IEnumerable<IncidentManagementSummary> inciMgtSumDetailsById, ref DataTable dtCost, ref DataTable dtBSCost)
        {
            dtCost = new DataTable();

            dtCost.Columns.Add("ID", typeof(Int32));
            dtCost.Columns.Add("BSID", typeof(Int32));
            dtCost.Columns.Add("BSImpactID", typeof(Int32));
            dtCost.Columns.Add("BFID", typeof(Int32));
            dtCost.Columns.Add("BFImpactID", typeof(Int32));
            dtCost.Columns.Add("BFName");
            dtCost.Columns.Add("BSName");
            dtCost.Columns.Add("BFCost", typeof(Double));
            dtCost.Columns.Add("BSCost", typeof(Double));

            List<string> addedParentBf = new List<string>();

            foreach (var incSum in inciMgtSumDetailsById)
            {

                #region Totally Impacted
                var TIParentBF = from TIBF in inciMgtSumDetailsById
                                 where TIBF.ParentBFImpactID ==  TotallyImpactId  // Convert.ToInt32(BIImpactType.Totally)
                                     && TIBF.ParentBFID == incSum.ParentBFID
                                 select TIBF;
                if (TIParentBF.Count() > 0 && TIParentBF != null)
                {
                    var TIparentBFFirst = TIParentBF.First();
                    BusinessFunction ParentBfdetails = null;
                    BusinessService ParentBsdetails = null;
                    if (TIparentBFFirst.ParentBFID > 0)
                    {
                        ParentBfdetails = facade.GetBusinessFunctionById(TIparentBFFirst.ParentBFID);
                    }
                    if (TIparentBFFirst.ParentBFID > 0)
                    {
                        ParentBsdetails = facade.GetBusinessServiceById(TIparentBFFirst.ParentBSID);
                    }


                    if (!addedParentBf.Contains(TIparentBFFirst.ParentBFID.ToString()))
                    {
                        addedParentBf.Add(TIparentBFFirst.ParentBFID.ToString());
                        dtCost.Rows.Add(
                       TIparentBFFirst.Id,
                       TIparentBFFirst.ParentBSID,
                       TIparentBFFirst.ParentBSImpactID,
                       TIparentBFFirst.ParentBFID,
                       TIparentBFFirst.ParentBFImpactID,
                       ParentBfdetails != null ? ParentBfdetails.Name : string.Empty,
                       ParentBsdetails != null ? ParentBsdetails.Name : string.Empty,
                       TIparentBFFirst.ParentBFCost,
                       0
                       );
                    }
                }
                //child BF TI
                var TIChildBFIMpacted = from TIBFHild in inciMgtSumDetailsById
                                        where TIBFHild.ChildBFImpactID == TotallyImpactId  // Convert.ToInt32(BIImpactType.Totally)
                                            && TIBFHild.ChildBFID == incSum.ChildBFID
                                        select TIBFHild;
                if (TIChildBFIMpacted.Count() > 0 && TIChildBFIMpacted != null)
                {
                    var TIChildBF = TIChildBFIMpacted.First();

                    BusinessFunction ChildBfdetailsCH = null;
                    BusinessService ChildBsdetailsCH = null;
                    if (TIChildBF.ChildBFID > 0)
                    {
                        ChildBfdetailsCH = facade.GetBusinessFunctionById(TIChildBF.ChildBFID);
                    }
                    if (TIChildBF.ChildBSID > 0)
                    {
                        ChildBsdetailsCH = facade.GetBusinessServiceById(TIChildBF.ChildBSID);
                    }

                    if (!addedParentBf.Contains(TIChildBF.ChildBFID.ToString()))
                    {
                        addedParentBf.Add(TIChildBF.ChildBFID.ToString());
                        dtCost.Rows.Add(
                       TIChildBF.Id,
                       TIChildBF.ChildBSID,
                       TIChildBF.ChildBSImpactID,
                       TIChildBF.ChildBFID,
                       TIChildBF.ChildBFImpactID,
                       ChildBfdetailsCH != null ? ChildBfdetailsCH.Name : string.Empty,
                       ChildBsdetailsCH != null ? ChildBsdetailsCH.Name : string.Empty,
                       TIChildBF.ChildBFCost,
                       0
                       );
                    }
                }
                #endregion
                #region Majorly Impacted

                var MajorlyParentBFIMpacted = from MIBF in inciMgtSumDetailsById
                                              where MIBF.ParentBFImpactID == MajorlyImpactId  // Convert.ToInt32(BIImpactType.Majorly)
                                                  && MIBF.ParentBFID == incSum.ParentBFID
                                              select MIBF;
                if (MajorlyParentBFIMpacted.Count() > 0 && MajorlyParentBFIMpacted != null)
                {
                    var MIparentBF = MajorlyParentBFIMpacted.First();

                    BusinessFunction ParentBfdetailsPA = null;
                    BusinessService ParentBsdetailsPA = null;
                    if (MIparentBF.ParentBFID > 0)
                    {
                        ParentBfdetailsPA = facade.GetBusinessFunctionById(MIparentBF.ParentBFID);
                    }
                    if (MIparentBF.ParentBSID > 0)
                    {
                        ParentBsdetailsPA = facade.GetBusinessServiceById(MIparentBF.ParentBSID);
                    }


                    if (!addedParentBf.Contains(MIparentBF.ParentBFID.ToString()))
                    {
                        addedParentBf.Add(MIparentBF.ParentBFID.ToString());
                        dtCost.Rows.Add(
                       MIparentBF.Id,
                       MIparentBF.ParentBSID,
                       MIparentBF.ParentBSImpactID,
                       MIparentBF.ParentBFID,
                       MIparentBF.ParentBFImpactID,
                       ParentBfdetailsPA != null ? ParentBfdetailsPA.Name : string.Empty,
                       ParentBsdetailsPA != null ? ParentBsdetailsPA.Name : string.Empty,
                       MIparentBF.ParentBFCost,
                       0
                       );
                    }
                }
                //child BF MI
                var MajorlyChildBFIMpactedCH = from MIBFHild in inciMgtSumDetailsById
                                               where MIBFHild.ChildBFImpactID == MajorlyImpactId  // Convert.ToInt32(BIImpactType.Majorly)
                                                   && MIBFHild.ChildBFID == incSum.ChildBFID
                                               select MIBFHild;
                if (MajorlyChildBFIMpactedCH.Count() > 0 && MajorlyChildBFIMpactedCH != null)
                {
                    var MIChildBF = MajorlyChildBFIMpactedCH.First();

                    BusinessFunction ChildBfdetailsCHMI = null;
                    BusinessService ChildBsdetailsCHMI = null;
                    if (MIChildBF.ChildBFID > 0)
                    {
                        ChildBfdetailsCHMI = facade.GetBusinessFunctionById(MIChildBF.ChildBFID);
                    }
                    if (MIChildBF.ChildBSID > 0)
                    {
                        ChildBsdetailsCHMI = facade.GetBusinessServiceById(MIChildBF.ChildBSID);
                    }
                    if (!addedParentBf.Contains(MIChildBF.ChildBFID.ToString()))
                    {
                        addedParentBf.Add(MIChildBF.ChildBFID.ToString());
                        dtCost.Rows.Add(
                       MIChildBF.Id,
                       MIChildBF.ChildBSID,
                       MIChildBF.ChildBSImpactID,
                       MIChildBF.ChildBFID,
                       MIChildBF.ChildBFImpactID,
                       ChildBfdetailsCHMI != null ? ChildBfdetailsCHMI.Name : string.Empty,
                       ChildBsdetailsCHMI != null ? ChildBsdetailsCHMI.Name : string.Empty,
                       MIChildBF.ChildBFCost,
                       0
                       );
                    }
                }
                #endregion Majorly impacted

                #region Partially Impacted

                var PartiallyParentBFIMpacted = from PIBF in inciMgtSumDetailsById
                                                where PIBF.ParentBFImpactID == Convert.ToInt32(BIImpactType.Partially)
                                                    && PIBF.ParentBFID == incSum.ParentBFID
                                                select PIBF;
                if (PartiallyParentBFIMpacted.Count() > 0 && PartiallyParentBFIMpacted != null)
                {
                    var PIparentBF = PartiallyParentBFIMpacted.First();

                    BusinessFunction ParentBfdetailsPI = null;
                    BusinessService ParentBsdetailsPI = null;
                    if (PIparentBF.ParentBFID > 0)
                    {
                        ParentBfdetailsPI = facade.GetBusinessFunctionById(PIparentBF.ParentBFID);
                    }
                    if (PIparentBF.ParentBSID > 0)
                    {
                        ParentBsdetailsPI = facade.GetBusinessServiceById(PIparentBF.ParentBSID);
                    }
                    if (!addedParentBf.Contains(PIparentBF.ParentBFID.ToString()))
                    {
                        addedParentBf.Add(PIparentBF.ParentBFID.ToString());
                        dtCost.Rows.Add(
                       PIparentBF.Id,
                       PIparentBF.ParentBSID,
                       PIparentBF.ParentBSImpactID,
                       PIparentBF.ParentBFID,
                       PIparentBF.ParentBFImpactID,
                       ParentBfdetailsPI != null ? ParentBfdetailsPI.Name : string.Empty,
                       ParentBsdetailsPI != null ? ParentBsdetailsPI.Name : string.Empty,
                       PIparentBF.ParentBFCost,
                       0
                       );
                    }
                }
                //child BF MI
                var PartiallyChildBFIMpactedCH = from PIBFHild in inciMgtSumDetailsById
                                                 where PIBFHild.ChildBFImpactID == Convert.ToInt32(BIImpactType.Partially)
                                                     && PIBFHild.ChildBFID == incSum.ChildBFID
                                                 select PIBFHild;
                if (PartiallyChildBFIMpactedCH.Count() > 0 && PartiallyChildBFIMpactedCH != null)
                {
                    var PIChildBF = PartiallyChildBFIMpactedCH.First();

                    BusinessFunction ChildBfdetailsCHPI = null;
                    BusinessService ChildBsdetailsCHPI = null;
                    if (PIChildBF.ChildBFID > 0)
                    {
                        ChildBfdetailsCHPI = facade.GetBusinessFunctionById(PIChildBF.ChildBFID);
                    }
                    if (PIChildBF.ChildBSID > 0)
                    {
                        ChildBsdetailsCHPI = facade.GetBusinessServiceById(PIChildBF.ChildBSID);
                    }
                    if (!addedParentBf.Contains(PIChildBF.ChildBFID.ToString()))
                    {
                        addedParentBf.Add(PIChildBF.ChildBFID.ToString());
                        dtCost.Rows.Add(
                       PIChildBF.Id,
                       PIChildBF.ChildBSID,
                       PIChildBF.ChildBSImpactID,
                       PIChildBF.ChildBFID,
                       PIChildBF.ChildBFImpactID,
                       ChildBfdetailsCHPI != null ? ChildBfdetailsCHPI.Name : string.Empty,
                       ChildBsdetailsCHPI != null ? ChildBsdetailsCHPI.Name : string.Empty,
                       PIChildBF.ChildBFCost,
                       0
                       );
                    }
                }
                #endregion Partially impacted
            }

            dtBSCost = new DataTable();

            dtBSCost.Columns.Add("ID", typeof(Int32));
            dtBSCost.Columns.Add("BSID", typeof(Int32));
            dtBSCost.Columns.Add("BSName");
            dtBSCost.Columns.Add("Cost", typeof(Double));
            dtBSCost.Columns.Add("BSImpactID", typeof(Int32));
            List<string> BSList = new List<string>();
            int BSImpactID = 0;
            for (int j = 0; j < dtCost.Rows.Count; j++)
            {
                int BSID = 0;
                Double totalBSCost = 0;
                BSID = Convert.ToInt32(dtCost.Rows[j]["BSID"].ToString());

                if (!BSList.Contains(BSID.ToString()))
                {
                    BSList.Add(BSID.ToString());
                    for (int i = 0; i < dtCost.Rows.Count; i++)
                    {
                        if (BSID == Convert.ToInt32(dtCost.Rows[i]["BSID"].ToString()))
                        {
                            totalBSCost += Convert.ToDouble(dtCost.Rows[i]["BFCost"].ToString());
                        }
                    }


                    var TIBSParent = from TIBSldParent in inciMgtSumDetailsById
                                     where TIBSldParent.ParentBSImpactID == TotallyImpactId  // Convert.ToInt32(BIImpactType.Totally)
                                                         && TIBSldParent.ParentBSID == BSID
                                     select TIBSldParent;
                    if (TIBSParent != null && TIBSParent.Count() > 0)
                    {
                        var BSIDFirst = TIBSParent.First();
                        BSImpactID = BSIDFirst.ParentBSImpactID;

                    }
                    else
                    {
                        var TIBSChild = from TIBSldChild in inciMgtSumDetailsById
                                        where TIBSldChild.ChildBSImpactID == TotallyImpactId  // Convert.ToInt32(BIImpactType.Totally)
                                                            && TIBSldChild.ChildBSID == BSID
                                        select TIBSldChild;
                        if (TIBSChild != null && TIBSChild.Count() > 0)
                        {
                            var BSIDFirst = TIBSChild.First();
                            BSImpactID = BSIDFirst.ChildBSImpactID;

                        }
                        else
                        {

                            var MIBSParent = from MIBSldParent in inciMgtSumDetailsById
                                             where MIBSldParent.ParentBSImpactID == MajorlyImpactId  // Convert.ToInt32(BIImpactType.Majorly)
                                                                 && MIBSldParent.ParentBSID == BSID
                                             select MIBSldParent;

                            if (MIBSParent != null && MIBSParent.Count() > 0)
                            {
                                var BSIDFirst = MIBSParent.First();
                                BSImpactID = BSIDFirst.ParentBSImpactID;

                            }
                            else
                            {
                                var MIBSChild = from MIBSldChild in inciMgtSumDetailsById
                                                where MIBSldChild.ChildBSImpactID == MajorlyImpactId  // Convert.ToInt32(BIImpactType.Majorly)
                                                                    && MIBSldChild.ChildBSID == BSID
                                                select MIBSldChild;
                                if (MIBSChild != null && MIBSChild.Count() > 0)
                                {
                                    var BSIDFirst = MIBSChild.First();
                                    BSImpactID = BSIDFirst.ChildBSImpactID;

                                }
                                else
                                {
                                    var PIBSParent = from PIBSldParent in inciMgtSumDetailsById
                                                     where PIBSldParent.ParentBSImpactID == PartiallyImpactId // Convert.ToInt32(BIImpactType.Partially)
                                                                         && PIBSldParent.ParentBSID == BSID
                                                     select PIBSldParent;

                                    if (PIBSParent != null && PIBSParent.Count() > 0)
                                    {
                                        var BSIDFirst = PIBSParent.First();
                                        BSImpactID = BSIDFirst.ParentBSImpactID;

                                    }
                                    else
                                    {
                                        var PIBSChild = from PIBSldChild in inciMgtSumDetailsById
                                                        where PIBSldChild.ChildBSImpactID == PartiallyImpactId // Convert.ToInt32(BIImpactType.Partially)
                                                                            && PIBSldChild.ChildBSID == BSID
                                                        select PIBSldChild;

                                        if (PIBSChild != null && PIBSChild.Count() > 0)
                                        {
                                            var BSIDFirst = PIBSChild.First();
                                            BSImpactID = BSIDFirst.ChildBSImpactID;

                                        }
                                    }
                                }
                            }


                        }
                    }
                    dtBSCost.Rows.Add(dtBSCost.Rows.Count + 1, Convert.ToInt32(dtCost.Rows[j]["BSID"].ToString()), dtCost.Rows[j]["BSName"].ToString(), totalBSCost, BSImpactID);
                }
            }

        }


        private void GetBIACost(string strParentApplicationID, string strImpactedType, ref int Upto2Hours, ref int Upto4Hours, ref int Upto8Hours, ref int Upto12Hours, ref int Upto24Hours, ref int Upto48Hours, ref int Upto72Hours, ref int Upto1Week, ref int Upto2Weeks, ref int Upto1Month)
        {
            //int TotalCost = 0;

            if (Convert.ToInt32(strImpactedType) > 0)
            {
                string BIAID = strParentApplicationID + strImpactedType;

                BusinessFunctionBIA BusinessFunctionBIA = Facade.GetBusinessFunctionBIACostByBIAID(Convert.ToInt32(BIAID));

                Upto2Hours = string.IsNullOrEmpty(BusinessFunctionBIA.Upto2HoursCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto2HoursCost.ToString());
                Upto4Hours = string.IsNullOrEmpty(BusinessFunctionBIA.Upto4HoursCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto4HoursCost.ToString());
                Upto8Hours = string.IsNullOrEmpty(BusinessFunctionBIA.Upto8HoursCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto8HoursCost.ToString());
                Upto12Hours = string.IsNullOrEmpty(BusinessFunctionBIA.Upto12HoursCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto12HoursCost.ToString());
                Upto24Hours = string.IsNullOrEmpty(BusinessFunctionBIA.Upto24HoursCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto24HoursCost.ToString());
                Upto48Hours = string.IsNullOrEmpty(BusinessFunctionBIA.Upto48HoursCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto48HoursCost.ToString());
                Upto72Hours = string.IsNullOrEmpty(BusinessFunctionBIA.Upto72HoursCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto72HoursCost.ToString());
                Upto1Week = string.IsNullOrEmpty(BusinessFunctionBIA.Upto1WeekCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto1WeekCost.ToString());
                Upto2Weeks = string.IsNullOrEmpty(BusinessFunctionBIA.Upto2WeeksCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto2WeeksCost.ToString());
                Upto1Month = string.IsNullOrEmpty(BusinessFunctionBIA.Upto1MonthCost.ToString()) ? 0 : Convert.ToInt32(BusinessFunctionBIA.Upto1MonthCost.ToString());


            }


        }

        private string CreateDiagramString(int IncidentId, string InfraComponenetName, string IncidentName, string JOBNAME)
        {
            StringBuilder sb = new StringBuilder();

            if (JOBNAME.ToLower() == "monitorapplicationprocess")
            {
                sb.Append(IncidentName + ":" + InfraComponenetName + "/Hide/logo/redInfraProcess");
                sb.Append(";");
                sb.Append(InfraComponenetName + "/Hide/logo/redInfraProcess:");
            }
            else
            {
                sb.Append(IncidentName + ":" + InfraComponenetName + "/Hide/logo/redInfracompo");
                sb.Append(";");
                sb.Append(InfraComponenetName + "/Hide/logo/redInfracompo:");
            }
            var allIncident = facade.GetAllIncidentManagementSummary();
            var IncSummMgt = from inc in allIncident where inc.IncidentID == IncidentId select inc;
            foreach (var incSum in IncSummMgt)
            {
                string BusinessFunctionName = "";
                if (incSum.ParentBFID > 0)
                {
                    var BFdetails = facade.GetBusinessFunctionById(incSum.ParentBFID);
                    if (BFdetails != null)
                    {
                        BusinessFunctionName = BFdetails.Name;

                        string BusinessImpact = GetImpactTpye(incSum.ParentBFImpactID.ToString()) + "BF";
                        sb.Append(BusinessFunctionName + "/" + BusinessImpact + ",");
                    }
                }
            }
            sb.Remove(sb.Length - 1, 1);
            sb.Append(";");

            foreach (var incSum in IncSummMgt)
            {
                /*INC-2015-10:172.16.128.221(Server)/red;
                172.16.128.221(Server)/red:BF1/PI,BF1/MI,BF_test/TI;
                BF1/PI:CRM/MI,BF3/MI;
                BF3/MI:CRM/MI;
                BF1/MI:CRM/TI,BF2/TI;
                BF2/TI:GLOBAL SALES/TI;
                BF_test/TI:TDS/TI,BF9/PI;
                BF9/PI:LOGISTIC/MI*/

                string BusinessFunctionName = "";
                string BusinessFunctionImpact = "";
                string BusinessServiceName = "";

                if (incSum.ParentBFID > 0)
                {
                    var BFDetails = facade.GetBusinessFunctionById(incSum.ParentBFID);
                    if (BFDetails != null)
                    {

                        BusinessFunctionName = BFDetails.Name;
                        BusinessFunctionImpact = GetImpactTpye(incSum.ParentBFImpactID.ToString()) + "BF";
                        if (incSum.ParentBSID > 0)
                        {
                            var bsdetails = facade.GetBusinessServiceById(incSum.ParentBSID);
                            if (bsdetails != null)
                            {
                                BusinessServiceName = bsdetails.Name;
                                string BusinessServiceImpact = GetImpactTpye(incSum.ParentBSImpactID.ToString()) + "BS";

                                sb.Append(BusinessFunctionName + "/" + BusinessFunctionImpact + ":" + BusinessServiceName + "/" + BusinessServiceImpact);
                            }
                        }
                    }
                    var childBF = from ch in IncSummMgt where ch.ParentBFID == incSum.ParentBFID && ch.ParentBFImpactID == incSum.ParentBFImpactID select ch;
                    foreach (var child in childBF)
                    {

                        string ChildBusinessFunctionName = "";
                        string ChildBusinessFunctionImpact = "";
                        if (child.ChildBFID > 0)
                        {
                            var ChildBFDetails = facade.GetBusinessFunctionById(child.ChildBFID);
                            if (ChildBFDetails != null)
                            {
                                sb.Append(",");
                                ChildBusinessFunctionName = ChildBFDetails.Name;
                                ChildBusinessFunctionImpact = GetImpactTpye(child.ChildBFImpactID.ToString()) + "BF";
                                sb.Append(ChildBusinessFunctionName + "/" + ChildBusinessFunctionImpact);

                            }
                        }
                    }
                    sb.Append(";");

                    foreach (var childbfStart in childBF)
                    {
                        string ChildStartBusinessFunctionName = "";
                        string ChildStartBusinessServiceName = "";
                        string ChildStartBusinessFunctionImpact = "";
                        string ChildStartBusinessServiceImpact = "";
                        if (childbfStart.ChildBFID > 0)
                        {
                            var childBFDetails = facade.GetBusinessFunctionById(childbfStart.ChildBFID);
                            if (childBFDetails != null)
                            {
                                ChildStartBusinessFunctionName = childBFDetails.Name;
                                ChildStartBusinessFunctionImpact = GetImpactTpye(childbfStart.ChildBFImpactID.ToString()) + "BF";
                                if (childbfStart.ChildBSID > 0)
                                {
                                    var ChildBSdetails1 = facade.GetBusinessServiceById(childbfStart.ChildBSID);
                                    if (ChildBSdetails1 != null)
                                    {
                                        ChildStartBusinessServiceName = ChildBSdetails1.Name;
                                        ChildStartBusinessServiceImpact = GetImpactTpye(childbfStart.ChildBSImpactID.ToString()) + "BS";
                                        sb.Append(ChildStartBusinessFunctionName + "/" + ChildStartBusinessFunctionImpact + ":" + ChildStartBusinessServiceName + "/" + ChildStartBusinessServiceImpact);
                                    }
                                }

                                var childBFStarted = from ch in IncSummMgt where ch.ParentBFID == childbfStart.ChildBFID && ch.ParentBFImpactID == childbfStart.ChildBFImpactID select ch;
                                foreach (var child in childBFStarted)
                                {

                                    string ChildBusinessFunctionName = "";
                                    if (child.ChildBFID > 0)
                                    {
                                        var ChildBFDetails1 = facade.GetBusinessFunctionById(child.ChildBFID);
                                        if (ChildBFDetails1 != null)
                                        {
                                            sb.Append(",");
                                            ChildBusinessFunctionName = ChildBFDetails1.Name;
                                            string ChildBusinessFunctionImpact = GetImpactTpye(child.ChildBFImpactID.ToString()) + "BF";
                                            sb.Append(ChildBusinessFunctionName + "/" + ChildBusinessFunctionImpact);

                                        }
                                    }
                                }

                                sb.Append(";");
                            }
                        }
                    }
                }
            }

            string str = sb.ToString().Remove(sb.ToString().Length - 1, 1);
            return str;

        }


        private string GetImpactTpye(string iImpact)
        {

            var INfraImpactType = facade.GetAllImpactRelType();
            var RelImpactType = from a in INfraImpactType where a.Id == Convert.ToInt32(iImpact) select a;
            string ReturnString = string.Empty;

            if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "T")
            {
                ReturnString = "TI";
            }
            else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "M")
            {
                ReturnString = "MI";
            }
            else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "P")
            {
                ReturnString = "PI";
            }

            //string ReturnString = string.Empty;
            //switch (iImpact)
            //{
            //    case "1":
            //        {
            //            ReturnString = "PI";
            //            break;
            //        }
            //    case "2":
            //        {
            //            ReturnString = "MI";
            //            break;
            //        }
            //    case "3":
            //        {
            //            ReturnString = "TI";
            //            break;
            //        }
            //}

            return ReturnString;

        }

        private void setImpactType()
        {
            var INfraImpactType = facade.GetAllImpactRelType();
            if (INfraImpactType != null && INfraImpactType.Count() > 0)
            {
                var RelImpactTypeT = from a in INfraImpactType where a.RelTypeValue.Trim().ToUpper() == "T" select a;
                TotallyImpactId = RelImpactTypeT.FirstOrDefault().Id;

                var RelImpactTypeM = from a in INfraImpactType where a.RelTypeValue.Trim().ToUpper() == "M" select a;
                MajorlyImpactId = RelImpactTypeM.FirstOrDefault().Id;

                var RelImpactTypeP = from a in INfraImpactType where a.RelTypeValue.Trim().ToUpper() == "P" select a;
                PartiallyImpactId = RelImpactTypeP.FirstOrDefault().Id;
            }

        }

        #region Methods

        #region Private

        /// <summary>
        /// Draw the Diagram For First Incident from Treeview
        /// </summary>
        /// <param name="tv"></param>
        /// <param name="updatePanelView"></param>
        /// <author>suryaji shinde Date : 08-Mar-2016</author>
        private void LoadFirstInc(String IncidentRecodr)
        {
            IEnumerable<IncidentManagementNew> incidentMgtDetails = null;
            IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>();
            RadTab rootTab = RadTabStrip1.Tabs[1];

            IEnumerable<IncidentManagementNew> inciMgtDetails = null;
               incidentMgtDetails = facade.GetAllIncidentManagementNew();
           /// var details = facade.GetAllIncidentManagementNew();
               if (!IsSuperAdmin && incidentMgtDetails != null)
            {
                IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                if (infralst != null)
                {
                    foreach (var infra in infralst)
                    {

                        var getdetails = from a in incidentMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                        foreach (var data in getdetails)
                        {
                            incidentMgtDetailsUserBased.Add(data);

                        }
                    }
                }
                incidentMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
            }
            
            _IncidentRecord = IncidentRecodr;
            _IncidentRecordId = Id;
            if (incidentMgtDetails == null) return;

            if (IncidentRecodr == "7Days")
                inciMgtDetails = from a in incidentMgtDetails where a.CreateDate >= DateTime.Now.AddDays(-7) select a;
            else if (IncidentRecodr == "Close")
                inciMgtDetails = from a in incidentMgtDetails where a.CreateDate >= DateTime.Now.AddDays(-30) && a.Status == 0 select a;
            else if (IncidentRecodr == "Open")
                inciMgtDetails = from a in incidentMgtDetails where a.Status == 1 select a;

            else if (IncidentRecodr == "ById" && Id > 0)
                inciMgtDetails = from a in incidentMgtDetails where a.Id == Id select a;
            else
                inciMgtDetails = incidentMgtDetails;

            int incidentId = 0;






            if (inciMgtDetails != null && inciMgtDetails.Count() > 0)
            {
                lblIncId.Text = inciMgtDetails.First().incidentCode;
                lblIncId.ToolTip = inciMgtDetails.First().incidentCode;
                incidentId = Convert.ToInt32(inciMgtDetails.First().Id);

                var incidentById = from a in inciMgtDetails where a.Id == incidentId select a;
                if (incidentById != null)
                {

                    lblIncdowntime.Text = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? (Convert.ToDateTime(incidentById.First().IncidentRecoveryTime) - Convert.ToDateTime(incidentById.First().IncidentTime)).ToString()
                         : ((DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Days) + " Days " + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Hours + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Minutes + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Seconds.ToString();

                    lblIncdowntime2.Text = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? (Convert.ToDateTime(incidentById.First().IncidentRecoveryTime) - Convert.ToDateTime(incidentById.First().IncidentTime)).ToString()
                         : ((DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Days) + " Days " + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Hours + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Minutes + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Seconds.ToString();

                    lblIncdowntime.ToolTip = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? (Convert.ToDateTime(incidentById.First().IncidentRecoveryTime) - Convert.ToDateTime(incidentById.First().IncidentTime)).ToString()
                         : ((DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Days) + " Days " + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Hours + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Minutes + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Seconds.ToString();

                    lblResolveTime.Text = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? incidentById.First().IncidentRecoveryTime.ToString() : "NA";
                    lblResolveTime.ToolTip = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? incidentById.First().IncidentRecoveryTime.ToString() : "NA";

                    lblIncidentTime.Text = incidentById.First().IncidentTime.ToString();
                    lblIncidentTime2.Text = incidentById.First().IncidentTime.ToString();
                    lblIncidentTime.ToolTip = incidentById.First().IncidentTime.ToString();

                    lblImpactStatus.Text = incidentById.First().Status == 1 ? "Open" : "Closed";
                    lblImpactStatus.ToolTip = incidentById.First().Status == 1 ? "Open" : "Closed";


                    var infradetails = facade.GetInfraObjectById(incidentById.First().InfraID);
                    if (infradetails != null)
                    {
                        lblInfraID.Text = infradetails.Name;
                        lblInfraID.ToolTip = infradetails.Name;

                        lblDRavailable.Text = infradetails.DRServerId != 0 ? "Yes" : "No";
                        lblDRActivated.Text = infradetails.DROperationStatus == 2 ? "Yes" + "At " : "NA";
                        lblDRavailable.ToolTip = infradetails.DRServerId != 0 ? "Yes" : "No";
                        lblDRActivated.ToolTip = infradetails.DROperationStatus == 2 ? "Yes" + "At " : "NA";

                    }
                    else
                    {
                        lblDRavailable.Text = "No";
                        lblDRActivated.Text = "NA";

                    }


                }
            }

            if (inciMgtDetails != null && inciMgtDetails.Count() > 0)
            {
                String incidentName = "";
                string JOBNAME = string.Empty;
                if (inciMgtDetails.Count() > 0)
                    incidentName = inciMgtDetails.First().incidentCode;

                JOBNAME = inciMgtDetails.First().JOBNAME;

                int InfraComponentID = Convert.ToInt32(inciMgtDetails.First().InfraComponentID);
                if (inciMgtDetails.First().InfraComponentType.Contains("Server"))
                    GetImpactedNodes(InfraComponentID, "Server", 0, incidentName, incidentId,JOBNAME);
                else
                    GetImpactedNodes(InfraComponentID, "Database", 0, incidentName, incidentId, JOBNAME);
                System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "renderAjaxDataForIncident('" + IncidentString + "');", true);
                // updatePanelView.Update();
                udpgraph.Update();

                table = BuildTableStructure();
                if (IncidentRecodr == "Close")
                {
                    FillDataTable("Close", 0);
                }
                if (IncidentRecodr == "7Days")
                {
                    FillDataTable("7Days", 0);
                }
                if (IncidentRecodr == "Open")
                {
                    FillDataTable("Open", 0);
                }



            }
            RdTreeListIncidentRecord.DataSource = table;

            udpgraph.Update();
            updPnlIncidentList.Update();
            updIcidentDetailsTabButton.Update();
            if (IncidentRecodr == "7Days")
            {
                if (inciMgtDetails != null && inciMgtDetails.Count() > 0)
                {
                    int INcId = Convert.ToInt32(inciMgtDetails.First().Id);

                    var incidentById = from a in inciMgtDetails where a.Id == INcId select a;
                    if (incidentById != null)
                    {
                        rootTab.Visible = incidentById.First().Status.Equals(0) ? false : true;
                        if (incidentById.First().Status.Equals(1))
                        {
                            updRadTab.Update();
                        }

                    }
                }

            }

        }

        /// <summary>
        /// Draw the Diagram on Node click for selected Incident
        /// </summary>
        /// <param name="tv"></param>
        /// <param name="updatePanelView"></param>
        /// <author>Goraknath Khule Date : 11-Mar-2015</author>
        private void DrawDiagram(TreeView tv, UpdatePanel updatePanelView)
        {
            string JOBNAME = string.Empty;
            String[] parentNodeValue;
            int incidentId = 0;
            IEnumerable<IncidentManagementNew> allIncident = null;
            IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>(); 
            if (tv.SelectedNode.Parent == null)
            {
                if (tv.SelectedNode != null)
                {
                    parentNodeValue = (tv.SelectedNode.Value.Split(','));
                    lblIncId.Text = parentNodeValue[1].ToString();
                    lblIncId.ToolTip = parentNodeValue[1].ToString();
                    incidentId = Convert.ToInt32(parentNodeValue[0]);



                  //  var allIncident = facade.GetAllIncidentManagementNew();
                     allIncident = facade.GetAllIncidentManagementNew();
                     if (!IsSuperAdmin && allIncident != null)
                    {
                        IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                        if (infralst != null)
                        {
                            foreach (var infra in infralst)
                            {

                                var getdetails = from a in allIncident where a.InfraID == Convert.ToInt32(infra.Id) select a;
                                foreach (var data in getdetails)
                                {
                                    incidentMgtDetailsUserBased.Add(data);

                                }
                            }
                        }
                        allIncident = incidentMgtDetailsUserBased.AsEnumerable();
                    }
                    if (allIncident != null && allIncident.Count() > 0)
                    {
                        var incidentById = from a in allIncident where a.Id == incidentId select a;
                        if (incidentById != null)
                        {
                            JOBNAME = incidentById.First().JOBNAME;

                            lblIncdowntime.Text = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? (Convert.ToDateTime(incidentById.First().IncidentRecoveryTime) - Convert.ToDateTime(incidentById.First().IncidentTime)).ToString()
                                 : ((DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Days) + " Days " + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Hours + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Minutes + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Seconds.ToString();

                            lblIncdowntime2.Text = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? (Convert.ToDateTime(incidentById.First().IncidentRecoveryTime) - Convert.ToDateTime(incidentById.First().IncidentTime)).ToString()
                                 : ((DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Days) + " Days " + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Hours + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Minutes + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Seconds.ToString();

                            lblIncdowntime.ToolTip = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? (Convert.ToDateTime(incidentById.First().IncidentRecoveryTime) - Convert.ToDateTime(incidentById.First().IncidentTime)).ToString()
                                 : ((DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Days) + " Days " + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Hours + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Minutes + ":" + (DateTime.Now - Convert.ToDateTime(incidentById.First().IncidentTime)).Seconds.ToString();

                            lblResolveTime.Text = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? incidentById.First().IncidentRecoveryTime.ToString() : "NA";
                            lblResolveTime.ToolTip = (incidentById.First().IncidentRecoveryTime > incidentById.First().IncidentTime) ? incidentById.First().IncidentRecoveryTime.ToString() : "NA";

                            lblIncidentTime.Text = incidentById.First().IncidentTime.ToString();
                            lblIncidentTime2.Text = incidentById.First().IncidentTime.ToString();
                            lblIncidentTime.ToolTip = incidentById.First().IncidentTime.ToString();

                            lblImpactStatus.Text = incidentById.First().Status == 1 ? "Open" : "Closed";
                            lblImpactStatus.ToolTip = incidentById.First().Status == 1 ? "Open" : "Closed";


                            var infradetails = facade.GetInfraObjectById(incidentById.First().InfraID);
                            if (infradetails != null)
                            {
                                lblInfraID.Text = infradetails.Name;
                                lblInfraID.ToolTip = infradetails.Name;

                                lblDRavailable.Text = infradetails.DRServerId != 0 ? "Yes" : "No";
                                lblDRActivated.Text = infradetails.DROperationStatus == 2 ? "Yes" + "At " : "NA";
                                lblDRavailable.ToolTip = infradetails.DRServerId != 0 ? "Yes" : "No";
                                lblDRActivated.ToolTip = infradetails.DROperationStatus == 2 ? "Yes" + "At " : "NA";


                            }
                            else
                            {
                                lblDRavailable.Text = "No";
                                lblDRActivated.Text = "NA";

                            }


                        }
                    }



                }

                if (tv.SelectedNode.ChildNodes.Count > 0)
                {
                    String incidentName = "";
                    if (tv.Nodes != null && tv.Nodes.Count > 0)
                        incidentName = tv.SelectedNode.Value.Split(',')[1];

                    int InfraComponentID = Convert.ToInt32(tv.SelectedNode.ChildNodes[0].Value);
                    if (tv.SelectedNode.ChildNodes[0].Text.Contains("Server"))
                        GetImpactedNodes(InfraComponentID, "Server", 0, incidentName, incidentId,JOBNAME);
                    else
                        GetImpactedNodes(InfraComponentID, "Database", 0, incidentName, incidentId,JOBNAME);
                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "renderAjaxDataForIncident('" + IncidentString + "');", true);
                    // updatePanelView.Update();
                    udpgraph.Update();

                    table = BuildTableStructure();
                    FillDataTable("ById", incidentId);
                    RdTreeListIncidentRecord.DataSource = table;
                    updPnlIncidentList.Update();

                }
            }
        }

        /// <summary>
        /// Load business service hierarchy in asp.net TreeView
        /// </summary>
        /// <param name="businessServiceId">businessServiceId</param>
        /// <author>Ram Mahajan-24/02/2015</author>
        private void LoadBusinessServiceHierarchy(int businessServiceId)
        {
            try
            {
                TreeNode ParentreeNode = null;
              //  BIImpactType ImpactType = BIImpactType.Undefined;
                var bsLogo = "<span class='green-dot'></span> ";
                IList<IncidentManagementSummary> IncSummary = facade.GetAllIncidentManagementSummary();
                if (businessServiceId > 0)
                {
                    var bsObject = facade.GetBusinessServiceById(businessServiceId);
                    string bsBreadCrumStr = bsObject.Name;
                    IEnumerable<IncidentManagementSummary> IncSummaryByBsId = null;
                    if (IncSummary != null && IncSummary.Count > 0)
                    {
                        IncSummaryByBsId = from i in IncSummary where i.ParentBSID == businessServiceId select i;
                        if (IncSummaryByBsId != null && IncSummaryByBsId.Count() > 0)
                        {
                            foreach (var bs in IncSummaryByBsId)
                            {
                                bs.ParentBFCost = bs.ParentBFCost == null ? 0 : bs.ParentBFCost;
                                if (bs != null && bs.ParentBFCost != null && bs.ParentBFCost == 0)
                                {
                                    IEnumerable<IncidentManagementNew> incidentMgtDetails = null;
                                    IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>(); 
                                     incidentMgtDetails = facade.GetAllIncidentManagementNew();

                                  //  var details = facade.GetAllIncidentManagementNew();
                                    if (!IsSuperAdmin && incidentMgtDetails != null)
                                    {
                                        IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                                        if (infralst != null)
                                        {
                                            foreach (var infra in infralst)
                                            {

                                                var getdetails = from a in incidentMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                                                foreach (var data in getdetails)
                                                {
                                                    incidentMgtDetailsUserBased.Add(data);

                                                }
                                            }
                                        }
                                        incidentMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
                                    }
                                    if (incidentMgtDetails != null && incidentMgtDetails.Count() > 0)
                                    {
                                       var TreeinciMgtDetails = from a in incidentMgtDetails where a.Id==bs.IncidentID && a.Status == 1 select a;
                                       if (TreeinciMgtDetails != null && TreeinciMgtDetails.Count() > 0)
                                       {
                                           bsLogo = GetLogo(bs.ParentBSImpactID);
                                           break;
                                       }
                                       else
                                       {
                                           bsLogo = " <span class='green-dot'></span> ";
                                         
                                       }
                                    
                                    }
                                   
                                }

                            }
                        }
                    }



                    ParentreeNode = new TreeNode(bsLogo + " <span style= font-weight:bold;>" + bsObject.Name + "</span>", string.Concat(businessServiceId.ToString() + "/", bsObject.Name + "/" + "BS" + "/" + bsBreadCrumStr + "/" + businessServiceId));

                    tvBSHierarchy.Nodes.Add(ParentreeNode);


                    var bfList = facade.GetBusinessFunctionsByBusinessServiceId(businessServiceId);
                    bsLogo = " <span class='green-dot'></span> ";
                    if (bfList != null && bfList.Count > 0)
                    {
                        foreach (var objBF in bfList)
                        {
                            string bfBreadCrumStr = bsObject.Name + "->" + objBF.Name;
                            if (IncSummary != null && IncSummary.Count > 0)
                            {
                                var IncSummaryByBFID = from i in IncSummary where i.ParentBFID == objBF.Id select i;

                                if (IncSummaryByBFID != null && IncSummaryByBFID.Count() > 0)
                                {
                                    foreach (var bf in IncSummaryByBFID)
                                    {
                                        bf.ParentBFCost = bf.ParentBFCost == null ? 0 : bf.ParentBFCost;
                                        if (bf != null && bf.ParentBFCost != null && bf.ParentBFCost == 0)
                                        {
                                            IEnumerable<IncidentManagementNew> incidentMgtDetails = null;
                                            IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>(); 

                                             incidentMgtDetails = facade.GetAllIncidentManagementNew();
                                             if (!IsSuperAdmin && incidentMgtDetails != null)
                                            {
                                                IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                                                if (infralst != null)
                                                {
                                                    foreach (var infra in infralst)
                                                    {

                                                        var getdetails = from a in incidentMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                                                        foreach (var data in getdetails)
                                                        {
                                                            incidentMgtDetailsUserBased.Add(data);

                                                        }
                                                    }
                                                }
                                                incidentMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
                                            }

                                            if (incidentMgtDetails != null && incidentMgtDetails.Count() > 0)
                                            {
                                                var TreeinciMgtDetails = from a in incidentMgtDetails where a.Id == bf.IncidentID && a.Status == 1 select a;
                                                if (TreeinciMgtDetails != null && TreeinciMgtDetails.Count() > 0)
                                                {
                                                    bsLogo = GetLogo(bf.ParentBFImpactID);
                                                    break;
                                                }
                                                else
                                                {
                                                    bsLogo = " <span class='green-dot'></span> ";
                                                    
                                                }

                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    var IncSummaryByChildBFID = from i in IncSummary where i.ChildBFID == objBF.Id select i;

                                    if (IncSummaryByChildBFID != null && IncSummaryByChildBFID.Count() > 0)
                                    {
                                        foreach (var bf in IncSummaryByChildBFID)
                                        {
                                            IEnumerable<IncidentManagementNew> incidentMgtDetails = null;
                                            IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>();
                                             incidentMgtDetails = facade.GetAllIncidentManagementNew();

                                             if (!IsSuperAdmin && incidentMgtDetails != null)
                                            {
                                                IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                                                if (infralst != null)
                                                {
                                                    foreach (var infra in infralst)
                                                    {

                                                        var getdetails = from a in incidentMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                                                        foreach (var data in getdetails)
                                                        {
                                                            incidentMgtDetailsUserBased.Add(data);

                                                        }
                                                    }
                                                }
                                                incidentMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
                                            }
                                            if (incidentMgtDetails != null && incidentMgtDetails.Count() > 0)
                                            {
                                                var TreeinciMgtDetails = from a in incidentMgtDetails where a.Id == bf.IncidentID && a.Status == 1 select a;
                                                if (TreeinciMgtDetails != null && TreeinciMgtDetails.Count() > 0)
                                                {
                                                    bsLogo = GetLogo(bf.ChildBFImpactID);
                                                    break;
                                                }
                                                else
                                                {
                                                    bsLogo = " <span class='green-dot'></span> ";
                                                    
                                                }

                                            }
                                        }
                                    }
                                }
                            }
                            TreeNode child1 = new TreeNode(bsLogo + " " + objBF.Name, string.Concat(businessServiceId.ToString() + "/", objBF.Name + "/" + "BF" + "/" + bfBreadCrumStr + "/" + objBF.Id));
                            //child1.SelectAction = TreeNodeSelectAction.None;
                            ParentreeNode.ChildNodes.Add(child1);

                            var ioList = facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(objBF.BusinessServiceId, objBF.Id);

                            if (ioList != null && ioList.Count > 0)
                            {
                                var filterIoList = GetInfraObjectByUserId(_currentLoginUserId, ioList);

                                if (filterIoList != null && filterIoList.Count > 0)
                                {
                                    foreach (var objInfra in filterIoList)
                                    {
                                        string ioBreadCrumStr = bsObject.Name + "->" + objBF.Name + "->" + objInfra.Name;
                                        IEnumerable<IncidentManagementNew> InciMgtDetails = null;
                                        IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>();
                                        TreeNode child2 = new TreeNode(objInfra.Name, string.Concat(businessServiceId.ToString() + "/", objInfra.Name + "/" + "IO" + "/" + ioBreadCrumStr + "/" + objInfra.Id));
                                        //child2.SelectAction = TreeNodeSelectAction.None;
                                        child1.ChildNodes.Add(child2);

                                        //Get server component details
                                        var infraObjectPRDRServerDetailsList_AllInfra = facade.GetServersByInfraObjectId(objInfra.Id);

                                         InciMgtDetails = facade.GetAllIncidentManagementNew();
                                        //var details = facade.GetAllIncidentManagementNew();
                                        if (!IsSuperAdmin && InciMgtDetails != null)
                                        {
                                            IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                                            if (infralst != null)
                                            {
                                                foreach (var infra in infralst)
                                                {

                                                    var getdetails = from a in InciMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                                                    foreach (var data in getdetails)
                                                    {
                                                        incidentMgtDetailsUserBased.Add(data);

                                                    }
                                                }
                                            }
                                            InciMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
                                        }

                                        if (infraObjectPRDRServerDetailsList_AllInfra != null && infraObjectPRDRServerDetailsList_AllInfra.Count > 0)
                                        {
                                            foreach (var obj1 in infraObjectPRDRServerDetailsList_AllInfra)
                                            {
                                                bsLogo = " <span class='green-dot'></span> ";
                                                if (InciMgtDetails != null && InciMgtDetails.Count() > 0)
                                                {
                                                    var IncMgtDetailsByComponentTypeAndId = from i in InciMgtDetails
                                                                                            where (i.InfraComponentID == obj1.Id && i.InfraComponentType == InfraobjectComponentType.Server.ToDescription() && i.Status == 1)
                                                                                            select i;

                                                    if (IncMgtDetailsByComponentTypeAndId != null && IncMgtDetailsByComponentTypeAndId.Count() > 0)
                                                    {
                                                        foreach (var bf in IncMgtDetailsByComponentTypeAndId)
                                                        {
                                                            // ImpactType = BIImpactType.Totally;
                                                            bsLogo = " <span class='close-dot'></span> ";//GetLogo(ImpactType);
                                                            break;
                                                        }
                                                    }
                                                }

                                                string ioCompBreadCrumStr = bsObject.Name + "->" + objBF.Name + "->" + objInfra.Name + "->" + obj1.Name;

                                                TreeNode child3 = new TreeNode(bsLogo + "" + obj1.Name, string.Concat(businessServiceId.ToString() + "/", obj1.Name + "/" + "IOC" + "/" + ioCompBreadCrumStr + "/" + obj1.Id));
                                                //child3.SelectAction = TreeNodeSelectAction.None;
                                                child2.ChildNodes.Add(child3);
                                            }
                                        }
                                        //get database component details
                                        var infraObjectPRDRDatabaseDetailsList_AllInfra = facade.GetDatabaseBaseByInfraObjectId(objInfra.Id);

                                        if (infraObjectPRDRDatabaseDetailsList_AllInfra != null && infraObjectPRDRDatabaseDetailsList_AllInfra.Count > 0)
                                        {
                                            foreach (var obj1 in infraObjectPRDRDatabaseDetailsList_AllInfra)
                                            {
                                                bsLogo = " <span class='green-dot'></span> ";
                                                if (InciMgtDetails != null && InciMgtDetails.Count() > 0)
                                                {
                                                    var IncMgtDetailsByComponentTypeAndId = from i in InciMgtDetails
                                                                                            where (i.InfraComponentID == obj1.Id && i.InfraComponentType == InfraobjectComponentType.Database.ToDescription() && i.Status == 1)
                                                                                            select i;

                                                    if (IncMgtDetailsByComponentTypeAndId != null && IncMgtDetailsByComponentTypeAndId.Count() > 0)
                                                    {
                                                        foreach (var bf in IncMgtDetailsByComponentTypeAndId)
                                                        {
                                                            // ImpactType = BIImpactType.Totally;
                                                            bsLogo = " <span class='close-dot'></span> ";
                                                            break;
                                                        }
                                                    }
                                                }
                                                string ioCompBreadCrumStr = bsObject.Name + "->" + objBF.Name + "->" + objInfra.Name + "->" + obj1.Name;

                                                TreeNode child3 = new TreeNode(bsLogo + " " + obj1.Name, string.Concat(businessServiceId.ToString() + "/", obj1.Name + "/" + "IOC" + "/" + ioCompBreadCrumStr + "/" + obj1.Id));
                                                // child3.SelectAction = TreeNodeSelectAction.None;
                                                child2.ChildNodes.Add(child3);
                                            }
                                        }
                                        //Get replication component details
                                        var infraObjectPRDRReplicationDetailsList_AllInfra = facade.GetReplicationBaseByInfraObjectId(objInfra.Id);
                                        bsLogo = " <span class='green-dot'></span> ";
                                        if (infraObjectPRDRReplicationDetailsList_AllInfra != null && infraObjectPRDRReplicationDetailsList_AllInfra.Count > 0)
                                        {
                                            foreach (var obj1 in infraObjectPRDRReplicationDetailsList_AllInfra)
                                            {
                                                string ioCompBreadCrumStr = bsObject.Name + "->" + objBF.Name + "->" + objInfra.Name + "->" + obj1.Name;

                                                TreeNode child3 = new TreeNode(bsLogo + " " + obj1.Name, string.Concat(businessServiceId.ToString() + "/", obj1.Name + "/" + "IOC" + "/" + ioCompBreadCrumStr + "/" + obj1.Id));
                                                //child3.SelectAction = TreeNodeSelectAction.None;
                                                child2.ChildNodes.Add(child3);
                                            }
                                        }
                                    }//end of infraobject loop
                                }
                            }
                        }//end of business function loop
                    }
                }//end of business service
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while loading business service hierarchy", ex);
                ExceptionManager.Manage(cpException);
            }
        }

        /// <summary>
        /// Return image by impact type
        /// </summary>
        /// <param name="ImpactType"></param>
        /// <returns></returns>
        private string GetLogo(int ImpactType)
        {
            //if (ImpactType == BIImpactType.Partially)
            //{
            //    return " <span class='yellow-dot'></span> ";
            //}
            //else if (ImpactType == BIImpactType.Majorly)
            //{
            //    return " <span class='orange-dot'></span> ";
            //}
            //else if (ImpactType == BIImpactType.Totally)
            //{
            //    return " <span class='red-dot'></span> ";
            //}
            //else
            //{
            //    return " <span class='green-dot'></span> ";
            //}

            var INfraImpactType = facade.GetAllImpactRelType();
            var RelImpactType = from a in INfraImpactType where a.Id == ImpactType select a;
            if (RelImpactType != null && RelImpactType.Count() > 0)
            {

                string IMType = string.Empty;
                if (RelImpactType.FirstOrDefault().RelTypeValue.Trim().ToUpper() == "T")
                {
                    return " <span class='red-dot'></span> ";
                }
                else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim().ToUpper() == "M")
                {
                    return " <span class='orange-dot'></span> ";
                }
                else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim().ToUpper() == "P")
                {
                    return " <span class='yellow-dot'></span> ";
                }
                else
                {
                    return " <span class='green-dot'></span> ";
                }
            }
            else
            {
                return " <span class='green-dot'></span> ";
            }
        }

        /// <summary>
        /// Get business services as per loggedin user role,companyid,userid etc.
        /// </summary>
        /// <author>Ram Mahajan-24/02/2015</author>
        private void GetAllBusinessServices()
        {
            try
            {
                int level = 0;
                var businessServiceList = Facade.GetBusinessServiceByCompanyIdAndRole(_currentLoginUserId, _companyId, LoggedInUserRole, _isParent, LoggedInUser.InfraObjectAllFlag);

                if (businessServiceList != null && businessServiceList.Count > 0)
                {
                    foreach (var bs in businessServiceList)
                    {
                        LoadBusinessServiceHierarchy(bs.Id);
                        //if (businessServiceList.First() == bs)
                        //{
                        //    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "renderAjaxData('" + businessServiceList[0].Id + "','" + level + "','" + bs.Name + "');", true);
                        //    ShowBSDetails(bs.Id);
                        //}
                    }

                }
                else
                {
                    //TO DO- no business service to load in treeview.
                }

            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting GetAllBusinessServices", ex);
                ExceptionManager.Manage(cpException);
            }
        }

        /// <summary>
        /// Get infraobject as per userid and Flag "InfraObjectAllFlag = True/false".
        /// </summary>
        /// <param name="userId">userId</param>
        /// <param name="allInfraObjectList">allInfraObjectList</param>
        /// <returns>List of InfraObject-24/02/2015</returns>
        private IList<InfraObject> GetInfraObjectByUserId(int userId, IList<InfraObject> allInfraObjectList)
        {
            IList<InfraObject> ioList = null;

            //Get infra Object list as per Flag "InfraObjectAllFlag"

            IList<UserInfraObject> userInfraDetail = new List<UserInfraObject>();

            var userDetail = facade.GetUserById(_currentLoginUserId);

            if (userDetail != null)
            {
                if (!userDetail.InfraObjectAllFlag)
                {
                    userInfraDetail = facade.GetUserInfraObjectByUserId(userDetail.Id);

                    if (userInfraDetail != null)
                    {
                        ioList = new List<InfraObject>();

                        foreach (var infraObject in allInfraObjectList)
                        {
                            foreach (var userinfra in userInfraDetail)
                            {
                                if (infraObject.Id == userinfra.InfraObjectId)
                                {
                                    ioList.Add(infraObject);
                                }
                            }
                        }
                    }
                    else
                    {
                        ioList = new List<InfraObject>();

                    }
                }
                else
                {
                    ioList = new List<InfraObject>();
                    ioList = allInfraObjectList;
                }
            }
            else
            {
                ioList = new List<InfraObject>();
            }
            return ioList;
        }

        /// <summary>
        /// Extract required relation from given node relation and index
        /// </summary>
        /// <param name="index">index</param>
        /// <param name="nodeRelation">nodeRelation</param>
        /// <returns>string</returns>
        /// <author>Ram Mahajan-25/02/2025</author>
        private string GetNodeRelationFromExistingRelation(int index, string nodeRelation)
        {
            string filterRelation = string.Empty;

            filterRelation = nodeRelation.Substring(index, nodeRelation.Length - index);

            return filterRelation;
        }

        /// <summary>
        /// Construct Json by creating nodeRelation using business serviceId
        /// </summary>
        /// <param name="businessServiceId">businessServiceId</param>
        /// <returns>Json as string</returns>
        /// <author>Ram Mahajan-26/02/2015</author>
        private string GetJsonData(int businessServiceId)
        {
            ClearAll();
            inputString = GetNodeValue(Convert.ToInt32(businessServiceId));

            if (!string.IsNullOrEmpty(inputString))
                return inputString;
            else
                return string.Empty;
        }

        /// <summary>
        /// Make ajax call and render graph
        /// </summary>
        /// <param name="index">index</param>
        /// <param name="nodeRelation">nodeRelation</param>
        /// <param name="type">type</param>
        /// <param name="level1">level1</param>
        /// <param name="level2">level2</param>
        /// <author>Ram Mahajan-26/02/2015</author>
        private void AjaxCallAndRenderGraph(int index, string nodeRelation, string type, int level1, int level2, string breadCrum, int id)
        {
            string finalNodeRelation = string.Empty;

            finalNodeRelation = GetNodeRelationFromExistingRelation(index, nodeRelation);

            if (!string.IsNullOrEmpty(finalNodeRelation))
            {
                string finalNodeRelationJson = string.Empty;

                if (type.Equals("BF", StringComparison.OrdinalIgnoreCase))
                {
                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "renderAjaxDataForInfraObjects('" + finalNodeRelation + "','" + level1 + "','" + type + "','" + breadCrum + "');", true);
                    udpgraph.Update();
                    ShowBFDetails(id);
                }
                else if (type.Equals("IO", StringComparison.OrdinalIgnoreCase))
                {
                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "renderAjaxDataForInfraObjects('" + finalNodeRelation + "','" + level2 + "','" + type + "','" + breadCrum + "');", true);
                    udpgraph.Update();
                    ShowIODetails(id);
                }
            }
        }

        /// <summary>
        /// Get database name by databaseType and Databasebase id
        /// </summary>
        /// <param name="databaseType">databaseType</param>
        /// <param name="DatabaseId">DatabaseId</param>
        /// <returns>DB name as string</returns>
        /// <author>Gorak Khule -24/04/2015</author>
        private String getDataBaseName(DatabaseType databaseType, int DatabaseId)
        {
            String sidName = "";
            try
            {

                switch (databaseType)
                {

                    case DatabaseType.PostgreSQL:
                        var db = (Facade.GetDatabasePostgreSqlByDatabaseBaseId(DatabaseId));

                        if (db != null)
                            sidName = db.DatabaseName;
                        break;

                    case DatabaseType.Oracle:
                        var db1 = Facade.GetDatabaseOracleByDatabaseBaseId(DatabaseId);
                        if (db1 != null)
                            sidName = db1.OracleSID;
                        break;

                    case DatabaseType.OracleRac:
                        var db2 = (Facade.GetDatabaseOracleRacByDatabaseBaseId(DatabaseId));
                        if (db2 != null)
                            sidName = db2.OracleSID;
                        break;

                    case DatabaseType.Sql:
                        var db3 = (Facade.GetDatabaseSqlByDatabaseBaseId(DatabaseId));
                        if (db3 != null)
                            sidName = db3.DatabaseSID;

                        break;

                    case DatabaseType.Exchange:
                        var db4 = (Facade.GetDatabaseExchangeByDatabaseBaseId(DatabaseId));
                        if (db4 != null)
                            sidName = db4.MailBoxDBName;

                        break;

                    case DatabaseType.DB2:
                        var db5 = (Facade.GetDatabaseDb2ByDatabaseBaseId(DatabaseId));
                        if (db5 != null)
                            sidName = db5.DatabaseSID;

                        break;

                    case DatabaseType.ExchangeDAG:
                        var db6 = (Facade.GetDatabaseExchangeDAGByDatabaseBaseId(DatabaseId));
                        if (db6 != null)
                            sidName = db6.MailBoxDBName;


                        break;

                    case DatabaseType.MySQL:
                        var db7 = (Facade.GetDatabaseMySqlByDatabaseBaseId(DatabaseId));
                        if (db7 != null)
                            sidName = db7.DatabaseID;


                        break;

                    case DatabaseType.Postgres9x:
                        var db8 = (Facade.GetDatabasePostgre9xByDatabaseBaseId(DatabaseId));
                        if (db8 != null)
                            sidName = db8.DatabaseName;


                        break;

                    case DatabaseType.SQLNative2008:
                        var db9 = (Facade.GetDatabaseMSSqlByDatabaseBaseId(DatabaseId));
                        if (db9 != null)
                            sidName = db9.DatabaseName;

                        break;
                }
            }
            catch (Exception)
            {

                sidName = "";
                return sidName;
            }
            return sidName;
        }

        private string getDataBaseNameByType(DatabaseType databaseType, int DatabaseId)
        {
            string sidName = "";
            try
            {

                var db = (Facade.GetDatabaseNameById(databaseType, DatabaseId));

                if (db != null)
                    sidName = db.Name;

            }
            catch (Exception)
            {
                return string.Empty;
            }
            return sidName;
        }
        /// <summary>
        /// Get All InfraObject to Business function incidents
        /// </summary>
        /// <author>Suryaji Shinde-10/03/2016</author>
        private void SetDynamicTabUrlAndDetails(string IncType)
        {
            if (_IncidentType == "Close")
            {
                liOpen.Visible = false;
                liclose.Visible = true;
                li7days.Visible = false;
                RadTabStrip1.Tabs[1].Visible = false;
                RadTabStrip1.Tabs[0].Visible = true;

                FillDataTable("Close", 0);
                tabOpenIncident.CssClass = "tab-pane";
                tab7DaysIncident.CssClass = "tab-pane";
                tabAllCloseIncident.CssClass = "tab-pane active";
                li7days.Attributes.Add("class", "");
                liclose.Attributes.Add("class", "active");
                liOpen.Attributes.Add("class", "");
                LoadFirstInc(_IncidentType);
                bindTreeView(tvAllCloseHierarchy, "Close");

            }
            if (_IncidentType == "7Days")
            {
                liOpen.Visible = false;
                liclose.Visible = false;
                li7days.Visible = true;

                RadTabStrip1.Tabs[0].Visible = true;
                RadTabStrip1.Tabs[1].Visible = false;

                var secureUrl = new SecureUrl(WhatIfTabCurrentUrl);

                secureUrl = UrlHelper.BuildSecureUrl(WhatIfTabCurrentUrl, string.Empty, Constants.UrlConstants.Params.IncidentType, _IncidentType);

                if (secureUrl != null)
                {
                    RadTabStrip1.Tabs[1].NavigateUrl = secureUrl.ToString();

                }

                FillDataTable("7Days", 0);
                tabAllCloseIncident.CssClass = "tab-pane";
                tab7DaysIncident.CssClass = "tab-pane active";
                tabOpenIncident.CssClass = "tab-pane";
                li7days.Attributes.Add("class", "active");
                liclose.Attributes.Add("class", "");
                liOpen.Attributes.Add("class", "");
                LoadFirstInc(_IncidentType);
                bindTreeView(tv7DaysIncident, "7Days");


            }
            if (_IncidentType == "Open")
            {
                liOpen.Visible = true;
                liclose.Visible = false;
                li7days.Visible = false;
                RadTabStrip1.Tabs[1].Visible = true;
                RadTabStrip1.Tabs[0].Visible = true;
                var secureUrl = new SecureUrl(WhatIfTabCurrentUrl);

                secureUrl = UrlHelper.BuildSecureUrl(WhatIfTabCurrentUrl, string.Empty, Constants.UrlConstants.Params.IncidentType,
                                                            _IncidentType);
                if (secureUrl != null)
                {
                    RadTabStrip1.Tabs[1].NavigateUrl = secureUrl.ToString();

                }
                FillDataTable("Open", 0);
                tabOpenIncident.CssClass = "tab-pane active";
                tab7DaysIncident.CssClass = "tab-pane";
                tabAllCloseIncident.CssClass = "tab-pane";
                li7days.Attributes.Add("class", "");
                liclose.Attributes.Add("class", "");
                liOpen.Attributes.Add("class", "active");
                LoadFirstInc(_IncidentType);
                bindTreeView(tvOpenIncident, "Open");
            }

        }

        /// <summary>
        /// Get All InfraObject to Business function incidents
        /// </summary>
        /// <author>Ram mahajan-05/03/2015</author>
        private void GetAllIncidents(string IncType)
        {
            //var bfToBf = facade.GetALLBusinessImpactBFtoBF();
            //var bfToBs = facade.GetALLBusinessImpactBFtoBS();
        }
        /// <summary>
        /// Bind Treeview 
        /// </summary>
        /// <param name="tvIndicentHierarchy"></param>
        private void bindTreeView(TreeView tvIndicentHierarchy, string tvName)
        {
            var bsLogo = "<span class='green-dot'></span> ";
            TreeNode ParentIncidentTreeNode = null;
            IEnumerable<IncidentManagementNew> incidentMgtDetails = null;
            IList<IncidentManagementNew> incidentMgtDetailsUserBased = new List<IncidentManagementNew>(); 
            
            TreeNode childParentBF = null;
            string BSName = "";
            string BFName = "";
            //a.CreateDate <= DateTime.Now &&
             incidentMgtDetails = facade.GetAllIncidentManagementNew();
           // var details = facade.GetAllIncidentManagementNew();
            if (!IsSuperAdmin && incidentMgtDetails != null)
                {
                    IList<InfraObject> infralst = facade.GetInfraObjectByLoggedInUserId(LoggedInUserId);

                    if (infralst != null)
                    {
                        foreach (var infra in infralst)
                        {

                            var getdetails = from a in incidentMgtDetails where a.InfraID == Convert.ToInt32(infra.Id) select a;
                            foreach (var data in getdetails)
                            {
                                incidentMgtDetailsUserBased.Add(data);

                            }
                        }
                    }
                    incidentMgtDetails = incidentMgtDetailsUserBased.AsEnumerable();
                }
            
            
            if (incidentMgtDetails == null) return;
            if (tvName == "7Days")
                //last 7 days closed 
                incidentMgtDetails = (from a in incidentMgtDetails where (a.IncidentRecoveryTime >= DateTime.Now.AddDays(-7) && a.Status == 0) || (a.CreateDate >= DateTime.Now.AddDays(-7) && a.Status == 1) select a);

            else if (tvName == "Close")
                incidentMgtDetails = from a in incidentMgtDetails where a.IncidentRecoveryTime >= DateTime.Now.AddDays(-30) && a.Status == 0 select a;
            else if (tvName == "Open")
                incidentMgtDetails = from a in incidentMgtDetails where a.Status == 1 select a;

            if (incidentMgtDetails != null && incidentMgtDetails.Count() > 0)
            {
                var incidentMgtSummaryDetails = facade.GetAllIncidentManagementSummary();
                foreach (var inc in incidentMgtDetails)
                {
                    List<string> lstParentBFid = new List<string>();
                    ParentIncidentTreeNode = new TreeNode(" <span style= font-weight:bold;>" + inc.incidentCode + "</span> ", string.Format("{0},{1}", inc.Id.ToString(), inc.incidentCode.ToString()), "../Images/icons/incident-icon.png");

                    tvIndicentHierarchy.Nodes.Add(ParentIncidentTreeNode);//Root Incident added
                    //tvIndicentHierarchy.Font.Bold = true;
                    string InfraComponent = string.Empty;
                    if (inc.InfraComponentType.Equals(InfraobjectComponentType.Server.ToDescription()))
                    {
                        var serverDetails = Facade.GetServerById(inc.InfraComponentID);
                        InfraComponent = CryptographyHelper.Md5Decrypt(serverDetails.IPAddress) + "(Server)";
                    }
                    //binding database details as per Infracomponent type
                    else if (inc.InfraComponentType.Equals(InfraobjectComponentType.Database.ToDescription()))
                    {
                        var DBDetails = Facade.GetDatabaseBaseById(inc.InfraComponentID);
                        String DatabaseName = getDataBaseName(DBDetails.DatabaseType, DBDetails.Id);
                        //String DatabaseName = getDataBaseNameByType(DBDetails.DatabaseType, DBDetails.Id);
                        InfraComponent = DatabaseName + "(DB)";
                    }
                    if (!string.IsNullOrEmpty(InfraComponent))
                    {
                        //bsLogo = GetLogo(BIImpactType.Totally);
                        TreeNode childInfraComponent = new TreeNode(" <span class='close-dot'></span> " + InfraComponent, inc.InfraComponentID.ToString());
                        ParentIncidentTreeNode.ChildNodes.Add(childInfraComponent);//Affected InfraComponent Added

                        var incidentMgtSummaryDetailsbyInID = from im in incidentMgtSummaryDetails where im.IncidentID == inc.Id select im;
                        foreach (var incmgtSumByIncID in incidentMgtSummaryDetailsbyInID)
                        {
                            Boolean IsBFpresent = false;

                            IsBFpresent = lstParentBFid.Contains(incmgtSumByIncID.ParentBFID.ToString().ToLower());
                            if (incmgtSumByIncID.ParentBFID > 0)
                            {

                                var bfdetails = facade.GetBusinessFunctionById(incmgtSumByIncID.ParentBFID);
                                if (bfdetails != null)
                                {
                                    BFName = bfdetails.Name;
                                    bsLogo = GetLogo(incmgtSumByIncID.ParentBFImpactID);
                                    childParentBF = new TreeNode(bsLogo + " " + string.Format("{0}", BFName), incmgtSumByIncID.ParentBFID.ToString());
                                    childInfraComponent.ChildNodes.Add(childParentBF);
                                }
                            }

                            //Impacted Business functions (parent) are added
                            lstParentBFid.Add(incmgtSumByIncID.ParentBFID.ToString().ToLower());
                            TreeNode ParentBS = null;
                            TreeNode childBF = null;
                            TreeNode childBS = null;
                            if (incmgtSumByIncID.ParentBSID > 0)
                            {

                                var bsdetails = facade.GetBusinessServiceById(incmgtSumByIncID.ParentBSID);
                                if (bsdetails != null)
                                {
                                    BSName = bsdetails.Name;
                                    bsLogo = GetLogo(incmgtSumByIncID.ParentBSImpactID);
                                    ParentBS = new TreeNode(bsLogo + " " + string.Format("{0}", BSName), incmgtSumByIncID.ParentBSID.ToString());
                                    childParentBF.ChildNodes.Add(ParentBS); //Impacted Business functions (parent) are added
                                }
                            }
                            //}

                            if (incmgtSumByIncID.ChildBFID > 0)
                            {
                                var childbfDetails = facade.GetBusinessFunctionById(incmgtSumByIncID.ChildBFID);
                                if (childbfDetails != null)
                                {
                                    BFName = childbfDetails.Name;
                                    bsLogo = GetLogo(incmgtSumByIncID.ChildBFImpactID);
                                    childBF = new TreeNode(bsLogo + " " + string.Format("{0}", BFName), incmgtSumByIncID.ChildBFID.ToString());
                                    childParentBF.ChildNodes.Add(childBF);//Impacted Business functions due to Impacted business function
                                }
                            }

                            if (incmgtSumByIncID.ChildBSID > 0)
                            {
                                var childbsDetails = facade.GetBusinessServiceById(incmgtSumByIncID.ChildBSID);
                                if (childbsDetails != null)
                                {
                                    BSName = childbsDetails.Name;
                                    bsLogo = GetLogo(incmgtSumByIncID.ChildBSImpactID);
                                    childBS = new TreeNode(bsLogo + " " + string.Format("{0}", BSName), incmgtSumByIncID.ChildBSID.ToString());
                                    childBF.ChildNodes.Add(childBS);
                                }
                            }

                        }




                    }

                }
            }
        }

        /// <summary>
        /// Get impact name
        /// </summary>
        /// <param name="bsImpact">bsImpact</param>
        /// <returns>Impact Name as string</returns>
        /// <author>Ram Mahajan-05/03/2015</author>
        private string GetImpactName(BusinessImpact bsImpact, string key)
        {
            string impactType = string.Empty;

          

            switch (key)
            {
                case "PBF":
                    if (bsImpact.BFImpactID == MajorlyImpactId) // (int)BIImpactType.Majorly)
                        impactType = "Major Impact";
                    else if (bsImpact.BFImpactID == PartiallyImpactId ) //(int)BIImpactType.Partially)
                        impactType = "Partial Impact";
                    else if (bsImpact.BFImpactID == TotallyImpactId ) // (int)BIImpactType.Totally)
                        impactType = "Total Impact";
                    break;
                case "CBF":
                    if (bsImpact.ChildBFImpactID == MajorlyImpactId) // (int)BIImpactType.Majorly)
                        impactType = "Major Impact";
                    else if (bsImpact.ChildBFImpactID == PartiallyImpactId) //(int)BIImpactType.Partially)
                        impactType = "Partial Impact";
                    else if (bsImpact.ChildBFImpactID == TotallyImpactId) // (int)BIImpactType.Totally)
                        impactType = "Total Impact";
                    break;
                case "BS":
                    if (bsImpact.BSImpactID == MajorlyImpactId) // (int)BIImpactType.Majorly)
                        impactType = "Major Impact";
                    else if (bsImpact.BSImpactID == PartiallyImpactId) //(int)BIImpactType.Partially)
                        impactType = "Partial Impact";
                    else if (bsImpact.BSImpactID == TotallyImpactId) // (int)BIImpactType.Totally)
                        impactType = "Total Impact";
                    break;
                default:
                    impactType = string.Empty;
                    break;
            }
            return impactType;
        }

        /// <summary>
        /// Create datatable for TreeList
        /// </summary>
        /// <returns></returns>
        protected DataTable BuildTableStructure()
        {
            DataTable table = new DataTable();

            table.Columns.Add("Id", typeof(String));
            table.Columns.Add("Item", typeof(String));
            table.Columns.Add("ItemId", typeof(String));
            table.Columns.Add("ItemType", typeof(String));
            table.Columns.Add("ParentID", typeof(String));
            table.Columns.Add("ImpactType", typeof(String));
            table.Columns.Add("ImpactEntity", typeof(String));
            table.Columns.Add("IncidentName", typeof(String));
            table.Columns.Add("ClientSystemTicketID", typeof(String));
            table.Columns.Add("ImpactStartDateTime", typeof(String));
            table.Columns.Add("ImpactEndDateTime", typeof(String));
            table.Columns.Add("ImpactReason", typeof(String));
            table.Columns.Add("ImpactCost", typeof(String));
            table.Columns.Add("incidentCode", typeof(String));


            return table;
        }
        #endregion

        #region Public

        #region Business-IT relationship tree

        /// <summary>
        /// Builds noderelation string
        /// </summary>
        /// <param name="businessServiceId">businessServiceId</param>
        /// <returns>string</returns>
        /// <author>Ram mahajan</author>
        public static string GetNodeValue(int businessServiceId)
        {
            try
            {
                if (businessServiceId > 0)
                {
                    #region variables

                    BFIsRedOrYellow = null;

                    //Object used for Heatmap
                    IList<Heatmap> heatMapCompList = null;

                    //Objects used for configured infraObjects for users
                    List<InfraObject> InfraObjectAllFlagList = null;

                    #endregion variables

                    businessService = facade.GetBusinessServiceById(businessServiceId);

                    BusinessFunctionList = facade.GetBusinessFunctionsByBusinessServiceId(businessServiceId);

                    if (BusinessFunctionList != null && BusinessFunctionList.Count > 0)
                    {
                        businessFunctionsTable = ConvertToDataTable<BusinessFunction>(BusinessFunctionList);
                        if (businessService != null && businessFunctionsTable.Rows.Count > 0)
                        {
                            if (buildNodeRelSb == null)
                                buildNodeRelSb = new StringBuilder();
                            //ConstructNodeRelation(businessService.Name, businessFunctionsTable);
                            if (BFIsRedOrYellow == null)
                                BFIsRedOrYellow = new List<string>();

                            ConstructNodeRelationForBusinessFunction(businessService.Name, businessFunctionsTable);
                        }

                        foreach (var bfObj in BusinessFunctionList)
                        {
                            businessFunctionObj = facade.GetBusinessFunctionByName(bfObj.Name);
                            if (businessFunctionObj != null)
                            {
                                infraObjectList = facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(businessServiceId, businessFunctionObj.Id);
                                InfraObjectAllFlagList = new List<InfraObject>();
                                if (infraObjectList != null && infraObjectList.Count > 0)
                                {
                                    //Get infra Object list as per Flag "InfraObjectAllFlag"-added newly on 22/05/2014
                                    IList<UserInfraObject> userInfraDetail = new List<UserInfraObject>();

                                    var userDetail = facade.GetUserById(_currentLoginUserId);

                                    if (userDetail != null)
                                    {
                                        #region InfraObjectAllFlag = true

                                        if (!userDetail.InfraObjectAllFlag)
                                        {
                                            userInfraDetail = facade.GetUserInfraObjectByUserId(userDetail.Id);

                                            if (userInfraDetail != null)
                                            {
                                                foreach (var infraObject in infraObjectList)
                                                {
                                                    foreach (var userinfra in userInfraDetail)
                                                    {
                                                        if (infraObject.Id == userinfra.InfraObjectId)
                                                        {
                                                            InfraObjectAllFlagList.Add(infraObject);
                                                        }
                                                    }
                                                }
                                                if (InfraObjectAllFlagList != null && InfraObjectAllFlagList.Count() > 0)
                                                {
                                                    buildNodeRelSb.Append(";");
                                                    infraObjectTable = ConvertToDataTable<InfraObject>(InfraObjectAllFlagList);
                                                    //if (infraObjectTable.Rows.Count > 0)
                                                    //{
                                                    //    bool isBFAffected = false;

                                                    //    foreach (var infraObj in InfraObjectAllFlagList)
                                                    //    {
                                                    //        isBFAffected = IsInfraObjectAffected(Convert.ToInt16(infraObj.BusinessServiceId), Convert.ToInt16(infraObj.Id));
                                                    //        if (isBFAffected)
                                                    //            break;
                                                    //    }
                                                    //    ConstructNodeRelationForInfraObjects(isBFAffected ? string.Concat(businessFunctionObj.Name, "/red") : businessFunctionObj.Name, infraObjectTable);
                                                    //}
                                                    //if (BFIsRedOrYellow != null && BFIsRedOrYellow.Count() > 0)
                                                    //{
                                                    string BFRedOrYellow = IsBusinessFuncRedOrYellow(BFIsRedOrYellow, businessFunctionObj.Name);
                                                    //    foreach (var item in BFIsRedOrYellow)
                                                    //    {
                                                    //        if (item.Contains(businessFunctionObj.Name))
                                                    //        {
                                                    //            if (item.Contains("/"))
                                                    //            {
                                                    //                string[] BFarray = item.Split('/');
                                                    //                BFRedOrYellow = string.Concat("/", BFarray[1]);
                                                    //                break;
                                                    //            }
                                                    //        }
                                                    //    }
                                                    ConstructNodeRelationForInfraObjects(!string.IsNullOrEmpty(BFRedOrYellow) ? string.Concat(businessFunctionObj.Name, BFRedOrYellow) : businessFunctionObj.Name, infraObjectTable);
                                                    //}
                                                    foreach (var infraObjectDetails in InfraObjectAllFlagList)
                                                    {
                                                        heatMapCompList = new List<Heatmap>();
                                                        heatMapCompList = facade.GetHeatMapByBusinessServiceIdandInfraObjectId(infraObjectDetails.BusinessServiceId, infraObjectDetails.Id);

                                                        GetNodeValueForAppDbInfra(infraObjectDetails, heatMapCompList);
                                                    }
                                                }
                                            }
                                        }

                                        #endregion InfraObjectAllFlag = true

                                        #region InfraObjectAllFlag = false

                                        else
                                        {
                                            buildNodeRelSb.Append(";");
                                            infraObjectTable = ConvertToDataTable<InfraObject>(infraObjectList);
                                            //if (infraObjectTable.Rows.Count > 0)
                                            //{
                                            //    bool isBFAffected = false;

                                            //    foreach (var infraObj in infraObjectList)
                                            //    {
                                            //        isBFAffected = IsInfraObjectAffected(Convert.ToInt16(infraObj.BusinessServiceId), Convert.ToInt16(infraObj.Id));
                                            //        if (isBFAffected)
                                            //            break;
                                            //    }
                                            //    ConstructNodeRelationForInfraObjects(isBFAffected ? string.Concat(businessFunctionObj.Name, "/red") : businessFunctionObj.Name, infraObjectTable);
                                            //}
                                            //if (BFIsRedOrYellow != null && BFIsRedOrYellow.Count() > 0)
                                            //{
                                            string BFRedOrYellow = IsBusinessFuncRedOrYellow(BFIsRedOrYellow, businessFunctionObj.Name);
                                            //    foreach (var item in BFIsRedOrYellow)
                                            //    {
                                            //        if (item.Contains(businessFunctionObj.Name))
                                            //        {
                                            //            if (item.Contains("/"))
                                            //            {
                                            //                string[] BFarray = item.Split('/');
                                            //                BFRedOrYellow = string.Concat("/", BFarray[1]);
                                            //                break;
                                            //            }
                                            //        }
                                            //    }
                                            ConstructNodeRelationForInfraObjects(!string.IsNullOrEmpty(BFRedOrYellow) ? string.Concat(businessFunctionObj.Name, BFRedOrYellow) : businessFunctionObj.Name, infraObjectTable);
                                            //}
                                            foreach (var infraObjectDetails in infraObjectList)
                                            {
                                                if (heatMapCompList == null)
                                                    heatMapCompList = new List<Heatmap>();
                                                heatMapCompList = facade.GetHeatMapByBusinessServiceIdandInfraObjectId(infraObjectDetails.BusinessServiceId, infraObjectDetails.Id);

                                                GetNodeValueForAppDbInfra(infraObjectDetails, heatMapCompList);
                                            }
                                        }

                                        #endregion InfraObjectAllFlag = false
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting nodevalue", ex);
                ExceptionManager.Manage(cpException);
            }
            return Convert.ToString(buildNodeRelSb);
        }

        /// <summary>
        /// Decides whether to draw yellow or red color lines from business service to business function .
        /// </summary>
        /// <param name="BFList">BFList</param>
        /// <param name="BfName">BfName</param>
        /// <returns>string (either red ,yellow or empty)</returns>
        /// <author>Ram Mahajan 10/10/2014</author>
        public static string IsBusinessFuncRedOrYellow(List<string> BFList, string BfName)
        {
            string BFRedOrYellow = string.Empty;

            if (BFList != null && BFList.Count() > 0)
            {
                foreach (var item in BFIsRedOrYellow)
                {
                    if (item.Contains(businessFunctionObj.Name))
                    {
                        if (item.Contains("/"))
                        {
                            string[] BFarray = item.Split('/');
                            BFRedOrYellow = string.Concat("/", BFarray[1]);
                            break;
                        }
                    }
                }
            }
            return BFRedOrYellow;
        }

        /// <summary>
        /// Get Database SID if SID is not null otherwise database name
        /// </summary>
        /// <param name="dbBase">DatabaseBase object</param>
        /// <returns>SID</returns>
        /// <author>Ram Mahajan-20062014</author>
        public static string GetDBSid(IList<DatabaseBase> prdrDatabaseList, DatabaseBase dbBase)
        {
            string sid = string.Empty;
            switch ((int)dbBase.DatabaseType)
            {
                case (int)DatabaseType.Oracle:
                    var dbOracleList = (from p in prdrDatabaseList select p).First();
                    if (dbOracleList != null)
                        sid = !string.IsNullOrEmpty(dbOracleList.DatabaseOracle.OracleSID) ? dbOracleList.DatabaseOracle.OracleSID : dbBase.Name;
                    else
                        sid = dbBase.Name;
                    break;

                case (int)DatabaseType.Sql:
                    var dbSqlList = (from p in prdrDatabaseList select p).First();
                    if (dbSqlList != null)
                        sid = !string.IsNullOrEmpty(dbSqlList.DatabaseSql.DatabaseSID) ? dbSqlList.DatabaseSql.DatabaseSID : dbBase.Name;
                    else
                        sid = dbBase.Name;
                    break;

                case (int)DatabaseType.DB2:
                    var dbDB2List = (from p in prdrDatabaseList select p).First();
                    if (dbDB2List != null)
                        sid = !string.IsNullOrEmpty(dbDB2List.DatabaseDb2.DatabaseSID) ? dbDB2List.DatabaseDb2.DatabaseSID : dbBase.Name;
                    else
                        sid = dbBase.Name;
                    break;

                case (int)DatabaseType.MySQL:
                    var dbMySQLList = (from p in prdrDatabaseList select p).First();
                    if (dbMySQLList != null)
                        sid = !string.IsNullOrEmpty(dbMySQLList.DatabaseMySql.DatabaseID) ? dbMySQLList.DatabaseMySql.DatabaseID : dbBase.Name;
                    else
                        sid = dbBase.Name;
                    break;

                case (int)DatabaseType.OracleRac:
                    var dbOracleRac = (from p in prdrDatabaseList select p).First();
                    if (dbOracleRac != null)
                        sid = !string.IsNullOrEmpty(dbOracleRac.DatabaseOracleRac.OracleSID) ? dbOracleRac.DatabaseOracleRac.OracleSID : dbBase.Name;
                    else
                        sid = dbBase.Name;
                    break;

                case (int)DatabaseType.Exchange:
                    var dbExchange = (from p in prdrDatabaseList select p).First();
                    if (dbExchange != null)
                        sid = !string.IsNullOrEmpty(dbExchange.Name) ? dbExchange.Name : dbBase.Name;
                    else
                        sid = dbBase.Name;
                    break;

                case (int)DatabaseType.ExchangeDAG:
                    var dbExchangeDag = (from p in prdrDatabaseList select p).First();
                    if (dbExchangeDag != null)
                        sid = !string.IsNullOrEmpty(dbExchangeDag.Name) ? dbExchangeDag.Name : dbBase.Name;
                    else
                        sid = dbBase.Name;
                    break;
            }
            return sid;
        }

        /// <summary>
        /// Construct node relation for App infra type
        /// </summary>
        /// <param name="infraObject">infraObject</param>
        /// <param name="heatMapCompList">heatMapCompList</param>
        /// <author>Ram mahajan-18/06/2014</author>
        public static void GetNodeValueForApplicationInfra(InfraObject infraObject, IList<Heatmap> heatMapCompList)
        {
            #region Dynamic variables

            //objects used for infraType=App in case of only few infraobjects are configured
            Dictionary<int, string> AppinfraObjectDetailsDictionary_AllInfra = null;
            Dictionary<int, string> AppInfraComponent = null;

            DataTable appinfraObjectDetailsTable5_AllInfra = new DataTable();
            DataColumn appDetails_AllInfra = new DataColumn("Name");
            appDetails_AllInfra.DataType = System.Type.GetType("System.String");
            appinfraObjectDetailsTable5_AllInfra.Columns.Add(appDetails_AllInfra);

            var appinfraObjectPRDRServerDetailsList_AllInfra = facade.GetServersByInfraObjectId(infraObject.Id);
            var appinfraObjectPRDRReplicationDetailsList_AllInfra = facade.GetReplicationBaseByInfraObjectId(infraObject.Id);

            //This table holds infraObject PR Server details
            DataTable appinfraObjectPRServerDetailsTable_AllInfra = new DataTable();
            DataColumn appPrServerDetails_AllInfra = new DataColumn("Name");
            appPrServerDetails_AllInfra.DataType = System.Type.GetType("System.String");
            appinfraObjectPRServerDetailsTable_AllInfra.Columns.Add(appPrServerDetails_AllInfra);

            //This table holds infraObject DR Server details
            DataTable appinfraObjectDRServerDetailsTable_AllInfra = new DataTable();
            DataColumn appDrServerDetails_AllInfra = new DataColumn("Name");
            appDrServerDetails_AllInfra.DataType = System.Type.GetType("System.String");
            appinfraObjectDRServerDetailsTable_AllInfra.Columns.Add(appDrServerDetails_AllInfra);

            //This table holds infraObject replication details
            DataTable appinfraObjectPRDRReplicationDetailsTable_AllInfra = new DataTable();
            DataColumn appReplication_AllInfra = new DataColumn("Name");
            appReplication_AllInfra.DataType = System.Type.GetType("System.String");
            appinfraObjectPRDRReplicationDetailsTable_AllInfra.Columns.Add(appReplication_AllInfra);

            #endregion Dynamic variables

            #region App PRDRServerDetails

            AppinfraObjectDetailsDictionary_AllInfra = new Dictionary<int, string>();
            AppInfraComponent = new Dictionary<int, string>();

            if (appinfraObjectPRDRServerDetailsList_AllInfra != null && appinfraObjectPRDRServerDetailsList_AllInfra.Count() > 0)
            {
                bool isAppPRDRServerAffected = false;
                bool isHMCServerFound = false;

                foreach (var appPRDRServerDetail_AllInfra in appinfraObjectPRDRServerDetailsList_AllInfra)
                {
                    isAppPRDRServerAffected = false;

                    if (!string.IsNullOrEmpty(appPRDRServerDetail_AllInfra.Name))
                    {
                        if (appPRDRServerDetail_AllInfra.Type.Contains("PR"))
                        {
                            AppinfraObjectDetailsDictionary_AllInfra.Add(1, appPRDRServerDetail_AllInfra.Name);

                            if (heatMapCompList != null && heatMapCompList.Count() > 0)
                            {
                                if (IsInfraObjectComponentAffected(heatMapCompList, appPRDRServerDetail_AllInfra.Id))
                                {
                                    AppInfraComponent.Add(1, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide/logo/red"));
                                    isAppPRDRServerAffected = true;
                                }
                                else
                                {
                                    AppInfraComponent.Add(1, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide"));
                                    isAppPRDRServerAffected = false;
                                }
                            }
                            else
                            {
                                AppInfraComponent.Add(1, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide"));
                                isAppPRDRServerAffected = false;
                            }
                        }
                        else if (appPRDRServerDetail_AllInfra.Type.Contains("DR"))
                        {
                            AppinfraObjectDetailsDictionary_AllInfra.Add(3, appPRDRServerDetail_AllInfra.Name);
                            if (heatMapCompList != null && heatMapCompList.Count() > 0)
                            {
                                if (IsInfraObjectComponentAffected(heatMapCompList, appPRDRServerDetail_AllInfra.Id))
                                {
                                    AppInfraComponent.Add(3, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide/logo/red"));
                                    isAppPRDRServerAffected = true;
                                }
                                else
                                {
                                    AppInfraComponent.Add(3, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide"));
                                    isAppPRDRServerAffected = false;
                                }
                            }
                            else
                            {
                                AppInfraComponent.Add(3, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide"));
                                isAppPRDRServerAffected = false;
                            }
                        }
                        else if (appPRDRServerDetail_AllInfra.Type.Equals(GroupServerType.HMCServer.ToString()))
                        {
                            if (!isHMCServerFound)
                            {
                                isHMCServerFound = true;
                                AppinfraObjectDetailsDictionary_AllInfra.Add(1, appPRDRServerDetail_AllInfra.Name);

                                if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                {
                                    if (IsInfraObjectComponentAffected(heatMapCompList, appPRDRServerDetail_AllInfra.Id))
                                    {
                                        AppInfraComponent.Add(1, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide/logo/red"));
                                        isAppPRDRServerAffected = true;
                                    }
                                    else
                                    {
                                        AppInfraComponent.Add(1, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide"));
                                        isAppPRDRServerAffected = false;
                                    }
                                }
                                else
                                {
                                    AppInfraComponent.Add(1, string.Concat(appPRDRServerDetail_AllInfra.Name, "/Hide"));
                                    isAppPRDRServerAffected = false;
                                }

                                if (!string.IsNullOrEmpty(appPRDRServerDetail_AllInfra.IPAddress))
                                {
                                    DataRow appPRServerDetailIPAddressRow_AllInfra = appinfraObjectPRServerDetailsTable_AllInfra.NewRow();
                                    //appPRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress));

                                    appPRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", isAppPRDRServerAffected ? string.Concat(CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress));

                                    appinfraObjectPRServerDetailsTable_AllInfra.Rows.Add(appPRServerDetailIPAddressRow_AllInfra);
                                }

                                if (!string.IsNullOrEmpty(appPRDRServerDetail_AllInfra.OSType))
                                {
                                    DataRow appPRServerDetailIOSTypeRow_AllInfra = appinfraObjectPRServerDetailsTable_AllInfra.NewRow();
                                    //appPRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", appPRDRServerDetail_AllInfra.OSType);

                                    appPRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", isAppPRDRServerAffected ? string.Concat(appPRDRServerDetail_AllInfra.OSType, "/red") : appPRDRServerDetail_AllInfra.OSType);

                                    appinfraObjectPRServerDetailsTable_AllInfra.Rows.Add(appPRServerDetailIOSTypeRow_AllInfra);
                                }
                            }
                        }
                    }

                    if ((appPRDRServerDetail_AllInfra.Type.Equals(GroupServerType.PRDBServer.ToString()) ||
                        (appPRDRServerDetail_AllInfra.Type.Equals(GroupServerType.PRAppServer.ToString()))))
                    {
                        if (!string.IsNullOrEmpty(appPRDRServerDetail_AllInfra.IPAddress))
                        {
                            DataRow appPRServerDetailIPAddressRow_AllInfra = appinfraObjectPRServerDetailsTable_AllInfra.NewRow();
                            //appPRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress));

                            appPRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", isAppPRDRServerAffected ? string.Concat(CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress));

                            appinfraObjectPRServerDetailsTable_AllInfra.Rows.Add(appPRServerDetailIPAddressRow_AllInfra);
                        }

                        if (!string.IsNullOrEmpty(appPRDRServerDetail_AllInfra.OSType))
                        {
                            DataRow appPRServerDetailIOSTypeRow_AllInfra = appinfraObjectPRServerDetailsTable_AllInfra.NewRow();
                            //appPRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", appPRDRServerDetail_AllInfra.OSType);

                            appPRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", isAppPRDRServerAffected ? string.Concat(appPRDRServerDetail_AllInfra.OSType, "/red") : appPRDRServerDetail_AllInfra.OSType);

                            appinfraObjectPRServerDetailsTable_AllInfra.Rows.Add(appPRServerDetailIOSTypeRow_AllInfra);
                        }
                    }
                    else if ((appPRDRServerDetail_AllInfra.Type.Equals(GroupServerType.DRDBServer.ToString()) ||
                        (appPRDRServerDetail_AllInfra.Type.Equals(GroupServerType.DRAppServer.ToString()))))
                    {
                        if (!string.IsNullOrEmpty(appPRDRServerDetail_AllInfra.IPAddress))
                        {
                            DataRow appDRServerDetailIPAddressRow_AllInfra = appinfraObjectDRServerDetailsTable_AllInfra.NewRow();
                            //appDRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress));

                            appDRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", isAppPRDRServerAffected ? string.Concat(CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(appPRDRServerDetail_AllInfra.IPAddress));

                            appinfraObjectDRServerDetailsTable_AllInfra.Rows.Add(appDRServerDetailIPAddressRow_AllInfra);
                        }

                        if (!string.IsNullOrEmpty(appPRDRServerDetail_AllInfra.OSType))
                        {
                            DataRow appDRServerDetailIOSTypeRow_AllInfra = appinfraObjectDRServerDetailsTable_AllInfra.NewRow();
                            //appDRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", appPRDRServerDetail_AllInfra.OSType);

                            appDRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", isAppPRDRServerAffected ? string.Concat(appPRDRServerDetail_AllInfra.OSType, "/red") : appPRDRServerDetail_AllInfra.OSType);

                            appinfraObjectDRServerDetailsTable_AllInfra.Rows.Add(appDRServerDetailIOSTypeRow_AllInfra);
                        }
                    }
                }
            }

            #endregion App PRDRServerDetails

            #region App PRDRReplicationDetails

            if (appinfraObjectPRDRReplicationDetailsList_AllInfra != null && appinfraObjectPRDRReplicationDetailsList_AllInfra.Count() > 0)
            {
                bool isAppDRReplication_AllInfra = false;
                bool isAppPRDRRepAffected = false;

                foreach (var appPRDRReplicationDetail_AllInfra in appinfraObjectPRDRReplicationDetailsList_AllInfra)
                {
                    isAppPRDRRepAffected = false;

                    if (!string.IsNullOrEmpty(appPRDRReplicationDetail_AllInfra.Name))
                    {
                        if (!string.IsNullOrEmpty(appPRDRReplicationDetail_AllInfra.Type.ToString()))
                        {
                            if (!isAppDRReplication_AllInfra)
                            {
                                if (appinfraObjectPRDRReplicationDetailsList_AllInfra.Count() > 1)
                                {
                                    if (appPRDRReplicationDetail_AllInfra.Id == infraObject.DRReplicationId)
                                    {
                                        isAppDRReplication_AllInfra = true;
                                        AppinfraObjectDetailsDictionary_AllInfra.Add(2, appPRDRReplicationDetail_AllInfra.Name);

                                        if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                        {
                                            if (IsInfraObjectComponentAffected(heatMapCompList, appPRDRReplicationDetail_AllInfra.Id))
                                            {
                                                AppInfraComponent.Add(2, string.Concat(appPRDRReplicationDetail_AllInfra.Name, "/Hide/logo/red"));
                                                isAppPRDRRepAffected = true;
                                            }
                                            else
                                            {
                                                AppInfraComponent.Add(2, string.Concat(appPRDRReplicationDetail_AllInfra.Name, "/Hide"));
                                                isAppPRDRRepAffected = false;
                                            }
                                        }
                                        else
                                        {
                                            AppInfraComponent.Add(2, string.Concat(appPRDRReplicationDetail_AllInfra.Name, "/Hide"));
                                            isAppPRDRRepAffected = false;
                                        }

                                        DataRow appReplicationRow_AllInfra = appinfraObjectPRDRReplicationDetailsTable_AllInfra.NewRow();
                                        //appReplicationRow_AllInfra["Name"] = appPRDRReplicationDetail_AllInfra.Type.ToString();

                                        appReplicationRow_AllInfra["Name"] = isAppPRDRRepAffected ? string.Concat(appPRDRReplicationDetail_AllInfra.Type.ToString(), "/red") : appPRDRReplicationDetail_AllInfra.Type.ToString();

                                        appinfraObjectPRDRReplicationDetailsTable_AllInfra.Rows.Add(appReplicationRow_AllInfra);
                                    }
                                }
                                else if (appinfraObjectPRDRReplicationDetailsList_AllInfra.Count() == 1)
                                {
                                    if (appPRDRReplicationDetail_AllInfra.Id == infraObject.DRReplicationId || appPRDRReplicationDetail_AllInfra.Id == infraObject.PRReplicationId)
                                    {
                                        isAppDRReplication_AllInfra = true;
                                        AppinfraObjectDetailsDictionary_AllInfra.Add(2, appPRDRReplicationDetail_AllInfra.Name);

                                        if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                        {
                                            if (IsInfraObjectComponentAffected(heatMapCompList, appPRDRReplicationDetail_AllInfra.Id))
                                            {
                                                AppInfraComponent.Add(2, string.Concat(appPRDRReplicationDetail_AllInfra.Name, "/Hide/logo/red"));
                                                isAppPRDRRepAffected = true;
                                            }
                                            else
                                            {
                                                AppInfraComponent.Add(2, string.Concat(appPRDRReplicationDetail_AllInfra.Name, "/Hide"));
                                                isAppPRDRRepAffected = false;
                                            }
                                        }
                                        else
                                        {
                                            AppInfraComponent.Add(2, string.Concat(appPRDRReplicationDetail_AllInfra.Name, "/Hide"));
                                            isAppPRDRRepAffected = false;
                                        }

                                        DataRow appReplicationRow_AllInfra = appinfraObjectPRDRReplicationDetailsTable_AllInfra.NewRow();
                                        //appReplicationRow_AllInfra["Name"] = appPRDRReplicationDetail_AllInfra.Type.ToString();

                                        appReplicationRow_AllInfra["Name"] = isAppPRDRRepAffected ? string.Concat(appPRDRReplicationDetail_AllInfra.Type.ToString(), "/red") : appPRDRReplicationDetail_AllInfra.Type.ToString();

                                        appinfraObjectPRDRReplicationDetailsTable_AllInfra.Rows.Add(appReplicationRow_AllInfra);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            #endregion App PRDRReplicationDetails

            #region Create Node relation

            if (AppInfraComponent != null && AppInfraComponent.Count > 0)
            {
                var sortedDic = (from dic in AppInfraComponent orderby dic.Key ascending select dic);
                foreach (var item in sortedDic)
                {
                    DataRow appRow = appinfraObjectDetailsTable5_AllInfra.NewRow();
                    appRow["Name"] = item.Value;
                    appinfraObjectDetailsTable5_AllInfra.Rows.Add(appRow);
                }
            }
            if (appinfraObjectDetailsTable5_AllInfra.Rows.Count > 0)
            {
                buildNodeRelSb.Append(";");
                ConstructNodeRelation(CheckLogoForComponent(appinfraObjectDetailsTable5_AllInfra) ? string.Concat(infraObject.Name, "/Hide/logo/red") : string.Concat(infraObject.Name, "/Hide"), appinfraObjectDetailsTable5_AllInfra);
            }

            if (AppinfraObjectDetailsDictionary_AllInfra != null && AppinfraObjectDetailsDictionary_AllInfra.Count() > 0)
            {
                var sortedDic = (from dic in AppinfraObjectDetailsDictionary_AllInfra orderby dic.Key ascending select dic);

                foreach (var item in sortedDic)
                {
                    switch (item.Key)
                    {
                        case 1:
                            buildNodeRelSb.Append(";");
                            if (CheckLogoForComponent(appinfraObjectDetailsTable5_AllInfra, item.Value))
                                ConstructNodeRelation(string.Concat(item.Value, "/Hide/logo/red"), appinfraObjectPRServerDetailsTable_AllInfra);
                            else
                                ConstructNodeRelation(string.Concat(item.Value, "/Hide"), appinfraObjectPRServerDetailsTable_AllInfra);
                            break;

                        case 3:
                            buildNodeRelSb.Append(";");
                            if (CheckLogoForComponent(appinfraObjectDetailsTable5_AllInfra, item.Value))
                                ConstructNodeRelation(string.Concat(item.Value, "/Hide/logo/red"), appinfraObjectDRServerDetailsTable_AllInfra);
                            else
                                ConstructNodeRelation(string.Concat(item.Value, "/Hide"), appinfraObjectDRServerDetailsTable_AllInfra);
                            break;

                        case 2:
                            buildNodeRelSb.Append(";");
                            if (CheckLogoForComponent(appinfraObjectDetailsTable5_AllInfra, item.Value))
                                ConstructNodeRelation(string.Concat(item.Value, "/Hide/logo/red"), appinfraObjectPRDRReplicationDetailsTable_AllInfra);
                            else
                                ConstructNodeRelation(string.Concat(item.Value, "/Hide"), appinfraObjectPRDRReplicationDetailsTable_AllInfra);
                            break;
                    }
                }

                AppinfraObjectDetailsDictionary_AllInfra = null;
                AppInfraComponent = null;
            }

            #endregion Create Node relation
        }

        /// <summary>
        /// Get Node relation for DB infraType
        /// </summary>
        /// <param name="infraObject">infraObject</param>
        /// <param name="heatMapCompList">heatMapCompList</param>
        /// <author>Ram Mahajan-19/06/2014</author>
        public static void GetNodeValueForDbInfra(InfraObject infraObject, IList<Heatmap> heatMapCompList)
        {
            //objects used for all infraObjects for users
            Dictionary<int, string> InfraObjectDetailsDictionary_AllInfra = null;
            Dictionary<int, string> InfraComponents = null;
            IList<DatabaseBase> PRdatabaseBaseList_AllInfra = null;
            IList<DatabaseBase> DRdatabaseBaseList_AllInfra = null;
            IList<DatabaseBase> PRfilterDataBaseList_AllInfra = null;
            IList<DatabaseBase> DRfilterDataBaseList_AllInfra = null;

            if (infraObject.DRReady == true && infraObject.Type > 0)
            {
                if (infraObject.SubType > 0)
                {
                    var recovrytype = FetchRecoveryType(infraObject.SubType);
                    if (recovrytype != null)
                    {
                        foreach (var rectype in recovrytype)
                        {
                            if (rectype.TypeId == infraObject.SubType && rectype.ID == infraObject.RecoveryType)
                            {
                                #region Dynamic tables

                                DataTable infraObjectDetailsTable1 = new DataTable();
                                DataColumn Details = new DataColumn("Name");
                                Details.DataType = System.Type.GetType("System.String");
                                infraObjectDetailsTable1.Columns.Add(Details);

                                //This table holds infraObject PR Server details
                                DataTable infraObjectPRServerDetailsTable_AllInfra = new DataTable();
                                DataColumn prServerDetails_AllInfra = new DataColumn("Name");
                                prServerDetails_AllInfra.DataType = System.Type.GetType("System.String");
                                infraObjectPRServerDetailsTable_AllInfra.Columns.Add(prServerDetails_AllInfra);

                                //This table holds infraObject DR Server details
                                DataTable infraObjectDRServerDetailsTable_AllInfra = new DataTable();
                                DataColumn drServerDetails_AllInfra = new DataColumn("Name");
                                drServerDetails_AllInfra.DataType = System.Type.GetType("System.String");
                                infraObjectDRServerDetailsTable_AllInfra.Columns.Add(drServerDetails_AllInfra);

                                //This table holds infraObject PR database details
                                DataTable infraObjectPRDBDetailsTable_AllInfra = new DataTable();
                                DataColumn prdbDetails_AllInfra = new DataColumn("Name");
                                prdbDetails_AllInfra.DataType = System.Type.GetType("System.String");
                                infraObjectPRDBDetailsTable_AllInfra.Columns.Add(prdbDetails_AllInfra);

                                //This table holds infraObject DR database details
                                DataTable infraObjectDRDBDetailsTable_AllInfra = new DataTable();
                                DataColumn drdbDetails_AllInfra = new DataColumn("Name");
                                drdbDetails_AllInfra.DataType = System.Type.GetType("System.String");
                                infraObjectDRDBDetailsTable_AllInfra.Columns.Add(drdbDetails_AllInfra);

                                //This table holds infraObject replication details
                                DataTable infraObjectPRDRReplicationDetailsTable_AllInfra = new DataTable();
                                DataColumn replication_AllInfra = new DataColumn("Name");
                                replication_AllInfra.DataType = System.Type.GetType("System.String");
                                infraObjectPRDRReplicationDetailsTable_AllInfra.Columns.Add(replication_AllInfra);

                                #endregion Dynamic tables

                                #region Get PR and DR Server,DB and Replication details

                                var infraObjectPRDRServerDetailsList_AllInfra = facade.GetServersByInfraObjectId(infraObject.Id);
                                var infraObjectPRDRDatabaseDetailsList_AllInfra = facade.GetDatabaseBaseByInfraObjectId(infraObject.Id);
                                var infraObjectPRDRReplicationDetailsList_AllInfra = facade.GetReplicationBaseByInfraObjectId(infraObject.Id);

                                #endregion Get PR and DR Server,DB and Replication details

                                #region PRDRServerDetails

                                InfraComponents = new Dictionary<int, string>();
                                InfraObjectDetailsDictionary_AllInfra = new Dictionary<int, string>();

                                if (infraObjectPRDRServerDetailsList_AllInfra != null && infraObjectPRDRServerDetailsList_AllInfra.Count() > 0)
                                {
                                    bool isPRDRServerAffected = false;
                                    bool isHMCServerFound = false;

                                    foreach (var PRDRServerDetail_AllInfra in infraObjectPRDRServerDetailsList_AllInfra)
                                    {
                                        isPRDRServerAffected = false;

                                        if (!string.IsNullOrEmpty(PRDRServerDetail_AllInfra.Name))
                                        {
                                            if (PRDRServerDetail_AllInfra.Type.Contains("PR"))
                                            {
                                                InfraObjectDetailsDictionary_AllInfra.Add(1, PRDRServerDetail_AllInfra.Name);

                                                if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                                {
                                                    if (IsInfraObjectComponentAffected(heatMapCompList, PRDRServerDetail_AllInfra.Id))
                                                    {
                                                        InfraComponents.Add(1, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide/logo/red"));
                                                        isPRDRServerAffected = true;
                                                    }
                                                    else
                                                    {
                                                        InfraComponents.Add(1, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide"));
                                                        isPRDRServerAffected = false;
                                                    }
                                                }
                                                else
                                                {
                                                    InfraComponents.Add(1, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide"));
                                                    isPRDRServerAffected = false;
                                                }
                                            }
                                            else if (PRDRServerDetail_AllInfra.Type.Contains("DR"))
                                            {
                                                InfraObjectDetailsDictionary_AllInfra.Add(4, PRDRServerDetail_AllInfra.Name);

                                                if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                                {
                                                    if (IsInfraObjectComponentAffected(heatMapCompList, PRDRServerDetail_AllInfra.Id))
                                                    {
                                                        InfraComponents.Add(4, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide/logo/red"));
                                                        isPRDRServerAffected = true;
                                                    }
                                                    else
                                                    {
                                                        InfraComponents.Add(4, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide"));
                                                        isPRDRServerAffected = false;
                                                    }
                                                }
                                                else
                                                {
                                                    InfraComponents.Add(4, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide"));
                                                    isPRDRServerAffected = false;
                                                }
                                            }
                                            else if (PRDRServerDetail_AllInfra.Type.Equals(GroupServerType.HMCServer.ToString()))
                                            {
                                                if (!isHMCServerFound)
                                                {
                                                    isHMCServerFound = true;

                                                    InfraObjectDetailsDictionary_AllInfra.Add(1, PRDRServerDetail_AllInfra.Name);

                                                    if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                                    {
                                                        if (IsInfraObjectComponentAffected(heatMapCompList, PRDRServerDetail_AllInfra.Id))
                                                        {
                                                            InfraComponents.Add(1, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide/logo/red"));
                                                            isPRDRServerAffected = true;
                                                        }
                                                        else
                                                        {
                                                            InfraComponents.Add(1, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide"));
                                                            isPRDRServerAffected = false;
                                                        }
                                                    }
                                                    else
                                                    {
                                                        InfraComponents.Add(1, string.Concat(PRDRServerDetail_AllInfra.Name, "/Hide"));
                                                        isPRDRServerAffected = false;
                                                    }

                                                    if ((PRDRServerDetail_AllInfra.Type.Equals(GroupServerType.HMCServer.ToString())))
                                                    {
                                                        if (!string.IsNullOrEmpty(PRDRServerDetail_AllInfra.IPAddress))
                                                        {
                                                            DataRow PRServerDetailIPAddressRow_AllInfra = infraObjectPRServerDetailsTable_AllInfra.NewRow();
                                                            //PRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress));

                                                            //PRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", (heatMapCompList != null && heatMapCompList.Count() > 0 ? (IsInfraObjectComponentAffected(heatMapCompList, PRDRServerDetail_AllInfra.Id) ? string.Concat(CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress)) : CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress)));

                                                            PRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", isPRDRServerAffected ? string.Concat(CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress));

                                                            infraObjectPRServerDetailsTable_AllInfra.Rows.Add(PRServerDetailIPAddressRow_AllInfra);
                                                        }

                                                        if (!string.IsNullOrEmpty(PRDRServerDetail_AllInfra.OSType))
                                                        {
                                                            DataRow PRServerDetailIOSTypeRow_AllInfra = infraObjectPRServerDetailsTable_AllInfra.NewRow();
                                                            //PRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", PRDRServerDetail_AllInfra.OSType);

                                                            PRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", isPRDRServerAffected ? string.Concat(PRDRServerDetail_AllInfra.OSType, "/red") : PRDRServerDetail_AllInfra.OSType);

                                                            infraObjectPRServerDetailsTable_AllInfra.Rows.Add(PRServerDetailIOSTypeRow_AllInfra);
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        if ((PRDRServerDetail_AllInfra.Type.Equals(GroupServerType.PRDBServer.ToString()) || (PRDRServerDetail_AllInfra.Type.Equals(GroupServerType.PRAppServer.ToString()))))
                                        {
                                            if (!string.IsNullOrEmpty(PRDRServerDetail_AllInfra.IPAddress))
                                            {
                                                DataRow PRServerDetailIPAddressRow_AllInfra = infraObjectPRServerDetailsTable_AllInfra.NewRow();
                                                //PRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress));

                                                //PRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", (heatMapCompList != null && heatMapCompList.Count() > 0 ? (IsInfraObjectComponentAffected(heatMapCompList, PRDRServerDetail_AllInfra.Id) ? string.Concat(CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress)) : CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress)));

                                                PRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", isPRDRServerAffected ? string.Concat(CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress));

                                                infraObjectPRServerDetailsTable_AllInfra.Rows.Add(PRServerDetailIPAddressRow_AllInfra);
                                            }

                                            if (!string.IsNullOrEmpty(PRDRServerDetail_AllInfra.OSType))
                                            {
                                                DataRow PRServerDetailIOSTypeRow_AllInfra = infraObjectPRServerDetailsTable_AllInfra.NewRow();
                                                //PRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", PRDRServerDetail_AllInfra.OSType);

                                                PRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", isPRDRServerAffected ? string.Concat(PRDRServerDetail_AllInfra.OSType, "/red") : PRDRServerDetail_AllInfra.OSType);

                                                infraObjectPRServerDetailsTable_AllInfra.Rows.Add(PRServerDetailIOSTypeRow_AllInfra);
                                            }
                                        }
                                        else if ((PRDRServerDetail_AllInfra.Type.Equals(GroupServerType.DRDBServer.ToString()) || (PRDRServerDetail_AllInfra.Type.Equals(GroupServerType.DRAppServer.ToString()))))
                                        {
                                            if (!string.IsNullOrEmpty(PRDRServerDetail_AllInfra.IPAddress))
                                            {
                                                DataRow DRServerDetailIPAddressRow_AllInfra = infraObjectDRServerDetailsTable_AllInfra.NewRow();
                                                //DRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress));

                                                DRServerDetailIPAddressRow_AllInfra["Name"] = string.Format("{0} - {1}", "IP", isPRDRServerAffected ? string.Concat(CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(PRDRServerDetail_AllInfra.IPAddress));

                                                infraObjectDRServerDetailsTable_AllInfra.Rows.Add(DRServerDetailIPAddressRow_AllInfra);
                                            }

                                            if (!string.IsNullOrEmpty(PRDRServerDetail_AllInfra.OSType))
                                            {
                                                DataRow DRServerDetailIOSTypeRow_AllInfra = infraObjectDRServerDetailsTable_AllInfra.NewRow();
                                                //DRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", PRDRServerDetail_AllInfra.OSType);

                                                DRServerDetailIOSTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "OS", isPRDRServerAffected ? string.Concat(PRDRServerDetail_AllInfra.OSType, "/red") : PRDRServerDetail_AllInfra.OSType);

                                                infraObjectDRServerDetailsTable_AllInfra.Rows.Add(DRServerDetailIOSTypeRow_AllInfra);
                                            }
                                        }
                                    }
                                }

                                #endregion PRDRServerDetails

                                #region PRDRReplicationDetails

                                if (infraObjectPRDRReplicationDetailsList_AllInfra != null && infraObjectPRDRReplicationDetailsList_AllInfra.Count() > 0)
                                {
                                    bool isDRReplication_AllInfra = false;
                                    bool isPRDRReplicationAffected = false;

                                    foreach (var PRDRReplicationDetail_AllInfra in infraObjectPRDRReplicationDetailsList_AllInfra)
                                    {
                                        isPRDRReplicationAffected = false;

                                        if (!string.IsNullOrEmpty(PRDRReplicationDetail_AllInfra.Name))
                                        {
                                            if (!string.IsNullOrEmpty(PRDRReplicationDetail_AllInfra.Type.ToString()))
                                            {
                                                if (!isDRReplication_AllInfra)
                                                {
                                                    if (infraObjectPRDRReplicationDetailsList_AllInfra.Count() > 1)
                                                    {
                                                        if (PRDRReplicationDetail_AllInfra.Id == infraObject.DRReplicationId)
                                                        {
                                                            isDRReplication_AllInfra = true;
                                                            InfraObjectDetailsDictionary_AllInfra.Add(3, PRDRReplicationDetail_AllInfra.Name);

                                                            if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                                            {
                                                                if (IsInfraObjectComponentAffected(heatMapCompList, PRDRReplicationDetail_AllInfra.Id))
                                                                {
                                                                    InfraComponents.Add(3, string.Concat(PRDRReplicationDetail_AllInfra.Name, "/Hide/logo/red"));
                                                                    isPRDRReplicationAffected = true;
                                                                }
                                                                else
                                                                {
                                                                    InfraComponents.Add(3, string.Concat(PRDRReplicationDetail_AllInfra.Name, "/Hide"));
                                                                    isPRDRReplicationAffected = false;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                InfraComponents.Add(3, string.Concat(PRDRReplicationDetail_AllInfra.Name, "/Hide"));
                                                                isPRDRReplicationAffected = false;
                                                            }

                                                            DataRow replicationRow_AllInfra = infraObjectPRDRReplicationDetailsTable_AllInfra.NewRow();
                                                            //replicationRow_AllInfra["Name"] = PRDRReplicationDetail_AllInfra.Type.ToString();

                                                            replicationRow_AllInfra["Name"] = isPRDRReplicationAffected ? string.Concat(PRDRReplicationDetail_AllInfra.Type.ToString(), "/red") : PRDRReplicationDetail_AllInfra.Type.ToString();

                                                            infraObjectPRDRReplicationDetailsTable_AllInfra.Rows.Add(replicationRow_AllInfra);
                                                        }

                                                    }
                                                    else if (infraObjectPRDRReplicationDetailsList_AllInfra.Count() == 1)
                                                    {
                                                        if (PRDRReplicationDetail_AllInfra.Id == infraObject.DRReplicationId || PRDRReplicationDetail_AllInfra.Id == infraObject.PRReplicationId)
                                                        {
                                                            isDRReplication_AllInfra = true;
                                                            InfraObjectDetailsDictionary_AllInfra.Add(3, PRDRReplicationDetail_AllInfra.Name);
                                                            if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                                            {
                                                                if (IsInfraObjectComponentAffected(heatMapCompList, PRDRReplicationDetail_AllInfra.Id))
                                                                {
                                                                    InfraComponents.Add(3, string.Concat(PRDRReplicationDetail_AllInfra.Name, "/Hide/logo/red"));
                                                                    isPRDRReplicationAffected = true;
                                                                }
                                                                else
                                                                {
                                                                    InfraComponents.Add(3, string.Concat(PRDRReplicationDetail_AllInfra.Name, "/Hide"));
                                                                    isPRDRReplicationAffected = false;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                InfraComponents.Add(3, string.Concat(PRDRReplicationDetail_AllInfra.Name, "/Hide"));
                                                                isPRDRReplicationAffected = false;
                                                            }

                                                            DataRow replicationRow_AllInfra = infraObjectPRDRReplicationDetailsTable_AllInfra.NewRow();
                                                            //replicationRow_AllInfra["Name"] = PRDRReplicationDetail_AllInfra.Type.ToString();

                                                            replicationRow_AllInfra["Name"] = isPRDRReplicationAffected ? string.Concat(PRDRReplicationDetail_AllInfra.Type.ToString(), "/red") : PRDRReplicationDetail_AllInfra.Type.ToString();

                                                            infraObjectPRDRReplicationDetailsTable_AllInfra.Rows.Add(replicationRow_AllInfra);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else if (infraObjectPRDRReplicationDetailsList_AllInfra == null)
                                {
                                    if (infraObject.SubType == (int)InfraObjectType.DatabaseNativeReplication && (infraObject.RecoveryType == 4 || infraObject.RecoveryType == 15 || infraObject.RecoveryType == 24))
                                    {
                                        InfraObjectDetailsDictionary_AllInfra.Add(3, RepKey);
                                        if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                        {
                                            if (IsInfraObjectComponentAffected(heatMapCompList, 0))
                                                InfraComponents.Add(3, string.Concat(RepKey, "/Hide/logo/red"));
                                            else
                                                InfraComponents.Add(3, string.Concat(RepKey, "/Hide"));
                                        }
                                        else
                                        {
                                            InfraComponents.Add(3, string.Concat(RepKey, "/Hide"));
                                        }
                                        DataRow replicationRow_AllInfra = infraObjectPRDRReplicationDetailsTable_AllInfra.NewRow();
                                        replicationRow_AllInfra["Name"] = RepValue;
                                        infraObjectPRDRReplicationDetailsTable_AllInfra.Rows.Add(replicationRow_AllInfra);
                                    }
                                }

                                #endregion PRDRReplicationDetails

                                #region PRDRDatabaseDetails

                                if (infraObjectPRDRDatabaseDetailsList_AllInfra != null && infraObjectPRDRDatabaseDetailsList_AllInfra.Count() > 0)
                                {
                                    PRdatabaseBaseList_AllInfra = new List<DatabaseBase>();
                                    DRdatabaseBaseList_AllInfra = new List<DatabaseBase>();

                                    bool isPRDRDBAffected = false;
                                    bool isPRDRDatabseFound = false;
                                    int prDBCount = 0, drDBCount = 0;

                                    foreach (var PRDRDatabaseDetail_AllInfra in infraObjectPRDRDatabaseDetailsList_AllInfra)
                                    {
                                        if (PRDRDatabaseDetail_AllInfra.DatabaseType.ToString().Equals("OracleRac") || PRDRDatabaseDetail_AllInfra.DatabaseType.ToString().Equals("Oracle") && PRDRDatabaseDetail_AllInfra.IsPartofRac)
                                        {
                                            if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.PRDatabase.ToString()))
                                                prDBCount = prDBCount + 1;
                                            else if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.DRDatabase.ToString()))
                                                drDBCount = drDBCount + 1;
                                        }
                                    }

                                    foreach (var PRDRDatabaseDetail_AllInfra in infraObjectPRDRDatabaseDetailsList_AllInfra)
                                    {
                                        isPRDRDBAffected = false;

                                        if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.Name))
                                        {
                                            if (infraObject.SubType != Convert.ToInt32(InfraObjectType.DatabaseStorageReplicationFullDB))
                                            {
                                                if (PRDRDatabaseDetail_AllInfra.Type.Contains("PR"))
                                                {

                                                    if (!InfraObjectDetailsDictionary_AllInfra.ContainsKey(2))
                                                        InfraObjectDetailsDictionary_AllInfra.Add(2, PRDRDatabaseDetail_AllInfra.Name);

                                                    if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                                    {
                                                        if (IsInfraObjectComponentAffected(heatMapCompList, PRDRDatabaseDetail_AllInfra.Id))
                                                        {
                                                            if (!InfraComponents.ContainsKey(2))
                                                            {
                                                                InfraComponents.Add(2, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide/logo/red"));
                                                                isPRDRDBAffected = true;
                                                            }
                                                        }
                                                        else
                                                        {
                                                            if (!InfraComponents.ContainsKey(2))
                                                            {
                                                                InfraComponents.Add(2, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide"));
                                                                isPRDRDBAffected = false;
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (!InfraComponents.ContainsKey(2))
                                                        {
                                                            InfraComponents.Add(2, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide"));
                                                            isPRDRDBAffected = false;
                                                        }
                                                    }
                                                }
                                                else if (PRDRDatabaseDetail_AllInfra.Type.Contains("DR"))
                                                {
                                                    if (!InfraObjectDetailsDictionary_AllInfra.ContainsKey(5))
                                                        InfraObjectDetailsDictionary_AllInfra.Add(5, PRDRDatabaseDetail_AllInfra.Name);

                                                    if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                                    {
                                                        if (IsInfraObjectComponentAffected(heatMapCompList, PRDRDatabaseDetail_AllInfra.Id))
                                                        {
                                                            if (!InfraComponents.ContainsKey(5))
                                                            {
                                                                InfraComponents.Add(5, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide/logo/red"));
                                                                isPRDRDBAffected = true;
                                                            }
                                                        }
                                                        else
                                                        {
                                                            if (!InfraComponents.ContainsKey(5))
                                                            {
                                                                InfraComponents.Add(5, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide"));
                                                                isPRDRDBAffected = false;
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (!InfraComponents.ContainsKey(5))
                                                        {
                                                            InfraComponents.Add(5, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide"));
                                                            isPRDRDBAffected = false;
                                                        }
                                                    }
                                                }

                                                if (prDBCount == infraObjectPRDRDatabaseDetailsList_AllInfra.Count() || drDBCount == infraObjectPRDRDatabaseDetailsList_AllInfra.Count())
                                                {
                                                    if (!isPRDRDatabseFound)
                                                    {
                                                        if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.PRDatabase.ToString()))
                                                        {
                                                            isPRDRDatabseFound = true;

                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                            {
                                                                DataRow PRDatabaseDetailsDBTypeRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                                //PRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                                PRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString(), "/red") : PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                                infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBTypeRow_AllInfra);
                                                            }

                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                            {
                                                                PRdatabaseBaseList_AllInfra = facade.GetDatabaseBasesByType(PRDRDatabaseDetail_AllInfra.DatabaseType);
                                                                if (PRdatabaseBaseList_AllInfra != null && PRdatabaseBaseList_AllInfra.Count() > 0)
                                                                {
                                                                    DataRow PRDatabaseDetailsDBSidRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                                    PRfilterDataBaseList_AllInfra = new List<DatabaseBase>();
                                                                    PRfilterDataBaseList_AllInfra = PRdatabaseBaseList_AllInfra.Where(a => a.Id == PRDRDatabaseDetail_AllInfra.Id).ToList();

                                                                    if (PRfilterDataBaseList_AllInfra != null && PRfilterDataBaseList_AllInfra.Count() > 0)
                                                                    {
                                                                        //PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                        PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", isPRDRDBAffected ? string.Concat(GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra), "/red") : GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                        infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBSidRow_AllInfra);
                                                                    }
                                                                    else
                                                                    {
                                                                        //PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", PRDRDatabaseDetail_AllInfra.Name);

                                                                        PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/red") : PRDRDatabaseDetail_AllInfra.Name);

                                                                        infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBSidRow_AllInfra);
                                                                    }
                                                                }
                                                            }

                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.ServerId.ToString()))
                                                            {
                                                                var dbServerIPAddress_AllInfra = facade.GetServerById(PRDRDatabaseDetail_AllInfra.ServerId);
                                                                if (dbServerIPAddress_AllInfra != null)
                                                                {
                                                                    DataRow PRDatabaseDetailsDBServerIPRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                                    //PRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                    PRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", isPRDRDBAffected ? string.Concat(CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                    infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBServerIPRow_AllInfra);
                                                                }
                                                            }
                                                        }
                                                        else if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.DRDatabase.ToString()))
                                                        {
                                                            if (infraObject.SubType != Convert.ToInt32(InfraObjectType.DatabaseStorageReplicationFullDB))
                                                            {
                                                                isPRDRDatabseFound = true;

                                                                if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                                {
                                                                    DataRow DRDatabaseDetailsDBTypeRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                    //DRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                                    DRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString(), "/red") : PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                                    infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBTypeRow_AllInfra);
                                                                }

                                                                if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                                {
                                                                    DRdatabaseBaseList_AllInfra = facade.GetDatabaseBasesByType(PRDRDatabaseDetail_AllInfra.DatabaseType);
                                                                    if (DRdatabaseBaseList_AllInfra != null && DRdatabaseBaseList_AllInfra.Count() > 0)
                                                                    {
                                                                        DataRow DRDatabaseDetailsDBSidRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                        DRfilterDataBaseList_AllInfra = new List<DatabaseBase>();
                                                                        DRfilterDataBaseList_AllInfra = DRdatabaseBaseList_AllInfra.Where(a => a.Id == PRDRDatabaseDetail_AllInfra.Id).ToList();

                                                                        if (DRfilterDataBaseList_AllInfra != null && DRfilterDataBaseList_AllInfra.Count() > 0)
                                                                        {
                                                                            //DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                            DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", isPRDRDBAffected ? string.Concat(GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra), "/red") : GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                            infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBSidRow_AllInfra);
                                                                        }
                                                                        else
                                                                        {
                                                                            //DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", PRDRDatabaseDetail_AllInfra.Name);

                                                                            DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/red") : PRDRDatabaseDetail_AllInfra.Name);

                                                                            infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBSidRow_AllInfra);
                                                                        }
                                                                    }
                                                                }

                                                                if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.ServerId.ToString()))
                                                                {
                                                                    var dbServerIPAddress_AllInfra = facade.GetServerById(PRDRDatabaseDetail_AllInfra.ServerId);
                                                                    if (dbServerIPAddress_AllInfra != null)
                                                                    {
                                                                        DataRow DRDatabaseDetailsDBServerIPRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                        //DRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                        DRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", isPRDRDBAffected ? string.Concat(CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                        infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBServerIPRow_AllInfra);
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                else if (prDBCount == 1 && drDBCount == 1)
                                                {
                                                    if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.PRDatabase.ToString()))
                                                    {
                                                        if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                        {
                                                            DataRow PRDatabaseDetailsDBTypeRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                            //PRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                            PRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString(), "/red") : PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                            infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBTypeRow_AllInfra);
                                                        }

                                                        if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                        {
                                                            PRdatabaseBaseList_AllInfra = facade.GetDatabaseBasesByType(PRDRDatabaseDetail_AllInfra.DatabaseType);
                                                            if (PRdatabaseBaseList_AllInfra != null && PRdatabaseBaseList_AllInfra.Count() > 0)
                                                            {
                                                                DataRow PRDatabaseDetailsDBSidRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                                PRfilterDataBaseList_AllInfra = new List<DatabaseBase>();
                                                                PRfilterDataBaseList_AllInfra = PRdatabaseBaseList_AllInfra.Where(a => a.Id == PRDRDatabaseDetail_AllInfra.Id).ToList();

                                                                if (PRfilterDataBaseList_AllInfra != null && PRfilterDataBaseList_AllInfra.Count() > 0)
                                                                {
                                                                    //PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                    PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", isPRDRDBAffected ? string.Concat(GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra), "/red") : GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                    infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBSidRow_AllInfra);
                                                                }
                                                                else
                                                                {
                                                                    //PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", PRDRDatabaseDetail_AllInfra.Name);

                                                                    PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/red") : PRDRDatabaseDetail_AllInfra.Name);

                                                                    infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBSidRow_AllInfra);
                                                                }
                                                            }
                                                        }

                                                        if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.ServerId.ToString()))
                                                        {
                                                            var dbServerIPAddress_AllInfra = facade.GetServerById(PRDRDatabaseDetail_AllInfra.ServerId);
                                                            if (dbServerIPAddress_AllInfra != null)
                                                            {
                                                                DataRow PRDatabaseDetailsDBServerIPRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                                //PRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                PRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", isPRDRDBAffected ? string.Concat(CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBServerIPRow_AllInfra);
                                                            }
                                                        }
                                                    }
                                                    else if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.DRDatabase.ToString()))
                                                    {
                                                        if (infraObject.SubType != Convert.ToInt32(InfraObjectType.DatabaseStorageReplicationFullDB))
                                                        {
                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                            {
                                                                DataRow DRDatabaseDetailsDBTypeRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                //DRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                                DRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString(), "/red") : PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                                infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBTypeRow_AllInfra);
                                                            }

                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                            {
                                                                DRdatabaseBaseList_AllInfra = facade.GetDatabaseBasesByType(PRDRDatabaseDetail_AllInfra.DatabaseType);
                                                                if (DRdatabaseBaseList_AllInfra != null && DRdatabaseBaseList_AllInfra.Count() > 0)
                                                                {
                                                                    DataRow DRDatabaseDetailsDBSidRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                    DRfilterDataBaseList_AllInfra = new List<DatabaseBase>();
                                                                    DRfilterDataBaseList_AllInfra = DRdatabaseBaseList_AllInfra.Where(a => a.Id == PRDRDatabaseDetail_AllInfra.Id).ToList();

                                                                    if (DRfilterDataBaseList_AllInfra != null && DRfilterDataBaseList_AllInfra.Count() > 0)
                                                                    {
                                                                        //DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                        DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", isPRDRDBAffected ? string.Concat(GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra), "/red") : GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                        infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBSidRow_AllInfra);
                                                                    }
                                                                    else
                                                                    {
                                                                        //DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", PRDRDatabaseDetail_AllInfra.Name);

                                                                        DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/red") : PRDRDatabaseDetail_AllInfra.Name);

                                                                        infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBSidRow_AllInfra);
                                                                    }
                                                                }
                                                            }

                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.ServerId.ToString()))
                                                            {
                                                                var dbServerIPAddress_AllInfra = facade.GetServerById(PRDRDatabaseDetail_AllInfra.ServerId);
                                                                if (dbServerIPAddress_AllInfra != null)
                                                                {
                                                                    DataRow DRDatabaseDetailsDBServerIPRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                    //DRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                    DRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", isPRDRDBAffected ? string.Concat(CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                    infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBServerIPRow_AllInfra);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                else if ((!PRDRDatabaseDetail_AllInfra.DatabaseType.ToString().Equals("OracleRac") || !PRDRDatabaseDetail_AllInfra.DatabaseType.ToString().Equals("Oracle") && !PRDRDatabaseDetail_AllInfra.IsPartofRac))
                                                {
                                                    if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.PRDatabase.ToString()))
                                                    {
                                                        if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                        {
                                                            DataRow PRDatabaseDetailsDBTypeRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                            //PRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                            PRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString(), "/red") : PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                            infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBTypeRow_AllInfra);
                                                        }

                                                        if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                        {
                                                            PRdatabaseBaseList_AllInfra = facade.GetDatabaseBasesByType(PRDRDatabaseDetail_AllInfra.DatabaseType);
                                                            if (PRdatabaseBaseList_AllInfra != null && PRdatabaseBaseList_AllInfra.Count() > 0)
                                                            {
                                                                DataRow PRDatabaseDetailsDBSidRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                                PRfilterDataBaseList_AllInfra = new List<DatabaseBase>();
                                                                PRfilterDataBaseList_AllInfra = PRdatabaseBaseList_AllInfra.Where(a => a.Id == PRDRDatabaseDetail_AllInfra.Id).ToList();

                                                                if (PRfilterDataBaseList_AllInfra != null && PRfilterDataBaseList_AllInfra.Count() > 0)
                                                                {
                                                                    //PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                    PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", isPRDRDBAffected ? string.Concat(GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra), "/red") : GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                    infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBSidRow_AllInfra);
                                                                }
                                                                else
                                                                {
                                                                    //PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", PRDRDatabaseDetail_AllInfra.Name);

                                                                    PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/red") : PRDRDatabaseDetail_AllInfra.Name);

                                                                    infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBSidRow_AllInfra);
                                                                }
                                                            }
                                                        }

                                                        if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.ServerId.ToString()))
                                                        {
                                                            var dbServerIPAddress_AllInfra = facade.GetServerById(PRDRDatabaseDetail_AllInfra.ServerId);
                                                            if (dbServerIPAddress_AllInfra != null)
                                                            {
                                                                DataRow PRDatabaseDetailsDBServerIPRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                                //PRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                PRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", isPRDRDBAffected ? string.Concat(CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBServerIPRow_AllInfra);
                                                            }
                                                        }
                                                    }
                                                    else if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.DRDatabase.ToString()))
                                                    {
                                                        if (infraObject.SubType != Convert.ToInt32(InfraObjectType.DatabaseStorageReplicationFullDB))
                                                        {
                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                            {
                                                                DataRow DRDatabaseDetailsDBTypeRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                //DRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                                DRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString(), "/red") : PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                                infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBTypeRow_AllInfra);
                                                            }

                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                            {
                                                                DRdatabaseBaseList_AllInfra = facade.GetDatabaseBasesByType(PRDRDatabaseDetail_AllInfra.DatabaseType);
                                                                if (DRdatabaseBaseList_AllInfra != null && DRdatabaseBaseList_AllInfra.Count() > 0)
                                                                {
                                                                    DataRow DRDatabaseDetailsDBSidRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                    DRfilterDataBaseList_AllInfra = new List<DatabaseBase>();
                                                                    DRfilterDataBaseList_AllInfra = DRdatabaseBaseList_AllInfra.Where(a => a.Id == PRDRDatabaseDetail_AllInfra.Id).ToList();

                                                                    if (DRfilterDataBaseList_AllInfra != null && DRfilterDataBaseList_AllInfra.Count() > 0)
                                                                    {
                                                                        //DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                        DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", isPRDRDBAffected ? string.Concat(GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra), "/red") : GetDBSid(DRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                        infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBSidRow_AllInfra);
                                                                    }
                                                                    else
                                                                    {
                                                                        //DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", PRDRDatabaseDetail_AllInfra.Name);

                                                                        DRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/red") : PRDRDatabaseDetail_AllInfra.Name);

                                                                        infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBSidRow_AllInfra);
                                                                    }
                                                                }
                                                            }

                                                            if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.ServerId.ToString()))
                                                            {
                                                                var dbServerIPAddress_AllInfra = facade.GetServerById(PRDRDatabaseDetail_AllInfra.ServerId);
                                                                if (dbServerIPAddress_AllInfra != null)
                                                                {
                                                                    DataRow DRDatabaseDetailsDBServerIPRow_AllInfra = infraObjectDRDBDetailsTable_AllInfra.NewRow();
                                                                    //DRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                    DRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", isPRDRDBAffected ? string.Concat(CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                                    infraObjectDRDBDetailsTable_AllInfra.Rows.Add(DRDatabaseDetailsDBServerIPRow_AllInfra);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            else if (infraObject.SubType == Convert.ToInt32(InfraObjectType.DatabaseStorageReplicationFullDB))
                                            {
                                                isPRDRDBAffected = false;

                                                if (PRDRDatabaseDetail_AllInfra.Type.Equals(PRDRDatabaseType.PRDatabase.ToString()))
                                                {
                                                    if (PRDRDatabaseDetail_AllInfra.Type.Contains("PR"))
                                                    {
                                                        InfraObjectDetailsDictionary_AllInfra.Add(2, PRDRDatabaseDetail_AllInfra.Name);
                                                        if (heatMapCompList != null && heatMapCompList.Count() > 0)
                                                        {
                                                            if (IsInfraObjectComponentAffected(heatMapCompList, PRDRDatabaseDetail_AllInfra.Id))
                                                            {
                                                                InfraComponents.Add(2, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide/logo/red"));
                                                                isPRDRDBAffected = true;
                                                            }
                                                            else
                                                            {
                                                                InfraComponents.Add(2, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide"));
                                                                isPRDRDBAffected = false;
                                                            }
                                                        }
                                                        else
                                                        {
                                                            InfraComponents.Add(2, string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/Hide"));
                                                            isPRDRDBAffected = false;
                                                        }
                                                    }

                                                    if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                    {
                                                        DataRow PRDatabaseDetailsDBTypeRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                        //PRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                        PRDatabaseDetailsDBTypeRow_AllInfra["Name"] = string.Format("{0} - {1}", "DB", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString(), "/red") : PRDRDatabaseDetail_AllInfra.DatabaseType.ToString());

                                                        infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBTypeRow_AllInfra);
                                                    }

                                                    if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.DatabaseType.ToString()))
                                                    {
                                                        PRdatabaseBaseList_AllInfra = facade.GetDatabaseBasesByType(PRDRDatabaseDetail_AllInfra.DatabaseType);
                                                        if (PRdatabaseBaseList_AllInfra != null && PRdatabaseBaseList_AllInfra.Count() > 0)
                                                        {
                                                            DataRow PRDatabaseDetailsDBSidRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                            PRfilterDataBaseList_AllInfra = new List<DatabaseBase>();
                                                            PRfilterDataBaseList_AllInfra = PRdatabaseBaseList_AllInfra.Where(a => a.Id == PRDRDatabaseDetail_AllInfra.Id).ToList();

                                                            if (PRfilterDataBaseList_AllInfra != null && PRfilterDataBaseList_AllInfra.Count() > 0)
                                                            {
                                                                //PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "SID", isPRDRDBAffected ? string.Concat(GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra), "/red") : GetDBSid(PRfilterDataBaseList_AllInfra, PRDRDatabaseDetail_AllInfra));

                                                                infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBSidRow_AllInfra);
                                                            }
                                                            else
                                                            {
                                                                //PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", PRDRDatabaseDetail_AllInfra.Name);

                                                                PRDatabaseDetailsDBSidRow_AllInfra["Name"] = string.Format("{0} - {1}", "DBName", isPRDRDBAffected ? string.Concat(PRDRDatabaseDetail_AllInfra.Name, "/red") : PRDRDatabaseDetail_AllInfra.Name);

                                                                infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBSidRow_AllInfra);
                                                            }
                                                        }
                                                    }

                                                    if (!string.IsNullOrEmpty(PRDRDatabaseDetail_AllInfra.ServerId.ToString()))
                                                    {
                                                        var dbServerIPAddress_AllInfra = facade.GetServerById(PRDRDatabaseDetail_AllInfra.ServerId);
                                                        if (dbServerIPAddress_AllInfra != null)
                                                        {
                                                            DataRow PRDatabaseDetailsDBServerIPRow_AllInfra = infraObjectPRDBDetailsTable_AllInfra.NewRow();
                                                            //PRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                            PRDatabaseDetailsDBServerIPRow_AllInfra["Name"] = string.Format("{0} - {1}", "Server", isPRDRDBAffected ? string.Concat(CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress), "/red") : CryptographyHelper.Md5Decrypt(dbServerIPAddress_AllInfra.IPAddress));

                                                            infraObjectPRDBDetailsTable_AllInfra.Rows.Add(PRDatabaseDetailsDBServerIPRow_AllInfra);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                #endregion PRDRDatabaseDetails

                                #region create Node Relation

                                if (InfraComponents != null && InfraComponents.Count > 0)
                                {
                                    var sortedDic = (from dic in InfraComponents orderby dic.Key ascending select dic);
                                    foreach (var item in sortedDic)
                                    {
                                        DataRow row = infraObjectDetailsTable1.NewRow();
                                        row["Name"] = item.Value;
                                        infraObjectDetailsTable1.Rows.Add(row);
                                    }
                                }

                                if (infraObjectDetailsTable1.Rows.Count > 0)
                                {
                                    buildNodeRelSb.Append(";");
                                    ConstructNodeRelation(CheckLogoForComponent(infraObjectDetailsTable1) ? string.Concat(infraObject.Name, "/Hide/logo/red") : string.Concat(infraObject.Name, "/Hide"), infraObjectDetailsTable1);
                                }

                                if (InfraObjectDetailsDictionary_AllInfra != null && InfraObjectDetailsDictionary_AllInfra.Count() > 0)
                                {
                                    var list = InfraObjectDetailsDictionary_AllInfra.Keys.ToList();
                                    var sortedDic = (from dic in InfraObjectDetailsDictionary_AllInfra orderby dic.Key ascending select dic);
                                    list.Sort();
                                    foreach (var item in sortedDic)
                                    {
                                        switch (item.Key)
                                        {
                                            case 1:
                                                buildNodeRelSb.Append(";");
                                                if (CheckLogoForComponent(infraObjectDetailsTable1, item.Value))
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide/logo/red"), infraObjectPRServerDetailsTable_AllInfra);
                                                else
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide"), infraObjectPRServerDetailsTable_AllInfra);
                                                break;

                                            case 4:
                                                buildNodeRelSb.Append(";");
                                                if (CheckLogoForComponent(infraObjectDetailsTable1, item.Value))
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide/logo/red"), infraObjectDRServerDetailsTable_AllInfra);
                                                else
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide"), infraObjectDRServerDetailsTable_AllInfra);
                                                break;

                                            case 2:
                                                buildNodeRelSb.Append(";");
                                                if (CheckLogoForComponent(infraObjectDetailsTable1, item.Value))
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide/logo/red"), infraObjectPRDBDetailsTable_AllInfra);
                                                else
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide"), infraObjectPRDBDetailsTable_AllInfra);
                                                break;

                                            case 5:
                                                buildNodeRelSb.Append(";");
                                                if (CheckLogoForComponent(infraObjectDetailsTable1, item.Value))
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide/logo/red"), infraObjectDRDBDetailsTable_AllInfra);
                                                else
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide"), infraObjectDRDBDetailsTable_AllInfra);
                                                break;

                                            case 3:
                                                buildNodeRelSb.Append(";");
                                                if (CheckLogoForComponent(infraObjectDetailsTable1, item.Value))
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide/logo/red"), infraObjectPRDRReplicationDetailsTable_AllInfra);
                                                else
                                                    ConstructNodeRelation(string.Concat(item.Value, "/Hide"), infraObjectPRDRReplicationDetailsTable_AllInfra);

                                                break;
                                        }
                                    }

                                    PRdatabaseBaseList_AllInfra = null;
                                    DRdatabaseBaseList_AllInfra = null;
                                    PRfilterDataBaseList_AllInfra = null;
                                    DRfilterDataBaseList_AllInfra = null;
                                    InfraObjectDetailsDictionary_AllInfra = null;
                                    InfraComponents = null;
                                }

                                #endregion create Node Relation
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Get Business-IT relationShip tree node relation for APP and DB infra type
        /// </summary>
        /// <param name="infraObject">infraObject</param>
        /// <param name="heatMapCompList">heatMapCompList</param>
        /// <author>Ram Mahajan-25/06/2014</author>
        public static void GetNodeValueForAppDbInfra(InfraObject infraObject, IList<Heatmap> heatMapCompList)
        {
            switch (infraObject.Type)
            {
                #region InfraType App

                case (int)InfraObjectType.Application:

                    if (infraObject.SubType == (int)InfraObjectType.ApplicationNoReplication)
                    {
                        GetNodeValueForApplicationInfra(infraObject, heatMapCompList);
                    }
                    else if (infraObject.DRReady == true)
                    {
                        if (infraObject.Type > 0 && infraObject.SubType > 0)
                        {
                            var recovrytype = FetchRecoveryType(infraObject.SubType);
                            if (recovrytype != null)
                            {
                                foreach (var rectype in recovrytype)
                                {
                                    if (rectype.TypeId == infraObject.SubType && rectype.ID == infraObject.RecoveryType)
                                    {
                                        GetNodeValueForApplicationInfra(infraObject, heatMapCompList);
                                    }
                                }
                            }
                        }
                    }
                    break;

                #endregion InfraType App

                #region InfraType DB

                case (int)InfraObjectType.DB:
                    GetNodeValueForDbInfra(infraObject, heatMapCompList);
                    break;

                #endregion InfraType DB
            }
        }

        /// <summary>
        /// Check whether infraObject component is affected or not affected.
        /// </summary>
        /// <param name="heatMapCompList">heatMapCompList</param>
        /// <param name="id">id</param>
        /// <returns>true/false</returns>
        /// <author>Ram Mahajan-19/06/2014</author>
        public static bool IsInfraObjectComponentAffected(IList<Heatmap> heatMapCompList, int id)
        {
            bool isAffected = false;

            if (heatMapCompList != null && heatMapCompList.Count() > 0)
            {
                foreach (var heatMap in heatMapCompList)
                {
                    if (heatMap.HeatmapType.Equals(HeatmapType.Server.ToString(), StringComparison.OrdinalIgnoreCase) ||
                        heatMap.HeatmapType.Equals(HeatmapType.Database.ToString(), StringComparison.OrdinalIgnoreCase) ||
                        heatMap.HeatmapType.Equals(HeatmapType.Replication.ToString(), StringComparison.OrdinalIgnoreCase))
                    {
                        if (heatMap.EntityId == id)
                        {
                            isAffected = true;
                            break;
                        }
                    }
                }
            }

            return isAffected;
        }

        /// <summary>
        /// Check whether to add "Logo" for node or not
        /// </summary>
        /// <param name="dt">dt</param>
        /// <param name="value">value</param>
        /// <returns>true/false</returns>
        /// <author>Ram Mahajan 25/06/2014</author>
        public static bool CheckLogoForComponent(DataTable dt, string value)
        {
            bool isLogo = false;

            if ((dt.Rows.Count > 0) && (!string.IsNullOrEmpty(value)))
            {
                foreach (DataRow row in dt.Rows)
                {
                    if (row["Name"].ToString().Contains("/logo"))
                    {
                        if (row["Name"].ToString().Contains(value))
                        {
                            isLogo = true;
                            break;
                        }
                    }
                }
            }
            return isLogo;
        }

        /// <summary>
        /// Check whether "Logo" exist for node(infraObject affected) or not
        /// </summary>
        /// <param name="dt">datatable</param>
        /// <returns>true/false</returns>
        /// <author>Ram Mahajan-24/07/2014</author>
        public static bool CheckLogoForComponent(DataTable dt)
        {
            bool isLogo = false;

            if (dt.Rows.Count > 0)
            {
                foreach (DataRow row in dt.Rows)
                {
                    if (row["Name"].ToString().Contains("/logo"))
                    {
                        isLogo = true;
                        break;
                    }
                }
            }
            return isLogo;
        }

        /// <summary>
        /// Gets/fetch recoveryType name for database-storage replication-full db.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>List of RecoveryTypeName</returns>
        /// <author>Ram mahajan 25/06/2014</author>
        public static List<RecoveryTypeName> FetchRecoveryType(int id)
        {
            var baseActionType = new RecoveryTypeName();
            return baseActionType.FetchRecoveryType(id);
        }

        /// <summary>
        /// Gets/fetch recoveryType name for database-storage replication-full db.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>List of RecoveryTypeName</returns>
        /// <author>Ram mahajan 25/06/2014</author>
        public static List<RecoveryTypeName> FetchRecoveryTypeName(int typeId, int id)
        {
            var baseActionType = new RecoveryTypeName();
            return baseActionType.FetchRecoveryTypeName(id, typeId);
        }

        /// <summary>
        /// Formats Strings in specific noderelation format
        /// </summary>
        /// <param name="parentNode">parentNode</param>
        /// <param name="dt">DataTable</param>
        /// <returns>dt</returns>
        /// <author>Ram mahajan 09/10/2014</author>
        public static string ConstructNodeRelation(string parentNode, DataTable dt)
        {
            int count = dt.Rows.Count;
            int i = 0;
            buildNodeRelSb.AppendFormat("{0}:", parentNode);
            foreach (DataRow row in dt.Rows)
            {
                buildNodeRelSb.AppendFormat("{0}", row["Name"].ToString());
                if (i < count - 1)
                    buildNodeRelSb.Append(",");
                i++;
            }
            return buildNodeRelSb.ToString();
        }

        /// <summary>
        /// Creates node relation for business function and returns it.
        /// </summary>
        /// <param name="parentNode">parentNode</param>
        /// <param name="dt">DataTable</param>
        /// <returns>string</returns>
        /// <author>Ram mahajan-09/10/2014</author>
        public static string ConstructNodeRelationForBusinessFunction(string parentNode, DataTable dt)
        {
            //Objects used for configured infraObjects for users
            List<InfraObject> InfraObjectAllFlagList = null;

            bool isBFAffected = false;
            int count = dt.Rows.Count;
            int i = 0;

            buildNodeRelSb.AppendFormat("{0}:", parentNode);

            foreach (DataRow row in dt.Rows)
            {
                isBFAffected = false;

                int affectedInfraCount = 0;
                int affectedHighMediumInfraCount = 0;
                decimal affectedInfraPercentage = 0;

                var infraObjectList = facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(Convert.ToInt16(row["BusinessServiceId"]), Convert.ToInt16(row["Id"]));

                InfraObjectAllFlagList = new List<InfraObject>();

                if (infraObjectList != null && infraObjectList.Count() > 0)
                {
                    //Get infra Object list as per Flag "InfraObjectAllFlag"-added newly on 22/05/2014
                    IList<UserInfraObject> userInfraDetail = new List<UserInfraObject>();

                    var userDetail = facade.GetUserById(_currentLoginUserId);

                    if (userDetail != null)
                    {
                        if (!userDetail.InfraObjectAllFlag)
                        {
                            userInfraDetail = facade.GetUserInfraObjectByUserId(userDetail.Id);

                            if (userInfraDetail != null)
                            {
                                foreach (var infraObject in infraObjectList)
                                {
                                    foreach (var userinfra in userInfraDetail)
                                    {
                                        if (infraObject.Id == userinfra.InfraObjectId)
                                        {
                                            InfraObjectAllFlagList.Add(infraObject);
                                        }
                                    }
                                }
                                if (InfraObjectAllFlagList != null && InfraObjectAllFlagList.Count() > 0)
                                {
                                    foreach (var infraObj in InfraObjectAllFlagList)
                                    {
                                        isBFAffected = IsInfraObjectAffected(Convert.ToInt16(infraObj.BusinessServiceId), Convert.ToInt16(infraObj.Id));
                                        if (isBFAffected)
                                        {
                                            affectedInfraCount = affectedInfraCount + 1;
                                            if (infraObj.Priority == Convert.ToInt32(InfraObjectPriority.High) || infraObj.Priority == Convert.ToInt32(InfraObjectPriority.Medium))
                                                affectedHighMediumInfraCount = affectedHighMediumInfraCount + 1;

                                        }
                                    }
                                    if (affectedInfraCount > 0 && affectedHighMediumInfraCount > 0)
                                    {
                                        affectedInfraPercentage = Math.Round(Convert.ToDecimal((Convert.ToDecimal(affectedHighMediumInfraCount) * 100) / Convert.ToDecimal(InfraObjectAllFlagList.Count())), 2);
                                    }
                                    else
                                    {
                                        affectedInfraPercentage = 0;
                                    }
                                }
                                else
                                {
                                    isBFAffected = false;
                                    affectedInfraPercentage = 0;

                                }
                            }
                            else
                            {
                                isBFAffected = false;
                                affectedInfraPercentage = 0;
                            }
                        }
                        else
                        {
                            foreach (var infraObj in infraObjectList)
                            {
                                isBFAffected = IsInfraObjectAffected(Convert.ToInt16(infraObj.BusinessServiceId), Convert.ToInt16(infraObj.Id));
                                if (isBFAffected)
                                {
                                    affectedInfraCount = affectedInfraCount + 1;
                                    if (infraObj.Priority == InfraObjectPriority.High.ToInteger() || infraObj.Priority == InfraObjectPriority.Medium.ToInteger())
                                        affectedHighMediumInfraCount = affectedHighMediumInfraCount + 1;

                                }
                            }
                            if (affectedInfraCount > 0 && affectedHighMediumInfraCount > 0)
                            {
                                affectedInfraPercentage = Math.Round(Convert.ToDecimal((Convert.ToDecimal(affectedHighMediumInfraCount) * 100) / Convert.ToDecimal(infraObjectList.Count())), 2);
                            }
                            else
                            {
                                affectedInfraPercentage = 0;
                            }
                        }
                    }
                    else
                    {
                        isBFAffected = false;
                        affectedInfraPercentage = 0;
                    }
                }
                else
                {
                    isBFAffected = false;
                    affectedInfraPercentage = 0;
                }
                if (affectedInfraPercentage > 0)
                {
                    if (affectedInfraPercentage > 30)
                    {
                        buildNodeRelSb.AppendFormat("{0}", string.Concat(row["Name"].ToString(), "/red"));
                        BFIsRedOrYellow.Add(string.Concat(row["Name"].ToString(), "/red"));
                    }
                    else if (affectedInfraPercentage < 30)
                    {
                        buildNodeRelSb.AppendFormat("{0}", string.Concat(row["Name"].ToString(), "/yellow"));
                        BFIsRedOrYellow.Add(string.Concat(row["Name"].ToString(), "/yellow"));
                    }
                }
                else
                {
                    buildNodeRelSb.AppendFormat("{0}", row["Name"].ToString());
                    BFIsRedOrYellow.Add(row["Name"].ToString());
                }
                //buildNodeRelSb.AppendFormat("{0}", (affectedInfraPercentage > 0 && affectedInfraPercentage > 30) ? string.Concat(row["Name"].ToString(), "/red") : row["Name"].ToString());

                if (i < count - 1)
                    buildNodeRelSb.Append(",");
                i++;
            }
            return buildNodeRelSb.ToString();
        }

        /// <summary>
        /// Converts IList into Datatable
        /// </summary>
        /// <typeparam name="T">Type</typeparam>
        /// <param name="data">IList Object</param>
        /// <returns>DataTable</returns>
        /// <author>Ram mahajan</author>
        public static DataTable ConvertToDataTable<T>(IList<T> data)
        {
            PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));
            DataTable table = new DataTable();
            foreach (PropertyDescriptor prop in properties)
                table.Columns.Add(prop.Name, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType);
            foreach (T item in data)
            {
                DataRow row = table.NewRow();
                foreach (PropertyDescriptor prop in properties)
                    row[prop.Name] = prop.GetValue(item) ?? DBNull.Value;
                table.Rows.Add(row);
            }
            return table;
        }

        /// <summary>
        /// Clears all global static variables
        /// </summary>
        /// <author>Ram mahajan</author>
        public static void ClearAll()
        {
            businessFunctionList = null;
            infraObjects = null;
            buildNodeRelSb = null;
            businessService = null;
            businessFunctionsTable = null;
            infraObjectTable = null;
            businessFunctionObj = null;
            currentNode = 0;
            first = true;
            firstSplitCount = 0;
            hasChild = false;
            jsonNodeRelSb = null;
            inputString = string.Empty;
            jsonString = string.Empty;
        }

        /// <summary>
        /// Convert input noderelation string into Json String format
        /// </summary>
        /// <param name="inputStr1">inputStr1</param>
        /// <returns>Json as string</returns>
        /// <author>Ram mahajan</author>
        public static string GetJsonString(string inputStr1)
        {
            if (jsonNodeRelSb == null)
                jsonNodeRelSb = new StringBuilder();
            Random randomNumber = new Random();
            string[] firstSplitStr = inputStr1.Split(';');
            string[] secSplitStr = firstSplitStr[currentNode].Split(':');
            string[] thirdSplitStr = secSplitStr[1].Split(',');

            int actualChildCount = thirdSplitStr.Length;
            firstSplitCount = firstSplitStr.Length;

            if (currentNode < firstSplitCount - 1)
            {
                currentNode = currentNode + 1;
            }
            for (int j = 0; j < actualChildCount; j++)
            {
                if (first)
                {
                    jsonNodeRelSb.Append("{");
                    if (secSplitStr[0].Contains("/Hide/logo"))
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                    else if (secSplitStr[0].Contains("/Hide"))
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                    else
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0] + "\"" + ",");

                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    first = false;
                }

                if (HasChild(inputStr1, thirdSplitStr[j]))
                {
                    jsonNodeRelSb.Append("{");
                    if (thirdSplitStr[j].Contains("/Hide/logo/red"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + hide + "\"", "\"" + hide + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePath + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/Hide"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + hide + "\"", "\"" + hide + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/red"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/yellow"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellowStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }

                    GetJsonString(inputStr1);

                    jsonNodeRelSb.Append("]");
                    jsonNodeRelSb.Append("}" + ",");
                }
                else
                {
                    jsonNodeRelSb.Append("{");
                    if (thirdSplitStr[j].Contains("/Hide/logo/red"))
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                    else if (thirdSplitStr[j].Contains("/Hide"))
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                    else if (thirdSplitStr[j].Contains("/red"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/yellow"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellowStr + "\"" + ",");
                    }
                    else
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"", ",");
                    jsonNodeRelSb.Append("}");
                    if (j != actualChildCount - 1)
                        jsonNodeRelSb.Append(",");
                }
            }

            return Convert.ToString(jsonNodeRelSb);
        }

        /// <summary>
        /// Determine whether parent node has any child nodes.
        /// </summary>
        /// <param name="inputString">inputString</param>
        /// <param name="thirdSplitStr1">thirdSplitStr1</param>
        /// <returns>True if haschild- false if not</returns>
        /// <author>Ram mahajan</author>
        public static bool HasChild(string inputString, string thirdSplitStr1)
        {
            hasChild = false;
            string sixthSplitStr = string.Empty;
            string[] fourthSplitStr = inputString.Split(';');
            int fourthSplitCount = fourthSplitStr.Length;
            for (int p = 0; p < fourthSplitCount; p++)
            {
                string[] fifthSplitStr = fourthSplitStr[p].Split(':');
                sixthSplitStr = fifthSplitStr[0];
                if (sixthSplitStr == thirdSplitStr1)
                {
                    hasChild = true;
                    break;
                }
            }
            return hasChild;
        }

        /// <summary>
        /// Webservice used in script to construct tree from Json as input
        /// </summary>
        /// <returns>string</returns>
        /// <author>Ram mahajan</author>
        [WebMethod]
        public static string NodeRelationConverter(string businessid)
        {
            ClearAll();
            inputString = GetNodeValue(Convert.ToInt32(businessid));

            if (!string.IsNullOrEmpty(inputString))
            {
                //jsonString = GetJsonStringForIncidents(inputString);
                jsonString = GetJsonString(inputString);
                jsonString = jsonString + "]" + "}";
                jsonString = jsonString.Replace("],", "]");
            }
            if (!string.IsNullOrEmpty(jsonString))
                return jsonString + '#' + inputString;
            else
                return string.Empty;
        }

        /// <summary>
        /// Webservice used in script to construct tree from Json as input for incidents
        /// </summary>
        /// <returns>string</returns>
        /// <author>Ram mahajan-05/03/2015</author>
        [WebMethod]
        public static string NodeRelationConverterForIncidents(string inputString)
        {
            ClearAll();
            if (!string.IsNullOrEmpty(inputString))
            {
                jsonString = GetJsonStringForIncidents(inputString);
                jsonString = jsonString + "]" + "}";
                jsonString = jsonString.Replace("],", "]");
            }
            if (!string.IsNullOrEmpty(jsonString))
                return jsonString + '#' + inputString;
            else
                return string.Empty;
        }

        /// <summary>
        /// Webservice used in script to construct tree for infraobject from Json as input
        /// </summary>
        /// <returns>string</returns>
        /// <author>Ram mahajan</author>
        [WebMethod]
        public static string NodeRelationConverterForInfraObjects(string infraObjectNodeRelation)
        {
            infraObjectJsonString = string.Empty;
            iObjInputString = string.Empty;
            ClearAll();

            if (!string.IsNullOrEmpty(infraObjectNodeRelation))
            {
                iObjInputString = infraObjectNodeRelation;

                infraObjectJsonString = GetJsonString(iObjInputString);
                infraObjectJsonString = infraObjectJsonString + "]" + "}";
                infraObjectJsonString = infraObjectJsonString.Replace("],", "]");
            }

            if (!string.IsNullOrEmpty(infraObjectJsonString))
                return infraObjectJsonString;
            else
                return string.Empty;
        }

        /// <summary>
        /// Formats Strings in specific noderelation format for infraObjects
        /// </summary>
        /// <param name="parentNode">parentNode</param>
        /// <param name="dt">DataTable</param>
        /// <returns>string</returns>
        /// <author>Ram mahajan</author>
        public static string ConstructNodeRelationForInfraObjects(string parentNode, DataTable dt)
        {
            bool isInfraObjectAffected = false;
            int count = dt.Rows.Count;
            int i = 0, id = 0;
            buildNodeRelSb.AppendFormat("{0}:", parentNode);
            foreach (DataRow row in dt.Rows)
            {
                var infraObjListByName = facade.GetInfraObjectByName(row["Name"].ToString());
                if (infraObjListByName != null)
                {
                    id = infraObjListByName.Id;
                    if (id > 0)
                        isInfraObjectAffected = IsInfraObjectAffected(Convert.ToInt16(row["BusinessServiceId"]), id);
                }

                if (Convert.ToInt16(row["Type"]) == InfraObjectType.DB.ToInteger())
                {
                    if (Convert.ToInt16(row["RecoveryType"]) == ReplicationType.OracleDataGuard.ToInteger() || Convert.ToInt16(row["RecoveryType"]) == ReplicationType.OracleWithDataSync.ToInteger() || Convert.ToInt16(row["RecoveryType"]) == ReplicationType.MySQLNative.ToInteger())
                    {
                        buildNodeRelSb.AppendFormat("{0}", isInfraObjectAffected ? string.Concat(row["Name"].ToString(), "/Hide/logo/red") : string.Concat(row["Name"].ToString(), "/Hide"));
                        if (i < count - 1)
                            buildNodeRelSb.Append(",");
                        i++;
                    }
                    else if (IsRecoveryTypeFound(row))
                    {
                        buildNodeRelSb.AppendFormat("{0}", isInfraObjectAffected ? string.Concat(row["Name"].ToString(), "/Hide/logo/red") : string.Concat(row["Name"].ToString(), "/Hide"));
                        if (i < count - 1)
                            buildNodeRelSb.Append(",");
                        i++;
                    }
                    else
                    {
                        buildNodeRelSb.AppendFormat("{0}", row["Name"].ToString());
                        if (i < count - 1)
                            buildNodeRelSb.Append(",");
                        i++;
                    }
                }
                else
                {
                    buildNodeRelSb.AppendFormat("{0}", isInfraObjectAffected ? string.Concat(row["Name"].ToString(), "/Hide/logo/red") : string.Concat(row["Name"].ToString(), "/Hide"));
                    if (i < count - 1)
                        buildNodeRelSb.Append(",");
                    i++;
                }
            }
            return buildNodeRelSb.ToString();
        }

        /// <summary>
        /// Used to find recoverytype of infraobjects configured.
        /// </summary>
        /// <param name="row">DataRow row</param>
        /// <returns>True/false</returns>
        /// <author>Ram Mahajan-16/06/2014</author>
        public static bool IsRecoveryTypeFound(DataRow row)
        {
            bool isfound = false;
            var recovrytype_fulldb = FetchRecoveryType(Convert.ToInt32(row["SubType"]));
            if (recovrytype_fulldb != null)
            {
                foreach (var recType in recovrytype_fulldb)
                {
                    if (recType.TypeId == Convert.ToInt32(row["SubType"]) && recType.ID == Convert.ToInt16(row["RecoveryType"]))
                    {
                        isfound = true;
                        break;
                    }
                }
            }
            return isfound;
        }

        /// <summary>
        /// Check whether InfraObject is affected or not
        /// </summary>
        /// <param name="bSid">BusinessServiceId</param>
        /// <param name="infraObjId">InfraObjectId</param>
        /// <returns>True/false</returns>
        /// <author>Ram Mahajan-24/07/2014</author>
        public static bool IsInfraObjectAffected(int bSid, int infraObjId)
        {
            int affectedRecordCount = 0;
            bool isAffected = false;
            var heatmapList = facade.GetHeatMapByBusinessServiceIdandInfraObjectId(bSid, infraObjId);
            if (heatmapList != null && heatmapList.Count() > 0)
            {
                affectedRecordCount = heatmapList.Count(a => a.IsAffected == true);
                if (affectedRecordCount > 0)
                    isAffected = true;
            }
            return isAffected;
        }

        /// <summary>
        /// Get Json String for given input incident relation
        /// </summary>
        /// <param name="inputStr1">inputStr1</param>
        /// <returns>Json as string</returns>
        /// <author>Ram Mahajan-05/03/2015</author>
        public static string GetJsonStringForIncidents(string inputStr1)
        {
            //inputStr1 = "Inc1:172.16.128.32/red;172.16.128.32/red:BF1/TA,BF2/PI;BF1/TA:BS2/PI;BF2/PI:BS1/TA,BF1/TA;BF1/TA:BS2/PI";

            //inputStr1 = "Inc1:172.16.128.32/red;172.16.128.32/red:BF1/TI,BF2/PI;BF1/TI:BS2/PI;BF2/PI:BS1/MI,BF1/TI;BF1/TI:BS2/PI";

            // inputStr1 = "INC-2015-10:172.16.128.221(Server)/red;172.16.128.221(Server)/red:BF1/PI,BF1/MI,BF_test/TI;BF1/PI:CRM/MI,BF3/MI;BF3/MI:CRM/MI;BF1/MI:CRM/TI,BF2/TI;BF2/TI:GLOBAL SALES/TI;BF_test/TI:TDS/TI,BF9/PI;BF9/PI:LOGISTIC/MI";
            if (jsonNodeRelSb == null)
                jsonNodeRelSb = new StringBuilder();
            Random randomNumber = new Random();
            string[] firstSplitStr = inputStr1.Split(';');
            string[] secSplitStr = firstSplitStr[currentNode].Split(':');
            string[] thirdSplitStr = secSplitStr[1].Split(',');

            int actualChildCount = thirdSplitStr.Length;
            firstSplitCount = firstSplitStr.Length;

            if (currentNode < firstSplitCount - 1)
            {
                currentNode = currentNode + 1;
            }
            for (int j = 0; j < actualChildCount; j++)
            {
                if (first)
                {
                    jsonNodeRelSb.Append("{");

                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0] + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathInc + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + NoImpact + "\"" + ",");

                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    first = false;
                }

                if (HasChild(inputStr1, thirdSplitStr[j]))
                {
                    jsonNodeRelSb.Append("{");
                    if (thirdSplitStr[j].Contains("/TIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathRedBF + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + TotallyImpacted + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/TIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathRedBS + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + TotallyImpacted + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }

                    else if (thirdSplitStr[j].Contains("/PIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathYellowBF + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellowStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + PartiallyImpacted + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/PIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathYellowBS + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellowStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + PartiallyImpacted + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/MIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathOrangeBF + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + MajorImpacted + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/MIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathOrangeBS + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + MajorImpacted + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/redInfracompo"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePath1 + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + NoImpact + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }
                    else if (thirdSplitStr[j].Contains("/redInfraProcess"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + ProcessimagePath + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + NoImpact + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }


                    else
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + NoImpact + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    }

                    GetJsonStringForIncidents(inputStr1);

                    jsonNodeRelSb.Append("]");
                    jsonNodeRelSb.Append("}" + ",");
                }
                else
                {
                    jsonNodeRelSb.Append("{");

                    if (thirdSplitStr[j].Contains("/TIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathRedBF + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + TotallyImpacted + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/TIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathRedBS + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + TotallyImpacted + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/PIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathYellowBF + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellowStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + PartiallyImpacted + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/PIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathYellowBS + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellowStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + PartiallyImpacted + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/MIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathOrangeBF + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + MajorImpacted + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/MIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePathOrangeBS + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + MajorImpacted + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/redInfracompo"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + imagePath1 + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + NoImpact + "\"" + ",");
                    }

                    else if (thirdSplitStr[j].Contains("/redInfraProcess"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + ProcessimagePath + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + NoImpact + "\"" + ",");
                    }

                    else
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + NoImpact + "\"" + ",");
                    }
                    jsonNodeRelSb.Append("}");
                    if (j != actualChildCount - 1)
                        jsonNodeRelSb.Append(",");
                }
            }

            return Convert.ToString(jsonNodeRelSb);
        }

        #endregion Business-IT relationship tree

        #region Static details section

        /// <summary>
        /// Show business function details on its click
        /// </summary>
        /// <param name="bfId">bfId</param>
        /// <author>Ram Mahajan-03/03/2015</author>
        public void ShowBFDetails(int bfId)
        {
            /* pnlbusinessFunction.Visible = true;
             pnlbusinessService.Visible = false;
             pnlinfraObject.Visible = false;

             var businessFunction = facade.GetBusinessFunctionById(bfId);

             if (businessFunction != null)
             {
                 var businessService = facade.GetBusinessServiceById(businessFunction.BusinessServiceId);
                 //TO DO - Assign to read only labels
                 lblName.Text = !string.IsNullOrEmpty(businessFunction.Name) ? businessFunction.Name : string.Empty;
                 if (businessService != null)
                     lblBSName.Text = !string.IsNullOrEmpty(businessService.Name) ? businessService.Name : string.Empty;

                 lblCriticalityLevel.Text = !string.IsNullOrEmpty(businessFunction.CriticalityLevel) ? businessFunction.CriticalityLevel : string.Empty;

                 lblIsStatic.Text = businessFunction.IsStatic > 0 ? "Yes" : "No";

                 lblConfiguredRPO.Text = !string.IsNullOrEmpty(businessFunction.ConfiguredRPO) ? businessFunction.ConfiguredRPO : string.Empty;

                 lblConfiguredRTO.Text = !string.IsNullOrEmpty(businessFunction.ConfiguredRTO) ? businessFunction.ConfiguredRTO : string.Empty;

                 lblConfiguredMAO.Text = !string.IsNullOrEmpty(businessFunction.ConfiguredMAO) ? businessFunction.ConfiguredMAO : string.Empty;

                 udpBF.Update();
             }*/

        }

        /// <summary>
        /// Show business service details on its click
        /// </summary>
        /// <param name="bsId">bsId</param>
        /// <Author>Ram Mahajan-03/03/2015</Author>
        public void ShowBSDetails(int bsId)
        {
            /* pnlbusinessFunction.Visible = false;
             pnlbusinessService.Visible = true;
             pnlinfraObject.Visible = false;

             var businessService = facade.GetBusinessServiceById(bsId);

             if (businessService != null)
             {
                 var companyInfo = facade.GetCompanyProfileById(businessService.CompanyId);

                 var siteInfo = facade.GetSiteById(businessService.SiteId);

                 //TO DO- Assign to read only labels
                 lblName.Text = !string.IsNullOrEmpty(businessService.Name) ? businessService.Name : string.Empty;
                 if (companyInfo != null)
                     lblCompanyName.Text = !string.IsNullOrEmpty(companyInfo.DisplayName) ? companyInfo.DisplayName : string.Empty;

                 if (siteInfo != null)
                     lblSiteName.Text = !string.IsNullOrEmpty(siteInfo.Name) ? siteInfo.Name : string.Empty;

                 if (businessService.Priority == 1)
                     lblPriority.Text = BusinessServicePriority.Low.ToString();
                 else if (businessService.Priority == 2)
                     lblPriority.Text = BusinessServicePriority.Medium.ToString();
                 else if (businessService.Priority == 3)
                     lblPriority.Text = BusinessServicePriority.High.ToString();


                 udpBF.Update();
             }*/

        }

        /// <summary>
        /// Show infraObject details on its click
        /// </summary>
        /// <param name="IOId">IOId</param>
        /// <author>Ram Mahajan-03/03/2015</author>
        public void ShowIODetails(int ioId)
        {
            /* pnlbusinessService.Visible = false;
             pnlbusinessFunction.Visible = false;
             pnlinfraObject.Visible = true;

             var infraObject = facade.GetInfraObjectById(ioId);
             if (infraObject != null)
             {
                 lblName.Text = infraObject.Name;

                 var bsDetails = facade.GetBusinessServiceById(infraObject.BusinessServiceId);
                 var bfDetails = facade.GetBusinessFunctionById(infraObject.BusinessFunctionId);

                 if (bsDetails != null)
                     lblIOBSName.Text = bsDetails.Name;
                 if (bfDetails != null)
                     lblIOBFName.Text = bfDetails.Name;
                 lblIsDRReady.Text = infraObject.DRReady ? "Yes" : "No";

                 if (infraObject.Type == 1)
                     lblType.Text = InfraObjectType.Application.ToString();
                 else if (infraObject.Type == 2)
                     lblType.Text = InfraObjectType.DB.ToString();
                 else if (infraObject.Type == 3)
                     lblType.Text = InfraObjectType.Virtual.ToString();

                 if (infraObject.SubType > 0)
                     lblSubType.Text = GetSubTypeName(infraObject.SubType, typeof(InfraObjectType));

                 if (infraObject.RecoveryType > 0)
                     lblRecSolType.Text = GetRecoverySolutionTypeName(infraObject.SubType, infraObject.RecoveryType);

                 lblIsNearSite.Text = infraObject.NearGroupId > 0 ? "Yes" : "No";

                 if (infraObject.Priority == 1)
                     lblIOPriority.Text = BusinessServicePriority.Low.ToString();
                 else if (infraObject.Priority == 2)
                     lblIOPriority.Text = BusinessServicePriority.Medium.ToString();
                 else if (infraObject.Priority == 3)
                     lblIOPriority.Text = BusinessServicePriority.High.ToString();

                 if (infraObject.PRServerId > 0)
                 {
                     var prServer = facade.GetServerById(infraObject.PRServerId);
                     if (prServer != null)
                         lblPRServer.Text = prServer.Name;
                 }

                 if (infraObject.DRServerId > 0)
                 {
                     var drServer = facade.GetServerById(infraObject.DRServerId);
                     if (drServer != null)
                         lblDRServer.Text = drServer.Name;
                 }

                 if (infraObject.PRDatabaseId > 0)
                 {
                     var prDatabase = facade.GetDatabaseBaseById(infraObject.PRDatabaseId);
                     if (prDatabase != null)
                         lblPRDatabase.Text = prDatabase.Name;
                 }

                 if (infraObject.DRDatabaseId > 0)
                 {
                     var drDatabase = facade.GetDatabaseBaseById(infraObject.DRDatabaseId);
                     if (drDatabase != null)
                         lblDRDatabase.Text = drDatabase.Name;
                 }

                 if (infraObject.PRReplicationId > 0)
                 {
                     var prRep = facade.GetReplicationBaseById(infraObject.PRReplicationId);
                     if (prRep != null)
                         lblPRRep.Text = prRep.Name;
                 }

                 if (infraObject.DRReplicationId > 0)
                 {
                     var drRep = facade.GetReplicationBaseById(infraObject.DRReplicationId);
                     if (drRep != null)
                         lblDRRep.Text = drRep.Name;
                 }

                 udpBF.Update();
             }*/
        }

        /// <summary>
        /// Get subtype name from type
        /// </summary>
        /// <param name="type">type</param>
        /// <param name="enumType">enumType</param>
        /// <returns>type as string</returns>
        /// <author>Ram Mahajan-03/03/2015</author>
        public string GetSubTypeName(int subType, Type enumType)
        {
            string typeName = string.Empty;

            foreach (var enmSiteLangItem in Enum.GetNames(enumType))
            {
                if ((enmSiteLangItem != null) && (enmSiteLangItem.Length > 0))
                {
                    int value = (int)Enum.Parse(enumType, enmSiteLangItem);
                    if (subType == value)
                    {
                        typeName = EnumHelper.GetDescription((Enum)Enum.Parse(enumType, enmSiteLangItem));
                        break;
                    }
                }

            }
            return typeName;
        }

        /// <summary>
        /// Get recovery type from subtype
        /// </summary>
        /// <param name="subType">subType</param>
        /// <returns>recovery type as string</returns>
        /// <author>Ram Mahajan-03/03/2015</author>
        public string GetRecoverySolutionTypeName(int subTypeId, int recoveryTypeId)
        {
            string recoType = string.Empty;

            var recoverytype = ImpactSummary.FetchRecoveryTypeName(subTypeId, recoveryTypeId);
            if (recoverytype != null)
            {
                foreach (var rtype in recoverytype)
                {
                    if (rtype.ID == recoveryTypeId)
                    {
                        recoType = rtype.RecoveryType;
                        break;
                    }
                }

            }
            return recoType;
        }

        #endregion

        protected void RdTreeListIncidentRecord_NeedDataSource(object sender, TreeListNeedDataSourceEventArgs e)
        {

            table = BuildTableStructure();
            FillDataTable(_IncidentRecord, _IncidentRecordId);
            RdTreeListIncidentRecord.DataSource = table;
            updPnlIncidentList.Update();

            //RdTreeListIncidentRecord.ExpandAllItems();
        }


        protected void lnk7Days_Click(object sender, EventArgs e)
        {
            table = BuildTableStructure();
            FillDataTable("7Days", 0);
            RdTreeListIncidentRecord.DataSource = table;
            tabAllCloseIncident.CssClass = "tab-pane ";
            tab7DaysIncident.CssClass = "tab-pane active";
            tabOpenIncident.CssClass = "tab-pane";
            li7days.Attributes.Add("class", "active");
            liclose.Attributes.Add("class", "");
            liOpen.Attributes.Add("class", "");
            LoadFirstInc("7Days");
            bindTreeView(tvOpenIncident, "7Days");
            updIcidentDetailsTabButton.Update();
            updPnlIncidentList.Update();


        }

        protected void lnkAllclose_Click(object sender, EventArgs e)
        {
            table = BuildTableStructure();
            FillDataTable("Close", 0);
            RdTreeListIncidentRecord.DataSource = table;
            tab7DaysIncident.CssClass = "tab-pane active";
            tabAllCloseIncident.CssClass = "tab-pane";
            tabOpenIncident.CssClass = "tab-pane";
            li7days.Attributes.Add("class", "");
            liclose.Attributes.Add("class", "active");
            liOpen.Attributes.Add("class", "");
            LoadFirstInc("Close");
            bindTreeView(tvAllCloseHierarchy, "Close");
            updIcidentDetailsTabButton.Update();
            updPnlIncidentList.Update();

        }

        protected void lnkOpen_Click(object sender, EventArgs e)
        {
            table = BuildTableStructure();
            FillDataTable("Open", 0);
            RdTreeListIncidentRecord.DataSource = table;
            tabOpenIncident.CssClass = "tab-pane active";
            tab7DaysIncident.CssClass = "tab-pane";
            tabAllCloseIncident.CssClass = "tab-pane";
            li7days.Attributes.Add("class", "");
            liclose.Attributes.Add("class", "");
            liOpen.Attributes.Add("class", "active");
            LoadFirstInc("Open");
            bindTreeView(tvOpenIncident, "Open");
            updIcidentDetailsTabButton.Update();
            updPnlIncidentList.Update();

        }

        /// <summary>
        /// Bind datasource to rad treeList
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        //protected void RdTreeListIncidentRecord_NeedDataSource(object sender, TreeListNeedDataSourceEventArgs e)
        //{

        //    table = BuildTableStructure();
        //    FillDataTable();
        //    RdTreeListIncidentRecord.DataSource = table;
        //    updPnlIncidentList.Update();


        //}



        #endregion








        #endregion methods







    }
}
        #endregion