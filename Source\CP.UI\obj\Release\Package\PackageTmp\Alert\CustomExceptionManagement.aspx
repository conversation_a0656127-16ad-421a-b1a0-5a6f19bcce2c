﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="CustomExceptionManagement.aspx.cs"
    Inherits="CP.UI.AlertTypeView" Title="Continuity Patrol :: Alert - Exception Manager" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>



<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">  
    <script type="text/javascript">       
        function CancelClick() {
            return false;
        }
    </script>
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />

      <div class="innerLR">
        
        <asp:Label ID="lblError" runat="server" ForeColor="Red" Visible="false"></asp:Label>
        <h3>
            <img src="../Images/management-icon.png">
            Exception Management</h3>
       
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <asp:Panel ID="panel1" runat="server">
                    <asp:Button runat="server" ID="HiddenForModal" Style="display: none" />

                 

                    <asp:Panel runat="server" ID="PopAuthenticationFailed" Visible="False">
                        <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                            <ContentTemplate>
                                <div id="Div1">
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 378px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">Authentication Failed</h3>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-12 form-horizontal uniformjs">
                                                            <div class="form-group">
                                                                <label class="col-md-12 control-label ">
                                                                    <asp:Label ID="Label1" runat="server" Text="Duplicate Name Exists" Style="color: red;"></asp:Label>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <div class="col-md-3">
                                                    </div>
                                                    <div class="col-md-9 text-right">
                                                        <asp:Button ID="BtnAuthenticationFail" CssClass="btn btn-primary" runat="server" Width="40%" Text="Ok" OnClick="BtnAuthenticationFailClick" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </asp:Panel>

                    <asp:ListView ID="lvCustom" InsertItemPosition="FirstItem" OnPreRender="LvCustomexceptionPreRender" OnItemCanceling="LvCustomItemCanceling" OnItemDeleting="LvCustomItemDeleting" OnItemEditing="LvCustomItemEditing" OnItemInserting="LvCustomItemInserting" OnItemUpdating="LvCustomItemUpdating" runat="server">
                        <LayoutTemplate>
                            <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed;">
                               
                                <thead>
                                    <tr>
                                        <th>Alert Type </th>
                                        <th>Message</th>
                                        <th>Code </th>
                                        <th style="width: 20%;">Severity </th>
                                        <th style="width: 8%;">Send Mail </th>
                                        <th style="width: 5%;" class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                </tbody>
                            </table>
                        </LayoutTemplate>
                        <ItemTemplate>
                            <tr>

                                <td class="td " style="word-wrap: break-word;">
                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                    <asp:Label ID="lblType" Text='<%# Eval("AlertType") %>' runat="server"> </asp:Label>
                                </td>
                                <td class="td " style="word-wrap: break-word;">
                                    <asp:Label ID="lblMessage" Text='<%# Eval("Message") %>' runat="server"> </asp:Label>
                                </td>
                                <td class="td " style="word-wrap: break-word;">
                                    <asp:Label ID="lblCode" Text='<%# Eval("Code") %>' runat="server"> </asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="lblsevrityIcon" runat="server" CssClass=' <%# SetSeverity(Eval("Severity")) %>'> </asp:Label>
                                    <asp:Label ID="lblSeverity" Text=' <%# Eval("Severity") %>' runat="server"> </asp:Label>
                                </td>

                                <td>
                                    <asp:Label ID="lblSendMail" runat="server" Text='<%# DisplaySendMail(Eval("SendMail")) %>'> </asp:Label>
                                </td>
                                <td style="width: 5%;" class="text-center">
                                    <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit" ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                </td>

                                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                    ConfirmText='<%# "Are you sure want to delete Alert "+ Eval("AlertType") + " ? " %>' OnClientCancel="CancelClick">
                                </TK1:ConfirmButtonExtender>
                            </tr>
                        </ItemTemplate>
                        <EditItemTemplate>
                            <tr>

                                <td>
                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                    <asp:TextBox ID="txtAlertType" Text='<%# Eval("AlertType") %>' runat="server" class="form-control" Width="90%"> </asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvAlertType" runat="server" ErrorMessage="*" ControlToValidate="txtAlertType" ValidationGroup="update"></asp:RequiredFieldValidator>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtMessage" Text='<%# Eval("Message") %>' runat="server" class="form-control" Width="90%"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="efvMessage" runat="server" ErrorMessage="*" ControlToValidate="txtMessage" ValidationGroup="update"></asp:RequiredFieldValidator>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtCode" runat="server" class="form-control" Width="90%" Text='<%# Eval("Code") %>'> </asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvCode" runat="server" ErrorMessage="*" Display="Dynamic" ControlToValidate="txtCode" ValidationGroup="update"></asp:RequiredFieldValidator>
                                </td>
                                <td>
                                    <asp:DropDownList ID="ddlSeverity" runat="server" CssClass="selectpicker col-md-9" data-style="btn-default" Width="90%" Text='<%# Eval("Severity") %>'>
                                        <asp:ListItem Selected="true" Value="None">Choose One</asp:ListItem>
                                        <asp:ListItem Value="Fatal">Fatal</asp:ListItem>
                                        <asp:ListItem Value="Critical">Critical</asp:ListItem>
                                        <asp:ListItem Value="Warning">Warning</asp:ListItem>
                                        <asp:ListItem Value="Advisory">Advisory</asp:ListItem>
                                        <asp:ListItem Value="Clear">Clear</asp:ListItem>
                                        <asp:ListItem Value="Informational">Informational</asp:ListItem>
                                    </asp:DropDownList>
                                </td>

                                <td>
                                    <asp:CheckBox ID="chkSendMail" Checked='<%# Eval("Sendmail") %>' runat="server" />
                                </td>
                                <td>
                                    <asp:ImageButton ID="ImgUpdate" runat="server" CommandName="Update" AlternateText="Update" ValidationGroup="update" ToolTip="Update" ImageUrl="../Images/icons/pencil.png" />
                                    <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel" ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                </td>
                            </tr>
                        </EditItemTemplate>
                        <InsertItemTemplate>
                            <tr>
                                <td>
                                    <asp:TextBox ID="txtAlertType" runat="server" class="form-control" Width="90%" MaxLength="500"> </asp:TextBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ErrorMessage="*" Display="Dynamic" ControlToValidate="txtAlertType" ValidationGroup="insert"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="regexp" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtAlertType" ErrorMessage="Invalid Characters"
                                        ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>
                                    <asp:Label ID="lblDescription" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtMessage" runat="server" class="form-control" Width="90%" MaxLength="500"> </asp:TextBox><%--Text='<%# Eval("Message") %>'--%>
                                    <asp:Label ID="lblMessage" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ErrorMessage="*" Display="Dynamic" ControlToValidate="txtMessage" ValidationGroup="insert"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator2" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtMessage" ErrorMessage="Invalid Characters"
                                        ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtCode" runat="server" class="form-control" Width="90%" MaxLength="100"> </asp:TextBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ErrorMessage="*" Display="Dynamic" ControlToValidate="txtCode" ValidationGroup="insert"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtCode" ErrorMessage="Invalid Characters"
                                        ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>
                                </td>
                                <td>
                                    <asp:DropDownList ID="ddlSeverity" runat="server" CssClass="selectpicker col-md-9" data-style="btn-default" Width="90%">
                                        <asp:ListItem Selected="true" Value="None">Choose One</asp:ListItem>
                                        <asp:ListItem Value="Fatal">Fatal</asp:ListItem>
                                        <asp:ListItem Value="Critical">Critical</asp:ListItem>
                                        <asp:ListItem Value="Warning">Warning</asp:ListItem>
                                        <asp:ListItem Value="Advisory">Advisory</asp:ListItem>
                                        <asp:ListItem Value="Clear">Clear</asp:ListItem>
                                        <asp:ListItem Value="Informational">Informational</asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvSeverity" runat="server"
                                        ControlToValidate="ddlSeverity" Display="Dynamic" ErrorMessage="*" ValidationGroup="insert"
                                        InitialValue="None"></asp:RequiredFieldValidator>
                                    <asp:Label ID="lblSeverity" runat="server" Visible="false" Text="" ForeColor="red"></asp:Label>
                                   
                                </td>
                                <td>
                                    <asp:CheckBox ID="chkSendMail" runat="server" />
                                   
                                </td>
                                <td>
                                    <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" ValidationGroup="insert" AlternateText="Insert"
                                        ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                </td>
                            </tr>
                        </InsertItemTemplate>
                    </asp:ListView>

                    <div class="row">
                        <div class="col-md-6">
                            <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvCustom" PageSize="4">
                                <Fields>
                                    <asp:TemplatePagerField>
                                        <PagerTemplate>

                                            <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                            Results <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                             Out Of <%# Container.TotalRowCount %>
                                            <br />
                                        </PagerTemplate>
                                    </asp:TemplatePagerField>
                                </Fields>
                            </asp:DataPager>
                        </div>
                        <div class="col-md-6 text-right">
                            <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvCustom" PageSize="10">
                                <Fields>
                                    <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false" ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                    <asp:NumericPagerField PreviousPageText="..." NextPageText="..." ButtonCount="10" NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel" NumericButtonCssClass="btn-pagination" />
                                    <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false" ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                </Fields>
                            </asp:DataPager>
                        </div>
                    </div>
                </asp:Panel>
            </div>
        </div>
    </div>
</asp:Content>