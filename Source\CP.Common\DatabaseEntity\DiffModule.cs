﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DiffModule", Namespace = "http://www.ContinuityPlatform.com/types")]
  public class DiffModule : BaseEntity
    {
        #region Properties


        [DataMember]
        public string ProfileType { get; set; }

        [DataMember]
        public string ProfileName { get; set; }

        [DataMember]
        public string Name { get; set; }


        [DataMember]
        public string WindowOptions { get; set; }

        [DataMember]
        public string LinuxOptions { get; set; }


        [DataMember]
        public int InfraObjectId { get; set; }


        #endregion Properties



    }
}
