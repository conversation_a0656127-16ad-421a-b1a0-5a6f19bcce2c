﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="InfraObjectJobList.aspx.cs" Inherits="CP.UI.Admin.InfraObjectJobList" Title="Continuity Patrol :: Group Job List" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="grid_5 grid-15 margin-bottom35">
        <div class="block-content no-padding">
            <div class="block-controls medium-margin height25" style="height: 25px;">
                <h1>Group Jobs List</h1>
                <asp:TextBox ID="txtsearchvalue" placeholder="Group Name" runat="server"></asp:TextBox>
                <asp:Button ID="btnSearch" runat="server" CssClass="buttonblue" Text="Search" OnClick="BtnSearchClick" /><br />
            </div>
            <asp:ListView ID="lvInfraObjectJobs" runat="server"
                OnPreRender="lvInfraObjectJobs_PreRender">
                <LayoutTemplate>
                    <table class="table font no-bottom-margin">
                        <thead>
                            <tr>
                                <th>No.
                                </th>
                                <th>Group Name
                                </th>
                                <th>Job Name
                                </th>
                                <th>Trigger Name
                                </th>
                                <th>Time Interval
                                </th>
                                <th>Next Fire Time
                                </th>
                                <th>Last Fire Time
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                        </tbody>
                    </table>
                </LayoutTemplate>

                <ItemTemplate>
                    <tr>
                        <td class="th table-check-cell sorting_1">
                            <%#Container.DataItemIndex+1 %>
                        </td>
                        <td>
                            <asp:Label ID="lblGroupName" runat="server" Text='<%# GetGroupName(Eval("GroupId")) %>' />
                        </td>
                        <td>
                            <asp:Label ID="lblJobName" runat="server" Text='<%# GetJobName(Eval("JobId")) %>' />
                        </td>
                        <td>
                            <asp:Label ID="lblTriggrName" runat="server" Text='<%# Eval("TriggerName") %>' />
                        </td>
                        <td>
                            <asp:Label ID="lblCronExpression" runat="server" Text='<%# Eval("CronTime") %>' />
                        </td>
                        <td>
                            <asp:Label ID="lblNFT" runat="server" Text='<%# Eval("NextFireTime") %>' />
                        </td>
                        <td>
                            <asp:Label ID="lblLFT" runat="server" Text='<%# Eval("LastFireTime") %>' />
                        </td>
                    </tr>
                </ItemTemplate>

                <EditItemTemplate>
                    <tr>
                        <td></td>

                        <td>
                            <asp:TextBox ID="txtGroupName" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtJobName" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtTriggerName" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtCronExpression" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtLFT" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtNFT" runat="server"></asp:TextBox></td>
                    </tr>
                </EditItemTemplate>
            </asp:ListView>

            <div class="message no-bottom-margin">
                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvInfraObjectJobs">
                    <Fields>
                        <asp:TemplatePagerField>
                            <PagerTemplate>
                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                <br />
                            </PagerTemplate>
                        </asp:TemplatePagerField>
                    </Fields>
                </asp:DataPager>
            </div>
            <div class="block-footer no-margin">
                &nbsp;
                        <div class="float-right margin-right">
                            <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvInfraObjectJobs" PageSize="10">
                                <Fields>
                                    <asp:NextPreviousPagerField ButtonCssClass="buttonblue" ButtonType="Button" ShowFirstPageButton="false"
                                        ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="Prev" />
                                    <asp:NumericPagerField PreviousPageText="..." NextPageText="..." ButtonCount="10"
                                        NextPreviousButtonCssClass="buttonblue" CurrentPageLabelCssClass="buttonblue"
                                        NumericButtonCssClass="button" />
                                    <asp:NextPreviousPagerField ButtonCssClass="buttonblue" ButtonType="Button" ShowFirstPageButton="false"
                                        ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" />
                                </Fields>
                            </asp:DataPager>
                        </div>
            </div>
        </div>
    </div>
</asp:Content>