﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RedHatVirtualizationRepli", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class RedHatVirtualizationReplication : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables


        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string StorageDomainName { get; set; }

        [DataMember]
        public string LoginProfile { get; set; }

        [DataMember]
        public string DataCenterName { get; set; }

        [DataMember]
        public string RHVHostName { get; set; }


        [DataMember]
        public string CreateDate { get; set; }



        [DataMember]
        public string UpdateDate { get; set; }



        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }



        #endregion Properties

    }
}
