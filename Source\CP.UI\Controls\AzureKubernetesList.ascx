﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="AzureKubernetesList.ascx.cs" Inherits="CP.UI.Controls.AzureKubernetesList" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<script type="text/javascript">
    function CancelClick() {
        return false;
    }
    </script>


 <asp:UpdateProgress id="UpdateProgress1" runat="server" associatedupdatepanelid="UpdatePanel_Azure" >
                        <progresstemplate>
                          <div class="loading-mask">
                          <span>Loading...</span>
                          </div>
                        </progresstemplate>
                    </asp:updateprogress>


<asp:UpdatePanel ID="UpdatePanel_Azure" runat="server" UpdateMode="Conditional"> <%--UpdateMode="Conditional"--%>
            <ContentTemplate>
                     <div class="row">
            <div class="col-md-5 col-md-push-7 text-right">
                            <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Replication Name"></asp:TextBox>
                        <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%"  Text="Search" OnClick="btnSearch_Click"
                                 />
                        </div>
                        </div>
                        <hr/>
                <asp:ListView ID="lvAzure" runat="server" OnItemEditing="lvAzure_ItemEditing" OnItemDeleting="lvAzure_ItemDeleting" OnPreRender="lvAzure_PreRender" OnItemDataBound="lvAzure_ItemDataBound" OnSelectedIndexChanged="lvAzure_SelectedIndexChanged">
                    <LayoutTemplate>
                        <table  class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" width="100%">
                            <thead>
                                <tr>
                                <th style="width: 4%;" class="text-center">
                                <span><img src="../Images/icons/replication_list.png"/></span>
                                </th>                                    
                                    <th>
                                    Replication Name
                                    </th>
                                    <th>
                                    Cluster Name
                                    </th>
                                    <th>
                                   Resource Group Name
                                    </th>
                                     <th>
                                  Cluster Location
                                    </th>
                                    <th style="width: 8%;" class="text-center">
                                        Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                               <asp:PlaceHolder runat="server" ID="itemPlaceholder" />                             
                            </tbody>
                        </table>
                    </LayoutTemplate>
                     <EmptyDataTemplate>
                         
                          <div class="message warning align-center bold">
                                <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                            </div>
                     </EmptyDataTemplate>
                    <ItemTemplate>
                        
                            <tr>
                             <td style="width: 4%;" class="text-center"> <asp:Label ID="ID" runat="server" Text='<%# Eval("ReplicationBase.Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                                           </td>                           
                                <td >
                                    <asp:Label ID="Rep_NAME" runat="server" Text='<%# Eval("ReplicationBase.Name") %>' />
                                </td>

                                <td>
                                    <asp:Label ID="lblJobName" runat="server" Text='<%# Eval("AKS_ClusterName") %>' />
                                </td>
                             <td>
                                    <asp:Label ID="lblJobType" runat="server" Text='<%# Eval("AKS_ResourceGroupName") %>' />
                             
                             </td>
                                <td>
                                    <asp:Label ID="Label1" runat="server" Text='<%# Eval("AkS_ClusterLocation") %>' />
                             
                             </td>
                                <td style="width: 8%;" class="text-center">
                                    <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit" ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"  ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                                </td>
                                 <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID ="ImgDelete" ConfirmText= '<%# "Are you sure want to delete " + Eval("ReplicationBase.Name") + " ? " %>' OnClientCancel ="CancelClick">
                           </TK1:ConfirmButtonExtender>
                            </tr>
                        
                    </ItemTemplate>
               
                </asp:ListView>
                 <div class="row">
                       <div class="col-md-6">
                        <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvAzure">
                            <Fields>
                                <asp:TemplatePagerField>
                                    <PagerTemplate>
                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                        Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                        <br />
                                    </PagerTemplate>
                                </asp:TemplatePagerField>
                            </Fields>
                        </asp:DataPager>
                    </div>
           <div class="col-md-6 text-right">
                            <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvAzure" PageSize="10">
                                <Fields>
                                    <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                        ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                    <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10" 
                                        NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                        NumericButtonCssClass="btn-pagination" />
                                    <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                        ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                </Fields>
                            </asp:DataPager>
                        </div>
                    </div>
            </ContentTemplate>
            </asp:UpdatePanel>
