﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ServicesMonitor.aspx.cs" Inherits="CP.UI.Admin.ServicesMonitor" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajax" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/lc_switch.css" rel="stylesheet" />
    <script src="../Script/d3.min.js"></script>
  <%--  <script src="../Script/ServiceTreeDesignProcessMoniter.js"></script>--%>
    <script src="../Script/lc_switch.js"></script>
    <script src="../Script/ServiceMonitor.js" type="text/javascript"></script>
    <style type="text/css">
        .dspnone {
            display: none;
        }

        .activeService {
            border-left: 4px solid #4a8bc2 !important;
        }        
        .bootstrap-select:not([class*="col"]), .cioselect .bootstrap-select:not([class*="col"]) .btn {
            width: 100%;
        }
    </style>

    <div class="innerLR">
        <h3>
            <img src="../Images/ServiceLogo/process-designer_icon.png">
            Services Monitor</h3>
        <!--Form start here -->
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body padding-none">
                <div class="row" style="margin-left: 0px ! important; margin-right: 0px ! important;">
                    <div class="col-md-12 form-horizontal uniformjs padding-none-LR">
                        <div class="col-md-3 padding-none-LR" style="width: 20%">
                            <%-- <div class="cioselect" style="background-color: #4a8bc2; padding: 5px;">
                                <asp:DropDownList ID="ddlServiceProfile" runat="server" CssClass="selectpicker" data-style="btn-default"
                                    OnSelectedIndexChanged="ddlServiceProfile_SelectedIndexChanged" AutoPostBack="true">
                                </asp:DropDownList>
                            </div>--%>

                            <%--  <ul class="profileul">
                                <li>
                                    <a><span class="cio-profile-icon"></span>Prepaid</a>
                                    <span class="profile-totalimapct"></span>
                                </li>
                                <li>
                                    <a><span class="cio-profile-icon"></span>HotLineService</a>
                                    <span class="profile-totalimapct"></span>
                                </li>
                                <li>
                                    <a><span class="cio-profile-icon"></span>Postpaid</a>
                                    <span class="profile-partialimpact"></span>
                                </li>
                                <li>
                                    <a><span class="cio-profile-icon"></span>Regulatory</a>
                                    <span class="profile-partialimpact"></span>
                                </li>
                                <li>
                                    <a><span class="cio-profile-icon"></span>Security</a>
                                    <span class="profile-impact"></span>
                                </li>
                                <li>
                                    <a><span class="cio-profile-icon"></span>Network</a>
                                    <span class="profile-noimpact"></span>
                                </li>
                                <li>
                                    <a><span class="cio-profile-icon"></span>AccountProvisioning</a>
                                    <span class="profile-noimpact"></span>
                                </li>
                                <li>
                                    <a><span class="cio-profile-icon"></span>CRM_New</a>
                                    <span class="profile-noimpact"></span>
                                </li>
                            </ul>--%>

                            <asp:UpdatePanel ID="updmain" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <div class="cioselect" style="background-color: #4a8bc2; padding: 5px;">
                                        <asp:DropDownList ID="ddlServiceProfile" runat="server" CssClass="selectpicker" data-style="btn-default"
                                            OnSelectedIndexChanged="ddlServiceProfile_SelectedIndexChanged" AutoPostBack="true">
                                        </asp:DropDownList>
                                    </div>

                                    <div id="sm-content-div" style="height: 450px;">
                                        <!-- Business Service Repeater -->
                                        <ul class="profileul">
                                            <asp:Repeater runat="server" ID="rptServices" Visible="false" OnItemDataBound="rptServices_ItemDataBound">
                                                <HeaderTemplate>
                                                </HeaderTemplate>
                                                <ItemTemplate>
                                                    <li id="listFocus" runat="server">
                                                        <i class="cio-profile-icon"></i>
                                                        <asp:LinkButton runat="server" ID="lnkbtnBusinessServiceName"
                                                            OnClientClick='<%# string.Format("OnClickService({0},{1},this);", Eval("Id"),Eval("ProfileId")) %>'></asp:LinkButton>
                                                        <asp:Label runat="server" ID="lblbusinessID" Text='<%#Eval("ID")+"_"+Eval("ProfileId")%> ' ToolTip='<%#Eval("ID")+"_"+Eval("ProfileId")%> ' CssClass="dspnone"></asp:Label>
                                                        <span id="Impact" runat="server"></span>
                                                    </li>
                                                </ItemTemplate>
                                                <FooterTemplate>

                                                    <%-- Label used for showing Empty Message --%>
                                                    <asp:Label ID="lblEmptyMsg" runat="server" Visible="false"> </asp:Label>
                                                </FooterTemplate>
                                            </asp:Repeater>
                                        </ul>
                                    </div>
                                </ContentTemplate>

                            </asp:UpdatePanel>

                            <div class="col-md-9" style="width: 80%">
                            </div>
                        </div>

                        <div class="col-md-9 row" style="width: 82.2%">                            
                                    <asp:UpdatePanel ID="upchkAutoRefresh" runat="server" UpdateMode="Conditional">
                                        <ContentTemplate>


                                            <div class="service_tab">
                                                <ul id="tabs" style="float: left;">
                                                    <!-- Tabs go here -->
                                                    <%-- <li><a class="tab";  id="default" href="#">CRM</a>&nbsp;<a href='#' class='remove'>x</a></li>--%>
                                                </ul>
                                                <div class="auto-refresh">
                                                    <asp:CheckBox ID="chkbxAutoRefresh" runat="server" CssClass="lcs_check" autocomplete="off" Enabled="false" AutoPostBack="true" />

                                                    <%--<input type="checkbox" name="check-3" value="6" class="lcs_check lcs_tt1" checked="checked" autocomplete="off" />--%>
                                     &nbsp;Auto Refresh Every&nbsp;
                                                <select id="timeInter" onchange="getval(this);" disabled>
                                                    <option value="60000">1</option>
                                                    <option value="120000">2</option>
                                                    <option value="180000">3</option>
                                                    <option value="240000">4</option>
                                                    <option value="300000">5</option>
                                                </select>
                                                    &nbsp;Min
                                                </div>
                                            </div>

                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                    <!-- Business Service tree-->
                                    <div class="service-diagram" style="height: 450px">
                                        <asp:UpdatePanel ID="udpgraph" runat="server" UpdateMode="Conditional" ViewStateMode="Disabled" ChildrenAsTriggers="false">
                                            <ContentTemplate>
                                                <div style="height: 450px">
                                                    <div id="bsbody3">
                                                    </div>
                                                </div>
                                                <div id="graphLegend" class="col-md-12 text-left graph-legend" style="display: none; background-color: #fff;">
                                                    <img src="../Images/ServiceLogo/legend-service_icon.png" />
                                                    <label class="margin-right">Service</label>
                                                    <img src="../Images/ServiceLogo/legend-application_icon.png" />
                                                    <label class="margin-right">Application</label>
                                                    <img src="../Images/ServiceLogo/legend-infraobject_icon.png" />
                                                    <label class="margin-right">InfraObject</label>
                                                    <img src="../Images/ServiceLogo/profile-totalimpact-icon.png" />
                                                    <label class="margin-right">Total Impact</label>
                                                    <img src="../Images/ServiceLogo/profile-partialimpact-icon.png" />
                                                    <label class="margin-right">Major Impact</label>
                                                    <img src="../Images/ServiceLogo/profile-impact-icon.png" />
                                                    <label class="margin-right">Partial Impact</label>
                                                </div>
                                            </ContentTemplate>
                                        </asp:UpdatePanel>
                                    </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script type="text/javascript">

        $(document).ready(function () {

            $("#sm-content-div").mCustomScrollbar({
                axis: "y",
            });
            $(".service-diagram").mCustomScrollbar({
                axis: "y",
            });
            $('#ctl00_cphBody_chkbxAutoRefresh').lc_switch();
        });

        function pageLoad() {

            $("#sm-content-div").mCustomScrollbar({
                axis: "y",
            });
            $(".service-diagram").mCustomScrollbar({
                axis: "y",
            });
            $('#ctl00_cphBody_chkbxAutoRefresh').lc_switch();
        }
        function ShowTree(profileId, apptype, businessId) {
            //$(".lcs_switch").addClass("lcs_on");
            //$(".lcs_switch").removeClass("lcs_disabled lcs_off");
            $('#ctl00_cphBody_chkbxAutoRefresh').removeAttr('disabled', 'disabled').attr('checked', 'checked');
            renderService(profileId, apptype, businessId);
            $("#graphLegend").css("display", "block");
            $("#timeInter").prop('disabled', false);

        };

    </script>

</asp:Content>
