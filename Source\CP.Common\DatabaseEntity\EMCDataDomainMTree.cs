﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
     public class EMCDataDomainMTree:BaseEntity
    {

        #region Member Variables

        public ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region properties

        [DataMember]
        public int ReplicationId { get; set; }
        //[DataMember]
        //public string DataDomainPRServerIP { get; set; }
        //[DataMember]
        //public string DataDomainPRServerUserNamePassword { get; set; }
        //[DataMember]
        //public string SourceMtreeReplicationpath { get; set; }
        //[DataMember]
        //public string SourceMtreeReplicationFullFQDNPath { get; set; }
        //[DataMember]
        //public string DestinationMtreeReplicationpath { get; set; }
        //[DataMember]
        //public string DestinationMtreeReplicationFullFQDNPath { get; set; }
        [DataMember]
        public int DomainPRServerId { get; set; }

        [DataMember]
        public int DomainDRServerId { get; set; }

        [DataMember]
        public string PRDataDomainServerIP { get; set; }

        [DataMember]
        public string DRDataDomainServerIP { get; set; }

        [DataMember]
        public string PRDataDomainServerUserName { get; set; }

        [DataMember]
        public string DRDataDomainServerUserName { get; set; }

        [DataMember]
        public string PRDataDomainServerPassword { get; set; }

        [DataMember]
        public string DRDataDomainServerPassword { get; set; }

        [DataMember]
        public string PRSourceMtreeReplicationpath { get; set; }

        [DataMember]
        public string DRDestinationMtreeReplicationpath { get; set; }

        //[DataMember]
        //public string DRSourceMtreeReplicationpath { get; set; }

        [DataMember]
        public string PRSourceMtreeReplicationFullFQDNPath { get; set; }

        [DataMember]
        public string DRDestinationMtreeReplicationFullFQDNPath { get; set; }

        //[DataMember]
        //public string DRSourceMtreeReplicationFullFQDNPath { get; set; }

        //[DataMember]
        //public string PRDestinationMtreeReplicationpath { get; set; }


        //[DataMember]
        //public string PRDestinationMtreeReplicationFullFQDNPath { get; set; }



        [DataMember]
        public string PRMTreeName { get; set; }

        [DataMember]
        public string DRMTreeName { get; set; }


        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }


        #endregion properties

    }
}
