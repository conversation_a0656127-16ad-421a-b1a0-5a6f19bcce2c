﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "MonitorServices", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class MonitorServices : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BusinessFunctionId { get; set; }

        [DataMember]
        public int InfraobjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string ServicePath { get; set; }

        [DataMember]
        public int Type { get; set; }

        [DataMember]
        public int WorkflowId { get; set; }

        [DataMember]
        public int WORKFLOWTYPE { get; set; }

        #endregion Properties
    }
}