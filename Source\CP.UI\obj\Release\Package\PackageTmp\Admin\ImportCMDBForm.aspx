﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ImportCMDBForm.aspx.cs" Inherits="CP.UI.Admin.ImportCMDBForm" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <style type="text/css">
        
        #ctl00_cphBody_ddlimpcmdbformat_chosen {
            width: 48% !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>Import CMDB</h3>
     
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-xs-12 form-horizontal uniformjs">
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Import CMDB Format <span class="inactive">*</span></label>

                            <div class="col-xs-9">
                                <asp:DropDownList ID="ddlimpcmdbformat" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default">
                                    <asp:ListItem Value="0">Select CMDB Format</asp:ListItem>
                                    <asp:ListItem Value="1">BMC Atrium</asp:ListItem>
                                    <asp:ListItem Value="2">HP UCMDB</asp:ListItem>
                                    <asp:ListItem Value="3">ManageEngine</asp:ListItem>
                                    <asp:ListItem Value="4">CA CMDB</asp:ListItem>
                                    <asp:ListItem Value="5">IBM CCMDB</asp:ListItem>
                                    <asp:ListItem Value="6">One CMDB</asp:ListItem>
                                    <asp:ListItem Value="7">Rapid OSS</asp:ListItem>
                                    <asp:ListItem Value="7">ECPB</asp:ListItem>
                                </asp:DropDownList>
                                  <asp:RequiredFieldValidator ID="rfvddlimport" runat="server" CssClass="error" InitialValue="0" ControlToValidate="ddlimpcmdbformat" ErrorMessage="Please Select CMDB Format">
                                </asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Host Name <span class="inactive">*</span></label>

                            <div class="col-xs-9">
                                <asp:TextBox ID="TextBox3" runat="server" class="form-control" />
                                <asp:RequiredFieldValidator ID="rfvhostname" runat="server" CssClass="error" ControlToValidate="TextBox3" ErrorMessage="Please Enter Host Name">
                                </asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                User Name <span class="inactive">*</span></label>

                            <div class="col-xs-9">
                                <asp:TextBox ID="txtUserName" runat="server" class="form-control" />
                                <asp:RequiredFieldValidator ID="rfvusername" runat="server" CssClass="error" ControlToValidate="txtUserName" ErrorMessage="Please Enter User Name">
                                </asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Password <span class="inactive">*</span></label>

                            <div class="col-xs-9">
                                <asp:TextBox ID="txtPassword" autocomplete="off" TextMode="Password" runat="server" class="form-control" />
                                 <asp:RequiredFieldValidator ID="rfvpassword" runat="server" CssClass="error" ControlToValidate="txtPassword" ErrorMessage="Please Enter Password">
                                </asp:RequiredFieldValidator>
                            </div>
                        </div>



                        <hr class="separator" />

                        <div class="form-actions row">
                            <div class="col-lg-5">
                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                            Fields</span>
                                <asp:Label ID="lblupdatestatus" runat="server" Text=""></asp:Label>
                            </div>
                            <div class="col-lg-7">
                                <asp:Button ID="btnSaveProfile" CssClass="btn btn-primary" Width="15%" runat="server" Style="margin-left:10px"
                                    Text="Import" TabIndex="11"></asp:Button>
                                <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server"
                                    Text="Cancel" TabIndex="12" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">

        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
</asp:Content>
