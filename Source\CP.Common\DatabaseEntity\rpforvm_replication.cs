﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{

    [Serializable]
    [DataContract(Name = "rpforvm_replication", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class rpforvm_replication : BaseEntity
    {
        #region Member Variable

        private ReplicationBase _replicationBase = new ReplicationBase();

        #endregion Member Variable
        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string ConsistencyGroupId { get; set; }

        [DataMember]
        public string ConsistencyGroupName { get; set; }

        [DataMember]
        public string PRClusterIP { get; set; }

        [DataMember]
        public string PRClusterName { get; set; }

        [DataMember]
        public string PRClusterUser { get; set; }

        [DataMember]
        public string PRClusterPassword { get; set; }

        [DataMember]
        public string PRClusterId { get; set; }

        [DataMember]
        public string PRClusterPortNo { get; set; }

        [DataMember]
        public string PRReplicaCopyName { get; set; }

        [DataMember]
        public string PRAuthCode { get; set; }

        [DataMember]
        public string DRClusterIP { get; set; }

        [DataMember]
        public string DRClusterName { get; set; }

        [DataMember]
        public string DRClusterUser { get; set; }

        [DataMember]
        public string DRClusterPassword { get; set; }

        [DataMember]
        public string DRClusterId { get; set; }

        [DataMember]
        public string DRClusterPortNo { get; set; }

        [DataMember]
        public string DRReplicaCopyName { get; set; }

        [DataMember]
        public string DRAuthCode { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }
        }

        #endregion Properties
    }
}
