﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="MonitoringQueue.aspx.cs" Inherits="CP.UI.Admin.MonitoringQueue" Title="Continuity Patrol :: Manage Monitoring Queue" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <!--breadcrumb -->
                <%-- <ul class="breadcrumb show">
                    <li>You are here</li>
                    <li><a href="#" class="glyphicons settings"><i></i>Manage</a></li>
                    <li class="divider"></li>
                    <li>Manage Monitoring Services</li>
                </ul>--%>
                <!--end here-->

                <h3><span class="business-setting-icon vertical-sub"></span>Manage Monitoring Services</h3>
                <!--Form start here -->
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Business Service <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="DdlApplication" runat="server" AutoPostBack="True" OnSelectedIndexChanged="DdlApplication_SelectedIndexChanged"
                                            CssClass="selectpicker col-md-6" data-style="btn-default">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator1" ControlToValidate="DdlApplication" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        InfraObject <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="DdlGroup" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" AutoPostBack="True" Enabled="false"
                                            OnSelectedIndexChanged="DdlGroup_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator2" ControlToValidate="DdlGroup" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Server <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="DdlServer" runat="server" Enabled="false" CssClass="selectpicker col-md-6" data-style="btn-default" AutoPostBack="True"
                                            OnSelectedIndexChanged="DdlServer_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator3" ControlToValidate="DdlServer" ValidationGroup="AddGroup" InitialValue="0" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Queue Name <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtQueueName" runat="server" Enabled="false" CssClass="form-control"></asp:TextBox>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator4" ControlToValidate="txtQueueName" ValidationGroup="AddGroup" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Thesehold<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtThesehold" runat="server" Enabled="false" CssClass="form-control"></asp:TextBox>
                                        <asp:RequiredFieldValidator CssClass="error" ID="RequiredFieldValidator5" ControlToValidate="txtThesehold" ValidationGroup="AddGroup" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-6">
                                    </div>
                                    <div class="col-md-6">
                                        <asp:Button ID="btnAdd" runat="server" Text="Save" Enabled="false"
                                            CssClass="btn btn-primary" Width="24%" ValidationGroup="AddGroup" OnClick="btnAdd_Click" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <asp:Panel ID="panelListView" CssClass="row" runat="server">
                            <asp:UpdatePanel ID="Updatepanel1" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>

                                    <div class="col-md-12 form-horizontal uniformjs">
                                        <asp:ListView ID="lvGroup" runat="server" OnItemDeleting="lvGroup_ItemDeleting"
                                            OnItemEditing="lvGroup_ItemEditing">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed margin-bottom-none" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th style="width: 5%; text-align: center">S.No
                                                            </th>
                                                            <th style="width: 20%">Business Service
                                                            </th>
                                                            <th style="width: 20%">InfraObject Name
                                                            </th>
                                                            <th style="width: 20%;">Server
                                                            </th>
                                                            <th style="width: 20%;">Queue Name
                                                            </th>
                                                            <th style="width: 10%;">Threshold
                                                            </th>
                                                            <th style="width: 5%; text-align: center">Action
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                </table>
                                                <div class="slim-scroll chat-items" data-scroll-height="190px" data-scroll-size="0">
                                                    <table class="table table-striped table-bordered table-condensed" width="100%"
                                                        id="lvGrouptbl">
                                                        <tbody>
                                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="th table-check-cell" style="width: 5%; text-align: center">
                                                        <asp:Label ID="lblId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                        <%#Container.DataItemIndex+1%>
                                                    </td>
                                                    <td style="width: 20%;">
                                                        <div class="tdword-wrap">
                                                            <asp:Label ID="lblapplicationId" runat="server" Text='<%#GetApplicationName(Eval("InfraobjectId")) %>' />
                                                        </div>
                                                    </td>
                                                    <td style="width: 20%">
                                                        <div class="tdword-wrap">

                                                            <asp:Label ID="lbGroupId" Visible="true" runat="server" Text='<%#GetGroupName(Eval("InfraobjectId")) %>' />
                                                        </div>
                                                    </td>
                                                    <td style="width: 20%;">
                                                        <div class="tdword-wrap">
                                                            <asp:Label ID="lblServerId" runat="server" Text='<%#GetServerName( Eval("ServerId")) %>' />
                                                        </div>
                                                    </td>

                                                    <td style="width: 20%;">
                                                        <div class="tdword-wrap">
                                                            <asp:Label ID="lblQueueName" runat="server" Text='<%# Eval("QueueName") %>' />
                                                        </div>
                                                    </td>
                                                    <td style="width: 10%;">
                                                        <div class="tdword-wrap">
                                                            <asp:Label ID="lblThreshold" runat="server" Text='<%# Eval("Threshold") %>' />
                                                        </div>
                                                    </td>
                                                    <td style="width: 5%; text-align: center">
                                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                            ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                                        <asp:ImageButton ID="ImgDelete" runat="server" Visible="true" Enabled="true"
                                                            CommandName="Delete" AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                        <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                            ConfirmText='<%# "Are you sure you want to delete server " + GetServerName( Eval("ServerId")) +  "? " %>'
                                                            OnClientCancel="CancelClick">
                                                        </TK1:ConfirmButtonExtender>
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <tr>
                                                    <td>
                                                        <div class="message warning align-center bold no-margin no-bottom-margin">
                                                            <asp:Label ID="pageResult" runat="server" Text="No Records"></asp:Label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>

                        </asp:Panel>
                        <div class="message no-margin no-bottom-margin">
                            <asp:Label ID="lblError" runat="server" ForeColor="Red"></asp:Label>
                            <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
