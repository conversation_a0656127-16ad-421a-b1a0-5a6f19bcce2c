﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseSql", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DatabaseSql : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string DatabaseSID { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public string Password { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public SqlAuthenticateType AuthenticationMode { get; set; }

        [DataMember]
        public string DataFilePath { get; set; }

        [DataMember]
        public string TransLogPath { get; set; }

        [DataMember]
        public string UndoFilePath { get; set; }

        [DataMember]
        public string BackupRestorePath { get; set; }

        [DataMember]
        public string NetworkSharedPath { get; set; }


        [DataMember]
        public string InstanceName
        {
            get;
            set;
        }

        #endregion Properties
    }
}