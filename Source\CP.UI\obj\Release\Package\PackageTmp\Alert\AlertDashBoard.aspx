﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AlertDashBoard.aspx.cs" Inherits="CP.UI.AlertDashBoard" MasterPageFile="~/Master/BcmsDefault.Master"
    Title="Continuity Patrol :: Alert-AlertDashBoard" %>

<%@ Register TagPrefix="asp" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script type="text/javascript">
        function checkDate(sender, args) {
            if (sender._selectedDate > new Date()) {
                alert("You cannot select a day greater than today!");
                sender._selectedDate = new Date();

                sender._textbox.set_Value(sender._selectedDate.format(sender._format))
            }

        }
    </script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
    <style>
        .chosen-select + .chosen-container {
            width: 55% !important;
            opacity: 1 !important;
        }
    </style>

</asp:Content>




<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <asp:UpdatePanel ID="upAlert" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

                <asp:Timer ID="Timer1" runat="server" Interval="600000" OnTick="tm_Tick" />

                <h3>
                    <img src="../Images/alerts-icon.png" />&nbsp;Alerts DashBoard</h3>


                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">

                            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <div class="col-md-4">
                                        <div class="form-group">


                                            <label class="col-md-4 padding-none">Select InfraObject <span class="inactive">*</span></label>

                                            <asp:DropDownList ID="ddlGroup" runat="server" CssClass="chosen-select" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlGroup_SelectedIndexChanged">
                                            </asp:DropDownList>
                                            <asp:Label ID="lblddlGroupValidation" runat="server" Text="*" ForeColor="Red" Visible="false"></asp:Label>
                                            <asp:RequiredFieldValidator ID="rfvgroup" runat="server" ErrorMessage="Select InfraObject" InitialValue="0"
                                                ControlToValidate="ddlGroup" ValidationGroup="valalert" ForeColor="Red"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>


                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-3 padding-none">Select Severity</label>
                                            <asp:DropDownList ID="ddlSeverity" runat="server" AutoPostBack="true" CssClass="chosen-select" data-style="btn-default" OnSelectedIndexChanged="ddlSeverity_SelectedIndexChanged">
                                                <asp:ListItem Text="- Select Severity -" Value="0"></asp:ListItem>
                                                <asp:ListItem Text="High" Value="High"></asp:ListItem>
                                                <asp:ListItem Text="Normal" Value="Normal"></asp:ListItem>
                                                <asp:ListItem Text="Information" Value="Information"></asp:ListItem>
                                                <asp:ListItem Text="Critical" Value="Critical"></asp:ListItem>
                                            </asp:DropDownList>
                                             <%--<asp:Label ID="lblddlSeverityValidation" runat="server" Text="*" ForeColor="Red" Visible="false"></asp:Label>--%>
                                            <%--<asp:RequiredFieldValidator ID="rfvSeverity" runat="server" ErrorMessage="Select Severity" InitialValue="0"
                                                ControlToValidate="ddlSeverity" ValidationGroup="valalert" ForeColor="Red"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="col-md-3 padding-none">Select Type</label>
                                            <%--<span class="inactive">*</span>--%>
                                            <%-- <asp:DropDownList ID="ddlType1" runat="server" CssClass="selectpicker col-md-5" data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlType_SelectedIndexChanged1">
                                    </asp:DropDownList>--%>
                                            <asp:DropDownList ID="ddlAlertType" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlAlertType_SelectedIndexChanged" CssClass="chosen-select" data-style="btn-default"></asp:DropDownList>
                                                 <%--<asp:Label ID="lblddlAlertTypeValidation" runat="server" Text="*" ForeColor="Red" Visible="false"></asp:Label>
                                            <asp:RequiredFieldValidator ID="rfvAlertType" runat="server" ErrorMessage="Select Type" InitialValue="0"
                                                ControlToValidate="ddlAlertType" ValidationGroup="valalert" ForeColor="Red"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>
                                    <div class="clearfix"></div>
                                </ContentTemplate>
                            </asp:UpdatePanel>

                            <div class="col-md-5 text-left">
                            </div>
                            <div class="col-md-7 text-right">
                                <span class="input-type-text">
                                    <asp:TextBox ID="txtToDate" runat="server" class="form-control" MaxLength="10" Width="20%" Style="background: none;"></asp:TextBox><img
                                        src="../images/icons/calendar-month.png" width="16" id="imgToDate" style="margin-left: 4px;" />
                                </span>

                                <asp:TextBoxWatermarkExtender ID="txtWatermarkExtender1" runat="server" TargetControlID="txtToDate"
                                    WatermarkText="Start Date" />
                                <asp:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtToDate" OnClientDateSelectionChanged="checkDate"
                                    PopupButtonID="imgToDate" Format="yyyy-MM-dd" />

                                <span class="input-type-text ">
                                    <asp:TextBox ID="txtFromDate" runat="server" class="form-control" MaxLength="10" Width="20%" Style="background: none;"></asp:TextBox><img
                                        src="../images/icons/calendar-month.png" width="16" id="imgFromDate" style="margin-left: 4px;" />
                                </span>

                                <asp:TextBoxWatermarkExtender ID="txtWatermarkExtender2" runat="server" TargetControlID="txtFromDate"
                                    WatermarkText="End Date">
                                </asp:TextBoxWatermarkExtender>
                                <asp:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtFromDate"
                                    CssClass="" PopupButtonID="imgFromDate" Format="yyyy-MM-dd">
                                </asp:CalendarExtender>

                                <span class="input-type-text " style="padding-left: 10px;">
                                    <asp:TextBox ID="txtUserMsg" runat="server" class="form-control" Width="34%" Style="background: none;"></asp:TextBox>
                                    <asp:TextBoxWatermarkExtender ID="txtWatermarkExtender3" runat="server" TargetControlID="txtUserMsg"
                                        WatermarkText="Type or JobName or Description">
                                    </asp:TextBoxWatermarkExtender>

                                    <asp:Button ID="btnDate" runat="server" Text="Search" CssClass="btn btn-primary" Width="16%" ValidationGroup="valalert"
                                        CausesValidation="true" OnClick="BtnDateClick" />
                                </span>
                            </div>
                        </div>
                        <div class="form-action row">
                            <div class="col-lg-3"></div>
                            <div class="col-lg-9 text-right">
                                <div id="ulmessage" class="message error" runat="server" visible="false">
                                    <asp:Label ID="lblDatemsg" runat="server"></asp:Label>
                                    <span class="close-bt"></span>
                                </div>
                            </div>
                        </div>
                        <asp:ListView ID="lvAlert" runat="server" OnPagePropertiesChanging="lvAlert_PagePropertiesChanging">
                            <%-- OnPreRender="lvAlert_PreRender"--%>

                            <LayoutTemplate>
                                <hr />
                                <table id="tblUser" class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed;">
                                    <thead>
                                        <tr>
                                            <th style="width: 4%;" class="text-center">
                                                <span>No.
                                            </th>
                                            <th style="width: 10%;">Type
                                            </th>
                                            <th style="width: 6%;">Severity
                                            </th>
                                            <th style="width: 28%;">Description
                                            </th>
                                            <th style="width: 11%;">IPAddress
                                            </th>
                                            <th style="width: 15%;">Job Name
                                            </th>
                                            <th style="width: 15%;">InfraObject Name
                                            </th>
                                            <th style="width: 11%;">Create Date
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td id="IdTD" runat="server" class="text-center">
                                        <asp:Label ID="Label1" runat="server" Text='<%#Eval("Id") %>' Visible="false"></asp:Label>
                                        <%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td id="TypeTD" runat="server" style="word-wrap: break-word ! important;">
                                        <asp:Label ID="lblType" runat="server" Text='<%# Eval("Type") %>' />
                                    </td>
                                    <td id="severityTD" runat="server" style="word-wrap: break-word ! important;">
                                        <asp:Label ID="imgSeverity" runat="server" Text='<%# GetRowStyle(Eval("Severity")) %>' />
                                        <%--      <center>--%>
                                        <%--<asp:ImageButton ID="imgSeverity" ImageUrl='<%# GetSeverityTypeCss(Eval("Severity")) %>'
                                            ToolTip='<%# GetRowStyle(Eval("Severity")) %>' runat="server" />--%>


                                        <%-- </center>--%>

                                      
                                    </td>
                                    <td id="descriptionTD" runat="server" style="word-wrap: break-word ! important;">

                                        <asp:Label ID="lblDescription" runat="server" Text='<%# Eval("UserDefinedMessage") %>' />
                                    </td>

                                    <td id="IPAddressTD" runat="server" style="word-wrap: break-word ! important;">

                                        <asp:Label ID="lblIPAddress" runat="server" Text='<%# Eval("IPADDRESS") %>' />
                                    </td>

                                    <td id="jobNameTD" runat="server" style="word-wrap: break-word ! important;">
                                        <asp:Label ID="lblJobName" runat="server" Text='<%# Eval("JobName") %>' />
                                    </td>
                                    <td id="GroupNameTd" runat="server" style="word-wrap: break-word; white-space: normal;">
                                        <asp:Label ID="lblGroupName" runat="server" Text='<%# Eval("InfraObjectName") %>' />
                                    </td>
                                    <td id="CreateDateTD" runat="server">
                                        <asp:Label ID="lblCreateDate" runat="server" Text='<%# Eval("CreateDate") %>'></asp:Label>
                                    </td>
                                </tr>
                            </ItemTemplate>

                            <EmptyDataTemplate>
                                <div class="row" align="center">
                                    <asp:Label ID="lblError" Text="No Record found" runat="server" ForeColor="#ff0000" Visible="true"></asp:Label>
                                </div>
                            </EmptyDataTemplate>
                        </asp:ListView>
                        <div class="row" style="padding-bottom: 15px;">
                            <div class="col-md-6" style="width: 20%; padding-right: 0;">

                                <asp:Label ID="lblmsg" runat="server" Text=""></asp:Label>
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvAlert" PageSize="100">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Records
                                        <%# (Container.StartRowIndex +1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-md-6 text-right" style="width: 80%;">
                                <asp:DataPager ID="DataPager1" runat="server" PagedControlID="lvAlert" PageSize="10     ">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                        <asp:NumericPagerField NextPageText="..." PreviousPageText="..." ButtonCount="10"
                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                            NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ShowFirstPageButton="false"
                                            ButtonType="Button" ShowLastPageButton="false" ShowPreviousPageButton="false"
                                            ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>
                        <hr class="separator" />
                        <div class="row">
                            <div class="col-md-6">
                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span class="black">Required Fields</span>
                            </div>
                            <div class="col-md-6 text-right">
                                <asp:Button ID="btnExportPdf" runat="server" CssClass="btn btn-primary" Width="20%" Text="Export to PDF"
                                    ValidationGroup="valalert" CausesValidation="true" OnClick="btn_ExportToPdf_Click" />

                                <asp:Button ID="btnExportExcel" runat="server" CssClass="btn btn-primary" Width="20%" Text="Export to Excel"
                                    ValidationGroup="valalert" CausesValidation="true" OnClick="btn_ExportToExcel_Click" />
                            </div>
                        </div>
                    </div>
                </div>
                </span>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    <asp:UpdateProgress ID="UpdateProgress1" AssociatedUpdatePanelID="upAlert" runat="server">
        <ProgressTemplate>
            <div id="imgLoading" class="loading-mask">
                <span>Loading...</span>
            </div>
        </ProgressTemplate>
    </asp:UpdateProgress>
</asp:Content>
