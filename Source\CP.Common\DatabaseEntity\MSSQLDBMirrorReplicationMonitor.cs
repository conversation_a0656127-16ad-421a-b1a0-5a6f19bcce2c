﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseMirrorMonitor", Namespace = "http://www.BCMS.com/types")]
    public class MSSQLDBMirrorReplicationMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public string PROperMode { get; set; }

        [DataMember]
        public string DROperMode { get; set; }

        [DataMember]
        public string PRRoleofDB { get; set; }

        [DataMember]
        public string DRRoleofDB { get; set; }

        [DataMember]
        public string PRMirroringState { get; set; }

        [DataMember]
        public string DRMirroringState { get; set; }

        [DataMember]
        public int PRLogGenerateRate { get; set; }

        [DataMember]
        public int DRLogGenerateRate { get; set; }

        [DataMember]
        public int PRUnsentLog { get; set; }

        [DataMember]
        public int DRUnsentLog { get; set; }

        [DataMember]
        public int PRLogSentRate { get; set; }

        [DataMember]
        public int DRLogSentRate { get; set; }

        [DataMember]
        public int PRUnrestoredQLog { get; set; }

        [DataMember]
        public int DRUnrestoredQLog { get; set; }

        [DataMember]
        public int PRLogRecoveryRate { get; set; }

        [DataMember]
        public int DRLogRecoveryRate { get; set; }

        [DataMember]
        public int PRTransactionDelay { get; set; }

        [DataMember]
        public int DRTransactionDelay { get; set; }

        [DataMember]
        public int PRTransactionPerSecond { get; set; }

        [DataMember]
        public int DRTransactionPerSecond { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string DataLag { get; set; }

        #endregion Properties
    }
}
