﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class DBSqlNative2008 : System.Web.UI.UserControl
    {
        public string DBName
        {
            get
            {
                return txtDBName.Text;
            }
            set
            {
                txtDBName.Text = value;
            }
        }
        public string UserName
        {
            get
            {
                return txtUserName.Text;
            }
            set
            {
                txtUserName.Text = value;
            }
        }

        public string Password
        {
            get
            {
                return txtPassword.Text;
            }
            set
            {
                txtPassword.Text = value;
            }
        }

        public string Port
        {
            get
            {
                return txtPort.Text;
            }
            set
            {
                txtPort.Text = value;
            }
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            txtDBName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");
           // txtUserName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
           // txtPassword.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            txtPort.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");
            if (rbtnListAuthenticationMode.SelectedIndex == 0)
            {
                dvusername.Visible = false;
                dvpassword.Visible = false;
            }
            else
            {
                dvusername.Visible = true;
                dvpassword.Visible = true;
            }
        }

        protected void rbtnListAuthenticationMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (rbtnListAuthenticationMode.SelectedIndex == 0)
            {
                dvusername.Visible = false;
                dvpassword.Visible = false;
            }
            else
            {
                dvusername.Visible = true;
                dvpassword.Visible = true;
            }
        }


        protected void chkconfig_CheckedChanged(object sender, EventArgs e)
        {
            if (chkconfig.Checked == true)
                divinstance.Visible = true;
            else
                divinstance.Visible = false;


        }

        //protected void chkCluster1_CheckedChanged(object sender, EventArgs e)
        //{
        //    if (chkCluster1.Checked)
        //    {
        //        dvclust.Visible = true;


        //    }
        //    else
        //    {
        //        dvclust.Visible = false;
        //    }
        //}
    }
}