namespace CP.UI.Report
{
    using System;
    using System.ComponentModel;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;
    using BusinessFacade;
    using DataAccess;
    using Common.DatabaseEntity;
    using System.Collections.Generic;
    using System.Collections;
    using System.Linq;
    using System.Collections.Generic;
    using System.Configuration;
    using System.Data;
    using System.Drawing;
    using System.Globalization;
    using System.IO;
    using System.Web;
    using System.Web.UI;
    using System.Web.UI.WebControls;
    using CP.Common.DatabaseEntity;
    using CP.ExceptionHandler;
    using CP.UI.Controls.ReportClients;
    using Gios.Pdf;
    using log4net;
    using SpreadsheetGear;
    using CP.Common.BusinessEntity;


    /// <summary>
    /// Summary description for BusinessServiceSummaryReport.
    /// </summary>
    public partial class BusinessServiceSummaryReport : Telerik.Reporting.Report
    {
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();

        public BusinessServiceSummaryReport()
        {
            //
            // Required for telerik Reporting designer support
            //           
            InitializeComponent();
            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }

        private void ShowTable()
        {
            //CompanyProfile _companyProfile;
            int LoggedInUserId = Convert.ToInt32(this.ReportParameters["LoggedInUserId"].Value);
            bool IsParentCompnay = Convert.ToBoolean(this.ReportParameters["IsParentCompnay"].Value);
            bool IsUserSuperAdmin = Convert.ToBoolean(this.ReportParameters["IsUserSuperAdmin"].Value);
            int LoggedInUserCompanyId = Convert.ToInt32(this.ReportParameters["LoggedInUserCompanyId"].Value);
            string LoggedInUserRole = (this.ReportParameters["LoggedInUserRole"].Value).ToString();
            IList<BusinessService> applicationGroups = new List<BusinessService>();
            var getcurrentuserDetails = Facade.GetUserById(LoggedInUserId);
            //_companyProfile = Facade.GetCompanyProfileById(getcurrentuserDetails.CompanyId);
            var appData = Facade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserId, getcurrentuserDetails.CompanyId, getcurrentuserDetails.Role, IsParentCompnay, getcurrentuserDetails.InfraObjectAllFlag);
            //var groupgdata = Facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(getcurrentuserDetails.CompanyId, IsUserSuperAdmin, IsParentCompnay);
            IList<InfraObject> groupgdata = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, getcurrentuserDetails.Role, IsParentCompnay, getcurrentuserDetails.InfraObjectAllFlag);

            foreach (var app in appData)
            {
                var appl = new BusinessService();
                int groupcount = 0;
                int avilcnt = 0;
                int maintcount = 0;
                appl.Name = app.Name;
                appl.Description = app.Description;
               
                    foreach (var groupse in groupgdata)
                    {
                        if (groupse.BusinessServiceId == app.Id)
                        {

                            groupcount++;

                            if (groupse.State == "Maintenance")
                            {
                                maintcount++;
                            }
                            else
                            {
                                bool isAvaliablity = CheckAvailablity(groupse);

                                if (isAvaliablity)
                                {
                                    avilcnt++;
                                }
                            }

                        }

                    }
                
                if (groupcount != 0)
                {
                    if (avilcnt == 0)
                    {
                        //int down = groupcount - avilcnt;
                        int down = groupcount - (avilcnt + maintcount);
                        appl.Up = avilcnt;
                        appl.Down = down;
                        // appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);
                        //appl.Health = "Critical";
                            appl.Maintenance = maintcount;
                        if (groupcount == maintcount)
                            appl.Health = "NA";
                        else
                            appl.Health = "Critical";
                    }
                    else
                    {
                        //int down = groupcount - avilcnt;
                        int down = groupcount - (avilcnt + maintcount);
                        appl.Up = avilcnt;
                        appl.Down = down;
                        //  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
                        //appl.Health = avilcnt == groupcount ? "Strength" : "Critical";
                        appl.Maintenance = maintcount;

                        if (groupcount == maintcount)
                            appl.Health = "NA";
                        else if (down == 0 && groupcount != maintcount)
                            appl.Health = "Strength";
                        else if (down > 0)
                            appl.Health = "Critical";
                    }
                    appl.Description = appl.Description;
                }
                else
                {
                    appl.Availabilty = "NA";
                    appl.Health = "NA";                    
                    appl.Up = 0;
                    appl.Down = 0;
                    appl.Maintenance = 0;
                }
                applicationGroups.Add(appl);
            }



            var countData = applicationGroups.Count();
            DataTable d1t = new DataTable();
            d1t.Columns.Add("Sr.No.");
            d1t.Columns.Add("BusinessServiceNames");
            d1t.Columns.Add("Description");
            d1t.Columns.Add("Availability");
            d1t.Columns.Add("DRHealth");
            d1t.Columns.Add("Up");
            d1t.Columns.Add("Down");
            d1t.Columns.Add("Maintenance");

            List<string> healthValues = new List<string>();

            int i = 1;

            foreach (var addData in applicationGroups)
            {
                for (int rowCtr = 1; rowCtr <= 1; rowCtr++)
                {
                    DataRow dr = d1t.NewRow();
                    dr["Sr.No."] = i.ToString();
                    dr["BusinessServiceNames"] = addData.Name;
                    dr["Description"] = addData.Description; 
                    dr["Availability"] = addData.Availabilty;
                    dr["DRHealth"] = addData.Health;
                    dr["Up"] = addData.Up;
                    dr["Down"] = addData.Down;
                    dr["Maintenance"] = addData.Maintenance != null ? Convert.ToString(addData.Maintenance) : "0";
                    //healthValues.Add(addData.Health);
                    i++;
                    d1t.Rows.Add(dr);
                }
            }
            this.DataSource = d1t;
        }

        private bool CheckAvailablity(CP.Common.DatabaseEntity.InfraObject groupse)
        {
            if (groupse.RecoveryType == 0)
            {
                return true;
            }
            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(groupse != null ? groupse.BusinessFunctionId : 0);

            BusinessServiceRPOInfo dataLag = Facade.GetCurrentRPOInfraInfobyInfraObjectId(groupse.Id, groupse.BusinessFunctionId);
            if (dataLag != null)
            {
                bool isHealth = Utility.GetReportDatlagHealth(dataLag.CurrentRPO, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                if (dataLag.CurrentRPO.Contains('.'))
                {
                    return false;
                }
                else
                {
                    if (isHealth)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            return false;
        }

        //private bool CheckAvailablity(CP.Common.DatabaseEntity.InfraObject groupse)
        //{
        //    BusinessFunction businessFtn = Facade.GetBusinessFunctionById(groupse != null ? groupse.BusinessFunctionId : 0);

        //    BusinessServiceRPOInfo dataLag = Facade.GetCurrentRPOInfraInfobyInfraObjectId(groupse.Id, groupse.BusinessFunctionId);
        //    if (dataLag != null)
        //    {
        //        bool isHealth = Utility.GetReportDatlagHealth(dataLag.CurrentRPO, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

        //        if (dataLag.CurrentRPO.Contains('.'))
        //        {
        //            return false;
        //        }
        //        else
        //        {
        //            if (isHealth)
        //            {
        //                return true;
        //            }
        //            else
        //            {
        //                return false;
        //            }
        //        }
        //    }
        //    return false;
        //}

        private void BusinessServiceSummaryReport_NeedDataSource(object sender, EventArgs e)
        {
            ShowTable();
        }
    }
}