﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    public class HP3PAR_Monitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public string PRStorageIPAddress { get; set; }

        [DataMember]
        public string PRStorageName { get; set; }

        [DataMember]
        public string PRGroupName { get; set; }

        [DataMember]
        public string PRRole { get; set; }

        [DataMember]
        public string PRReplicationMode { get; set; }

        [DataMember]
        public string PRState { get; set; }

        [DataMember]
        public string PRGroupState { get; set; }

        [DataMember]
        public string PRLastSyncTime { get; set; }

        [DataMember]
        public string DRStorageIPAddress { get; set; }

        [DataMember]
        public string DRStorageName { get; set; }

        [DataMember]
        public string DRGroupName { get; set; }

        [DataMember]
        public string DRRole { get; set; }

        [DataMember]
        public string DRReplicationMode { get; set; }

        [DataMember]
        public string DRState { get; set; }

        [DataMember]
        public string DRGroupState { get; set; }

        [DataMember]
        public string DRLastSyncTime { get; set; }

        [DataMember]
        public string DataLag { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string PRreplicaSetName { get; set; }

        [DataMember]
        public string DRreplicaSetName { get; set; }

        [DataMember]
        public string PRmemberID { get; set; }

        [DataMember]
        public string DRmemberID { get; set; }

        [DataMember]
        public string PRCurrentPriority { get; set; }

        [DataMember]
        public string DRCurrentPriority { get; set; }

        [DataMember]
        public string PRDatalag { get; set; }

        [DataMember]
        public string DRDatalag { get; set; }

        #endregion Properties
    }
}
