﻿using CP.Common.Base;
using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common
{
    [Serializable]
    [DataContract(Name = "zertomonitorstatus", Namespace = "http://www.ContinuityPlatform.com/types")]
   
    public class zertomonitorstatus : BaseEntity
    {

        #region Member Variables

        public ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region properties

        [DataMember]
        public int InfraObjectID { get; set; }

        [DataMember]
        public string PRIPAddress { get; set; }

        [DataMember]
        public string DRIPAddress { get; set; }

        [DataMember]
        public string PRSite { get; set; }

        [DataMember]
        public string DRSite { get; set; }

        [DataMember]
        public string PRSiteName { get; set; }

        [DataMember]
        public string DRSiteName { get; set; }

        [DataMember]
        public string PRVGPName { get; set; }

        [DataMember]
        public string DRVGPName { get; set; }

        [DataMember]
        public string PRSiteType { get; set; }

        [DataMember]
        public string DRSiteType { get; set; }

        [DataMember]
        public string PRProtectionStatus { get; set; }

        [DataMember]
        public string DRProtectionStatus { get; set; }

        [DataMember]
        public string PRVPGState { get; set; }

        [DataMember]
        public string DRVPGState { get; set; }

        [DataMember]
        public string DataLag { get; set; }

      

        #endregion properties
    }
}
