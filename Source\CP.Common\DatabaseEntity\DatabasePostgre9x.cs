﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabasePostgre9x", Namespace = "http://www.BCMS.com/types")]
    public class DatabasePostgre9x : BaseEntity
    {
         #region Properties

        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        [DataMember]
        public string DatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }

        [DataMember]
        public int Port
        {
            get;
            set;
        }

        [DataMember]
        public string DBDataDirectory
        { 
            get; 
            set; 
        }

        [DataMember]
        public string DBbinDirectory
        { 
            get; 
            set; 
        }

        [DataMember]
        public string SULogin
        {
            get;
            set;
        }

        [DataMember]
        public string ServiceName
        {
            get;
            set;
        }

        [DataMember]
        public string IsCluster
        {
            get;
            set;
        }

        [DataMember]
        public string ResGroupMethods
        {
            get;
            set;
        }
        [DataMember]
        public string ResGroupNames
        {
            get;
            set;
        }

        //public string ResGroupNamesInc
        //{
        //    get;
        //    set;
        //}
        [DataMember]
        public string ClusterNodeServerIds
        {
            get;
            set;
        }
        
        #endregion Properties

        #region Constructor

        public DatabasePostgre9x()
            : base()
        {
        }

        #endregion Constructor
    }
}
