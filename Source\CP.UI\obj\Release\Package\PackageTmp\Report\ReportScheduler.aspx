﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ReportScheduler.aspx.cs" Inherits="CP.UI.ReportScheduler" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<%@ Register Assembly="DropDownCheckBoxes" Namespace="Saplin.Controls" TagPrefix="asp" %>

<!DOCTYPE html>
<html class="fluid top-full sticky-top sidebar sidebar-full sticky-sidebar js no-touch ">
<head id="Head1" runat="server">

    <title>Report Scheduler</title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-ui.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/jquery.slimscroll.min.js"></script>
    <style type="text/css">
        .btm-brd {
            border: 1px solid #d2d2d2;
        }

        .width30 .btn-group.bootstrap-select, .width30 .btn-group.bootstrap-select .btn {
            width: 90px;
        }

        .bootstrap-select.col-md-6 {
            padding-right: 0px !important;
            width: 64% !important;
        }
    </style>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <asp:HiddenField ID="hdtokenKey" runat="server" />
        <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

                <TK1:ToolkitScriptManager ID="ToolkitScriptManager1" runat="server"></TK1:ToolkitScriptManager>
                <div class="modal-body" style="height: auto;">
                    <div class="row">
                        <div class="col-xs-12 form-horizontal uniformjs">

                            <div class="form-group">
                                <asp:Label ID="lblDatemsg" runat="server" CssClass="error"></asp:Label>
                                <asp:Label ID="lblAdded" runat="server" ForeColor="#009933" Visible="False"></asp:Label>
                                <label class="col-xs-3 control-label">
                                    Select Report <span class="inactive">*</span>
                                </label>
                                <div class="col-xs-9">
                                    <asp:DropDownList ID="ddlReport" runat="server" AutoPostBack="True"
                                        CssClass="selectpicker col-md-6" data-style="btn-default" OnSelectedIndexChanged="ddlReport_SelectedIndexChanged"
                                        Style="padding-right: 3px !important; text-indent: 0px;">
                                        <asp:ListItem Selected="True" Value="0">- Select Report -</asp:ListItem>
                                        <asp:ListItem Value="1">RPO SLA Report</asp:ListItem>
                                        <asp:ListItem Value="2">Datalag Status Report</asp:ListItem>


                                        <asp:ListItem Value="3">DataSync Monitoring Report</asp:ListItem>
                                        <asp:ListItem Value="4">InfraObject Summary</asp:ListItem>
                                        <asp:ListItem Value="5">DR Readiness Execution Log</asp:ListItem>
                                        <asp:ListItem Value="6">DR Ready Report</asp:ListItem>

                                        <%--  <asp:ListItem Value="19">Weekly Summary Report</asp:ListItem>
                                        <asp:ListItem Value="24">Monthly Summary Report</asp:ListItem>--%>
                                    </asp:DropDownList>
                                    <asp:Label ID="lblReportlist" runat="server" CssClass="error" Text="" Visible="false"></asp:Label>
                                    <%--   <asp:RequiredFieldValidator ID="rfvCategory" runat="server" InitialValue="-0" ErrorMessage="Select Report"
                                        ControlToValidate="ddlReport" ForeColor="Red" ValidationGroup="reportpage" setFocusOnError="true" Display="Dynamic"></asp:RequiredFieldValidator>--%>
                                </div>
                            </div>
                            <asp:Panel runat="server" ID="pnlDatalag">
                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        InfraObject Name<span class="inactive">*</span>
                                    </label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlInfra" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default"
                                            AutoPostBack="true">
                                        </asp:DropDownList>

                                        <asp:RequiredFieldValidator ID="rfvInfra" runat="server" ControlToValidate="ddlInfra" InitialValue="0" ErrorMessage="Select InfraObject Name"
                                            ValidationGroup="reportpage" CssClass="error"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group" visible="false" runat="server">
                                    <label class="col-xs-3 control-label">
                                        Start Date <span class="inactive">*</span>
                                    </label>
                                    <div class="col-xs-9">
                                        <TK1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtstart"
                                            PopupButtonID="imgFromDate" Format="yyyy-MM-dd">
                                        </TK1:CalendarExtender>
                                        <asp:TextBox ID="txtstart" runat="server" CssClass="form-control" Width="66.7%"></asp:TextBox><img
                                            src="../images/icons/calendar-month.png" width="16" id="imgFromDate" style="margin-left: 5px;" />
                                        <asp:RequiredFieldValidator ID="rfvStart" ValidationGroup="reportpage" runat="server" Display="Dynamic" CssClass="error" ErrorMessage="Select From Date" ControlToValidate="txtstart">
                                        </asp:RequiredFieldValidator>

                                    </div>
                                </div>
                                <div class="form-group" visible="false" runat="server">
                                    <label class="col-xs-3 control-label">
                                        End Date <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:TextBox ID="txtend" runat="server" CssClass="form-control" Width="66.7%"></asp:TextBox><img
                                            src="../images/icons/calendar-month.png" width="16" id="img1" style="margin-left: 5px;" />
                                        <TK1:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtend"
                                            PopupButtonID="img1" Format="yyyy-MM-dd">
                                        </TK1:CalendarExtender>
                                        <asp:RequiredFieldValidator ID="rfvEnd" runat="server" ErrorMessage="Select To Date"
                                            CssClass="error" ControlToValidate="txtend" ValidationGroup="reportpage" Display="Dynamic"></asp:RequiredFieldValidator>

                                    </div>
                                </div>

                            </asp:Panel>



                            <asp:Panel runat="server" ID="pnlMonthlyRpt">

                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        Report Type<span class="inactive">*</span>
                                    </label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlType" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlType_SelectedIndexChanged">
                                            <asp:ListItem Text="-Select Report Type-" Selected="True" Value="0"></asp:ListItem>
                                            <asp:ListItem Text="CustomPeriod" Value="1"></asp:ListItem>
                                            <asp:ListItem Text="Monthly" Value="2"></asp:ListItem>
                                        </asp:DropDownList>

                                        <asp:RequiredFieldValidator ID="rfvddlGroup" runat="server" CssClass="error" ControlToValidate="ddlType"
                                            ValidationGroup="reportpage" InitialVale="0" Display="Dynamic" ErrorMessage="Select Report Type" InitialValue="0"></asp:RequiredFieldValidator>

                                        <asp:Label ID="lblvalidation" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
                                    </div>
                                </div>

                                <div id="divstrtdt" runat="server">

                                    <div class="form-group">
                                        <label class="col-xs-3 control-label">
                                            Start Date <span class="inactive">*</span>
                                        </label>
                                        <div class="col-xs-9">
                                            <TK1:CalendarExtender ID="CalendarExtender7" runat="server" TargetControlID="txtMstart"
                                                PopupButtonID="imgFromDate" Format="yyyy-MM-dd">
                                            </TK1:CalendarExtender>
                                            <asp:TextBox ID="txtMstart" runat="server" CssClass="form-control" AutoPostBack="True"></asp:TextBox><img src="../images/icons/calendar-month.png" width="16" id="img6" style="margin-left: 5px;" />
                                            <asp:RequiredFieldValidator ID="rfvSic" ForeColor="Red" runat="server" ErrorMessage="" ControlToValidate="txtMstart" ValidationGroup="reportpage"></asp:RequiredFieldValidator>
                                            <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ForeColor="Red" ErrorMessage="Choose Date"
                                                ControlToValidate="txtMstart" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-xs-3 control-label">
                                            End Date <span class="inactive">*</span>
                                        </label>
                                        <div class="col-xs-9">
                                            <asp:TextBox ID="txtMend" runat="server" CssClass="form-control" AutoPostBack="True"></asp:TextBox><img
                                                src="../images/icons/calendar-month.png" width="16" id="img7" style="margin-left: 5px;" />
                                            <TK1:CalendarExtender ID="CalendarExtender8" runat="server" TargetControlID="txtMend"
                                                PopupButtonID="img1" Format="yyyy-MM-dd">
                                            </TK1:CalendarExtender>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator5" ForeColor="Red" runat="server" ErrorMessage=""
                                                ControlToValidate="txtMend" ValidationGroup="reportpage"></asp:RequiredFieldValidator>
                                            <asp:RegularExpressionValidator ID="RegularExpressionValidator2" ForeColor="Red" runat="server" ErrorMessage="Choose Date"
                                                ControlToValidate="txtMend" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                        </div>
                                    </div>

                                </div>

                                <div id="divMontly" runat="server">
                                    <div class="form-group">
                                        <label class="col-xs-3 control-label">
                                            Month <span class="inactive">*</span>
                                        </label>
                                        <div class="col-xs-9">
                                            <asp:DropDownList ID="ddlMonth" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default"
                                                AutoPostBack="true" OnSelectedIndexChanged="ddlMonth_SelectedIndexChanged">
                                                <asp:ListItem Text="-Select Month-" Selected="True" Value="0"></asp:ListItem>
                                                <asp:ListItem Text="JAN" Value="1"></asp:ListItem>
                                                <asp:ListItem Text="FEB" Value="2"></asp:ListItem>
                                                <asp:ListItem Text="MAR" Value="3"></asp:ListItem>
                                                <asp:ListItem Text="APR" Value="4"></asp:ListItem>
                                                <asp:ListItem Text="MAY" Value="5"></asp:ListItem>
                                                <asp:ListItem Text="JUN" Value="6"></asp:ListItem>
                                                <asp:ListItem Text="JUL" Value="7"></asp:ListItem>
                                                <asp:ListItem Text="AUG" Value="8"></asp:ListItem>
                                                <asp:ListItem Text="SEP" Value="9"></asp:ListItem>
                                                <asp:ListItem Text="OCT" Value="10"></asp:ListItem>
                                                <asp:ListItem Text="NOV" Value="11"></asp:ListItem>
                                                <asp:ListItem Text="DEC" Value="12"></asp:ListItem>
                                            </asp:DropDownList>
                                            <%--<asp:RequiredFieldValidator ID="rfvddlMonth" runat="server" CssClass="error" ControlToValidate="ddlMonth"
              ValidationGroup="reportpage" InitialVale="0" Display="Dynamic" ErrorMessage="Select Month" InitialValue="0"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-xs-3 control-label">
                                            Year <span class="inactive">*</span>
                                        </label>
                                        <div class="col-xs-9">
                                            <asp:DropDownList ID="ddlYear" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default"
                                                AutoPostBack="true" OnSelectedIndexChanged="ddlYear_SelectedIndexChanged">
                                                <asp:ListItem Text="-Select Year-" Selected="True" Value="0"></asp:ListItem>
                                                <asp:ListItem Text="2011" Value="2011"></asp:ListItem>
                                                <asp:ListItem Text="2012" Value="2012"></asp:ListItem>
                                                <asp:ListItem Text="2013" Value="2013"></asp:ListItem>
                                                <asp:ListItem Text="2014" Value="2014"></asp:ListItem>
                                                <asp:ListItem Text="2015" Value="2015"></asp:ListItem>
                                                <asp:ListItem Text="2016" Value="2016"></asp:ListItem>
                                                <asp:ListItem Text="2017" Value="2017"></asp:ListItem>
                                                <asp:ListItem Text="2018" Value="2018"></asp:ListItem>
                                                <asp:ListItem Text="2019" Value="2019"></asp:ListItem>
                                                <asp:ListItem Text="2020" Value="2020"></asp:ListItem>
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="rfvddlYear" runat="server" CssClass="error" ControlToValidate="ddlYear"
                                                ValidationGroup="reportpage" InitialVale="0" Display="Dynamic" ErrorMessage="Select Year" InitialValue="0"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>

                                </div>

                            </asp:Panel>

                            <asp:Panel runat="server" ID="pnlAppName">
                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        InfraObject Name <span class="inactive">*</span></label>


                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlApp" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default" OnSelectedIndexChanged="DdlCompanySelectedIndexChanged"
                                            AutoPostBack="true" TabIndex="5">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvddlApp" runat="server" InitialValue="0" ErrorMessage="Select InfraObject Name"
                                            ControlToValidate="ddlApp" ValidationGroup="reportpage" ForeColor="Red" Display="Dynamic"></asp:RequiredFieldValidator>

                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        Job Name <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlJobs" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default"
                                            AutoPostBack="True" Style="padding-right: 3px !important; text-indent: -4px;">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvJob" runat="server" InitialValue="0" ErrorMessage="Select Job Name"
                                            ControlToValidate="ddlJobs" ValidationGroup="reportpage" Display="Dynamic"></asp:RequiredFieldValidator>

                                    </div>
                                </div>

                                <div class="form-group" id="divtxtStartDSM" runat="server">
                                    <label class="col-xs-3 control-label">
                                        Start Date <span class="inactive">*</span>
                                    </label>
                                    <div class="col-xs-9">
                                        <TK1:CalendarExtender ID="CalendarExtender3" runat="server" TargetControlID="txtStartDSM"
                                            PopupButtonID="img2" Format="yyyy-MM-dd">
                                        </TK1:CalendarExtender>
                                        <asp:TextBox ID="txtStartDSM" runat="server" CssClass="form-control" Width="64.5%"></asp:TextBox><img
                                            src="../images/icons/calendar-month.png" width="16" id="img2" />
                                        <asp:RequiredFieldValidator ID="rfvDSM1" Display="Dynamic" runat="server" ErrorMessage="Select From Date" ControlToValidate="txtStartDSM">
                                        </asp:RequiredFieldValidator>

                                    </div>
                                </div>
                                <div class="form-group" id="divtxtEndDSM" runat="server">
                                    <label class="col-xs-3 control-label">
                                        End Date <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:TextBox ID="txtEndDSM" runat="server" CssClass="form-control" Width="64.5%"></asp:TextBox><img
                                            src="../images/icons/calendar-month.png" width="16" id="img3" />
                                        <TK1:CalendarExtender ID="CalendarExtender4" runat="server" TargetControlID="txtEndDSM"
                                            PopupButtonID="img3" Format="yyyy-MM-dd">
                                        </TK1:CalendarExtender>
                                        <asp:RequiredFieldValidator ID="rfvDSM2" runat="server" ErrorMessage="Select To Date"
                                            ControlToValidate="txtEndDSM"></asp:RequiredFieldValidator>

                                    </div>
                                </div>
                            </asp:Panel>
                            <asp:Panel runat="server" ID="pnlInfrObj">
                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        Infra Object Name <span class="inactive">*</span>
                                    </label>

                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlInfraObj" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default"
                                            AutoPostBack="True" Style="padding-right: 3px !important; text-indent: -4px;">
                                        </asp:DropDownList>

                                        <asp:RequiredFieldValidator ID="rfvddlInfraObj" runat="server" InitialValue="0" ErrorMessage="SelectInfraObject Name"
                                            ValidationGroup="reportpage" ForeColor="Red" ControlToValidate="ddlInfraObj" Display="Dynamic"></asp:RequiredFieldValidator>


                                    </div>
                                </div>
                            </asp:Panel>
                            <asp:Panel runat="server" ID="pnlParllDROpr">
                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        ParallelDR Oper Name</label>

                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlParDrOpr" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default"
                                            AutoPostBack="True" Style="padding-right: 3px !important; text-indent: -4px;">
                                        </asp:DropDownList>


                                    </div>
                                </div>
                            </asp:Panel>
                            <asp:Panel runat="server" ID="pnlBussService">
                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        Business Serice Name</label>


                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlBussServ" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default"
                                            AutoPostBack="True" Style="padding-right: 3px !important; text-indent: 0px;">
                                        </asp:DropDownList>

                                        <%-- <asp:RequiredFieldValidator ID="rfvddlBussServ" runat="server" InitialValue="0" ErrorMessage="Select Anyone Business Serice Name"
                                            ControlToValidate="ddlBussServ" ForeColor="Red" Display="Dynamic" ValidationGroup="reportpage"></asp:RequiredFieldValidator>--%>
                                    </div>
                                </div>
                            </asp:Panel>


                            <asp:Panel runat="server" ID="pnlDrReadness">
                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        Business Service Name</span>
                                    </label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlBussService" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default" AutoPostBack="true"
                                            Style="padding-right: 3px !important; text-indent: -4px;" OnSelectedIndexChanged="ddlBussService_SelectedIndexChanged">
                                        </asp:DropDownList>
                                    </div>
                                </div>

                                <div class="form-group" visible="false" runat="server" id="dvbf">
                                    <label class="col-xs-3 control-label">
                                        Business Function Name</span>
                                    </label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlfn" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default"
                                            Style="padding-right: 3px !important; text-indent: -4px;" AutoPostBack="true" OnSelectedIndexChanged="ddlfn_SelectedIndexChanged">
                                        </asp:DropDownList>
                                    </div>
                                </div>

                                <div class="form-group" visible="false" runat="server" id="dvinfra">
                                    <label class="col-xs-3 control-label">
                                        Infraobject Name </span>
                                    </label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlinfra1" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default" AutoPostBack="true"
                                            Style="padding-right: 3px !important; text-indent: -4px;" OnSelectedIndexChanged="ddlinfra1_SelectedIndexChanged">
                                        </asp:DropDownList>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        Start Date <span class="inactive">*</span>
                                    </label>
                                    <div class="col-xs-9">
                                        <TK1:CalendarExtender ID="CalendarExtender5" runat="server" TargetControlID="txtstart1"
                                            PopupButtonID="imgFromDate1" Format="yyyy-MM-dd">
                                        </TK1:CalendarExtender>
                                        <asp:TextBox ID="txtstart1" runat="server" CssClass="form-control" Width="66.7%"></asp:TextBox><img
                                            src="../images/icons/calendar-month.png" width="16" id="imgFromDate1" style="margin-left: 5px;" />
                                        <asp:RequiredFieldValidator ID="rvfstart1" ValidationGroup="reportpage" runat="server" Display="Dynamic" CssClass="error" ErrorMessage="Select From Date" ControlToValidate="txtstart1">
                                        </asp:RequiredFieldValidator>

                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        End Date <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:TextBox ID="txtend1" runat="server" CssClass="form-control" Width="66.7%"></asp:TextBox><img
                                            src="../images/icons/calendar-month.png" width="16" id="imgToDate1" style="margin-left: 5px;" />
                                        <TK1:CalendarExtender ID="CalendarExtender6" runat="server" TargetControlID="txtend1"
                                            PopupButtonID="imgToDate1" Format="yyyy-MM-dd">
                                        </TK1:CalendarExtender>
                                        <asp:RequiredFieldValidator ID="rfvEnd1" runat="server" ErrorMessage="Select To Date"
                                            CssClass="error" ControlToValidate="txtend1" ValidationGroup="reportpage" Display="Dynamic"></asp:RequiredFieldValidator>

                                    </div>
                                </div>
                            </asp:Panel>


                            <asp:Panel runat="server" ID="pnlDrReady">
                                <div class="form-group">
                                    <label class="col-xs-3 control-label">
                                        Business Service Name</span>
                                    </label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlbusser" runat="server" CssClass="selectpicker col-xs-8" data-style="btn-default" AutoPostBack="true"
                                            Style="padding-right: 3px !important; text-indent: -4px;">
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </asp:Panel>

                            <div class="form-group">
                                <label class="col-xs-3 control-label ">Receivers List</label>
                                <div class="col-xs-9">


                                    <%-- <asp:DropDownCheckBoxes ID="ddlCheckEmailRec" AutoPostBack="true" runat="server" style="text-indent: 5px; height: 28px ! important;" CssClass="col-xs-8"
                                        AddJQueryReference="True" UseButtons="False" UseSelectAllNode="True" OnSelectedIndexChanged="ddlCheckEmailRec_SelectedIndexChanged">

                                        <Texts SelectBoxCaption="- Select Email -" />
                                    </asp:DropDownCheckBoxes>--%>
                                    <asp:Panel ID="Panel8" runat="server" ScrollBars="Vertical" Height="101px" class="padding pull-left"
                                        Width="63.5%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px" TabIndex="7">
                                        <asp:UpdatePanel ID="uplcbsltGroup" runat="server" UpdateMode="Conditional">
                                            <ContentTemplate>
                                                <asp:CheckBoxList ID="cblstGroup" runat="server" AutoPostBack="True" OnSelectedIndexChanged="cblstGroup_SelectedIndexChanged">
                                                </asp:CheckBoxList>
                                            </ContentTemplate>
                                        </asp:UpdatePanel>
                                    </asp:Panel>
                                    <asp:Label ID="lblEmailErr" CssClass="error" runat="server" Visibl="false"></asp:Label>
                                </div>
                            </div>
                            <asp:UpdatePanel ID="upnlTime" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <div class="form-group" runat="server" id="DWM_Div">
                                        <label class="col-xs-3 control-label ">
                                            Time Interval<span class="inactive">*</span>

                                        </label>
                                        <div class="col-xs-9" runat="server" style="padding-top: 10px;">
                                            <asp:Panel ID="PanelInterval" Visible="true" runat="server">
                                                <asp:RadioButtonList ID="RadioButtonList1" runat="server" RepeatDirection="Horizontal"
                                                    CssClass="dropbox text-indent"
                                                    AutoPostBack="True"
                                                    OnSelectedIndexChanged="RadioButtonList1_SelectedIndexChanged" Style="margin-top: -3px;">

                                                    <asp:ListItem>Daily</asp:ListItem>
                                                    <asp:ListItem>Weekly</asp:ListItem>
                                                    <asp:ListItem>Monthly</asp:ListItem>

                                                </asp:RadioButtonList>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="RadioButtonList1"
                                                    Display="Dynamic" CssClass="error" ErrorMessage="Select Interval Time" Font-Size="9pt"></asp:RequiredFieldValidator>
                                                <asp:Label ID="lbltimeintervalErrormessage" runat="server" Visible="false" Text="Select Interval Time"></asp:Label>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="RadioButtonList1" Display="Dynamic"
                                                    CssClass="error" ErrorMessage="Select Time Interval In Either Minutes, Hours or Days"></asp:RequiredFieldValidator>
                                            </asp:Panel>
                                        </div>

                                    </div>


                                    <div id="DWM_Basis" class="form-group" runat="server" visible="false">



                                        <asp:Panel ID="Panel_Daily" runat="server" Visible="false">
                                            <label class="col-xs-3 control-label "></label>

                                            <div class="col-xs-9">
                                                <%--<div class="form-group" style="padding-left: 15px;">
                                                   <asp:Label ID="lblEverydaily" runat="server" Text="Every"></asp:Label>
                                                    <asp:TextBox ID="txteverydaily" runat="server" CssClass="form-control" Style="width: 10%;"></asp:TextBox>
                                                    <asp:Label ID="lbldays" runat="server" Text="Day(s)" CssClass="margin-right"></asp:Label>
                                                    <asp:RegularExpressionValidator ID="revdays" runat="server" CssClass="error" ControlToValidate="txteverydaily" Display="Dynamic"
                                                        ErrorMessage="Please Enter only Numbers" ValidationExpression="\d+"></asp:RegularExpressionValidator>
                                                    <asp:RequiredFieldValidator ID="rfveverydaily" runat="server" ControlToValidate="txteverydaily"
                                                        CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days" ForeColor="Red"></asp:RequiredFieldValidator>

                                                </div>--%>
                                                <div class="form-group width30" style="padding-left: 15px;">
                                                    <asp:Label ID="lblstartTime" runat="server" CssClass="pull-left margin-right margin-top" Text="StartTime "></asp:Label>
                                                    <asp:DropDownList ID="ddlhours" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8" AutoPostBack="True" Enabled="True" Width="15%">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                    </asp:DropDownList>

                                                    <asp:Label ID="Label4" runat="server" CssClass="margin-right" Text="Hours" Style="margin-top: 4px;"></asp:Label>

                                                    <asp:DropDownList ID="ddlminutes" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                                        AutoPostBack="True" Enabled="True" Width="15%">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                        <asp:ListItem>24</asp:ListItem>
                                                        <asp:ListItem>25</asp:ListItem>
                                                        <asp:ListItem>26</asp:ListItem>
                                                        <asp:ListItem>27</asp:ListItem>
                                                        <asp:ListItem>28</asp:ListItem>
                                                        <asp:ListItem>29</asp:ListItem>
                                                        <asp:ListItem>30</asp:ListItem>
                                                        <asp:ListItem>31</asp:ListItem>
                                                        <asp:ListItem>32</asp:ListItem>
                                                        <asp:ListItem>33</asp:ListItem>
                                                        <asp:ListItem>34</asp:ListItem>
                                                        <asp:ListItem>35</asp:ListItem>
                                                        <asp:ListItem>36</asp:ListItem>
                                                        <asp:ListItem>37</asp:ListItem>
                                                        <asp:ListItem>38</asp:ListItem>
                                                        <asp:ListItem>39</asp:ListItem>
                                                        <asp:ListItem>40</asp:ListItem>
                                                        <asp:ListItem>41</asp:ListItem>
                                                        <asp:ListItem>42</asp:ListItem>
                                                        <asp:ListItem>43</asp:ListItem>
                                                        <asp:ListItem>44</asp:ListItem>
                                                        <asp:ListItem>45</asp:ListItem>
                                                        <asp:ListItem>46</asp:ListItem>
                                                        <asp:ListItem>47</asp:ListItem>
                                                        <asp:ListItem>48</asp:ListItem>
                                                        <asp:ListItem>49</asp:ListItem>
                                                        <asp:ListItem>50</asp:ListItem>
                                                        <asp:ListItem>51</asp:ListItem>
                                                        <asp:ListItem>52</asp:ListItem>
                                                        <asp:ListItem>53</asp:ListItem>
                                                        <asp:ListItem>54</asp:ListItem>
                                                        <asp:ListItem>55</asp:ListItem>
                                                        <asp:ListItem>56</asp:ListItem>
                                                        <asp:ListItem>57</asp:ListItem>
                                                        <asp:ListItem>58</asp:ListItem>
                                                        <asp:ListItem>59</asp:ListItem>
                                                    </asp:DropDownList>

                                                    <asp:Label ID="Label5" runat="server" CssClass="margin-right" Text="Minutes" Style="margin-top: 4px;"></asp:Label>
                                                </div>
                                            </div>

                                        </asp:Panel>

                                        <asp:Panel ID="Panel_Wekly" runat="server" Visible="false">
                                            <label class="col-xs-3 control-label "></label>

                                            <div class="col-xs-9">
                                                <div class="form-group width30" style="padding-left: 15px;">
                                                    <asp:Label ID="lblstartWeklyTime" runat="server" CssClass="pull-left margin-right margin-top" Text="StartTime "></asp:Label>
                                                    <asp:DropDownList ID="ddlWeklyhours" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                                        AutoPostBack="True" Enabled="True" Width="15%">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                    </asp:DropDownList>

                                                    <asp:Label ID="Label2" runat="server" CssClass="margin-right" Text="Hours" Style="margin-top: 4px;"></asp:Label>

                                                    <asp:DropDownList ID="ddlWeklyminutes" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                                        AutoPostBack="True" Enabled="True" Width="15%">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                        <asp:ListItem>24</asp:ListItem>
                                                        <asp:ListItem>25</asp:ListItem>
                                                        <asp:ListItem>26</asp:ListItem>
                                                        <asp:ListItem>27</asp:ListItem>
                                                        <asp:ListItem>28</asp:ListItem>
                                                        <asp:ListItem>29</asp:ListItem>
                                                        <asp:ListItem>30</asp:ListItem>
                                                        <asp:ListItem>31</asp:ListItem>
                                                        <asp:ListItem>32</asp:ListItem>
                                                        <asp:ListItem>33</asp:ListItem>
                                                        <asp:ListItem>34</asp:ListItem>
                                                        <asp:ListItem>35</asp:ListItem>
                                                        <asp:ListItem>36</asp:ListItem>
                                                        <asp:ListItem>37</asp:ListItem>
                                                        <asp:ListItem>38</asp:ListItem>
                                                        <asp:ListItem>39</asp:ListItem>
                                                        <asp:ListItem>40</asp:ListItem>
                                                        <asp:ListItem>41</asp:ListItem>
                                                        <asp:ListItem>42</asp:ListItem>
                                                        <asp:ListItem>43</asp:ListItem>
                                                        <asp:ListItem>44</asp:ListItem>
                                                        <asp:ListItem>45</asp:ListItem>
                                                        <asp:ListItem>46</asp:ListItem>
                                                        <asp:ListItem>47</asp:ListItem>
                                                        <asp:ListItem>48</asp:ListItem>
                                                        <asp:ListItem>49</asp:ListItem>
                                                        <asp:ListItem>50</asp:ListItem>
                                                        <asp:ListItem>51</asp:ListItem>
                                                        <asp:ListItem>52</asp:ListItem>
                                                        <asp:ListItem>53</asp:ListItem>
                                                        <asp:ListItem>54</asp:ListItem>
                                                        <asp:ListItem>55</asp:ListItem>
                                                        <asp:ListItem>56</asp:ListItem>
                                                        <asp:ListItem>57</asp:ListItem>
                                                        <asp:ListItem>58</asp:ListItem>
                                                        <asp:ListItem>59</asp:ListItem>
                                                    </asp:DropDownList>

                                                    <asp:Label ID="Label3" runat="server" CssClass="margin-right" Text="Minutes" Style="margin-top: 4px;"></asp:Label>
                                                </div>
                                                <div class="form-group" style="padding-left: 15px;">
                                                    <asp:Label ID="Label1" runat="server" CssClass="pull-left margin-right margin-top" Text="Week Day"></asp:Label>

                                                    <asp:DropDownList ID="ddlDays" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                                        AutoPostBack="True" Enabled="True" Width="25%">
                                                        <asp:ListItem>SUNDAY</asp:ListItem>
                                                        <asp:ListItem>MONDAY</asp:ListItem>
                                                        <asp:ListItem>TUESDAY</asp:ListItem>
                                                        <asp:ListItem>WEDNESDAY</asp:ListItem>
                                                        <asp:ListItem>THURSDAY</asp:ListItem>
                                                        <asp:ListItem>FRIDAY</asp:ListItem>
                                                        <asp:ListItem>SATURDAY</asp:ListItem>
                                                    </asp:DropDownList>
                                                </div>

                                            </div>

                                        </asp:Panel>

                                        <asp:Panel ID="Panel_Monthly" runat="server" Visible="false">
                                            <label class="col-xs-3 control-label "></label>


                                            <div class="col-xs-9 formrel">
                                                <asp:Label ID="Label14" runat="server" CssClass="pull-left margin-right" Text="Month" Style="margin-top: 4px;"></asp:Label>
                                                <asp:DropDownList ID="ddl_month" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                                    AutoPostBack="True" Enabled="True" Style="width: 20%; margin-right: 4px;">
                                                    <asp:ListItem Value="All">All</asp:ListItem>
                                                    <asp:ListItem Value="JAN">January</asp:ListItem>
                                                    <asp:ListItem Value="FEB">February</asp:ListItem>
                                                    <asp:ListItem Value="MAR">March</asp:ListItem>
                                                    <asp:ListItem Value="APR">April</asp:ListItem>
                                                    <asp:ListItem Value="MAY">May</asp:ListItem>
                                                    <asp:ListItem Value="JUN">June</asp:ListItem>
                                                    <asp:ListItem Value="JUL">July</asp:ListItem>
                                                    <asp:ListItem Value="AUG">August</asp:ListItem>
                                                    <asp:ListItem Value="SEP">September</asp:ListItem>
                                                    <asp:ListItem Value="OCT">October</asp:ListItem>
                                                    <asp:ListItem Value="NOV">November</asp:ListItem>
                                                    <asp:ListItem Value="DEC">December</asp:ListItem>


                                                </asp:DropDownList>
                                                <br />
                                                <asp:Panel ID="Panel_dayType" runat="server" CssClass="set_pnl" Style="padding: 15px 0;">
                                                    <asp:RadioButtonList ID="rdbtn_Daytype" runat="server" RepeatDirection="Horizontal"
                                                        CssClass="dropbox text-indent" OnSelectedIndexChanged="rdbtn_Daytype_SelectedIndexChanged"
                                                        AutoPostBack="True">
                                                        <asp:ListItem Value="DOM">Day of Month</asp:ListItem>
                                                        <asp:ListItem Value="WKD">Weekdays</asp:ListItem>

                                                    </asp:RadioButtonList>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="RadioButtonList1"
                                                        Display="Dynamic" CssClass="error" ErrorMessage="Select Interval Time" Font-Size="9pt" ValidationGroup="ValidationTimeInterval"></asp:RequiredFieldValidator>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator6" runat="server" ControlToValidate="RadioButtonList1" Display="Dynamic"
                                                        CssClass="error" ErrorMessage="Select Time Interval In Either Minutes, Hours or Days or Months" ValidationGroup="ValidationTimeInterval"></asp:RequiredFieldValidator>
                                                </asp:Panel>
                                                <asp:Panel ID="pnl_daysofmonth" runat="server" Visible="true" Style="padding: 0px 0 0px 15px;" class="form-group">
                                                    <asp:Label ID="Label15" runat="server" Text="Every" CssClass="col-xs-1" Style="margin-top: 4px; padding: 0px; margin-right: 10px"></asp:Label>
                                                    <asp:TextBox ID="txtday" runat="server" CssClass="form-control col-xs-1" Style="width: 20% !important; margin-left: 9px;"></asp:TextBox>
                                                    <asp:Label ID="Label17" runat="server" Text="Day(s)" CssClass="col-xs-1" Style="margin-top: 4px"></asp:Label>
                                                    <span class="errormsg">
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator4" runat="server" CssClass="error" ControlToValidate="txtday"
                                                            ErrorMessage="Please Enter Valid Day" ValidationExpression="^[1-9]+[0-9]*$" Display="Dynamic" ValidationGroup="ValidationTimeInterval"></asp:RegularExpressionValidator>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator9" runat="server" ControlToValidate="txtday"
                                                            CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days"
                                                            ForeColor="Red" ValidationGroup="ValidationTimeInterval"></asp:RequiredFieldValidator>
                                                </asp:Panel>
                                                <asp:Panel ID="pnl_weekdays" runat="server" Style="max-height: 80px; min-height: 32px"
                                                    Width="66%" CssClass="btm-brd tablescroll" Visible="false">
                                                    <asp:CheckBoxList ID="cbl_weekdays" runat="server" OnSelectedIndexChanged="cbl_weekdays_SelectedIndexChanged" AutoPostBack="true">
                                                        <asp:ListItem Value="All">All</asp:ListItem>
                                                        <asp:ListItem Value="MON">Monday</asp:ListItem>
                                                        <asp:ListItem Value="TUE">Tuesday</asp:ListItem>
                                                        <asp:ListItem Value="WED">Wednesday</asp:ListItem>
                                                        <asp:ListItem Value="THU">Thursday</asp:ListItem>
                                                        <asp:ListItem Value="FRI">Friday</asp:ListItem>
                                                        <asp:ListItem Value="SAT">Saturday</asp:ListItem>
                                                        <asp:ListItem Value="SUN">Sunday</asp:ListItem>

                                                    </asp:CheckBoxList>
                                                </asp:Panel>
                                                <div class="col-md-12 inlinedrop width30" style="padding: 0px; margin-top: 15px; width: 100%; display: block;">
                                                    <asp:Label ID="Label18" runat="server" CssClass="pull-left margin_right10" Text="Start Time" Style="margin-top: 4px; margin-right: 10px;"></asp:Label>
                                                    <asp:DropDownList ID="ddl_Hours1" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                                        AutoPostBack="True" Enabled="True" Style="width: 15%; margin-right: 4px;">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:Label ID="Label19" runat="server" CssClass="margin-right" Text="Hours" Style="margin-top: 4px;"></asp:Label>
                                                    <asp:DropDownList ID="ddl_minutes1" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                                        AutoPostBack="True" Enabled="True" Style="width: 15%; margin-right: 4px;">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                        <asp:ListItem>24</asp:ListItem>
                                                        <asp:ListItem>25</asp:ListItem>
                                                        <asp:ListItem>26</asp:ListItem>
                                                        <asp:ListItem>27</asp:ListItem>
                                                        <asp:ListItem>28</asp:ListItem>
                                                        <asp:ListItem>29</asp:ListItem>
                                                        <asp:ListItem>30</asp:ListItem>
                                                        <asp:ListItem>31</asp:ListItem>
                                                        <asp:ListItem>32</asp:ListItem>
                                                        <asp:ListItem>33</asp:ListItem>
                                                        <asp:ListItem>34</asp:ListItem>
                                                        <asp:ListItem>35</asp:ListItem>
                                                        <asp:ListItem>36</asp:ListItem>
                                                        <asp:ListItem>37</asp:ListItem>
                                                        <asp:ListItem>38</asp:ListItem>
                                                        <asp:ListItem>39</asp:ListItem>
                                                        <asp:ListItem>40</asp:ListItem>
                                                        <asp:ListItem>41</asp:ListItem>
                                                        <asp:ListItem>42</asp:ListItem>
                                                        <asp:ListItem>43</asp:ListItem>
                                                        <asp:ListItem>44</asp:ListItem>
                                                        <asp:ListItem>45</asp:ListItem>
                                                        <asp:ListItem>46</asp:ListItem>
                                                        <asp:ListItem>47</asp:ListItem>
                                                        <asp:ListItem>48</asp:ListItem>
                                                        <asp:ListItem>49</asp:ListItem>
                                                        <asp:ListItem>50</asp:ListItem>
                                                        <asp:ListItem>51</asp:ListItem>
                                                        <asp:ListItem>52</asp:ListItem>
                                                        <asp:ListItem>53</asp:ListItem>
                                                        <asp:ListItem>54</asp:ListItem>
                                                        <asp:ListItem>55</asp:ListItem>
                                                        <asp:ListItem>56</asp:ListItem>
                                                        <asp:ListItem>57</asp:ListItem>
                                                        <asp:ListItem>58</asp:ListItem>
                                                        <asp:ListItem>59</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:Label ID="Label20" runat="server" CssClass="margin-right" Text="Minutes " Style="margin-top: 4px;"></asp:Label>
                                                </div>
                                            </div>

                                        </asp:Panel>


                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>

                            <div class="">

                                <div class="form-group" id="timediv" runat="server" visible="true">
                                    <label class="col-xs-3 control-label ">Select Time <span class="inactive">*</span></label>
                                    <div class="col-xs-9 width30">
                                        <asp:TextBox ID="txtDays" CssClass="form-control" runat="server" Width="15%" Style="margin-top: -3px;"></asp:TextBox>&nbsp;&nbsp;Day(s)
                                     
                                        <asp:DropDownList ID="ddlHrs" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                            AutoPostBack="True" Enabled="True" Width="15%">

                                            <asp:ListItem Value="0">00</asp:ListItem>
                                            <asp:ListItem>01</asp:ListItem>
                                            <asp:ListItem>02</asp:ListItem>
                                            <asp:ListItem>03</asp:ListItem>
                                            <asp:ListItem>04</asp:ListItem>
                                            <asp:ListItem>05</asp:ListItem>
                                            <asp:ListItem>06</asp:ListItem>
                                            <asp:ListItem>07</asp:ListItem>
                                            <asp:ListItem>08</asp:ListItem>
                                            <asp:ListItem>09</asp:ListItem>
                                            <asp:ListItem>10</asp:ListItem>
                                            <asp:ListItem>11</asp:ListItem>
                                            <asp:ListItem>12</asp:ListItem>
                                            <asp:ListItem>13</asp:ListItem>
                                            <asp:ListItem>14</asp:ListItem>
                                            <asp:ListItem>15</asp:ListItem>
                                            <asp:ListItem>16</asp:ListItem>
                                            <asp:ListItem>17</asp:ListItem>
                                            <asp:ListItem>18</asp:ListItem>
                                            <asp:ListItem>19</asp:ListItem>
                                            <asp:ListItem>20</asp:ListItem>
                                            <asp:ListItem>21</asp:ListItem>
                                            <asp:ListItem>22</asp:ListItem>
                                            <asp:ListItem>23</asp:ListItem>
                                        </asp:DropDownList>
                                        Hrs.

                                        <asp:DropDownList ID="ddlMins" runat="server" CssClass="selectpicker" data-style="btn-default" TabIndex="8"
                                            AutoPostBack="True" Enabled="True" Width="15%">

                                            <asp:ListItem Value="0">00</asp:ListItem>
                                            <asp:ListItem>01</asp:ListItem>
                                            <asp:ListItem>02</asp:ListItem>
                                            <asp:ListItem>03</asp:ListItem>
                                            <asp:ListItem>04</asp:ListItem>
                                            <asp:ListItem>05</asp:ListItem>
                                            <asp:ListItem>06</asp:ListItem>
                                            <asp:ListItem>07</asp:ListItem>
                                            <asp:ListItem>08</asp:ListItem>
                                            <asp:ListItem>09</asp:ListItem>
                                            <asp:ListItem>10</asp:ListItem>
                                            <asp:ListItem>11</asp:ListItem>
                                            <asp:ListItem>12</asp:ListItem>
                                            <asp:ListItem>13</asp:ListItem>
                                            <asp:ListItem>14</asp:ListItem>
                                            <asp:ListItem>15</asp:ListItem>
                                            <asp:ListItem>16</asp:ListItem>
                                            <asp:ListItem>17</asp:ListItem>
                                            <asp:ListItem>18</asp:ListItem>
                                            <asp:ListItem>19</asp:ListItem>
                                            <asp:ListItem>20</asp:ListItem>
                                            <asp:ListItem>21</asp:ListItem>
                                            <asp:ListItem>22</asp:ListItem>
                                            <asp:ListItem>23</asp:ListItem>
                                            <asp:ListItem>24</asp:ListItem>
                                            <asp:ListItem>25</asp:ListItem>
                                            <asp:ListItem>26</asp:ListItem>
                                            <asp:ListItem>27</asp:ListItem>
                                            <asp:ListItem>28</asp:ListItem>
                                            <asp:ListItem>29</asp:ListItem>
                                            <asp:ListItem>30</asp:ListItem>
                                            <asp:ListItem>31</asp:ListItem>
                                            <asp:ListItem>32</asp:ListItem>
                                            <asp:ListItem>33</asp:ListItem>
                                            <asp:ListItem>34</asp:ListItem>
                                            <asp:ListItem>35</asp:ListItem>
                                            <asp:ListItem>36</asp:ListItem>
                                            <asp:ListItem>37</asp:ListItem>
                                            <asp:ListItem>38</asp:ListItem>
                                            <asp:ListItem>39</asp:ListItem>
                                            <asp:ListItem>40</asp:ListItem>
                                            <asp:ListItem>41</asp:ListItem>
                                            <asp:ListItem>42</asp:ListItem>
                                            <asp:ListItem>43</asp:ListItem>
                                            <asp:ListItem>44</asp:ListItem>
                                            <asp:ListItem>45</asp:ListItem>
                                            <asp:ListItem>46</asp:ListItem>
                                            <asp:ListItem>47</asp:ListItem>
                                            <asp:ListItem>48</asp:ListItem>
                                            <asp:ListItem>49</asp:ListItem>
                                            <asp:ListItem>50</asp:ListItem>
                                            <asp:ListItem>51</asp:ListItem>
                                            <asp:ListItem>52</asp:ListItem>
                                            <asp:ListItem>53</asp:ListItem>
                                            <asp:ListItem>54</asp:ListItem>
                                            <asp:ListItem>55</asp:ListItem>
                                            <asp:ListItem>56</asp:ListItem>
                                            <asp:ListItem>57</asp:ListItem>
                                            <asp:ListItem>58</asp:ListItem>
                                            <asp:ListItem>59</asp:ListItem>
                                        </asp:DropDownList>
                                        Mins.
                                    </div>
                                </div>




                                <div class="form-group">
                                    <div class="col-xs-6">
                                    </div>
                                    <div class="col-xs-6 text-right" runat="server" id="divButtons">
                                        <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false"></asp:Label>
                                        <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" ValidationGroup="reportpage" Text="Save" OnClick="btnSave_Click" />
                                        <asp:Button ID="btnCancel" Visible="false" CssClass="btn btn-primary" Width="20%" runat="server" Text="Cancel" OnClick="btnCancel_Click" OnClientClick="window.close(); return false;" />

                                    </div>
                                </div>
                                <hr />

                                <asp:ListView ID="lvReportScheduler" runat="server" OnItemCommand="lvReportScheduler_ItemCommand" OnItemEditing="lvReportScheduler_ItemEditing" OnItemDeleting="lvReportScheduler_ItemDeleting1">

                                    <LayoutTemplate>
                                        <table id="Table1" class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white margin-bottom-none" width="100%"
                                            runat="server">

                                            <thead>
                                                <tr>
                                                    <th style="width: 4%;">No.
                                                    </th>
                                                    <th style="width: 20%;">Report Name
                                                    </th>
                                                    <th style="width: 46%;">Receiver List
                                                    </th>
                                                    <th style="width: 10%;">Interval Type
                                                    </th>
                                                    <th style="width: 10%;">Time Interval
                                                    </th>
                                                    <th style="width: 10%;">Action
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div style="height: 190px; overflow-y: auto; overflow-x: auto;">

                                            <table class="table table-striped table-bordered table-condensed">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>
                                    <ItemTemplate>

                                        <tr>
                                            <td style="width: 4% ! important;">

                                                <%#Container.DataItemIndex+1 %>
                                            </td>

                                            <td style="width: 20% ! important;">
                                                <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                <asp:Label ID="Report" runat="server" Text='<%# Eval("ReportName") %>' />
                                            </td>
                                            <!-- <td style="width: 55%;">-->
                                            <td style="width: 46%;">
                                                <asp:Label ID="ReceivesList" runat="server" Text='<%# PutName(Eval("ReceiverId")) %>' Style="text-wrap: normal; word-wrap: break-word; overflow: auto; display: block; word-break: break-all;" />
                                            </td>
                                            <td style="width: 10%;">
                                                <asp:Label ID="lbltimeinterval" runat="server" Text='<%# Eval("Frequency") %>' />
                                            </td>
                                            <td style="width: 10%;">
                                                <asp:Label ID="Time" runat="server" Text='<%# Eval("Time") %>' />
                                            </td>
                                            <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                ConfirmText='<%# "Are you sure want to delete " + Eval("ReportName") + " ? " %>' OnClientCancel="CancelClick">
                                            </TK1:ConfirmButtonExtender>
                                            <td class="text-center" style="width: 10%;">
                                                <asp:ImageButton ID="ImgEdit" Enabled="<%#!IsUserOperator%>" runat="server" CommandName="Edit" AlternateText="Edit"
                                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" /><!--Visible="false"-->
                                                <asp:ImageButton ID="ImgDelete" Enabled="<%#!IsUserOperator%>" runat="server" CommandName="Delete"
                                                    AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />


                                            </td>
                                        </tr>
                                    </ItemTemplate>

                                    <EmptyDataTemplate>
                                        <div class="message warning align-center bold no-bottom-margin">
                                            <asp:Label ID="lblError" Text="No Record Found" ForeColor="Red" runat="server" Visible="true"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>
                                </asp:ListView>
                            </div>
                        </div>
                    </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </form>
    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script>
        $(document).ready(function () {
            $(".tablescroll").mCustomScrollbar({
                axis: "y",
                // setHeight: "200px",
            });
        });

        function pageLoad() {

            $(".tablescroll").mCustomScrollbar({
                axis: "y",
                // setHeight: "200px",
            });
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
        }


    </script>
</body>
</html>
