﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="BusinessServiceDashboard.aspx.cs" Inherits="CP.UI.Admin.BusinessServiceDashboard" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <script src="../Script/jquery.canvasjs.min.js"></script>
    <script src="../Script/BusinessServiceDashboard.js"></script>
    <style>
        .canvasjs-chart-credit {
            display: none;
        }

        .bxlegend {
            height: 150px !important;
            padding-top: calc(100% - 72px);
        }

        .bx1 {
            padding: 5px;
        }

            .bx1 span.blck {
                width: 8px;
                height: 8px;
                background-color: #f11a1a;
                display: inline-block;
                margin-right: 4px;
            }
    </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <h3>
            <img src="../Images/BSDashboard/dashboard.png">
            Business Services Dashboard </h3>

        <div class="row">
            <div class="col-md-12">
                <div class="col-md-6" style="padding: 0;">
                    <div class="pbox">
                        <div class="phead">
                            <img src="../Images/BSDashboard/BS_availability.png" />
                            Business Service Availability Count

                            <%--<asp:button ID="btnrep1" runat="server" CssClass="report-icon-white blk_btn" ToolTip="Generate Report"/>--%>
                        </div>
                        <div class="ptext">
                            <div class="col-md-12">
                                <div class="col-md-4 pd_ln" style="width: 20%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/configure.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblBSAvailTotalCnt" runat="server" Text="0"></asp:Label></h3>
                                            <span>Configured </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 pd_ln" style="width: 14%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/up.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblBSAvailUPCnt" runat="server" Text="0"></asp:Label></h3>
                                            <span>Up </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 pd_ln" style="width: 15%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/down.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblBSAvailDownCnt" runat="server" Text="0"></asp:Label></h3>
                                            <span>Down </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 pd_ln" style="width: 22%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/maintaince.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblMaintenance" runat="server" Text="0"></asp:Label></h3>
                                            <span>Maintenance </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 pd_ln" style="width: 29%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/under-construction.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblUnderConfig" runat="server" Text="0"></asp:Label></h3>
                                            <span>Under Configuration </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-7 border_right" style="margin-top: 10px">

                                <asp:HiddenField ID="hdnbsList" runat="server"></asp:HiddenField>
                                <div class="clearfix"></div>
                                <div class="col-md-7">
                                    <div id="bsavail" style="width: 200px; height: 140px;"></div>
                                </div>
                                <div class="col-md-5">
                                    <div class="bxlegend" style="padding-top: 0px;">
                                        <div class="bx1"><span class="blck" style="background-color: #f8a217;"></span>Up </div>
                                        <div class="bx1"><span class="blck" style="background-color: #ff1c1c;"></span>Down </div>
                                        <div class="bx1"><span class="blck" style="background-color: #055bb3;"></span>Maintenance </div>
                                        <div class="bx1"><span class="blck" style="background-color: #7d7b7b;"></span>Under Configuration </div>
                                    </div>
                                </div>
                                <div class="clearfix"></div>

                            </div>
                            <div class="col-md-5" style="margin-top: 10px">
                                <%--  <table class="d_table">
                                    <thead>
                                        <tr>
                                            <th style="width: 70%">Business Service</th>
                                            <th style="width: 30%">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="lblstatus" runat="server" CssClass="bs_up" Text="Up"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label1" runat="server" CssClass="bs_down" Text="Down"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label2" runat="server" CssClass="bs_up" Text="Up"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label3" runat="server" CssClass="bs_down" Text="Down"></asp:Label>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>--%>
                                <asp:ListView ID="lvBSList" runat="server" OnItemDataBound="lvBSList_ItemDataBound">
                                    <LayoutTemplate>
                                        <table class="d_table" style="table-layout: fixed; margin-bottom: 0;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 44%;">Business Service
                                                    </th>
                                                    <th style="width: 44%;">Status
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="notifyscroll" style="height: 110px">
                                            <table class="d_table" style="table-layout: fixed; margin-bottom: 0;">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>

                                    <ItemTemplate>
                                        <tr>
                                            <%--  <td class="th table-check-cell text-center" style="width: 4%;">--%>
                                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%--<%#Container.DataItemIndex+1 %>--%>
                                            <%--  </td>--%>
                                            <td class="tdword-wrap" style="width: 44%;">
                                                <asp:Label ID="lblBusinessServiceName" runat="server" Text='<%# Eval("Name") %>' />
                                            </td>
                                            <td class="tdword-wrap" style="width: 44%;">
                                                <asp:Label ID="StateusImgIcon" runat="server"></asp:Label>
                                                <asp:Label ID="lblBSStatus" runat="server" Text='<%#Eval("Id") %>' />
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>
                                <%-- <div class="text-center mt10">
                                    <a href="#" class="d_btn">View All </a>
                                </div>--%>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" style="padding: 0;">
                    <div class="pbox">
                        <div class="phead">
                            <img src="../Images/BSDashboard/BS_health.png" />
                            Business Service Health Summary
                            <%--<asp:button ID="Button1" runat="server" CssClass="report-icon-white blk_btn" ToolTip="Generate Report"/>--%>
                        </div>
                        <div class="ptext">
                            <div class="col-md-12">
                                <div class="col-md-3 pd_ln" style="width:25%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/BS_health_total.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblTotalHealthSummary" runat="server" Text="0"></asp:Label></h3>
                                            <span>Total </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 pd_ln" style="width:20%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/BS_health_pr.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblProductionCnt" runat="server" Text="0"></asp:Label></h3>
                                            <span>Production </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 pd_ln" style="width:20%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/BS_health_dr.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblDRCount" runat="server" Text="0"></asp:Label></h3>
                                            <span>DR </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 pd_ln" style="width:35%">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/under-construction.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblUnderConfigCnt" runat="server" Text="0"></asp:Label></h3>
                                            <span>Under Configuration </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <div class="col-md-7 border_right" style="margin-top:10px">

                                <div class="col-md-12">
                                    <div id="bsHealth" style="width: 350px; height: 140px;"></div>
                                </div>
                                <%-- <div class="col-md-5">
                                    <div class="bxlegend">
                                        <div class="bx1"><span class="blck" style="background-color: #f8a217;"></span>Up </div>
                                        <div class="bx1"><span class="blck" style="background-color: #ff1c1c;"></span>Down </div>
                                    </div>
                                </div>--%>
                                <div class="clearfix"></div>

                            </div>
                            <div class="col-md-5" style="margin-top:10px">
                                <asp:ListView ID="lvHealthSummary" runat="server" OnItemDataBound="lvHealthSummary_ItemDataBound">
                                    <LayoutTemplate>
                                        <table class="d_table" style="table-layout: fixed; margin-bottom: 0;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 44%;">Business Service
                                                    </th>
                                                    <th style="width: 44%;">Status
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="notifyscroll" style="height: 80px">
                                            <table class="d_table" style="table-layout: fixed; margin-bottom: 0;">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>

                                    <ItemTemplate>
                                        <tr>
                                            <%--  <td class="th table-check-cell text-center" style="width: 4%;">--%>
                                            <asp:Label ID="HealthId" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%--<%#Container.DataItemIndex+1 %>--%>
                                            <%--  </td>--%>
                                            <td class="tdword-wrap" style="width: 44%;">
                                                <asp:Label ID="lblHealthBSName" runat="server" Text='<%# Eval("Name") %>' />
                                            </td>
                                            <td class="tdword-wrap" style="width: 44%;">
                                                <asp:Label ID="StatusHealthImgIcon" runat="server"></asp:Label>
                                                <asp:Label ID="lblHealthBSStatus" runat="server" Text='<%#Eval("Id") %>' />
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>
                                <%--  <table class="d_table">
                                    <thead>
                                        <tr>
                                            <th style="width: 60%">Business Service</th>
                                            <th style="width: 40%">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label4" runat="server" CssClass="bs_prod" Text="Production"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label5" runat="server" CssClass="bs_prod" Text="Production"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label6" runat="server" CssClass="bs_dr" Text="DR"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label7" runat="server" CssClass="bs_dr" Text="DR"></asp:Label>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>--%>

                                 <div class="text-center mt10">
                                    <a href="../Admin/BSHealthSummary.aspx" class="d_btn">View All </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="clearfix"></div>
                <div class="col-md-6" style="padding: 0;">
                    <div class="pbox">
                        <div class="phead">
                            <img src="../Images/BSDashboard/BS_replication.png" />
                            Business Service Replication Summary
                            <%--<asp:button ID="Button2" runat="server" CssClass="report-icon-white blk_btn" ToolTip="Generate Report"/>--%>
                        </div>
                        <div class="ptext" style="min-height: 223px;">
                            <div class="col-md-6 border_right">
                                <div class="col-md-7 pd_ln">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/non_impacted.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblNonImpactedCnt" runat="server"></asp:Label>
                                            </h3>
                                            <span>Non Impacted </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-5 pd_ln">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/impacted.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblImpacted" runat="server"></asp:Label>
                                            </h3>
                                            <span>Impacted </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                                <div class="col-md-7">
                                    <div id="bsReplication" style="width: 200px; height: 150px;"></div>
                                </div>
                                <div class="col-md-5" style="padding-right: 0px;">
                                    <div class="bxlegend">
                                        <div class="bx1"><span class="blck" style="background-color: #f8a217;"></span>Non Impacted </div>
                                        <div class="bx1"><span class="blck" style="background-color: #ff1c1c;"></span>Impacted </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <%--  <table class="d_table">
                                    <thead>
                                        <tr>
                                            <th style="width: 60%">Business Service</th>
                                            <th style="width: 40%">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label8" runat="server" CssClass="bs_nonimpacted" Text="Non Impacted"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label9" runat="server" CssClass="bs_impacted" Text="Impacted"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label10" runat="server" CssClass="bs_nonimpacted" Text="Non Impacted"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label11" runat="server" CssClass="bs_impacted" Text="Impacted"></asp:Label>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>--%>

                                <asp:ListView ID="lvRepliSummary" runat="server" OnItemDataBound="lvRepliSummary_ItemDataBound">
                                    <LayoutTemplate>
                                        <table class="d_table" style="table-layout: fixed; margin-bottom: 0px;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 44%;">Business Services
                                                    </th>
                                                    <th style="width: 44%;">Status
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="notifyscroll" style="height: 169px">
                                            <table class="d_table" style="table-layout: fixed">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>

                                    <ItemTemplate>
                                        <tr>
                                            <%--  <td class="th table-check-cell text-center" style="width: 4%;">--%>
                                            <asp:Label ID="BSId" runat="server" Text='<%# Eval("serviceId") %>' Visible="false" /><%--<%#Container.DataItemIndex+1 %>--%>
                                            <%--  </td>--%>
                                            <td class="tdword-wrap" style="width: 44%;">
                                                <asp:Label ID="lblBSNameForSummary" runat="server" Text='<%# Eval("BusinessServiceName") %>' />
                                            </td>
                                            <td class="tdword-wrap" style="width: 44%;">
                                                <asp:Label ID="StateusImgIconForSummary" runat="server"></asp:Label>
                                                <asp:Label ID="lblBSStatusForSummary" runat="server" Text='<%#Eval("Status") %>' />
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>

                                <%-- <div class="text-center mt10">
                                    <a href="#" class="d_btn">View All </a>
                                </div>--%>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" style="padding: 0;">
                    <div class="pbox">
                        <div class="phead">
                            <img src="../Images/BSDashboard/BS_drill.png" />
                            Business Service Drill Summary
                            <div class="toggle_btn">
                                <span id="bx_list1" title="Switch To List View">
                                    <img src="../Images/BSDashboard/BS_drill_list.png">
                                </span>
                                <span class="active" id="bx_chart1" title="Switch To Graph View">
                                    <img src="../Images/BSDashboard/BS_drill_chart.png">
                                </span>
                            </div>
                            <%--<asp:button ID="Button3" runat="server" CssClass="report-icon-white blk_btn" ToolTip="Generate Report" />--%>
                        </div>
                        <div class="ptext">
                            <div class="col-md-12">
                                <div class="col-md-3 pd_ln">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/executed.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblTotalExecuted" runat="server"></asp:Label>
                                            </h3>
                                            <span>Executed </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 pd_ln">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/success.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblSuccess" runat="server"></asp:Label></h3>
                                            <span>Successful </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 pd_ln">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/partial_success.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblPartialSuccess" runat="server"></asp:Label></h3>
                                            <span>Partial Success </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 pd_ln">
                                    <div class="i_bx">
                                        <img src="../Images/BSDashboard/failed.png" />
                                        <div>
                                            <h3>
                                                <asp:Label ID="lblFailed" runat="server"></asp:Label></h3>
                                            <span>Failed </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="clearfix"></div>
                            <div class="col-md-12 bx_list1" style="display: none">

                                <asp:ListView ID="lvDrillSummary" runat="server">
                                    <LayoutTemplate>
                                        <table class="d_table" style="table-layout: fixed; margin-bottom: 0px;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 24%">Business Service</th>
                                                    <th style="width: 12%">Executed</th>
                                                    <th style="width: 12%">Success</th>
                                                    <th style="width: 17%">Partial Success</th>
                                                    <th style="width: 12%">Failed</th>
                                                    <th style="width: 23%">Last Drill Execution</th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="notifyscroll" style="height: 125px">
                                            <table class="d_table" style="table-layout: fixed">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>

                                    <ItemTemplate>
                                        <tr>
                                            <%--  <td class="th table-check-cell text-center" style="width: 4%;">--%>
                                            <asp:Label ID="BSDrillId" runat="server" Text='<%# Eval("BusinessServiceId") %>' Visible="false" /><%--<%#Container.DataItemIndex+1 %>--%>
                                            <%--  </td>--%>
                                            <td class="tdword-wrap" style="width: 24%;">
                                                <asp:Label ID="lblBSNameForDrill" runat="server" Text='<%# Eval("BusinesServiceName") %>' />
                                            </td>
                                            <td class="tdword-wrap" style="width: 12%;">
                                                <asp:Label ID="ImgIconForDrillExecuted" runat="server" CssClass="bs_executed"></asp:Label>
                                                <asp:Label ID="lblDrillExecuted" runat="server" Text='<%#Eval("ExecutedAction") %>' />
                                            </td>
                                            <td class="tdword-wrap" style="width: 12%;">
                                                <asp:Label ID="ImgIconForDrillSuccess" runat="server" CssClass="bs_success"></asp:Label>
                                                <asp:Label ID="lblDrillSuccess" runat="server" Text='<%#Eval("SuccessAction") %>' />
                                            </td>
                                            <td class="tdword-wrap" style="width: 17%;">
                                                <asp:Label ID="ImgIconForDrillPartialSuccess" runat="server" CssClass="bs_partial"></asp:Label>
                                                <asp:Label ID="lblDrillPartialSuccess" runat="server" Text='<%#Eval("PartialSuccessAction") %>' />
                                            </td>
                                            <td class="tdword-wrap" style="width: 12%;">
                                                <asp:Label ID="ImgIconForDrillFailed" runat="server" CssClass="bs_failed"></asp:Label>
                                                <asp:Label ID="lblDrillFailed" runat="server" Text='<%#Eval("FailedAction") %>' />
                                            </td>
                                            <td class="tdword-wrap" style="width: 23%;">
                                                <asp:Label ID="ImgLastDrill" runat="server" CssClass="bs_lastdrill"></asp:Label>
                                                <asp:Label ID="lblLastDrill" runat="server" Text='<%#Eval("LastExecuted") %>' />
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>

                                <%--   <table class="d_table mt10">
                                    <thead>
                                        <tr>
                                            <th style="width: 24%">Business Service</th>
                                            <th style="width: 12%">Executed</th>
                                            <th style="width: 12%">Success</th>
                                            <th style="width: 17%">Partial Success</th>
                                            <th style="width: 12%">Failed</th>
                                            <th style="width: 23%">Last Drill Execution</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label16" runat="server" CssClass="bs_executed" Text="20"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label17" runat="server" CssClass="bs_success" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label18" runat="server" CssClass="bs_partial" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label19" runat="server" CssClass="bs_failed" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label12" runat="server" CssClass="bs_lastdrill" Text="13/11/2020"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label13" runat="server" CssClass="bs_executed" Text="20"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label20" runat="server" CssClass="bs_success" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label21" runat="server" CssClass="bs_partial" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label22" runat="server" CssClass="bs_failed" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label23" runat="server" CssClass="bs_lastdrill" Text="13/11/2020"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label14" runat="server" CssClass="bs_executed" Text="20"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label24" runat="server" CssClass="bs_success" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label25" runat="server" CssClass="bs_partial" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label26" runat="server" CssClass="bs_failed" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label27" runat="server" CssClass="bs_lastdrill" Text="13/11/2020"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="tdword-wrap">BS-Virtual_Hyperv</td>
                                            <td>
                                                <asp:Label ID="Label15" runat="server" CssClass="bs_executed" Text="20"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label28" runat="server" CssClass="bs_success" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label29" runat="server" CssClass="bs_partial" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label30" runat="server" CssClass="bs_failed" Text="10"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="Label31" runat="server" CssClass="bs_lastdrill" Text="13/11/2020"></asp:Label>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>--%>

                                <%-- <div class="text-center mt10">
                                    <a href="#" class="d_btn">View All </a>
                                </div>--%>
                            </div>
                            <div class="col-md-12 bx_chart1">
                                <div class="col-md-10">
                                    <div id="bsdrill"></div>
                                </div>
                                <div class="col-md-2" style="padding: 0px;">
                                    <div class="bxlegend" style="padding-top: calc(100% - 92px);">
                                        <div class="bx1"><span class="blck" style="background-color: #4a8bc2;"></span>Performed </div>
                                        <div class="bx1"><span class="blck" style="background-color: #1a8900;"></span>Success </div>
                                        <div class="bx1"><span class="blck" style="background-color: #ff7d1d;"></span>Part Success </div>
                                        <div class="bx1"><span class="blck" style="background-color: #cc2a3c;"></span>Failed </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <asp:Panel ID="pnlmodalviewall" runat="server" Width="100%" Visible="false">
        <div class="bg" style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;"></div>
        <div class="modal" style="display: block; z-index: 9999999;">
            <div class="modal-dialog maindashmodal" style="width: 800px;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">
                            <asp:Label ID="lblmodalname" runat="server" Text="Popup Name"></asp:Label>
                        </h3>
                        <asp:LinkButton ID="LnkbtnCloseEnableDSMonitoring" runat="server" ToolTip="Close window" CausesValidation="False" class="close" CommandName="Close">x</asp:LinkButton>
                        <%--OnClick="LnkbtnCloseEnableDSMonitoring_Click"--%>
                    </div>
                    <div class="modal-body">
                        <asp:Panel ID="p1" runat="server">
                            <table id="tblCommonComponent" class="table table-white margin-bottom-none" width="100%" runat="server">
                                <thead>
                                    <tr>
                                        <th style="width: 22%">Disk Info
                                        </th>
                                        <th style="width: 39%">Production Server
                                        </th>
                                        <th style="width: 39%">DR Server
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                            <div class="notifyscroll" style="height: 300px">
                                <table id="Table1" class="table table-white margin-bottom-none" width="100%" runat="server">
                                    <tbody>
                                        <tr>
                                            <td>Test 1 
                                            </td>
                                            <td>Test 1  
                                            </td>
                                            <td>Test 1 
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </asp:Panel>
                    </div>
                    <div class="modal-footer" style="padding: 5px;">
                        <div class="text-right">
                            <asp:Button ID="btnClose" runat="server" Text="Close" CssClass="btn btn-default" Style="font-size: 12px"
                                Width="12%" CausesValidation="False" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </asp:Panel>

    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script type="text/javascript">
        $(document).on("click", ".toggle_btn span", function () {
            var sid = $(this).attr("id");
            $(".toggle_btn span").removeClass("active");
            $("#" + sid).addClass("active");
            $(".bx_chart1, .bx_list1").hide();
            $("." + sid).show();
        })

        function count_avail_repli(cdata) {
            var datajson = cdata.split("$$$");
            alert("avail + replic");
            BSAvail(datajson[0]);
            BSreplication(datajson[1]);
        }

        function pageLoad() {
            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                },

            });

            //var ss = "1,2";
            //var ss11 = "1,2,5";
            //BSAvail(ss);
            //BSHealth(ss11);
            //BSreplication(ss);
            //BSDrill();
        }
    </script>
</asp:Content>
