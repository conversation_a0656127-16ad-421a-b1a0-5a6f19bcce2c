﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CustomError.aspx.cs" Inherits="CP.UI.CustomError" MasterPageFile="~/Master/BcmsLogin.Master" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <!DOCTYPE html>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE" />
    <script src="Script/less.min.js"></script>
    <link href="App_Themes/CPTheme/CPMaster.less" rel="stylesheet" />
    <link href="App_Themes/CPTheme/bootstrap.min.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/CPMaster.less" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/bootstrap.min.css" rel="stylesheet" />
    <style type="text/css">
       
        body{
	margin:0px !important;
	}
	.innerAll{
	padding:0px !important;
	}
         .logoutTop {
            background-color: #4a8bc2;
            border-bottom: 1px solid #000;
            height: 49px;
        }

        *, *:before, *:after {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
        }

        .row {
            margin-left: -15px;
            margin-right: -15px;
        }

        .logout-back {
            background-color: #fafafa !important;
            border: 1px solid #dbdbdb;
            border-radius: 5px;
            margin-top: 45px;
        }

        .center {
            text-align: center !important;
        }

        .col-md-push-4 {
            left: 33.33333333333333%;
        }

        .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11 {
            float: left;
        }

        .col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
            min-height: 1px;
            position: relative;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .btn {
            border-radius: 2px;
            font-weight: 600;
        }

        .btn-primary {
            background-color: #428bca;
            border-color: #357ebd;
            color: #fff;
        }

        .btn {
            -moz-user-select: none;
            cursor: pointer;
            display: inline-block;
            font-size: 14px;
            line-height: 1.42857;
            margin-bottom: 0;
            padding: 6px 12px;
            text-align: center;
            vertical-align: middle;
            white-space: nowrap;
        }

        a {
            text-decoration: none;
        }
    </style>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphBody" runat="server">
    <div class="logoutTop "></div>
    <div class="row innerAll margin-none">
        <div class="col-md-push-4 col-md-4 center logout-back" style="padding: 15px; box-shadow: 0 0px 0 0 #f6f6f6;">
            <div class="innerAll">
                <div class="form-group">
                    <img src="./Images/oopsError.png"  alt="DisConnect" runat="server" />
                </div>
                <a href="./Login.aspx" class=" btn btn-primary" runat="server"><i class="fa fa-home" ></i>Home</a>
              
            </div>
        </div>
    </div>

</asp:Content>
