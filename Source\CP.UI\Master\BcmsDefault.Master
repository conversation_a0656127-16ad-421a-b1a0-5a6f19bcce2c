﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="BcmsDefault.master.cs"
    Inherits="CP.UI.Master.BcmsDefault" %>

<%@ Register Src="../Controls/Header.ascx" TagName="Header" TagPrefix="uc1" %>
<%@ Register Src="../Controls/Footer.ascx" TagName="Footer" TagPrefix="uc3" %>
<%@ Register Src="../Controls/ErrorSuccessNotifier.ascx" TagName="ErrorSuccessNotifier"
    TagPrefix="uc4" %>

<!DOCTYPE html>
<html class="fluid top-full sticky-top sidebar sidebar-full sticky-sidebar js no-touch fullbg">
<head id="Head1" runat="server">
    <title>Continuity Patrol</title>
    <style>
        html.fullbg {
            height: 100%;
        }

        html body {
            height: 100%;
        }
    </style>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE" />


    <link href="../Images/CP_Favicon.ico" rel="shortcut icon" type="image/icon" />

    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate-3.5.1.js"></script>
    <script src="../Script/modernizr.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/ie.prototype.polyfill.js"></script>
    <script src="../Script/html5shiv.js"></script>
    <script type="text/javascript" src="../Script/jquery-ui.min.js "></script>
    <script src="../Script/tag-it.js"></script>
    <script type="text/javascript">
        function DisableBackButton() {
            window.history.forward();
        }
        DisableBackButton();
        window.onload = DisableBackButton;
        window.onpageshow = function (evt) { if (evt.persisted) DisableBackButton(); };
        window.onunload = function () { void (0); };
    </script>


    <asp:ContentPlaceHolder ID="cphHead" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body onload="load()">
    <form id="form1" runat="server" enctype="multipart/form-data">
        <asp:ScriptManager ID="smDefault" runat="server" EnablePartialRendering="true" AsyncPostBackTimeout="36000"
            LoadScriptsBeforeUI="false">
        </asp:ScriptManager>
        <div class="container-fluid fluid menu-left">
            <div id="wrapper">
                <div id="content">
                    <uc1:Header ID="Header1" runat="server" />
                    <uc4:ErrorSuccessNotifier ID="ErrorSuccessNotifier1" runat="server" />
                    <asp:ContentPlaceHolder ID="cphBody" runat="server">
                    </asp:ContentPlaceHolder>
                </div>
                <asp:HiddenField ID="hdSelectedMenu" runat="server" />
            </div>
            <uc3:Footer ID="Footer1" runat="server" Visible="true" />
        </div>
    </form>
    <script src="../Script/jquery.AssignZoneModal.js" type="text/javascript"></script>
    <script src="../Script/jquery.modal.js" type="text/javascript"></script>
    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/jquery.slimscroll.min.js"></script>
    <script src="../Script/jquery.cookie.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script src="../Script/core.init.js"></script>
    <script src="../Script/jquery.capslockstate.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {

            if ($("form").attr("action") != "ServerConfiguration.aspx") {
                localStorage.removeItem("ADMIP");
            }


            var logusrlgth = $("[id$=lblLoggedUser]").text().length;
            if (logusrlgth > 7 && $(".topnav li[id$=lstAlert]") != "undefined") {
                $("[id$=lblLoggedUser]").addClass("hide");
            }

            radiobutton();
            $('input[type="checkbox"]').checkbox();

            $('#ulrepctype').slimScroll({
                height: '312px'
            });

            sessionStorage.setItem("ScrollTo", 0);

            sessionStorage.setItem('tab-index', 0);
            currentClock();
            SelectedMenu();
            if (typeof window.Page_Validators != 'undefined')
                for (var i = 0; i < window.Page_Validators.length; i++) {
                    $(window.Page_Validators).attr("class", "error");
                }

            $('[id$=successMsg1]').css({ opacity: 0 });
            $('[id$=successMsg1]').fadeTo("slow", 1.0);
            setTimeout(function () {
                $('.AutoHide').fadeOut('slow', function () {
                    $('.AutoHide').remove();
                });
            }, 2000);
        });

        function SelectedMenu() {
            var radioArray = new Array("Module1", "Module2", "Module3", "Module4", "Module5", "Module6", "Module7", "Module8");
            for (var i = 0; i < radioArray.length; i++) $("#" + radioArray[i]).parent().removeClass('active');
            var value = $('[id$=hdSelectedMenu]').val(); $('[id$=' + value + ']').parent().addClass('active');
        }
        $('nav > ul > li[class="disabled"]').children().removeAttr('href');

        var xPos, yPos;

        function getCurrentFinancialYear() {
            var fiscalyear = "";
            var today = new Date();
            if ((today.getMonth() + 1) <= 3) {
                fiscalyear = (today.getFullYear() - 1) + "-" + today.getFullYear()
            } else {
                fiscalyear = today.getFullYear() + "-" + (today.getFullYear() + 1)
            }
            return fiscalyear
        }

        function load() {
            Sys.WebForms.PageRequestManager.getInstance().add_pageLoaded(ShowSlimScrollBar);
            Sys.WebForms.PageRequestManager.getInstance().add_endRequest(ShowSlimScrollBar);
            Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(SaveAutoPostbackScroll);
            document.getElementById("spFY").innerHTML = getCurrentFinancialYear();
        }




        function SaveAutoPostbackScroll(sender, args) {

            xPos = document.getElementById('wrapper').scrollLeft;
            yPos = document.getElementById('wrapper').scrollTop;

        }


        function ShowSlimScrollBar() {

            var scrollposition = 0;
            $('[id$=lnkbtnBusinessServiceName]').click(function () {
                var str = $(this).attr('id');
                var lnkbtnindex = str.indexOf("_lnkbtnBusinessServiceName");
                var lnkbtnNum = str.indexOf("_ctl");
                var lnkbtncount = lnkbtnindex - lnkbtnNum - 4;
                var lnkbtnval = str.substr(29, lnkbtncount) - 1;

                scrollposition = parseInt(120 * lnkbtnval);

                sessionStorage.setItem("ScrollTo", scrollposition);
            });

            scrollposition = sessionStorage.getItem("ScrollTo");
            $('#inner-content-div').slimScroll({
                scrollTo: scrollposition,
                height: 'auto'
            });


            document.getElementById('wrapper').scrollLeft = xPos;
            document.getElementById('wrapper').scrollTop = yPos;



            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();
            $('.slim-scroll').slimScroll({
                height: '190px'
            });
            $('[id$=successMsg1]').css({ opacity: 0 });
            $('[id$=successMsg1]').fadeTo("slow", 1.0);
            setTimeout(function () {
                $('.AutoHide').fadeOut('slow', function () {
                    $('.AutoHide').remove();
                });
            }, 5000);

        }

        function currentClock() {
            var d = new Date();
            var nd = new Date();
            var h, m;
            var s;
            var time = " ";
            h = nd.getHours();
            m = nd.getMinutes();
            s = nd.getSeconds();
            if (h <= 9) h = "0" + h;
            if (m <= 9) m = "0" + m;
            if (s <= 9) s = "0" + s;
            time += h + ":" + m + ":" + s;
            $("#time").html(time);
            $("#day").html(d.toLocaleDateString());
            setTimeout("currentClock()", 1000);
        }

        $('[id$=notifications]').click(function () {
            $(this).fadeOut("slow");

        });
         </script>


        <script type="text/javascript">
        $(document).ready(function () {
            /*For Single Password field chk-caps*/
            $(window).bind("capsOn", function (event) {
                if ($(".chk-caps:focus").length > 0) {
                    $(".chk-caps").nextAll(".caps-error").show();
                }
                if ($(".chk-capsp:focus").length > 0) {
                    $(".chk-capsp").nextAll(".caps-error").show();
                }
                if ($(".chk-capspc:focus").length > 0) {
                    $(".chk-capspc").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-caps").nextAll(".caps-error").hide();
                $(".chk-capsp").nextAll(".caps-error").hide();
                $(".chk-capspc").nextAll(".caps-error").hide();
            });
            $(".chk-caps, .chk-capsp, .chk-capspC").bind("focusout", function (event) {
                $(".chk-caps").nextAll(".caps-error").hide();
                $(".chk-capsp").nextAll(".caps-error").hide();
                $(".chk-capspc").nextAll(".caps-error").hide();
            });
            $(".chk-caps, .chk-capsp, .chk-capspc").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-caps").nextAll(".caps-error").show();
                    $(".chk-capsp").nextAll(".caps-error").show();
                    $(".chk-capspc").nextAll(".caps-error").show();
                }
            });
        
            $(window).capslockstate();


        });
   
    </script>
</body>
</html>
