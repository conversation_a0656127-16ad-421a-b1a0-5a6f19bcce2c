﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "CyberRecoveryVaultMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class CyberRecoveryVaultMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int id { get; set; }
        [DataMember]
        public int InfraObjectId { get; set; }

        //[DataMember]
        //public string RMMServerIPAddress { get; set; }
        [DataMember]
        public string CyberRecoverVaultState { get; set; }
        [DataMember]
        public String CyberRecoveryPolicyName { get; set; }
        [DataMember]
        public string JobName { get; set; }
        [DataMember]
        public string JobCopyName { get; set; }
        [DataMember]
        public string JobStatus { get; set; }
        [DataMember]
        public string RequestType { get; set; }
        [DataMember]
        public string StartTime { get; set; }
        [DataMember]
        public string EndTime { get; set; }
        [DataMember]
        public string ElapsedTime { get; set; }


        [DataMember]
        public string LastJobCompletedEndTime { get; set; }

        [DataMember]
        public string LastJobName { get; set; }

        [DataMember]
        public string LastJobCopyName { get; set; }


        [DataMember]
        public string LastCopyStatus { get; set; }

        [DataMember]
        public string DataLag { get; set; }
     
        [DataMember]
        public string CreateDate { get; set; }

        [DataMember]
        public string UpdateDate { get; set; }


        public string InfraObjectName
        {
            get;
            set;
        }
        public string Server
        {
            get;
            set;
        }
        #endregion Properties
    }
}
