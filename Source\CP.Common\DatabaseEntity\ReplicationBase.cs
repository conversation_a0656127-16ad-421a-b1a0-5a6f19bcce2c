﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ReplicationBase", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ReplicationBase : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public ReplicationType Type { get; set; }

        [DataMember]
        public int SiteId { get; set; }

        [DataMember]
        public int RepliSrvTypeCount { get; set; }

        [DataMember]
        public string reptype { get; set; }

       
        //[DataMember]
        //public DataGuard DataGuard
        //{
        //    get
        //    {
        //        return _dataGuard;
        //    }
        //    set
        //    {
        //        _dataGuard = value;
        //    }
        //}

        //[DataMember]
        //public GlobalMirror GlobalMirror
        //{
        //    get
        //    {
        //        return _globalMirror;
        //    }
        //    set
        //    {
        //        _globalMirror = value;
        //    }
        //}

        //[DataMember]
        //public SnapMirror SnapMirror
        //{
        //    get
        //    {
        //        return _snapMirror;
        //    }
        //    set
        //    {
        //        _snapMirror = value;
        //    }
        //}

        //[DataMember]
        //public SCR SCR
        //{
        //    get
        //    {
        //        return _scr;
        //    }
        //    set
        //    {
        //        _scr = value;
        //    }
        //}

        //[DataMember]
        //public FastCopy FastCopy
        //{
        //    get
        //    {
        //        return _fastCopy;
        //    }
        //    set
        //    {
        //        _fastCopy = value;
        //    }
        //}

        //[DataMember]
        //public EMCSRDF EMCSRDF
        //{
        //    get
        //    {
        //        return _emcsrdf;
        //    }
        //    set
        //    {
        //        _emcsrdf = value;
        //    }
        //}

        #endregion Properties
    }
}