﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="BusinessFunctionsBIA.aspx.cs" Inherits="CP.UI.Admin.BusinessFunctionsBIA"
    Title="Continuity Patrol :: Functions Business Impact Analysis " %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <script type="text/javascript">
       
        function openRadWindow(Url) {
            window.radopen(Url, "TelRadWindow");
        }
       

        function CancelClick() {
            return false;
        }

        $(document).ready(function () {
            sessionStorage.setItem('accordion-index', '#collapse-1-1');
            hreftext = sessionStorage.getItem('accordion-index');
            $(hreftext).addClass('in');

        });

        function GetAccordion() {
            var hreftext;
            $('a.accordion-toggle').click(function () {
                hreftext = $(this).attr("href");
                sessionStorage.setItem('accordion-index', hreftext);
                hreftext = sessionStorage.getItem('accordion-index');
            });

            hreftext = sessionStorage.getItem('accordion-index');
            $(hreftext).addClass('in');
        }

        Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
        Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
        function BeginRequestHandler(sender, args) {

        }
        function EndRequestHandler(sender, args) {
            GetAccordion();
        }
    </script>
    <telerik:RadWindowManager ID="RadWindowManager1" runat="server" IconUrl="~/Images/icons/alerts/sign_add.png">
        <Windows>
            <telerik:RadWindow ID="TelRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="520"
                Width="600" Skin="Metro" />
        </Windows>
    </telerik:RadWindowManager>  
    <input type="hidden" id="hdfStaticGuid" runat="server" />
<asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
             <img src="../Images/bia-icon.png">
            Manage Financial Impact Analysis-Business Functions</h3>

        <div class="widget">
            <div class="widget-head" style="padding-left: 10px;">
                <h3 style="font-size: 14px; line-height: 30px;"><b>Business Function FIA</b></h3>
            </div>
            <div class="widget-body">
                <asp:UpdatePanel ID="Udplvfunction" runat="server" UpdateMode="Conditional">
                    <ContentTemplate>
                        <asp:ListView ID="lvFunctionsList" runat="server" DataKeyNames="ID" OnPreRender="lvFunctionsList_PreRender" OnItemEditing="lvFunctionsList_ItemEditing" OnItemDataBound="lvFunctionsList_ItemDataBound">
                            <LayoutTemplate>
                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                    <thead>
                                        <tr>
                                            <th style="width: 2%; background-color: #4a8bc2 !important; color: #FFFFFF !important;">#
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">Business Function Name
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">Selected FIA Template
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">Business Service Name
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">Criticality Level
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">Configured RTO
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">Configured RPO
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">Manage Dependency Rules
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">View Report
                                            </th>
                                            <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">View RTO
                                            </th>
                                             <th style="background-color: #4a8bc2 !important; color: #FFFFFF !important;">Perform FIA
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td>
                                        <%#Container.DataItemIndex+1 %>
                                        <asp:Label ID="lblFunctionID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                        <asp:Label ID="lblFIATemplateID" Text='<%# GetFIATemplateByID(Eval("Id")) %>' runat="server" Visible="false" />
                                        <asp:Label ID="lblBusinessServiceId" runat="server" Text='<%# Eval("BusinessServiceId") %>' Visible="false" />
                                     
                                    </td>
                                    <td>
                                        <asp:Label ID="lblFunctionName" Text='<%# Eval("Name") %>' runat="server" />

                                    </td>
                                  
                                    <td>
                                          <asp:Label ID="lblFIATemplateName" Text='<%# GetFIATemplateName(Eval("Id")) %>' runat="server" />
                                    </td>
                                    <td>
                                        <asp:Label ID="lblbusinessServiceName" Text='<%# GetBusinessServiceName(Eval("BusinessServiceId")) %>' runat="server" />
                                    </td>
                                    <td>
                                        <asp:Label ID="lblCriticalityLevel" Text='<%# Eval("CriticalityLevel") %>' runat="server" />
                                    </td>
                                    <td>
                                        <asp:Label ID="lblRPO" Text='<%# GetFormattedRTO(Eval("ConfiguredRPO")) %>' runat="server" />
                                    </td>
                                    <td>
                                        <asp:Label ID="lblRTO" Text='<%# GetFormattedRTO(Eval("ConfiguredRTO")) %>' runat="server" />
                                    </td>
                                    <td align="center">
                                        <%--  <asp:LinkButton ID="lnkviewdependency" runat="server" CssClass="glyphicons snowflake" ToolTip="View Dependency"></asp:LinkButton>--%>
                                        <a id="anchviewdependency" runat="server" href="javascript:void(0)" onserverclick="anchviewdependency_ServerClick"
                                            class="glyphicons snowflake" title="View Dependency"><i></i></a>
                                    </td>
                                    <td align="center">
                                        <asp:ImageButton ID="lnkBtnViewReport" ToolTip="View Report" runat="server" ImageUrl="../images/report-icon.png" CausesValidation="false" OnClick="lnkBtnViewReport_Click"></asp:ImageButton>
                                    </td>
                                    <td align="center">
                                        <asp:ImageButton ID="lnkBtnEdit" ToolTip="Edit BIA Details" runat="server" ImageUrl="../images/icons/pencil.png" CommandName="Edit" CausesValidation="false"></asp:ImageButton>
                                    </td>
                                    <td align="center">
                                        <asp:ImageButton ID="lnkbtnPerformBIA" ToolTip="Perform BIA" runat="server" ImageUrl="../images/icons/pencil.png" OnClick="lnkbtnPerformBIA_Click"  CausesValidation="false"></asp:ImageButton>
                                    </td>

                                </tr>
                            </ItemTemplate>
                            <EmptyDataTemplate>
                                <div class="message warning align-center bold no-bottom-margin">
                                    <asp:Label ID="lblMsg" runat="server" Text="No Records Found" Font-Bold="true" Style="color: Green;"></asp:Label>
                                </div>
                            </EmptyDataTemplate>
                        </asp:ListView>
                    </ContentTemplate>
                </asp:UpdatePanel>
                <div class="row">
                    <div class="col-md-6">
                        <asp:DataPager ID="dataPagerFunctionsList1" runat="server" PagedControlID="lvFunctionsList" PageSize="5">
                            <Fields>
                                <asp:TemplatePagerField>
                                    <PagerTemplate>
                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                        Results
                                                    <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                    Out Of
                                                    <%# Container.TotalRowCount %>
                                        <br />
                                    </PagerTemplate>
                                </asp:TemplatePagerField>
                            </Fields>
                        </asp:DataPager>
                    </div>
                    <div class="col-md-6 text-right">
                        <asp:DataPager ID="dataPagerFunctionsList2" runat="server" PagedControlID="lvFunctionsList"
                            PageSize="5">
                            <Fields>
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                    ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                <asp:NumericPagerField PreviousPageText="..." NextPageText="..." ButtonCount="10"
                                    NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                    NumericButtonCssClass="btn-pagination" />
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                    ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true"
                                    NextPageText="Next → " />
                            </Fields>
                        </asp:DataPager>
                    </div>
                </div>
            </div>
        </div>
        <asp:UpdatePanel ID="UdpBIA" runat="server" ChildrenAsTriggers="true" UpdateMode="Conditional">
            <ContentTemplate>
                <div class="widget" runat="server" id="divtitle" visible="false">
                    <div class="widget-body">
                        <asp:UpdatePanel ID="Udptblfunction" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <table class="table table-striped table-bordered table-condensed" width="100%" style="margin-bottom: 0px;">
                                    <thead>
                                        <tr>
                                            <th align="left">Business Impact Analysis For :
                                                                <asp:Label ID="lblfunctionhead" runat="server"></asp:Label>
                                                <asp:Label ID="lblfunctionheadid" runat="server" Text="0" Visible="false"></asp:Label></h3>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                        </tr>
                                    </tbody>
                                </table>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                        <div runat="server" id="divAccordian" visible="false">

                            <div class="relativeWrap">
                                <div class="panel-group accordion accordion-2" id="tabAccountAccordion">
                                  
                                    <div class="panel panel-default" runat="server" visible="false">
                                        <div class="panel-heading">
                                            <h3 class="panel-title"><a class="accordion-toggle glyphicons right_arrow" style="text-decoration: none !important;" data-toggle="collapse" data-parent="#tabAccountAccordion" href="#collapse-1-1"><i></i>Application Activities:</a></h3>
                                        </div>
                                        <div id="collapse-1-1" class="panel-collapse collapse">
                                            <div class="panel-body">
                                                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <asp:Panel ID="panel1" runat="server" Visible="true">

                                                            <asp:ListView ID="lvActivitylist" runat="server">
                                                                <LayoutTemplate>
                                                                    <table id="tblapplicationactivities" class="table table-bordered table-striped table-white margin-bottom-none" width="100%" style="margin-bottom: 10px;">
                                                                        <thead>
                                                                            <tr>
                                                                                <th style="width: 5%; text-align: center;">#
                                                                                </th>
                                                                                <th align="left">Activities
                                                                                </th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            <tr>
                                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </LayoutTemplate>
                                                                <ItemTemplate>
                                                                    <tr>
                                                                        <td style="vertical-align: middle; text-align: center;">
                                                                            <%#Container.DataItemIndex+1 %>
                                                                            <asp:Label ID="lblActivityId" runat="Server" Text=' <%#Eval("ActivityID")%>' Width="10px"
                                                                                Visible="false"></asp:Label>
                                                                        </td>
                                                                        <td>
                                                                            <asp:TextBox ID="txtActivityName" runat="server" CssClass="form-control" Text='<%#Eval("ActivityDetails") %>'
                                                                                Width="100%" />
                                                                        </td>
                                                                    </tr>
                                                                </ItemTemplate>
                                                            </asp:ListView>
                                                            <div class="row">
                                                                <div class="col-md-12 form-horizontal uniformjs" style="margin-top: 5px;">
                                                                    <div class="form-group">
                                                                        <div class="col-md-6 ">
                                                                            <asp:Button ID="btnAddNewRowlvActivitylist" OnClick="btnAddNewRowlvActivitylist_Click" runat="server" CssClass="btn btn-primary" ToolTip="Add new row" Text="+" />
                                                                            <asp:Button ID="btnCancelAddNewRowlvActivitylist" OnClick="btnCancelAddNewRowlvActivitylist_Click" runat="server" Text="x" CssClass="btn btn-danger" ToolTip="Remove last row" />
                                                                        </div>
                                                                        <div class="col-md-6 text-right ">
                                                                            <asp:Label ID="lbllvActivitylistInfo" runat="server" Text="" ForeColor="Green" Visible="false"></asp:Label>
                                                                            <asp:Button ID="btnSavelvActivitylist" runat="server" Text="Save" Width="20%" CssClass="btn btn-primary" OnClick="btnSavelvActivitylist_Click" />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </asp:Panel>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>
                                    </div>
                                  
                                    <div class="panel panel-default" runat="server" visible="false">
                                        <div class="panel-heading">
                                            <h3 class="panel-title"><a class="accordion-toggle glyphicons right_arrow" style="text-decoration: none !important;" data-toggle="collapse" data-parent="#tabAccountAccordion" href="#collapse-2-1"><i></i>UpStream Application Dependency Details</a></h3>
                                        </div>
                                        <div id="collapse-2-1" class="panel-collapse collapse">
                                            <div class="panel-body">
                                                <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <asp:Panel ID="panel2" runat="server" Visible="true">

                                                            <asp:ListView ID="lvUpStreamDependencylist" runat="server" OnItemDataBound="lvUpStreamDependencylist_OnItemDataBound">
                                                                <LayoutTemplate>
                                                                    <table id="tblbia" class="table table-bordered table-striped table-white margin-bottom-none" width="100%">
                                                                        <thead>
                                                                            <tr>
                                                                                <th style="width: 2%">#
                                                                                </th>
                                                                                <th style="width: 13%">Upstream Dependent Application
                                                                                </th>
                                                                                <th style="width: 13%">Impacted
                                                                                </th>
                                                                                <th style="width: 12%">Input Data Source
                                                                                </th>
                                                                                <th style="width: 7%">Real Time Process
                                                                                </th>
                                                                                <th style="width: 15%">What is the input that these applications provide to the application under focus?
                                                                                </th>
                                                                                <th style="width: 7%">Is activity critical for BCM
                                                                                </th>
                                                                                <th style="width: 6%">BCM Scope
                                                                                </th>
                                                                                <th style="width: 3%">Supported?
                                                                                </th>
                                                                                <th style="width: 9%">if Yes RTO
                                                                                </th>
                                                                                <th style="width: 6%">IsCritical
                                                                                </th>
                                                                                <th style="width: 7%">If yes whether need to cover under DR
                                                                                </th>
                                                                                <th>Action
                                                                                </th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            <tr>
                                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </LayoutTemplate>
                                                                <ItemTemplate>
                                                                    <tr>
                                                                        <td>
                                                                            <%#Container.DataItemIndex+1 %>
                                                                            <asp:Label ID="lblRelationID" runat="Server" Text='<%#Eval("RelationID")%>' Visible="false"></asp:Label>
                                                                            <asp:Label ID="lblId" runat="Server" Text='<%#Eval("ID")%>' Visible="false"></asp:Label>
                                                                        </td>
                                                                        <td>

                                                                            <asp:DropDownList ID="ddlbusinessfunctions" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" AutoPostBack="false">
                                                                            </asp:DropDownList>
                                                                            <asp:Label ID="lblBusinessFunctionID" runat="server" Text='<%#Eval("OutBondAppID")%>' Visible="false"></asp:Label>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlRelationalImpact" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" Width="90%" AutoPostBack="false">
                                                                            </asp:DropDownList>
                                                                            <asp:Label ID="lblRelID" runat="server" Text='<%#Eval("RelTypeID")%>' Visible="false"></asp:Label>
                                                                        </td>
                                                                        <td>
                                                                            <asp:TextBox ID="txtInputDataSource" runat="server" Text='<%#Eval("DataSource")%>' CssClass="form-control"
                                                                                Width="96%" />
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlIsRealTimeProcess" runat="server" Width="85%" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("IsRealTime")%>'>
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:TextBox ID="txtAreaoffocus" runat="server" Width="100%" Text='<%#Eval("AreaOfFocus")%>' CssClass="form-control" />
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlCriticalForBCM" runat="server" Width="85%" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("CriticalForBCM")%>'>
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlBCMScope" runat="server" Width="90%" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("BCMScope")%>'>
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlSupported" runat="server" Width="85%" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("Supported")%>'>
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlRTO" runat="server" Width="100%" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;">
                                                                            </asp:DropDownList>
                                                                            <asp:Label ID="lblRTO" runat="server" Visible="false" Text='<%#Eval("RTO")%>'></asp:Label>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlIsCritical" runat="server" Width="96%" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("IsCritical")%>'>
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlDRRequired" runat="server" Width="90%" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("IsDRRequired")%>'>
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td align="center">
                                                                            <asp:ImageButton ID="lnkBtnDelete" runat="server"
                                                                                OnClick="lnkBtnDelete_Click" ImageUrl="../Images/icons/cross-circle.png" Width="14px"
                                                                                Style="padding-top: 7px; padding-bottom: 7px;" CausesValidation="false" ToolTip="Delete"></asp:ImageButton>
                                                                            <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="lnkBtnDelete"
                                                                                ConfirmText='<%# "Are you sure want to delete the relationship ? " %>' OnClientCancel="CancelClick">
                                                                            </TK1:ConfirmButtonExtender>
                                                                        </td>
                                                                    </tr>
                                                                </ItemTemplate>
                                                            </asp:ListView>
                                                            <div class="row">
                                                                <div class="col-md-12 form-horizontal uniformjs" style="margin-top: 5px;">
                                                                    <div class="form-group">
                                                                        <div class="col-md-6">
                                                                            <asp:Button ID="btnAddNewRowlvUpStreamDependencylist" OnClick="btnAddNewRowlvUpStreamDependencylist_Click" runat="server" CssClass="btn btn-primary"
                                                                                ToolTip="Add new row" Text="+" />
                                                                            <asp:Button ID="btnCancelAddNewRowlvUpStreamDependencylist" OnClick="btnCancelAddNewRowlvUpStreamDependencylist_Click" runat="server" Visible="false" Text="x" CssClass="btn btn-danger" ToolTip="Remove last row" />
                                                                        </div>
                                                                        <div class="col-md-6 text-right">
                                                                            <asp:Label ID="lbllvUpStreamDependencylistInfo" runat="server" Text="" ForeColor="Green" Visible="false"></asp:Label>
                                                                            <asp:Button ID="btnSaveUpStream" runat="server" Text="Save" Width="20%" OnClick="btnSaveUpStream_Click" CssClass="btn btn-primary" />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>


                                                        </asp:Panel>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>
                                    </div>
                                   
                                    <div class="panel panel-default" runat="server" visible="false">
                                        <div class="panel-heading">
                                            <h3 class="panel-title"><a class="accordion-toggle glyphicons right_arrow" style="text-decoration: none !important;" data-toggle="collapse" data-parent="#tabAccountAccordion" href="#collapse-3-1"><i></i>DownStream Application Dependency Details</a></h3>
                                        </div>
                                        <div id="collapse-3-1" class="panel-collapse collapse">
                                            <div class="panel-body">
                                                <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <asp:Panel ID="panel3" runat="server" Visible="true">

                                                            <asp:ListView ID="lvDownStreamDependencylist" runat="server" OnItemDataBound="lvDownStreamDependencylist_OnItemDataBound">
                                                                <LayoutTemplate>
                                                                    <table id="tblebia" class="table table-bordered table-striped table-white margin-bottom-none" width="100%">
                                                                        <thead>
                                                                            <tr>
                                                                                <th style="width: 2%">#
                                                                                </th>
                                                                                <th style="width: 10%">DownStream Dependent Application
                                                                                </th>
                                                                                <th style="width: 10%">Impacted
                                                                                </th>
                                                                                <th style="width: 12%">Input Data Source
                                                                                </th>
                                                                                <th style="width: 7%">Real Time Process
                                                                                </th>
                                                                                <th style="width: 29%">What is the input that these applications provide to the application under focus?
                                                                                </th>
                                                                                <th style="width: 10%">Is activity critical for BCM
                                                                                </th>
                                                                                <th style="width: 9%">BCM Scope
                                                                                </th>
                                                                                <th style="width: 3%">Supported?
                                                                                </th>
                                                                                <th style="width: 12%">if Yes RTO
                                                                                </th>

                                                                                <th>Action
                                                                                </th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            <tr>
                                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </LayoutTemplate>
                                                                <ItemTemplate>
                                                                    <tr>
                                                                        <td>
                                                                            <%#Container.DataItemIndex+1 %>
                                                                            <asp:Label ID="lblRelationID" runat="Server" Text='<%#Eval("RelationID")%>' Visible="false"></asp:Label>
                                                                            <asp:Label ID="lblId" runat="Server" Text='<%#Eval("ID")%>' Visible="false"></asp:Label>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlbusinessfunctions" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" Width="100%" AutoPostBack="false">
                                                                            </asp:DropDownList>
                                                                            <asp:Label ID="lblBusinessFunction" runat="server" Text='<%#Eval("InBondAppID")%>' Visible="false"></asp:Label>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlRelationalImpact" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" Width="90%" AutoPostBack="false">
                                                                            </asp:DropDownList>
                                                                            <asp:Label ID="lblRelID" runat="server" Text='<%#Eval("RelTypeID")%>' Visible="false"></asp:Label>
                                                                        </td>
                                                                        <td>
                                                                            <asp:TextBox ID="txtInputDataSource" runat="server" Width="100%" CssClass="form-control" Text='<%#Eval("DataSource")%>' />
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlIsRealTimeProcess" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("IsRealTime")%>'
                                                                                Width="100%">
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:TextBox ID="txtAreaoffocus" runat="server" Text='<%#Eval("AreaOfFocus")%>' CssClass="form-control" Width="100%" />
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlCriticalForBCM" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("CriticalForBCM")%>'>
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlBCMScope" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("BCMScope")%>'>
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlSupported" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;" SelectedValue='<%#Eval("Supported")%>'
                                                                                Width="100%">
                                                                                <asp:ListItem Text="Yes" Value="1"></asp:ListItem>
                                                                                <asp:ListItem Text="No" Value="0"></asp:ListItem>
                                                                            </asp:DropDownList>
                                                                        </td>
                                                                        <td>
                                                                            <asp:DropDownList ID="ddlRTO" runat="server" CssClass="col-md-12 padding-none-LR" Style="font-size: 11px;">
                                                                            </asp:DropDownList>
                                                                            <asp:Label ID="lblRTO" runat="server" Text='<%#Eval("RTO")%>' Visible="false"></asp:Label>
                                                                        </td>
                                                                       
                                                                        <td align="center">
                                                                            <asp:ImageButton ID="lnkBtnDelete" runat="server"
                                                                                ImageUrl="../Images/icons/cross-circle.png" Width="14px" OnClick="lnkBtnDelete_Click"
                                                                                Style="padding-top: 7px; padding-bottom: 7px;" CausesValidation="false" ToolTip="Delete"></asp:ImageButton>
                                                                            <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="lnkBtnDelete"
                                                                                ConfirmText='<%# "Are you sure want to delete the relationship ? " %>' OnClientCancel="CancelClick">
                                                                            </TK1:ConfirmButtonExtender>
                                                                        </td>
                                                                    </tr>
                                                                </ItemTemplate>
                                                            </asp:ListView>
                                                            <div class="row">
                                                                <div class="col-md-12 form-horizontal uniformjs" style="margin-top: 5px;">
                                                                    <div class="form-group">
                                                                        <div class="col-md-6">
                                                                            <asp:Button ID="btnAddNewRowlvDownStreamDependencylist" runat="server" OnClick="btnAddNewRowlvDownStreamDependencylist_Click" CssClass="btn btn-primary"
                                                                                ToolTip="Add new row" ValidationGroup="3" Text="+" />
                                                                            <asp:Button ID="btnCancelAddNewRowlvDownStreamDependencylist" runat="server" OnClick="btnCancelAddNewRowlvDownStreamDependencylist_Click"
                                                                                Visible="false" CssClass="btn btn-danger" ToolTip="Remove last row" Text="x"
                                                                                OnClientClick="return confirmation();" />
                                                                        </div>
                                                                        <div class="col-md-6 text-right">
                                                                            <asp:Label ID="lbllvDownStreamDependencylistInfo" runat="server" Text="" ForeColor="Green" Visible="false"></asp:Label>
                                                                            <asp:Button ID="btnSaveDownStreamDependencylist" OnClick="btnSaveDownStreamApp_Click" runat="server" Width="20%" Text="Save" CssClass="btn btn-primary" />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </asp:Panel>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>
                                    </div>
                                 
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h3 class="panel-title"><a class="accordion-toggle glyphicons right_arrow" style="text-decoration: none !important;" data-toggle="collapse" data-parent="#tabAccountAccordion" href="#collapse-4-1"><i></i>Qualitative Application BIA</a></h3>
                                        </div>
                                        <div id="collapse-4-1" class="panel-collapse collapse">
                                            <div class="panel-body">
                                                <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <div class="row">
                                                            <div class="col-md-12 form-horizontal uniformjs">
                                                                <div class="form-group">
                                                                    <div class="col-md-3">
                                                                        Select Impact Severity
                                                                    </div>
                                                                    <div class="col-md-9">
                                                                        <asp:DropDownList ID="ddlImactTypeQA" runat="server" CssClass="selectpicker col-md-4" data-style="btn-default" Width="20%" AutoPostBack="true" OnSelectedIndexChanged="ddlImactTypeQA_OnSelectedIndexChanged">
                                                                        </asp:DropDownList>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row" style="padding-bottom: 10px;">
                                                            <div class="col-md-12 form-horizontal uniformjs">
                                                                <telerik:RadGrid ID="RgApplicationBIAQualitative" runat="server" AllowPaging="False" OnItemDataBound="RgApplicationBIAQualitative_ItemDataBound"
                                                                    ShowFooter="true" AllowSorting="false" AllowAutomaticInserts="false" AllowAutomaticDeletes="false"
                                                                    AutoGenerateColumns="false" AllowAutomaticUpdates="false" AllowFilteringByColumn="false" Skin="Default" CssClass="table table-hover table-striped table-bordered table-condensed"
                                                                    AllowCustomPaging="false" CellSpacing="0" GridLines="None" Font-Size="Small">
                                                                    <MasterTableView DataKeyNames="ID,ImpactTypeID,ImpactID">
                                                                        <GroupByExpressions>
                                                                            <telerik:GridGroupByExpression>
                                                                                <SelectFields>
                                                                                    <telerik:GridGroupByField FieldName="ImpactTypeName" HeaderText="Type" />
                                                                                </SelectFields>
                                                                                <GroupByFields>
                                                                                    <telerik:GridGroupByField FieldName="ImpactTypeName" SortOrder="Ascending" />
                                                                                </GroupByFields>
                                                                            </telerik:GridGroupByExpression>
                                                                        </GroupByExpressions>
                                                                        <Columns>
                                                                            <telerik:GridTemplateColumn HeaderText="Impact Name" ItemStyle-Width="100px" HeaderStyle-Width="100px">
                                                                                <ItemTemplate>
                                                                                    <asp:Label ID="lblImpactName" runat="server" Text='<%# Eval("ImpactName") %>'></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:Label ID="lblOverAllImpact" runat="server" Font-Bold="true" Text='OverAllImpact'></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 2 Hrs" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTO2hrs" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo2hrs" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo2hrs" runat="server" Text='<%# Eval("Upto2Hours") %>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo2HrsOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo2HrsOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 4 Hrs" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo4hrs" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo4hrs" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo4hrs" runat="server" Text='<%# Eval("Upto4Hours") %>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo4HrsOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo4HrsOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 8 Hrs" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo8hrs" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo8hrs" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo8hrs" runat="server" Text='<%# Eval("Upto8Hours") %>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo8HrsOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo8HrsOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 12 Hrs" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo12hrs" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo12hrs" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo12hrs" runat="server" Text='<%# Eval("Upto12Hours") %>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo12HrsOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo12HrsOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 24 Hrs" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo24hrs" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo24hrs" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo24hrs" runat="server" Text='<%# Eval("Upto24Hours")%>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo24HrsOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo24HrsOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 48 Hrs" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo48hrs" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo48hrs" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo48hrs" runat="server" Text='<%# Eval("Upto48Hours")%>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo48HrsOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo48HrsOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 72 Hrs" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo72hrs" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo72hrs" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo72hrs" runat="server" Text='<%# Eval("Upto72Hours") %>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo72HrsOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo72HrsOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 1 Wk" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo1Week" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo1Week" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo1Week" runat="server" Text='<%# Eval("UpTo1Week") %>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo1WeekOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo1WeekOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 2 Wk's" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo2Weeks" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo2Weeks" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo2Weeks" runat="server" Text='<%# Eval("UpTo2Weeks")%>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo2WeeksOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo2WeeksOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 1 M'th" HeaderStyle-Font-Size="11px" ItemStyle-Width="90px" HeaderStyle-Width="90px">
                                                                                <ItemTemplate>
                                                                                    <asp:DropDownList ID="ddlUpTo1Month" runat="server" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" Width="130%" OnSelectedIndexChanged="SelectedIndexChanged_Rating">
                                                                                    </asp:DropDownList>
                                                                                    <asp:Label ID="lblUpTo1Month" runat="server" Text="*" Font-Size="11" ForeColor="Red"
                                                                                        Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblSelectedValueUpTo1Month" runat="server" Text='<%# Eval("UpTo1Month")%>'
                                                                                        Visible="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:TextBox ID="txtUpTo1MonthOI" runat="server" Visible="false"></asp:TextBox>
                                                                                    <asp:Label ID="lblUpTo1MonthOIM" runat="server"></asp:Label>
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Comments" HeaderStyle-Font-Size="11px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadTextBox ID="txtComment" runat="server" Text='<%# Eval("Comment") %>'
                                                                                        TextMode="MultiLine" Rows="1" Width="220px" />
                                                                                </ItemTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                        </Columns>
                                                                    </MasterTableView>
                                                                    <GroupingSettings ShowUnGroupButton="true"></GroupingSettings>
                                                                </telerik:RadGrid>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-12 form-horizontal uniformjs">
                                                                <div class="form-group">

                                                                    <div class="col-md-3">
                                                                        Calculated RTO (in hrs)
                                                                    </div>
                                                                    <div class="col-md-9">
                                                                        <asp:TextBox ID="txtCalculatedRTO" runat="server" Enabled="false" BackColor="White" CssClass="form-control"></asp:TextBox>
                                                                        <asp:Label ID="lblCalcualtedRTO" runat="server" Visible="false"></asp:Label>
                                                                    </div>
                                                                </div>
                                                                <div class="form-group">
                                                                    <div class="col-md-3">
                                                                        Calculated MTR (in hrs)
                                                                    </div>
                                                                    <div class="col-md-9">
                                                                        <asp:TextBox ID="txtCalculatedMTR" runat="server" Enabled="false" BackColor="White" CssClass="form-control"></asp:TextBox>
                                                                        <asp:Label ID="lblCalcualtedMTR" runat="server" Visible="false"></asp:Label>
                                                                    </div>
                                                                </div>
                                                                <div class="form-group">
                                                                    <div class="col-md-3"></div>
                                                                    <div class="col-md-9 text-right">
                                                                        <asp:Label ID="lblInfoQualitativeBIA" runat="server" Text="" ForeColor="Green" Font-Bold="false" Visible="false"></asp:Label>&nbsp;&nbsp;
                                                                        <asp:Button ID="btnSaveQualitativeBIA" runat="server" Text="Save " CssClass="btn btn-primary" OnClick="btnSaveQualitativeBIA_Click" Width="15%" />
                                                                        <asp:Button ID="btnQualitativeBIAClear" runat="server" Text="Cancel" CssClass="btn btn-default" OnClick="btnQualitativeBIAClear_Click" Width="15%"
                                                                            CausesValidation="false" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>
                                    </div>
                                  
                                    <div class="panel panel-default" runat="server" visible="false">
                                        <div class="panel-heading">
                                            <h3 class="panel-title"><a class="accordion-toggle glyphicons right_arrow" style="text-decoration: none !important;" data-toggle="collapse" data-parent="#tabAccountAccordion" href="#collapse-5-1"><i></i>Quantitative Application BIA</a></h3>
                                        </div>
                                        <div id="collapse-5-1" class="panel-collapse collapse">
                                            <div class="panel-body">
                                                <asp:UpdatePanel ID="UpdatePanel5" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <div class="row">
                                                            <div class="col-md-12 form-horizontal uniformjs">
                                                                <div class="form-group">
                                                                    <div class="col-md-3">
                                                                        Select Impact Severity
                                                                    </div>
                                                                    <div class="col-md-9">
                                                                        <asp:DropDownList ID="ddlImactTypeQN" runat="server" CssClass="selectpicker col-md-4" data-style="btn-default" Width="20%" AutoPostBack="true" OnSelectedIndexChanged="ddlImactTypeQN_OnSelectedIndexChanged">
                                                                        </asp:DropDownList>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row" style="padding-bottom: 10px;">
                                                            <div class="col-md-12 form-horizontal uniformjs">
                                                                <telerik:RadGrid ID="RgApplicationBIAQuantitative" runat="server" AllowPaging="false"
                                                                    ShowFooter="true" AllowSorting="false" AllowAutomaticInserts="false" AllowAutomaticDeletes="false"
                                                                    AutoGenerateColumns="false" AllowAutomaticUpdates="false" Skin="Default" AllowFilteringByColumn="false"
                                                                    AllowCustomPaging="false" CellSpacing="0" GridLines="None" CssClass="table-hover">
                                                                    <MasterTableView DataKeyNames="ID,ImpactTypeID,ImpactID">
                                                                        <GroupByExpressions>
                                                                            <telerik:GridGroupByExpression>
                                                                                <SelectFields>
                                                                                    <telerik:GridGroupByField FieldName="ImpactTypeName" HeaderText="Type" />
                                                                                </SelectFields>
                                                                                <GroupByFields>
                                                                                    <telerik:GridGroupByField FieldName="ImpactTypeName" SortOrder="Ascending" />
                                                                                </GroupByFields>
                                                                            </telerik:GridGroupByExpression>
                                                                        </GroupByExpressions>
                                                                        <Columns>
                                                                            <telerik:GridBoundColumn DataField="ImpactName" HeaderText="Impact" UniqueName="ImpactName"
                                                                                ItemStyle-Width="250px" HeaderStyle-Width="250px">
                                                                            </telerik:GridBoundColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 2 Hrs" ItemStyle-Width="80px" HeaderStyle-Width="60px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo2hrs" runat="server" Text='<%# Eval("Upto2Hours") == "" ? 0 : Eval("Upto2Hours") %>'
                                                                                        Width="100%" CssClass="form-control" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo2hrs" runat="server" Width="100%" CssClass="form-control" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 4 Hrs" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo4hrs" runat="server" CssClass="form-control" Text='<%# Eval("Upto4Hours") == "" ? 0 : Eval("Upto4Hours")%>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo4hrs" runat="server" Width="100%" CssClass="form-control" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 8 Hrs" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo8hrs" runat="server" CssClass="form-control" Text='<%# Eval("Upto8Hours") == "" ? 0 : Eval("Upto8Hours") %>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo8hrs" runat="server" CssClass="form-control" Width="100%" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 12 Hrs" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo12hrs" runat="server" CssClass="form-control" Text='<%# Eval("Upto12Hours") == "" ? 0 : Eval("Upto12Hours") %>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo12hrs" runat="server" Width="100%" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 24 Hrs" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo24hrs" runat="server" CssClass="form-control" Text='<%# Eval("Upto24Hours") == "" ? 0 : Eval("Upto24Hours")%>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo24hrs" CssClass="form-control" runat="server" Width="100%" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 48 Hrs" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo48hrs" runat="server" CssClass="form-control" Text='<%# Eval("Upto48Hours") == "" ? 0 : Eval("Upto48Hours") %>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo48hrs" runat="server" Width="100%" CssClass="form-control" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 72 Hrs" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo72hrs" runat="server" CssClass="form-control" Text='<%# Eval("Upto72Hours") == "" ? 0 : Eval("Upto72Hours") %>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo72hrs" runat="server" Width="100%" CssClass="form-control" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 1 Wk" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo1Week" runat="server" CssClass="form-control" Text='<%# Eval("Upto1Week") == "" ? 0 : Eval("Upto1Week")%>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo1Week" runat="server" CssClass="form-control" Width="100%" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 2 Wk's" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo2Week" runat="server" CssClass="form-control" Text='<%# Eval("Upto2Weeks") == "" ? 0 : Eval("Upto2Weeks") %>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo2Week" runat="server" Width="100%" CssClass="form-control" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Upto 1 M'th" ItemStyle-Width="80px" HeaderStyle-Width="80px">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadNumericTextBox ID="txtUpTo1month" runat="server" CssClass="form-control" Text='<%# Eval("Upto1Month") == "" ? 0 : Eval("Upto1Month") %>'
                                                                                        Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <telerik:RadNumericTextBox ID="fttxtUpTo1month" runat="server" CssClass="form-control" Width="100%" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                            <telerik:GridTemplateColumn HeaderText="Comments">
                                                                                <ItemTemplate>
                                                                                    <telerik:RadTextBox ID="txtComment" runat="server" CssClass="form-control" Text='<%# Eval("Comment") %>'
                                                                                        TextMode="MultiLine" Rows="1" Width="100%" />
                                                                                </ItemTemplate>
                                                                                <FooterTemplate>
                                                                                    <asp:Button ID="btnCalculateCost" runat="server" CssClass="btn btn-default" Text="Calculate Cost"
                                                                                        OnClick="btnCalculateCost_Click" />
                                                                                </FooterTemplate>
                                                                            </telerik:GridTemplateColumn>
                                                                        </Columns>
                                                                    </MasterTableView>
                                                                    <GroupingSettings ShowUnGroupButton="true"></GroupingSettings>
                                                                </telerik:RadGrid>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-12 form-horizontal uniformjs">
                                                                <div class="form-group">
                                                                    <div class="col-md-3">
                                                                    </div>
                                                                    <div class="col-md-9 text-right">
                                                                        <asp:Label ID="lblInfoQuantitaiveBIA" runat="server" Text="" ForeColor="Green" Font-Bold="false" Visible="false"></asp:Label>&nbsp;&nbsp;
                                                                            <asp:Button ID="btnSaveQuantitativeBIA" runat="server" Text="Save " CssClass="btn btn-primary" Width="15%" OnClick="btnSaveQuantitativeBIA_Click" />
                                                                        <asp:Button ID="btnCancelQuantitativeBIAClear" runat="server" Text="Cancel" CssClass="btn btn-default" Width="15%" OnClick="btnCancelQuantitativeBIAClear_Click" CausesValidation="false" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>
                                    </div>
                                   

                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>

    </div>
</asp:Content>
