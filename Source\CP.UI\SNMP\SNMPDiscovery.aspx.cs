﻿using CP.BusinessFacade;
using CP.Common.Shared;
using CP.Helper;
using SnmpSharpNet;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Net;
using System.Globalization;
using log4net;

namespace CP.UI
{
    public partial class SNMPDiscovery : BasePage//System.Web.UI.Page
    {

        private static readonly ILog _logger = LogManager.GetLogger(typeof(SNMPDiscovery));
        public static string IPAddress = string.Empty;

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Page.IsPostBack)
            {
                // *******.*******.0
                txtIPAddressFrom.Text = "************";
                txtIPAddressTo.Text = "**************";
            }

            // GetAgents();
        }
        protected void btnScanNetwork_Click(object sender, EventArgs e)
        {
            if (ValidateRequest("SNMPDiscovery", UserActionType.SNMPDiscovery))
            GetAgents();
           

        }
        private void GetAgents()
        {

            string strMask = string.Empty;
            int iStart = Convert.ToInt32(txtIPAddressFrom.Text.Substring(txtIPAddressFrom.Text.LastIndexOf(".") + 1));
            int iEnd = Convert.ToInt32(txtIPAddressTo.Text.Substring(txtIPAddressTo.Text.LastIndexOf(".") + 1));
            strMask = txtIPAddressTo.Text.Substring(0, txtIPAddressTo.Text.LastIndexOf(".") + 1);
            var dt = new DataTable();
            dt.Columns.Add("ID", typeof(int));
            dt.Columns.Add("IPAddress", typeof(string));
            dt.Columns.Add("HostName", typeof(string));
            dt.Columns.Add("Location", typeof(string));
            dt.Columns.Add("Type", typeof(string));
            dt.Columns.Add("ImageURL", typeof(string));


            dt.Rows.Clear();
            DateTime dtStartTime = DateTime.Now;
            GetAgents(iStart, iEnd, strMask, ref dt);
            DateTime dtEndTime = DateTime.Now;

            lblTime.Text = GetCompletionTime(dtStartTime.ToString(), dtEndTime.ToString());
            lblCount.Text = dt.Rows.Count.ToString();


            gvNetworkDevices.DataSource = dt;
            gvNetworkDevices.DataBind();

        }

        public static string GetCompletionTime(string strNotificationDate, string strCompletionDate)
        {
            StringBuilder sbtime = new StringBuilder();
            if (!(string.IsNullOrEmpty(strNotificationDate) || string.IsNullOrEmpty(strCompletionDate)))
            {
                DateTime dtNotificationDate = DateTime.Parse(strNotificationDate);
                DateTime dtCompletionDate = DateTime.Parse(strCompletionDate);

                TimeSpan diffdate = dtCompletionDate - dtNotificationDate;

                if (diffdate.Days > 0)
                {
                    sbtime.Append(diffdate.Days + " Day(s) ");
                }
                if (diffdate.Hours > 0)
                {
                    sbtime.Append(diffdate.Hours + " Hour(s) ");
                }
                if (diffdate.Minutes > 0)
                {
                    sbtime.Append(diffdate.Minutes + " Minute(s) ");
                }
                if (diffdate.Seconds > 0)
                {
                    sbtime.Append(diffdate.Seconds + " Second(s)");
                }
            }
            return sbtime.ToString();
        }
        public void GetAgents(int iStart, int iEnd, string Mask, ref DataTable dt)
        {
            for (int i = iStart; i <= iEnd; i++)
            {
                string strIpAddress = Mask + i.ToString(CultureInfo.InvariantCulture);

                string objectType = string.Empty;
                string ObjectTypeName = "Unknown";
                string strHostName = "NA";
                string strHostLocation = "NA";
                string strImpageURL = "icon-unknown-device";

                if (!IsHostAccessible(strIpAddress)) continue;

                try
                {
                    objectType = GetSnmp("*******.*******.0", strIpAddress, txtCommunity.Text);
                    if (objectType.Contains("1.3.6"))
                    {
                        ObjectTypeName = GetdeviceType2(objectType, ref strImpageURL);
                        strHostName = GetSnmp("*******.*******.0", strIpAddress, txtCommunity.Text);
                        strHostLocation = GetSnmp("*******.*******.0", strIpAddress, txtCommunity.Text);

                        DataRow dr = dt.NewRow();
                        dr["ID"] = dt.Rows.Count + 1;
                        dr["IPAddress"] = strIpAddress;
                        dr["HostName"] = strHostName;
                        dr["Location"] = strHostLocation;
                        dr["Type"] = ObjectTypeName;
                        dr["ImageURL"] = strImpageURL;
                        dt.Rows.Add(dr);
                    }

                    
                }
                catch (Exception ex)
                {
                    objectType = string.Empty;
                }
              
            }
        }
        public static bool IsHostAccessible(string hostNameOrAddress)
        {
            Ping ping = new Ping();

            PingReply reply = ping.Send(hostNameOrAddress, 100);
            return reply.Status == IPStatus.Success;
        }

        public string GetdeviceType2(string strGetString, ref string strImpageURL)
        {
            string strReturnString = string.Empty;
         
            switch (strGetString)
            {
                case "*******.*******.*******.3":
                    {
                        strImpageURL = "icon-aix";
                        strReturnString = "Aix";
                        break;
                    }
                case "*******.4.1.2021.250.10":
                    {
                        strImpageURL = "icon-linux";
                        strReturnString = "Linux";
                        break;

                    }
                case "*******.4.1.789.2.1":
                    {
                        strImpageURL = "icon-storageDR";
                        strReturnString = "Storage";
                        break;
                    }

                case "*******.4.1.1588.*******":
                    {
                        //old //*******.4.1.9.6.1.82.48.1
                        //new //*******.4.1.1588.*******
                        strImpageURL = "icon-switch";
                        strReturnString = "Switch";
                        break;
                    }

                case "*******.4.1.11.2.3.9.1":
                    {
                        strImpageURL = "icon-printer";
                        strReturnString = "Printer";
                        break;
                    } 
                case "*******.4.1.311.1.1.3.1.1":
                    {
                        strImpageURL = "icon-Windows";
                        strReturnString = "WorkStation";
                        break;
                    }
                case "*******.4.1.311.1.1.3.1.2":
                    {
                        strImpageURL = "server-icon";
                        strReturnString = "WindowsServer";
                        break;
                    }
                default:
                    {
                        strImpageURL = "icon-unknown-device";
                        strReturnString = "Unknown";
                        break;
                    }
            }

           return strReturnString;
        }

     
        private static string GetSnmp(string OID, string AgentIP, string Community)
        {
            StringBuilder sb = new StringBuilder();
            UdpTarget target = null;
            string strReturnString = string.Empty;

            try
            {
                // SNMP community name
                OctetString community = new OctetString(Community);

                // Define agent parameters class
                AgentParameters param = new AgentParameters(community);
                // Set SNMP version to 1 (or 2)
                param.Version = SnmpVersion.Ver1;
                // Construct the agent address object
                // IpAddress class is easy to use here because
                //  it will try to resolve constructor parameter if it doesn't
                //  parse to an IP address
                IpAddress agent = new IpAddress(AgentIP);

                // Construct target
                target = new UdpTarget((IPAddress)agent, 161, 2000, 1);
                // Pdu class used for all requests
                Pdu pdu = new Pdu(PduType.Get);

                pdu.VbList.Add(OID);

                // Make SNMP request
                SnmpV1Packet result = (SnmpV1Packet)target.Request(pdu, param);

                // If result is null then agent didn't reply or we couldn't parse the reply.
                if (result != null)
                {
                    // ErrorStatus other then 0 is an error returned by 
                    // the Agent - see SnmpConstants for error definitions
                    if (result.Pdu.ErrorStatus != 0)
                    {
                        // agent reported an error with the request
                        // sb.Append("Error in SNMP reply. Error: " + result.Pdu.ErrorStatus + " index: " + result.Pdu.ErrorIndex);
                        strReturnString = "SNMP is not Enabled";
                        _logger.Info("SNMP is not Enabled");
                    }
                    else
                    {
                        // Reply variables are returned in the same order as they were added
                        //  to the VbList
                        // sb.Append(GetNameByOid(result.Pdu.VbList[0].Oid.ToString()) + " : " + SnmpConstants.GetTypeName(result.Pdu.VbList[0].Value.Type) + " : " + result.Pdu.VbList[0].Value.ToString());
                        strReturnString = result.Pdu.VbList[0].Value.ToString();
                    }
                }
                else
                {
                    // sb.Append("No response received from SNMP agent.");
                }

            }
            catch (Exception ex)
            {
                strReturnString = "SNMP is not Enabled"; // sb.Append("Error Occured: " + ex.Message);
                _logger.Error("SNMP is not Enabled");
            }
            finally
            {
                if (target != null)
                    target.Close();
            }

            return strReturnString;
        }
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId,IPAddress);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId,IPAddress);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
    }

}