﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "HADRReplication", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class HADRReplication : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string HRole { get; set; }

        [DataMember]
        public string State { get; set; }

        [DataMember]
        public string SyncMode { get; set; }

        [DataMember]
        public string ConnectionStatus { get; set; }

        [DataMember]
        public string HeartbeatsMissed { get; set; }

        [DataMember]
        public string LocalHost { get; set; }

        [DataMember]
        public string LocalService { get; set; }

        [DataMember]
        public string RemoteHost { get; set; }

        [DataMember]
        public string RemoteService { get; set; }

        [DataMember]
        public string Timeout { get; set; }

        [DataMember]
        public string LogGap { get; set; }

        [DataMember]
        public string PrimaryInstanceName { get; set; }

        [DataMember]
        public string StandbyInstanceName { get; set; }

        #endregion Properties
    }
}