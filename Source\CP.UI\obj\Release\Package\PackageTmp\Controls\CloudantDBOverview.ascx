﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="CloudantDBOverview.ascx.cs" Inherits="CP.UI.Controls.CloudantDBOverview" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<script type="text/javascript">
    function CancelClick() {
        return false;
    }
</script>

<div class="row">
    <div class="col-md-5 col-md-push-7 text-right">
        <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Replication Name"></asp:TextBox>
        <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" OnClick="btnSearch_Click" />
    </div>
</div>
<hr />

<%--<table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" width="100%"
                    style="table-layout: fixed; margin-bottom: 0;">
                    <thead>
                        <tr>
                            <th style="width: 4%;" class="text-center">
                                <span>
                                     <img src="../Images/icons/replication_list.png" /></span>
                            </th>
                            <th style="width: 24%;">Replication Name 
                            </th> 
                            <th style="width: 20%;">Relationship
                            </th>
                            <th style="width: 12%;">PR -> DR Doc Id
                            </th>
                            <th style="width: 12%;"> DR -> PR Doc Id
                            </th>                             
                            <th runat="server" id="ActionHead" style="width: 8%;" class="text-center">Action
                            </th>
                        </tr>
                    </thead>
                </table>--%>

<%-- <table class="table table-striped table-bordered table-condensed" width="100%">
                    <tbody>--%>
<%--  <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />--%>
<%--<tr>
                            <td style="width: 4%;" class="text-center">
                                <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>--%>
<%--<%#Container.DataItemIndex+1 %>--%>
<%-- </td>
                            <td style="width: 24%;"> 
                                <asp:Label ID="lblName" runat="server" Text='<%# Eval("ReplicationBase.Name") %>'></asp:Label> 
                            </td> 
                            <td style="width: 20%;">
                                 <asp:Label ID="lblRelationship" runat="server" Text='<%# Eval("Relationship") %>'></asp:Label>
                            </td>
                            <td style="width: 22%;">
                                 <asp:Label ID="lblPrToDr" runat="server" Text='<%# Eval("PRToDRDocId") %>'></asp:Label>
                            </td>
                            <td style="width: 22%;"> 
                                 <asp:Label ID="lblDrToPr" runat="server" Text='<%# Eval("DRToPRDocId") %>'></asp:Label>
                            </td>                             
                            <td runat="server" id="Th1" style="width: 8%;" class="text-center">
                                  <asp:ImageButton ID="ibtnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                            ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                        <asp:ImageButton ID="ibtnDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                            ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                            </td>
                        </tr>
                    </tbody>
                </table>--%>

<div id="dvCloudant">

    <asp:ListView ID="lvCloudantDB" runat="server" OnPreRender="lvCloudantDB_PreRender" OnItemEditing="lvCloudantDB_ItemEditing" OnItemDeleting="lvCloudantDB_ItemDeleting">
        <LayoutTemplate>
            <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" width="100%">
                <thead>
                    <tr>
                        <th style="width: 4%;" class="text-center">
                            <span>
                                <img src="../Images/icons/replication_list.png" /></span>
                        </th>
                        <th style="width: 24%;">Replication Name 
                        </th>
                        <th style="width: 20%;">Relationship
                        </th>
                        <th style="width: 12%;">PR -> DR Doc Id
                        </th>
                        <th style="width: 12%;">DR -> PR Doc Id
                        </th>
                        <th runat="server" id="ActionHead" style="width: 8%;" class="text-center">Action
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                </tbody>
            </table>
        </LayoutTemplate>
        <EmptyDataTemplate>
            <div class="message warning align-center bold">
                <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
            </div>
        </EmptyDataTemplate>
        <ItemTemplate>
            <tr>
                <td style="width: 4%;" class="text-center">
                    <asp:Label ID="ID" runat="server" Text='<%# Eval("ReplicationBase.Id") %>' Visible="false"></asp:Label>
                    <%#Container.DataItemIndex+1 %>
                </td>
                <td style="width: 24%;">
                    <asp:Label ID="lblName" runat="server" Text='<%# Eval("ReplicationBase.Name") %>'></asp:Label>
                </td>
                <td style="width: 20%;">
                    <asp:Label ID="lblRelationship" runat="server" Text='<%# Eval("Relationship") %>'></asp:Label>
                </td>
                <td style="width: 22%;">
                    <asp:Label ID="lblPrToDr" runat="server" Text='<%#Eval("CloudantDBReplicationJob.PRToDRDocId")%>'></asp:Label>
                </td>
                <td style="width: 22%;">
                    <asp:Label ID="lblDrToPr" runat="server" Text='<%#Eval("CloudantDBReplicationJob.DRToPRDocId")%>'></asp:Label>
                </td>
                <td style="width: 6%;" class="text-center">
                    <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                        ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                        ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                </td>
                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                    ConfirmText='<%# "Are you sure want to delete " + Eval("ReplicationBase.Name") + " ? " %>' OnClientCancel="CancelClick">
                </TK1:ConfirmButtonExtender>
            </tr>
        </ItemTemplate>

    </asp:ListView>


           <div class="row">
            <div class="col-md-6">
                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvCloudantDB">
                    <Fields>
                        <asp:TemplatePagerField>
                            <PagerTemplate>
                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                <br />
                            </PagerTemplate>
                        </asp:TemplatePagerField>
                    </Fields>
                </asp:DataPager>
            </div>
            <div class="col-md-6 text-right">
                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvCloudantDB" PageSize="10">
                    <Fields>
                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                            NumericButtonCssClass="btn-pagination" />
                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                    </Fields>
                </asp:DataPager>
            </div>
        </div>

</div>
