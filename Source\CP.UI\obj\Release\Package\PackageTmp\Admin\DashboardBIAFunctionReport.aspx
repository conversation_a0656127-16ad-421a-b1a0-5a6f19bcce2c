﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="DashboardBIAFunctionReport.aspx.cs" Inherits="CP.UI.Admin.DashboardBIAFunctionReport"
    Title="Continuity Patrol : Business Functions Impact Analysis " %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Business Functions Impact Analysis </title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
   <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>



</head>
<body>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="sp" runat="server"></asp:ScriptManager>
        <div class="innerLR">
            <asp:UpdatePanel ID="udpmain" runat="server">
                <Triggers>
                    <asp:AsyncPostBackTrigger ControlID="btnShowDependency" />
                   
                </Triggers>
                <ContentTemplate>

                    <div class="widget margin-top">
                        <div class="widget-body">
                            <div class="row">
                                <div class="col-md-12 form-horizontal">
                                    <div class="form-group">
                                        <label class="col-md-1 control-label">Name</label>
                                        <div class="col-md-11 control-label">
                                            <asp:Label ID="lblfunctionName" CssClass="text-bold" runat="server" />
                                        </div>
                                    </div>
                                    <hr />
                                    <div class="form-group margin-bottom-none">
                                        <label class="col-md-1 control-label">Impact</label>
                                        <div class="col-md-2">
                                            <asp:DropDownList ID="ddlImactType" runat="server" CssClass="selectpicker col-md-12" data-style="btn-default">
                                            </asp:DropDownList>
                                        </div>
                                        <label class="col-md-1 control-label">RTO</label>
                                        <div class="col-md-2">
                                            <asp:DropDownList ID="ddlRTO" runat="server" CssClass="selectpicker col-md-12" data-style="btn-default">
                                            </asp:DropDownList>
                                        </div>

                                        <label class="col-md-1 control-label">Method</label>
                                        <div class="col-md-3">
                                            <asp:DropDownList ID="ddlMethods" runat="server" OnSelectedIndexChanged="ddlMethods_SelectedIndexChanged" 
                                                AutoPostBack="false" CssClass="selectpicker col-md-12" data-style="btn-default">
                                                <asp:ListItem Text="BIA Data" Value="1"></asp:ListItem>
                                                <asp:ListItem Text="Exponential" Value="2"></asp:ListItem>
                                                <asp:ListItem Text="Linear" Value="3"></asp:ListItem>
                                                <asp:ListItem Text="Common Log" Value="4"></asp:ListItem>
                                                <asp:ListItem Text="Natural Log" Value="5"></asp:ListItem>
                                            </asp:DropDownList>
                                        </div>

                                        <div class="col-md-2 text-right">
                                            <asp:Button ID="btnShowDependency" runat="server" Text="Show Impact" OnClick="btnShowDependency_Click"
                                                CssClass=" btn btn-primary" />
                                         
                                        </div>

                                    </div>
                                </div>
                                </div>
                            </div>

                        </div>
                   
                    <div class="row">
                        <div class="col-md-8">
                            <div class="widget " >
                                <div class="widget-head">
                                    <h4 class=" heading">Business Function Impact</h4>
                                </div>
                                <div class="widget-body">

                                    <asp:ListView ID="rptImpactDetails" runat="server">
                                        <LayoutTemplate>
                                            <table class="table table-bordered table-striped table-white margin-bottom-none">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 41.6667%">Business Function Name </th>
                                                        <th style="width: 33.3333%">Impact</th>
                                                        <th>RTO</th>
                                                    </tr>
                                                </thead>
                                            </table>
                                            <div style="overflow: auto; height: 125px;">
                                                <table class="table table-bordered table-striped table-white">
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </div>

                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td style="width: 41.6667%">
                                                    <asp:Label ID="Label1" runat="server"
                                                        Text='<%#Eval("ApplicationName").ToString()%>'></asp:Label></td>
                                                <td style="width: 33.3333%">
                                                    <asp:Label ID="Label2" runat="server" CssClass='<%#SetImpactColor(Eval("ImpactSummary"))%>'
                                                        Text='<%#Eval("ImpactSummary").ToString() %>'></asp:Label></td>
                                                <td>
                                                    <span class="icon-Time"></span>
                                                    <asp:Label ID="Label3" runat="server"
                                                        Text='<%#Eval("RTO").ToString() %>'></asp:Label></td>

                                            </tr>
                                           
                                        </ItemTemplate>
                                    </asp:ListView>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="widget-stats widget-stats-2 widget-stats-gray widget-stats-easy-pie" style="height: 220px;">
                                <h3>Total Financial Impact</h3>
                                <div class="informer" style="margin-top: 22px;">
                                    <a href="#" class="informera">
                                        <span class="fa fa-dollar text-primary text-large"></span>
                                        <asp:Label ID="lblCountOfImpact" runat="server" Text="" CssClass="text-large"></asp:Label>
                                    </a>

                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="relativeWrap">
                        <div class="widget">
                            <div class="widget-head">
                                <h4 class=" heading">Financial Impact by Category</h4>
                            </div>
                            <div class="widget-body text-center">
                                <div class="informer">
                                    <span class="informera">
                                        <asp:Label ID="lblHeading1" runat="server" Text="" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                        <asp:Label ID="lblCost1" runat="server" Text="" Font-Size="21px"></asp:Label>

                                        
                                    </span>
                                </div>
                                <div class="informer">
                                    <span class="informera">
                                        <asp:Label ID="lblHeading2" runat="server" Text="" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                        <asp:Label ID="lblCost2" runat="server" Text="" Font-Size="21px"></asp:Label>
                                    </span>
                                </div>
                                <div class="informer">
                                    <span class="informera">

                                        <asp:Label ID="lblHeading3" runat="server" Text="" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                        <asp:Label ID="lblCost3" runat="server" Text="" Font-Size="21px"></asp:Label>

                                        
                                    </span>
                                </div>
                                <div class="informer">
                                    <span class="informera">

                                        <asp:Label ID="lblHeading4" runat="server" Text="" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                        <asp:Label ID="lblCost4" runat="server" Text="" Font-Size="21px"></asp:Label>
                                        
                                    </span>
                                </div>
                                <div class="informer">
                                    <span class="informera">

                                        <asp:Label ID="lblHeading5" runat="server" Text="" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                        <asp:Label ID="lblCost5" runat="server" Text="" Font-Size="21px"></asp:Label>
                                    </span>
                                </div>
                                <div class="informer">
                                    <span class="informera">
                                        <asp:Label ID="lblHeading6" runat="server" Text="" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                        <asp:Label ID="lblCost6" runat="server" Text="" Font-Size="21px"></asp:Label>
                                    </span>
                                </div>
                                <div class="informer">
                                    <span class="informera">
                                        <asp:Label ID="lblHeading7" runat="server" Text="" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                        <asp:Label ID="lblCost7" runat="server" Text="" Font-Size="21px"></asp:Label>
                                    </span>
                                </div>
                                <div class="informer">
                                    <span class="informera">

                                        <asp:Label ID="lblHeading8" runat="server" Text="" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                        <asp:Label ID="lblCost8" runat="server" Text="" Font-Size="21px"></asp:Label>
                                        
                                    </span>
                                </div>
                                <div class="informer">
                                    <span class="informera">

                                        <asp:Label ID="lblHeading9" runat="server" Text="Employee Morale" CssClass="text"></asp:Label>
                                    </span>
                                    <span class="caption">
                                        <span class="fa fa-dollar text-larger"></span>
                                       <asp:Label ID="lblCost9" runat="server" Text="" Font-Size="21px"></asp:Label>
                                    </span>
                                </div>
                        
                            </div>


                        </div>
                    </div>
                    <div class="relativeWrap">
                        <div class="widget">
                            <div class="widget-head">
                                <h4 class=" heading">Financial Impact Summary</h4>
                            </div>
                            <div class="widget-body">
                                <div class="row">
                                    <div class="col-md-6 text-center">
                                        <label class="text-uppercase">
                                            Financial Impact Distribution</label>
                                        <telerik:RadHtmlChart runat="server" ID="RadChartFinancialImpDistribution" Width="490px" Height="400px"
                                            Transitions="true">
                                            <Appearance>
                                                <FillStyle BackgroundColor="White"></FillStyle>
                                            </Appearance>
                                            <ChartTitle>
                                                <Appearance Align="Center" BackgroundColor="White" Position="Top">
                                                </Appearance>
                                            </ChartTitle>
                                            <Legend>
                                                <Appearance BackgroundColor="White" Position="Bottom" Visible="true">
                                                </Appearance>
                                            </Legend>
                                            <PlotArea>
                                                <Appearance>
                                                    <FillStyle BackgroundColor="White"></FillStyle>
                                                </Appearance>
                                                <Series>
                                                    <telerik:PieSeries>
                                                        <LabelsAppearance Position="Center" DataFormatString="{0} %">
                                                        </LabelsAppearance>
                                                        <TooltipsAppearance DataFormatString="{0} %">
                                                        </TooltipsAppearance>
                                                        <SeriesItems>
                                                        </SeriesItems>
                                                    </telerik:PieSeries>
                                                </Series>
                                            </PlotArea>
                                        </telerik:RadHtmlChart>
                                    </div>
                                    <div class="col-md-6 text-center">
                                        <label class="text-uppercase ">
                                            Impact Type Distribution In <span class="fa fa-dollar text-regular"></span>
                                        </label>
                                        <telerik:RadHtmlChart runat="server" ID="RadChartImpTypeDistribution" Width="490px" Height="400px"
                                            Visible="true" Transitions="True">
                                            <PlotArea>
                                                <Series>
                                                </Series>
                                                <YAxis>
                                                    <TitleAppearance Text="" />
                                                    <MinorGridLines Visible="false" />
                                                    <MajorGridLines Visible="false" />
                                                </YAxis>
                                                <XAxis>
                                                    <MajorGridLines Visible="false" />
                                                    <MinorGridLines Visible="false" />
                                                </XAxis>
                                            </PlotArea>
                                            <ChartTitle>
                                            </ChartTitle>
                                            <Legend>
                                                <Appearance Position="Bottom" />
                                            </Legend>
                                        </telerik:RadHtmlChart>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="relativeWrap">
                        <div class="widget">
                            <div class="widget-head">
                                <h4 class=" heading">Application Impact Summary</h4>
                            </div>
                            <div class="widget-body">
                                <div class="row">
                                    <div class="col-md-6 text-center">
                                        <label class="text-uppercase ">
                                            Impact Analysis In <span class="fa fa-dollar text-regular"></span>
                                        </label>
                                        <telerik:RadHtmlChart runat="server" ID="RadChartImpactAnalysis" Width="490px" Height="400px">
                                            <ChartTitle Text="">
                                                <Appearance Align="left" Position="Top">
                                                </Appearance>
                                            </ChartTitle>
                                            <Legend>
                                                <Appearance Position="bottom" Visible="true">
                                                </Appearance>
                                            </Legend>
                                            <PlotArea>
                                                <Appearance>
                                                    <FillStyle></FillStyle>
                                                </Appearance>
                                                <Series>
                                                    <telerik:PieSeries>
                                                        <LabelsAppearance Position="Center" DataFormatString="{0} %">
                                                        </LabelsAppearance>
                                                        <TooltipsAppearance DataFormatString="{0} %">
                                                        </TooltipsAppearance>
                                                        <SeriesItems>
                                                        </SeriesItems>
                                                    </telerik:PieSeries>
                                                </Series>
                                            </PlotArea>
                                        </telerik:RadHtmlChart>
                                    </div>

                                    <div class="col-md-6 text-center">
                                        <label class="text-center text-uppercase">
                                            Financial Cost in <span class="fa fa-dollar text-regular"></span>
                                        </label>
                                        <telerik:RadHtmlChart runat="server" ID="RadHtmlChart1" Width="490px" Height="400px"
                                            Visible="true" Align="bottom" Legend-Appearance-Position="Bottom">
                                            <PlotArea>
                                                <Series>
                                                </Series>
                                                <XAxis>
                                                    <MinorGridLines Visible="false" />
                                                    <MajorGridLines Visible="false" />
                                                </XAxis>
                                                <YAxis>
                                                    <MinorGridLines Visible="false" />
                                                    <MajorGridLines Visible="false" />
                                                </YAxis>
                                            </PlotArea>
                                        </telerik:RadHtmlChart>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </ContentTemplate>
            </asp:UpdatePanel>
        </div>

    </form>

    <script src="../Script/jquery.modal.js" type="text/javascript"></script>

    <script src="../Script/bootstrap.min.js"></script>

    <script src="../Script/modernizr.js"></script>

    <script src="../Script/jquery.slimscroll.min.js"></script>

    <script src="../Script/jquery.cookie.js"></script>

    <script src="../Script/bootstrap-select.js"></script>

    <script src="../Script/bootstrap-select.init.js"></script>



    <script src="../Script/core.init.js"></script>


    <script type="text/javascript">
        $(document).ready(function () {
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('#businessimactdiv').slimScroll({
                height: '125px',
                alwaysVisible: true,
                railDraggable: true,
                allowPageScroll: false

            });
        });

        function pageLoad() {
            $('#businessimactdiv').slimScroll({
                height: '125px',
                alwaysVisible: true,
                railDraggable: true,
                allowPageScroll: false
            });
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
        }
    </script>
</body>
</html>
