﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ViewApplicationDependency.aspx.cs" 
    Inherits="CP.UI.SNMP.ViewApplicationDependency" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
     <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
     <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
            <img src="../Images/business-process-icon.png" alt="View Application Dependency ">
            Application Dependency</h3>
        
        <asp:UpdatePanel ID="updPnlScan" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="col-md-6 padding-none-LR">
                                    <label class="col-md-1 control-label  padding-none-LR" style="margin-top: 5px;">Server</label>
                                    
                                    <div class="col-md-8 padding-none-LR">

                                        <input name="inIPAddress" type="text" id="inIPAddress" value="-Select Server Name-" onkeypress="return isNumberKey(event)" runat="server" class="form-control absoluteWrap" CssClass="chosen-select col-md-9"
                                            onchange="DropDownIndexClear('ddlServersList');" style="width: 196px; height: 30px; z-index: 2;" />

                                        <select name="ddlServersList" id="ddlServersList"
                                            onchange="DropDownTextToBox(this,'ctl00_cphBody_inIPAddress');" style="width: 226px; height: 30px;">
                                        </select>
                                        <asp:Button ID="btnscan" runat="server" CssClass="btn btn-primary pull-right margin-right" Style="margin-top: -2px" Text="Find Dependency" OnClick="btnScanNetwork_Click" />
                                    </div>
                                </div>
                               
                                <div class="col-md-5 col-md-push-1 margin-top">
                                    <div class="col-md-5">
                                        <span class="count-icon vertical-sub"></span>
                                        Dependent Hosts : 
                               <asp:Label ID="lblCount" runat="server" CssClass="text-danger margin-right" Text=""></asp:Label>
                                    </div>
                                    <div class="col-md-7">
                                        <span class="icon-Time"></span>
                                        Discovery Time :  
                               <asp:Label ID="lblTime" runat="server" Text="" CssClass="text-danger"></asp:Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr class="margin-bottom-none" style="margin: 5px 0;" />
                        <div class="text-right">
                            <asp:Label ID="lblNoResultFound" CssClass="text-left" runat="server" Text="" ForeColor="Red" Visible="false"></asp:Label>
                        </div>
                        <div id="body">
                        </div>

                    </div>
                </div>
                <asp:UpdateProgress ID="UpdateProgress1" AssociatedUpdatePanelID="updPnlScan" runat="server">
                    <ProgressTemplate>
                        <div id="imgLoading" class="loading-mask">
                            <span>Discovering...</span>
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
            </ContentTemplate>
        </asp:UpdatePanel>

    </div>
    <input type="hidden" id="hdnHostUserName" runat="server" />
    <input type="hidden" id="hdnHostUserPwd" runat="server" />
    <input type="hidden" id="hdnOsType" runat="server" />
    <input type="hidden" id="hdnSelectedIndexValue" runat="server" />

    <script src="../script/d3.min.js"></script>
    <script src="../Script/appDependency.js" type="text/javascript"></script>
</asp:Content>
