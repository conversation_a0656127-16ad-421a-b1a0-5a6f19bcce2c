﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "AccessManagerCustom", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class OracleCloudDataGuard_WorkRequestStatusLatest : BaseEntity
    {
        public string PRWorkRequestType { get; set; }
        public string DRWorkRequestType { get; set; }
        public string PRWorkRequestLatestStartTime { get; set; }
        public string DRWorkRequestLatestStartTime { get; set; }
        public string PRWorkRequestLatestEndTime { get; set; }
        public string DRWorkRequestLatestEndTime { get; set; }
        public string PRWorkRequestLatestStatus { get; set; }
        public string DRWorkRequestLatestStatus { get; set; }
        public string PRWorkRequestLatestErrorLog { get; set; }
        public string DRWorkRequestLatestErrorLog { get; set; }
    }
}
