﻿using System;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using log4net;
using System.Net;

namespace CP.UI.Controls
{
    public partial class EC2DataSync : ReplicationControl
    {

        #region Variable
        private TextBox _txtReplicationName = new TextBox();
        private TextBox _txtAwsKey = new TextBox();
        private TextBox _txtAwsServerKey = new TextBox();
        private TextBox _txtRegionEndPointe = new TextBox();
        private TextBox _txtEC2InstaceID = new TextBox();
        private TextBox _txtS3BucketName = new TextBox();
        private TextBox _txtSourceDataPath = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        private DropDownList _ddlPushPull = new DropDownList();
        private EC2S3DataSyncReplication _EC2S3DataSyncReplication = null;
        private readonly ILog _logger = LogManager.GetLogger(typeof(EC2DataSync));
        public static string IPAddress = string.Empty;

        public EC2S3DataSyncReplication CurrentEntity
        {
            get { return _EC2S3DataSyncReplication ?? (_EC2S3DataSyncReplication = new EC2S3DataSyncReplication()); }
            set
            {
                _EC2S3DataSyncReplication = value;
            }
        }
        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }
        public TextBox AwsKey
        {
            get
            {
                _txtAwsKey = Parent.FindControl("txtAwsKey") as TextBox;
                return _txtAwsKey;
            }
            set { _txtAwsKey = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }
        public DropDownList PushPull
        {
            get
            {
                _ddlPushPull = Parent.FindControl("ddlPushPull") as DropDownList;
                return _ddlPushPull;
            }
            set
            {
                _ddlPushPull = value;
            }
        }

        public TextBox AwsServerKey
        {
            get
            {
                _txtAwsServerKey = Parent.FindControl("txtAwsServerKey") as TextBox;
                return _txtAwsServerKey;
            }
            set { _txtAwsServerKey = value; }
        }

        public TextBox RegionEndPointe
        {
            get
            {
                _txtRegionEndPointe = Parent.FindControl("txtRegionEndPointe") as TextBox;
                return _txtRegionEndPointe;
            }
            set { _txtRegionEndPointe = value; }
        }

        public TextBox EC2InstaceID
        {
            get
            {
                _txtEC2InstaceID = Parent.FindControl("txtEC2InstaceID") as TextBox;
                return _txtEC2InstaceID;
            }
            set { _txtEC2InstaceID = value; }
        }

        public TextBox S3BucketName
        {
            get
            {
                _txtS3BucketName = Parent.FindControl("txtS3BucketName") as TextBox;
                return _txtS3BucketName;
            }
            set { _txtS3BucketName = value; }
        }

        public string MessageInitials
        {
            get { return "EC2S3DataSync Replication"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion Variable
        //protected void Page_Load(object sender, EventArgs e)
        //{

        //}

        public override void PrepareView()
        {

            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST 
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

            txtAwsKey.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtAwsKey.ClientID + ")");
            txtAwsServerKey.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtAwsServerKey.ClientID + ")");
            txtRegionEndPoint.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtRegionEndPoint.ClientID + ")");
            txtEC2InstaceID.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtEC2InstaceID.ClientID + ")");
            txtS3BucketName.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtS3BucketName.ClientID + ")");
            txtSourceDataPath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtSourceDataPath.ClientID + ")");

            Session.Remove("CurrentEC2S3DataSync");

            LoadData();

        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }


        private void LoadData()
        {
            if (CurrentEC2S3DataSyncReplication != null)
            {
                CurrentEntity = CurrentEC2S3DataSyncReplication;

                Session["CurrentEC2S3DataSync"] = CurrentEntity;
                txtAwsKey.Text = CurrentEC2S3DataSyncReplication.AWSKey;
                txtAwsServerKey.Text = CurrentEC2S3DataSyncReplication.AWSSecretKey;
                txtRegionEndPoint.Text = CurrentEC2S3DataSyncReplication.RegionEndPoint;

                txtEC2InstaceID.Text = CurrentEC2S3DataSyncReplication.EC2InstanceId;
                txtS3BucketName.Text = CurrentEC2S3DataSyncReplication.S3BucketName;
                txtSourceDataPath.Text = CurrentEC2S3DataSyncReplication.SourceDataPath;
                ddlPushPull.SelectedValue = Convert.ToString(CurrentEC2S3DataSyncReplication.IsPullPushMechanism);

                Save.Text = "Update";
            }
        }
        protected void SaveRepClick(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                currentTransactionType = TransactionType.Save;

            else if (buttionText.Contains(" update "))
                currentTransactionType = TransactionType.Update;

            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;

            if (Page.IsValid)
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                    returnUrl = ReturnUrl;


                try
                {
                    //if (ValidateRequest("EC2DataSync", UserActionType.CreateReplicationComponent))
                    //if (currentTransactionType != TransactionType.Undefined && ValidateRequest("EC2DataSync", UserActionType.CreateReplicationComponent))
                    if (currentTransactionType != TransactionType.Undefined)
                    {
                        BuildEntities();
                        StartTransaction();
                        SaveEditor();
                        EndTransaction();

                        string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));

                        Save.Enabled = false;
                    }
                }
                catch (CpException ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);

                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, Page);
                    }
                }
                if (returnUrl.IsNotNullOrEmpty())
                {
                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                    Helper.Url.Redirect(secureUrl);
                }
            }
        }
        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }
        private void BuildEntities()
        {
            if (Session["CurrentEC2S3DataSync"] != null)
            {
                CurrentEntity = (EC2S3DataSyncReplication)Session["CurrentEC2S3DataSync"];

            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.AWSKey = txtAwsKey.Text;
            CurrentEntity.AWSSecretKey = txtAwsServerKey.Text;
            CurrentEntity.RegionEndPoint = txtRegionEndPoint.Text;
            CurrentEntity.EC2InstanceId = txtEC2InstaceID.Text;
            CurrentEntity.S3BucketName = txtS3BucketName.Text;
            CurrentEntity.SourceDataPath = txtSourceDataPath.Text;

            CurrentEntity.IsPullPushMechanism = (ddlPushPull.SelectedValue == "1" ? 1 : 0);



            // CurrentEntity.UserId =Convert.ToString(LoggedInUserId);
        }


        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
                return false;


            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }
        private void SaveEditor()
        {
            try
            {

                if (Session["CurrentEC2S3DataSync"] == null)
                {
                    CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                    CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.AddEC2S3DataSyncreplication(CurrentEntity);
                    ActivityLogger.AddLog(LoggedInUserName, "EC2S3DataSync", UserActionType.CreateReplicationComponent, "The EC2S3DataSync Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
                }
                else if (Session["CurrentEC2S3DataSync"] != null)
                {
                    CurrentEntity.Id = CurrentEC2S3DataSyncReplication.Id;
                    CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.UpdateEC2S3DataSyncReplication(CurrentEntity);
                    ActivityLogger.AddLog(LoggedInUserName, "EC2S3DataSync", UserActionType.UpdateReplicationComponent, "The Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
                }
            }
            catch (CpException ex)
            {
                if (ex != null)
                {

                    _logger.Error("CP exception while loading MSSqlDoubleTakeConfig in  SaveEditor method on MSSqlDoubleTakeConfig page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        _logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                ExceptionManager.Manage(ex);
            }
        }
    }
}