﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Code.Base;
using CP.UI.Controls;


namespace CP.UI.Controls
{
    public partial class ActiveDirectoryConfig : ReplicationControl
    {

        #region Variable

        TextBox _txtReplicationName = new TextBox();
        DropDownList _ddlReplicationType = new DropDownList();
        DropDownList _ddlSiteId = new DropDownList();

        private ActiveDirectory _activeDirectory;

        #endregion

        #region Properties

        public ActiveDirectory CurrentEntity
        {
            get { return _activeDirectory ?? (_activeDirectory = new ActiveDirectory()); }
            set
            {
                _activeDirectory = value;
            }
        }


        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "Active Directory Replication"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion

        #region Method


        public void PrepareEditView()
        {
            //CurrentEntity = CurrentMSSqlDoubletek;
            //Session["DoubleTake"] = CurrentEntity;
            //Session.Remove("PreviousItem");
            if (CurrentActiveDirectoryReplication != null && CurrentActiveDirectoryReplication.Id > 0)
            {
                txtTargetName.Text = CurrentEntity.TargetName;
                txtScopeName.Text = CurrentEntity.ScopeName;
                btnSave.Text = "Update";
            }
        }


        public void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddActiveDirectory(CurrentEntity);//Facade.AddActiveDirectory(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "Active Directory", UserActionType.CreateReplicationComponent, "The Active Directory Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);

            }
            else
            {
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateActiveDirectory(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "Active Directory", UserActionType.UpdateReplicationComponent, "The Active Directory Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        public void BuildEntities()
        {
            if (Session["ActiveDirectory"] != null)
            {
                CurrentEntity = (ActiveDirectory)Session["ActiveDirectory"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.TargetName = txtTargetName.Text;
            CurrentEntity.ScopeName = txtScopeName.Text;
            // CurrentEntity.ProtectionMode = txtProtection.Text;
        }

        public override void PrepareView()
        {
            txtTargetName.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtTargetName.ClientID + ")");
            txtScopeName.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtScopeName.ClientID + ")");
            Session.Remove("ActiveDirectory");
            LoadData();
            PrepareEditView();

        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        private void LoadData()
        {
            if (CurrentActiveDirectoryReplication != null)
            {
                CurrentEntity = CurrentActiveDirectoryReplication;
                Session["ActiveDirectory"] = CurrentEntity;
                txtTargetName.Text = CurrentActiveDirectoryReplication.TargetName; //CurrentMSSqlDoubletek.JobName;
                txtScopeName.Text = CurrentActiveDirectoryReplication.ScopeName;//CurrentMSSqlDoubletek.JobType;
                // txtProtection.Text = CurrentDataGuard.ProtectionMode;
                // btnSave.Text = "Update";

            }
        }

        #endregion

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }
            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            if (Page.IsValid)
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }

                try
                {
                   
                        StartTransaction();
                        BuildEntities();
                        SaveEditor();
                        //ReplicationName.Text = string.Empty;
                        CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
                        EndTransaction();

                        string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                currentTransactionType));
                        btnSave.Enabled = false;
                    
                }
                catch (CpException ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                    {
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);
                    }
                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, Page);
                    }
                }
                if (returnUrl.IsNotNullOrEmpty())
                {
                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                    Helper.Url.Redirect(secureUrl);
                }
            }
        }
    }
}