﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "EMCProtectionPolicy", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class EMCProtectionPolicyStatus : BaseEntity
    {
        #region Properties


        [DataMember]
        public int InfraObjectID { get; set; }

        [DataMember]
        public string PolicyName { get; set; }

        [DataMember]
        public int AssetCount { get; set; }


        [DataMember]
        public string LastRunStatus { get; set; }

        [DataMember]
        public string State { get; set; }

        #endregion Properties
    }
    public class EMCProtectionPolicyLog : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectID { get; set; }

        [DataMember]
        public string PolicyName { get; set; }

        [DataMember]
        public int AssetCount { get; set; }


        [DataMember]
        public string LastRunStatus { get; set; }

        [DataMember]
        public string State { get; set; }

        #endregion Properties
    }
    public class EMCProtectionPolicyLogJobStatus : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectID { get; set; }
        [DataMember]
        public string JobId { get; set; }
        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public string PolicyName { get; set; }
        [DataMember]
        public int Assets { get; set; }

        [DataMember]
        public string JobType { get; set; }
        [DataMember]
        public string StartTime { get; set; }


        #endregion Properties
    }



    public class AllProtectionPolicyJobStatus : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }
        [DataMember]
        public string JobStatus { get; set; }
        [DataMember]
        public int JobCount { get; set; }


        #endregion Properties
    }
    public class ProtectionPolicyMonitorJobStatus : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }
        [DataMember]
        public string JobID { get; set; }
        [DataMember]
        public string Status { get; set; }
        [DataMember]
        public string Description { get; set; }
        [DataMember]
        public string PolicyName { get; set; }
        [DataMember]
        public int Assets { get; set; }
        [DataMember]
        public string JobType { get; set; }
        [DataMember]
        public string StartTime { get; set; }


        #endregion Properties
    }
    public class ProtectionPolicyMonitorStatus : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }
        [DataMember]
        public string PolicyName { get; set; }
        [DataMember]
        public int AssetCount { get; set; }
        [DataMember]
        public string LastRunStatus { get; set; }
        [DataMember]
        public string State { get; set; }


        #endregion Properties
    }

}