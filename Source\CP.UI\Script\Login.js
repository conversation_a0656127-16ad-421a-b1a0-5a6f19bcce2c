﻿var IsUserNameEncrypted = false;
var IsPasswordEncrypted = false;
$(document).ready(function () {

    binddropdown();
    new MaskedPassword(document.getElementById("ctl00_cphBody_Password"), '\u25CF');
});

function binddropdown() {
    $.ajax({
        type: "POST",
        url: "Login.aspx/DiscoverDomains",
        data: "{}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function Success(data) {
            getBindValue(data.d);
        }
    });
}
function cleartext() {
   
    var input = document.createElement('input');
    input.value = $('[id$=UserEncrypt]').val();
    if (input.value != "" && input.value != null && $('[id$=ctl00_cphBody_UserName]').val() != "") {
        $('[id$=ctl00_cphBody_UserName]').val(getOrignalData(input, $('#ctl00_cphBody_hdfStaticGuid').val()));
    }
    $('[id$=UserEncrypt]').val("");
    $('[id$=txtcaptcha]').val("");
    $('[id$=ctl00_cphBody_Password]').val("");
    $('[id$=passHidden]').val("");
    $('[id$=ctl00_cphBody_PassEncyptHidden]').val("");
}


//function getBindValue(value) {

//    var data = value.split(":");
//    for (var i = 0; i < data.length; i++) {

//        var volume = data[i].split(",");
//        var text = volume[0];
//        //   var html = '<tr><td><input type="text" id="' + text + '" </td></tr>';

//        // jQuery('[id$=combobox1]').append("<option value='" + html + "</option>");
//        jQuery(function () {
//            jQuery('[id$=combobox1]').combobox([
//                    text
//            ]);
//            $('[id$=combobox1]').val(volume[0]);

//        });

//        //   $("[id$=ctl00_cphBody_combobox1] option:contains(" + text + ")").attr('selected', true);
//        //  $("[id$=combobox1]).combobox(text[0]);

//    }




//}

function getBindValue(value) {
    var domain = [];
    var data = value.split(",");
    for (var i = 0; i < data.length; i++) {

        var volume = data[i].split(",");
        var text = volume[0];
        domain.push(text)
        //   var html = '<tr><td><input type="text" id="' + text + '" </td></tr>';

        // jQuery('[id$=combobox1]').append("<option value='" + html + "</option>");
        //jQuery(function () {
        //    jQuery('[id$=combobox1]').combobox([
        //            text
        //    ]);
        //    $('[id$=combobox1]').val(volume[0]);

        //});
        jQuery(function () {
            jQuery('[id$=combobox1]').combobox(
               domain);
        });

        //   $("[id$=ctl00_cphBody_combobox1] option:contains(" + text + ")").attr('selected', true);
        //  $("[id$=combobox1]).combobox(text[0]);

    }




}



var base64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef" +
   "ghijklmnopqrstuvwxyz0123456789+/=";

function encode64(input) {
    var output = "";
    var ch1, ch2, ch3, enc1, enc2, enc3, enc4;
    var i = 0;

    do {
        ch1 = input.charCodeAt(i++);
        ch2 = input.charCodeAt(i++);
        ch3 = input.charCodeAt(i++);

        enc1 = ch1 >> 2;
        enc2 = ((ch1 & 3) << 4) | (ch2 >> 4);
        enc3 = ((ch2 & 15) << 2) | (ch3 >> 6);
        enc4 = ch3 & 63;

        if (isNaN(ch2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(ch3)) {
            enc4 = 64;
        }

        output = output + base64.charAt(enc1) + base64.charAt(enc2) +
          base64.charAt(enc3) + base64.charAt(enc4);
        ch1 = ch2 = ch3 = "";
        enc1 = enc2 = enc3 = enc4 = "";
    } while (i < input.length);

    return output;
}



function generateCode() {
    var passCode = $('[id$=passHidden]').val();
    var UserCode = $('[id$=ctl00_cphBody_UserName]').val();
    var passencode = "";
    var Userencode = "";
    if (passCode.trim().length > 0 && IsPasswordEncrypted == false) {
        passencode = encode64(passCode);
        $('[id$=ctl00_cphBody_Password]').val("\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF\u25CF");
        $('[id$=passHidden]').val(passencode);
        $('[id$=ctl00_cphBody_PassEncyptHidden]').val(passencode);
        IsPasswordEncrypted = true;
    }
    if (UserCode.trim().length > 0 && IsUserNameEncrypted == false) {
        //Userencode = encode64(UserCode);
        Userencode = UserCode;
        $('[id$=UserEncrypt]').val(Userencode);
        $('[id$=ctl00_cphBody_UserName]').val(Userencode);
        IsUserNameEncrypted = true
    }
}

function clearControlData(control) {
    $('[id$=ctl00_cphBody_Password]').val("");
    $('[id$=passHidden]').val("");
    $('[id$=ctl00_cphBody_PassEncyptHidden]').val("");

}

function getUserNameHash(control) {
    $('[id$=ctl00_cphBody_UserEncrypt]').val(genrateUserNameHash(control, $('#ctl00_cphBody_hdfStaticGuid').val()));
}

function getPasswordHash(control) {
    var strData = genrateUserNameHash(document.getElementById('passHidden'), $('#ctl00_cphBody_hdfStaticGuid').val());
    $('[id$=ctl00_cphBody_PassEncyptHidden]').val(strData);
    $('[id$=ctl00_cphBody_Password]').val(strData);
    document.getElementById('passHidden').value = strData;
}

function checkKey(event) {

    var isFirefox = typeof InstallTrigger !== 'undefined';
    if (isFirefox == true) {
        var x = event.which || event.keyCode;
        if (x != null && x !== undefined) {
            if (x == 13 && document.activeElement.id == "ctl00_cphBody_Password") {
                getPasswordHash($('[id$=ctl00_cphBody_Password]'));
            }
        }
    }
    else {
        if (window.event.keyCode == 13 && document.activeElement.id == "ctl00_cphBody_Password") {
            getPasswordHash($('[id$=ctl00_cphBody_Password]'));
        }
    }
}


