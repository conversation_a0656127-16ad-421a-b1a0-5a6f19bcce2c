﻿using SnmpSharpNet;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;
using System.Xml.Linq;
using CP.ExceptionHandler;
using System.Web.Services;
using CPDiscovery;
using System.ComponentModel;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using System.Security.Cryptography;
using CP.Common.Shared;
using log4net;
using System.Configuration;
using CP.Helper;

namespace CP.UI.SNMP
{
    public partial class ViewApplicationDiscovery : BasePage   //  System.Web.UI.Page
    {
        #region Variables
        private readonly ILog _logger = LogManager.GetLogger(typeof(ViewApplicationDiscovery));

        static IFacade _facade = new Facade();
        List<CPDiscovery.DiscoveryResult> discoveryResult = null;

        private static StringBuilder buildNodeRelSb = null;
        private static StringBuilder jsonNodeRelSb = null;
        private static DataTable discoveryResultTable = null;

        private static string parentNode = "Infra";
        public static string inputString = string.Empty;
        public static string jsonString = string.Empty;
        public static string finalJsonString = string.Empty;

        public static int currentNode = 0;
        public static string nameStr = "name", childStr = "children", levelStr = "level", colorRedStr = "rgb(0, 100, 0)";
        private static bool hasChild = false;
        private static bool first = true;
        private static int firstSplitCount = 0;
        private static string osName, logo = "logo", allOSAnsApp = "All";

        #endregion

        #region Events

        /// <summary>
        /// Page load event
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-03/11/2014</author>
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Page.IsPostBack)
            {
                inOsName.Value = allOSAnsApp;
                inApp.Value = allOSAnsApp;
                PopulateDiscoveryProfiles(ddlScannedHost, true);
            }
        }

        public override void PrepareView()
        {  
            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 
        }

        /// <summary>
        /// When clicked scans given range of IP addresses
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-03/11/2014</author>
        protected void btnScanNetwork_Click(object sender, EventArgs e)
        {
            try
            {
                _logger.Info(Environment.NewLine);
                _logger.Info("======Start of Application discovery======");
                _logger.Info(Environment.NewLine);

                inOsName.Disabled = inApp.Disabled = false;
                inIPAddressFrom.Disabled = inIPAddressTo.Disabled = false;
                spnNewhost.Visible = false;
                ddlScannedHost.SelectedIndex = 0;
                DateTime dtStartTime = DateTime.Now;
                if (!string.IsNullOrEmpty(inIPAddressFrom.Value) && !string.IsNullOrEmpty(inIPAddressTo.Value))
                {
                    _logger.Info("Calling PerformNMapDiscovery method with following parameters");
                    _logger.Info(Environment.NewLine);
                    _logger.Info("StartIP:--" + inIPAddressFrom.Value);
                    _logger.Info(Environment.NewLine);
                    _logger.Info("EndIP:--" + inIPAddressTo.Value);
                    _logger.Info(Environment.NewLine);
                    _logger.Info("NmapPath:--" + ConfigurationManager.AppSettings["NmapPath"]);
                    _logger.Info(Environment.NewLine);

                    var osFilter = !string.IsNullOrEmpty(inOsName.Value) ? inOsName.Value.ToLower() : allOSAnsApp.ToLower();
                    var AppFilter = !string.IsNullOrEmpty(inApp.Value) ? inApp.Value.ToLower() : allOSAnsApp.ToLower();

                    _logger.Info("OSFilter:--" + osFilter);
                    _logger.Info(Environment.NewLine);
                    _logger.Info("ApplicationFilter:--" + AppFilter);
                    _logger.Info(Environment.NewLine);

                    string disProfileName = GetTimestamp(DateTime.Now);

                    //discoveryResult = Discovery.PerformNMapDiscovery(inIPAddressFrom.Value, inIPAddressTo.Value, @"C:\nmap-6.47-win32\nmap-6.47", 300000, !string.IsNullOrEmpty(inOsName.Value) ? inOsName.Value.ToLower() : allOSAnsApp.ToLower(), !string.IsNullOrEmpty(inApp.Value) ? inApp.Value.ToLower() : allOSAnsApp.ToLower());
                    discoveryResult = Discovery.PerformNMapDiscovery(inIPAddressFrom.Value, inIPAddressTo.Value, ConfigurationManager.AppSettings["NmapPath"], 300000, !string.IsNullOrEmpty(inOsName.Value) ? inOsName.Value.ToLower() : allOSAnsApp.ToLower(), !string.IsNullOrEmpty(inApp.Value) ? inApp.Value.ToLower() : allOSAnsApp.ToLower(), disProfileName);

                    DateTime dtEndTime = DateTime.Now;
                    lblSaveMsg.Visible = false;

                    lblTime.Text = GetCompletionTime(dtStartTime.ToString(), dtEndTime.ToString());
                    if (discoveryResult != null && discoveryResult.Count() > 0)
                    {
                        _logger.Info("discoveryResult.Count:--" + discoveryResult.Count);
                        _logger.Info(Environment.NewLine);


                        foreach (var ip in discoveryResult)
                        {
                            _logger.Info("IP discovered:--" + ip.Host);
                            _logger.Info(Environment.NewLine);
                        }

                        lblNoResultFound.Visible = false;
                        lblCount.Text = discoveryResult.Count.ToString();

                        discoveryResultTable = ConvertToDataTable<CPDiscovery.DiscoveryResult>(discoveryResult);

                        if (discoveryResultTable != null && discoveryResultTable.Rows.Count > 0)
                        {
                            Session["discoverytbl"] = discoveryResultTable;

                            inputString = GetNodeRelation(discoveryResultTable);
                            if (!string.IsNullOrEmpty(inputString))
                            {
                                finalJsonString = NodeRelationConverter(inputString);
                                if (!string.IsNullOrEmpty(finalJsonString))
                                {
                                    Session["SavedJsonString"] = finalJsonString;

                                    int defaultHeight = 500;
                                    int svgHeight = 0;

                                    btnSaveDiscovery.Visible = btnSaveDiscovery.Enabled = true;
                                    btnRediscover.Visible = false;

                                    if (discoveryResult.Count() > 10)
                                    {
                                        svgHeight = (defaultHeight + ((discoveryResult.Count() - 10) * 40));
                                        System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + svgHeight + "');", true);
                                    }
                                    else
                                    {
                                        System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + defaultHeight + "');", true);
                                    }

                                }
                            }

                        }
                        else
                        {
                            Session["discoverytbl"] = null;
                            Session["SavedJsonString"] = null;
                        }
                    }
                    else
                    {
                        //lblCount.Text = discoveryResult.Count.ToString();
                        lblCount.Text = "0";
                        lblNoResultFound.Visible = true;
                        lblNoResultFound.Text = "No applications found";
                        btnSaveDiscovery.Visible = false;
                        btnRediscover.Visible = false;
                        Session["discoverytbl"] = null;
                        Session["SavedJsonString"] = null;
                        _logger.Info("discoveryResult count is zero");
                        _logger.Info(Environment.NewLine);
                    }
                }
                else
                {
                    lblNoResultFound.Visible = true;
                    lblNoResultFound.Text = "Please enter IP Address range";
                    Session["discoverytbl"] = null;
                    Session["SavedJsonString"] = null;
                    btnSaveDiscovery.Visible = false;
                    btnRediscover.Visible = false;
                }

                _logger.Info("======End of Application discovery======");
                _logger.Info(Environment.NewLine);
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while application discovery", ex);
                ExceptionManager.Manage(cpException);
            }

        }

        /// <summary>
        /// Rediscover application discovery
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-12/11/2014</author>
        protected void btnRediscover_click(object sender, EventArgs e)
        {
            #region Variables

            DataTable originalTable1 = new DataTable();
            DataTable rediscoverTable2 = new DataTable();
            DataTable newDataTable3 = new DataTable();
            DataTable savedTbl = new DataTable();

            IList<ApplicationDiscoveryProfileDetails> appDiscProfDetailsList = null;

            string osFilter = string.Empty;
            string appFilter = string.Empty;

            #endregion

            #region Code

            if (ddlScannedHost.SelectedIndex > 0)
            {
                #region 1.Get Discovery Profile details

                //disable textbox's
                inOsName.Disabled = inApp.Disabled = true;
                inIPAddressFrom.Disabled = inIPAddressTo.Disabled = true;

                //1) First get discovery profile details to populate at different textboxes.

                if (appDiscProfDetailsList == null)
                    appDiscProfDetailsList = new List<ApplicationDiscoveryProfileDetails>();

                appDiscProfDetailsList = _facade.GetApplicationDiscoveryProfileDetailsByDiscoveryProfilename(ddlScannedHost.SelectedValue);

                if (appDiscProfDetailsList != null && appDiscProfDetailsList.Count() > 0)
                {
                    //foreach (var filter in appDiscProfDetailsList)
                    //{
                    //    osFilter = filter.OsFilter;
                    //    appFilter = filter.AppFilter;
                    //}

                    foreach (var item in appDiscProfDetailsList)
                    {
                        inIPAddressFrom.Value = item.HostFrom;
                        inIPAddressTo.Value = item.HostTo;

                        //if (inOsName.Value.Equals(osFilter, StringComparison.OrdinalIgnoreCase))
                        inOsName.Value = item.OsFilter;

                        //if (inApp.Value.Equals(appFilter, StringComparison.OrdinalIgnoreCase))
                        inApp.Value = item.AppFilter;

                        lblCount.Text = item.HostFound.ToString();
                        lblTime.Text = item.DiscoveryTime;
                    }
                }
                else
                {
                    inIPAddressFrom.Value = "";
                    inIPAddressTo.Value = "";
                    inOsName.Value = allOSAnsApp;
                    inApp.Value = allOSAnsApp;
                    lblCount.Text = "";
                    lblTime.Text = "";
                }

                lblSaveMsg.Visible = false;
                lblNoResultFound.Visible = false;

                #endregion

                #region 2.Get original/saved table from session/database

                // 2) Get original table saved from session by comparing filter
                if (Session["Saveddiscoverytbl"] != null)
                {
                    newDataTable3 = (DataTable)Session["Saveddiscoverytbl"];

                    //if ((!string.IsNullOrEmpty(osFilter)) && (!string.IsNullOrEmpty(appFilter)))
                    //{
                    //    if ((inOsName.Value.Equals(osFilter, StringComparison.OrdinalIgnoreCase) && (inApp.Value.Equals(appFilter, StringComparison.OrdinalIgnoreCase))))
                    //    {
                    if (newDataTable3 != null)
                        originalTable1 = newDataTable3;
                    //    }
                    //    else
                    //    {
                    //        //do new scan if filters are changed even if we have selected discovery profile from dropdown.
                    //        originalTable1 = GetDiscoveryTblByNewScan();

                    //    }
                    //}

                }
                else
                {
                    //Get saved table data from database by discovery profile name
                    var appDiscoveryList = _facade.GetAllApplicationDiscoveryByDiscoveryProfile(ddlScannedHost.SelectedValue);

                    if (appDiscoveryList != null && appDiscoveryList.Count() > 0)
                        savedTbl = ConvertToDataTable<ApplicationDiscovery>(appDiscoveryList);

                    //if ((!string.IsNullOrEmpty(osFilter)) && (!string.IsNullOrEmpty(appFilter)))
                    //{
                    //    if ((inOsName.Value.Equals(osFilter, StringComparison.OrdinalIgnoreCase) && (inApp.Value.Equals(appFilter, StringComparison.OrdinalIgnoreCase))))
                    //    {
                    if (savedTbl != null)
                        originalTable1 = savedTbl;
                    //}
                    //else
                    //{
                    //    //do new scan if filters are changed even if we are selecting discovery profile from dropdown.
                    //    originalTable1 = GetDiscoveryTblByNewScan();

                    //}
                    //}
                }
                #endregion

                #region 3.Get rediscovery table by new scan
                //Next get rediscovery table by doing rediscovery
                rediscoverTable2 = GetDiscoveryTblByNewScan();
                #endregion

                #region 4.Do comparisions in two table

                // Then do comparision between original table(i.e. saved in DB table) and newly discovered table
                if ((originalTable1 != null && originalTable1.Rows.Count > 0) && (rediscoverTable2 != null && rediscoverTable2.Rows.Count > 0))
                {
                    foreach (DataRow row in originalTable1.Rows)
                    {
                        if (row["Host"].ToString().Contains("/"))
                            row["Host"] = row["Host"].ToString().Substring(0, row["Host"].ToString().IndexOf("/"));

                        if (row["OperatingSystem"].ToString().Contains("/"))
                            row["OperatingSystem"] = row["OperatingSystem"].ToString().Substring(0, row["OperatingSystem"].ToString().IndexOf("/"));

                        if (row["AllApps"].ToString().Contains("/"))
                        {
                            List<string> allAppsList = new List<string>();
                            allAppsList = GetListOfAllFilteredApplications(row);
                            StringBuilder allApps = null;
                            int count = 0;

                            if (allAppsList != null && allAppsList.Count() > 0)
                            {
                                foreach (var app in allAppsList)
                                {
                                    if (allApps == null)
                                        allApps = new StringBuilder();
                                    if (app.ToString().Contains("/"))
                                    {
                                        allApps.Append(app.ToString().Substring(0, app.ToString().IndexOf("/")));
                                        if (count < allAppsList.Count() - 1)
                                            allApps.Append(",");
                                        count++;
                                    }
                                    else
                                    {
                                        allApps.Append(app.ToString());
                                        if (count < allAppsList.Count() - 1)
                                            allApps.Append(",");
                                        count++;
                                    }

                                }
                                if (allApps != null)
                                    row["AllApps"] = allApps.ToString();
                            }
                        }

                        originalTable1.AcceptChanges();
                    }

                    if (originalTable1.Rows.Count > rediscoverTable2.Rows.Count)
                        GetMergedTable(originalTable1, rediscoverTable2, originalTable1, rediscoverTable2);
                    else if (rediscoverTable2.Rows.Count > originalTable1.Rows.Count)
                        GetMergedTable(rediscoverTable2, originalTable1, originalTable1, rediscoverTable2);
                    else if (originalTable1.Rows.Count == rediscoverTable2.Rows.Count)
                        GetMergedTable(rediscoverTable2, originalTable1, originalTable1, rediscoverTable2);
                }
                else
                {
                    lblCount.Text = "0";
                    lblNoResultFound.Visible = true;
                    lblNoResultFound.Text = "No applications found";
                    spnNewhost.Visible = false;
                    btnSaveDiscovery.Visible = false;
                    btnRediscover.Visible = false;
                    Session["Saveddiscoverytbl"] = null;
                }
                #endregion
            }
            else
            {
                inIPAddressFrom.Value = inIPAddressTo.Value = lblCount.Text = string.Empty;
                inOsName.Value = inApp.Value = allOSAnsApp;
                lblNoResultFound.Visible = true;
                lblNoResultFound.Text = "Please select discovery profile";
                spnNewhost.Visible = btnSaveDiscovery.Visible = btnRediscover.Visible = false;
            }

            #endregion
        }

        /// <summary>
        /// Save application discovery result into table application_discovery 
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-11/11/2014</author>
        protected void btnSaveDiscovery_Click(object sender, EventArgs e)
        {
            try
            {
                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("ViewApplicationDiscovery", UserActionType.ViewApplicationDiscovery))
                {

                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        CP.UI.Controls.ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {


                        if (Session["discoverytbl"] != null)
                        {
                            DataTable discoveryFromSession = new DataTable();

                            discoveryFromSession = (DataTable)Session["discoverytbl"];

                            if (discoveryFromSession != null && discoveryFromSession.Rows.Count > 0)
                            {

                                if (Session["SavedJsonString"] != null)
                                {
                                    if (!string.IsNullOrEmpty(finalJsonString))
                                    {
                                        int defaultHeight = 500;
                                        int svgHeight = 0;

                                        lblSaveMsg.Visible = true;
                                        btnSaveDiscovery.Enabled = false;
                                        btnRediscover.Visible = false;

                                        if (discoveryFromSession.Rows.Count > 10)
                                        {
                                            svgHeight = (defaultHeight + ((discoveryFromSession.Rows.Count - 10) * 40));
                                            System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + svgHeight + "');", true);
                                        }
                                        else
                                        {
                                            System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + defaultHeight + "');", true);
                                        }

                                    }
                                }

                                string discoveryProfileName = GetTimestamp(DateTime.Now);

                                //save application discovery result into table application_discovery
                                foreach (DataRow row in discoveryFromSession.Rows)
                                {
                                    ApplicationDiscovery appDiscover = new ApplicationDiscovery();
                                    appDiscover.Host = !string.IsNullOrEmpty(row["Host"].ToString()) ? row["Host"].ToString() : "OS";
                                    appDiscover.State = row["State"].ToString();
                                    appDiscover.LastBoot = row["LastBoot"].ToString();
                                    appDiscover.OperatingSystem = row["OperatingSystem"].ToString();
                                    appDiscover.AllApps = row["AllApps"].ToString();
                                    appDiscover.ApplicationCount = row["AllApps"].ToString().Split(',').Count();
                                    appDiscover.DiscoveryProfileName = discoveryProfileName;
                                    if (appDiscover != null)
                                        _facade.AddOrUpdateApplicationDiscovery(appDiscover);
                                }
                                ApplicationDiscoveryProfileDetails appDiscProfileDetails = new ApplicationDiscoveryProfileDetails();
                                appDiscProfileDetails.DiscoveryProfileName = discoveryProfileName;
                                appDiscProfileDetails.HostFrom = inIPAddressFrom.Value;
                                appDiscProfileDetails.HostTo = inIPAddressTo.Value;
                                appDiscProfileDetails.OsFilter = string.IsNullOrEmpty(inOsName.Value) ? allOSAnsApp : inOsName.Value;
                                appDiscProfileDetails.AppFilter = string.IsNullOrEmpty(inApp.Value) ? allOSAnsApp : inApp.Value;
                                appDiscProfileDetails.HostFound = Convert.ToInt16(lblCount.Text);
                                appDiscProfileDetails.DiscoveryTime = lblTime.Text;

                                if (appDiscProfileDetails != null)
                                    _facade.AddApplicationDiscoveryProfileDetails(appDiscProfileDetails);
                                PopulateDiscoveryProfiles(ddlScannedHost, true);
                            }
                        }
                    }
                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while saving application discovery data", ex);
                ExceptionManager.Manage(cpException);
            }

        }

        /// <summary>
        /// SelectedIndexChanged event of dropdown
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-12/11/2014</author>
        protected void ddlScannedHost_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlScannedHost.SelectedIndex > 0)
            {
                var appDiscProfileDetails = _facade.GetApplicationDiscoveryProfileDetailsByDiscoveryProfilename(ddlScannedHost.SelectedValue);

                inOsName.Disabled = inApp.Disabled = true;
                inIPAddressFrom.Disabled = inIPAddressTo.Disabled = true;

                if (appDiscProfileDetails != null)
                {
                    foreach (var item in appDiscProfileDetails)
                    {
                        inIPAddressFrom.Value = item.HostFrom;
                        inIPAddressTo.Value = item.HostTo;
                        inOsName.Value = item.OsFilter;
                        inApp.Value = item.AppFilter;
                        lblCount.Text = item.HostFound.ToString();
                        lblTime.Text = item.DiscoveryTime;
                    }
                }
                else
                {
                    inIPAddressFrom.Value = "";
                    inIPAddressTo.Value = "";
                    inOsName.Value = allOSAnsApp;
                    inApp.Value = allOSAnsApp;
                    lblCount.Text = "";
                    lblTime.Text = "";
                }

                lblSaveMsg.Visible = false;
                lblNoResultFound.Visible = false;


                var appDiscoveryList = _facade.GetAllApplicationDiscoveryByDiscoveryProfile(ddlScannedHost.SelectedValue);
                if (appDiscoveryList != null && appDiscoveryList.Count() > 0)
                {
                    var redNodesCount = appDiscoveryList.Where(a => a.Host.Contains("/Red")).Count();
                    if (redNodesCount > 0)
                    {
                        spnNewhost.Visible = true;
                        lblNewHostsFound.Text = redNodesCount.ToString();
                    }
                    else
                    {
                        spnNewhost.Visible = false;
                    }
                    DataTable appDiscovery = new DataTable();

                    appDiscovery = ConvertToDataTable<ApplicationDiscovery>(appDiscoveryList);
                    if (appDiscovery != null && appDiscovery.Rows.Count > 0)
                    {
                        Session["Saveddiscoverytbl"] = appDiscovery;

                        inputString = GetNodeRelation(appDiscovery);
                        if (!string.IsNullOrEmpty(inputString))
                        {
                            finalJsonString = NodeRelationConverter(inputString);
                            if (!string.IsNullOrEmpty(finalJsonString))
                            {
                                //Session["SavedJsonString"] = finalJsonString;

                                int defaultHeight = 500;
                                int svgHeight = 0;
                                btnSaveDiscovery.Visible = false;
                                btnRediscover.Visible = true;

                                if (appDiscoveryList.Count() > 10)
                                {
                                    svgHeight = (defaultHeight + ((appDiscoveryList.Count() - 10) * 40));
                                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + svgHeight + "');", true);
                                }
                                else
                                {
                                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + defaultHeight + "');", true);
                                }

                            }
                        }
                    }
                    else
                    {
                        Session["Saveddiscoverytbl"] = null;
                        Session["SavedJsonString"] = null;
                    }
                }
                else
                {
                    lblCount.Text = "0";
                    lblNoResultFound.Visible = true;
                    lblNoResultFound.Text = "No applications found";
                    spnNewhost.Visible = false;
                    Session["Saveddiscoverytbl"] = null;
                    Session["SavedJsonString"] = null;
                    btnSaveDiscovery.Visible = false;
                    btnRediscover.Visible = false;
                }

            }
            else
            {
                inOsName.Disabled = inApp.Disabled = false;
                inIPAddressFrom.Disabled = inIPAddressTo.Disabled = false;
                inIPAddressFrom.Value = inIPAddressTo.Value = lblCount.Text = lblTime.Text = string.Empty;
                inOsName.Value = inApp.Value = allOSAnsApp;
                lblNoResultFound.Visible = true;
                lblNoResultFound.Text = "Please select discovery profile";
                spnNewhost.Visible = btnSaveDiscovery.Visible = btnRediscover.Visible = false;
            }
        }

        #endregion

        #region Methods

        #region Graphical Network Application Discovery tree

        /// <summary>
        /// Gets node relation of Host,OS and List of applications.
        /// </summary>
        /// <param name="discoveryTable">discovery table</param>
        /// <returns>Node relation as string</returns>
        /// <author>Ram mahajan-03/11/2014</author>
        public static string GetNodeRelation(DataTable discoveryTable)
        {
            try
            {
                if (discoveryTable != null && discoveryTable.Rows.Count > 0)
                {
                    if (buildNodeRelSb == null)
                        buildNodeRelSb = new StringBuilder();
                    ConstructNodeRelationForIPAddress(parentNode, discoveryTable);

                    buildNodeRelSb.Append(";");

                    foreach (DataRow row in discoveryTable.Rows)
                    {
                        ConstructNodeRelationForOSAndApp(row);
                    }
                }

            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting GetNodeRelation", ex);
                ExceptionManager.Manage(cpException);
            }
            return Convert.ToString(buildNodeRelSb);
        }

        /// <summary>
        /// Creates node relation for IPAddresses
        /// </summary>
        /// <param name="parentNode">parentNode</param>
        /// <param name="dt">datatable</param>
        /// <author>Ram mahajan-03/11/2014</author>
        public static void ConstructNodeRelationForIPAddress(string parentNode, DataTable dt)
        {
            int count = dt.Rows.Count;
            int i = 0;

            buildNodeRelSb.AppendFormat("{0}:", parentNode);

            foreach (DataRow row in dt.Rows)
            {
                buildNodeRelSb.AppendFormat("{0}", string.Concat(row["Host"].ToString(), "/IP"));

                if (i < count - 1)
                    buildNodeRelSb.Append(",");
                i++;
            }
        }

        /// <summary>
        /// Creates node relation for OS and List of Applications
        /// </summary>
        /// <param name="row">row of data</param>
        /// <author>Ram mahajan-03/11/2014</author>
        public static void ConstructNodeRelationForOSAndApp(DataRow row)
        {
            string operatingSys = string.Empty;

            buildNodeRelSb.AppendFormat("{0}:", string.Concat(row["Host"].ToString(), "/IP"));

            operatingSys = GetOSName(row["OperatingSystem"].ToString());

            buildNodeRelSb.AppendFormat("{0}", !string.IsNullOrEmpty(operatingSys) ? string.Concat(operatingSys, "/opsys") : "OS/opsys");

            buildNodeRelSb.Append(";");

            if (row["AllApps"].ToString().Contains(','))
            {
                string[] splitApplications = row["AllApps"].ToString().Split(',');
                List<string> filterAllApps = null;
                if (splitApplications.Count() > 0)
                {
                    for (int j = 0; j < splitApplications.Count(); j++)
                    {
                        if ((splitApplications[j].ToString().Equals("ssh", StringComparison.OrdinalIgnoreCase)) || (splitApplications[j].ToString().Equals("unknown", StringComparison.OrdinalIgnoreCase)))
                        {
                            //Ignore these apps;
                        }
                        else
                        {
                            if (filterAllApps == null)
                                filterAllApps = new List<string>();

                            filterAllApps.Add(splitApplications[j].ToString());
                        }
                    }
                    if (filterAllApps != null)
                    {
                        if (filterAllApps.Count() > 0)
                        {
                            int k = 0;
                            buildNodeRelSb.AppendFormat("{0}:", !string.IsNullOrEmpty(operatingSys) ? string.Concat(operatingSys, "/opsys") : "OS/opsys");
                            foreach (var item in filterAllApps)
                            {
                                buildNodeRelSb.AppendFormat("{0}", string.Concat(item, "/app"));
                                if (k < filterAllApps.Count() - 1)
                                    buildNodeRelSb.Append(",");
                                k++;
                            }
                            buildNodeRelSb.Append(";");
                        }

                    }
                }

            }
            else
            {
                //if (row["AllApps"].ToString().Equals("ssh", StringComparison.OrdinalIgnoreCase) || row["AllApps"].ToString().Equals("unknown", StringComparison.OrdinalIgnoreCase))
                if (!string.IsNullOrEmpty(row["AllApps"].ToString()))
                {
                    buildNodeRelSb.AppendFormat("{0}:", !string.IsNullOrEmpty(operatingSys) ? string.Concat(operatingSys, "/opsys") : "OS/opsys");
                    buildNodeRelSb.AppendFormat("{0}", string.Concat(row["AllApps"].ToString(), "/app"));
                    buildNodeRelSb.Append(";");
                }
            }
        }

        /// <summary>
        /// Get operating system names along with shortform
        /// </summary>
        /// <param name="row">row of data</param>
        /// <returns>Os name</returns>
        /// <author>Ram mahajan-03/11/2014</author>
        public static string GetOSName(string row)
        {
            List<string> osNameList = null;
            osName = string.Empty;

            if (!string.IsNullOrEmpty(row))
            {
                if (row.Contains(','))
                {
                    osNameList = null;
                    if (osNameList == null)
                        osNameList = new List<string>();

                    osNameList = row.Split(',').ToList();
                }
                else if (row.Contains(';'))
                {
                    osNameList = null;
                    if (osNameList == null)
                        osNameList = new List<string>();

                    osNameList = row.Split(';').ToList();
                }
                else
                {
                    osNameList = null;
                    if (osNameList == null)
                        osNameList = new List<string>();
                    osNameList.Add(row);
                }
                if (osNameList != null)
                {
                    if (osNameList.Count() > 0)
                    {
                        if (osNameList[0].Contains(';'))
                            osName = osNameList[0].Split(';')[0].ToString();
                        else
                            osName = osNameList[0];

                        if (!string.IsNullOrEmpty(osName))
                        {
                            if (osName.StartsWith("Microsoft"))
                            {
                                return osName.Replace("Microsoft", "MS");
                            }
                        }
                    }
                }
            }
            return osName;
        }

        /// <summary>
        /// Convert input noderelation string into Json String format
        /// </summary>
        /// <param name="inputStr1">inputStr1</param>
        /// <returns>Json as string</returns>
        /// <author>Ram mahajan-03/11/2014</author>
        public static string GetJsonString(string inputStr1)
        {
            if (jsonNodeRelSb == null)
                jsonNodeRelSb = new StringBuilder();
            Random randomNumber = new Random();
            string[] firstSplitStr = inputStr1.Split(';');
            string[] secSplitStr = firstSplitStr[currentNode].Split(':');
            string[] thirdSplitStr = secSplitStr[1].Split(',');

            string serverImagePath = "../Images/icons/serverIP-icon.png";
            //string newServerImagePath = "../Images/icons/serverIP-iconG.png";

            int actualChildCount = thirdSplitStr.Length;
            firstSplitCount = firstSplitStr.Length;

            if (currentNode < firstSplitCount - 1)
            {
                currentNode = currentNode + 1;
            }
            for (int j = 0; j < actualChildCount; j++)
            {
                if (first)
                {
                    jsonNodeRelSb.Append("{");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0] + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    first = false;
                }

                if (HasChild(inputStr1, thirdSplitStr[j]))
                {
                    jsonNodeRelSb.Append("{");
                    if (thirdSplitStr[j].Contains("/Red/IP"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + serverImagePath + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/IP"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + serverImagePath + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/Red/opsys"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByOSType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/opsys"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByOSType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/Red/app"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByAppType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/app"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByAppType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                    }
                    else
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"" + ",");
                    }

                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    GetJsonString(inputStr1);

                    jsonNodeRelSb.Append("]");
                    jsonNodeRelSb.Append("}" + ",");
                }
                else
                {
                    jsonNodeRelSb.Append("{");
                    if (thirdSplitStr[j].Contains("/Red/IP"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + serverImagePath + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/IP"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + serverImagePath + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/Red/opsys"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByOSType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/opsys"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByOSType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/Red/app"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByAppType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/app"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByAppType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                    }
                    else
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"", ",");
                    }

                    jsonNodeRelSb.Append("}");
                    if (j != actualChildCount - 1)
                        jsonNodeRelSb.Append(",");
                }
            }

            return Convert.ToString(jsonNodeRelSb);
        }

        /// <summary>
        /// Get imagepath as per operating system name
        /// </summary>
        /// <param name="osName">osName</param>
        /// <returns>string as path</returns>
        /// <author>Ram Mahajan-05/11/2014</author>
        public static string GetImageByOSType(string osName)
        {
            string imagePath = string.Empty;
            if (!string.IsNullOrEmpty(osName))
            {
                if (osName.Contains("Linux"))
                    imagePath = "../Images/icons/linux1.png";
                else if (osName.Contains("Microsoft Windows") || osName.Contains("Windows") || osName.Contains("MS Windows"))
                    imagePath = "../Images/icons/os.png";
                else if (osName.Contains("aix"))
                    imagePath = "../Images/icons/aix.png";
                else
                    imagePath = "../Images/icons/os_2.png";
            }
            return imagePath;
        }

        /// <summary>
        /// Get imagepath as per application
        /// </summary>
        /// <param name="Application">Application</param>
        /// <returns>string as path</returns>
        /// <author>Ram Mahajan-05/11/2014</author>
        public static string GetImageByAppType(string Application)
        {
            string imagePath = string.Empty;
            if (!string.IsNullOrEmpty(Application))
            {
                if (Application.Contains("oracle"))
                    imagePath = "../Images/icons/dbnew.png";
                else if (Application.Contains("ms-sql-s"))
                    imagePath = "../Images/icons/dbnew.png";
                else if (Application.Contains("mysql"))
                    imagePath = "../Images/icons/dbnew.png";
                else if (Application.Contains("postgresql"))
                    imagePath = "../Images/icons/dbnew.png";
                else
                    imagePath = "../Images/icons/app-icon.png";
            }
            return imagePath;
        }

        /// <summary>
        /// Determine whether parent node has any child nodes.
        /// </summary>
        /// <param name="inputString">inputString</param>
        /// <param name="thirdSplitStr1">thirdSplitStr1</param>
        /// <returns>True if haschild- false if not</returns>
        /// <author>Ram mahajan-31/10/2014</author>
        public static bool HasChild(string inputString, string thirdSplitStr1)
        {
            hasChild = false;
            string sixthSplitStr = string.Empty;
            string[] fourthSplitStr = inputString.Split(';');
            int fourthSplitCount = fourthSplitStr.Length;
            for (int p = 0; p < fourthSplitCount; p++)
            {
                string[] fifthSplitStr = fourthSplitStr[p].Split(':');
                sixthSplitStr = fifthSplitStr[0];
                if (sixthSplitStr == thirdSplitStr1)
                {
                    hasChild = true;
                    break;
                }
            }
            return hasChild;
        }

        /// <summary>
        /// Clears all global static variables
        /// </summary>
        /// <author>Ram mahajan-31/10/2014</author>
        public static void ClearAll()
        {
            discoveryResultTable = null;
            buildNodeRelSb = null;
            currentNode = 0;
            first = true;
            firstSplitCount = 0;
            hasChild = false;
            jsonNodeRelSb = null;
            inputString = string.Empty;
            jsonString = string.Empty;
            finalJsonString = string.Empty;
        }

        /// <summary>
        /// Get noderelation and creates Json for it.
        /// </summary>
        /// <param name="nodeRelation">nodeRelation</param>
        /// <returns>string</returns>
        /// <author>Ram Mahajan-31/10/2014</author>
        public static string NodeRelationConverter(string nodeRelation)
        {
            ClearAll();
            if (!string.IsNullOrEmpty(nodeRelation))
            {
                jsonString = GetJsonString(nodeRelation);
                jsonString = jsonString + "]" + "}";
                jsonString = jsonString.Replace("],", "]");
            }
            if (!string.IsNullOrEmpty(jsonString))
                return jsonString;
            else
                return string.Empty;
        }

        /// <summary>
        /// Converts IList into Datatable
        /// </summary>
        /// <typeparam name="T">Type</typeparam>
        /// <param name="data">IList Object</param>
        /// <returns>DataTable</returns>
        /// <author>Ram mahajan-31/10/2014</author>
        public static DataTable ConvertToDataTable<T>(IList<T> data)
        {
            PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));
            DataTable table = new DataTable();
            foreach (PropertyDescriptor prop in properties)
                table.Columns.Add(prop.Name, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType);
            foreach (T item in data)
            {
                DataRow row = table.NewRow();
                foreach (PropertyDescriptor prop in properties)
                    row[prop.Name] = prop.GetValue(item) ?? DBNull.Value;
                table.Rows.Add(row);
            }
            return table;
        }

        /// <summary>
        /// Gets application discovery time.
        /// </summary>
        /// <param name="strNotificationDate">strNotificationDate</param>
        /// <param name="strCompletionDate">strCompletionDate</param>
        /// <returns>string as time</returns>
        /// <author>Ram Mahajan-03/11/2014</author>
        public static string GetCompletionTime(string strNotificationDate, string strCompletionDate)
        {
            StringBuilder sbtime = new StringBuilder();
            if (!(string.IsNullOrEmpty(strNotificationDate) || string.IsNullOrEmpty(strCompletionDate)))
            {
                DateTime dtNotificationDate = DateTime.Parse(strNotificationDate);
                DateTime dtCompletionDate = DateTime.Parse(strCompletionDate);

                TimeSpan diffdate = dtCompletionDate - dtNotificationDate;

                if (diffdate.Days > 0)
                {
                    sbtime.Append(diffdate.Days + " Day(s) ");
                }
                if (diffdate.Hours > 0)
                {
                    sbtime.Append(diffdate.Hours + " Hr(s) ");
                }
                if (diffdate.Minutes > 0)
                {
                    sbtime.Append(diffdate.Minutes + " Min(s) ");
                }
                if (diffdate.Seconds > 0)
                {
                    sbtime.Append(diffdate.Seconds + " Sec(s)");
                }
            }
            return sbtime.ToString();
        }

        /// <summary>
        /// Creates unique key
        /// </summary>
        /// <returns>returns string as unique key</returns>
        /// <author>Ram Mahajan-12/11/2014</author>
        private string GetUniqueKey()
        {
            int maxSize = 8;
            int minSize = 5;
            char[] chars = new char[62];
            string a;
            a = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
            chars = a.ToCharArray();
            int size = maxSize;
            byte[] data = new byte[1];
            RNGCryptoServiceProvider crypto = new RNGCryptoServiceProvider();
            crypto.GetNonZeroBytes(data);
            size = maxSize;
            data = new byte[size];
            crypto.GetNonZeroBytes(data);
            StringBuilder result = new StringBuilder(size);
            foreach (byte b in data)
            { result.Append(chars[b % (chars.Length - 1)]); }
            return result.ToString();
        }

        /// <summary>
        /// Create unique application discovery profile name
        /// </summary>
        /// <param name="value">Current DateTime value</param>
        /// <returns>unique application discovery profile name</returns>
        /// <author>Ram Mahajan-12/11/2014</author>
        public string GetTimestamp(DateTime value)
        {
            return string.Concat("Discovery", value.ToString("MMddyyHHmmss"));
        }

        /// <summary>
        /// Populates discovery profile dropdown with existing or saved application discovery profiles
        /// </summary>
        /// <param name="lstDiscovery">lstDiscovery</param>
        /// <param name="addDefaultItem">addDefaultItem</param>
        /// <author>Ram Mahajan-12/11/2014</author>
        public static void PopulateDiscoveryProfiles(ListControl lstDiscovery, bool addDefaultItem)
        {
            lstDiscovery.Items.Clear();
            var appDiscProfDetailsList = _facade.GetAllApplicationDiscoveryProfiles();
            if (appDiscProfDetailsList != null)
            {
                lstDiscovery.DataSource = appDiscProfDetailsList;
                lstDiscovery.DataBind();
            }
            if (addDefaultItem)
            {
                lstDiscovery.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectDiscoveryProfile, "0"));
            }
        }

        /// <summary>
        /// Gets resultant table by merging table1 and table2 and renders new tree
        /// </summary>
        /// <param name="table1">table1</param>
        /// <param name="table2">table2</param>
        /// <param name="originalTbl">originalTbl</param>
        /// <author>Ram Mahajan-13/11/2014</author>
        public void GetMergedTable(DataTable table1, DataTable table2, DataTable originalTbl, DataTable rediscoveredTbl)
        {
            DataTable table3 = new DataTable();
            DataTable table4 = new DataTable();

            var hostNotIntable2 = table1.AsEnumerable().Select(r => r.Field<string>("Host"))
                                                    .Except(table2.AsEnumerable().Select(r => r.Field<string>("Host")));

            if (hostNotIntable2 != null && hostNotIntable2.Count() > 0)
            {
                //found some new nodes.Add them to table3
                table3 = (from row in table1.AsEnumerable()
                          join Host in hostNotIntable2 on row.Field<string>("Host") equals Host
                          select row).CopyToDataTable();

                if (table3 != null && table3.Rows.Count > 0)
                {
                    spnNewhost.Visible = true;
                    lblNewHostsFound.Text = table3.Rows.Count.ToString();

                    var hostNotInTable3 = table1.AsEnumerable().Select(r => r.Field<string>("Host"))
                                                .Except(table3.AsEnumerable().Select(r => r.Field<string>("Host")));

                    if (hostNotInTable3 != null && hostNotInTable3.Count() > 0)
                    {
                        //First get original rows from rediscovered table and add them to table4
                        table4 = (from row in table1.AsEnumerable()
                                  join Host in hostNotInTable3 on row.Field<string>("Host") equals Host
                                  select row).CopyToDataTable();

                        if (table4 != null && table4.Rows.Count > 0)
                        {
                            DataTable table5 = GetOriginalTblModifiedWithRediscoveryTbl(originalTbl, table4);
                            DataTable table7 = GetDataTblOfNewHostsFoundWithModifications(table3);

                            if ((table5 != null && table5.Rows.Count > 0) && (table7 != null && table7.Rows.Count > 0))
                            {
                                originalTbl.Clear();
                                originalTbl.AcceptChanges();
                                originalTbl.Merge(table5);
                                originalTbl.Merge(table3);
                                originalTbl.AcceptChanges();
                            }
                        }
                    }
                    else
                    {
                        //i.e. we found all new nodes only after rediscovery.there is no old nodes found which are saved in db table.

                        DataTable table8 = GetDataTblOfNewHostsFoundWithModifications(table3);
                        originalTbl.Clear();
                        originalTbl.AcceptChanges();
                        originalTbl.Merge(table8);
                        originalTbl.AcceptChanges();
                    }

                }
            }
            else
            {
                spnNewhost.Visible = true;
                lblNewHostsFound.Text = hostNotIntable2.Count().ToString();
                //i.e. there is no new hosts found
                DataTable table6 = GetOriginalTblModifiedWithRediscoveryTbl(originalTbl, rediscoveredTbl);
                if (table6 != null && table6.Rows.Count > 0)
                {
                    originalTbl.Clear();
                    originalTbl.AcceptChanges();
                    originalTbl.Merge(table6);
                    originalTbl.AcceptChanges();
                }

            }

            if (originalTbl != null && originalTbl.Rows.Count > 0)
            {
                Session["discoverytbl"] = originalTbl;

                inputString = GetNodeRelation(originalTbl);
                if (!string.IsNullOrEmpty(inputString))
                {
                    finalJsonString = NodeRelationConverter(inputString);
                    if (!string.IsNullOrEmpty(finalJsonString))
                    {
                        Session["SavedJsonString"] = finalJsonString;

                        int defaultHeight = 500;
                        int svgHeight = 0;

                        btnSaveDiscovery.Visible = btnSaveDiscovery.Enabled = true;
                        btnRediscover.Visible = false;

                        if (originalTbl.Rows.Count > 10)
                        {
                            svgHeight = (defaultHeight + ((originalTbl.Rows.Count - 10) * 40));
                            System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + svgHeight + "');", true);
                        }
                        else
                        {
                            System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + defaultHeight + "');", true);
                        }

                    }
                }

            }
        }

        /// <summary>
        /// Compare original saved table with newly rediscovered table ,extract changes like new applications, build table and return it.
        /// </summary>
        /// <param name="editOriginalTable">editOriginalTable</param>
        /// <param name="rediscoveredTable">rediscoveredTable</param>
        /// <returns>DataTable</returns>
        /// <author>Ram Mahajan-19/11/2014</author>
        public DataTable GetOriginalTblModifiedWithRediscoveryTbl(DataTable editOriginalTable, DataTable rediscoveredTable)
        {
            List<string> originalAllApps = null;
            List<string> rediscoveredAllApps1 = null;
            DataTable d4 = new DataTable();
            DataTable d6 = new DataTable();
            d4 = editOriginalTable.Clone();

            foreach (DataRow rowOuter in editOriginalTable.Rows)
            {
                originalAllApps = GetListOfAllFilteredApplications(rowOuter);

                foreach (DataRow rowInner in rediscoveredTable.Rows)
                {
                    if (rowInner["Host"].ToString().Equals(rowOuter["Host"].ToString(), StringComparison.OrdinalIgnoreCase))
                    {
                        rediscoveredAllApps1 = GetListOfAllFilteredApplications(rowInner);

                        if (rediscoveredAllApps1 != null && rediscoveredAllApps1.Count() > 0)
                        {
                            List<string> newAppsFound = rediscoveredAllApps1.Except(originalAllApps).ToList();

                            if (newAppsFound != null && newAppsFound.Count() > 0)
                            {
                                List<string> finalAppList = rediscoveredAllApps1.Except(newAppsFound).ToList();

                                int newAppCount = 0;
                                StringBuilder newAppStr = null;
                                StringBuilder newAppColumnData = null;

                                foreach (var newApp in newAppsFound)
                                {
                                    if (newAppStr == null)
                                        newAppStr = new StringBuilder();
                                    newAppStr.Append(string.Concat(newApp, "/Red"));
                                    if (newAppCount < newAppsFound.Count() - 1)
                                        newAppStr.Append(",");
                                    newAppCount++;
                                }
                                if (newAppStr != null && newAppStr.Length > 0)
                                {
                                    if (finalAppList == null)
                                        finalAppList = new List<string>();
                                    finalAppList.Add(newAppStr.ToString());
                                }
                                if (finalAppList != null && finalAppList.Count() > 0)
                                {
                                    int j = 0;
                                    foreach (var item in finalAppList)
                                    {
                                        if (newAppColumnData == null)
                                            newAppColumnData = new StringBuilder();
                                        newAppColumnData.Append(item.ToString());
                                        if (j < finalAppList.Count() - 1)
                                            newAppColumnData.Append(",");
                                        j++;
                                    }

                                }
                                if (newAppColumnData != null && newAppColumnData.Length > 0)
                                {
                                    rowOuter["AllApps"] = newAppColumnData.ToString();
                                    editOriginalTable.AcceptChanges();

                                    var editedHostFromeditOriginalTable = editOriginalTable.AsEnumerable().Where(r => r.Field<string>("Host").Equals(rowOuter["Host"].ToString()));


                                    if (editedHostFromeditOriginalTable != null)
                                    {
                                        editedHostFromeditOriginalTable.CopyToDataTable(d4, LoadOption.PreserveChanges);
                                    }

                                }
                            }
                            else
                            {
                                if (d4 != null)
                                {

                                    var NotEditedhostFromeditOriginalTable = editOriginalTable.AsEnumerable().Where(r => r.Field<string>("Host").Equals(rowOuter["Host"].ToString()));
                                    if (NotEditedhostFromeditOriginalTable != null)
                                    {
                                        NotEditedhostFromeditOriginalTable.CopyToDataTable(d4, LoadOption.PreserveChanges);
                                    }
                                }

                            }

                        }

                    }
                }
            }
            return d4;

        }

        /// <summary>
        /// Get list of filtered applications for given host.
        /// </summary>
        /// <param name="row">row</param>
        /// <returns>List of applications</returns>
        /// <author>Ram Mahajan-19/11/2014</author>
        public List<string> GetListOfAllFilteredApplications(DataRow row)
        {
            List<string> listOfAllFilteredApps = null;

            if (row["AllApps"].ToString().Contains(','))
            {
                string[] splitAppsFromRediscoverTbl = row["AllApps"].ToString().Split(',');

                if (splitAppsFromRediscoverTbl.Count() > 0)
                {
                    for (int j = 0; j < splitAppsFromRediscoverTbl.Count(); j++)
                    {
                        if ((splitAppsFromRediscoverTbl[j].ToString().Equals("ssh", StringComparison.OrdinalIgnoreCase)) || (splitAppsFromRediscoverTbl[j].ToString().Equals("unknown", StringComparison.OrdinalIgnoreCase)))
                        {
                            //Ignore these apps;
                        }
                        else
                        {
                            if (listOfAllFilteredApps == null)
                                listOfAllFilteredApps = new List<string>();

                            listOfAllFilteredApps.Add(splitAppsFromRediscoverTbl[j].ToString());
                        }
                    }
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(row["AllApps"].ToString()))
                {
                    if (listOfAllFilteredApps == null)
                        listOfAllFilteredApps = new List<string>();

                    listOfAllFilteredApps.Add(row["AllApps"].ToString());
                }
            }
            return listOfAllFilteredApps;
        }

        /// <summary>
        /// Modify newly rediscovered hosts by adding "/Red" and return it.
        /// </summary>
        /// <param name="newHostTbl">newHostTbl</param>
        /// <returns>DataTable</returns>
        /// <author>Ram Mahajan-19/11/2014</author>
        public DataTable GetDataTblOfNewHostsFoundWithModifications(DataTable newHostTbl)
        {
            foreach (DataRow row in newHostTbl.Rows)
            {
                List<string> listOfApp = null;
                StringBuilder listOfAppWithCommas = null;
                int appCount = 0;

                row["Host"] = string.Concat(row["Host"].ToString(), "/Red");
                row["OperatingSystem"] = string.Concat(GetOSName(row["OperatingSystem"].ToString()), "/Red");

                if (listOfApp == null)
                    listOfApp = new List<string>();
                listOfApp = GetListOfAllFilteredApplications(row);
                if (listOfApp != null && listOfApp.Count() > 0)
                {
                    if (listOfAppWithCommas == null)
                        listOfAppWithCommas = new StringBuilder();
                    foreach (var app in listOfApp)
                    {
                        listOfAppWithCommas.Append(string.Concat(app.ToString(), "/Red"));
                        if (appCount < listOfApp.Count() - 1)
                            listOfAppWithCommas.Append(",");
                        appCount++;
                    }
                    if (listOfAppWithCommas != null && listOfAppWithCommas.Length > 0)
                    {
                        row["AllApps"] = listOfAppWithCommas.ToString();
                    }
                }

            }
            return newHostTbl;
        }

        /// <summary>
        /// Get new discovery table after rediscovery
        /// </summary>
        /// <returns>DataTable</returns>
        /// <author>Ram Mahajan-21/11/2014</author>
        public DataTable GetDiscoveryTblByNewScan()
        {
            DataTable rediscoveryTbl = new DataTable();

            DateTime dtStartTime = DateTime.Now;

            _logger.Info(Environment.NewLine);
            _logger.Info("======Start of Application rediscovery======");
            _logger.Info(Environment.NewLine);

            if (!string.IsNullOrEmpty(inIPAddressFrom.Value) && !string.IsNullOrEmpty(inIPAddressTo.Value))
            {
                _logger.Info("Calling PerformNMapDiscovery method with following parameters");
                _logger.Info(Environment.NewLine);
                _logger.Info("StartIP:--" + inIPAddressFrom.Value);
                _logger.Info(Environment.NewLine);
                _logger.Info("EndIP:--" + inIPAddressTo.Value);
                _logger.Info(Environment.NewLine);

                _logger.Info("NmapPath:--" + ConfigurationManager.AppSettings["NmapPath"]);
                _logger.Info(Environment.NewLine);

                var osFilter = !string.IsNullOrEmpty(inOsName.Value) ? inOsName.Value.ToLower() : allOSAnsApp.ToLower();
                var AppFilter = !string.IsNullOrEmpty(inApp.Value) ? inApp.Value.ToLower() : allOSAnsApp.ToLower();

                _logger.Info("OSFilter:--" + osFilter);
                _logger.Info(Environment.NewLine);
                _logger.Info("ApplicationFilter:--" + AppFilter);
                _logger.Info(Environment.NewLine);

                //var discoveryResult = Discovery.PerformNMapDiscovery(inIPAddressFrom.Value, inIPAddressTo.Value, @"C:\nmap-6.47-win32\nmap-6.47", 300000, !string.IsNullOrEmpty(inOsName.Value) ? inOsName.Value.ToLower() : allOSAnsApp.ToLower(), !string.IsNullOrEmpty(inApp.Value) ? inApp.Value.ToLower() : allOSAnsApp.ToLower());
                var discoveryResult = Discovery.PerformNMapDiscovery(inIPAddressFrom.Value, inIPAddressTo.Value, ConfigurationManager.AppSettings["NmapPath"], 300000, !string.IsNullOrEmpty(inOsName.Value) ? inOsName.Value.ToLower() : allOSAnsApp.ToLower(), !string.IsNullOrEmpty(inApp.Value) ? inApp.Value.ToLower() : allOSAnsApp.ToLower());


                DateTime dtEndTime = DateTime.Now;
                lblSaveMsg.Visible = false;

                lblTime.Text = GetCompletionTime(dtStartTime.ToString(), dtEndTime.ToString());

                if (discoveryResult != null && discoveryResult.Count() > 0)
                {
                    _logger.Info("discoveryResult.Count:--" + discoveryResult.Count);
                    _logger.Info(Environment.NewLine);

                    foreach (var ip in discoveryResult)
                    {
                        _logger.Info("IP discovered:--" + ip.Host);
                        _logger.Info(Environment.NewLine);
                    }

                    lblNoResultFound.Visible = false;
                    lblCount.Text = discoveryResult.Count.ToString();

                    rediscoveryTbl = ConvertToDataTable<CPDiscovery.DiscoveryResult>(discoveryResult);

                }
                else
                {
                    lblCount.Text = "0";
                    lblNoResultFound.Visible = true;
                    lblNoResultFound.Text = "No applications found";
                    btnSaveDiscovery.Visible = false;
                    btnRediscover.Visible = false;
                    Session["Saveddiscoverytbl"] = null;
                    _logger.Info("discoveryResult.Count is zero");
                    _logger.Info(Environment.NewLine);
                }
            }
            else
            {
                lblNoResultFound.Visible = true;
                lblNoResultFound.Text = "Please enter host range";
                Session["Saveddiscoverytbl"] = null;
                btnSaveDiscovery.Visible = false;
                btnRediscover.Visible = false;
            }
            _logger.Info("======End of Application rediscovery======");
            _logger.Info(Environment.NewLine);

            return rediscoveryTbl;
        }
        #endregion

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request


        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtCountryCode");
                //IgnoreIDs.Add("txtCountryCode2");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        #endregion
    }
}