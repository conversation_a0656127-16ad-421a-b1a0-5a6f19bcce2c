﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Text;
using CP.Common.DatabaseEntity;
//using Microsoft.Practices.EnterpriseLibrary.Data;
using log4net;
using System.Net;
using System.IO;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Web;

namespace CP.Helper
{
    public class EmailManager 
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(EmailManager));
        private SmtpConfiguration _smtpConfiguration = null;
        private SMSConfiguration _smsConfiguration = null;
        private string _errorMessage = String.Empty;
        public static string Md5Key = "20Pe,rvtu!itLtPa/d10?Mah$petnThaohes%h2ktilso3*4ftMarSe(rTe)cs@ctimizhnviceP";

        public SmtpConfiguration SmtpConfiguration
        {
            get;
            set;
        }

        public SMSConfiguration SmsConfig
        {
            get;
            set;
        }

        public String From
        {
            set;
            get;
        }

        public String Subject
        {
            set;
            get;
        }

        public String Body
        {
            get;
            set;
        }

        public int GroupId 
        { 
            get; 
            set; 
        }

        public bool SendEmail(SmtpConfiguration SmtpDetails)
        {
            //SmtpConfiguration = SmtpDetails;

            SmtpConfiguration = GetSmtpConfiguration();

            if (Subject != String.Empty && Body != String.Empty && SmtpConfiguration != null && SmtpConfiguration.Id > 0)
            {
                var mailMessage = new MailMessage { From = new MailAddress(From, "CP") };

                mailMessage.To.Add(SmtpConfiguration.UserName);

                mailMessage.IsBodyHtml = SmtpConfiguration.IsBodyHTML;
                mailMessage.Subject = Subject;
                mailMessage.Body = SmtpConfiguration.IsBodyHTML ? Body.Replace("\r\n", "<br/>") : Body.Replace("<br/>", "\r\n");

                var mailSender = new SmtpClient(SmtpConfiguration.SmtpHost) { Port = SmtpConfiguration.Port }; //Commented by MAHESH 040612
                var networkCredential = new System.Net.NetworkCredential(SmtpConfiguration.UserName, SmtpConfiguration.Password); //Commented by MAHESH 040612

                mailSender.UseDefaultCredentials = false;
                mailSender.Credentials = networkCredential;
                mailSender.EnableSsl = SmtpConfiguration.EnableSSL;

                if (mailMessage.To.Count > 0)
                {
                    try
                    {
                        mailSender.Send(mailMessage);
                        return true;
                    }
                    catch (SmtpFailedRecipientsException ex)
                    {
                        Logger.ErrorFormat("SMTP ERROR: Invalid Recipients in alert receiver " + ex.Message);

                    }
                    catch (SmtpFailedRecipientException ex)
                    {
                        Logger.ErrorFormat("SMTP ERROR: Invalid Recipient in alert receiver " + ex.Message);
                    }
                    catch (SmtpException ex)
                    {
                        Logger.ErrorFormat(" SMTP ERROR: SMTP Not Responding  " + ex.Message);
                    }
                    finally
                    {
                        mailMessage.Dispose();
                    }
                }
            }
            return false;
        }


        public static SmtpConfiguration GetSmtpConfiguration()
        {
            IList<SmtpConfiguration> smtplist = null;
            SmtpConfiguration smtp = null;

            try
            {
                
                //Added by hanumant  - for getting smtp details companywise 

                smtplist = GetAllSmtpConfiguration();

                if (smtplist != null && smtplist.Count > 0)
                {
                    var currentuserdetails = GetUserDetailsById(Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]));
                    if (currentuserdetails != null)
                    {
                        foreach (var smtpuser in smtplist)
                        {
                            var smtpuserdetails = GetUserDetailsById(Convert.ToInt32(smtpuser.CreatorId));
                            if (smtpuserdetails != null && smtpuserdetails.CompanyId == currentuserdetails.CompanyId)
                            {
                                smtp = smtpuser;
                                break;
                            }
                            else
                            {
                                smtp = null;
                                Logger.Info(" Smtp not matched for user id and company id=" + smtpuserdetails.Id + ", " + smtpuserdetails.CompanyId);
                            }
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("Exception occurred while Get Smtp Details.Exception Message is {0}", exc.Message);
                return null;
            }
            return smtp;
        }

        public static IList<SmtpConfiguration> GetAllSmtpConfiguration()
        {
            IList<SmtpConfiguration> smtplist = null;

            try
            {
                var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = db.GetSqlStringCommand("SELECT * FROM SMTP"))
                {
                    using (IDataReader myAlertReceiverReader = db.ExecuteReader(dbCommand))
                    {
                        while (myAlertReceiverReader.Read())
                        {
                            var smtp1 = new SmtpConfiguration
                            {
                                Id = Convert.ToInt32(myAlertReceiverReader[0]),
                                SmtpHost = CryptographyHelper.Md5Decrypt(myAlertReceiverReader[1].ToString()),
                                Port = Convert.ToInt32(myAlertReceiverReader[2]),
                                UserName = CryptographyHelper.Md5Decrypt(myAlertReceiverReader[3].ToString()),
                                Password = CryptographyHelper.Md5Decrypt(myAlertReceiverReader[4].ToString()),
                                EnableSSL = Convert.ToBoolean(myAlertReceiverReader[5].ToString()),
                                IsBodyHTML = Convert.ToBoolean(myAlertReceiverReader[6].ToString()),
                                CreatorId = Convert.ToInt32(myAlertReceiverReader[7])
                            };

                            smtplist.Add(smtp1);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("Exception occurred while Get Smtp list .Exception Message is " + exc.Message);
                return null;
            }

            return smtplist;
        }

        public static User GetUserDetailsById(int uid)
        {
            User userdetails = null;

            try
            {

                var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = db.GetSqlStringCommand("SELECT * FROM USERS where Id=" + uid))
                {
                    using (IDataReader myAlertReceiverReader = db.ExecuteReader(dbCommand))
                    {
                        while (myAlertReceiverReader.Read())
                        {
                            userdetails.Id = Convert.ToInt32(myAlertReceiverReader[0]);
                            userdetails.CompanyId = Convert.ToInt32(myAlertReceiverReader[3]);
                        }
                    }
                }
            }

            catch (Exception exc)
            {
                Logger.Error(" Email manager Exception occurred while Get user details by id " + exc.Message);
                return null;
            }

            return userdetails;
        }

        public String SendTestMail(SmtpConfiguration SmtpDetails)
        {
            Logger.Info("SendTestMail Method Execution Start.");
            SmtpConfiguration = SmtpDetails;
            String returnVal = "";

            if (Subject != String.Empty && Body != String.Empty && SmtpConfiguration != null )
            {
                var mailMessage = new MailMessage { From = new MailAddress(From, "CP") };

                mailMessage.To.Add(SmtpConfiguration.UserName);

                mailMessage.IsBodyHtml = SmtpConfiguration.IsBodyHTML;
                mailMessage.Subject = Subject;
                mailMessage.Body = SmtpConfiguration.IsBodyHTML ? Body.Replace("\r\n", "<br/>") : Body.Replace("<br/>", "\r\n");

                var mailSender = new SmtpClient(SmtpConfiguration.SmtpHost) { Port = SmtpConfiguration.Port };
                var networkCredential = new System.Net.NetworkCredential(From, SmtpConfiguration.Password);

                mailSender.UseDefaultCredentials = false;
                mailSender.Credentials = networkCredential;
                mailSender.EnableSsl = SmtpConfiguration.EnableSSL;

                if (mailMessage.To.Count > 0)
                {
                    try
                    {
                        Logger.Info("Mail Message To Count Is > 0");
                        mailSender.Send(mailMessage);
                        returnVal="E-Mail sent successfully.";
                      
                    }
                    catch (SmtpFailedRecipientsException ex)
                    {
                        returnVal = "SMTP ERROR: Invalid Recipients." ;
                        Logger.ErrorFormat("SMTP ERROR: Invalid Recipients in alert receiver " + ex.Message);
                        if(ex.InnerException != null)
                            Logger.ErrorFormat("SMTP ERROR: Invalid Recipients in alert receiver, InnerException Message " + ex.InnerException.Message);
                      
                    }
                    catch (SmtpFailedRecipientException ex)
                    {
                        returnVal = "SMTP ERROR: Invalid Recipient." ;
                        Logger.ErrorFormat("SMTP ERROR: Invalid Recipient In Alert Receiver " + ex.Message);
                        if (ex.InnerException != null)
                            Logger.ErrorFormat("SMTP ERROR: Invalid Recipients In Alert Receiver, InnerException Message - " + ex.InnerException.Message);
                    }
                    catch (SmtpException ex)
                    {
                        returnVal = "SMTP ERROR: SMTP Not Responding.";
                        Logger.ErrorFormat(" SMTP ERROR: SMTP Not Responding " + ex.Message);
                        if (ex.InnerException != null)
                            Logger.ErrorFormat("SMTP ERROR: SMTP Not Responding, InnerException Message - " + ex.InnerException.Message);
                    }
                    finally
                    {
                        mailMessage.Dispose();
                    }
                }
            }
            Logger.Info("SendTestMail Method Execution Completed.");
            return returnVal;
        }


        public bool SendSms(SMSConfiguration SmsDetails)
        {
            SmsConfig = SmsDetails;

            var success = false;

            success = Sms(SmsConfig.UserName, SmsConfig.SenderId);

            return success;
        }

        public String SendTestSms(SMSConfiguration SmsDetails)
        {
            SmsConfig = SmsDetails;

            //var success = false;
            
            var success= TestSms(SmsConfig.UserName, SmsConfig.Recipient);
                
            return success;
        }

        public bool Send(string strAttchmentPath, List<string> alertReceivers)
        {
            if (Subject != String.Empty && Body != String.Empty)
            {
                if (alertReceivers.Count > 0)
                {
                    var mailMessage = new MailMessage { From = new MailAddress(From, "CP") };

                    foreach (var alertReceiver in alertReceivers)
                    {
                        try
                        {
                            // mailMessage.To.Add("<EMAIL>");
                            mailMessage.To.Add(alertReceiver);
                        }
                        catch
                        {
                            _errorMessage = _errorMessage + "Invalid Email";
                            continue;
                        }
                    }
                    if (!string.IsNullOrEmpty(strAttchmentPath))
                    {
                        Attachment atc = new Attachment(strAttchmentPath);
                        mailMessage.Attachments.Add(atc);
                    }
                    mailMessage.Subject = Subject;
                    mailMessage.Body = Body;
                    mailMessage.IsBodyHtml = true;
                    var mailSender = new SmtpClient("smtp.ptechnosoft.com") { Port = 25 }; //Commented by MAHESH 040612
                    var networkCredential = new System.Net.NetworkCredential("<EMAIL>", "admin123"); //Commented by MAHESH 040612
                    //var mailSender = new SmtpClient("smtp.gmail.com") { Port = 587 };
                    //var networkCredential = new System.Net.NetworkCredential("", "");


                    mailSender.UseDefaultCredentials = false;
                    mailSender.Credentials = networkCredential;
                    mailSender.EnableSsl = true;

                    if (mailMessage.To.Count > 0)
                    {
                        try
                        {
                            mailSender.Send(mailMessage);
                            return true;
                        }
                        catch (SmtpFailedRecipientsException ex)
                        {
                            Logger.ErrorFormat("SMTP ERROR: Invalid Recipients in alert receiver " + ex.Message);
                        }
                        catch (SmtpFailedRecipientException ex)
                        {
                            Logger.ErrorFormat("SMTP ERROR: Invalid Recipient in alert receiver " + ex.Message);
                        }
                        catch (SmtpException ex)
                        {
                            Logger.ErrorFormat(" SMTP ERROR: SMTP Not Responding  " + ex.Message);
                        }
                        finally
                        {
                            mailMessage.Dispose();
                        }
                    }
                }
            }

            return false;
        }



        //public static IList<AlertReceiver> GetAllAlertReceiver()
        //{
        //    IList<AlertReceiver> alertReceivers = new List<AlertReceiver>();
        //    try
        //    {
        //        var db = DatabaseFactory.CreateDatabase();
        //        using (DbCommand dbCommand = db.GetSqlStringCommand("SELECT * FROM  alert_receiver where IsActive=1"))
        //        {
        //            using (IDataReader myAlertRecieverReader = db.ExecuteReader(dbCommand))
        //            {
        //                while (myAlertRecieverReader.Read())
        //                {
        //                    var alert = new AlertReceiver
        //                    {
        //                        Id = Convert.ToInt32(myAlertRecieverReader[0]),
        //                        EmailAddress = myAlertRecieverReader[1].ToString(),
        //                        MobileNumber=myAlertRecieverReader[2].ToString(),
        //                        Name = myAlertRecieverReader[3].ToString(),
        //                        InfraObjectId = myAlertRecieverReader[4].ToString()
        //                    };

        //                    alertReceivers.Add(alert);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        Logger.ErrorFormat("Exception occured while Get Alert Receiver Details.Exception Message is {0}", exc.Message);
        //        return null;
        //    }
        //    return alertReceivers;

        //}


        //public static SmtpConfiguration GetSmtpConfigration()
        //{
        //    var smtp = new SmtpConfiguration();
                            
        //    try
        //    {
        //        var db = DatabaseFactory.CreateDatabase();

        //        using (DbCommand dbCommand = db.GetSqlStringCommand("SELECT * FROM bcms_smtp Limit 1"))
        //        {
        //            using (IDataReader myAlertRecieverReader = db.ExecuteReader(dbCommand))
        //            {
        //                while (myAlertRecieverReader.Read())
        //                {
        //                    smtp.Id = Convert.ToInt32(myAlertRecieverReader[0]);
        //                    smtp.Smtphost = CryptographyHelper.Md5Decrypt(myAlertRecieverReader[1].ToString());
        //                    smtp.Port = Convert.ToInt32(myAlertRecieverReader[2]);
        //                    smtp.Username = CryptographyHelper.Md5Decrypt(myAlertRecieverReader[3].ToString());
        //                    smtp.Password = CryptographyHelper.Md5Decrypt(myAlertRecieverReader[4].ToString());
        //                    smtp.EnableSsl = Convert.ToBoolean(myAlertRecieverReader[5].ToString());
        //                    smtp.IsBodyHtml = Convert.ToBoolean(myAlertRecieverReader[6].ToString());

        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        Logger.ErrorFormat("Exception occured while Get Smtp Details.Exception Message is {0}", exc.Message);
        //        return null;
        //    }
        //    return smtp;

        //}

//        public static IList<AlertReceiver> GetAllAlertReceiver()
//        {
//            IList<AlertReceiver> alertReceivers = new List<AlertReceiver>();
//            try
//            {
//                //var db = DatabaseFactory.CreateDatabase();
//                //using (DbCommand dbCommand = db.GetSqlStringCommand("SELECT * FROM  bcms_alert_receiver where IsActive=1"))
//                //{
//                //    using (IDataReader myAlertRecieverReader = db.ExecuteReader(dbCommand))
//                //    {


//                using (DbCommand dbCommand = Database.GetStoredProcCommand("AlertReceiver_GetAll"))
//                {
//#if ORACLE
//                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
//#endif

//                    using (IDataReader myAlertRecieverReader = Database.ExecuteReader(dbCommand))
//                    {

//                        while (myAlertRecieverReader.Read())
//                        {
//                            var alert = new AlertReceiver
//                            {
//                                //Id = Convert.ToInt32(myAlertRecieverReader[0]),
//                                //EmailAddress = myAlertRecieverReader[1].ToString(),
//                                //Name = myAlertRecieverReader[2].ToString(),
//                                //GroupId = myAlertRecieverReader[3].ToString()

//                                Id = Convert.IsDBNull(myAlertRecieverReader["Id"]) ? 0 : Convert.ToInt32(myAlertRecieverReader["Id"]),
//                                EmailAddress = Convert.IsDBNull(myAlertRecieverReader["EmailAddress"]) ? string.Empty : Convert.ToString(myAlertRecieverReader["EmailAddress"]),
//                                Name = Convert.IsDBNull(myAlertRecieverReader["Name"]) ? string.Empty : Convert.ToString(myAlertRecieverReader["Name"]),
//                                InfraObjectId = Convert.IsDBNull(myAlertRecieverReader["InfraObjectId"]) ? string.Empty : Convert.ToString(myAlertRecieverReader["InfraObjectId"]),
//                                IsActive = !Convert.IsDBNull(myAlertRecieverReader["IsActive"]) && Convert.ToBoolean(myAlertRecieverReader["IsActive"])
//                            };

//                            alertReceivers.Add(alert);
//                        }
//                    }
//                }
//            }
//            catch (Exception exc)
//            {
//                Logger.ErrorFormat("Exception occured while Get Alert Receiver Details.Exception Message is {0}", exc.Message);
//                return null;
//            }
//            return alertReceivers;

//        }

        //        public static SmtpConfiguration GetSmtpConfigration()
//        {
//            var smtp = new SmtpConfiguration();

//            try
//            {
//                using (DbCommand dbCommand =Database.GetStoredProcCommand( "SmtpConfiguration_GetAll"))
//                {
//#if ORACLE
//                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
//#endif
//                    using (IDataReader myAlertRecieverReader =Database.ExecuteReader(dbCommand))
//                    {
//                        while (myAlertRecieverReader.Read())
//                        {
//                            smtp.Id = Convert.IsDBNull(myAlertRecieverReader["Id"]) ? 0 : Convert.ToInt32(myAlertRecieverReader["Id"]);
//                            smtp.Smtphost = Convert.IsDBNull(myAlertRecieverReader["SmtpHost"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(myAlertRecieverReader["SmtpHost"]));
//                            smtp.Port = Convert.IsDBNull(myAlertRecieverReader["Port"]) ? 0 : Convert.ToInt32(myAlertRecieverReader["Port"]);
//                            smtp.Username = Convert.IsDBNull(myAlertRecieverReader["UserName"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(myAlertRecieverReader["UserName"]));
//                            smtp.Password = Convert.IsDBNull(myAlertRecieverReader["Password"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(myAlertRecieverReader["Password"]));

//                            if (!Convert.IsDBNull(myAlertRecieverReader["EnableSSL"]))
//                                smtp.EnableSsl = Convert.ToBoolean(myAlertRecieverReader["EnableSSL"]);

//                            if (!Convert.IsDBNull(myAlertRecieverReader["IsBodyHTML"]))
//                                smtp.IsBodyHtml = Convert.ToBoolean(myAlertRecieverReader["IsBodyHTML"]);


//                        }
//                    }
//                }
//            }
//            catch (Exception exc)
//            {
//                Logger.ErrorFormat("Exception occured while Get Smtp Details.Exception Message is {0}", exc.Message);
//                return null;
//            }
//            return smtp;

//        }

//        public static SMSConfiguration GetSMSConfigration()
//        {
//            var sms = new SMSConfiguration();

//            try
//            {
//                var db = DatabaseFactory.CreateDatabase();

//                using (DbCommand dbCommand = db.GetSqlStringCommand("SELECT * FROM sms_configuration Limit 1"))
//                {
//                    using (IDataReader smsGate = db.ExecuteReader(dbCommand))
//                    {
//                        while (smsGate.Read())
//                        {
//                            sms.Id = Convert.ToInt32(smsGate[0]);
//                            sms.URL = smsGate[1].ToString();
//                            sms.SenderId = smsGate[2].ToString();
//                            sms.UserName = smsGate[3].ToString();
//                            sms.Password = CryptographyHelper.Md5Decrypt(smsGate[4].ToString());
//                        }
//                    }
//                }
//            }
//            catch (Exception exc)
//            {
//                Logger.ErrorFormat("Exception occured while Get Smtp Details.Exception Message is {0}", exc.Message);
//                return null;
//            }
//            return sms;

//        }        

        public static string Md5Decrypt(string cipherString)
        {
            byte[] toEncryptArray = Convert.FromBase64String(cipherString);

            var hashmd5 = new MD5CryptoServiceProvider();
            byte[] keyArray = hashmd5.ComputeHash(Encoding.UTF8.GetBytes(Md5Key));
            hashmd5.Clear();

            var tdes = new TripleDESCryptoServiceProvider
            {
                Key = keyArray,
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            var cTransform = tdes.CreateDecryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            tdes.Clear();
            return Encoding.UTF8.GetString(resultArray);

        }

        public string GetSMSBodyforNotification(string username, string smsBody)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("Dear ");
            sb.Append(username);
            sb.Append(" , ");
            sb.Append(" This is to inform you that ");
            sb.Append(smsBody);
            sb.Append(" .");
            return sb.ToString();
        }


        public bool Sms(string username, string MobileNo)
        {
            string MsgText = GetSMSBodyforNotification(username, Body);
            string dataString = string.Empty;
            try
            {
                Logger.ErrorFormat("SMS Text: " + MsgText);
                Logger.ErrorFormat("Mobile Number: " + MobileNo);

                string Url = SmsConfig.URL;
                var user = SmsConfig.UserName;
                var psd = SmsConfig.Password;
                var sender = SmsConfig.SenderId;

                //string strUrl = "http://api.mVaayoo.com/mvaayooapi/MessageCompose?user=<EMAIL>:873210&senderID=CVAULT&receipientno=" + MobileNo + "&msgtxt=" + MsgText;
                //"http://api.mVaayoo.com/mvaayooapi/MessageCompose?user=SMSUSERNAME:SMSPASSWORD&senderID=SENDERID&receipientno=""
                string strUrl = Url + user + ":" + psd + "&senderID=" + sender + "&receipientno=" + MobileNo + "&msgtxt=" + MsgText;

                WebRequest request = HttpWebRequest.Create(strUrl);
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                Stream s = (Stream)response.GetResponseStream();
                StreamReader readStream = new StreamReader(s);
                dataString = readStream.ReadToEnd();
                response.Close();
                s.Close();
                readStream.Close();
                Logger.ErrorFormat("SMS Send Successfully");
            }
            catch (Exception ex)
            {
                dataString = string.Empty;
                Logger.ErrorFormat("Exception occured while Send SMS.Exception Message is {0}", ex.Message);

            }


            return dataString.ToLower().Contains("status=0");
        }


        public String TestSms(string username, string MobileNo)
        {
            string MsgBody = "this is Testing SMS Gateway Message";
            string MsgText = GetSMSBodyforNotification(username, MsgBody);            
            string dataString = string.Empty;
            try
            {
                Logger.ErrorFormat("SMS Text: " + MsgText);
                Logger.ErrorFormat("Mobile Number: " + MobileNo);

                string Url = SmsConfig.URL;
                var user = SmsConfig.UserName;
                var psd = SmsConfig.Password!=string.Empty?CryptographyHelper.Md5Decrypt(SmsConfig.Password):"";
                var sender = SmsConfig.SenderId;

                //string strUrl = "http://api.mVaayoo.com/mvaayooapi/MessageCompose?user=<EMAIL>:873210&senderID=CVAULT&receipientno=" + MobileNo + "&msgtxt=" + MsgText;
                //"http://api.mVaayoo.com/mvaayooapi/MessageCompose?user=SMSUSERNAME:SMSPASSWORD&senderID=SENDERID&receipientno=""
                string strUrl = Url + "?user=" + user + ":" + psd + "&senderID=" + sender + "&receipientno=" + MobileNo + "&msgtxt=" + MsgText;

                WebRequest request = HttpWebRequest.Create(strUrl);
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                Stream s = (Stream)response.GetResponseStream();
                StreamReader readStream = new StreamReader(s);
                dataString = readStream.ReadToEnd();
                response.Close();
                s.Close();
                readStream.Close();
                Logger.ErrorFormat("SMS Send Successfully");
                dataString = "Test SMS Sent Successfully";
            }
            catch (Exception ex)
            {
                dataString = ex.Message;
                Logger.ErrorFormat("Exception occured while Send SMS.Exception Message is {0}", ex.Message);

            }
            return dataString;
        }
    }    
}
