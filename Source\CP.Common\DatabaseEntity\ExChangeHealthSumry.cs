﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ExChangeSummary", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ExChangeHealthSumry : BaseEntity
    {
        #region Properties

        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string ServerName { get; set; }

        [DataMember]
        public string ServerType { get; set; }

        [DataMember]
        public string ClusterService { get; set; }

        [DataMember]
        public string ReplayService { get; set; }

        [DataMember]
        public string ActiveManager { get; set; }

        [DataMember]
        public string TasksRPCListener { get; set; }

        [DataMember]
        public string TCPListener { get; set; }

        [DataMember]
        public string ServerLocatorService { get; set; }

        [DataMember]
        public string DagMembersUp { get; set; }

        [DataMember]
        public string MonitoringService { get; set; }

        [DataMember]
        public string ClusterNetwork { get; set; }

        [DataMember]
        public string QuorumGroup { get; set; }

        [DataMember]
        public string DatabaseRedundancy { get; set; }

        [DataMember]
        public string DatabaseAvailability { get; set; }

        [DataMember]
        public string DBCopySuspended { get; set; }

        [DataMember]
        public string DBCopyFailed { get; set; }

        [DataMember]
        public string DBInitializing { get; set; }

        [DataMember]
        public string DBDisconnected { get; set; }

        [DataMember]
        public string DBLogCopyKeepingUp { get; set; }

        [DataMember]
        public string DBLogReplayKeepingUp { get; set; }

        #endregion Properties
    }
}
