﻿using CP.Common.Base;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseExchangeDAG", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DataBaseExchangeDAG : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string MailBoxDBName { get; set; }

        [DataMember]
        public string AuthenticationType { get; set; }

        [DataMember]
        public string ProtocolType { get; set; }

        #endregion Properties
    }
}