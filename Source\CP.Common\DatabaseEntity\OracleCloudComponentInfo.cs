﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "OracleCloudComponentInfo", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class OracleCloudComponentInfo : BaseEntity
    {
        #region Properties

        public int InfraObjectId { get; set; }
        public string CompartmentName { get; set; }
        public string CompartmentActiveStatus { get; set; }


        #endregion Properties


    }
}
