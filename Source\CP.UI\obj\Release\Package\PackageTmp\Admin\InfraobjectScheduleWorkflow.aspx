﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="InfraobjectScheduleWorkflow.aspx.cs" Inherits="CP.UI.Admin.InfraobjectScheduleWorkflow" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<!DOCTYPE html >
<html>
<head id="Head1" runat="server">
    <title>Continuity Patrol :: Infraobject Schedule WorkFlow</title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.simple-dtpicker.css" type="text/css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.timepicker.css" type="text/css" rel="stylesheet" />
     <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/less.min.js"></script>
    
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <style type="text/css">
        .btn-group.bootstrap-select {
            width: 85%;
        }

        .btn.dropdown-toggle.clearfix.btn-default {
            width: 100%;
        }
        .btn-group.bootstrap-select.col-xs-2 {
        width:18%;
        padding-right:11px !important;
        }
    </style>

</head>

<body>
    <form id="form1" runat="server">
        <div>
            <asp:ScriptManager ID="ScriptManager1" runat="server" ScriptMode="Release">
            </asp:ScriptManager>
            <script type="text/javascript">
                var xPos, yPos;
                Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
                Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
                function BeginRequestHandler(sender, args) {
                    if ($("#SwitchScrollbar").length > 0) {
                        xPos = window.$get('SwitchScrollbar').scrollLeft;
                        yPos = window.$get('SwitchScrollbar').scrollTop;
                    }
                }
                function EndRequestHandler(sender, args) {
                    if ($("#SwitchScrollbar").length > 0) {
                        window.$get('SwitchScrollbar').scrollLeft = xPos;
                        window.$get('SwitchScrollbar').scrollTop = yPos;
                    }
                }

            </script>

            <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
                
                <ContentTemplate>



                    <asp:Panel ID="PanelAuthenticate" runat="server">


                        <div class="modal-body">

                          
                            <div class="container">
                                <div class="row">
                                    <div class="col-xs-12 form-horizontal uniformjs">

                                        <div class="form-group">
                                            <label class="col-xs-3 control-label" for="txtDescription">
                                                Select Action <span class="inactive">*</span></label>
                                            <div class="col-xs-9">
                                                <asp:DropDownList ID="ddlActions" runat="server" CssClass="selectpicker" Width="85%" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlActions_IndexChanged">
                                                    <%--<asp:ListItem Value="1" Text="CPSL Script"></asp:ListItem>--%>
                                                    <asp:ListItem Value="2" Text="WorkFlow"></asp:ListItem>
                                                  
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rvddlActions" runat="server" ControlToValidate="ddlActions"
                                                    Display="Dynamic" CssClass="error" ErrorMessage="Select Workflow." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>
                                            </div>
                                        </div>

                                        <div class="form-group" runat="server" id="dvWorkFlow" visible="false">
                                            <label class="col-xs-3 control-label" for="txtDescription">
                                                Select Workflow <span class="inactive">*</span></label>
                                            <div class="col-xs-9">
                                                <asp:DropDownList ID="ddlSelectWorkflow" runat="server" CssClass="selectpicker" Width="85%" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlSelectWorkflowIndexChanged">
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rvWorkflow" runat="server" ControlToValidate="ddlSelectWorkflow"
                                                    Display="Dynamic" CssClass="error" ErrorMessage="Select Workflow." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>
                                            </div>
                                        </div>
                                        <div class="form-group" runat="server" id="dvCPLScript" >
                                            <label class="col-xs-3 control-label" for="txtDescription">
                                                Select Script <span class="inactive">*</span></label>
                                            <div class="col-xs-9">
                                                <asp:DropDownList ID="ddlScript" runat="server" CssClass="selectpicker" Width="85%" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlScript_IndexChanged">
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="rvddlScript" runat="server" ControlToValidate="ddlScript"
                                                    Display="Dynamic" CssClass="error" ErrorMessage="Select Script." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>
                                            </div>
                                        </div>
                                        <div runat="server" id="dvCPSlBox" visible="false">
                                            <div class="form-group">
                                                <label class="col-xs-3 control-label">Script Name</label>
                                                <div class="col-xs-9">
                                                    <asp:TextBox ID="txtScriptName" runat="server" CssClass="form-control" Width="85%"> </asp:TextBox>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-xs-3 control-label">Script</label>
                                                <div class="col-xs-9">
                                                    <asp:TextBox ID="txtScript" runat="server" CssClass="form-control" Width="85%" TextMode="MultiLine"> </asp:TextBox>
                                                </div>
                                            </div>
                                        </div>
                                        <div runat="server" id="SchedulingInfraObject" visible="false">
                                            <div class="form-group">
                                                <label class="col-xs-3 control-label" for="txtDescription">
                                                    Scheduling <span class="inactive">*</span></label>
                                                <div class="col-xs-9">
                                                    <asp:DropDownList ID="ddlScheduling" runat="server" CssClass="selectpicker" Width="85%" data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlSchedulingIndexChanged">
                                                        <asp:ListItem Value="0" Text="Select"></asp:ListItem>
                                                        <asp:ListItem Value="1" Text="Once"></asp:ListItem>
                                                        <asp:ListItem Value="2" Text="Cycle"></asp:ListItem>
                                                    </asp:DropDownList>


                                                    <asp:RequiredFieldValidator ID="rvScheduling" runat="server" ControlToValidate="ddlScheduling"
                                                        Display="Dynamic" CssClass="error" ErrorMessage="Select Scheduling." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>

                                                </div>
                                            </div>
                                            <div class="form-group" visible="false" runat="server" id="divCal">
                                                <label class="col-xs-3 control-label">Select Date-Time</label>
                                                <div class="col-xs-9">
                                                    <asp:TextBox ID="txtDateTime" runat="server" CssClass="form-control" Width="85%"> </asp:TextBox>
                                                </div>
                                            </div>
                                        </div>


                                        <div runat="server" id="timeMgmt" visible="false">
                                            <div class="form-group">
                                                <label class="col-xs-3">
                                                    Time Interval <span class="inactive">*</span>
                                                </label>
                                                <div class="col-xs-9">
                                                    <asp:Panel ID="PanelInterval" runat="server">
                                                        <asp:RadioButtonList ID="rdTimeInterval" runat="server" RepeatDirection="Horizontal"
                                                            CssClass="dropbox text-indent" OnSelectedIndexChanged="rdTimeInterval_SelectedIndexChanged"
                                                            AutoPostBack="True">
                                                            <asp:ListItem>Minute(s)</asp:ListItem>
                                                            <asp:ListItem>Hour(s)</asp:ListItem>
                                                            <asp:ListItem>Day(s)</asp:ListItem>
                                                        </asp:RadioButtonList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="rdTimeInterval"
                                                            Display="Dynamic" CssClass="error" ErrorMessage="Select Interval Time" Font-Size="9pt"></asp:RequiredFieldValidator>
                                                        <asp:Label ID="lbltimeintervalErrormessage" runat="server" Visible="false" Text="Select Interval Time"></asp:Label>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="rdTimeInterval" Display="Dynamic" ValidationGroup="Infra"
                                                            CssClass="error" ErrorMessage="Select Time Interval In Either Minutes, Hours or Days"></asp:RequiredFieldValidator>
                                                    </asp:Panel>
                                                </div>
                                            </div>

                                            <asp:Panel ID="Panel_Minuite" runat="server" Visible="false">
                                                <div class="form-group">
                                                    <label class="col-xs-3">
                                                        Set Time
                                                    </label>
                                                    <div class="col-xs-9">
                                                        <asp:Label ID="lblEveryMinuite" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                        <asp:TextBox ID="txteveryminuite" runat="server" CssClass="form-control" Style="width: 12%;"
                                                            MaxLength="4" CausesValidation="True"></asp:TextBox>
                                                        <asp:Label ID="lblminutes" runat="server" Text="Minute(s)" CssClass="margin-right"></asp:Label>
                                                        <asp:RegularExpressionValidator ID="REVEveryminuite" runat="server" CssClass="error"
                                                            ControlToValidate="txteveryminuite" ErrorMessage="Please Enter only Numbers" Display="Dynamic"
                                                            ValidationExpression="\d+" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                        <asp:RequiredFieldValidator ID="rfvtxteveryminuite" runat="server" Enabled="true"
                                                            ControlToValidate="txteveryminuite" Display="Dynamic" ForeColor="Red"
                                                            CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>


                                            <asp:Panel ID="Panel_Hourly" runat="server" Visible="false">
                                                <div class="form-group">
                                                    <label class="col-xs-3 control-label">
                                                        Set Time
                                                    </label>
                                                    <div class="col-xs-9">
                                                        <asp:Label ID="lblEveryHourly" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                        <asp:TextBox ID="txteveryhour" runat="server" CssClass="form-control" Style="width: 12%;"
                                                            MaxLength="2"></asp:TextBox>

                                                        <asp:Label ID="lblhours" runat="server" Text="Hour(s)" CssClass="margin-right"></asp:Label>
                                                        <asp:TextBox ID="txteveryhourlyminuite" runat="server" CssClass="form-control" Style="width: 12%;" AutoPostBack="true"
                                                            MaxLength="2" OnTextChanged="txteveryhourlyminuite_TextChanged"></asp:TextBox>
                                                        <asp:Label ID="lblhourlyminutes" runat="server" Text="Minute(s)"></asp:Label>
                                                        <asp:RequiredFieldValidator ID="rfvtxteveryhour" runat="server" Enabled="true" ControlToValidate="txteveryhour"
                                                            Display="Dynamic" Font-Size="8" ForeColor="Red" CssClass="error" ErrorMessage="Enter hours and  minutes"></asp:RequiredFieldValidator>
                                                        <asp:RequiredFieldValidator ID="rfvtxteveryhourlyminuite" runat="server" Enabled="true"
                                                            ControlToValidate="txteveryhourlyminuite" Display="Dynamic" ForeColor="Red"
                                                            CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="revtxteveryhour" runat="server" CssClass="error"
                                                            ControlToValidate="txteveryhour" ErrorMessage="Enter only Numbers" ValidationExpression="\d+"
                                                            Font-Size="9pt"></asp:RegularExpressionValidator>
                                                        <asp:RangeValidator ID="rng" runat="server" CssClass="error" ControlToValidate="txteveryhourlyminuite" Display="Dynamic"
                                                            ErrorMessage="Minutes Should Be Less Than 60" Type="Integer" Enabled="false" MaximumValue="60" MinimumValue="0"></asp:RangeValidator>
                                                        <asp:RegularExpressionValidator ID="regexpfornumeric" runat="server" CssClass="error" Enabled="false"
                                                            ControlToValidate="txteveryhourlyminuite" ErrorMessage="Enter Numeric Only" Display="Dynamic"
                                                            ValidationExpression="^[0-99]*$" SetFocusOnError="True" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>

                                            <asp:Panel ID="Panel_Daily" runat="server" Visible="false">
                                                <div class="form-group">
                                                    <label class="col-xs-3 control-label" style="vertical-align: top">
                                                        Set Time
                                                    </label>
                                                    <div class="col-xs-9">
                                                        <asp:Label ID="lblEverydaily" runat="server" Text="Every" CssClass="margin-right"></asp:Label>
                                                        <asp:TextBox ID="txteverydaily" runat="server" CssClass="form-control" Style="width: 16%; margin-left: 21px;"></asp:TextBox>
                                                        <asp:Label ID="lbldays" runat="server" Text="Day(s)" CssClass="margin-right"></asp:Label>
                                                        <br />
                                                        <br />
                                                        <asp:Label ID="lblstartTime" runat="server" CssClass="pull-left margin-right margin-top" Text="StartTime "></asp:Label>
                                                        <asp:DropDownList ID="ddlhours" runat="server" CssClass="selectpicker col-xs-2" data-style="btn-default" Style="width: 16%">
                                                            <asp:ListItem>00</asp:ListItem>
                                                            <asp:ListItem>01</asp:ListItem>
                                                            <asp:ListItem>02</asp:ListItem>
                                                            <asp:ListItem>03</asp:ListItem>
                                                            <asp:ListItem>04</asp:ListItem>
                                                            <asp:ListItem>05</asp:ListItem>
                                                            <asp:ListItem>06</asp:ListItem>
                                                            <asp:ListItem>07</asp:ListItem>
                                                            <asp:ListItem>08</asp:ListItem>
                                                            <asp:ListItem>09</asp:ListItem>
                                                            <asp:ListItem>10</asp:ListItem>
                                                            <asp:ListItem>11</asp:ListItem>
                                                            <asp:ListItem>12</asp:ListItem>
                                                            <asp:ListItem>13</asp:ListItem>
                                                            <asp:ListItem>14</asp:ListItem>
                                                            <asp:ListItem>15</asp:ListItem>
                                                            <asp:ListItem>16</asp:ListItem>
                                                            <asp:ListItem>17</asp:ListItem>
                                                            <asp:ListItem>18</asp:ListItem>
                                                            <asp:ListItem>19</asp:ListItem>
                                                            <asp:ListItem>20</asp:ListItem>
                                                            <asp:ListItem>21</asp:ListItem>
                                                            <asp:ListItem>22</asp:ListItem>
                                                            <asp:ListItem>23</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:Label ID="lblhr" runat="server" CssClass="pull-left margin-right margin-top" Text="Hr"></asp:Label>
                                                        <asp:DropDownList ID="ddlminutes" runat="server" CssClass="selectpicker col-xs-2"
                                                            data-style="btn-default" Style="width: 16%">
                                                            <asp:ListItem>00</asp:ListItem>
                                                            <asp:ListItem>01</asp:ListItem>
                                                            <asp:ListItem>02</asp:ListItem>
                                                            <asp:ListItem>03</asp:ListItem>
                                                            <asp:ListItem>04</asp:ListItem>
                                                            <asp:ListItem>05</asp:ListItem>
                                                            <asp:ListItem>06</asp:ListItem>
                                                            <asp:ListItem>07</asp:ListItem>
                                                            <asp:ListItem>08</asp:ListItem>
                                                            <asp:ListItem>09</asp:ListItem>
                                                            <asp:ListItem>10</asp:ListItem>
                                                            <asp:ListItem>11</asp:ListItem>
                                                            <asp:ListItem>12</asp:ListItem>
                                                            <asp:ListItem>13</asp:ListItem>
                                                            <asp:ListItem>14</asp:ListItem>
                                                            <asp:ListItem>15</asp:ListItem>
                                                            <asp:ListItem>16</asp:ListItem>
                                                            <asp:ListItem>17</asp:ListItem>
                                                            <asp:ListItem>18</asp:ListItem>
                                                            <asp:ListItem>19</asp:ListItem>
                                                            <asp:ListItem>20</asp:ListItem>
                                                            <asp:ListItem>21</asp:ListItem>
                                                            <asp:ListItem>22</asp:ListItem>
                                                            <asp:ListItem>23</asp:ListItem>
                                                            <asp:ListItem>24</asp:ListItem>
                                                            <asp:ListItem>25</asp:ListItem>
                                                            <asp:ListItem>26</asp:ListItem>
                                                            <asp:ListItem>27</asp:ListItem>
                                                            <asp:ListItem>28</asp:ListItem>
                                                            <asp:ListItem>29</asp:ListItem>
                                                            <asp:ListItem>30</asp:ListItem>
                                                            <asp:ListItem>31</asp:ListItem>
                                                            <asp:ListItem>32</asp:ListItem>
                                                            <asp:ListItem>33</asp:ListItem>
                                                            <asp:ListItem>34</asp:ListItem>
                                                            <asp:ListItem>35</asp:ListItem>
                                                            <asp:ListItem>36</asp:ListItem>
                                                            <asp:ListItem>37</asp:ListItem>
                                                            <asp:ListItem>38</asp:ListItem>
                                                            <asp:ListItem>39</asp:ListItem>
                                                            <asp:ListItem>40</asp:ListItem>
                                                            <asp:ListItem>41</asp:ListItem>
                                                            <asp:ListItem>42</asp:ListItem>
                                                            <asp:ListItem>43</asp:ListItem>
                                                            <asp:ListItem>44</asp:ListItem>
                                                            <asp:ListItem>45</asp:ListItem>
                                                            <asp:ListItem>46</asp:ListItem>
                                                            <asp:ListItem>47</asp:ListItem>
                                                            <asp:ListItem>48</asp:ListItem>
                                                            <asp:ListItem>49</asp:ListItem>
                                                            <asp:ListItem>50</asp:ListItem>
                                                            <asp:ListItem>51</asp:ListItem>
                                                            <asp:ListItem>52</asp:ListItem>
                                                            <asp:ListItem>53</asp:ListItem>
                                                            <asp:ListItem>54</asp:ListItem>
                                                            <asp:ListItem>55</asp:ListItem>
                                                            <asp:ListItem>56</asp:ListItem>
                                                            <asp:ListItem>57</asp:ListItem>
                                                            <asp:ListItem>58</asp:ListItem>
                                                            <asp:ListItem>59</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:Label ID="lblmin" runat="server" CssClass="pull-left margin-right margin-top" Text="Min "></asp:Label>
                                                        <asp:RegularExpressionValidator ID="revdays" runat="server" CssClass="error" ControlToValidate="txteverydaily" Display="Dynamic"
                                                            ErrorMessage="Please Enter only Numbers" ValidationExpression="\d+"></asp:RegularExpressionValidator>
                                                        <asp:RequiredFieldValidator ID="rfveverydaily" runat="server" ControlToValidate="txteverydaily"
                                                            CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days" ForeColor="Red"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>

                                        </div>
                                        <div class="form-group">
                                            <div class="text-right">

                                                <asp:Label ID="lblMessage" runat="server"></asp:Label>

                                                <asp:Button ID="btnAddToSchedule" runat="server" Text="Add" CssClass="btn btn-primary" OnClick="btnAddToSchedule_Click" />
                                                <asp:HiddenField ID="hdnSCPlName" runat="server" />
                                                 <asp:Button ID="btnCPSLDelete" runat="server" Text="Delete" CssClass="btn btn-primary" Visible="false" CausesValidation="false" />
                                             <%--   <input type="button" id="btnCPSLDelete" class="btn btn-primary" visible="false" value="Delete" />--%>
                                                <%-- <TK1:ConfirmButtonExtender ID="ConfirmsDelete" runat="server" TargetControlID="btnCPSLDelete"
                                                            ConfirmText='<%# "Are you sure want to delete " + hdnSCPlName.Value + " ? " %>'
                                                            OnClientCancel="CancelClick">
                                                        </TK1:ConfirmButtonExtender>OnClick="btnCPSLDelete_Click"--%>

                                                <asp:Button ID="btnCancel" runat="server" Text="Cancel" CssClass="btn btn-primary" OnClick="btnCancel_Click" CausesValidation="false" />

                                            </div>
                                        </div>
                                        <div class="form-group" runat="server" id="dvlvWorkFlow"  visible="false">
                                            <asp:ListView ID="lvInfraobjectSchedule" runat="server" OnItemEditing="lvInfraobjectScheduleItemEditing"
                                                OnItemDeleting="lvInfraobjectScheduleItemDeleting" OnPreRender="lvInfraobjectSchedulePreRender" OnItemDataBound="lvInfraobjectSchedule_ItemDataBound" DataKeyNames="Id">

                                                <LayoutTemplate>
                                                    <table class="table table-striped table-bordered table-condensed margin-bottom-none">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 4%;">
                                                                    <span>
                                                                        <img src="../Images/icons/replication_list.png" /></span>
                                                                </th>
                                                                <th style="width: 30%;">Name
                                                                </th>
                                                                <th style="width: 40%;">Schedule
                                                                </th>
                                                                <th style="width: 12%;">Enable
                                                                </th>
                                                                <th class="text-center">Action
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                    </table>
                                                    <div class="infraScheduleScroll">
                                                        <table class="table table-bordered">
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </LayoutTemplate>
                                                <ItemTemplate>
                                                    <tr>
                                                        <td style="width: 4%;">
                                                            <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                                            <asp:Label ID="lblSchdulTypes" runat="server" Text='<%# Eval("ScheduleType")  %>' Visible="false" />
                                                        </td>
                                                        <td style="width: 30%;">
                                                            <asp:Label ID="lblName" runat="server" Text='<%# Eval("WorkFlowName") %>' />
                                                        </td>
                                                        <td style="width: 40%;">
                                                            <asp:Label ID="lblScheduleTime" runat="server" Text='<%# Eval("ScheduleTime") %>' />
                                                        </td>
                                                        <td style="width: 12%;">
                                                            <asp:CheckBox ID="chkEnable" runat="server" Checked='<%# Convert.ToBoolean(Eval("IsEnable")) %>'
                                                                OnCheckedChanged="chkEnable_CheckedChanged" AutoPostBack="true"></asp:CheckBox>
                                                        </td>
                                                        <td class="text-center">
                                                            <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                ToolTip="Edit" ImageUrl="../images/icons/pencil.png" CausesValidation="false" />
                                                            <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" CausesValidation="false" />
                                                            <asp:LinkButton ID="lnkBtnHistory" runat="server" ToolTip="History" CssClass="interval-icon text-right" Style="vertical-align: top; margin-left: 2px;" CausesValidation="false"></asp:LinkButton>
                                                        </td>
                                                        <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                            ConfirmText='<%# "Are you sure want to delete " + Eval("WorkFlowName") + " ? " %>'
                                                            OnClientCancel="CancelClick">
                                                        </TK1:ConfirmButtonExtender>
                                                    </tr>
                                                </ItemTemplate>
                                                <EmptyDataTemplate>
                                                    <div class="message warning align-center bold">
                                                        <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                                    </div>
                                                </EmptyDataTemplate>
                                            </asp:ListView>

                                        </div>

                                        <div class="form-group" runat="server" id="dvlvCPSL">
                                            <asp:ListView ID="lvCPSL" runat="server" OnItemEditing="lvCPSLItemEditing"
                                                OnItemDeleting="lvCPSLItemDeleting" OnPreRender="lvCPSLPreRender" OnItemDataBound="lvCPSL_ItemDataBound" DataKeyNames="Id">

                                                <LayoutTemplate>
                                                    <table class="table table-striped table-bordered table-condensed margin-bottom-none">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 4%;">
                                                                    <span>
                                                                        <img src="../Images/icons/replication_list.png" /></span>
                                                                </th>
                                                                <th style="width: 30%;">Name
                                                                </th>
                                                                <th style="width: 40%;">Schedule
                                                                </th>
                                                                <th style="width: 12%;">Enable
                                                                </th>
                                                                <th class="text-center">Action
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                    </table>
                                                    <div class="infraScheduleScroll">
                                                        <table class="table table-bordered">
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </LayoutTemplate>
                                                <ItemTemplate>
                                                    <tr>
                                                        <td style="width: 4%;">
                                                            <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                                            <asp:Label ID="lblCpslID" runat="server" Text='<%# Eval("CPSLId") %>' Visible="false" />
                                                            <asp:Label ID="lblCPSLTypes" runat="server" Text='<%# Eval("CPSLType")  %>' Visible="false" />
                                                        </td>
                                                        <td style="width: 30%;">
                                                            <asp:Label ID="lblName" runat="server" Text='<%# Eval("CPSLNames") %>' />
                                                        </td>
                                                        <td style="width: 40%;">
                                                            <asp:Label ID="lblCPSLTime" runat="server" Text='<%# Eval("CPSLTime") %>' />
                                                        </td>
                                                        <td style="width: 12%;">
                                                            <asp:CheckBox ID="chkCPSLEnable" runat="server" Checked='<%# Convert.ToBoolean(Eval("IsEnable")) %>'
                                                                OnCheckedChanged="chkCPSLEnable_CheckedChanged" AutoPostBack="true"></asp:CheckBox>
                                                        </td>
                                                        <td class="text-center">
                                                            <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                                ToolTip="Edit" ImageUrl="../images/icons/pencil.png" CausesValidation="false" />
                                                            <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                                ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" CausesValidation="false" />
                                                            <asp:LinkButton ID="lnkBtnHistory" runat="server" ToolTip="History" CssClass="interval-icon text-right" Style="vertical-align: top; margin-left: 2px;" CausesValidation="false"></asp:LinkButton>
                                                        </td>
                                                        <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                            ConfirmText='<%# "Are you sure want to delete " + Eval("CPSLNames") + " ? " %>'
                                                            OnClientCancel="CancelClick">
                                                        </TK1:ConfirmButtonExtender>
                                                    </tr>
                                                </ItemTemplate>
                                                <EmptyDataTemplate>
                                                    <div class="message warning align-center bold">
                                                        <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                                    </div>
                                                </EmptyDataTemplate>
                                            </asp:ListView>

                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </asp:Panel>
                </ContentTemplate>
            </asp:UpdatePanel>


        </div>
    </form>

    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script src="../Script/jquery.simple-dtpicker.js" type="text/javascript"></script>
    <script src="../Script/jquery.timepicker.min.js" type="text/javascript"></script> 
      <script src="../Script/AlertModal.js" type="text/javascript"></script> 
    <script src="../Script/jquery.modal.js"></script>
    <%--<script src="../Script/BusinessFunctionRPOSpan.js"></script>--%>
    <script type="text/javascript">
        $(document).ready(function () {
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();

            $(".infraScheduleScroll").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
                autoDraggerLength: false,
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });

        });
        function bindDelete(win) {
            var values = $("#ddlScript :selected").val();
            $.ajax({
                type: "POST",
                url: "InfraobjectScheduleWorkflow.aspx/DeleteSCPLScript",
                data: '{values: "' + values + '" }',
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                success: function Success(data) {
                    getDelete(data.d);
                }

            });


            CloseModel(win);

        }
        function getDelete(win) {
            if (win == 1) {

                location.pathname = location.pathname.replace(/(.*)\/[^/]*/, "$1/" + "InfraobjectScheduleWorkflow.aspx");


            }
        }
        function pageLoad() {
            var k = 0;
            //if (k == i)
            //    $("#ddlScript :selected").text() = "CPSL Script";
            $('#btnCPSLDelete').click(function () {
                k = 1;
                var values = $("#ddlScript :selected").val();
                var scripname = $("#ddlScript :selected").text();
                if (values > 1) {
                    OpenAlertModelLoad(' Do you want to Delete ' + scripname, bindDelete);
                    //}
                }

            });

            $('[id$=txtDateTime]').appendDtpicker();
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();

            $(".infraScheduleScroll").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
                autoDraggerLength: false,
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });
        }
    </script>


</body>
</html>

