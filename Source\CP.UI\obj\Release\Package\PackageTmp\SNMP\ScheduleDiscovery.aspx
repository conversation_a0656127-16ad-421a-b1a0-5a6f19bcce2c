﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ScheduleDiscovery.aspx.cs" Inherits="CP.UI.SNMP.ScheduleDiscovery" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style>
        .appleft
        {
            margin-left: 4px;
        }

        .appright
        {
            margin-right: 6px;
              margin-left:2px;
        }

        .savestyl
        {
            margin-right: 4px;
            margin-bottom:6px;
        }


        .apptop
        {
            float: right;
            display: inline;
            margin-top: 0px;
        }

        .appiconright
        {
            margin-right: 5px;
          
        }

        .bootstrap-select:not([class*="col"]) .btn, .bootstrap-select:not([class*="col"])
        {
            width: 207px;
        }

        .div-discover-btn
        {
            bottom: 10px;
            right: 0;
            position: absolute;
        }
    </style>
    <script>
        function Clear()
        {
            var obj = document.getElementById("ctl00_cphBody_lblSaveMsg");
            obj.style.display = "none";
            obj.style.visibility = "hidden";
            return true;
        }
       

        function isNumberKey(evt) {
            var charCode = (evt.which) ? evt.which : event.keyCode
            if (charCode != 46 && charCode > 31
              && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }
      
    </script>
</asp:Content>


<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
     <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
            <img src="../Images/business-process-icon.png" alt="View Application Discovery ">
            Schedule Discovery</h3>

        <asp:UpdatePanel ID="updPnlScan" runat="server" UpdateMode="Always">
            <ContentTemplate>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">

                        <label class="appright">ProfileName</label>


                        <input type="text" id="txtProfileName" class="form-control" placeholder="" runat="server" style="width: 9%" onfocus="Clear()" />
                        <asp:RequiredFieldValidator ID="rfvtxtProfileName" runat="server" CssClass="error" ErrorMessage="Enter profile Name" Display="Dynamic" ControlToValidate="txtProfileName"></asp:RequiredFieldValidator>
                        <asp:CustomValidator ID="CustomValidator1" runat="server" ControlToValidate="txtProfileName" Display="Dynamic" OnServerValidate="ValidationFunctionName" CssClass="error" ErrorMessage="Profile name aleady exist"></asp:CustomValidator>

                        

                        <label class="appright">Host From</label>


                        <input type="text" id="inIPAddressFrom" class="form-control" placeholder="IP Address" runat="server" onkeypress="return isNumberKey(event)" style="width: 9%" />
                        <asp:RequiredFieldValidator ID="rvfinIPAddressFrom" runat="server" CssClass="error" ErrorMessage="Enter source host" Display="Dynamic" ControlToValidate="inIPAddressFrom"></asp:RequiredFieldValidator>


                        <label class="appleft appright">To</label>


                        <input type="text" id="inIPAddressTo" class="form-control" placeholder="IP Address" runat="server" onkeypress="return isNumberKey(event)" style="width: 9%" />
                        <asp:RequiredFieldValidator ID="rfvinIPAddressTo" runat="server" CssClass="error" ErrorMessage="Enter destination host" Display="Dynamic" ControlToValidate="inIPAddressTo"></asp:RequiredFieldValidator>

                        <label class="appleft appright">OS</label>


                        <input type="text" id="inOsName" runat="server" style="width: 6%" class="form-control" placeholder="All" />

                        <label class="appleft appright">Applications</label>


                        <input type="text" id="inApp" runat="server" style="width: 6%" class="form-control appright" placeholder="All" />

                        <hr class="margin-bottom-none" style="margin: 7px 0;" />
                        <div class="margin-right div-discover-btn">
                            <asp:Label ID="lblSaveMsg" CssClass="text-right padding" runat="server" Text="Saved successfully..." ForeColor="Green" Visible="false"></asp:Label>
                            <asp:Button ID="btnSaveDiscoverySchedule" runat="server" CssClass="btn btn-primary savestyl" Text="Save" OnClick="btnSaveDiscoverySchedule_Click" />
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
