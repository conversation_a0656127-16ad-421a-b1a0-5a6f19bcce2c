﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ComponentFailureDetails.ascx.cs" Inherits="CP.UI.ComponentFailureDetails" %>
<asp:UpdatePanel ID="updcompfail" runat="server" UpdateMode="Conditional">

    <ContentTemplate>

<div class="widget widget-heading-simple widget-body-white">
    <div class="widget-body">
        <div class="cioheader">
            <div class="title">
                Component Failure Details
                <asp:ImageButton ID="imgBtnReport" runat="server" ImageUrl="../../Images/CIO/report-icon.png" Style="float: right;" OnClick="imgBtnReport_Click" />
            </div>
            <asp:Label ID="lblcurrentmnth" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
            <span class="subhead">(Current Month)</span>

        </div>
        <div class="ciocontent">

            <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 2px;">
                <div>
                    <asp:Label ID="lblTotalCount" runat="server" CssClass="count" Text="0"></asp:Label>
                </div>
                <asp:Label Text="Components" runat="server" CssClass="subhead" ID="lblComponent" />
            </div>

            <div class="text-center">
                <%--<img src="../Images/CIO/bar-graph-02.png" />--%>
                <telerik:RadHtmlChart runat="server" ID="BarChart" Width="190px" Height="120px" Transitions="true" Skin="Silk">
                    <PlotArea>

                        <XAxis AxisCrossingValue="0" Color="white" MajorTickType="none" MinorTickType="none"
                            Reversed="false">
                            <MajorGridLines Visible="false" />
                            <MinorGridLines Visible="false" />
                            <LabelsAppearance DataFormatString="{0}" RotationAngle="0" Skip="0" Step="1" Color="#222222"></LabelsAppearance>

                        </XAxis>
                        <YAxis AxisCrossingValue="0" Color="black" MajorTickSize="1" MajorTickType="Outside"
                            MinorTickType="None" Reversed="false" Visible="false">
                            <MajorGridLines Visible="false" />
                            <MinorGridLines Visible="false" />
                            <LabelsAppearance DataFormatString="{0}" RotationAngle="0" Skip="0" Step="1" Visible="false"></LabelsAppearance>
                            <%-- <TitleAppearance Position="Center" RotationAngle="0" Text="Sum"></TitleAppearance>--%>
                        </YAxis>
                    </PlotArea>
                    <Appearance>
                        <FillStyle BackgroundColor="Transparent"></FillStyle>
                    </Appearance>

                    <Legend>
                        <Appearance BackgroundColor="Transparent" Position="Bottom"></Appearance>
                    </Legend>
                </telerik:RadHtmlChart>
                <div>

                    <asp:Label ID="Label50" runat="server" CssClass="leg2"></asp:Label>
                    <span class="legtext">Server</span>
                    <asp:Label ID="Label51" runat="server" CssClass="leg1"></asp:Label>
                    <span class="legtext">DB</span>
                    <asp:Label ID="Label1" runat="server" CssClass="leg3"></asp:Label>
                    <span class="legtext">Repl</span>
                </div>
            </div>

        </div>
        <div class="ciofooter">
            <div class="col-md-9 padding-none-LR">
                <img src="../Images/CIO/cogwheel_icon.png" />
            </div>
            <div class="col-md-3 padding-none-LR text-right">
                   <span class="ciopopover" data-toggle="popover" data-title="Component Failure Details"
                    data-content="1) Continuity Patrol monitors Components as part of its monitoring infrastructure and records failures<br />
                                  2) Component Failure BreakUp by Storage/DB/Server, etc is calculated and stored as count data on daily basis" data-placement="top" data-html="true"></span>
            </div>
        </div>
    </div>
</div>
     <asp:UpdateProgress ID="UpdateProgress1" runat="server" AssociatedUpdatePanelID="updcompfail">
            <ProgressTemplate>
                <div id="imgLoading" class="loading-mask">
                    <span>Report Generating...</span>
                </div>
            </ProgressTemplate>
        </asp:UpdateProgress>

    </ContentTemplate>
</asp:UpdatePanel>
