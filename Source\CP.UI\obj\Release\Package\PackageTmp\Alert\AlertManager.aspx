﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AlertManager.aspx.cs" Inherits="CP.UI.AlertManager"
    MasterPageFile="~/Master/BcmsDefault.Master" Title="Continuity Patrol :: Alert-AlertManagee" %>

<%@ Register TagPrefix="TK" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <asp:UpdatePanel ID="upAlert1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
            <ContentTemplate>

               
                <h3>
                    <img src="../Images/alerts-icon.png" alt="Alert Manager" width="16" class="vertical-baseline"  />
                    Alerts Manager</h3>
               
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Select InfraObject <span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlGroup" CssClass="selectpicker col-md-6" data-style="btn-default"
                                            runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlGroup_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvGroup" runat="server" ControlToValidate="ddlGroup" CssClass="error"
                                            InitialValue="0" Display="Dynamic" ErrorMessage="Select InfraObject" ValidationGroup="save"></asp:RequiredFieldValidator>
                                        <asp:Label ID="lblGroup" runat="server" CssClass="Rerror" Visible="false" ForeColor="Red">Please
                                 Select Infra Object</asp:Label>
                                    </div>
                                </div>

                                <asp:ListView ID="ListView1" runat="server" Visible="true" OnItemCommand="LvAlertItemCommand" OnItemEditing="ListView1ItemEditing" OnItemDataBound="lv_AlertsItemDataBound">
                                    <LayoutTemplate>
                                        <table class="table table-bordered margin-bottom-none ">
                                            <thead>
                                                <tr>
                                                    <th style="width: 4%;">No.
                                                    </th>
                                                    <th style="width: 18%;">Alert Name
                                                    </th>
                                                    <th style="width: 10%;" class="text-center">Priority
                                                    </th>
                                                    <th style="width: 20%;">Description
                                                    </th>
                                                    <th style="width: 12%;" class="text-center">Subscribe Alert
                                                    </th>
                                                    <th style="width: 12%;" class="text-center">SNMP Trap
                                                    </th>
                                                    <th style="width: 12%;" class="text-center">Raise Incident
                                                    </th>
                                                    <th class="text-center">Incident Manager
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="entity-list-scroll">
                                            <table class="table table-bordered margin-bottom-none">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr  >
                                            <td style="width: 4%;">
                                                <%#Container.DataItemIndex+1 %><asp:Label ID="lblID" Visible="false" runat="server"
                                                    Text='<%# Eval("ID") %>' />
                                            </td>
                                            <td style="width: 18%;">
                                                <asp:Label ID="lblNames" runat="server" Text='<%# Eval("Names") %>' Visible="true" />
                                            </td>
                                            <td style="width: 10%;" class="text-center">
                                                <asp:ImageButton ID="imgSeverity" ImageUrl='<%# GetSeverityTypeCss(Eval("Priority")) %>'
                                                    ToolTip='<%# GetRowStyle(Eval("Priority")) %>' runat="server" />
                                                <asp:Label ID="lblPriority" runat="server" Visible="false" Text='<%#Eval("Priority")%>' />
                                            </td>
                                            <td style="width: 20%;">
                                                <asp:Label ID="lblDescription" runat="server" Text='<%#Eval("Description")%>' />
                                            </td>
                                            <td style="width: 12%;" class="text-center">
                                                <asp:CheckBox ID="CblstAlertType" AutoPostBack="true"
                                                    runat="server" Enabled="true" Visible="true" Checked='<%# IsChecked( Eval("Status")) %>' />
                                            </td> 
                                            <td style="width: 12%;" class="text-center">
                                                <asp:CheckBox ID="cblstSnmp" Checked='<%# IsCheckedSnmp( Eval("snmp")) %>' runat="server"
                                                    Enabled="true" Visible="true" />
                                            </td>

                                            <td style="width: 12%;" class="text-center">
                                                <asp:CheckBox ID="chkIncident" Checked='<%# IsIncident( Eval("Incident")) %>' AutoPostBack="true" OnCheckedChanged="chkIncident_CheckedChanged" runat="server"
                                                    Enabled="true" Visible="true" />
                                            </td>
                                            <td class="text-center"> 
                                                <asp:ImageButton ID="imgbtn" runat="server" Enabled="<%#!IsUserOperator%>" CommandArgument='<%# Eval("Names") %>' CommandName="Edit" AlternateText="Edit"
                                                    ToolTip="Raise Incident" />
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                    <EmptyDataTemplate>
                                        <div class="warning text-left text-bold">
                                            <asp:Label ID="lblError" Text="No Record Found" ForeColor="Red" runat="server" Visible="true"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>
                                </asp:ListView>

                                <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 100%; height: 100%;">
                                </asp:Panel>

                                <asp:Button ID="btnShowPopup" runat="server" Style="display: none" />
                                <asp:Panel ID="Panel_incidentmanager" runat="server" Visible="false" Width="100%">
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 875px;">
                                            <div class="modal-content widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">Incident Manager </h3>
                                                    <asp:LinkButton ID="Lkbtncloseincident" runat="server" ToolTip="Close window" OnClick="Lkbtncloseincident_Click" class="close">×</asp:LinkButton>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-12 form-horizontal uniformjs">
                                                            <div class="form-group">
                                                                <label class="col-md-3 control-label ">
                                                                    InfraObject Name
                                                                </label>

                                                                <div class="col-md-9">
                                                                    <asp:Label ID="lblGroupName" Style="vertical-align: sub;" runat="server"></asp:Label>
                                                                </div>
                                                            </div>
                                                            <div class="form-group">
                                                                <label class="col-md-3 control-label ">
                                                                    Incident Number
                                                                </label>

                                                                <div class="col-md-9">
                                                                    <asp:Label ID="lblAuto" Style="vertical-align: sub;" runat="server"></asp:Label>
                                                                </div>
                                                            </div>

                                                            <div class="form-group">
                                                                <label class="col-md-3 control-label ">
                                                                    <asp:Label ID="lblsmtp" runat="server" Text="Incident Name"></asp:Label><span class="inactive">*</span>
                                                                </label>
                                                                <div class="col-md-9">
                                                                    <asp:TextBox ID="txtIncident" runat="server" CssClass="form-control"></asp:TextBox>
                                                                    <asp:RequiredFieldValidator ID="rfvIncident" runat="server" Enabled="true"
                                                                        ControlToValidate="txtIncident" Display="Dynamic" CssClass="error"
                                                                        ErrorMessage="Enter Incident" ValidationGroup="incident"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </div>
                                                            <div class="form-group">
                                                                <label class="col-md-3 control-label ">
                                                                    <asp:Label ID="lblport" runat="server" Text="Description"></asp:Label><span class="inactive">*</span>
                                                                </label>
                                                                <div class="col-md-9">
                                                                    <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine"></asp:TextBox>
                                                                    <asp:RequiredFieldValidator ID="rfvDescription" runat="server" Enabled="true"
                                                                        ControlToValidate="txtDescription" Display="Dynamic" CssClass="error"
                                                                        ErrorMessage="Enter Description" ValidationGroup="incident"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </div>
                                                            <div class="form-group">
                                                                <label class="col-md-3 control-label ">
                                                                    <asp:Label ID="Label4" runat="server" Text="Incident Proccessing Mechanism"></asp:Label><span class="inactive">*</span>
                                                                </label>

                                                                <div class="col-md-9">

                                                                    <asp:DropDownList ID="ddlWorkflow" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default">
                                                                    </asp:DropDownList>
                                                                    <asp:RequiredFieldValidator ID="rfvWorkflow" runat="server" Enabled="true"
                                                                        ControlToValidate="ddlWorkflow" Display="Dynamic" InitialValue="0" CssClass="error"
                                                                        ErrorMessage="select workflow" ValidationGroup="incident"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <asp:Label ID="lblsmtpsavemessage" runat="server" Visible="False" ForeColor="#999999"></asp:Label>
                                                <div class="modal-footer">
                                                    <div class="col-md-3">
                                                    </div>

                                                    <div class="col-md-9 text-right">
                                                        <asp:Button ID="btnsave" runat="server" OnClick="BtnsaveIncidentClick" ValidationGroup="incident" Text="Save" CssClass="btn btn-primary" Width="20%" />
                                                        <asp:Button ID="btnclose" runat="server" OnClick="BtncancelIncidentClick" Text="Close" CssClass="btn btn-default" Width="20%" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </asp:Panel>
                                <asp:Panel ID="Panel_raiseIncident" runat="server" Width="100%" Visible="false">
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 420px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <h3 class="modal-title">Raise Incident </h3>
                                                    <asp:LinkButton ID="LinkButton1" runat="server" ToolTip="Close window" OnClick="Lkbtncloseraiseincident_Click" class="close">×</asp:LinkButton>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="col-md-12 form-horizontal uniformjs">
                                                        <div class="form-group">
                                                            <label class="text-danger text-center">
                                                                Please raise incident first.
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </asp:Panel>

                            </div>
                        </div>
                        <div class="row margin-top" runat="server" id="divButtons">
                            <div class="col-md-7 col-md-push-3">
                                <asp:Button ID="btnsave_newuser" CssClass="btn btn-primary" Width="20%" runat="server"
                                    Text="Save" ValidationGroup="save" OnClick="BtnsaveNewuserClick" CausesValidation="true" />
                                <asp:Button ID="btncancel" CssClass="btn btn-default" Width="20%" runat="server"
                                    Text="Cancel" OnClick="btncancel_Click" />
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script type="text/javascript">
        function validateCheckBoxes() {
            var isValid = false;
            var gridView = document.getElementById('<%= ListView1.ClientID %>');
              for (var i = 1; i < gridView.rows.length; i++) {
                  var inputs = gridView.rows[i].getElementsByTagName('input');
                  if (inputs != null) {
                      if (inputs[0].type == "checkbox") {
                          if (inputs[0].checked) {
                              isValid = true;
                              return true;
                          }
                      }
                  }
              }
              alert("Please select atleast one checkbox");
              return false;
          }
          $(document).ready(function () {
              $('.entity-list-scroll').mCustomScrollbar({
                  axis: "y",
                  advanced: {
                      updateOnContentResize: true,
                      autoExpandHorizontalScroll: true
                  }
              });
          });
          Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
          function EndRequestHandler(sender, args) {
              $('.entity-list-scroll').mCustomScrollbar({
                  axis: "y",
                  advanced: {
                      updateOnContentResize: true,
                      autoExpandHorizontalScroll: true
                  }
              });
          }
    </script>
</asp:Content>
