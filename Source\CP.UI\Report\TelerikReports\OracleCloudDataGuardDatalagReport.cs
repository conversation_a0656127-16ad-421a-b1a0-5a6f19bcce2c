namespace CP.UI.Report.TelerikReports
{
    using CP.Common.DatabaseEntity;
    using log4net;
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Data;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;
    using CP.Common.Shared;
    /// <summary>
    /// Summary description for OracleCloudDataGuardDatalagReport.
    /// </summary>
    public partial class OracleCloudDataGuardDatalagReport : Telerik.Reporting.Report
    {

        private readonly ILog _logger = LogManager.GetLogger(typeof(OracleCloudDataGuardDatalagReport));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();
        public OracleCloudDataGuardDatalagReport()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }
        private void showtable()
        {
            try
            {
                var dataTable = new DataTable();

                string strDate = Convert.ToDateTime(this.ReportParameters["iStartDate"].Value).ToString("yyyy-MM-dd");
                string endDate = Convert.ToDateTime(this.ReportParameters["iEndDate"].Value).ToString("yyyy-MM-dd");
                int infraObjectId = Convert.ToInt32(this.ReportParameters["iInfraId"].Value);

                IList<OracleCloudDataGuard_DBSystemDetails> Oraclecloud_list = new List<OracleCloudDataGuard_DBSystemDetails>();
                Oraclecloud_list = Facade.Get_OracleCloudDataGuard_DBSystemDetails_ByDate(infraObjectId, strDate, endDate);

                if (Oraclecloud_list != null && Oraclecloud_list.Count > 0)
                {
                    dataTable.Columns.Add("Sr.No.");
                    dataTable.Columns.Add("PRCompartmentName");
                    dataTable.Columns.Add("DRCompartmentName");
                    dataTable.Columns.Add("DatabaseName");
                    dataTable.Columns.Add("PRDBSystemDisplayName");
                    dataTable.Columns.Add("DRDBSystemDisplayName");
                    dataTable.Columns.Add("PRDBSystemState");
                    dataTable.Columns.Add("DRDBSystemState");
                    dataTable.Columns.Add("PRDBSystemAvailabilityDomain");
                    dataTable.Columns.Add("DRDBSystemAvailabilityDomain");
                    dataTable.Columns.Add("ApplyLag");
                    dataTable.Columns.Add("CreateDate");
                    _logger.Info("Data Mapping Start For Report.");





                    int i = 1;
                    foreach (OracleCloudDataGuard_DBSystemDetails ocdg in Oraclecloud_list)
                    {
                        DataRow dr = dataTable.NewRow();
                        dr["Sr.No."] = i.ToString();
                        dr["PRCompartmentName"] = ocdg.PRCompartmentName != null ? ocdg.PRCompartmentName : "N/A";
                        dr["DRCompartmentName"] = ocdg.DRCompartmentName != null ? ocdg.DRCompartmentName : "N/A";
                        dr["DatabaseName"] = ocdg.PRDatabaseName != null ? ocdg.PRDatabaseName : "N/A";

                          InfraObject infradetails = Facade.GetInfraObjectById(ocdg.InfraObjectId);


                          //if (infradetails.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted || infradetails.DROperationStatus == (int)InfraWorkflowOperation.FailOverCompleted)
                          //{

                          //    dr["PRDBSystemDisplayName"] = ocdg.DRDBSystemDisplayName != null ? ocdg.DRDBSystemDisplayName : "N/A";
                          //    dr["DRDBSystemDisplayName"] = ocdg.PRDBSystemDisplayName != null ? ocdg.PRDBSystemDisplayName : "N/A";
                          //}
                          //else
                          //{
                              dr["PRDBSystemDisplayName"] = ocdg.PRDBSystemDisplayName != null ? ocdg.PRDBSystemDisplayName : "N/A";
                              dr["DRDBSystemDisplayName"] = ocdg.DRDBSystemDisplayName != null ? ocdg.DRDBSystemDisplayName : "N/A";

                          //}
                        dr["PRDBSystemState"] = ocdg.PRDBSystemState != null ? ocdg.PRDBSystemState : "N/A";
                        dr["DRDBSystemState"] = ocdg.DRDBSystemState != null ? ocdg.DRDBSystemState : "N/A";
                        dr["PRDBSystemAvailabilityDomain"] = ocdg.PRDBSystemAvailabilityDomain != null ? ocdg.PRDBSystemAvailabilityDomain : "N/A";
                        dr["DRDBSystemAvailabilityDomain"] = ocdg.DRDBSystemAvailabilityDomain != null ? ocdg.DRDBSystemAvailabilityDomain : "N/A";
                        dr["ApplyLag"] = ocdg.ApplyLag != null ? Utility.ConvertSecondsToHHMMSS(ocdg.ApplyLag) : "N/A";



                        dr["CreateDate"] = Utility.Formatdate(Convert.ToDateTime(ocdg.CreateDate).ToString("MM-dd-yyyy HH:mm:ss"));
                        //   dr["DataLag"] = vsmoni.CreateDate != null ? vsmoni.CreateDate : "N/A";
                        i++;
                        dataTable.Rows.Add(dr);

                    }
                }
                this.DataSource = dataTable;
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method, InnerException Message " + ex.InnerException.Message);
            }
        }



      

        private void OracleCloudDataGuardDatalagReport_NeedDataSource(object sender, EventArgs e)
        {
            showtable();
        }
    }
}