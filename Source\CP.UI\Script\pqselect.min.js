/*!
 * ParamQuery Select v2.0.0
 *
 * Copyright (c) 2015-2022 Paramvir Dhindsa (http://paramquery.com)
 * Released under GNU General Public License v3
 * http://paramquery.com/license
 *
 */
if("function"==typeof require){var jQuery=require("jquery"),pq={};module.exports=pq}else var pq=window.pq||{};!function(a){"use strict";a.support.touch="ontouchend"in document;var b={};b.options=function(){var b=a.ui.keyCode;return{radio:!1,singlePlaceholder:"Select an option",checkbox:!1,displayText:"{0} of {1} selected",maxDisplay:4,maxHeight:300,maxSelect:0,multiplePlaceholder:"Select options",selectallText:"Select All",closeOnWindowScroll:!0,closeOnWindowResize:!0,itemCls:"pq-select-item ui-corner-all ui-state-default",bootstrap:{on:!1,btn:"btn btn-default",popupCont:"panel panel-default",selectCls:"label label-info",itemCls:"label label-default",closeIcon:"glyphicon glyphicon-remove",searchIcon:"glyphicon glyphicon-search",hoverCls:""},position:{my:"left top",at:"left bottom",collision:"flipfit"},kcOpen:[b.DOWN,b.ENTER,b.UP],deselect:!0,hoverCls:"pq-state-hover",optionsResizable:{handles:"e,s"},search:!0,searchRule:"contain",_selectCls:"pq-state-select",selectCls:"ui-state-highlight",width:null,rowHt:25,maxSelectReach:null,maxSelectExceed:null}}(),b.destroy=function(){this._super();for(var a in this)delete this[a]},b._setOption=function(a,b){this._super(a,b),"disabled"==a?(b&&this.close(),this.$button[b?"addClass":"removeClass"]("ui-state-disabled")):"maxHeight"==a&&(this.$popup.css({maxHeight:b,height:""}),this.setMenuHt(),this.renderView())},a.widget("paramquery.pqSelect",b),pq.select=function(b,c){var d=a(b).pqSelect(c),e=d.data("paramqueryPqSelect")||d.data("paramquery-pqSelect");return e},b=a.paramquery.pqSelect.prototype,b._setButtonWidth=function(){var a=this.element;a.show();var b=this.options,c=b.width?b.width:a[0].offsetWidth,c=1*c==c?c+"px":c;a.hide(),this.$button[0].style.width=c},b._create=function(){if(!this.element.is("select"))throw"pqSelect only for select elements";var b=this,c=this.options,d=c.bootstrap,e=d.on,f=this.element,g=f.attr("name"),h=f.attr("multiple"),i=b.eventNamespace,j=a(["<div class='pq-select-button pq-no-capture ",e?d.btn:"ui-state-default ui-widget ui-corner-all","' "," data-name='",g,"' tabindex='0'>",h?"":"<span class='pq-icon "+(e?"caret":"ui-icon ui-icon-triangle-1-s")+"'></span>","<div class='pq-select-text'></div>","</div>"].join(""));e&&(c.selectCls+=" "+d.selectCls,c.itemCls+=" "+d.itemCls),this.$button=j,this.multiple=h?!0:!1,this.selector=".pq-select-option.ui-state-enable:visible",this._setButtonWidth(),f.after(j),j.attr("name",g),j.addClass(f.attr("class")),f.addClass("pq-select"),c.data?this.data=c.data:this._extractData(),b._createPopup(),b._createMenu(),j.on({click:function(){if(!c.disabled){var d=a(this).parent(".pq-select-item"),e=parseInt(d.attr("data-id"));return b.select(e,!1),b.setText(),b.setSelectAllState(),b.focus(),b.triggerChange(),!1}}},".pq-item-close"),j.on({click:function(){return c.disabled?void 0:(b.toggle(),!1)},focus:function(){c.disabled||a(this).addClass("ui-state-hover")},blur:function(){a(this).removeClass("ui-state-hover")},mousedown:function(){c.disabled||a(this).addClass("ui-state-active")},mouseup:function(){c.disabled||a(this).removeClass("ui-state-active")},keydown:function(d){if(!c.disabled){var e=d.keyCode,f=c.kcOpen,g=a.ui.keyCode;-1!==a.inArray(e,f)?b.open():e===g.ESCAPE?b.close():e===g.SPACE&&b.toggle()}}}),b.setText(),b.setSelectAllState(),a(window).on("resize"+i,b.onWindowResize.bind(b)).on("scroll"+i,b.onWindowScroll.bind(b))},b.onWindowResize=function(a){var b=a.originalEvent;b&&"mousemove"==b.type||!this.options.closeOnWindowResize||this.close()},b.onWindowScroll=function(){this.options.closeOnWindowScroll&&!this._preventClose&&this.close()},b.focus=function(){var b=this,c=b.$search;a.support.touch||(c[0]?c.focus():b.$popup.focus(),b._highlight())},b.getOption=function(b){return a("#"+this.getOptionId(b))},b.getOptionId=function(a){return"pq-option-"+this.uuid+"-"+a},b._move=function(a,b,c){var d,e,f=this,g=f.optionIndxV,h=null==g?-1:g,i=f.options,j=f.$menu,k=j[0],l=k.offsetHeight,m=f.dataV,n=m.length,o=i.rowHt,p=function(a){return!m[a].disabled&&!m[a].pq_title},q=Math.floor((l-17)/o);if(c)d=a?n-1:0;else if(b)d=a?h+q:h-q;else if(a){for(e=h+1;n>e;e++)if(p(e)){d=e;break}}else for(e=h-1;e>=0;e--)if(p(e)){d=e;break}0>d&&(d=0),d>=n&&(d=n-1),f.focusOption(d)},b.focusOption=function(a){var b,c=this,d=c.options,e=d.rowHt,f=a*e,g=f+e,h=c.$menu,i=h[0],j=i.scrollTop,k=i.offsetHeight,l=j+k,m=function(){c._highlight(a)};j>f?b=f:g>l&&(b=g-k),null!=b?(h.one("scroll",function(){m()}),i.scrollTop=b):m()},b._onkeydown=function(b){var c,d=b.keyCode,e=this,f=e.optionIndxV,g=a.ui.keyCode;return d===g.DOWN||d===g.UP?(e._move(d===g.DOWN),c=!1):d===g.PAGE_DOWN||d===g.PAGE_UP?(e._move(d==g.PAGE_DOWN,!0),c=!1):d==g.HOME||d==g.END?(e._move(d==g.END,!1,!0),c=!1):d===g.TAB?(e.close(),c=!1):d===g.ESCAPE?e.close():d===g.ENTER&&null!=f&&(e.getOption(f).trigger("label_changed"),c=!1),c},b.search=function(a){for(var b=this.data,c=this.options.searchRule,d="contain"===c,e=0,f=b.length;f>e;e++){var g=b[e],h=(g.text+"").toUpperCase(),i=h.indexOf(a);g.searchIndx=null,-1===i?g.hidden=!0:d===!1&&i>0?g.hidden=!0:(g.hidden=!1,g.searchIndx=i)}},b._onkeyupsearch=function(b){var c=a(b.target),d=this,e=a.trim(c.val()).toUpperCase();(e||d.searchText)&&e!=d.searchText&&(d.searchText=e,d.search(e),d._createMenu(),d.renderView(),d.positionPopup(),d.setSelectAllState())},b.onChange=function(a){var b=this,c=b.dataV[a],d=c.pq_indx,e=!c.selected,f=b.options,g=b.multiple,h=f.maxSelect,i=b.selectIndx;if(g){if(e&&h&&i.length>=h)return b._trigger("maxSelectExceed",null,{option:b.$options[d]}),b.focus(),!1}else if(i.length){var j=i[0];if(d===j)return!1;e&&b.select(j,!1)}b.select(d,e),b.setText(),b.setSelectAllState(),g?h&&i.length>=h?b._trigger("maxSelectReach",null,{option:b.$options[d]})!==!1&&b.close():b.focus():b.close(),b.triggerChange()},b.setSelectAllState=function(){var a=this.$popup.find(".pq-select-all input"),b=a[0];if(b){for(var c,d,e=this.dataV,f=0,g=0,h=0,i=e.length;i>h;h++)d=e[h],d.disabled||d.pq_title||(f++,d.selected&&g++);c=f===g,a.prop("checked",c),b.indeterminate=c?!1:g>0}},b.getInstance=function(){return{select:this}},b.select=function(b,c){var d,e=this,f=e.selectIndx,g=e.options,h=e.data[b],i=e.$options,j=i?a(i[b]):a(),k=e.getOption(h.pq_indxV),l=k.find("input");k[c?"addClass":"removeClass"](g._selectCls+" "+g.selectCls),l.prop("checked",c),h.selected=c,e.multiple?(d=f.indexOf(b),c?-1==d&&f.push(b):d>-1&&f.splice(d,1),j.prop("selected",c)):c?(f.length&&(this.data[f[0]].selected=!1),f[0]=b,j.prop("selected",c)):(e.selectIndx=[0],this.data[0].selected=!0,(i?a(i[0]):a()).prop("selected",!0))},b.triggerChange=function(){this._trigger("change"),this.element.trigger("change")},b._extractData=function(){var b,c,d=this.data=[],e=this.element,f=e.find("option,optgroup"),g=!1,h=!1;this.$options=e.find("option");for(var i=0,j=f.length;j>i;i++){var k=f[i],l=a(k);if("optgroup"==k.nodeName.toLowerCase())c=l.attr("label"),g=!0,h=l.prop("disabled");else{var m=l.prop("selected"),n=l.prop("disabled");!n&&g&&(n=h),b=l.text(),d.push({selected:m,disabled:n,text:b,optgroup:c})}}this.grouping=g},b.exportData=function(){return JSON.stringify(this.data,null,2)},b.refresh=function(){this.search(""),this._setButtonWidth(),this._createPopup(),this._createMenu(),this.setText(),this.setSelectAllState()},b.refreshData=function(){this._extractData(),this.refresh()},b._createPopup=function(){var b=this,c=this.options,d=c.bootstrap,e=d.on,f=b.multiple,g="",h="";f&&c.selectallText&&!c.maxSelect&&(h=["<label class='pq-select-all ui-widget-header ui-corner-all'>","<span class='ui-icon ui-icon-close ",e?d.closeIcon:"","'></span>","<input type='checkbox' >",c.selectallText,"</label>"].join("")),c.search&&(g=["<div class='pq-select-search-div ui-corner-all'>","<span class='ui-icon ui-icon-search ",e?d.searchIcon:"","' />","<div class='pq-select-search-div1'>","<input type='text' class='pq-select-search-input' autocomplete='off' />","</div>","</div>"].join(""));var i=a(["<div class='pq-select-popup-cont ",e?d.popupCont:"","'>","<div class='pq-select-popup ui-widget-content ui-corner-all' tabindex='-1'>",h,g,"<div class='pq-select-menu'><div style='margin:0;padding:0;' ></div></div>","</div><div class='pq-select-shadow-fix'></div></div>"].join("")),j=i.children(".pq-select-popup");b.$menu=j.find(".pq-select-menu").on("scroll",b.onScroll.bind(b)).on("mousemove",b.onMouseMove.bind(b)),i.css({"font-family":this.$button.css("font-family"),"font-size":this.$button.css("font-size")}),j.css("maxHeight",c.maxHeight).on({keydown:function(a){return b._onkeydown(a)}}).on({mouseenter:function(){b._highlight(b.getIndxV(this.id))},label_changed:function(){var a=this.id;return a?b.onChange(b.getIndxV(a)):void 0}},".pq-select-option.ui-state-enable"),j.find(".ui-icon-close").on({click:function(){return b.close(),!1}}),f&&c.checkbox||!f&&c.radio?j.on({click:function(){a(this).closest("label").trigger("label_changed")}},"label.pq-select-option.ui-state-enable input"):j.on({click:function(){a(this).trigger("label_changed")}},"label.pq-select-option.ui-state-enable"),b.$popupCont&&b.$popupCont.remove(),b.$popupCont=i,j.resizable(a.extend(!0,{stop:b.onResized.bind(b)},c.optionsResizable)),b.$popup=j,b.$search=j.find(".pq-select-search-input").on({keyup:function(a){return b._onkeyupsearch(a)}}),b.$selectAll=j.find(".pq-select-all").on({change:b.onSelectAll.bind(b)},"input"),a(document.body).append(i)},b.onSelectAll=function(b){for(var c,d=a(b.target),e=this,f=d.prop("checked")?!0:!1,g=e.dataV,h=0,i=e.$options;h<g.length;h++)c=g[h],c.disabled||c.selected===f||(c.selected=f,a(i[c.pq_indx]).prop("selected",f));e._createMenu(),e.renderView(),e.setText(),e.focus(),e.triggerChange()},b.onResized=function(a,b){this.width=b.size.width,this.option("maxHeight",b.size.height)},b.getIndxV=function(a){return 1*a.split("-")[3]},b.getIndx=function(a){return this.dataV[this.getIndxV(a)].pq_indx},b._createMenu=function(){var a,b,c=this,d=c.data,e=c.options.rowHt,f=c.selectIndx=[],g=c.dataV=[],h=0,i=c.$menu.children();d.forEach(function(c,d){c.pq_indx=d,a=c.optgroup,(0!=d||""!=c.text)&&(c.selected&&f.push(d),b===a||c.hidden||(g[h++]={pq_title:!0,text:a},b=a),c.hidden||(c.pq_indxV=h,g[h++]=c))}),c._initV=c._finalV=null,i.html(""),i.height(g.length*e),c.setMenuHt()},b.onMouseMove=function(){this.preventClose()},b.preventClose=function(){var a=this;a._preventClose=!0,clearTimeout(a.mmtimerID),a.mmtimerID=setTimeout(function(){a._preventClose=!1},2e3)},b.getViewPortIndx=function(){var a=this.$menu[0],b=this.options.rowHt,c=a.scrollTop,d=Math.floor(c/b),e=Math.floor((c+a.offsetHeight)/b);return[d,e]},b.renderView=function(){var a,b,c,d,e,f,g=this,h=g.uuid,i=g.getViewPortIndx(),j=i[0],k=i[1],l=g._initV,m=g._finalV,n=null!=l,o=g.options,p=o.search?(g.searchText||"").length:0,q=" "+o._selectCls+" "+o.selectCls+" ",r=g.multiple,s=r?o.checkbox?'type="checkbox"':"":o.radio?'type="radio"':"",t=[],u=s?"pq-left-input":g.grouping?"pq-left-group":"",v=o.rowHt,w=function(a){return"style='position:absolute;top:"+a*v+"px;width:100%;left:0;height:"+v+"px;overflow:hidden;'"},x=g.dataV;if(n)for(d=l;m>=d;d++)(j>d||d>k)&&g.getOption(d).remove();for(d=j;k>=d;d++)if(e=x[d]){var a=e.disabled,y=e.selected,z=e.text;if(n&&d>=l&&m>=d)continue;if(e.pq_title)t.push("<label ",w(d)," class='pq-select-optgroup'"," id='",g.getOptionId(d),"'>","<span>",z,"</span>","</label>");else{var A=y?' checked="checked" ':"",B=y?q:"",C=a?' disabled="disabled" ':"",b=a?"ui-state-disabled":"ui-state-enable";p&&(c=e.searchIndx,z=z.substr(0,c)+"<span class='pq-select-search-highlight'>"+z.substr(c,p)+"</span>"+z.substr(c+p,z.length)),t.push("<label ",w(d)," class='pq-select-option ",B,b,"'"," id='",g.getOptionId(d),"'>",s?"<input name='pq-select-input-"+h+"' "+s+" "+A+C+" >":"","<span class='",u,"'>",z,"</span>","</label>")}}g._initV=j,g._finalV=k,f=g.$menu.children(),f.append(t.join(""))},b.onScroll=function(){this.renderView(),this.preventClose()},b._highlight=function(a){var b=this,c=b.options.hoverCls,d=b._initV,e=b._finalV,f=function(a){return null!=a&&a>=d&&e>=a},g=b.optionIndxV,a=f(a)?a:f(g)?g:null,h=null!=a?b.getOption(a):b.$menu.find(".pq-select-option.ui-state-enable:visible:first"),i=h[0];i&&(a=b.getIndxV(i.id),null!=g&&b.getOption(g).removeClass(c),h.addClass(c),b.optionIndxV=a,b._trigger("focused",{label:i,rowIndx:a}))},b._setPopupWidth=function(){this.$popupCont.width(this.width||this.$button[0].offsetWidth)},b.positionPopup=function(){var b=this.options,c=this.$button,d=a.extend({of:c},b.position),e=this.$popupCont;this._setPopupWidth(),e.position(d)},b.isOpen=function(){return this.$popupCont&&"block"==this.$popupCont.css("display")?!0:!1},b.setMenuHt=function(){var a=this,b=a.options,c=b.rowHt,d=a.$popup.find(".pq-select-search-div").outerHeight(!0)+a.$selectAll.outerHeight(!0),e=a.dataV.length,f=e*c,g=Math.min(f+d,b.maxHeight),h=g-d;a.$menu.css("height",h+"px")},b.open=function(){var b=this,c=b.$popupCont;return b.isOpen()?!1:(c.show(),b.positionPopup(),b.setMenuHt(),b.renderView(),a(document).on("mousedown"+b.eventNamespace,function(c){var d=a(c.target);d.closest(b.$popupCont)[0]||d.closest(b.$button)[0]||b.close()}),void b.focus())},b.setText=function(){var a,b=this,c=b.$button,d=c.find(".pq-select-text"),e=b.element,f=b.options,g=f.deselect,h=b.data,i=f.itemCls,j=f.bootstrap,k=j.on?j.closeIcon:"ui-icon ui-icon-close",l=function(a){return g?["<span class='",i,"' data-id = '",a,"'>","<span class='pq-item-close ",k,"'></span>","<span class='pq-select-item-text'>",h[a].text,"</span>","</span>"].join(""):h[a].text},m=b.selectIndx;if(b.multiple){c.addClass("pq-select-multiple");var n=m.length,o=f.maxDisplay,p=h.length;if(n>0)if(o>=n){for(var q=[],r=0;n>r;r++){var s=m[r];q.push(l(s))}a=q.join(g?"":", ")}else a=f.displayText,a=a.replace("{0}",m.length),a=a.replace("{1}",p);else a=e.attr("data-placeholder"),a||(a=f.multiplePlaceholder)}else{c.addClass("pq-select-single"),d.css("maxWidth",c.width()-16);var s=m[0],a=null!=s?h[s].text:"";null!=a&&""!==a?a=l(s):(a=e.attr("data-placeholder"),a||(a=f.singlePlaceholder))}d.html(a),b.multiple||d.find(".pq-select-item-text").css({maxWidth:c.width()-35}),setTimeout(function(){b.uuid&&b.positionPopup()})},b.close=function(b){this.isOpen()&&(b=b||{},b.focus!==!1&&this.$button.focus(),this.$popupCont.hide()),a(document).off(this.eventNamespace)},b.toggle=function(){this.isOpen()?this.close():this.open()},b.disable=function(){this.option({disabled:!0})},b.enable=function(){this.option({disabled:!1})},b._destroy=function(){this.$popupCont.remove(),this.$button.remove(),this.element.removeClass("pq-select").show();var b=this.eventNamespace;a(document).off(b),a(window).off(b)}}(jQuery);