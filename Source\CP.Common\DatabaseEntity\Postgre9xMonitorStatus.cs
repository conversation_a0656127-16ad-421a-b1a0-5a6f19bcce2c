﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using CP.Common.Base;
using System;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    public class Postgre9xMonitorStatus : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string ReplicationStatusPR { get; set; }

        [DataMember]
        public string ReplicationStatusDR { get; set; }

        [DataMember]
        public string DataDirectoryPathPR { get; set; }

        [DataMember]
        public string DataDirectoryPathDR { get; set; }

        [DataMember]
        public string Current_xlog_location { get; set; }

        [DataMember]
        public string Last_xlog_receive_location { get; set; }

        [DataMember]
        public string Last_xlog_replay_location { get; set; }

        [DataMember]
        public string DataLag_MB { get; set; }

        [DataMember]
        public string DataLag_HHMMSS { get; set; }

        [DataMember]
        public string CurrentXlogFileName { get; set; }

        [DataMember]
        public string XlogReceiveFileName { get; set; }

        [DataMember]
        public string XlogReplayFileName { get; set; }

        [DataMember]
        public string CURRENTWAL_ISNPR { get; set; }

        [DataMember]
        public string CURRENTWAL_ISNDR { get; set; }

        [DataMember]
        public string CURRENTWAL_FILENAMEPR { get; set; }

        [DataMember]
        public string CURRENTWAL_FILENAMEDR { get; set; }

        [DataMember]
        public string LASTWAL_REC_ISNPR { get; set; }

        [DataMember]
        public string LASTWAL_REC_ISNDR { get; set; }



        [DataMember]
        public string LastWal_rec_FilenamePR { get; set; }

        [DataMember]
        public string LastWal_rec_FilenameDR { get; set; }

        [DataMember]
        public string LastWal_replayIsnPR { get; set; }

        [DataMember]
        public string LastWal_replayIsnDR { get; set; }

        [DataMember]
        public string LastWal_replayFilenamePR { get; set; }

        [DataMember]
        public string LastWal_replayFilenameDR { get; set; }

        #endregion
    }
}
