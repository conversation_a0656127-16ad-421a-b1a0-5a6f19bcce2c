
(function(n,t){function i(t){return!n(t).parents().andSelf().filter(function(){return n.curCSS(this,"visibility")==="hidden"||n.expr.filters.hidden(this)}).length}(n.ui=n.ui||{},n.ui.version)||(n.extend(n.ui,{version:"1.8.9",keyCode:{ALT:18,BACKSPACE:8,CAPS_LOCK:20,COMMA:188,COMMAND:91,COMMAND_LEFT:91,COMMAND_RIGHT:93,CONTROL:17,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,INSERT:45,LEFT:37,MENU:93,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SHIFT:16,SPACE:32,TAB:9,UP:38,WINDOWS:91}}),n.fn.extend({_focus:n.fn.focus,focus:function(t,i){return typeof t=="number"?this.each(function(){var r=this;setTimeout(function(){n(r).focus(),i&&i.call(r)},t)}):this._focus.apply(this,arguments)},scrollParent:function(){var t;return t=n.browser.msie&&/(static|relative)/.test(this.css("position"))||/absolute/.test(this.css("position"))?this.parents().filter(function(){return/(relative|absolute|fixed)/.test(n.curCSS(this,"position",1))&&/(auto|scroll)/.test(n.curCSS(this,"overflow",1)+n.curCSS(this,"overflow-y",1)+n.curCSS(this,"overflow-x",1))}).eq(0):this.parents().filter(function(){return/(auto|scroll)/.test(n.curCSS(this,"overflow",1)+n.curCSS(this,"overflow-y",1)+n.curCSS(this,"overflow-x",1))}).eq(0),/fixed/.test(this.css("position"))||!t.length?n(document):t},zIndex:function(i){if(i!==t)return this.css("zIndex",i);if(this.length)for(var r=n(this[0]),u,f;r.length&&r[0]!==document;){if(u=r.css("position"),(u==="absolute"||u==="relative"||u==="fixed")&&(f=parseInt(r.css("zIndex"),10),!isNaN(f)&&f!==0))return f;r=r.parent()}return 0},disableSelection:function(){return this.bind((n.support.selectstart?"selectstart":"mousedown")+".ui-disableSelection",function(n){n.preventDefault()})},enableSelection:function(){return this.unbind(".ui-disableSelection")}}),n.each(["Width","Height"],function(i,r){function e(t,i,r,u){return n.each(o,function(){i-=parseFloat(n.curCSS(t,"padding"+this,!0))||0,r&&(i-=parseFloat(n.curCSS(t,"border"+this+"Width",!0))||0),u&&(i-=parseFloat(n.curCSS(t,"margin"+this,!0))||0)}),i}var o=r==="Width"?["Left","Right"]:["Top","Bottom"],u=r.toLowerCase(),f={innerWidth:n.fn.innerWidth,innerHeight:n.fn.innerHeight,outerWidth:n.fn.outerWidth,outerHeight:n.fn.outerHeight};n.fn["inner"+r]=function(i){return i===t?f["inner"+r].call(this):this.each(function(){n(this).css(u,e(this,i)+"px")})},n.fn["outer"+r]=function(t,i){return typeof t!="number"?f["outer"+r].call(this,t):this.each(function(){n(this).css(u,e(this,t,!0,i)+"px")})}}),n.extend(n.expr[":"],{data:function(t,i,r){return!!n.data(t,r[3])},focusable:function(t){var r=t.nodeName.toLowerCase(),o=n.attr(t,"tabindex"),u,f,e;return"area"===r?(u=t.parentNode,f=u.name,!t.href||!f||u.nodeName.toLowerCase()!=="map")?!1:(e=n("img[usemap=#"+f+"]")[0],!!e&&i(e)):(/input|select|textarea|button|object/.test(r)?!t.disabled:"a"==r?t.href||!isNaN(o):!isNaN(o))&&i(t)},tabbable:function(t){var i=n.attr(t,"tabindex");return(isNaN(i)||i>=0)&&n(t).is(":focusable")}}),n(function(){var i=document.body,t=i.appendChild(t=document.createElement("div"));n.extend(t.style,{minHeight:"100px",height:"auto",padding:0,borderWidth:0}),n.support.minHeight=t.offsetHeight===100,n.support.selectstart="onselectstart"in t,i.removeChild(t).style.display="none"}),n.extend(n.ui,{plugin:{add:function(t,i,r){var f=n.ui[t].prototype,u;for(u in r)f.plugins[u]=f.plugins[u]||[],f.plugins[u].push([i,r[u]])},call:function(n,t,i){var u=n.plugins[t],r;if(u&&n.element[0].parentNode)for(r=0;r<u.length;r++)n.options[u[r][0]]&&u[r][1].apply(n.element,i)}},contains:function(n,t){return document.compareDocumentPosition?n.compareDocumentPosition(t)&16:n!==t&&n.contains(t)},hasScroll:function(t,i){if(n(t).css("overflow")==="hidden")return!1;var r=i&&i==="left"?"scrollLeft":"scrollTop",u=!1;return t[r]>0?!0:(t[r]=1,u=t[r]>0,t[r]=0,u)},isOverAxis:function(n,t,i){return n>t&&n<t+i},isOver:function(t,i,r,u,f,e){return n.ui.isOverAxis(t,r,f)&&n.ui.isOverAxis(i,u,e)}}))})(jQuery);

(function(n,t){var i,r;n.cleanData?(i=n.cleanData,n.cleanData=function(t){for(var r=0,u;(u=t[r])!=null;r++)n(u).triggerHandler("remove");i(t)}):(r=n.fn.remove,n.fn.remove=function(t,i){return this.each(function(){return i||(!t||n.filter(t,[this]).length)&&n("*",this).add([this]).each(function(){n(this).triggerHandler("remove")}),r.call(n(this),t,i)})}),n.widget=function(t,i,r){var u=t.split(".")[0],e,f;t=t.split(".")[1],e=u+"-"+t,r||(r=i,i=n.Widget),n.expr[":"][e]=function(i){return!!n.data(i,t)},n[u]=n[u]||{},n[u][t]=function(n,t){arguments.length&&this._createWidget(n,t)},f=new i,f.options=n.extend(!0,{},f.options),n[u][t].prototype=n.extend(!0,f,{namespace:u,widgetName:t,widgetEventPrefix:n[u][t].prototype.widgetEventPrefix||t,widgetBaseClass:e},r),n.widget.bridge(t,n[u][t])},n.widget.bridge=function(i,r){n.fn[i]=function(u){var f=typeof u=="string",e=Array.prototype.slice.call(arguments,1),o=this;return(u=!f&&e.length?n.extend.apply(null,[!0,u].concat(e)):u,f&&u.charAt(0)==="_")?o:(f?this.each(function(){var r=n.data(this,i),f=r&&n.isFunction(r[u])?r[u].apply(r,e):r;if(f!==r&&f!==t)return o=f,!1}):this.each(function(){var t=n.data(this,i);t?t.option(u||{})._init():n.data(this,i,new r(u,this))}),o)}},n.Widget=function(n,t){arguments.length&&this._createWidget(n,t)},n.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",options:{disabled:!1},_createWidget:function(t,i){n.data(i,this.widgetName,this),this.element=n(i),this.options=n.extend(!0,{},this.options,this._getCreateOptions(),t);var r=this;this.element.bind("remove."+this.widgetName,function(){r.destroy()}),this._create(),this._trigger("create"),this._init()},_getCreateOptions:function(){return n.metadata&&n.metadata.get(this.element[0])[this.widgetName]},_create:function(){},_init:function(){},destroy:function(){this.element.unbind("."+this.widgetName).removeData(this.widgetName),this.widget().unbind("."+this.widgetName).removeAttr("aria-disabled").removeClass(this.widgetBaseClass+"-disabled ui-state-disabled")},widget:function(){return this.element},option:function(i,r){var u=i;if(arguments.length===0)return n.extend({},this.options);if(typeof i=="string"){if(r===t)return this.options[i];u={},u[i]=r}return this._setOptions(u),this},_setOptions:function(t){var i=this;return n.each(t,function(n,t){i._setOption(n,t)}),this},_setOption:function(n,t){return this.options[n]=t,n==="disabled"&&this.widget()[t?"addClass":"removeClass"](this.widgetBaseClass+"-disabled ui-state-disabled").attr("aria-disabled",t),this},enable:function(){return this._setOption("disabled",!1)},disable:function(){return this._setOption("disabled",!0)},_trigger:function(t,i,r){var e=this.options[t],u,f;if(i=n.Event(i),i.type=(t===this.widgetEventPrefix?t:this.widgetEventPrefix+t).toLowerCase(),r=r||{},i.originalEvent)for(u=n.event.props.length;u;)f=n.event.props[--u],i[f]=i.originalEvent[f];return this.element.trigger(i,r),!(n.isFunction(e)&&e.call(this.element[0],i,r)===!1||i.isDefaultPrevented())}}})(jQuery);

(function(n){n.widget("ui.mouse",{options:{cancel:":input,option",distance:1,delay:0},_mouseInit:function(){var t=this;this.element.bind("mousedown."+this.widgetName,function(n){return t._mouseDown(n)}).bind("click."+this.widgetName,function(i){if(!0===n.data(i.target,t.widgetName+".preventClickEvent"))return n.removeData(i.target,t.widgetName+".preventClickEvent"),i.stopImmediatePropagation(),!1}),this.started=!1},_mouseDestroy:function(){this.element.unbind("."+this.widgetName)},_mouseDown:function(t){if(t.originalEvent=t.originalEvent||{},!t.originalEvent.mouseHandled){this._mouseStarted&&this._mouseUp(t),this._mouseDownEvent=t;var i=this,r=t.which==1,u=typeof this.options.cancel=="string"?n(t.target).parents().add(t.target).filter(this.options.cancel).length:!1;return!r||u||!this._mouseCapture(t)?!0:(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){i.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(t)&&this._mouseDelayMet(t)&&(this._mouseStarted=this._mouseStart(t)!==!1,!this._mouseStarted))?(t.preventDefault(),!0):(this._mouseMoveDelegate=function(n){return i._mouseMove(n)},this._mouseUpDelegate=function(n){return i._mouseUp(n)},n(document).bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate),t.preventDefault(),t.originalEvent.mouseHandled=!0,!0)}},_mouseMove:function(t){return n.browser.msie&&!(document.documentMode>=9)&&!t.button?this._mouseUp(t):this._mouseStarted?(this._mouseDrag(t),t.preventDefault()):(this._mouseDistanceMet(t)&&this._mouseDelayMet(t)&&(this._mouseStarted=this._mouseStart(this._mouseDownEvent,t)!==!1,this._mouseStarted?this._mouseDrag(t):this._mouseUp(t)),!this._mouseStarted)},_mouseUp:function(t){return n(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,t.target==this._mouseDownEvent.target&&n.data(t.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(t)),!1},_mouseDistanceMet:function(n){return Math.max(Math.abs(this._mouseDownEvent.pageX-n.pageX),Math.abs(this._mouseDownEvent.pageY-n.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}})})(jQuery),function(n){n.widget("ui.sortable",n.ui.mouse,{widgetEventPrefix:"sort",options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3},_create:function(){var n=this.options;this.containerCache={},this.element.addClass("ui-sortable"),this.refresh(),this.floating=this.items.length?/left|right/.test(this.items[0].item.css("float")):!1,this.offset=this.element.offset(),this._mouseInit()},destroy:function(){this.element.removeClass("ui-sortable ui-sortable-disabled").removeData("sortable").unbind(".sortable"),this._mouseDestroy();for(var n=this.items.length-1;n>=0;n--)this.items[n].item.removeData("sortable-item");return this},_setOption:function(t,i){t==="disabled"?(this.options[t]=i,this.widget()[i?"addClass":"removeClass"]("ui-sortable-disabled")):n.Widget.prototype._setOption.apply(this,arguments)},_mouseCapture:function(t,i){var u;if(this.reverting||this.options.disabled||this.options.type=="static")return!1;this._refreshItems(t);var r=null,f=this,e=n(t.target).parents().each(function(){if(n.data(this,"sortable-item")==f)return r=n(this),!1});return(n.data(t.target,"sortable-item")==f&&(r=n(t.target)),!r)?!1:this.options.handle&&!i&&(u=!1,n(this.options.handle,r).find("*").andSelf().each(function(){this==t.target&&(u=!0)}),!u)?!1:(this.currentItem=r,this._removeCurrentsFromItems(),!0)},_mouseStart:function(t,i,r){var u=this.options,e=this,f;if(this.currentContainer=this,this.refreshPositions(),this.helper=this._createHelper(t),this._cacheHelperProportions(),this._cacheMargins(),this.scrollParent=this.helper.scrollParent(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),n.extend(this.offset,{click:{left:t.pageX-this.offset.left,top:t.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.originalPosition=this._generatePosition(t),this.originalPageX=t.pageX,this.originalPageY=t.pageY,u.cursorAt&&this._adjustOffsetFromHelper(u.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!=this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),u.containment&&this._setContainment(),u.cursor&&(n("body").css("cursor")&&(this._storedCursor=n("body").css("cursor")),n("body").css("cursor",u.cursor)),u.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",u.opacity)),u.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",u.zIndex)),this.scrollParent[0]!=document&&this.scrollParent[0].tagName!="HTML"&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",t,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!r)for(f=this.containers.length-1;f>=0;f--)this.containers[f]._trigger("activate",t,e._uiHash(this));return n.ui.ddmanager&&(n.ui.ddmanager.current=this),n.ui.ddmanager&&!u.dropBehaviour&&n.ui.ddmanager.prepareOffsets(this,t),this.dragging=!0,this.helper.addClass("ui-sortable-helper"),this._mouseDrag(t),!0},_mouseDrag:function(t){var i,r,u;for(this.position=this._generatePosition(t),this.positionAbs=this._convertPositionTo("absolute"),this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs),this.options.scroll&&(i=this.options,r=!1,this.scrollParent[0]!=document&&this.scrollParent[0].tagName!="HTML"?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-t.pageY<i.scrollSensitivity?this.scrollParent[0].scrollTop=r=this.scrollParent[0].scrollTop+i.scrollSpeed:t.pageY-this.overflowOffset.top<i.scrollSensitivity&&(this.scrollParent[0].scrollTop=r=this.scrollParent[0].scrollTop-i.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-t.pageX<i.scrollSensitivity?this.scrollParent[0].scrollLeft=r=this.scrollParent[0].scrollLeft+i.scrollSpeed:t.pageX-this.overflowOffset.left<i.scrollSensitivity&&(this.scrollParent[0].scrollLeft=r=this.scrollParent[0].scrollLeft-i.scrollSpeed)):(t.pageY-n(document).scrollTop()<i.scrollSensitivity?r=n(document).scrollTop(n(document).scrollTop()-i.scrollSpeed):n(window).height()-(t.pageY-n(document).scrollTop())<i.scrollSensitivity&&(r=n(document).scrollTop(n(document).scrollTop()+i.scrollSpeed)),t.pageX-n(document).scrollLeft()<i.scrollSensitivity?r=n(document).scrollLeft(n(document).scrollLeft()-i.scrollSpeed):n(window).width()-(t.pageX-n(document).scrollLeft())<i.scrollSensitivity&&(r=n(document).scrollLeft(n(document).scrollLeft()+i.scrollSpeed))),r!==!1&&n.ui.ddmanager&&!i.dropBehaviour&&n.ui.ddmanager.prepareOffsets(this,t)),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&this.options.axis=="y"||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&this.options.axis=="x"||(this.helper[0].style.top=this.position.top+"px"),u=this.items.length-1;u>=0;u--){var f=this.items[u],e=f.item[0],o=this._intersectsWithPointer(f);if(o&&e!=this.currentItem[0]&&this.placeholder[o==1?"next":"prev"]()[0]!=e&&!n.ui.contains(this.placeholder[0],e)&&(this.options.type=="semi-dynamic"?!n.ui.contains(this.element[0],e):!0)){if(this.direction=o==1?"down":"up",this.options.tolerance=="pointer"||this._intersectsWithSides(f))this._rearrange(t,f);else break;this._trigger("change",t,this._uiHash());break}}return this._contactContainers(t),n.ui.ddmanager&&n.ui.ddmanager.drag(this,t),this._trigger("sort",t,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(t,i){if(t){if(n.ui.ddmanager&&!this.options.dropBehaviour&&n.ui.ddmanager.drop(this,t),this.options.revert){var r=this,u=r.placeholder.offset();r.reverting=!0,n(this.helper).animate({left:u.left-this.offset.parent.left-r.margins.left+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollLeft),top:u.top-this.offset.parent.top-r.margins.top+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollTop)},parseInt(this.options.revert,10)||500,function(){r._clear(t)})}else this._clear(t,i);return!1}},cancel:function(){var i=this,t;if(this.dragging)for(this._mouseUp({target:null}),this.options.helper=="original"?this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper"):this.currentItem.show(),t=this.containers.length-1;t>=0;t--)this.containers[t]._trigger("deactivate",null,i._uiHash(this)),this.containers[t].containerCache.over&&(this.containers[t]._trigger("out",null,i._uiHash(this)),this.containers[t].containerCache.over=0);return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.options.helper!="original"&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),n.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?n(this.domPosition.prev).after(this.currentItem):n(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(t){var r=this._getItemsAsjQuery(t&&t.connected),i=[];return t=t||{},n(r).each(function(){var r=(n(t.item||this).attr(t.attribute||"id")||"").match(t.expression||/(.+)[-=_](.+)/);r&&i.push((t.key||r[1]+"[]")+"="+(t.key&&t.expression?r[1]:r[2]))}),!i.length&&t.key&&i.push(t.key+"="),i.join("&")},toArray:function(t){var r=this._getItemsAsjQuery(t&&t.connected),i=[];return t=t||{},r.each(function(){i.push(n(t.item||this).attr(t.attribute||"id")||"")}),i},_intersectsWith:function(n){var t=this.positionAbs.left,h=t+this.helperProportions.width,i=this.positionAbs.top,c=i+this.helperProportions.height,r=n.left,f=r+n.width,u=n.top,e=u+n.height,o=this.offset.click.top,s=this.offset.click.left,l=i+o>u&&i+o<e&&t+s>r&&t+s<f;return this.options.tolerance=="pointer"||this.options.forcePointerForContainers||this.options.tolerance!="pointer"&&this.helperProportions[this.floating?"width":"height"]>n[this.floating?"width":"height"]?l:r<t+this.helperProportions.width/2&&h-this.helperProportions.width/2<f&&u<i+this.helperProportions.height/2&&c-this.helperProportions.height/2<e},_intersectsWithPointer:function(t){var u=n.ui.isOverAxis(this.positionAbs.top+this.offset.click.top,t.top,t.height),f=n.ui.isOverAxis(this.positionAbs.left+this.offset.click.left,t.left,t.width),e=u&&f,i=this._getDragVerticalDirection(),r=this._getDragHorizontalDirection();return e?this.floating?r&&r=="right"||i=="down"?2:1:i&&(i=="down"?2:1):!1},_intersectsWithSides:function(t){var u=n.ui.isOverAxis(this.positionAbs.top+this.offset.click.top,t.top+t.height/2,t.height),f=n.ui.isOverAxis(this.positionAbs.left+this.offset.click.left,t.left+t.width/2,t.width),i=this._getDragVerticalDirection(),r=this._getDragHorizontalDirection();return this.floating&&r?r=="right"&&f||r=="left"&&!f:i&&(i=="down"&&u||i=="up"&&!u)},_getDragVerticalDirection:function(){var n=this.positionAbs.top-this.lastPositionAbs.top;return n!=0&&(n>0?"down":"up")},_getDragHorizontalDirection:function(){var n=this.positionAbs.left-this.lastPositionAbs.left;return n!=0&&(n>0?"right":"left")},refresh:function(n){return this._refreshItems(n),this.refreshPositions(),this},_connectWith:function(){var n=this.options;return n.connectWith.constructor==String?[n.connectWith]:n.connectWith},_getItemsAsjQuery:function(t){var h=this,s=[],u=[],e=this._connectWith(),o,f,i,r;if(e&&t)for(r=e.length-1;r>=0;r--)for(o=n(e[r]),f=o.length-1;f>=0;f--)i=n.data(o[f],"sortable"),i&&i!=this&&!i.options.disabled&&u.push([n.isFunction(i.options.items)?i.options.items.call(i.element):n(i.options.items,i.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),i]);for(u.push([n.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):n(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),r=u.length-1;r>=0;r--)u[r][0].each(function(){s.push(this)});return n(s)},_removeCurrentsFromItems:function(){for(var i=this.currentItem.find(":data(sortable-item)"),t,n=0;n<this.items.length;n++)for(t=0;t<i.length;t++)i[t]==this.items[n].item[0]&&this.items.splice(n,1)},_refreshItems:function(t){var o,i,r,s,h,u,l,c;this.items=[],this.containers=[this];var a=this.items,v=this,f=[[n.isFunction(this.options.items)?this.options.items.call(this.element[0],t,{item:this.currentItem}):n(this.options.items,this.element),this]],e=this._connectWith();if(e)for(r=e.length-1;r>=0;r--)for(o=n(e[r]),u=o.length-1;u>=0;u--)i=n.data(o[u],"sortable"),i&&i!=this&&!i.options.disabled&&(f.push([n.isFunction(i.options.items)?i.options.items.call(i.element[0],t,{item:this.currentItem}):n(i.options.items,i.element),i]),this.containers.push(i));for(r=f.length-1;r>=0;r--)for(s=f[r][1],h=f[r][0],u=0,l=h.length;u<l;u++)c=n(h[u]),c.data("sortable-item",s),a.push({item:c,instance:s,width:0,height:0,left:0,top:0})},refreshPositions:function(t){var r,f,i,u;for(this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset()),i=this.items.length-1;i>=0;i--)r=this.items[i],f=this.options.toleranceElement?n(this.options.toleranceElement,r.item):r.item,t||(r.width=f.outerWidth(),r.height=f.outerHeight()),u=f.offset(),r.left=u.left,r.top=u.top;if(this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(i=this.containers.length-1;i>=0;i--)u=this.containers[i].element.offset(),this.containers[i].containerCache.left=u.left,this.containers[i].containerCache.top=u.top,this.containers[i].containerCache.width=this.containers[i].element.outerWidth(),this.containers[i].containerCache.height=this.containers[i].element.outerHeight();return this},_createPlaceholder:function(t){var i=t||this,r=i.options,u;r.placeholder&&r.placeholder.constructor!=String||(u=r.placeholder,r.placeholder={element:function(){var t=n(document.createElement(i.currentItem[0].nodeName)).addClass(u||i.currentItem[0].className+" ui-sortable-placeholder").removeClass("ui-sortable-helper")[0];return u||(t.style.visibility="hidden"),t},update:function(n,t){(!u||r.forcePlaceholderSize)&&(t.height()||t.height(i.currentItem.innerHeight()-parseInt(i.currentItem.css("paddingTop")||0,10)-parseInt(i.currentItem.css("paddingBottom")||0,10)),t.width()||t.width(i.currentItem.innerWidth()-parseInt(i.currentItem.css("paddingLeft")||0,10)-parseInt(i.currentItem.css("paddingRight")||0,10)))}}),i.placeholder=n(r.placeholder.element.call(i.element,i.currentItem)),i.currentItem.after(i.placeholder),r.placeholder.update(i,i.placeholder)},_contactContainers:function(t){for(var f=null,i=null,u,o,r=this.containers.length-1;r>=0;r--)if(!n.ui.contains(this.currentItem[0],this.containers[r].element[0]))if(this._intersectsWith(this.containers[r].containerCache)){if(f&&n.ui.contains(this.containers[r].element[0],f.element[0]))continue;f=this.containers[r],i=r}else this.containers[r].containerCache.over&&(this.containers[r]._trigger("out",t,this._uiHash(this)),this.containers[r].containerCache.over=0);if(f)if(this.containers.length===1)this.containers[i]._trigger("over",t,this._uiHash(this)),this.containers[i].containerCache.over=1;else if(this.currentContainer!=this.containers[i]){var s=1e4,e=null,h=this.positionAbs[this.containers[i].floating?"left":"top"];for(u=this.items.length-1;u>=0;u--)n.ui.contains(this.containers[i].element[0],this.items[u].item[0])&&(o=this.items[u][this.containers[i].floating?"left":"top"],Math.abs(o-h)<s&&(s=Math.abs(o-h),e=this.items[u]));if(!e&&!this.options.dropOnEmpty)return;this.currentContainer=this.containers[i],e?this._rearrange(t,e,null,!0):this._rearrange(t,null,this.containers[i].element,!0),this._trigger("change",t,this._uiHash()),this.containers[i]._trigger("change",t,this._uiHash(this)),this.options.placeholder.update(this.currentContainer,this.placeholder),this.containers[i]._trigger("over",t,this._uiHash(this)),this.containers[i].containerCache.over=1}},_createHelper:function(t){var r=this.options,i=n.isFunction(r.helper)?n(r.helper.apply(this.element[0],[t,this.currentItem])):r.helper=="clone"?this.currentItem.clone():this.currentItem;return i.parents("body").length||n(r.appendTo!="parent"?r.appendTo:this.currentItem[0].parentNode)[0].appendChild(i[0]),i[0]==this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),(i[0].style.width==""||r.forceHelperSize)&&i.width(this.currentItem.width()),(i[0].style.height==""||r.forceHelperSize)&&i.height(this.currentItem.height()),i},_adjustOffsetFromHelper:function(t){typeof t=="string"&&(t=t.split(" ")),n.isArray(t)&&(t={left:+t[0],top:+t[1]||0}),"left"in t&&(this.offset.click.left=t.left+this.margins.left),"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left),"top"in t&&(this.offset.click.top=t.top+this.margins.top),"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var t=this.offsetParent.offset();return this.cssPosition=="absolute"&&this.scrollParent[0]!=document&&n.ui.contains(this.scrollParent[0],this.offsetParent[0])&&(t.left+=this.scrollParent.scrollLeft(),t.top+=this.scrollParent.scrollTop()),(this.offsetParent[0]==document.body||this.offsetParent[0].tagName&&this.offsetParent[0].tagName.toLowerCase()=="html"&&n.browser.msie)&&(t={top:0,left:0}),{top:t.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:t.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if(this.cssPosition=="relative"){var n=this.currentItem.position();return{top:n.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:n.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var i=this.options;if(i.containment=="parent"&&(i.containment=this.helper[0].parentNode),(i.containment=="document"||i.containment=="window")&&(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,n(i.containment=="document"?document:window).width()-this.helperProportions.width-this.margins.left,(n(i.containment=="document"?document:window).height()||document.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]),!/^(document|window|parent)$/.test(i.containment)){var t=n(i.containment)[0],r=n(i.containment).offset(),u=n(t).css("overflow")!="hidden";this.containment=[r.left+(parseInt(n(t).css("borderLeftWidth"),10)||0)+(parseInt(n(t).css("paddingLeft"),10)||0)-this.margins.left,r.top+(parseInt(n(t).css("borderTopWidth"),10)||0)+(parseInt(n(t).css("paddingTop"),10)||0)-this.margins.top,r.left+(u?Math.max(t.scrollWidth,t.offsetWidth):t.offsetWidth)-(parseInt(n(t).css("borderLeftWidth"),10)||0)-(parseInt(n(t).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,r.top+(u?Math.max(t.scrollHeight,t.offsetHeight):t.offsetHeight)-(parseInt(n(t).css("borderTopWidth"),10)||0)-(parseInt(n(t).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top]}},_convertPositionTo:function(t,i){i||(i=this.position);var r=t=="absolute"?1:-1,e=this.options,u=this.cssPosition=="absolute"&&!(this.scrollParent[0]!=document&&n.ui.contains(this.scrollParent[0],this.offsetParent[0]))?this.offsetParent:this.scrollParent,f=/(html|body)/i.test(u[0].tagName);return{top:i.top+this.offset.relative.top*r+this.offset.parent.top*r-(n.browser.safari&&this.cssPosition=="fixed"?0:(this.cssPosition=="fixed"?-this.scrollParent.scrollTop():f?0:u.scrollTop())*r),left:i.left+this.offset.relative.left*r+this.offset.parent.left*r-(n.browser.safari&&this.cssPosition=="fixed"?0:(this.cssPosition=="fixed"?-this.scrollParent.scrollLeft():f?0:u.scrollLeft())*r)}},_generatePosition:function(t){var i=this.options,o=this.cssPosition=="absolute"&&!(this.scrollParent[0]!=document&&n.ui.contains(this.scrollParent[0],this.offsetParent[0]))?this.offsetParent:this.scrollParent,s=/(html|body)/i.test(o[0].tagName),f,e,r,u;return this.cssPosition!="relative"||this.scrollParent[0]!=document&&this.scrollParent[0]!=this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),f=t.pageX,e=t.pageY,this.originalPosition&&(this.containment&&(t.pageX-this.offset.click.left<this.containment[0]&&(f=this.containment[0]+this.offset.click.left),t.pageY-this.offset.click.top<this.containment[1]&&(e=this.containment[1]+this.offset.click.top),t.pageX-this.offset.click.left>this.containment[2]&&(f=this.containment[2]+this.offset.click.left),t.pageY-this.offset.click.top>this.containment[3]&&(e=this.containment[3]+this.offset.click.top)),i.grid&&(r=this.originalPageY+Math.round((e-this.originalPageY)/i.grid[1])*i.grid[1],e=this.containment?(r-this.offset.click.top<this.containment[1]||r-this.offset.click.top>this.containment[3])?(r-this.offset.click.top<this.containment[1])?r+i.grid[1]:r-i.grid[1]:r:r,u=this.originalPageX+Math.round((f-this.originalPageX)/i.grid[0])*i.grid[0],f=this.containment?(u-this.offset.click.left<this.containment[0]||u-this.offset.click.left>this.containment[2])?(u-this.offset.click.left<this.containment[0])?u+i.grid[0]:u-i.grid[0]:u:u)),{top:e-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+(n.browser.safari&&this.cssPosition=="fixed"?0:this.cssPosition=="fixed"?-this.scrollParent.scrollTop():s?0:o.scrollTop()),left:f-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+(n.browser.safari&&this.cssPosition=="fixed"?0:this.cssPosition=="fixed"?-this.scrollParent.scrollLeft():s?0:o.scrollLeft())}},_rearrange:function(n,t,i,r){i?i[0].appendChild(this.placeholder[0]):t.item[0].parentNode.insertBefore(this.placeholder[0],this.direction=="down"?t.item[0]:t.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var u=this,f=this.counter;window.setTimeout(function(){f==u.counter&&u.refreshPositions(!r)},0)},_clear:function(t,i){var u,f,r;if(this.reverting=!1,u=[],f=this,!this._noFinalSort&&this.currentItem[0].parentNode&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]==this.currentItem[0]){for(r in this._storedCSS)(this._storedCSS[r]=="auto"||this._storedCSS[r]=="static")&&(this._storedCSS[r]="");this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper")}else this.currentItem.show();if(this.fromOutside&&!i&&u.push(function(n){this._trigger("receive",n,this._uiHash(this.fromOutside))}),(this.fromOutside||this.domPosition.prev!=this.currentItem.prev().not(".ui-sortable-helper")[0]||this.domPosition.parent!=this.currentItem.parent()[0])&&!i&&u.push(function(n){this._trigger("update",n,this._uiHash())}),!n.ui.contains(this.element[0],this.currentItem[0]))for(i||u.push(function(n){this._trigger("remove",n,this._uiHash())}),r=this.containers.length-1;r>=0;r--)n.ui.contains(this.containers[r].element[0],this.currentItem[0])&&!i&&(u.push(function(n){return function(t){n._trigger("receive",t,this._uiHash(this))}}.call(this,this.containers[r])),u.push(function(n){return function(t){n._trigger("update",t,this._uiHash(this))}}.call(this,this.containers[r])));for(r=this.containers.length-1;r>=0;r--)i||u.push(function(n){return function(t){n._trigger("deactivate",t,this._uiHash(this))}}.call(this,this.containers[r])),this.containers[r].containerCache.over&&(u.push(function(n){return function(t){n._trigger("out",t,this._uiHash(this))}}.call(this,this.containers[r])),this.containers[r].containerCache.over=0);if(this._storedCursor&&n("body").css("cursor",this._storedCursor),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex",this._storedZIndex=="auto"?"":this._storedZIndex),this.dragging=!1,this.cancelHelperRemoval){if(!i){for(this._trigger("beforeStop",t,this._uiHash()),r=0;r<u.length;r++)u[r].call(this,t);this._trigger("stop",t,this._uiHash())}return!1}if(i||this._trigger("beforeStop",t,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.helper[0]!=this.currentItem[0]&&this.helper.remove(),this.helper=null,!i){for(r=0;r<u.length;r++)u[r].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1,!0},_trigger:function(){n.Widget.prototype._trigger.apply(this,arguments)===!1&&this.cancel()},_uiHash:function(t){var i=t||this;return{helper:i.helper,placeholder:i.placeholder||n([]),position:i.position,originalPosition:i.originalPosition,offset:i.positionAbs,item:i.currentItem,sender:t?t.element:null}}}),n.extend(n.ui.sortable,{version:"1.8.9"})}(jQuery)