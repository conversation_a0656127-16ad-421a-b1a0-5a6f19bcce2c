﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="Archive.aspx.cs" Inherits="CP.UI.Admin.Archive" Title="Continuity Patrol :: Archive" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script type="text/javascript">
        function ClientValidate(source, args) {

            var MyRadioButton = document.getElementById("<%=RadioButtonList1.ClientID %>");
            var options = MyRadioButton.getElementsByTagName("input");
            var Checkvalue = false;
            var check;
            for (i = 0; i < options.length; i++) {
                if (options[i].checked) {
                    Checkvalue = true;
                    check = options[i].value;
                }
            }
            if (!Checkvalue) {
                args.IsValid = false;
            }
            else {
                args.IsValid = true;
            }

        }
    </script>
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="upArchive" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <div>
                    <ul id="ulMessage" runat="server" visible="false">
                        <li>
                            <asp:Label ID="lblMessage" runat="server" Text="ddddd"></asp:Label>
                        </li>
                        <li class="close-bt"></li>
                    </ul>
                </div>

               

                <h3>
                    <img src="../Images/archieve-icon.png" />
                    Archive Configuration
                </h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal ">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Table Name <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlTables" CssClass="selectpicker col-md-6" data-style="btn-default" runat="server">
                                            <asp:ListItem Selected="True" Value="-1">-- Select Table --</asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvTable" runat="server" ControlToValidate="ddlTables"
                                            InitialValue="0" Display="Dynamic" ErrorMessage="Select Table" ValidationGroup="save"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Select Period <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:RadioButtonList ID="RadioButtonList1" runat="server" RepeatDirection="Horizontal">
                                            <asp:ListItem Value="1">One Week</asp:ListItem>
                                            <asp:ListItem Value="2">One Month</asp:ListItem>
                                            <asp:ListItem Value="3">Three Months</asp:ListItem>
                                            <asp:ListItem Value="4">Six Months</asp:ListItem>
                                            <asp:ListItem Value="5">One Year</asp:ListItem>
                                        </asp:RadioButtonList>
                                        <asp:CustomValidator ID="cvType" ValidationGroup="save" runat="server" ControlToValidate="RadioButtonList1"
                                            ClientValidationFunction="ClientValidate" Display="Dynamic" ValidateEmptyText="true" ErrorMessage="Please select period">
                                        </asp:CustomValidator>
                                        <asp:Label ID="lblErr" runat="server" Visible="false"></asp:Label>
                                    </div>
                                </div>
                                <hr class="separator" />
                                <div class="form-actions row">
                                    <div class="col-lg-6">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                            Fields</span>
                                        <asp:Label ID="lblupdatestatus" runat="server" Text=""></asp:Label>
                                    </div>

                                    <div class="col-lg-6" style="padding-left: 26px;">
                                        <asp:Button ID="btnSaveProfile" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save"
                                            TabIndex="9" ValidationGroup="save" OnClick="BtnSaveProfileClick" />
                                    </div>
                                </div>
                            </div>
                        </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    <asp:UpdateProgress ID="UpdateProgress1" AssociatedUpdatePanelID="upArchive" runat="server">
        <ProgressTemplate>
            <div id="imgLoading" class="loading-mask">
                <span>Loading...</span>
            </div>
        </ProgressTemplate>
    </asp:UpdateProgress>
</asp:Content>