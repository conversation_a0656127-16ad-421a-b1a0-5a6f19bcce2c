﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ServiceAndAlertStatus.ascx.cs" Inherits="CP.UI.Admin.DashboardContols.ServiceAndAlertStatus" %>
<div class="row">
    <div class="col-md-3">
        
        <div class="widget-stats widget-stats-2">
            <div class="txt" style="padding-bottom: 10px; color: #454545;">
                Services Available
            </div>
            <h1 style="font-size: 5.5em;">
                <a href="#" style="text-decoration: none;">7</a> / <a href="#" style="text-decoration: none;">10</a></h1>
        </div>
      
    </div>
    <div class="col-md-3">
        
        <div class="widget-stats widget-stats-2">
            <div class="txt" style="padding-bottom: 10px; color: #454545;">
                Services DR Ready
            </div>
            <h1 style="font-size: 5.5em;">
                <a href="#" style="text-decoration: none;">8</a> / <a href="#" style="text-decoration: none;">10</a></h1>
        </div>
       
    </div>
    <div class="col-md-3">
       
        <div class="widget-stats widget-stats-2">
            <div class="txt" style="padding-bottom: 10px; color: #454545;">
                Alerts
            </div>
            <h1 style="font-size: 5.5em;">
                <asp:LinkButton ID="lnkAlertCount" PostBackUrl="~/Alert/AlertManagement.aspx" Style="text-decoration: none;" runat="server"></asp:LinkButton>

               
            </h1>
        </div>
       
    </div>
    <div class="col-md-3">
      
        <div class="widget-stats widget-stats-2">

            <div class="txt" style="padding-bottom: 10px; color: #454545;">
                Incident
            </div>
            <h1 style="font-size: 5.5em;">
                <asp:LinkButton ID="lnkIncidentCount" PostBackUrl="" Style="text-decoration: none;" runat="server"></asp:LinkButton>

               
            </h1>
        </div>
    </div>

    
</div>
