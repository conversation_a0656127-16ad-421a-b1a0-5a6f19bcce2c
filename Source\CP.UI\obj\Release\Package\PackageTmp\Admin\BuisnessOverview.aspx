﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="BusinessOverview.aspx.cs" Inherits="CP.UI.Admin.BusinessOverview" Title="Continuity Patrol :: Admin-BusinessOverview" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <link href="../App_Themes/CPTheme/chart.css" rel="stylesheet" type="text/css" />
    <script src="../Script/ECOTree.js" type="text/javascript"> </script>

    <script type="text/javascript">
        var myTree = null;

        function CreateTree(msg) {
            myTree = new ECOTree('myTree', 'myTreeContainer');
            myTree.config.colorStyle = ECOTree.CS_LEVEL;
            myTree.config.nodeFill = ECOTree.NF_FLAT;
            myTree.config.useTarget = false;
            myTree.config.selectMode = ECOTree.SL_MULTIPLE;
            myTree.config.defaultNodeWidth = 65;
            myTree.config.defaultNodeHeight = 20;
            myTree.config.iSubtreeSeparation = 30;
            myTree.config.iSiblingSeparation = 20;
            myTree.config.iLevelSeparation = 50;

            var splitmsg = msg.d.split(";");
            var canvaslenght = 1;
            var canvasWidth = 0;
            myTree.add(1, -1, '<span class="icon_company">&nbsp;</span><span style="float:left; padding-top:6px;">' + splitmsg[0] + '</span>', 130, 0);
            var totalElement = 0;
            var appChild = splitmsg[1].split(',');
            for (var i = 0; i < appChild.length; i++) {
                myTree.add("app" + [i], 1, "<span>" + appChild[i] + '</span>', 130, 10);
              
            }
            var subchild = splitmsg[2].split(',');

            var childs = "";
            var groupIds = "";
            var spanValue = "";
            for (var i = 0; i < subchild.length; i++) {
                if (subchild[i] == "")
                    continue;

                if (subchild[i].indexOf(":") >= 0) {
                    childs = subchild[i].split(":");
                    groupIds = childs[0].split("/");
                    spanValue = '<span class="idclass" style="display:none;" >' + groupIds[1] + '</span> <span class="icon_app_group">&nbsp;</span><span style="float:left; padding:5px;">' + groupIds[0] + '</span>';

                    myTree.add("childlevel" + subchild[i] + "0", "app" + [i], spanValue, 150, 25);

                    for (var j = 1; j < childs.length; j++) {
                        groupIds = childs[j].split("/");
                        spanValue = '<span class="idclass" style="display:none;" >' + groupIds[1] + '</span><span class="icon_app_group">&nbsp;</span><span style="float:left; padding-top:5px;">' + groupIds[0] + '</span>';
                        myTree.add("childlevel" + subchild[i] + j, "childlevel" + subchild[i] + parseInt(j - 1), spanValue, 150, 25);
                        totalElement++;
                        canvaslenght++;
                    }
                }
                else {
                    groupIds = subchild[i].split("/");
                    spanValue = '<span class="idclass" style="display:none;" >' + groupIds[1] + '</span><span class="icon_app_group">&nbsp;</span><span style="float:left; padding-top:5px;">' + groupIds[0] + '</span>';
                    myTree.add("childlevel" + subchild[i] + "0", "app" + [i], spanValue, 150, 25);
                }
            }

           
            canvaslenght = canvaslenght + 3;
            canvaslenght = canvaslenght * 80;
            myTree.config.CanvasHeight = canvaslenght;
            
            canvasWidth = totalElement * 350;
            myTree.config.CanvasWidth = canvasWidth;
          
            myTree.UpdateTree();
            $("#1").attr("class", "chartmain");
            var styleValue = $("#1").attr("style");
            for (var i = 0; i < appChild.length; i++) {
                $("#app" + i).attr("class", "chartapp_group ");
            }
            $(".econode").each(function () {
                $(this).attr("class", "chartsub_group");
            });
        }

        $(document).ready(function () {
            var ajaxUrl = "BusinessOverview.aspx/GetBusinessView";
            var ajaxData = "{}";
            AjaxFunction(ajaxUrl, ajaxData, CreateTree, OnError);
            $(".chartsub_group").live("click", function () {
                var grouId = $(this).find(".idclass").html();
                var ajaxUrl = "BusinessOverview.aspx/GetCurrentDetail";
                var ajaxData = "{id:" + grouId + "}";
                AjaxFunction(ajaxUrl, ajaxData, showDetail, OnError);
            });

            function showDetail(msg) {
                var result = msg.d.split(',');
                var table = '<table class="dtable font"  width="100%" > ' +
				    ' <thead> ' +
				    '    <tr> ' +
				    '    <th> Component Monitor </th> ' +
				    '       <th> ' +
				    '           Production Server ' +
				    '       </th> ' +
				    '       <th> ' +
				    '           DR Server ' +
				    '       </th> ' +
				    '   </tr> ' +
				    ' </thead> ' +
				    ' <tbody> ' +
				    '    <tr> ' +
				    '       <td> ' +
				    '      Server Host Name ' +
				    '      </td> ' +
				    '      <td> ' +
				    result[0] +
				    '      </td> ' +
				    '      <td> ' +
				    result[3] +
				    '      </td> ' +
				    '  </tr> ' +
				    ' <tr> ' +
				    '   <td> ' +
				    '      IP Address  ' +
				    '   </td> ' +
				    '   <td > ' +
				    '  ' + result[1] + '<div class="' + result[2] + '"> </div>  ' +
				    '   </td> ' +
				    '    <td > ' +
				    ' ' + result[4] + '<div class="' + result[5] + '"> </div>  ' +
				    '   </td> ' +
				    ' </tr> ' +
				    ' <tr> ' +
				    '     <td> ' +
				    '       DataBase SID ' +
				    '   </td> ' +
				    '   <td> ' +
				    result[6] +
				    '  </td> ' +
				    '   <td> ' +
				    result[7] +
				    '    </td> ' +
				    ' </tr> ' +
				    ' <tr> ' +
				    ' <td> ' +
				    ' Last Generated Log Sequence ' +
				    ' </td> ' +
				    ' <td> ' +
				    result[8] +
				    ' </td> ' +
				    '  <td> ' +
				    '     -- ' +
				    '    </td> ' +
				    ' </tr> ' +
				    '  <tr> ' +
				    '      <td> ' +
				    '   Last Applied Log Sequence ' +
				    '      </td> ' +
				    '       <td> ' +
				    '          -- ' +
				    '        </td> ' +
				    '         <td> ' +
				    result[9] +
				    '          </td> ' +
				    '       </tr> ' +
				    '  <tr> ' +
				    '      <td> ' +
				    '   Current DataLag ' +
				    '      </td> ' +
				    '       <td colspan="2"> ' +
				    '<span style="margin-left:50px;font-weight:bold;"> ' + result[10] + '</span> ' +
				    '        </td> ' +
				    '       </tr> ' +
				    '    </tbody> ' +
				    ' </table> ';

                var header = '<h1>' + result[11] + '</h1>';

                ViewAlertModel(table, header, "800", 'ok');

            }
        });
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="grid_5 grid-15">
        <div class="block-border">
            <div class="block-content no-padding" style="height: 430px">

                <div class="block-controls" style="height: 25px;">
                    <h1>Business Overview</h1>
                </div>
                <div id="Div1" class="grid-18 chart-scroll">
                    <div id="myTreeContainer" style="margin: 0 30px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>