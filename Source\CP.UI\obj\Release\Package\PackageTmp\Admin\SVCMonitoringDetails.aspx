﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="SVCMonitoringDetails.aspx.cs" Inherits="CP.UI.Admin.SVCMonitoringDetails"
    MasterPageFile="~/Master/BcmsDefault.Master" Title="Continuity Patrol :: Infra Object Monitoring" %>


<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">

        <h3>
            <img src="../Images/monitor.png">
            Infra Object Monitor</h3>
        <div class="widget">
          
            <div class="widget-head">
                <asp:Label ID="lblApplicationName" class="heading" runat="server" Text="SVC-Metro or Global Mirror Consistency Group Monitoring"></asp:Label>
            </div>
          
            <div class="widget-body innerAll inner-2x">
                <div id="content-monitor" class="tabs-content">
                    <table class="table table-bordered table-white" width="100%">
                        <thead>
                            <tr>
                                <th class="col-md-4">Component
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th class="col-md-4">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Group ID 
                                </td>
                                <td><%--<span class="host-icon"></span>--%>
                                    <asp:Label ID="lblPRConsistencyGroupID" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="host-icon"></span>--%>
                                    <asp:Label ID="lblDRConsistencyGroupID" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group Name
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRConsistencyGroupName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRConsistencyGroupName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Master Cluster Name 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRMasterClusterName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRMasterClusterName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Auxiliary Cluster Name 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRAuxiliaryClusterName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRAuxiliaryClusterName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group Primary Value 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRGroupPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRGroupPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group State 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRGroupState" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRGroupState" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group Freeze Time 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRGroupfreezetime" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRGroupfreezetime" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group Status 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRGroupStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRGroupStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group Sync Status 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRGroupSyncStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRGroupSyncStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Mirror Relationship Name 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRMirrorRelationshipName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRMirrorRelationshipName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group Relationship Count & Names 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRGroupRelationshipCountNames" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRGroupRelationshipCountNames" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="widget" runat="server">
          
            <div class="widget-head">
                <asp:Label ID="lblReplicationName" class="heading" runat="server" Text="SVC Relationship Monitoring"></asp:Label>
            </div>
           
            <div class="widget-body innerAll inner-2x">
                <div id="repligmcontent" class="tabs-content ">
                    <table class="table table-bordered table-white" width="100%"
                        runat="server">
                        <thead>
                            <tr>
                                <th class="col-md-4">Relationship Monitoring
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th class="col-md-4">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Relationship Name 
                                </td>
                                <td><%--<span class="host-icon"></span>--%>
                                    <asp:Label ID="lblPRRelationshipName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRelationshipName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group Name
                                </td>
                                <td>
                                    <asp:Label ID="lblPRCGroupName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRCGroupName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Relationship Primary Value 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRRelationshipPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRelationshipPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Master Change Volume Name 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRMasterChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRMasterChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Auxiliary Change Volume Name  
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRAuxiliaryChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRAuxiliaryChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Relationship State 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRRelationshipState" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRelationshipState" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Relationship Progress 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRRelationshipProgress" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRelationshipProgress" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="widget">
          
            <div class="widget-head">
                <asp:Label ID="Label1" class="heading" runat="server" Text="SVC Storage Controller Monitoring"></asp:Label>
            </div>
          
            <div class="widget-body innerAll inner-2x">
                <div id="Div1" class="tabs-content">
                    <table class="table table-bordered table-white" width="100%">
                        <thead>
                            <tr>
                                <th class="col-md-4">Controller Monitoring
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th class="col-md-4">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Disk Controller ID 
                                </td>
                                <td><%--<span class="host-icon"></span>--%>
                                    <asp:Label ID="lblPRDiskControllerID" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="host-icon"></span>--%>
                                    <asp:Label ID="lblDRDiskControllerID" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Disk Controller Name
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRDiskControllerName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRDiskControllerName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Mdisks Degrade Status 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRMdisksDegradeStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRMdisksDegradeStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <%--<tr>
                                <td>Auxiliary Cluster Name 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRCPAuxiliaryClusterName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRCPAuxiliaryClusterName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>--%>
                            <tr>
                                <td>Controller Product ID 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRControllerProductID" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRControllerProductID" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <%--<tr>
                                <td>Storage Pool Status
                                </td>
                                <td>
                                    <asp:Label ID="lblPRStoragePoolStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRStoragePoolStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Clustered System Node ID 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRClusteredSystemNodeID" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRClusteredSystemNodeID" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>--%>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="widget">
          
            <div class="widget-head">
                <asp:Label ID="Label3" class="heading" runat="server" Text="SVC Storage Pool Monitoring"></asp:Label>
            </div>
         
            <div class="widget-body ">
                <div class="tabs-content ">
                    <table runat="server" class="table table-bordered table-white" width="100%">
                        <thead>
                            <tr>
                                <th class="col-md-4">Storage Pool Status Monitoring
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th class="col-md-4">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Storage Pool Status
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRStoragePoolStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRStoragePoolStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
      
        <div class="widget">
         
            <div class="widget-head">
                <asp:Label ID="Label6" class="heading" runat="server" Text="SVC Clustered System Node Monitoring"></asp:Label>
            </div>
        
            <div class="widget-body ">
                <div class="tabs-content ">
                    <table runat="server" class="table table-bordered table-white" width="100%">
                        <thead>
                            <tr>
                                <th class="col-md-4">Clustered System Node Monitoring
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th class="col-md-4">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Clustered System Node ID 
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblPRClusteredSystemNodeID" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td><%--<span class="health-up"></span>--%>
                                    <asp:Label ID="lblDRClusteredSystemNodeID" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="widget">
          
            <div class="widget-head">
                <asp:Label ID="Label2" class="heading" runat="server" Text="Clustered System Node Detailed Monitoring"></asp:Label>
            </div>
          
            <div class="widget-body innerAll inner-2x">
                <div id="Div2" class="tabs-content">
                    <table class="table table-bordered table-white" width="100%">
                        <thead>
                            <tr>
                                <th class="col-md-4">Node Detailed Monitoring
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th class="col-md-4">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Node ID  
                                </td>
                                <td>
                                    <asp:Label ID="lblPRNodeID" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRNodeID" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Node Name 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRNodeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRNodeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Node Status
                                </td>
                                <td>
                                    <asp:Label ID="lblPRNodeStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRNodeStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Service IP Address/HostName 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRServiceIPAddress" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRServiceIPAddress" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Product Machine Type
                                </td>
                                <td>
                                    <asp:Label ID="lblPRProductMachineType" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRProductMachineType" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Machine Code Level
                                </td>
                                <td>
                                    <asp:Label ID="lblPRMachineCodeLevel" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRMachineCodeLevel" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <%-- <tr>
                                <td>Group Sync Status 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRMGGroupSyncStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRMGGroupSyncStatus" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>--%>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="widget">
         
            <div class="widget-head">

                <asp:Label ID="Label4" class="heading" runat="server" Text="Services"></asp:Label>
            </div>
            <div class="widget-body ">
                <div id="Div3" class="tabs-content ">
                    <asp:Panel ID="Panel_listview" runat="server">
                        <asp:ListView ID="lvMonitorService" runat="server" OnItemDataBound="MonitorService_OnItemDataBound" Visible="true">
                            <LayoutTemplate>
                                <table class="table table-bordered table-condensed margin-bottom-none" width="100%">
                                    <thead>
                                        <tr>
                                            <th class="col-md-4">Service Name
                                            </th>
                                            <th class="col-md-4">Server IP
                                            </th>
                                            <th>Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <%--      </table>
                                <div class="slim-scroll chat-items" data-scroll-height="190px" data-scroll-size="0">
                                    <table class="table table-striped table-bordered table-condensed" >--%>
                                    <tbody>
                                        <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />
                                    </tbody>
                                </table>
                                <%--</div>--%>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td class="col-md-4">

                                        <asp:Label ID="ServiceId" runat="server" Text='<%# Eval("ServiceId") %>' Visible="false" />
                                        <asp:Label ID="lblServiceName" runat="server" Text='' />
                                    </td>
                                    <td class="col-md-4">
                                        <%--<asp:Label ID="Label16" runat="server" CssClass="InActive"></asp:Label>--%>

                                        <asp:Label ID="lblIpAddress" runat="server" Text='' />
                                    </td>
                                    <td>
                                        <asp:Label ID="Label15" runat="server" CssClass='<%#Eval("Status").ToString()=="1" ? "Replicating" : "InActive" %>'></asp:Label>

                                        <asp:Label ID="lblStatus" runat="server" CssClass='<%#Eval("Status").ToString()=="1" ? "text-success" : "text-danger" %>' Text='<%#Eval("Status").ToString()=="1" ? "Running" : "Stopped" %>' />
                                    </td>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                    </asp:Panel>
                </div>
            </div>
        </div>
        <%--
        <div class="widget">
          
            <div class="widget-head">
                <asp:Label ID="Label3" class="heading" runat="server" Text="Replication Monitoring Summary Layout"></asp:Label>
            </div>
          
            <div class="widget-body innerAll inner-2x">
                <div id="Div3" class="tabs-content">
                    <table class="table table-bordered table-white" width="100%">
                        <thead>
                            <tr>
                                <th class="col-md-4">Replication Monitoring
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th class="col-md-4">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Relationship Name 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRRMRelationshipName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRMRelationshipName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Group Name 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRRMGroupName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRMGroupName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Relationship Primary Value 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRRMRelationshipPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRMRelationshipPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Master Change Volume Name 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRRMMasterChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRMMasterChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Auxiliary Change Volume Name 
                                </td>
                                <td>
                                    <asp:Label ID="lblPRRMAuxiliaryChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRMAuxiliaryChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Relationship State  
                                </td>
                                <td>
                                    <asp:Label ID="lblPRRMRelationshipState" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRMRelationshipState" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Relationship Progress  
                                </td>
                                <td>
                                    <asp:Label ID="lblPRRMRelationshipProgress" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRRMRelationshipProgress" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>


                        </tbody>
                    </table>
                </div>
            </div>
        </div>--%>
    </div>
</asp:Content>
