﻿
var GlobalInstanceType = '';
var ready = $(document).ready(function(){
	var click = $("input[id$=btnDBSave]").click(function(){
		$(this).prev().hide();
		var content ="<div class='float-left side1'>BCMS Name<span class='error'>*</span> </div><div> <input type='text' id ='txtBcmsdbName' runat='server' maxlength='25' /><span></span><span id='spBcmsdbnum' style='display:none;' class='error'>Enter AlphaNumeric value only</span><span style='display:none;' class='error' id='spBcmsdbname'>BCMS Database Name not Available</span></div> <hr/>";
		content +="<div class='float-left side1'>Database Name<span class='error'>*</span> </div><div> <input type='text' id ='txtDbName' runat='server' maxlength='25' /><span></span><span id='spDbnum' style='display:none;' class='error'>Enter AlphaNumeric value only</span><span style='display:none;' class='error' id='spnDbName'>Database Name not Available</span></div> <hr/>";
		content +="<div class='float-left side1'>Category<span class='error'>*</span></div><div> <select id='ddlCategory' name='Category' style='width:145px;'> <option value='000'>Select Category</option><option value='Oracle'>Oracle</option><option value='SQL'>SQL</option><option value='Exchange'>Exchange</option></select></div> <hr />";
		content +="<div class='float-left side1'>Type<span class='error'>*</span></div><div> <select id='ddlType' name='Type' style='width:145px;'> <option value='000'>Select Type</option><option value='PR'>PR</option><option value='DR'>DR</option></select></div> <hr />";
		var hideButtonIds = "Reset:Update:Close";
		openModal("Save Database", content, hideButtonIds,saveDatabase, null);
	});

	function saveDatabase(win){
		//var bcmsDbName = jQuery.trim($('#txtBcmsdbName').val());
		//var dbName = jQuery.trim($('#txtDbName').val());
		var bcmsDbName = jQuery.trim($("input[id$=txtBcmsdbName]").val());
		var dbName = jQuery.trim($("input[id$=txtDbName]").val());
		var c = document.getElementById('ddlCategory');
		var category = c.options[c.selectedIndex].value;
		var t = document.getElementById('ddlType');
		var type = t.options[t.selectedIndex].value;
		var isRac = false;//$("input[id$=cbRac]").attr("checked");
		var valueToPass;
		valueToPass = bcmsDbName + "," + dbName + "," + category + "," + type + "," + isRac;
		if (bcmsDbName == "" || dbName == "" || c.options[c.selectedIndex].value == "000" ||  t.options[t.selectedIndex].value == "000"){
			alert("Required fields must be filled");
			return false;
		}
		$('#txtBcmsdbName').trigger("blur");
		if ($("#spBcmsdbname").is(":visible")) {
			return false;
		}
		if ($("#spBcmsdbnum").is(":visible")) {
			return false;
		}
		$('#txtDbName').trigger("blur");
		if ($("#spnDbName").is(":visible")) {
			return false;
		}
		if ($("#spDbnum").is(":visible")) {
			return false;
		}
		var ajaxUrl = "DatabaseConfiguration.aspx/SaveDatabase";
		var ajaxData = "{'Details':'" + valueToPass +"'}";
		AjaxFunction(ajaxUrl, ajaxData, databaseSaved, OnError);
		CloseModel(win);
		return false;
	}

	function databaseSaved(){
		window.location.reload();
	}

    function onNodeChange(oId,oType){
		//alert("Type="+oType);
		GlobalInstanceType = oType;
		var ajaxUrl = "Home.aspx/GetInstanceName";
		var ajaxData = "{'Details':'" + oId + "'}";
		AjaxFunction(ajaxUrl, ajaxData, setInstanceName, OnError);
	}

	function setInstanceName(msg){
		//alert("message="+ msg +" Nodetype="+Type);

		var result = msg;
		if(window.NodeType=="PR")
			$('id$=lblPrInstanceName').Text(result);
		if(window.NodeType=="DR")
			$('id$=lblDRServerName').Text(result);
	}
});

var savePopUpClose = "";
$("input[id$=btnVersionAdd]").click(function(){
	var typeId = $("[id$=ddlDBType] option:selected").val();
	var lblError ="";
		if(typeId==0)
		{
			$('#lblError').html("Select Database Type");
			return;
		}

	var content ="<div class='grid-21 float-left'>"+lblError+"</div></br>"+
	"<div > Version  <input type='text' id ='txtVersion' /><span id='spAppnum'></span><span style='display:none;' class='error' id='spnDBName'>Already Exist</span></div><hr/>";
	var hideButtonIds = "Reset:Update:Close";
	openModalForInfraObjectJob("Add Database Version", content, hideButtonIds, SaveApplication, null);
	});
	    $('[id$=txtVersion]').live("blur", function() {
           RequireField($(this).attr("id"));

		var dbVersion= jQuery.trim( $('[id$=txtVersion]').val());
		dbVersion = dbVersion.toLowerCase();
		var isError = false;
		$("[id$=ddlVersion] option").each(function()
		{
            if (dbVersion == jQuery.trim($(this).text()).toLowerCase())
            {
				$("#spnDBName").show();
				isError= true;
				return false;
			}
        });
		if(isError==false)
			$("#spnDBName").hide();

		if ($("#spAppnum").is(":visible")) {
			$("#spnDBName").hide();
		}
    });
	function SaveApplication(win)
	{
		 $('[id$=txtVersion]').trigger("blur");
		if ($("#spAppnum").is(":visible")) {
			return false;
		}
		if ($("#spnDBName").is(":visible")) {
			return false;
		}
		savePopUpClose = win;
		var typeId = $("[id$=ddlDBType] option:selected").val();
		var version=jQuery.trim($('#txtVersion').val());
		var value=typeId+"," +version;
		if (typeId == "")
			return false;
		$('#typeId').trigger("blur");
		 $.ajax({
                type: "POST",
                url: "DatabaseConfiguration.aspx/SaveDatabaseVersion",
                data: "{'value':'"+ value +"'}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                success: function(data)
                {
                        populateDatabase(data.d);
                }
           });
	}

   function populateDatabase(msg)
   {
	   var result = msg.split(":");
	   if(result[0]=="Success")
		{
		   AppendOption("ddlVersion", result[1], result[2]);
		   OpenAlertModelJobAlert("<span class='active bold'>Database Version Added Successfully</span>");
		   savePopUpClose.closeModal();
	    }
	   else if(result=="Error")
	    {
		   OpenAlertModelJobAlert("Database Version Not Inserted");
	    }
   }