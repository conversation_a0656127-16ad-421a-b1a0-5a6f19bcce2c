﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "BusinessFunction", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BusinessFunction : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public string CriticalityLevel { get; set; }

        [DataMember]
        public string ConfiguredRPO { get; set; }

        [DataMember]
        public string ConfiguredRTO { get; set; }

        [DataMember]
        public string ConfiguredMAO { get; set; }

        [DataMember]
        public int IsStatic { get; set; }

        [DataMember]
        public int DataLagInByte {get; set;}

        [DataMember]
        public int BFBIAProfileID { get; set; }

        [DataMember]
        public string InputInRPO { get; set; }

        [DataMember]
        public string InputInRTO { get; set; }

        #endregion Properties
    }
}