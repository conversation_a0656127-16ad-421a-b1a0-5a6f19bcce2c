﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Azure_Monitoring", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class Azure_Monitoring : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }
        
        [DataMember]
        public string FriendlyName { get; set; }

        [DataMember]
        public string AllowedOperations { get; set; }

        [DataMember]
        public string RecoveryServicesVaultName { get; set; }

        [DataMember]
        public string ActiveLocationState { get; set; }

        [DataMember]
        public string ReplicationProvider { get; set; }

        [DataMember]
        public string FailoverRecoveryPointId { get; set; }

        [DataMember]
        public string LastSuccessfulFailoverTime { get; set; }

        [DataMember]
        public string LastSuccessfulTestFailoverTime { get; set; }

        [DataMember]
        public string PolicyFriendlyName { get; set; }

        [DataMember]
        public string PrimaryFabricFriendlyName { get; set; }

        [DataMember]
        public string ProtectionState { get; set; }

        [DataMember]
        public string ReplicationHealth { get; set; }

        [DataMember]
        public string CrashConsistent { get; set; }

        [DataMember]
        public string ApplicationConsistent { get; set; }

        [DataMember]
        public string DataLag { get; set; }



        [DataMember]
        public string IPAddress { get; set; }

        [DataMember]
        public string DRIPAddress { get; set; }

        [DataMember]
        public string RecoveryFabricFriendlyName { get; set; }

        //[DataMember]
        //public string ProtectionState { get; set; }

        //[DataMember]
        //public string StatusDR { get; set; }

        //[DataMember]
        //public string SourceResourceGroupNamePR { get; set; }

        //[DataMember]
        //public string TargetResourceGroupNameDR { get; set; }


        //[DataMember]
        //public DateTime RPOPR { get; set; }

        //[DataMember]
        //public DateTime RPODR { get; set; }

        //[DataMember]
        //public DateTime CrashConsistentPR { get; set; }

        //[DataMember]
        //public DateTime CrashConsistentDR { get; set; }

        //[DataMember]
        //public DateTime AppConsistentPR { get; set; }

        //[DataMember]
        //public DateTime AppConsistentDR { get; set; }

        //[DataMember]
        //public DateTime LastSuccessfulFailoverTimePR { get; set; }

        //[DataMember]
        //public DateTime LastSuccessfulFailoverTimeDR { get; set; }

        //[DataMember]
        //public DateTime LastSuccessfulTestFailoverTimePR { get; set; }

        //[DataMember]
        //public DateTime LastSuccessfulTestFailoverTimeDR { get; set; }

       
        //[DataMember]
        //public string SourcePublicIPAddressPR { get; set; }

        //[DataMember]
        //public string SourcePublicIPAddressDR { get; set; }

        #endregion 
    }
}
