﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using Telerik.Web.UI;
using Telerik.Web.UI.HtmlChart;
using System.Data;
using System.Drawing;
using System.Xml;
using System.IO;
using SpreadsheetGear;
using System.Globalization;
using System.Configuration;
using System.Diagnostics;


namespace CP.UI
{
    public partial class CurrentStatus : BaseControl
    {
        public override void PrepareView()
        {
            BindData();

            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(imgBtnReport);
            // updcurrent.Update();

            //  ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(imgBtnReport);
            updcurrent.Update();

        }

        public void BindData()
        {
            try
            {
                IList<DashboardAlert> _Alert = Facade.GetDashboardAlert();
                IList<IncidentCIOMontly> _Incident = Facade.GetIncidentMontly();
                int ServicBreachCountBymonth = (Facade.GetServicBreachCountBymonth(DateTime.Now.Month));
                int FunctionBreachCountBymonth = (Facade.GetFunctionBreachCountBymonth(DateTime.Now.Month));
                IList<ComponentFailureDaily> _comp = Facade.GetAllComponentFailureDaily();
                IList<BusinessServiceRTOInfo> _DrillExecu = Facade.GetDrillExecutionCount();
                IList<ServiceRTODaily> _RTOMeet = Facade.GetRTOMeetDetails_ByMonths();
                IList<BSDRReadyDaily> _BSDRReady = Facade.GetAllBSDRReady_ByMonths();

                lblcurrentmnth.Text = DateTime.Now.ToString("MMM") + "-" + DateTime.Now.Year;
                DashboardAlert alert = (from _alert in _Alert where Convert.ToInt32(_alert.AlertDate) == (DateTime.Now.Month) select _alert).FirstOrDefault();
                if (alert != null)
                    lblActiveAlerts.Text = Convert.ToString(Convert.ToInt32(alert.Critical) + Convert.ToInt32(alert.High) + Convert.ToInt32(alert.Normal));

                var _objIncident = (from Incidentval in _Incident where Convert.ToInt32(Incidentval.IncidentDate) == (DateTime.Now.Month) select Incidentval.OpenCount).FirstOrDefault();
                if (_objIncident != null)
                    lblActiveIncidents.Text = _objIncident;

                lblServiceRPO_SLA.Text = Convert.ToString(ServicBreachCountBymonth);
                lblAppRPO_SLA.Text = Convert.ToString(FunctionBreachCountBymonth);

                var Obj = (from _cmp in _comp where _cmp.MonthID == DateTime.Now.Month select _cmp).FirstOrDefault();
                if (Obj != null)
                    lblcompFailure.Text = (Obj.Server + Obj.Database + Obj.Replication).ToString();

                var _ObjDrill = (from _DrillExcution in _DrillExecu where _DrillExcution.MonthID == DateTime.Now.Month select _DrillExcution.ServiceCount).FirstOrDefault();
                if (_ObjDrill != null)
                    lblDrillExecution.Text = Convert.ToString(_ObjDrill);

                var _objRTOMeet = (from rto in _RTOMeet where rto.MonthID == DateTime.Now.Month select rto.RTOMeet).FirstOrDefault();
                if (_objRTOMeet != null)
                    lblMeetRTO.Text = Convert.ToString(_objRTOMeet);

                var _ObjDR = (from _DRReady in _BSDRReady where _DRReady.MonthID == DateTime.Now.Month select _DRReady.BServiceCount).FirstOrDefault();
                if (_ObjDR != null)
                    lblseriveWithOutDR.Text = Convert.ToString(_ObjDR);

                var _ObjAppDR = (from _DRReady in _BSDRReady where _DRReady.MonthID == DateTime.Now.Month select _DRReady.FunctionCount).FirstOrDefault();
                if (_ObjAppDR != null)
                    lblappithOutDR.Text = Convert.ToString(_ObjAppDR);
            }
            catch (Exception)
            {

                throw;
            }
        }


        #region DashboardReport

        //protected void btnReport_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        DashBoardAlert();
        //    }
        //    catch (CpException ex)
        //    {
        //        ExceptionManager.Manage(ex);
        //    }
        //    catch (Exception ex)
        //    {
        //        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
        //        ExceptionManager.Manage(customEx);
        //    }
        //}


        static DateTimeFormatInfo mm = new DateTimeFormatInfo();
        static string currentMM = DateTime.Now.Month.ToString();
        static string currentYear = DateTime.Now.Year.ToString();
        static int monthNumber = Convert.ToInt32(currentMM);
        static string MonthName = mm.GetMonthName(monthNumber);

        string SerBreRPOSLA = "0";
        string FunBreRPOSLA = "0";
        string InfBreRPOSLA = "0";

        string SerWODRConfig = "0";
        string FunWODRConfig = "0";
        string InfWODRConfig = "0";

        string SerWODRReadiness = "0";
        string FunWODRReadiness = "0";
        string InfWODRReadiness = "0";

        string SerCurNotDrReady = "0";
        string FunCurNotDrReady = "0";
        string InfCurNotDrReady = "0";

        string SerMeetingRTO = "0";
        string SerNotMeetingRTO = "0";
        string AvgSerRecEfficiency = "00.00";

        string SerMeetingRTOWithHumInt = "0";
        string SerMeetingRTOWithOutHumInt = "0";
        string AvgSerRecEffectiveness = "00.00";

        string SerNotRec = "0";
        string FunNotRec = "0";
        string InfNotRec = "0";

        string CompFail = "0";

        protected void DashBoardAlert()
        {

            IWorkbookSet workbookSet;
            string ssFile;
            IWorkbook workbook;

            // IRange cells;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = Server.MapPath("../SampleWorkbook/DashBoardReport.xls");
            workbook = workbookSet.Workbooks.Open(ssFile);

            var reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet worksheet = null;

            BusinessServicesHealth(workbook, worksheet);

            BusinessServicesProtection(workbook, worksheet);

            BusinessServicesRecovery(workbook, worksheet);

            BusinessServicesComponentFailures(workbook, worksheet);

            Dashboard(workbook, worksheet);

            OpenExcelFile1(workbook);

        }

        public void Dashboard(IWorkbook workbook, IWorksheet worksheet)
        {
            IRange cells;

            worksheet = workbook.Worksheets["Dashboard"];

            cells = worksheet.Cells;


            cells["B3:I3"].Merge();
            cells["B3:I3"].Formula = "Continuity Patrol - Business Services Dashboard - " + MonthName + " " + currentYear;
            cells["B3:I3"].HorizontalAlignment = HAlign.Center;

            cells["B4"].Formula = "Report Generated Time: " + DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt");
            cells["I4"].Formula = "Logged in User :" + LoggedInUser.LoginName;
            cells["I4"].HorizontalAlignment = HAlign.Left;


            //----------------------------------------------Performance Indicators-----------------------------------------------------------


            cells["C9"].Formula = SerBreRPOSLA;
            cells["C10"].Formula = FunBreRPOSLA;
            cells["C11"].Formula = InfBreRPOSLA;

            cells["C14"].Formula = SerWODRConfig;
            cells["C15"].Formula = FunWODRConfig;
            cells["C16"].Formula = InfWODRConfig;

            cells["C18"].Formula = SerWODRReadiness;
            cells["C19"].Formula = FunWODRReadiness;
            cells["C20"].Formula = InfWODRReadiness;

            cells["C22"].Formula = SerCurNotDrReady;
            cells["C23"].Formula = FunCurNotDrReady;
            cells["C24"].Formula = InfCurNotDrReady;

            cells["G9"].Formula = SerMeetingRTO;
            cells["G10"].Formula = SerNotMeetingRTO;
            cells["G11"].Formula = AvgSerRecEfficiency;

            cells["G13"].Formula = SerMeetingRTOWithOutHumInt;
            cells["G14"].Formula = SerMeetingRTOWithHumInt;
            cells["G15"].Formula = AvgSerRecEffectiveness;
            cells["G16"].Formula = SerNotRec;
            cells["G17"].Formula = FunNotRec;
            cells["G18"].Formula = InfNotRec;

            cells["G20"].Formula = CompFail;

            //cells["E10"].Formula = "@";
            //cells["E10"].Formula = DateTime.Now.ToString("MMM-yyyy");

            // IList<ComponentFailureDaily> _comp = Facade.GetAllComponentFailDailyServiceMonthCount();

            // IList<ComponentFailureDaily> _compyear = Facade.GetAllComponentFailDailyServiceyearCount();                    

        }

        public void BusinessServicesHealth(IWorkbook workbook, IWorksheet worksheet)
        {
            IRange cells;

            worksheet = workbook.Worksheets["Business Services Health"];

            cells = worksheet.Cells;

            cells["B3:K3"].Merge();
            cells["B3:K3"].Formula = "Continuity Patrol - Business Services Dashboard - " + MonthName + " " + currentYear;
            cells["B3:K3"].HorizontalAlignment = HAlign.Center;

            cells["B4"].Formula = "Report Generated Time: " + DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt");
            cells["E10"].Formula = "@";
            cells["E10"].Formula = DateTime.Now.ToString("MMM-yyyy");
            cells["E23"].Formula = "@";
            cells["E23"].Formula = DateTime.Now.ToString("MMM-yyyy");
            cells["E36"].Formula = "@";
            cells["E36"].Formula = DateTime.Now.ToString("MMM-yyyy");

            cells["I4"].Formula = "Logged in User :" + LoggedInUser.LoginName;
            cells["I4"].HorizontalAlignment = HAlign.Left;


            //----------------------------------------Services Breached RPO SLA (Count)------------------------------------------------------------

            string mm = DateTime.Now.ToString("MM");
            int currmonth = Convert.ToInt32(mm);

            string bsName = "BS";
            IList<BusinessServiceRPOInfo> busSerM = Facade.GetAllBusinessServiceRPOCountByMonthNYear(bsName, currmonth);

            IList<BusinessServiceRPOInfo> busSerY = Facade.GetAllBusinessServiceRPOCountByYear(bsName);

            IRange _cells = worksheet.Cells;

            int dataCountBSY = 0;
            int rowBSY = 12;
            int rowBSM = 12;
            int dtc1 = 0;
            foreach (var data in busSerY)
            {
                var sercnt = (from bsm in busSerM where bsm.BusinessServiceId == data.BusinessServiceId select bsm.ServiceCount).FirstOrDefault();

                if (rowBSY < 17)  //&& totalcntM <= totalcnty
                {


                    dataCountBSY++;
                    int column = 0;
                    string[] xlColumn = { "B", "D", "E" };

                    string ndx = xlColumn[column] + rowBSY.ToString();

                    _cells[ndx].Formula = data.BServiceName != null ? data.BServiceName : "NA";
                    column++;

                    ndx = xlColumn[column] + rowBSY.ToString();
                    _cells[ndx].Formula = data.ServiceCount.ToString() != null ? data.ServiceCount.ToString() : "0";// Fail Count";//Total count ";
                    column++;

                    ndx = xlColumn[column] + rowBSY.ToString();
                    //if (busSerM != null && busSerM.Count > 0 && busSerM.Count > dtc1)
                    //    _cells[ndx].Formula = busSerM[dtc1].ServiceCount.ToString() != null ? busSerM[dtc1].ServiceCount.ToString() : "0";// Fail Count";
                    //else
                    _cells[ndx].Formula = Convert.ToString(sercnt);// Fail Count";

                    column++;
                    dtc1++;
                    rowBSY++;
                }
            }

            //  var data1 = busSerM.OrderByDescending(X => X.Month);
            var BScnt = (from bsm in busSerM where bsm.ServiceCount > 0 select bsm.BusinessServiceId).Count();

            //var s = bfcnt;
            //int BScnt = 0;
            //foreach (var data in busSerM)
            //{

            //    BScnt = BScnt + data.ServiceCount;
            //}

            SerBreRPOSLA = BScnt.ToString();
            //------------------------------------------Functions / Applications Breached RPO SLA (Count)----------------------------------------------------------------

            string bfName = "BF";

            IList<BusinessServiceRPOInfo> busFunY = Facade.GetAllBusinessServiceRPOCountByYear(bfName);

            IList<BusinessServiceRPOInfo> busFunM = Facade.GetAllBusinessServiceRPOCountByMonthNYear(bfName, currmonth);

            //int totalcntM = busFunM.Count();
            //int totalcnty = busFunY.Count();



            int dataCountBFY = 0;
            int rowBFY = 25;
            int rowBFM = 25;
            int dtc = 0;
            foreach (var data in busFunY)
            {
                var bfcnt = (from bsm in busFunM where bsm.BusinessFunctionId == data.BusinessFunctionId select bsm.FunctionCount).FirstOrDefault();

                //var cnt =(from a in  busFunM where  a. )

                if (rowBFY < 30)  //&& totalcntM <= totalcnty
                {
                    dataCountBFY++;
                    int column = 0;
                    string[] xlColumn = { "B", "D", "E" };

                    string ndx = xlColumn[column] + rowBFY.ToString();

                    _cells[ndx].Formula = data.BFunctionName != null ? data.BFunctionName : "NA";
                    column++;

                    ndx = xlColumn[column] + rowBFY.ToString();
                    _cells[ndx].Formula = data.FunctionCount.ToString() != null ? data.FunctionCount.ToString() : "0";// Fail Count";//Total count ";
                    column++;

                    ndx = xlColumn[column] + rowBFY.ToString();
                    //if (busFunM != null && busFunM.Count > 0 && busFunM.Count > dtc)
                    //    _cells[ndx].Formula = busFunM[dtc].FunctionCount.ToString() != null ? busFunM[dtc].FunctionCount.ToString() : "0";// Fail Count";
                    //else
                    _cells[ndx].Formula = Convert.ToString(bfcnt);// Fail Count";

                    column++;
                    dtc++;
                    rowBFY++;
                }
            }

            var BFcnt = (from bfm in busFunM where bfm.FunctionCount > 0 select bfm.BusinessFunctionId).Count();
            //int BFcnt = 0;
            //foreach (var data in busFunM)
            //{
            //    BFcnt = BFcnt + data.FunctionCount;
            //}
            FunBreRPOSLA = BFcnt.ToString();




            //-------------------------------------------InfraObjects Breached RPO SLA (Count)----------------------------------------------------------
            string InfraObName = "IO";
            IList<BusinessServiceRPOInfo> infraObM = Facade.GetAllBusinessServiceRPOCountByMonthNYear(InfraObName, currmonth);

            IList<BusinessServiceRPOInfo> infraObY = Facade.GetAllBusinessServiceRPOCountByYear(InfraObName);

            int dataCountIOM = 0;
            int rowIOM = 38;
            int dtinfra = 0;
            foreach (var data in infraObY)
            {

                var infracnt = (from bsm in infraObM where bsm.InfraObjectId == data.InfraObjectId select bsm.InfraObjCount).FirstOrDefault();

                if (rowIOM < 43)
                {
                    dataCountIOM++;
                    int column = 0;
                    string[] xlColumn = { "B", "D", "E" };

                    string ndx = xlColumn[column] + rowIOM.ToString();

                    _cells[ndx].Formula = data.InraobjectName != null ? data.InraobjectName : "NA";
                    column++;



                    ndx = xlColumn[column] + rowIOM.ToString();
                    // if (infraObY != null && infraObY.Count > 0 && infraObY.Count > dtinfra)
                    _cells[ndx].Formula = data.InfraObjCount.ToString() != null ? data.InfraObjCount.ToString() : "0";// Fail Count";
                    //else
                    //    _cells[ndx].Formula = "0";// Fail Count";


                    //ndx = xlColumn[column] + rowBFY.ToString();
                    //_cells[ndx].Formula = data.InfraObjCount.ToString() != null ? data.InfraObjCount.ToString() : "0";// Fail Count";//Total count ";
                    column++;


                    ndx = xlColumn[column] + rowIOM.ToString();
                    //if (infraObM != null && infraObM.Count > 0 && infraObM.Count > dtinfra)
                    //    _cells[ndx].Formula = infraObM[dtinfra].InfraObjCount.ToString() != null ? infraObM[dtinfra].InfraObjCount.ToString() : "0";// Fail Count";
                    //else
                    _cells[ndx].Formula = Convert.ToString(infracnt);// Fail Count";

                    //ndx = xlColumn[column] + rowIOM.ToString();
                    //_cells[ndx].Formula = data.InfraObjCount.ToString() != null ? data.InfraObjCount.ToString() : "0";// Fail Count";
                    //column++;

                    rowIOM++;
                }
            }

            var Infracnt = (from infraObjM in infraObM where infraObjM.InfraObjCount > 0 select infraObjM.InfraObjectId).Count();
            //int Infracnt = 0;
            //foreach (var data in infraObM)
            //{
            //    Infracnt = Infracnt + data.InfraObjCount;
            //}
            InfBreRPOSLA = Infracnt.ToString();


        }

        public void BusinessServicesProtection(IWorkbook workbook, IWorksheet worksheet)
        {
            IRange cells;

            worksheet = workbook.Worksheets["Business Services-Protection"];

            cells = worksheet.Cells;

            cells["B3:O3"].Merge();
            cells["B3:O3"].Formula = "Continuity Patrol - Business Services Dashboard - " + MonthName + " " + currentYear;
            cells["B3:O3"].HorizontalAlignment = HAlign.Center;

            cells["B4"].Formula = "Report Generated Time: " + DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt");

            cells["M4"].Formula = "Logged in User :" + LoggedInUser.LoginName;
            cells["M4"].HorizontalAlignment = HAlign.Left;

            //----------------------------------Business Services / Applications / InfraObjects Currently Not DR Ready (Count)------------------------------------------------------------

            IList<InfraObject> infra = Facade.GetAllInfraObject();

            //int infraDrReadyCount = 0;
            //int infraDRNotReadyCount = 0;
            //if (infra != null)
            //{
            //    var drReadyInfra = (from infraObj in infra where infraObj.IsDRReady == 1 select infraObj).ToList();
            //    var drNotReadyInfra = (from infraObj in infra where infraObj.IsDRReady != 1 select infraObj).ToList();

            //    if (drReadyInfra != null)
            //        infraDrReadyCount = drReadyInfra.Count;
            //    if (drNotReadyInfra != null)
            //        infraDRNotReadyCount = drNotReadyInfra.Count;

            //}



            //int BusfunNotDRRedy = Facade.GetInfraobjFunctionCountNotDRReadyCurrentMonth();
            //int BusfunDRRedy = Facade.GetInfraobjFunctionCountDRReadyCurrentMonth();
            //int BusSerNotDRRedy = Facade.GetInfraobjServiceCountNotDRReadyCurrentMonth();
            //int BusSerDRRedy = Facade.GetInfraobjServiceCountDRReadyCurrentMonth();

            //      int infraDrReadyCount_ = Facade.GetDRstatusbyInfraid(1);

            List<int> databind2 = new List<int>();

            databind2 = GetDRStatusCount();

            if (databind2 != null && databind2.Count>0)
            {
                SerCurNotDrReady = databind2[0].ToString();
                FunCurNotDrReady = databind2[2].ToString();
                InfCurNotDrReady = databind2[4].ToString();

                cells["B11"].Formula = databind2[0].ToString();//Business Services Currently Not DR Ready
                cells["C11"].Formula = databind2[1].ToString();//Business Services Currently DR Ready
                cells["D11"].Formula = databind2[2].ToString();//Functions/ Apps Currently Not DR Ready
                cells["E11"].Formula = databind2[3].ToString();//Functions/ Apps Currently DR Ready
                cells["F11"].Formula = databind2[4].ToString();//InfraObjects Currently Not DR Ready
                cells["G11"].Formula = databind2[5].ToString();//InfraObjects Currently DR Ready
            }
            //-----------------------------------DR Readiness Checks for Business Services / Applications / InfraObjects (Count)-----------------------------------------------------

            List<int> databind = new List<int>();
            databind = GetDRReadyCount();

            SerWODRReadiness = databind[0].ToString();
            FunWODRReadiness = databind[2].ToString();
            InfWODRReadiness = databind[4].ToString();

            cells["B34"].Formula = databind[0].ToString();//Business Services Without DR Readiness Checks
            cells["C34"].Formula = databind[1].ToString();//Business Services With DR Readiness Checks
            cells["D34"].Formula = databind[2].ToString();//Functions/ Apps Without DR Readiness Checks
            cells["E34"].Formula = databind[3].ToString();//Functions/ Apps With DR Readiness Checks
            cells["F34"].Formula = databind[4].ToString();//InfraObjects Without DR Readiness Checks
            cells["G34"].Formula = databind[5].ToString();//InfraObjects With DR Readiness Checks

            //-------------------------------------DR Protection Infrastructure Not Configured OR Not Available for Business Services / Functions & Applications / InfraComponents(Count)----------------------------------------------------

            List<int> databind1 = new List<int>();
            databind1 = GetDRCount();

            SerWODRConfig = databind1[0].ToString();
            FunWODRConfig = databind1[2].ToString();
            InfWODRConfig = databind1[4].ToString();

            cells["B57"].Formula = databind1[0].ToString();//Business Services DR UnAvailable / Not Configured
            cells["C57"].Formula = databind1[1].ToString(); //Business Services DR Available / Configured
            cells["D57"].Formula = databind1[2].ToString(); //Functions/ Apps DR UnAvailable / Not Configured
            cells["E57"].Formula = databind1[3].ToString(); //Functions / Apps DR Available / Configured
            cells["F57"].Formula = databind1[4].ToString(); //InfraObjects DR UnAvailable / Not Configured
            cells["G57"].Formula = databind1[5].ToString();//InfraObjects DR Available / Configured

        }

        public void BusinessServicesRecovery(IWorkbook workbook, IWorksheet worksheet)
        {
            IRange cells;

            worksheet = workbook.Worksheets["Business Services-Recovery"];

            cells = worksheet.Cells;

            cells["B3:K3"].Merge();
            cells["B3:K3"].Formula = "Continuity Patrol - Business Services Dashboard - " + MonthName + " " + currentYear;
            cells["B3:K3"].HorizontalAlignment = HAlign.Center;

            cells["B4"].Formula = "Report Generated Time: " + DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt");

            cells["I4"].Formula = "Logged in User :" + LoggedInUser.LoginName;
            cells["I4"].HorizontalAlignment = HAlign.Left;

            //------------------------------------Service Recovery Efficiency and Effectiveness---------------------------------------------------------

            IList<WrokFlowEfficiency> wrokFlowEfficiency = Facade.GetWrokFlowEfficiency();

            var wrokFlowEfficiencylst = wrokFlowEfficiency.OrderByDescending(X => X.Month);

            IRange _cells = worksheet.Cells;
            DateTimeFormatInfo mm = new DateTimeFormatInfo();
            // mm.GetMonthName(8).ToString();


            string CurrMonth = DateTime.Now.ToString("MM");
            int CurMonth = Convert.ToInt32(CurrMonth);

            int dataCount = 0;
            int row = 10;
            foreach (var data in wrokFlowEfficiencylst)
            {
                if (row < 16)
                {
                    dataCount++;
                    int column = 0;
                    string[] xlColumn = { "B", "C", "D" };

                    string ndx = xlColumn[column] + row.ToString();
                    int month = Convert.ToInt32(data.Month);
                    _cells[ndx].Formula = mm.GetMonthName(month).ToString();
                    column++;
                    // ---------------
                    if (CurMonth == month)
                    {

                        AvgSerRecEfficiency = (Convert.ToDouble(data.Efficiency) / 100).ToString();
                        AvgSerRecEffectiveness = (Convert.ToDouble(data.Effectiveness) / 100).ToString();
                    }
                    //    --------------

                    ndx = xlColumn[column] + row.ToString();

                    if (data.Efficiency != null)
                    {
                        double efficiency = Convert.ToDouble(data.Efficiency) / 100;
                        _cells[ndx].Formula = efficiency.ToString();
                        column++;
                    }

                    if (data.Effectiveness != null)
                    {
                        double effectiveness = Convert.ToDouble(data.Effectiveness) / 100;
                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = effectiveness.ToString();
                        column++;
                    }


                    row++;
                }
            }

            //---------------------------------------Services Meeting RTO Objectives Versus Services Not Meeting RTO Objectives (Count)----------------------------------------------

            IList<ServiceRTODaily> rtoMeet = Facade.GetAllServiceRTO_MeetNotMeet("meet");
            IList<ServiceRTODaily> rtoNotMeet = Facade.GetAllServiceRTO_MeetNotMeet("notmeet");

            cells["B24"].Formula = rtoMeet.Count().ToString();//Services Meeting RTO Objectives	
            cells["D24"].Formula = rtoNotMeet.Count().ToString(); //Services Not Meeting RTO Objectives		

            SerMeetingRTO = rtoMeet.Count().ToString();
            SerNotMeetingRTO = rtoNotMeet.Count().ToString();

            int dataCountrtoMeet = 0;
            int rowdataCountrtoMeet = 25;
            foreach (var data in rtoMeet)
            {
                if (rowdataCountrtoMeet < 30)
                {
                    dataCountrtoMeet++;
                    int column = 0;
                    string[] xlColumn = { "B" };

                    string ndx = xlColumn[column] + rowdataCountrtoMeet.ToString();

                    ndx = xlColumn[column] + rowdataCountrtoMeet.ToString();
                    _cells[ndx].Formula = data.BusinessServiceName.ToString() != null ? data.BusinessServiceName.ToString() : "0";// Fail Count";//Total count ";
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    rowdataCountrtoMeet++;
                }

            }


            int dataCountNotrtoMeet = 0;
            int rowdataCountNotrtoMeet = 25;
            foreach (var data in rtoMeet)
            {
                if (rowdataCountNotrtoMeet < 30)
                {
                    dataCountNotrtoMeet++;
                    int column = 0;
                    string[] xlColumn = { "D" };

                    string ndx = xlColumn[column] + dataCountNotrtoMeet.ToString();

                    ndx = xlColumn[column] + rowdataCountNotrtoMeet.ToString();
                    _cells[ndx].Formula = data.BusinessServiceName.ToString() != null ? data.BusinessServiceName.ToString() : "0";// Fail Count";//Total count ";
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    rowdataCountNotrtoMeet++;
                }
            }


            //-----------------------------------Business Services Recovery (Meeting RTO Objectives without Human Intervention) (Count)--------------------------------------------------

            Dictionary<int, string> dicHumanInter = null;
            Dictionary<int, string> dicNonHumanInter = null;
            ActionHumanIntervention actionIntervention = null;
            IList<ActionHumanIntervention> interventionList = null;
            IList<ActionHumanIntervention> interventionCount = null;

            IList<string> wolst = new List<string>();
            IList<string> wilst = new List<string>();
            //int Count = 0;
            string mmm = DateTime.Now.ToString("MMM");

            var businessServiceList = Facade.GetAllBusinessServices();
            interventionList = Facade.GetAllActionHumanIntervention();
            interventionCount = new List<ActionHumanIntervention>();

            if (businessServiceList != null && businessServiceList.Count > 0)
            {
                if (interventionList != null && interventionList.Count > 0)
                {
                    var interbyMonth = interventionList.GroupBy(p => p.Month);

                    foreach (var tempList in interbyMonth)
                    {

                        dicHumanInter = new Dictionary<int, string>();
                        dicNonHumanInter = new Dictionary<int, string>();
                        actionIntervention = new ActionHumanIntervention();

                        actionIntervention.Month = tempList.Key;

                        var humanInter = from t in tempList where t.Skip > 0 || t.Abort > 0 || t.Retry > 0 group t by t.BusinessServiceId into info select info;
                        if (humanInter != null)
                        {
                            foreach (var intr in humanInter)
                            {
                                var ServiceList = (from item in intr select item).ToList().First();
                                if (ServiceList != null)
                                {
                                    if (!dicHumanInter.ContainsKey(intr.Key))
                                        dicHumanInter.Add(intr.Key, ServiceList.ServiceName);
                                    if (ServiceList.Month == mmm.ToUpper())
                                    {
                                        int dataCountrtoMeetWH = 0;
                                        int rowdataCountrtoMeetWH = 38;
                                        if (dicHumanInter != null)
                                        {
                                            // foreach (var data in dicHumanInter)
                                            // {
                                            if (rowdataCountrtoMeetWH < 43)
                                            {
                                                dataCountrtoMeetWH++;
                                                int column = 0;
                                                string[] xlColumn = { "B" };

                                                string ndx = xlColumn[column] + rowdataCountrtoMeetWH.ToString();

                                                ndx = xlColumn[column] + rowdataCountrtoMeetWH.ToString();
                                                _cells[ndx].Formula = ServiceList.ServiceName.ToString() != null ? ServiceList.ServiceName.ToString() : "NA";// Fail Count";//Total count ";
                                                _cells[ndx].HorizontalAlignment = HAlign.Left;
                                                wilst.Add(ServiceList.ServiceName);
                                                column++;

                                                rowdataCountrtoMeetWH++;
                                            }
                                        }

                                    }
                                }
                            }
                        }

                        if (businessServiceList != null)
                        {
                            foreach (var itemService in businessServiceList)
                            {
                                //var ServiceList = (from item in itemService where m=>m.i ).ToList().First();

                                if (!dicHumanInter.ContainsKey(itemService.Id))
                                {
                                    if (!dicNonHumanInter.ContainsKey(itemService.Id))
                                        dicNonHumanInter.Add(itemService.Id, itemService.Name);

                                    //  -------------------

                                    var WOBS = Facade.GetBusinessServiceById(itemService.Id);
                                    string name = WOBS.Name;
                                    if (WOBS.Name != null && WOBS.Name != "")
                                    { wolst.Add(name); }

                                }
                            }

                        }

                        actionIntervention.WithHumanIntervetnion = dicHumanInter.Count;
                        actionIntervention.WithoutHumanIntervetnion = dicNonHumanInter.Count;

                        interventionCount.Add(actionIntervention);
                    }

                    if (interventionCount.Count > 0 && interventionCount != null)
                    {
                        var interrst = from a in interventionCount.OrderByDescending(m => DateTime.ParseExact(m.Month, "MMM", CultureInfo.InvariantCulture).Month)
                                       select a;

                        foreach (var a in interrst)
                        {
                            // string mmm = DateTime.Now.ToString("MMM");
                            if (a.Month == mmm.ToUpper())
                            {
                                string wi = Convert.ToString(a.WithHumanIntervetnion);
                                string wo = Convert.ToString(a.WithoutHumanIntervetnion);
                                SerMeetingRTOWithHumInt = wi != null ? wi : "0";
                                SerMeetingRTOWithOutHumInt = wo != null ? wo : "0";
                                cells["B37"].Formula = SerMeetingRTOWithHumInt;//Services Meeting RTO Objectives with Human Intervention	
                                cells["D37"].Formula = SerMeetingRTOWithOutHumInt;  //Services Meeting RTO Objectives W/o Human Intervention
                            }

                        }

                    }


                }
            }


            IList<string> finalwolst = new List<string>();
            var resultwolst = wolst.Except(wilst);

            int dataCountrtoMeetWO = 0;
            int rowdataCountNotrtoMeetWO = 38;
            foreach (var data in resultwolst.Distinct())
            {
                if (dicNonHumanInter != null)
                {
                    if (rowdataCountNotrtoMeetWO < 43)
                    {
                        dataCountrtoMeetWO++;
                        int column = 0;
                        string[] xlColumn = { "D" };

                        string ndx = xlColumn[column] + dataCountrtoMeetWO.ToString();

                        ndx = xlColumn[column] + rowdataCountNotrtoMeetWO.ToString();
                        _cells[ndx].Formula = data;// Fail Count";//Total count ";
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        rowdataCountNotrtoMeetWO++;
                    }
                }
            }


            cells["B37"].Formula = dicHumanInter != null ? dicHumanInter.Count().ToString() : "0";//Services Meeting RTO Objectives with Human Intervention	
            cells["D37"].Formula = dicNonHumanInter != null ? dicNonHumanInter.Count().ToString() : "0"; //Services Meeting RTO Objectives W/o Human Intervention

            //--------------------------------Business Services / Applications / InfraObjects Never Recovered Or No Drills Conducted (Count)----------------------------------------------


            // B F I not Recov
            var SerNotRec1 = Facade.GetMonthlyNotRecoveredServices("Service", CurMonth);
            var FunNotRec1 = Facade.GetMonthlyNotRecoveredServices("Function", CurMonth);
            var InfraNotRec1 = Facade.GetMonthlyNotRecoveredServices("InfraObject", CurMonth);

            var BusSerNotRec = SerNotRec1.ServiceCount;// for Service
            var BusFunNotRec = FunNotRec1.ServiceCount; // for Function
            var InfraNotRec = InfraNotRec1.ServiceCount; // for infra

            // // B F I Recov
            var SerRec1 = Facade.GetMonthlyRecoveredServices("Service", CurMonth);
            var FunRec1 = Facade.GetMonthlyRecoveredServices("Function", CurMonth);
            var InfraRec1 = Facade.GetMonthlyRecoveredServices("InfraObject", CurMonth);

            var BusSerRec = SerRec1.ServiceCount;// for Service
            var BusFunRec = FunRec1.ServiceCount; // for Function
            var InfraRec = InfraRec1.ServiceCount; // for infra

            // int BusSerRec = Facade.GetBusinessServiceRTORecoveredServiceCount();
            //int BusSerNotRec = Facade.GetBusinessServiceRTONotRecoveredServiceCount();
            //int BusFunRec = Facade.GetAll_FunctionCount_Recovered();
            //int BusFunNotRec = Facade.GetAll_FunctionCount_NotRecovered();
            // int InfraRec = Facade.GetBusinessServiceRTORecoveredInfraObjCount();
            // int InfraNotRec = Facade.GetInfraObject_NotRecovered();

            SerNotRec = BusSerNotRec.ToString();
            FunNotRec = BusFunNotRec.ToString();
            InfNotRec = InfraNotRec.ToString();

            cells["B52"].Formula = BusSerRec.ToString();//Business Services Recovered
            cells["C52"].Formula = BusSerNotRec.ToString();//Business Services Not Recovered
            cells["D52"].Formula = BusFunRec.ToString();//Applications Recovered
            cells["E52"].Formula = BusFunNotRec.ToString();//Applications Not Recovered
            cells["F52"].Formula = InfraRec.ToString();//InfraObjects Recovered
            cells["G52"].Formula = InfraNotRec.ToString();//InfraObjects Not Recovered

        }

        public void BusinessServicesComponentFailures(IWorkbook workbook, IWorksheet worksheet)
        {
            IRange cells;

            worksheet = workbook.Worksheets["Services Component Failures"];

            cells = worksheet.Cells;

            cells["B3:K3"].Merge();
            cells["B3:K3"].Formula = "Continuity Patrol - Business Services Dashboard - " + MonthName + " " + currentYear;
            cells["B3:K3"].HorizontalAlignment = HAlign.Center;

            cells["B4"].Formula = "Report Generated Time: " + DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt");

            cells["I4"].Formula = "Logged in User :" + LoggedInUser.LoginName;
            cells["I4"].HorizontalAlignment = HAlign.Left;

            //----------------------------------------------Business Services Component Failure (Count)-----------------------------------------------------------

            cells["E10"].Formula = "@";
            cells["E10"].Formula = DateTime.Now.ToString("MMM-yyyy");

            string[] xlcomp = { "Server", "DB", "Replication" };

            IList<ComponentFailureDaily> _comp = Facade.GetAllComponentFailDailyServiceMonthCount();

            IList<ComponentFailureDaily> _compyear = Facade.GetAllComponentFailDailyServiceyearCount();

            //  IList<ComponentFailureDaily> _comp = Facade.GetAllComponentFailureDaily();

            if (_compyear != null)
            {

                IRange _cells = worksheet.Cells;

                int dataCount = 0;
                int xlRow = 9;
                int row = 12;
                int compcnt = 0;
                foreach (var data in _compyear)
                {
                    if (row < 17)
                    {
                        dataCount++;
                        int column = 0;
                        string[] xlColumn = { "B", "D", "E" };
                        xlRow++;

                        string ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = data.ServiceName != null ? data.ServiceName : "NA";
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = data.ServiceCount.ToString() != null ? data.ServiceCount.ToString() : "0";
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        if (_comp != null && _comp.Count > 0 && _comp.Count > compcnt)
                            _cells[ndx].Formula = _comp[compcnt].ServiceCount.ToString() != null ? _comp[compcnt].ServiceCount.ToString() : "0";
                        else
                            _cells[ndx].Formula = "0";

                        column++;
                        compcnt++;
                        row++;
                    }
                }
            }

            int Infracnt = 0;
            foreach (var data in _comp)
            {
                Infracnt = Infracnt + data.ServiceCount;
            }
            CompFail = Infracnt.ToString();
        }

        private void OpenExcelFile1(IWorkbook workbook)
        {
            //Response.Clear();
            //Response.ContentType = "application/vnd.ms-excel";
            //Response.AddHeader("Content-Disposition", "attachment; filename=Dashboard.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "DashboardReport" + str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);
            //  var myUrl = "/files/" + str;

            //string myUrl = "E:/RND/files/" + str;
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];

            // string myUrl = "E:\\solutions\\JULY\\cp4.0\\Source\\CP.UI" + "ExcelFiles\\" + str;

            string myUrl = reportPath + "/ExcelFiles/" + str;

            string s = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes')";
            ScriptManager.RegisterStartupScript(updcurrent, updcurrent.GetType(), "Pop up", "OpenPopup('" + myUrl + "');", true);
            //   string fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=InfraObject Summary Report');";
            //ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", s, true);

            // reportPath = reportPath + str;

            //  string myUrl = reportPath + "/ExcelFiles/" + str;
            // Process.Start(reportPath);
            //string myUrl = reportPath + "/ExcelFiles/" + str;
            //var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar = Dashboard Report');";
            //ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }

        #region Protection Function
        // for DR READY OR NOT FOR ALL BS BF INFRA Second
        public List<int> GetDRReadyCount()
        {
            try
            {
                List<int> retrundata = new List<int>();

                int businessServiceDRReadyCount = 0;


                int notConfiguredService = 0;

                IList<BusinessFunction> _BusinessFunctionList = new List<BusinessFunction>();



                int nonConfigureService = 0;
                int businessfunctionCount = 0;

                int bsrdcnt = 0;
                int bsntrdcnt = 0;

                int bfrdcnt = 0;
                int bfntrdcnt = 0;

                int infrardcnt = 0;
                int infrantrdcnt = 0;


                var BusinessServiceList = Facade.GetAllBusinessServices();// (_currentLoginUserId, _companyId, LoggedInUserRole, _isParent, LoggedInUser.InfraObjectAllFlag);
                if (BusinessServiceList != null)
                {
                    IList<BusinessService> FilterBSlist = new List<BusinessService>();
                    if (BusinessServiceList.Count > 0)
                    {
                        int infraObjectCount = 0;
                        int drReadynessCount = 0;
                        int allInfraObjectCount = 0;
                        IList<InfraObject> InfraObjectList = null;
                        foreach (var bService in BusinessServiceList)
                        {

                            InfraObjectList = new List<InfraObject>();
                            InfraObjectList = Facade.GetInfraObjectByBusinessServiceId(bService.Id);


                            if (InfraObjectList == null && bService.IsActive == 1)
                            {
                                nonConfigureService++;
                                FilterBSlist.Add(bService);

                            }
                            if (InfraObjectList != null && InfraObjectList.Count() > 0)
                            {
                                string isDRReady = string.Empty;
                                string infraObjNameList = string.Empty;
                                allInfraObjectCount = InfraObjectList.Count;

                                infraObjectCount = infraObjectCount + allInfraObjectCount;
                                drReadynessCount = Convert.ToInt16(InfraObjectList.Count(a => a.IsDRReady == 1));
                                infrardcnt = infrardcnt + drReadynessCount;
                                infrantrdcnt = infrantrdcnt + Convert.ToInt16(InfraObjectList.Count(a => a.IsDRReady == 0));
                                FilterBSlist.Add(bService);

                                // Considered DRReadyness at business service level i.e.if anyone of infraObject is not having DRReady then that businessService is not DRReady.
                                if (allInfraObjectCount == drReadynessCount)
                                {
                                    businessServiceDRReadyCount = businessServiceDRReadyCount + 1;
                                    isDRReady = "Y";
                                }
                                else
                                {
                                    _BusinessFunctionList = Facade.GetBusinessFunctionsByBusinessServiceId(bService.Id);
                                    if (_BusinessFunctionList != null && _BusinessFunctionList.Count() > 0)
                                    {
                                        businessfunctionCount = businessfunctionCount + _BusinessFunctionList.Count();
                                        bfntrdcnt = businessfunctionCount;

                                    }


                                    isDRReady = "N";
                                }

                            }

                        }



                        _BusinessFunctionList = Facade.GetAllBusinessFunctions();
                        bfrdcnt = _BusinessFunctionList.Count() - bfntrdcnt;

                        int serviceCount = (BusinessServiceList.Count - businessServiceDRReadyCount);



                        bsrdcnt = businessServiceDRReadyCount; //BUSINESSNESS SERVICE READY
                        bsntrdcnt = (BusinessServiceList.Count - businessServiceDRReadyCount) - nonConfigureService;//BUSINESSNESS SERVICE NOT READY

                        bfrdcnt = _BusinessFunctionList.Count() - bfntrdcnt; //BUSINESS FUCTION REDAY
                        bfntrdcnt = bfntrdcnt + 0; //BUSINESS FUCTION NOT REDAY

                        infrardcnt = infrardcnt + 0; //INFRA READY
                        infrantrdcnt = infrantrdcnt + 0; // INFRA NOT READY

                        retrundata.Add(bsrdcnt);
                        retrundata.Add(bsntrdcnt);
                        retrundata.Add(bfrdcnt);
                        retrundata.Add(bfntrdcnt);
                        retrundata.Add(infrardcnt);
                        retrundata.Add(infrantrdcnt);





                    }
                }
                return retrundata;
            }
            catch (CpException exc)
            {
                return null;

                //ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                return null;
                //var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting DRReadyCount", ex);
                //ExceptionManager.Manage(cpException);
            }
        }

        //FOR DR OR NOT DR Third
        public List<int> GetDRCount()
        {
            try
            {
                List<int> retrundata1 = new List<int>();



                IList<BusinessFunction> _BusinessFunctionList = new List<BusinessFunction>();


                var bf = new List<int>();
                var bs = new List<int>();

                int nonConfigureService = 0;


                int bsrdcnt = 0;
                int bsntrdcnt = 0;

                int bfrdcnt = 0;
                int bfntrdcnt = 0;

                int infrardcnt = 0;
                int infrantrdcnt = 0;


                var BusinessServiceList = Facade.GetAllBusinessServices();// (_currentLoginUserId, _companyId, LoggedInUserRole, _isParent, LoggedInUser.InfraObjectAllFlag);
                if (BusinessServiceList != null)
                {
                    IList<BusinessService> FilterBSlist = new List<BusinessService>();
                    if (BusinessServiceList.Count > 0)
                    {

                        IList<InfraObject> InfraObjectList = null;
                        foreach (var bService in BusinessServiceList)
                        {

                            InfraObjectList = new List<InfraObject>();
                            InfraObjectList = Facade.GetInfraObjectByBusinessServiceId(bService.Id);


                            if (InfraObjectList == null && bService.IsActive == 1)
                            {
                                nonConfigureService++;
                                FilterBSlist.Add(bService);

                            }
                            if (InfraObjectList != null && InfraObjectList.Count() > 0)
                            {

                                foreach (var infra in InfraObjectList)
                                {


                                    if (DRWorkFlowIsAttach(infra.Id))
                                    {
                                        infrardcnt++;
                                        if (!bs.Contains(infra.BusinessServiceId))
                                        {
                                            bs.Add(infra.BusinessServiceId);
                                            bsrdcnt++;

                                        }

                                        if (!bf.Contains(infra.BusinessFunctionId))
                                        {
                                            bf.Add(infra.BusinessFunctionId);
                                            bfrdcnt++;

                                        }

                                    }

                                    else
                                    {

                                        infrantrdcnt++;
                                        if (!bs.Contains(infra.BusinessServiceId))
                                        {
                                            bs.Add(infra.BusinessServiceId);
                                            bsntrdcnt++;

                                        }

                                        if (!bf.Contains(infra.BusinessFunctionId))
                                        {
                                            bf.Add(infra.BusinessFunctionId);
                                            bfntrdcnt++;

                                        }

                                    }

                                }

                            }
                        }

                        bsrdcnt = bsrdcnt + 0;
                        bsntrdcnt = bsntrdcnt + 0;

                        bfrdcnt = bfrdcnt + 0;
                        bfntrdcnt = bfntrdcnt + 0;

                        infrardcnt = infrardcnt + 0;
                        infrantrdcnt = infrantrdcnt + 0;

                        retrundata1.Add(bsntrdcnt);
                        retrundata1.Add(bsrdcnt);
                        retrundata1.Add(bfntrdcnt);
                        retrundata1.Add(bfrdcnt);
                        retrundata1.Add(infrantrdcnt);
                        retrundata1.Add(infrardcnt);

                    }
                }
                return retrundata1;
            }

            catch (CpException exc)
            {
                return null;
                //xceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                return null;
                //ar cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting DRReadyCount", ex);
                //xceptionManager.Manage(cpException);
            }


        }

        public bool DRWorkFlowIsAttach(int infraId)
        {

            var _attach_Infra_Wf = Facade.GetGroupWorkflowsByInfraObjectId(infraId);

            if (_attach_Infra_Wf == null)
                return false;

            foreach (var item in _attach_Infra_Wf)
            {
                var wrkFlw = Facade.GetWorkflowById(Convert.ToInt32(item.WorkflowId));
                if (wrkFlw != null)
                {
                    string actionsId = ReadWorkflowXml(wrkFlw.Xml);

                    string[] actionIdArray = actionsId.Split(',');

                    string actionsInfo = string.Empty;

                    for (int i = 0; i < actionIdArray.Length; i++)
                    {
                        var workflowAction = Facade.GetWorkflowActionById(Convert.ToInt32(actionIdArray[i]));

                        var actionType = workflowAction != null ? (WorkflowActionType)Convert.ToInt32(workflowAction.ActionType) : new WorkflowActionType();

                        if (actionType == WorkflowActionType.DRReady)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        public string ReadWorkflowXml(string wrkflowxml)
        {
            try
            {
                var reader = XmlReader.Create(new StringReader(wrkflowxml));

                var xmldoc = new XmlDocument();
                xmldoc.Load(reader);

                var nodeslist = xmldoc.ChildNodes;

                var actionsIds = string.Empty;

                foreach (XmlNode nd in nodeslist)
                {
                    if (nd.HasChildNodes)
                    {
                        var chldnd = nd.ChildNodes;

                        foreach (XmlNode chd in chldnd)
                        {
                            if (chd.Attributes["id"].Value.Contains("div"))
                            {
                                //  actionsIds = actionsIds + "," + chd.Attributes["id"].Value + "^" + chd.Attributes["targetActionId"].Value + "^" + chd.Attributes["actionDifference"].Value;
                            }
                            else
                            {
                                if (chd.Attributes["id"].Value.All(char.IsDigit))
                                    actionsIds = actionsIds + "," + chd.Attributes["id"].Value;
                            }
                        }
                    }
                }

                actionsIds = actionsIds.TrimStart(',');

                return actionsIds;
            }
            catch (Exception ex)
            {
                ExceptionManager.Manage(new CpException(CpExceptionType.DataAccessInsertOperation, "Error While ReadWorkflowXml: " + ex.InnerException.Message, ex));
                return "";
            }
        }
        //END

        // Dr Ready First 
        public List<int> GetDRStatusCount()
        {
            try
            {
                List<int> retrundata1 = new List<int>();



                IList<BusinessFunction> _BusinessFunctionList = new List<BusinessFunction>();


                var bf = new List<int>();
                var bs = new List<int>();

                int nonConfigureService = 0;


                int bsrdcnt = 0;
                int bsntrdcnt = 0;

                int bfrdcnt = 0;
                int bfntrdcnt = 0;

                int infrardcnt = 0;
                int infrantrdcnt = 0;


                var BusinessServiceList = Facade.GetAllBusinessServices();// (_currentLoginUserId, _companyId, LoggedInUserRole, _isParent, LoggedInUser.InfraObjectAllFlag);
                if (BusinessServiceList != null)
                {
                    IList<BusinessService> FilterBSlist = new List<BusinessService>();
                    if (BusinessServiceList.Count > 0)
                    {

                        IList<InfraObject> InfraObjectList = null;
                        foreach (var bService in BusinessServiceList)
                        {

                            InfraObjectList = new List<InfraObject>();
                            InfraObjectList = Facade.GetInfraObjectByBusinessServiceId(bService.Id);


                            if (InfraObjectList == null && bService.IsActive == 1)
                            {
                                nonConfigureService++;
                                FilterBSlist.Add(bService);

                            }
                            if (InfraObjectList != null && InfraObjectList.Count() > 0)
                            {

                                foreach (var infra in InfraObjectList)
                                {


                                    if (getdrstatus(infra.Id))
                                    {
                                        infrardcnt++;
                                        if (!bs.Contains(infra.BusinessServiceId))
                                        {
                                            bs.Add(infra.BusinessServiceId);
                                            bsrdcnt++;

                                        }

                                        if (!bf.Contains(infra.BusinessFunctionId))
                                        {
                                            bf.Add(infra.BusinessFunctionId);
                                            bfrdcnt++;

                                        }

                                    }

                                    else
                                    {

                                        infrantrdcnt++;
                                        if (!bs.Contains(infra.BusinessServiceId))
                                        {
                                            bs.Add(infra.BusinessServiceId);
                                            bsntrdcnt++;

                                        }

                                        if (!bf.Contains(infra.BusinessFunctionId))
                                        {
                                            bf.Add(infra.BusinessFunctionId);
                                            bfntrdcnt++;

                                        }

                                    }

                                }

                            }
                        }

                        bsrdcnt = bsrdcnt + 0;
                        bsntrdcnt = bsntrdcnt + 0;

                        bfrdcnt = bfrdcnt + 0;
                        bfntrdcnt = bfntrdcnt + 0;

                        infrardcnt = infrardcnt + 0;
                        infrantrdcnt = infrantrdcnt + 0;

                        retrundata1.Add(bsntrdcnt);
                        retrundata1.Add(bsrdcnt);
                        retrundata1.Add(bfntrdcnt);
                        retrundata1.Add(bfrdcnt);
                        retrundata1.Add(infrantrdcnt);
                        retrundata1.Add(infrardcnt);

                    }
                }
                return retrundata1;
            }

            catch (CpException exc)
            {
                return null;
                //xceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                return null;
                //ar cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting DRReadyCount", ex);
                //xceptionManager.Manage(cpException);
            }


        }

        private Boolean getdrstatus(int infraid)
        {
            Boolean rv = false;

            int infrstatus = Facade.GetDRstatusbyInfraid(infraid);

            rv = infrstatus == 0 ? false : true;
            return rv;
        }

        #endregion Protection Function

        #endregion  DashboardReport

        protected void imgBtnReport_Click(object sender, ImageClickEventArgs e)
        {
            DashBoardAlert();
            //updcurrent.Update();
        }
    }
}