﻿<%@ Page Title="Continuity Patrol :: Postgre SQLMonitoring" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="PostgreSQLMonitoring.aspx.cs" Inherits="CP.UI.Admin.PostgreSQLMonitoring" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <div class="innerLR">
  
         <h3><img src="../Images/infra-icon.png">
           Postgre SQL Monitoring</h3>
      
        <div class="widget" data-toggle="collapse-widget" data-collapse-closed="true">
            <div class="widget-head" id="exhealth">
                
                <h4 class="heading">
                    <img src="../Images/health.png" />
                    <asp:Label ID="lblgrName" runat="server" Text="DB Monitoring" Style=""></asp:Label>
                </h4>
            </div>
            <div id="exhealth-content">
                <div class="widget-body">


                    <table class="table table-striped table-bordered table-condensed " width="100%">
                     
                        <thead>
                            <tr>
                                <th class="col-md-4">Component
                                </th>
                                <th class="col-md-4">Production Server <asp:Label ID="lblPRIPAddress" runat="server" Text=""></asp:Label>
                                </th>
                                <th>DR Server  <asp:Label ID="lblDRIPAddress" runat="server" Text=""></asp:Label>
                                </th>
                            </tr>
                      
                        </thead>
                        
                        <tbody>
                            <tr>
                                <td>Database Version
                                </td>
                                <td class="text-indent">
                                    <span class="" id="spnPRDatabaseVersion" runat="server"></span>
                                    <asp:Label ID="lblPRDatabaseVersion" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span class="" runat="server" id="spnDRDatabaseVersion"></span>
                                    <asp:Label ID="lblDRDatabaseVersion" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Service Status
                                </td>
                                <td class="text-indent">
                                    <span class="" id="spnprdatabaseServiceStatus" runat="server"></span>
                                    <asp:Label ID="lblprdatabaseServiceStatus" runat="server" CssClass="text-success"
                                        Text="NA"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span class="" id="spndrdatabaseServiceStatus" runat="server"></span>
                                    <asp:Label ID="lbldrdatabaseServiceStatus" runat="server" CssClass="text-success"
                                        Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Cluster State
                                </td>
                                <td class="text-indent">
                                    <span class="" id="spnprDBClusterState" runat="server"></span>
                                    <asp:Label ID="lblprDBClusterState" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span class="" id="spndrDBClusterState" runat="server"></span>&nbsp;
                                    <asp:Label ID="lbldrDBClusterState" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Current xlog location
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnCurrentxloglocation" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblCurrentxloglocation" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>--
                                </td>
                            </tr>
                            <tr>
                                <td>Last xlog receive location
                                </td>
                                <td>--
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnLastxlogreceivelocation" CssClass="icon-numbering" runat="server"></asp:Label>
                                    <asp:Label ID="lblLastxlogreceivelocation" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last xlog replay location
                                </td>
                                <td>--
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnLastxlogreplaylocation" CssClass="icon-numbering" runat="server"></asp:Label>
                                    <asp:Label ID="lblLastxlogreplaylocation" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>DataLag (in MB)
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnDataLagMB" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDataLagMB" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>--
                                </td>
                            </tr>
                            <tr>
                                <td>DataLag (hh:mm:ss)
                                </td>
                                <td>--
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnDataLagHR" CssClass="" runat="server"></asp:Label>
                                    <asp:Label ID="lblDataLagHR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                       
                    </table>
               
                </div>
            </div>
        </div>
        <div class="widget" data-toggle="collapse-widget" data-collapse-closed="true">
            <div class="widget-head" id="monitor">
               
                <h4 class="heading">
                    <asp:Label ID="lblAppName" runat="server" Text="DB Replication"></asp:Label>
                </h4>
            </div>
            <div class="widget-body">


                <div id="monitor-content">
                    <table id="tblPostgreReplication" class="table table-striped table-bordered table-condensed margin-bottom-none" width="100%">
                        <thead>
                            <tr>
                                <th>Replication Monitor
                                </th>
                                <th>Production Server
                                </th>
                                <th>DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Replication Status
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnPRReplicationStatus" runat="server" CssClass=""></asp:Label>
                                    <asp:Label ID="lblPRReplicationStatus" runat="server" CssClass="active" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="spnDRReplicationStatus" runat="server" CssClass=""></asp:Label>&nbsp;
                                    <asp:Label ID="lblDRReplicationStatus" runat="server" CssClass="" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Recovery Status
                                </td>
                                <td>
                                    <asp:Label ID="spnPRDatabaseRecoveryStatus" runat="server" CssClass=""></asp:Label>&nbsp;
                                    <asp:Label ID="lblPRDatabaseRecoveryStatus" runat="server" CssClass="inactive" Text="NA"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnDRDatabaseRecoveryStatus" runat="server" CssClass=""></asp:Label>
                                    <asp:Label ID="lblDRDatabaseRecoveryStatus" runat="server" CssClass="active" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Data Directory path
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnPRDatabaseDataDirectorypath" runat="server" CssClass=""></asp:Label>
                                    <asp:Label ID="lblPRDatabaseDataDirectorypath" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnDRDatabaseDataDirectorypath" runat="server" CssClass=""></asp:Label>
                                    <asp:Label ID="lblDRDatabaseDataDirectorypath" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</asp:Content>
