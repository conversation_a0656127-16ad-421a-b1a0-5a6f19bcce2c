﻿<?xml version="1.0" encoding="utf-8" ?>
<Configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <ControlEncodingContexts>
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlAnchor" PropertyName="HRef" EncodingContext="HtmlAttribute" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlHead" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlImage" PropertyName="Src" EncodingContext="HtmlAttribute" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlInputImage" PropertyName="Src" EncodingContext="HtmlAttribute" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlInputRadioButton" PropertyName="Value" EncodingContext="HtmlAttribute" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlTitle" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.BaseDataList" PropertyName="Caption" EncodingContext="HtmlAttribute" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Calendar" PropertyName="Caption" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Calendar" PropertyName="NextMonthText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Calendar" PropertyName="PrevMonthText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Calendar" PropertyName="SelectMonthText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Calendar" PropertyName="SelectWeekText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="CancelDestinationPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="ChangePasswordFailureText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="ChangePasswordTitleText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="ConfirmNewPasswordLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="ContinueDestinationPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="CreateUserText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="EditProfileText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="HelpPageText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="NewPasswordLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="PasswordHintText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="PasswordLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="PasswordRecoveryText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="SuccessPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="SuccessText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="SuccessTitleText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ChangePassword" PropertyName="UserNameLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CheckBox" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CompareValidator" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="AnswerLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="CompleteSuccessText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="ConfirmPasswordLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="ContinueDestinationPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="DuplicateEmailErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="DuplicateUserNameErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="EditProfileText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="EmailLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="UnknownErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="HelpPageText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="InvalidAnswerErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="InvalidEmailErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="InvalidPasswordErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="InvalidQuestionErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="PasswordHintText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="PasswordLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="QuestionLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="UserNameLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="CancelDestinationPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="FinishDestinationPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CreateUserWizard" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.CustomValidator" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.DataControlFieldCell" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.DataControlFieldHeaderCell" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.DataGrid" PropertyName="Caption" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.DataList" PropertyName="Caption" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.DetailsView" PropertyName="Caption" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.DetailsView" PropertyName="EmptyDataText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.DetailsView" PropertyName="FooterText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.DetailsView" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.FormView" PropertyName="Caption" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.FormView" PropertyName="EmptyDataText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.FormView" PropertyName="FooterText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.FormView" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.GridView" PropertyName="Caption" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.GridView" PropertyName="EmptyDataText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.HyperLink" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Label" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.LinkButton" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ListBox" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.ListControl" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Literal" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="CreateUserText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="DestinationPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="HelpPageText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="FailureText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="LoginButtonText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="PasswordLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="PasswordRecoveryText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="RememberMeText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="TitleText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Login" PropertyName="UserNameLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.LoginStatus" PropertyName="LoginText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.LoginStatus" PropertyName="LogoutPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.LoginStatus" PropertyName="LogoutText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Panel" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="AnswerLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="GeneralFailureText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="HelpPageText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="QuestionFailureText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="QuestionInstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="QuestionLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="QuestionTitleText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="SubmitButtonText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="SuccessPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="SuccessText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="UserNameFailureText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="UserNameInstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="UserNameLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.PasswordRecovery" PropertyName="UserNameTitleText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.RadioButton" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.RadioButtonList" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.RangeValidator" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.RegularExpressionValidator" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.RequiredFieldValidator" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Table" PropertyName="Caption" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.TableCell" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.TableHeaderCell" PropertyName="Text" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.AppearanceEditorPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.BehaviorEditorPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.BehaviorEditorPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogZone" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogZone" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogZone" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogZone" PropertyName="SelectTargetZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogZoneBase" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogZoneBase" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogZoneBase" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.CatalogZoneBase" PropertyName="SelectTargetZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConfigureConnectionTitle" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConnectToConsumerInstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConnectToConsumerText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConnectToConsumerTitle" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConnectToProviderInstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConnectToProviderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConnectToProviderTitle" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConsumersTitle" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ConsumersInstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ExistingConnectionErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="GetText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="GetFromText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="InstructionTitle" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="NewConnectionErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="NoExistingConnectionInstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="NoExistingConnectionTitle" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ProvidersTitle" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="ProvidersInstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="SendText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ConnectionsZone" PropertyName="SendToText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.DeclarativeCatalogPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.DeclarativeCatalogPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorZone" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorZone" PropertyName="ErrorText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorZone" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorZone" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorZoneBase" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorZoneBase" PropertyName="ErrorText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorZoneBase" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.EditorZoneBase" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ErrorWebPart" PropertyName="AuthorizationFilter" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ErrorWebPart" PropertyName="ImportErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ErrorWebPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ErrorWebPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.GenericWebPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.GenericWebPart" PropertyName="AuthorizationFilter" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.GenericWebPart" PropertyName="ImportErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.GenericWebPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ImportCatalogPart" PropertyName="BrowseHelpText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ImportCatalogPart" PropertyName="ImportedPartLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ImportCatalogPart" PropertyName="PartImportErrorLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ImportCatalogPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ImportCatalogPart" PropertyName="UploadHelpText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ImportCatalogPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.LayoutEditorPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.LayoutEditorPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.PageCatalogPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.PageCatalogPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.Part" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.Part" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.PropertyGridEditorPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.PropertyGridEditorPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ProxyWebPart" PropertyName="AuthorizationFilter" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ProxyWebPart" PropertyName="ImportErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ProxyWebPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ProxyWebPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ToolZone" PropertyName="InstructionText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ToolZone" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.ToolZone" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.UnauthorizedWebPart" PropertyName="AuthorizationFilter" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.UnauthorizedWebPart" PropertyName="ImportErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.UnauthorizedWebPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.UnauthorizedWebPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPart" PropertyName="AuthorizationFilter" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPart" PropertyName="ImportErrorMessage" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPart" PropertyName="Title" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPart" PropertyName="GroupingText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPartZone" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPartZone" PropertyName="MenuLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPartZone" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPartZoneBase" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPartZoneBase" PropertyName="MenuLabelText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebPartZoneBase" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebZone" PropertyName="EmptyZoneText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.WebParts.WebZone" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Wizard" PropertyName="CancelDestinationPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Wizard" PropertyName="FinishDestinationPageUrl" EncodingContext="Url" />
    <ControlEncodingContext FullClassName="System.Web.UI.WebControls.Wizard" PropertyName="HeaderText" EncodingContext="Html" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlTableCell" PropertyName="InnerHtml" EncodingContext="SafeHtml" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlTableRow" PropertyName="InnerHtml" EncodingContext="SafeHtml" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlTextArea" PropertyName="InnerHtml" EncodingContext="SafeHtml" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlAnchor" PropertyName="InnerHtml" EncodingContext="SafeHtml" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlButton" PropertyName="InnerHtml" EncodingContext="SafeHtml" />
    <ControlEncodingContext FullClassName="System.Web.UI.HtmlControls.HtmlGenericControl" PropertyName="InnerHtml" EncodingContext="SafeHtml" />
  </ControlEncodingContexts>
  <DoubleEncodingFilter Enabled="true" />
  <EncodeDerivedControls Enabled="true" />
  <MarkAntiXssOutput Enabled="false" Color="Yellow"/>
</Configuration>