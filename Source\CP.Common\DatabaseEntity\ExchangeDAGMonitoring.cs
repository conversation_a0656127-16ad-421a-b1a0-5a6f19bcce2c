﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ExchangeDAGMonitoring", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ExchangeDAGMonitoring : BaseEntity
    {
        private ReplicationBase _replicationBase = new ReplicationBase();

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string PRMailboxDatabaseName { get; set; }

        [DataMember]
        public string DRMailboxDatabaseName { get; set; }

        [DataMember]
        public int CopyQueueLength { get; set; }

        [DataMember]
        public int ReplayQueueLength { get; set; }

        [DataMember]
        public string ReplicationStatus { get; set; }

        [DataMember]
        public string DAGName { get; set; }

        [DataMember]
        public string IPAddress { get; set; }

        [DataMember]
        public string WitnessServer { get; set; }

        [DataMember]
        public string WITNESSDIRECTORY { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }
        }

        //[DataMember]
        //public string PRCopyQueueLength { get; set; }

        //[DataMember]
        //public string DRCopyQueueLength { get; set; }

        //[DataMember]
        //public string PRReplayQueueLength { get; set; }


        //[DataMember]
        //public string DRReplayQueueLength { get; set; }


        //[DataMember]
        //public IList<ExchangeDAGMonitoring> ExchangeDAGMonitoringList
        //{
        //    get
        //    {
        //        return _monitorings;
        //    }
        //    set
        //    {
        //        _monitorings = value;
        //    }
        //}

        #endregion Properties
    }
}