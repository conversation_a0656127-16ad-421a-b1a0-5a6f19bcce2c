﻿using System;
using System.Collections.Generic;
using System;
using System.Runtime.Serialization;
using CP.Common.Base;


namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "EMCDevicesDetails", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class OpenShiftMonitoringDetails : BaseEntity
    {

        #region Properties
        //Cluster Monitoring
        [DataMember]
        public int InfraObjectId { get; set; }
        [DataMember]
        public string OpenShiftClusterHostnameFQDN { get; set; }
        [DataMember]
        public string ClusterResourceName { get; set; }
        [DataMember]
        public string ControlPlaneType { get; set; }
        [DataMember]
        public string ClusterStatus { get; set; }
        [DataMember]
        public string Infrastructure { get; set; }
        [DataMember]
        public string DistributionVersion { get; set; }
        [DataMember]
        public string ClusterAPIAddress { get; set; }
        [DataMember]
        public string ProjectName { get; set; }
        [DataMember]
        public string ProjectStatus { get; set; }

        //Workload Monitoring
        [DataMember]
        public string StatefulSetNameAndPODNamesAndReadyStatusPodCount { get; set; }
        [DataMember]
        public string ReplicaSetNameAndPODNamesAndReadyStatusPodCount { get; set; }
        [DataMember]
        public string DeploymentSetNameAndPODNamesAndReadyStatusPodCount { get; set; }

        //Virtualization Monitoring
        [DataMember]
        public string VirtualMachineNameAndStatus { get; set; }

        //MachineSet Monitoring
        [DataMember]
        public string MachineSetNameAndMachineCount { get; set; }

        //Node Monitoring
        [DataMember]
        public string NodeNameAndNodeStatusAndNodeRole { get; set; }

        #endregion Properties

    }
}