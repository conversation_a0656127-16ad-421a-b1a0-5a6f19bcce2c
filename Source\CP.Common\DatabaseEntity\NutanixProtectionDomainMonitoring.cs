﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "NutanixProtectionDomainMonitoring", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class NutanixProtectionDomainMonitoring : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string PRProtectionDomainName { get; set; }

        [DataMember]
        public string DRProtectionDomainName { get; set; }

        [DataMember]
        public string PRProtectionDomainMode { get; set; }

        [DataMember]
        public string DRProtectionDomainMode { get; set; }

        [DataMember]
        public string PRProtectionDomainVMCount { get; set; }

        [DataMember]
        public string DRProtectionDomainVMCount { get; set; }

        [DataMember]
        public string PRProtectedEntities { get; set; }

        [DataMember]
        public string DRProtectedEntities { get; set; }

        [DataMember]
        public DateTime CreateDate { get; set; }


        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }
        #endregion Properties
    }
}
