﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ActiveDirectoryConfig.ascx.cs" Inherits="CP.UI.Controls.ActiveDirectoryConfig" %>
<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>

<asp:UpdatePanel ID="upnlDoubleTake" runat="server" UpdateMode="Conditional">
    <ContentTemplate>
        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-head">
                    <h4 class="heading">
                        Active Directory Configuration</h4>
                </div>
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-replication" for="txtName">
                            Active Directory Target Name<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtTargetName" CssClass="form-control" style="width:49% !important;" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvtxtTargetName" runat="server" CssClass="error" ControlToValidate="txtTargetName"
                                Display="Dynamic" ErrorMessage="Please Enter Target Name"></asp:RequiredFieldValidator>
                        </div>
                    </div>   
                    
                     <div class="form-group">
                        <label class="col-replication" for="txtName">
                            Active Directory Scope Name<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtScopeName" CssClass="form-control" style="width:49% !important;" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvtxtScopeName" runat="server" CssClass="error" ControlToValidate="txtScopeName"
                                Display="Dynamic" ErrorMessage="Please Enter Scope Name"></asp:RequiredFieldValidator>
                        </div>
                    </div>                 
                </div>
                </div>
                </div>
                <div class="form-actions row">
                        <div class="col-lg-3">
                            <asp:Label ID="Label7" runat="server" Text="&nbsp;"></asp:Label>
                        </div>
                        <div class="col-lg-6" style="margin-left: 40.3%;">
                            <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save" OnClick ="btnSave_Click" />
                                
                            <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server"
                                Text="Cancel" CausesValidation="false" OnClick ="btnCancel_Click" />
                        </div>
                    </div>
    </ContentTemplate>
</asp:UpdatePanel>



