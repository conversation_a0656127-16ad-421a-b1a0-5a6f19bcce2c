﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.Shared;
using log4net;
using System.Net;

namespace CP.UI.SNMP
{
    public partial class DiscoverySummary : BasePage
    {
        #region Current User variables
        private static int _currentLoginUserId;
        private static string _currentLoggedUserName;
        private static int _companyId;
        private static bool _isUserSuperAdmin;
        private static bool _isParent;

        public static string IPAddress = string.Empty;

        private static readonly ILog logger = LogManager.GetLogger(typeof(DiscoverySummary));

        #endregion

        static IFacade _facade = new Facade();

        #region PrepareView

        public override void PrepareView()
        {
            logger.Info("In Prepareview Method of DiscoverySummary and Logged In User is:" + LoggedInUserName);
            try
            {
                string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
                IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();
                if (IsUserOperator || IsUserManager)
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }

                // current variables
                _currentLoggedUserName = LoggedInUserName;
                _currentLoginUserId = LoggedInUserId;
                _companyId = LoggedInUserCompanyId;
                _isUserSuperAdmin = IsUserSuperAdmin;
                _isParent = LoggedInUserCompany.IsParent;

                PopulateScanSummary();
            }
            catch (Exception ex)
            {
                logger.Error("Exception in prepareview method of DiscoverySummary() and the exception is :" + ex.Message);
            }
        }

        #endregion

        #region Public Methods

        public void PopulateScanSummary()
        {
            logger.Info("In PopulateScanSummary()");
            try
            {
                IList<DiscoverScan> objListScan = _facade.GetAllDiscoverScan();
                lvScan.DataSource = objListScan;
                lvScan.DataBind();
            }
            catch (Exception ex)
            {
                logger.Error("Exception in PopulateScanSummary() and the exception is:" + ex.Message);
            }
        }

        public void PopulateScanData(int iScanID)
        {
            logger.Info("In PopulateScanData()");
            try
            {
                IList<DiscoveryHostLogs> objListSummary = _facade.GetAllDiscoveryHostLogsByScanId(iScanID);
                lvScanSummary.DataSource = objListSummary;
                lvScanSummary.DataBind();
            }
            catch (Exception ex)
            {
                logger.Error("Exception in PopulateScanData() and the exception is:" + ex.Message);
            }
        }

        protected void btnView_OnClick(object sender, EventArgs e)
        {
            try
            {
            ListViewItem objItem = ((ListViewItem)((Button)sender).NamingContainer);
            Label lblScanID = objItem.FindControl("lblScanID") as Label;
            if (lblScanID != null)
            {
                PopulateScanData(Convert.ToInt32(lblScanID.Text));
            }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in btnView_OnClick() and the exception is :" + ex.Message);

            }
        }

        #endregion
    }
}