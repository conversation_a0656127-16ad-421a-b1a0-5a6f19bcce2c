﻿

(function($)
{
	
	$.modal1 = function(options)
	{
		var settings = $.extend({}, $.modal1.defaults, options),
			root = getModalDiv(),

			
			winX = 0,
			winY = 0,
			contentWidth = 0,
			contentHeight = 0,
			mouseX = 0,
			mouseY = 0,
			resized;

		
		var content = '';
		var contentObj;
		if (settings.content)
		{
			if (typeof(settings.content) == 'string')
			{
				content = settings.content;
			}
			else
			{
				contentObj = settings.content.clone(true).show();
			}
		}
		else
		{
			
			content = '';
		}

	
		var titleClass = settings.title ? '' : ' no-title';
		var title = settings.title ? '<div class="modal-header"> <h3 class="modal-title">'+settings.title+'</h3><a class="close">ˣ</a> </div>' : '';

	
		var sizeParts = new Array();
		sizeParts.push('min-width:'+settings.minWidth+'px;');
		sizeParts.push('min-height:'+settings.minHeight+'px;');
		if (settings.width)
		{
			sizeParts.push('width:'+settings.width+'px; ');
		}
		if (settings.height)
		{
			sizeParts.push('height:'+settings.height+'px; ');
		}
		if (settings.maxWidth)
		{
			//sizeParts.push('max-width:'+settings.maxWidth+'px; ');
		}
		if (settings.maxHeight)
		{
			sizeParts.push('max-height:'+settings.maxHeight+'px; ');
		}
		var contentStyle = (sizeParts.length > 0) ? ' style="'+sizeParts.join(' ')+'"' : '';

		
		var borderOpen = settings.border ? '"><div class="modal-content '+titleClass : titleClass;
		var borderClose = settings.border ? '></div' : '';

	
		//var scrollClass = settings.scrolling ? ' modal-scroll' : '';

		var win = $('<div class="modal-dialog'+borderOpen+'">'+title+'<div class="modal-body">'+content+'</div></div'+borderClose+'>').appendTo(root);
		var contentDiv = win.find('.modal-body');
		if (contentObj)
		{
			contentObj.appendTo(contentDiv);
		}

		
		if (settings.resizable && settings.border)
		{
		
			var resizeFunc = function(event)
			{
				
				var offsetX = event.pageX-mouseX,
					offsetY = event.pageY-mouseY,

					
					newWidth = Math.max(settings.minWidth, contentWidth+(resized.width*offsetX)),
					newHeight = Math.max(settings.minHeight, contentHeight+(resized.height*offsetY)),

					
					correctX = 0,
					correctY = 0;

			
				if (settings.maxWidth && newWidth > settings.maxWidth)
				{
					correctX = newWidth-settings.maxWidth;
					newWidth = settings.maxWidth;
				}
				if (settings.maxHeight && newHeight > settings.maxHeight)
				{
					correctY = newHeight-settings.maxHeight;
					newHeight = settings.maxHeight;
				}

				contentDiv.css({
					width: newWidth+'px',
					height: newHeight+'px'
				});
				win.css({
					left: (winX+(resized.left*(offsetX+correctX)))+'px',
					top: (winY+(resized.top*(offsetY+correctY)))+'px'
				});
			};

			
			$('<div class="modal-resize-tl"></div>').appendTo(win).data('modal-resize', {
				top: 1, left: 1,
				height: -1, width: -1
			}).add(
				$('<div class="modal-resize-t"></div>').appendTo(win).data('modal-resize', {
					top: 1, left: 0,
					height: -1, width: 0
				})
			).add(
				$('<div class="modal-resize-tr"></div>').appendTo(win).data('modal-resize', {
					top: 1, left: 0,
					height: -1, width: 1
				})
			).add(
				$('<div class="modal-resize-r"></div>').appendTo(win).data('modal-resize', {
					top: 0, left: 0,
					height: 0, width: 1
				})
			).add(
				$('<div class="modal-resize-br"></div>').appendTo(win).data('modal-resize', {
					top: 0, left: 0,
					height: 1, width: 1
				})
			).add(
				$('<div class="modal-resize-b"></div>').appendTo(win).data('modal-resize', {
					top: 0, left: 0,
					height: 1, width: 0
				})
			).add(
				$('<div class="modal-resize-bl"></div>').appendTo(win).data('modal-resize', {
					top: 0, left: 1,
					height: 1, width: -1
				})
			).add(
				$('<div class="modal-resize-l"></div>').appendTo(win).data('modal-resize', {
					top: 0, left: 1,
					height: 0, width: -1
				})
			).mousedown(function(event)
			{
			
				contentWidth = contentDiv.width();
				contentHeight = contentDiv.height();
				var position = win.position();
				winX = position.left;
				winY = position.top;
				mouseX = event.pageX;
				mouseY = event.pageY;
				resized = $(this).data('modal-resize');

				
				document.onselectstart = function () { return false; };

				$(document).bind('mousemove', resizeFunc);
			})
			root.mouseup(function()
			{
				$(document).unbind('mousemove', resizeFunc);

				
				document.onselectstart = null;
			});
		}

	
		win.mousedown(function()
		{
			$(this).putModalOnFront();
		});

		
		if (settings.draggable && title)
		{
			
			var moveFunc = function(event)
			{
				
				var width = win.outerWidth(),
					height = win.outerHeight();

				
				win.css({
					left: Math.max(0, Math.min(winX+(event.pageX-mouseX), $(root).width()-width))+'px',
					top: Math.max(0, Math.min(winY+(event.pageY-mouseY), $(root).height()-height))+'px'
				});
			};

			
			win.find('h1:first').mousedown(function(event)
			{
				
				var position = win.position();
				winX = position.left;
				winY = position.top;
				mouseX = event.pageX;
				mouseY = event.pageY;

				
				document.onselectstart = function () { return false; };

				$(document).bind('mousemove', moveFunc);
			})
			root.mouseup(function()
			{
				$(document).unbind('mousemove', moveFunc);

				
				document.onselectstart = null;
			});
		}

		
		if (settings.closeButton)
		{
//			$('<ul class="action-tabs"><li><a href="#" title="Close window"><img src="../images/icons/btn_close.png" width="32px" height="32px"></a></li></ul>')
//				.prependTo(win)
			$(".modal-header").find('a').click(function(event)
				{
					event.preventDefault();
					$(this).closest('.modal-dialog').closeModal1();
					//$('.bg').style.Add('display', 'none');
					//$(this).closest('.modal-dialog').closeModal();
				});
		}

		
		var buttonsFooter = false;
		$.each(settings.buttons, function(key, value)
		{
			
			if (!buttonsFooter)
			{
			    buttonsFooter = $('<div class="modal-footer align-' + settings.buttonsAlign + '"></div>').insertAfter(contentDiv);
			    //buttonsFooter = $('<div class="modal-footer align-' + settings.buttonsAlign + '"><div id="divInnerFooter"></div></div>').insertAfter(contentDiv);
			}
			else
			{
			
				buttonsFooter.append('&nbsp;');
			}
			//$('<button type="button" class="btn btn-primary">' + key + '</button>').appendTo($("#divInnerFooter")).click(function (event)
			$('<button type="button" class="btn btn-primary">'+key+'</button>').appendTo(buttonsFooter).click(function(event)
			{
				value.call(this, $(this).closest('.modal-dialog'), event);
			});
		});

		if (settings.onClose)
		{
			win.bind('closeModal', settings.onClose);
		}

		//win.applyTemplateSetup();

	
		if (!root.is(':visible'))
		{
			win.hide();
			root.fadeIn('normal', function()
			{
				win.show().centerModal();
			});
		}
		else
		{
			win.centerModal();
		}

		
		$.modal1.current = win;
		$.modal1.all = root.children('.modal-dialog');

	
		if (settings.onOpen)
		{
			settings.onOpen.call(win.get(0));
		}

	
		if (settings.url)
		{
			win.loadModalContent(settings.url, settings);
		}

		return win;
	};


	$.modal1.current = false;

	
	$.modal1.all = $();

	$.fn.modal1 = function(options)
	{
		var modals = $();

		this.each(function()
		{
			modals.add($.modal1($.extend({}, $.modal1.defaults, {content: $(this).clone(true).show()})));
		});

		return modals;
	};

	$.fn.getModalContentBlock = function()
	{
		return this.find('.modal-body');
	}

	$.fn.getModalWindow = function()
	{
		return this.closest('.modal-dialog');
	}


	$.fn.setModalContent = function(content, resize)
	{
		this.each(function()
		{
			var contentBlock = $(this).getModalContentBlock();

			
			if (typeof(content) == 'string')
			{
				contentBlock.html(content);
			}
			else
			{
				content.clone(true).show().appendTo(contentBlock);
			}
			contentBlock.applyTemplateSetup();

			
			if (resize)
			{
				contentBlock.setModalContentSize(true, false);
			}
		});

		return this;
	}


	$.fn.setModalContentSize = function(width, height)
	{
		this.each(function()
		{
			var contentBlock = $(this).getModalContentBlock();

			
			if (width !== true)
			{
				contentBlock.css('width', width ? width+'px' : '');
			}
			if (height !== true)
			{
				contentBlock.css('height', height ? height+'px' : '');
			}
		});

		return this;
	}


	$.fn.loadModalContent = function(url, options)
	{
		var settings = $.extend({
			loadingMessage: '',
			data: {},
			complete: function(responseText, textStatus, XMLHttpRequest) {},
			resize: true,
			resizeOnMessage: false,
			resizeOnLoad: false
		}, options)

		this.each(function()
		{
			var win = $(this),
				contentBlock = win.getModalContentBlock();

			
			if (settings.loadingMessage)
			{
				win.setModalContent('<div class="modal-loading">'+settings.loadingMessage+'</div>', (settings.resize || settings.resizeOnMessage));
			}

			contentBlock.load(url, settings.data, function(responseText, textStatus, XMLHttpRequest)
			{
				
				contentBlock.applyTemplateSetup();

				if (settings.resize || settings.resizeOnLoad)
				{
					contentBlock.setModalContentSize(true, false);
				}

			
				settings.complete.call(this, responseText, textStatus, XMLHttpRequest);
			});
		});

		return this;
	}

	
	$.fn.setModalTitle = function(newTitle)
	{
		this.each(function()
		{
			var win = $(this),
				title = $(this).find('h1'),
				contentBlock = win.hasClass('modal-content') ? win : win.children('.modal-content:first');

			if (newTitle.length > 0)
			{
				if (title.length == 0)
				{
					contentBlock.removeClass('no-title');
					title = $('<h1>'+newTitle+'</h1>').prependTo(contentBlock);
				}

				title.html(newTitle);
			}
			else if (title.length > 0)
			{
				title.remove();
				contentBlock.addClass('no-title');
			}
		});

		return this;
	}

	
	$.fn.centerModal = function(animate)
	{
		var root = getModalDiv(),
			rootW = root.width()/2,
			rootH = root.height()/2;

		this.each(function()
		{
			var win = $(this),
				winW = Math.round(win.outerWidth()/2),
				winH = Math.round(win.outerHeight()/2);

			win[animate ? 'animate' : 'css']({
				left: (rootW-winW)+'px',
				top: (rootH-winH)+'px'
			});
		});

		return this;
	};

	
	$.fn.putModalOnFront = function()
	{
		if ($.modal1.all.length > 1)
		{
			var root = getModalDiv();
			this.each(function()
			{
				if ($(this).next('.modal-dialog').length > 0)
				{
					$(this).detach().appendTo(root);
				}
			});
		}

		return this;
	};

	
	$.fn.closeModal1 = function()
	{
		this.each(function()
		{
			var event = $.Event('closeModal'),
				win = $(this);

		
			win.trigger(event);
			if (!event.isDefaultPrevented())
			{
				win.remove();

			
				var root = getModalDiv();
				$.modal1.all = root.children('.modal-dialog');
				if ($.modal1.all.length == 0)
				{
					$.modal1.current = false;
					root.fadeOut('normal');
				}
				else
				{
				
					$.modal1.current = $.modal1.all.last();
				}
			}
		});

		return this;
	};

	
	$.modal1.defaults = {
	
		content: false,

	
		url: false,

	
		title: false,

	
		border: true,

		draggable: true,

	
		resizable: true,

		scrolling: true,

	
		closeButton: true,

	
		buttons: {},

		
		buttonsAlign: 'right',

		
		onOpen: false,

		
		onClose: false,

		minHeight: 40,

		
		minWidth: 200,

		
		maxHeight: false,

		
		maxWidth: false,

		
		height: false,

		
		width: 450,

		
		loadingMessage: 'Loading...',

		
		data: {},

		
		complete: function(responseText, textStatus, XMLHttpRequest) {},

		
		resize: true,

		
		resizeOnMessage: false,

	
		resizeOnLoad: false
	};

	
	function getModalDiv()
	{
		var modal1 = $('.modal1');
		if (modal1.length == 0)
		{
			$(document.body).append('<div class="modal1 bg" id="MainDiv"></div>');
			modal1 = $('.modal1').hide();
		}

		return modal1;
	};



})(jQuery);