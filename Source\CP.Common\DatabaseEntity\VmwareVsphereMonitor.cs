﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
//using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "VmwareVsphereMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]

   public class VmwareVsphereMonitor : BaseEntity
   {
       #region Properties

        [DataMember]
        public int id { get; set; }
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string PRReplicaVMName { get; set; }
        [DataMember]
        public string DRReplicaVMName { get; set; }
        [DataMember]
        public String PREsxiServerIP { get; set; }
        [DataMember]
        public string DREsxiServerIP { get; set; }
        [DataMember]
        public string PRTargetVsphereServer { get; set; }
        [DataMember]
        public string DRTargetVsphereServer { get; set; }
        [DataMember]
        public string PRReplicationStatus { get; set; }
        [DataMember]
        public string DRReplicationStatus { get; set; }
        [DataMember]
        public string PRRPO { get; set; }
        [DataMember]
        public string DRRPO { get; set; }
        [DataMember]
        public string PRLastSyncComptedTM { get; set; }
        [DataMember]
        public string DRLastSyncComptedTM { get; set; }

        [DataMember]
        public string PRLastSyncDuration { get; set; }
        [DataMember]
        public string DRLastSyncDuration { get; set; }
        [DataMember]
        public string PRLastSyncSize { get; set; }
        [DataMember]
        public string DRLastSyncSize { get; set; }
        [DataMember]
        public string DataLag { get; set; }


        public string InfraObjectName
        {
            get;
            set;
        }
        public string Server
        {
            get;
            set;
        }
       #endregion Properties

   }
}
