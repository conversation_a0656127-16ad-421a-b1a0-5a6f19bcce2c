﻿function BSHealthdash() {
    $.ajax({
        type: "POST",
        url: "BSHealthSummary.aspx/GetBSgraphData",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function (data) {
       
            // var data = '10,4,4,2'
            if (data.d != "") {
                var datajson = data.d.split(",");
                var chart = new CanvasJS.Chart("bsHealthdash", {
                    animationEnabled: true,

                    axisX: {
                        interval: 1,
                        gridColor: "#000",
                        color: "#fff"
                    },
                    axisY2: {
                        //interlacedColor: "rgba(1,77,101,.2)",
                        gridColor: "#fff",
                        lineThickness: 0,
                        lineColor: "#fff",
                        labelFontColor: "#fff",
                        tickColor: "#fff"
                        //title: "Number of Companies"
                    },
                    data: [{
                        type: "column",
                        //name: "companies",
                        //axisYType: "secondary",
                        //color: "#000",
                        dataPoints: [
                            { y: parseFloat(datajson[0]), label: "Total", color: "#007c97" },
                            { y: parseFloat(datajson[1]), label: "Production", color: "#b33a3a" },
                            { y: parseFloat(datajson[2]), label: "DR", color: "#2c8ad9" },
                            { y: parseFloat(datajson[3]), label: "Under Configuration", color: "#7d7b7b" },
                        ]
                    }]
                });
                chart.render();

            }
        }
    });
}