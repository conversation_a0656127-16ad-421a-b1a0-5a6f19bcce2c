using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.BusinessEntity;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Net;
using System.Web.Security;
using System.Web;
using CP.UI.Admin;
using log4net;

namespace CP.UI
{
    public partial class InfraObjectsConfiguration : InfraObjectsBasePage
    {
        public static string IPAddress = string.Empty;
        private readonly ILog _logger = LogManager.GetLogger(typeof(InfraObjectsConfiguration));
        private static IFacade _facade = new Facade();
        private static IList<InfraObjectsLuns> _InfraLuns = new List<InfraObjectsLuns>();
        private static IList<InfraobjectGlobalMirrorluns> _InfraGMluns = new List<InfraobjectGlobalMirrorluns>();
        private static IList<InfraobjectCGDetails> _InfraCGName = new List<InfraobjectCGDetails>();
        private static IList<InfraobjectVolumeDetails> _InfravmName_temp = new List<InfraobjectVolumeDetails>();
        private static IList<InfraobjectVolumeDetails> _InfravmName = new List<InfraobjectVolumeDetails>();
        private static int _currentLoggedUserId;
        private static string _currentLoggedUserName;
        private static int _companyId;
        private static int currentgroupId;
        private static int dbcount = 0;
        private static int PRservercount = 0;
        private static int drservercount = 0;
        private static int drsitecount = 0;
        private static int prsitecount = 0;
        private static bool _isUserSuperAdmin;
        private static bool _isParent;
        private static bool _isRac = false;
        public static bool isupdate = true;
        public string CurrentStep = "1";
        public string CompletedSteps = "0";
        public static string NodeList = "";

        public string LunsScript;

        public static IList<InfraobjectGlobalMirrorLunsDetails> luns = new List<InfraobjectGlobalMirrorLunsDetails>();
        public static IList<InfraobjectVolumeDetails> cgd = new List<InfraobjectVolumeDetails>();
        public string MessageInitials
        {
            get { return "Infra Object"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Group.InfraObjectsList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

            if (IsUserOperator || IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            if (IsUserCustom)
            {
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.FirstOrDefault(x => x.AccessMenuType == AccessManagerType.Configuration.ToString()
                        || (x.AccessMenuType == AccessManagerType.View.ToString() && x.AccessSubMenuType == 4));
                    if (ObjAccess == null)
                    {
                        Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                        return;
                    }
                }
                else
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }

            }

            Utility.SelectMenu(Master, "Module2");
            currentgroupId = CurrentInfraObjectId;
            _currentLoggedUserName = LoggedInUserName;
            _currentLoggedUserId = LoggedInUserId;
            _companyId = LoggedInUserCompanyId;
            _isUserSuperAdmin = IsUserSuperAdmin;
            _isParent = LoggedInUserCompany.IsParent;


            HttpContext.Current.Session["LoggedInUserId"] = LoggedInUserId;

            var appGroupList = Facade.GetBusinessServiceByCompanyIdAndRole(_currentLoggedUserId, _companyId,
                LoggedInUserRole, _isParent,
                LoggedInUser.InfraObjectAllFlag);
            ddlBusinessService.DataSource = appGroupList;
            ddlBusinessService.DataTextField = "Name";
            ddlBusinessService.DataValueField = "Id";
            ddlBusinessService.DataBind();
            ddlBusinessService.Items.Insert(0,
                new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessService, "000"));

            lblDrMonitorAppWorkflow.Visible = false;
            divDrMonitorWorkflow.Visible = false;

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 

            IList<InfraObject> Infra = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

            if (Infra != null)
            {
                /// Added by Vishal as per discussed with Kiran Sir for IDEA.
                var InfraDB = (from infra in Infra where infra.IsPair == 0 select infra).ToList();

                ddlPairinfraobjectid.DataSource = InfraDB;
                ddlPairinfraobjectid.DataTextField = "Name";
                ddlPairinfraobjectid.DataValueField = "Id";
                ddlPairinfraobjectid.DataBind();
                ddlPairinfraobjectid.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "000"));
            }
            else
                ddlPairinfraobjectid.Items.Insert(0, new ListItem("No Infra Object Assigned", "000"));

            Utility.PopulateBusinessType(ddlBusinessType, true);
            if (CurrentInfraObjectId > 0)
                PrepareEditView();
        }

        private void PrepareEditView()
        {
            BindControlValues();

            if (CurrentInfraObject.IsPRHostName == 1)
            {
                chkPRhostname.Checked = true;
            }
            else
            {
                chkPRhostname.Checked = false;
            }
            if (CurrentInfraObject.IsDRHostName == 1)
            {
                chkDRhostname.Checked = true;
            }
            else
            {
                chkDRhostname.Checked = false;
            }
            if (CurrentInfraObject.IsQueueMonitor == 1)
            {
                ChkIsQueueMonitor.Checked = true;
            }
            else
            {
                ChkIsQueueMonitor.Checked = false;
            }

            if (CurrentInfraObject.IsPair == 1)
            {
                ChkIspair.Checked = true;
            }
            else
            {
                ChkIspair.Checked = false;
            }

            if (CurrentInfraObject.IsAssociate == 1)
            {
                ChkIsAssociate.Checked = true;
            }
            else
            {
                ChkIsAssociate.Checked = false;
            }
            if (CurrentInfraObject.IsCluster == 1)
            {
                chkIsCluster.Checked = true;
                tdCluster.Visible = true;
                ddlCluster.SelectedValue = CurrentInfraObject.ClusterName;
            }
            else
            {
                chkIsCluster.Checked = false;
                tdCluster.Visible = false;
            }

            if (CurrentInfraObject.IsPRCloud == 1)
                chkPrCloud.Checked = true;
            else
                chkPrCloud.Checked = false;

            if (CurrentInfraObject.IsDRCloud == 1)
                chkDrCloud.Checked = true;
            else
                chkDrCloud.Checked = false;

        }

        private void BindControlValues()
        {
            Session["Infralunsdetails"] = null;
            isupdate = false;

            txtName.Text = CurrentInfraObject.Name;
            txtDescription.Text = CurrentInfraObject.Description;
            ddlBusinessService.SelectedValue = CurrentInfraObject.BusinessServiceId.ToString();

            if (ddlBusinessService.SelectedItem.Value != "000")
            {
                var bizSericeId = Convert.ToInt32(ddlBusinessService.SelectedItem.Value);
                Utility.PopulateBusinessFunction(ddlFunction, true, bizSericeId);

                if (ddlFunction.Items.Count > 0 && ddlFunction.Items[0].Value == "0")
                {
                    ddlFunction.Items[0].Value = "000";
                }
            }

            ddlFunction.SelectedValue = CurrentInfraObject.BusinessFunctionId.ToString();
            chkDRReady.Checked = CurrentInfraObject.DRReady;

            if (chkDRReady.Checked == false)
            {
                ddlReplicationType.Enabled = true;
                ddlServerDr.Visible = false;
                ddlDatabaseDr.Visible = false;
                ddlReplicationDr.Visible = false;
                trDr.Visible = false;
                //trPr.Style.Add("width", "80% !important");
                //trPr.Attributes.CssStyle.Add("width", "80% !important");
                trPr.Attributes.Add("style", "width:80%; !important");
            }
            else
            {
                ddlServerDr.Visible = true;
                ddlDatabaseDr.Visible = true;
                ddlReplicationDr.Visible = true;
                trDr.Visible = true;
                //trPr.Style.Add("width", "40% !important");
                trPr.Attributes.CssStyle.Add("width", "40% !important");
            }
            //txtCommand.Text = CurrentInfraObject.Command;trSummaryOutput
            //txtOutput.Text = CurrentInfraObject.Output;
            ddlPriority.SelectedValue = CurrentInfraObject.Priority.ToString();

            ddlPairinfraobjectid.SelectedValue = CurrentInfraObject.PairInfraObjectId.ToString();

            if (ChkIspair.Checked == false || ChkIsAssociate.Checked == false)
            {
                ddlPairinfraobjectid.Enabled = true;
            }
            else
            {
                ddlPairinfraobjectid.Enabled = false;
            }
            if (CurrentInfraObject.IsCluster == 1)
            {
                tdCluster.Visible = true;
                tdClusterPr.Visible = true;

                if (CurrentInfraObject.ClusterName.Equals("Veritas"))
                {
                    ddlCluster.SelectedValue = "1";
                }
                else if (CurrentInfraObject.ClusterName.Equals("HACMP"))
                {
                    ddlCluster.SelectedValue = "2";
                    tdClusterDr.Visible = true;
                }
                else
                {
                    ddlCluster.SelectedValue = "000";
                }

                ddlCluster_SelectedIndexChanged(null, null);

                ddlClusterPR.SelectedValue = !string.IsNullOrEmpty(Convert.ToString(CurrentInfraObject.ClusterPRServerId)) ? CurrentInfraObject.ClusterPRServerId.ToString() : "000";
                ddlClusterDR.SelectedValue = CurrentInfraObject.ClusterDRServerId.ToString();


            }
            else
            {
                tdCluster.Visible = false;
                tdClusterPr.Visible = false;
                tdClusterDr.Visible = false;
            }
            ddlBusinessType.SelectedValue = CurrentInfraObject.Type.ToString();
            BindRecoveryType();
            ddlSubBusinessType.SelectedValue = CurrentInfraObject.SubType.ToString();
            BindSubBuisnessType();

            ddlReplicationType.SelectedValue = CurrentInfraObject.RecoveryType.ToString();
            SetReplicationType();
            SelectedBusinessType();

            ddlServerPr.SelectedValue = CurrentInfraObject.PRServerId.ToString();
            ddlServerDr.SelectedValue = CurrentInfraObject.DRServerId.ToString();
            if (ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.CloudantDB).ToString())
            {
                // _facade.GetDatabaseBasesByTypeRoleAndCompanyId(DatabaseType.CloudantNoSQL, IsSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany.IsParent);

                Utility.PopulateDatabaseByDBType(ddlDatabasePr, "CloudantDB", "pr", true);
                Utility.PopulateDatabaseByDBType(ddlDatabaseDr, "CloudantDB", "dr", true);

            }
            else
            {
                Utility.PopulateDatabaseByServerID(ddlDatabasePr, CurrentInfraObject.PRServerId, "pr", true);
                Utility.PopulateDatabaseByServerID(ddlDatabaseDr, CurrentInfraObject.DRServerId, "dr", true);
            }
            ddlDatabasePr.SelectedValue = CurrentInfraObject.PRDatabaseId.ToString();
            ddlDatabaseDr.SelectedValue = CurrentInfraObject.DRDatabaseId.ToString();

            if (CurrentInfraObject.Type == 2)
            {
                if (CurrentInfraObject.RecoveryType != 107 && CurrentInfraObject.RecoveryType != 20)
                {
                    var prserverid1 = ddlServerPr.SelectedValue;
                    int prid1 = Convert.ToInt32(prserverid1);
                    var prdb1 = Facade.GetDatabaseBasesByServerId(prid1).FirstOrDefault();
                    Utility.PopulateDatabaseByServerID(ddlDatabasePr, CurrentInfraObject.PRServerId, prdb1.Type, true);

                    var drserverid1 = ddlServerDr.SelectedValue;
                    int drid1 = Convert.ToInt32(drserverid1);
                    var drdb1 = Facade.GetDatabaseBasesByServerId(drid1).FirstOrDefault();
                    Utility.PopulateDatabaseByServerID(ddlDatabaseDr, CurrentInfraObject.DRServerId, drdb1.Type, true);

                    //Utility.PopulateDatabaseByServerID(ddlDatabasePr, CurrentInfraObject.PRServerId, "pr", true);
                    //Utility.PopulateDatabaseByServerID(ddlDatabaseDr, CurrentInfraObject.DRServerId, "dr", true);

                    ddlDatabasePr.SelectedValue = CurrentInfraObject.PRDatabaseId.ToString();
                    ddlDatabaseDr.SelectedValue = CurrentInfraObject.DRDatabaseId.ToString();
                }
            }
            if (CurrentInfraObject.PRDatabaseId > 0 && CurrentInfraObject.DRDatabaseId > 0)
            {
                var prbd = _facade.GetDatabaseBaseById(CurrentInfraObject.PRDatabaseId);
                var drbd = _facade.GetDatabaseBaseById(CurrentInfraObject.DRDatabaseId);
                if (prbd != null && drbd != null)
                {
                    if (prbd.IsPartofRac && drbd.IsPartofRac)
                    {
                        btnRelateNodes.Visible = true;
                        BindDataBaseNodes(prbd.Id, "pr");
                        BindDataBaseNodes(drbd.Id, "dr");
                        btnRacNodeSummary.Visible = true;
                        btnSave.Value = "Update";
                        _isRac = true;
                        hdnCurrentObjID.Value = CurrentInfraObjectId.ToString();
                    }
                }
            }
            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.Application).ToString())
            {
                ddlMonitorWorkflow.SelectedValue = CurrentInfraObject.MonitoringWorkflow.ToString();
                chkDrMonitorApplication.Checked = CurrentInfraObject.DrMonitorApplicationCheck == 1 ? true : false;
                if (chkDrMonitorApplication.Checked)
                {
                    lblDrMonitorAppWorkflow.Visible = true;
                    divDrMonitorWorkflow.Visible = true;
                }
                ddlDRMonitorWorkflow.SelectedValue = CurrentInfraObject.DrMonitoringWorkflow.ToString();

                if ((ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.SRMVMware).ToString()) ||
                    (ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.vSphereReplication).ToString()))
                {
                    ddlServerPr2.SelectedValue = CurrentInfraObject.PRServerId2.ToString();
                    ddlServerDr2.SelectedValue = CurrentInfraObject.DRServerId2.ToString();

                }

            }


            var replicationtype = (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
            if (replicationtype == ReplicationType.OracleFullDBIBMGlobalMirror)
                replicationtype = ReplicationType.IBMGlobalMirror;
            if (replicationtype == ReplicationType.SqlServerDataSync)
                replicationtype = ReplicationType.DataSync;

            if (ddlReplicationType.SelectedItem.Value == "90" || ddlReplicationType.SelectedItem.Value == "91" || ddlReplicationType.SelectedItem.Value == "92")
            {
                var repListunsorted = Facade.GetEmcmirrorviewDetailsbyServerType123("PR");
                if (repListunsorted != null && repListunsorted.Count > 0)
                {
                    var repList = repListunsorted.OrderBy(o => o.Name).ToList();

                    ddlReplicationPr.DataSource = repList;
                    ddlReplicationPr.DataTextField = "Name";
                    ddlReplicationPr.DataValueField = "Id";
                    ddlReplicationPr.DataBind();
                    //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                    //ddlReplicationPr.SelectedValue = "0";
                    ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                    ddlReplicationPr.SelectedValue = CurrentInfraObject.PRReplicationId.ToString();


                }
                else
                {
                    //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                    //ddlReplicationPr.SelectedValue = "0";
                    ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                    // ddlReplicationPr.SelectedValue = "- Select Replication Name -";
                }


                var repListunsortedDR = Facade.GetEmcmirrorviewDetailsbyServerType123("DR");
                if (repListunsortedDR != null && repListunsortedDR.Count > 0)
                {
                    var repListDR = repListunsortedDR.OrderBy(o => o.Name).ToList();

                    ddlReplicationDr.DataSource = repListDR;
                    ddlReplicationDr.DataTextField = "Name";
                    ddlReplicationDr.DataValueField = "Id";
                    ddlReplicationDr.DataBind();
                    //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                    //ddlReplicationDr.SelectedValue = "0";
                    ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                    ddlReplicationDr.SelectedValue = CurrentInfraObject.DRReplicationId.ToString();
                }
                else
                {
                    //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -","0",true));
                    //ddlReplicationDr.SelectedValue = "0";
                    ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                    // ddlReplicationDr.SelectedValue = "- Select Replication Name -";
                }
            }

            else
            {
                Utility.PopulateReplicationBaseByType(ddlReplicationPr, replicationtype, true);
                Utility.PopulateReplicationBaseByType(ddlReplicationDr, replicationtype, true);
                ddlReplicationPr.SelectedValue = CurrentInfraObject.PRReplicationId.ToString();
                ddlReplicationDr.SelectedValue = CurrentInfraObject.DRReplicationId.ToString();
            }

            if (replicationtype == ReplicationType.MSSQLServerNative)
            {
                BindSqlNavtiveLogSummary();
            }
            if (replicationtype == ReplicationType.HITACHIUROracleFullDB || replicationtype == ReplicationType.HITACHIUROracleLogShipping || replicationtype == ReplicationType.HitachiOracleFullDBRac || replicationtype == ReplicationType.HitachiSyabse || replicationtype == ReplicationType.AppHitachiUr || replicationtype == ReplicationType.HitachiUrDB2FullDB || replicationtype == ReplicationType.HitachiUrMySqlFullDB)
            {
                BindArchiveRedoLogSummary();
            }
            if (replicationtype == ReplicationType.DB2HADR || replicationtype == ReplicationType.DB2HADR9X || replicationtype == ReplicationType.MySQLNative || replicationtype == ReplicationType.SAPHANADBReplication || replicationtype == ReplicationType.MariaDBGaleraCluster || replicationtype == ReplicationType.OracleCloudDataGuard)
            {
                trSummaryReplication.Visible = false;
            }
            if (replicationtype == ReplicationType.VMWareDataSync || replicationtype == ReplicationType.VMWareGlobalMirror || replicationtype == ReplicationType.VMWareHitachiUR || replicationtype == ReplicationType.VMWareSnapMirror || replicationtype == ReplicationType.MSExchangeDAG)
            {
                trSummaryDataBase.Visible = false;
            }
            if (replicationtype == ReplicationType.MSExchangeDAG)
            {
                PopulateExchangeDagMailbox(lstExcDAGPR, Convert.ToInt32(CurrentInfraObject.PRServerId));
                PopulateExchangeDagMailbox(lstSummaryExcDAGPR, Convert.ToInt32(CurrentInfraObject.PRServerId));
                PopulateExchangeDagMailbox(lstExcDAGDR, Convert.ToInt32(CurrentInfraObject.DRServerId));
                PopulateExchangeDagMailbox(lstSummaryExcDAGDR, Convert.ToInt32(CurrentInfraObject.DRServerId));
                trSummaryExchangeMailBoxDAG.Visible = true;
                tdReplicationD.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
                tdDrReplication.Visible = false;
                tdDataBaseComponant.Visible = false;
            }
            if (replicationtype == ReplicationType.VirtualeBDR)
            {
                tdDataBaseComponant.Visible = true;
                tdProductionDataBase.Visible = true;
                tdDrDatabase.Visible = true;
            }
            ddlReplicationType.Enabled = false;

            if (replicationtype == ReplicationType.EC2S3DataSync)
            {
                RequiredFieldValidator11.Enabled = false;
                RequiredFieldValidator13.Enabled = false;
            }

            if (replicationtype == ReplicationType.ApplicationDoubleTake)
            {
                divCommand.Visible = false;
                divOutPut.Visible = false;
            }
            //if (replicationtype == ReplicationType.MSSQLDataBaseMirror)
            //{
            //    RequiredFieldValidator10.Enabled = false;
            //    RequiredFieldValidator13.Enabled = false;
            //}
            if (replicationtype == ReplicationType.MariaDBGaleraCluster)
            {
                trSummaryReplication.Visible = false;
                ddlReplicationType.Enabled = false;
                RequiredFieldValidator10.Enabled = false;
                RequiredFieldValidator13.Enabled = false;
            }
            else
            {
                trSummaryReplication.Visible = true;
                trRecoveryType.Visible = true;
            }

            if (replicationtype == ReplicationType.SRMVMware)
            {
                pnlSummary.Visible = true;
                BindSummary();
            }

            if (replicationtype == ReplicationType.MariaDBGaleraCluster)
            {
                trSummaryReplication.Visible = false;
            }

            if (replicationtype == ReplicationType.Undefined)
            {
                ddlSubBusinessType.Enabled = false;
                pnlSummary.Visible = true;
                BindSummary();
                trSummaryReplication.Visible = false;
                trRecoveryType.Visible = false;
            }

            if (replicationtype == ReplicationType.GoldenGateRepli)
            {
                tdDataBaseComponant.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }
            if (replicationtype == ReplicationType.SQLNative2008)
            {
                divPRhostname.Visible = true;
                divDRhostname.Visible = true;
            }
            if (replicationtype == ReplicationType.NutanixLeapReplication)
            {
                pnlSummary.Visible = true;
                BindSummary();
            }
            if (replicationtype == ReplicationType.NutanixProtectionDomainReplication)
            {
                pnlSummary.Visible = true;
                BindSummary();
            }
            if (replicationtype == ReplicationType.REDHAT_Virtulization)
            {
                pnlSummary.Visible = true;
                trSummaryDataBase.Visible = false;
                BindSummary();
            }


            if (ddlDatabasePr.Items.Count > 0 && ddlDatabasePr.Items[0].Value == "0")
            {
                ddlDatabasePr.Items[0].Value = "000";
            }
            else if (ddlDatabasePr.Items.Count <= 0)
            {
                ddlDatabasePr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBName, "000"));
            }

            if (ddlDatabaseDr.Items.Count > 0 && ddlDatabaseDr.Items[0].Value == "0")
            {
                ddlDatabaseDr.Items[0].Value = "000";
            }
            else if (ddlDatabaseDr.Items.Count <= 0)
            {
                ddlDatabaseDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBName, "000"));
            }

        }

        protected void BindLunsListItemDataBound(object sender, ListViewItemEventArgs e)
        {



            if (CurrentInfraObjectId > 0)
            {
                if (_InfraLuns != null & _InfraLuns.Count > 0)
                {
                    var l = _InfraGMluns[e.Item.DataItemIndex];

                    //var ddlItem = e.Item.FindControl("ddl") as HtmlSelect;
                    DropDownList ddlFiltype = e.Item.FindControl("ddlFileType") as DropDownList;

                    TextBox txtMountPointName = e.Item.FindControl("txtMountPointName") as TextBox;

                    TextBox txtLVName = e.Item.FindControl("txtLVName") as TextBox;

                    TextBox txtVGName = e.Item.FindControl("txtVGName") as TextBox;

                    ddlFiltype.SelectedValue = l.FileTypeId.ToString();
                    txtMountPointName.Text = l.MountPointName.ToString();
                    txtLVName.Text = l.LVName.ToString();
                    txtVGName.Text = l.VGName.ToString();

                    IList<InfraobjectGlobalMirrorLunsDetails> list = _facade.GetInfraobjectGlobalMirrorLunsDetailsbyInfraLunsId(l.Id);
                    if (list != null)
                    {
                        foreach (var item in list)
                        {
                            luns.Add(item);
                        }
                    }
                    ListView childListv = e.Item.FindControl("lvmanual") as ListView;
                    childListv.DataSource = list;
                    childListv.DataBind();
                    //var txtgroupA = e.Item.FindControl("groupA") as HtmlInputText;
                    //var txtmountA = e.Item.FindControl("mountA") as HtmlInputText;

                    //var txtgroupB = e.Item.FindControl("groupB") as HtmlGenericControl;
                    //var txtmountB = e.Item.FindControl("mountB") as HtmlGenericControl;

                    //var txtgroupC = e.Item.FindControl("groupC") as HtmlGenericControl;
                    //var txtmountC = e.Item.FindControl("mountC") as HtmlGenericControl;

                    //var txtgroupD = e.Item.FindControl("groupD") as HtmlGenericControl;
                    //var txtmountD = e.Item.FindControl("mountD") as HtmlGenericControl;

                    //var txtgroupE = e.Item.FindControl("groupE") as HtmlGenericControl;
                    //var txtmountE = e.Item.FindControl("mountE") as HtmlGenericControl;

                    //var txtgroupF = e.Item.FindControl("groupF") as HtmlGenericControl;
                    //var txtmountF = e.Item.FindControl("mountF") as HtmlGenericControl;

                    //ddlItem.Value = l.Logs.ToString();

                    //txtgroupA.Value = l.AGroup;
                    //txtmountA.Value = l.AMount;

                    //txtgroupB.InnerHtml = l.AGroup;
                    //txtmountB.InnerHtml = l.AMount;

                    //txtgroupC.InnerHtml = l.AGroup;
                    //txtmountC.InnerHtml = l.AMount;

                    //txtgroupD.InnerHtml = l.AGroup;
                    //txtmountD.InnerHtml = l.AMount;

                    //txtgroupE.InnerHtml = l.AGroup;
                    //txtmountE.InnerHtml = l.AMount;

                    //txtgroupF.InnerHtml = l.AGroup;
                    //txtmountF.InnerHtml = l.AMount;
                }
            }


            // To get Infraobject Gloabal mirror luns details listview in insert mode.
            ListView objlvManual = (ListView)e.Item.FindControl("lvmanual");
            // ListView objlist = (ListView) Page.FindControl("lvmanual");

        }


        protected void DatabasePrSelectedIndexChanged(object sender, EventArgs e)
        {
            btnRelateNodes.Visible = false;
            var replicationtype = (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
            if (replicationtype == ReplicationType.MSSQLServerNative || replicationtype == ReplicationType.SqlServerDataSync)
            {
                if (ddlDatabasePr.SelectedItem != null)
                {
                    if (ddlDatabasePr.SelectedItem.Value != "000")
                    {
                        var nativedbpr = Facade.GetDatabaseSqlByDatabaseBaseId(Convert.ToInt32(ddlDatabasePr.SelectedItem.Value));
                        if (nativedbpr != null)
                        {
                            txtBackup.Text = nativedbpr.BackupRestorePath;
                            txtBackupNetwork.Text = nativedbpr.NetworkSharedPath;
                        }
                    }
                }
            }
            if (replicationtype == ReplicationType.SQLNative2008)
            {
                divPRhostname.Visible = true;
            }

            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.DB).ToString())
            {
                if (replicationtype == ReplicationType.OracleDataGuard || replicationtype == ReplicationType.HitachiOracleFullDBRac || replicationtype == ReplicationType.EMCSRDFOracleRacFullDB || replicationtype == ReplicationType.SVCGlobalMirrorOracleFullDBRac || replicationtype == ReplicationType.HP3PARORACLEFULLDB)
                {
                    if (ddlDatabasePr.SelectedItem != null && ddlDatabaseDr.SelectedItem != null)
                    {
                        if (ddlDatabasePr.SelectedItem.Value != "000" && ddlDatabaseDr.SelectedItem.Value != "000")
                        {
                            var dbpr = Facade.GetDatabaseBaseById(Convert.ToInt32(ddlDatabasePr.SelectedItem.Value));
                            var dbdr = Facade.GetDatabaseBaseById(Convert.ToInt32(ddlDatabaseDr.SelectedItem.Value));
                            if (dbpr.IsPartofRac && dbdr.IsPartofRac)
                            {
                                btnRelateNodes.Visible = true;
                                BindDataBaseNodes(dbpr.Id, "pr");
                                BindDataBaseNodes(dbdr.Id, "dr");
                                btnRacNodeSummary.Visible = true;
                                _isRac = true;
                            }
                        }
                    }
                }
            }
        }

        protected void DatabaseDrSelectedIndexChanged(object sender, EventArgs e)
        {
            btnRelateNodes.Visible = false;
            var replicationtype =
                (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
            if (replicationtype == ReplicationType.MSSQLServerNative || replicationtype == ReplicationType.SqlServerDataSync)
            {
                if (ddlDatabaseDr.SelectedItem != null)
                {
                    if (ddlDatabaseDr.SelectedItem.Value != "000")
                    {
                        var nativedbdr =
                            Facade.GetDatabaseSqlByDatabaseBaseId(Convert.ToInt32(ddlDatabaseDr.SelectedItem.Value));
                        if (nativedbdr != null)
                        {
                            txtRestore.Text = nativedbdr.BackupRestorePath;
                            txtRestoreNetwork.Text = nativedbdr.NetworkSharedPath;
                        }
                    }
                }
            }
            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.DB).ToString())
            {
                if (replicationtype == ReplicationType.OracleDataGuard || replicationtype == ReplicationType.HitachiOracleFullDBRac || replicationtype == ReplicationType.EMCSRDFOracleRacFullDB || replicationtype == ReplicationType.SVCGlobalMirrorOracleFullDBRac || replicationtype == ReplicationType.HP3PARORACLEFULLDB)
                {
                    if (ddlDatabasePr.SelectedItem != null && ddlDatabaseDr.SelectedItem != null)
                    {
                        if (ddlDatabasePr.SelectedItem.Value != "000" && ddlDatabaseDr.SelectedItem.Value != "000")
                        {
                            var dbpr = Facade.GetDatabaseBaseById(Convert.ToInt32(ddlDatabasePr.SelectedItem.Value));
                            var dbdr = Facade.GetDatabaseBaseById(Convert.ToInt32(ddlDatabaseDr.SelectedItem.Value));
                            if (dbpr.IsPartofRac && dbdr.IsPartofRac)
                            {
                                btnRelateNodes.Visible = true;
                                BindDataBaseNodes(dbpr.Id, "pr");
                                BindDataBaseNodes(dbdr.Id, "dr");
                                btnRacNodeSummary.Visible = true;
                                _isRac = true;
                            }
                        }
                    }
                }
            }
        }

        protected void BindDataBaseNodes(int dbid, string type)
        {
            switch (type)
            {
                case "pr":
                    var dbNodeListpr = _facade.GetAllDataBaseNodesByDatabaseId(dbid);
                    if (dbNodeListpr != null)
                    {
                        foreach (var database in dbNodeListpr)
                        {
                            var nodedetails = _facade.GetNodesById(database.NodeId);
                            if (nodedetails != null)
                            {
                                if (nodedetails.Id > 0)
                                {
                                    ddlNodePr.Items.Add(new ListItem(nodedetails.Name, nodedetails.Id.ToString()));
                                }
                            }
                        }
                    }
                    break;

                case "dr":
                    var dbNodeListdr = _facade.GetAllDataBaseNodesByDatabaseId(dbid);
                    if (dbNodeListdr != null)
                    {
                        foreach (var database in dbNodeListdr)
                        {
                            var nodedetails = _facade.GetNodesById(database.NodeId);
                            if (nodedetails != null)
                            {
                                if (nodedetails.Id > 0)
                                {
                                    ddlNodeDr.Items.Add(new ListItem(nodedetails.Name, nodedetails.Id.ToString()));
                                }
                            }
                        }
                    }
                    break;
            }
        }

        protected void OnSelectedIndexChangedBindFunctions(object sender, EventArgs e)
        {
            if (ddlBusinessService.SelectedItem != null)
            {
                if (ddlBusinessService.SelectedItem.Value != "000")
                {
                    var bizSericeId = Convert.ToInt32(ddlBusinessService.SelectedItem.Value);
                    Utility.PopulateBusinessFunction(ddlFunction, true, bizSericeId);

                    if (ddlFunction.Items.Count > 0 && ddlFunction.Items[0].Value == "0")
                    {
                        ddlFunction.Items[0].Value = "000";
                    }
                }
            }
        }

        protected void BusinessType_IndexChanged(object sender, EventArgs e)
        {
            BindRecoveryType();
        }

        protected void BusinessSubType_IndexChanged(object Sender, EventArgs e)
        {
            ddlServerDr.Items.Clear();
            ddlServerPr.Items.Clear();
            ddlServerDr2.Items.Clear();
            ddlServerPr2.Items.Clear();
            ddlReplicationDr.Items.Clear();
            ddlReplicationPr.Items.Clear();
            ddlDatabaseDr.Items.Clear();
            ddlDatabasePr.Items.Clear();

            BindSubBuisnessType();
            SelectedBusinessType();
            tdMailboxComponent.Visible = false;
            tdProductionExchaneDAGMailBox.Visible = false;
            tdDrExchaneDAGMailBox.Visible = false;

        }

        protected void ReplicationType_IndexChanged(object sender, EventArgs e)
        {
            //ddlServerDr.Items.Clear();
            //ddlServerPr.Items.Clear();
            //ddlServerDr2.Items.Clear();
            //ddlServerPr2.Items.Clear();
            //ddlReplicationDr.Items.Clear();
            //ddlReplicationPr.Items.Clear();
            //ddlDatabaseDr.Items.Clear();
            //ddlDatabasePr.Items.Clear();

            SetReplicationType();
        }

        protected void OnCheckedChangedEnableControls(object sender, EventArgs e)
        {
            ddlBusinessType.ClearSelection();
            ddlSubBusinessType.ClearSelection();
            ddlPriority.ClearSelection();
            ddlReplicationType.ClearSelection();
            ddlServerDr.ClearSelection();
            ddlDatabaseDr.ClearSelection();
            ddlReplicationDr.ClearSelection();
            ddlServerPr.ClearSelection();
            ddlDatabasePr.ClearSelection();
            ddlReplicationPr.ClearSelection();

            if (CurrentInfraObjectId <= 0)
            {
                if (chkDRReady.Checked)
                {
                    ddlReplicationType.Visible = true;
                    ddlReplicationType.Enabled = true;
                    trDr.Visible = true;
                    lblrecoverytype.Visible = true;
                    if (ddlSubBusinessType.SelectedValue == "15")
                    {
                        ddlReplicationType.Visible = false;
                        lblrecoverytype.Visible = false;
                    }
                }
                else
                {
                    trDr.Visible = false;
                    ddlReplicationType.Enabled = false;

                    //   ddlReplicationType.Visible = false;
                    //   lblrecoverytype.Visible = false;
                }
            }
        }

        protected void OnCheckedChangedEnableNearSite(object sender, EventArgs e)
        {
            // trNearDr.Visible = chkNearSite.Checked;
        }

        protected void OnDrMonitorCheckChange(object sender, EventArgs e)
        {

            if (chkDrMonitorApplication.Checked)
            {
                lblDrMonitorAppWorkflow.Visible = true;
                divDrMonitorWorkflow.Visible = true;
            }
            else
            {
                lblDrMonitorAppWorkflow.Visible = false;
                divDrMonitorWorkflow.Visible = false;
            }
        }

        protected void PrServer_SelectedIndexChanged(object sender, EventArgs e)
        {
            ddlDatabasePr.Items.Clear();
            if (ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.CloudantDB).ToString())
            {
                var listDB = new List<DatabaseBase>();
                List<string> list = new List<string>();
                IList<DatabaseBase> DbBase = _facade.GetAllDatabaseBases();
                DatabaseType DBType = DatabaseType.Undefined;
                String sidName = "";

                IList<DatabaseBase> lstDBbase = Facade.GetDatabaseBasesByType(DatabaseType.CloudantDB);
                if (lstDBbase != null)
                {

                    ddlDatabasePr.DataSource = lstDBbase.Where(x => x.Type == "PRDatabase").ToList();
                    ddlDatabasePr.DataTextField = "Name";
                    ddlDatabasePr.DataValueField = "Id";
                    ddlDatabasePr.DataBind();


                }
                //foreach (var dbDetails in DbBase)
                //{
                //    var groupDatabase = new DatabaseBase();
                // sidName = getDataBaseNameByType(dbDetails.DatabaseType, dbDetails.Id);
                //var Database = Facade.GetDatabaseBaseById(dbDetails.Id);
                //var db = Facade.GetDBNameById(dbDetails.DatabaseType, dbDetails.Id);
                //Database.Id = db.Id;
                //dbDetails.Name = db.Name;
                //if (dbDetails.Type == "PRDatabase" || )
                //{
                //    listDB.Add(dbDetails);
                //}


                // DBType = dbDetails.DatabaseType;


                //if (dbDetails.Type == "PRDatabase")
                //{
                //    Session["PrdbId"] = dbDetails.Id;
                //    list.Add(sidName);
                //    list.Remove("");
                //}




                // }
                //ddlDatabasePr.DataSource = listDB;
                //ddlDatabasePr.DataTextField = "Name";
                //ddlDatabasePr.DataValueField = "Id";


                ddlDatabasePr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBName, "000"));

            }
            else
            {
                var prserverid = ddlServerPr.SelectedItem.Value;
                Utility.PopulateDatabaseByServerID(ddlDatabasePr, Convert.ToInt32(prserverid),
                     PRDRDatabaseType.PRDatabase.ToString(), true);
                PopulateExchangeDagMailbox(lstExcDAGPR, Convert.ToInt32(prserverid));
                PopulateExchangeDagMailbox(lstSummaryExcDAGPR, Convert.ToInt32(prserverid));
            }


            if (ddlDatabasePr.Items.Count > 0 && ddlDatabasePr.Items[0].Value == "0")
            {
                ddlDatabasePr.Items[0].Value = "000";
            }
            else if (ddlDatabasePr.Items.Count <= 0)
            {
                ddlDatabasePr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBName, "000"));
            }

        }

        protected void DrServer_SelectedIndexChanged(object sender, EventArgs e)
        {
            ddlDatabaseDr.Items.Clear();
            if (ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.CloudantDB).ToString())
            {
                List<string> list = new List<string>();
                IList<DatabaseBase> DbBase = _facade.GetAllDatabaseBases();
                DatabaseType DBType = DatabaseType.Undefined;
                String sidName = "";
                IList<DatabaseBase> lstDBbase = Facade.GetDatabaseBasesByType(DatabaseType.CloudantDB);
                if (lstDBbase != null)
                {

                    ddlDatabaseDr.DataSource = lstDBbase.Where(x => x.Type == "DRDatabase").ToList();
                    ddlDatabaseDr.DataTextField = "Name";
                    ddlDatabaseDr.DataValueField = "Id";
                    ddlDatabaseDr.DataBind();


                }
                //foreach (var dbDetails in DbBase)
                //{

                //    DBType = dbDetails.DatabaseType;
                //    sidName = getDataBaseNameByType(DBType, dbDetails.Id);
                //    if (dbDetails.Type == "DRDatabase")
                //    {
                //        Session["DrdbId"] = dbDetails.Id;
                //        list.Add(sidName);

                //        list.Remove("");
                //    }


                //}

                //ddlDatabaseDr.DataSource = list;
                //ddlDatabaseDr.DataBind();
                ddlDatabaseDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBName, "000"));
            }
            else
            {
                var drserverid = ddlServerDr.SelectedItem.Value;
                Utility.PopulateDatabaseByServerID(ddlDatabaseDr, Convert.ToInt32(drserverid),
                    PRDRDatabaseType.DRDatabase.ToString(), true);

                PopulateExchangeDagMailbox(lstExcDAGDR, Convert.ToInt32(drserverid));
                PopulateExchangeDagMailbox(lstSummaryExcDAGDR, Convert.ToInt32(drserverid));
            }


            if (ddlDatabaseDr.Items.Count > 0 && ddlDatabaseDr.Items[0].Value == "0")
            {
                ddlDatabaseDr.Items[0].Value = "000";
            }
            else if (ddlDatabaseDr.Items.Count <= 0)
            {
                ddlDatabaseDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBName, "000"));
            }
        }

        public static void PopulateExchangeDagMailbox(ListControl lstExcDAG, int serverId)
        {
            lstExcDAG.Items.Clear();
            var exchangeDagMailboxDatabaseList = _facade.GetDatabaseExchangeDAGByServerId(serverId);
            if (exchangeDagMailboxDatabaseList != null)
            {
                lstExcDAG.DataSource = exchangeDagMailboxDatabaseList;
                lstExcDAG.DataTextField = "MailBoxDBName";
                lstExcDAG.DataValueField = "ID";
                lstExcDAG.DataBind();
            }
        }

        protected void ReplicationPr_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlReplicationType.SelectedItem.Value != "000")
            {
                var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                switch (replicationtype)
                {
                    case (int)(ReplicationType.IBMGlobalMirror):
                        if (ddlReplicationPr != null)
                            ddlReplicationDr.SelectItemByValue(ddlReplicationPr.SelectedItem.Value);
                        ddlReplicationDr.Attributes.Add("disabled", "true");
                        break;

                    case (int)ReplicationType.VMWareGlobalMirror:
                        if (ddlReplicationPr != null)
                            ddlReplicationDr.SelectItemByValue(ddlReplicationPr.SelectedItem.Value);
                        ddlReplicationDr.Attributes.Add("disabled", "true");
                        break;
                    case (int)(ReplicationType.OracleFullDBIBMGlobalMirror):
                        if (ddlReplicationPr != null)
                            ddlReplicationDr.SelectItemByValue(ddlReplicationPr.SelectedItem.Value);
                        // ddlReplicationDr.Attributes.Add("disabled", "true");
                        break;

                }
            }
        }

        protected void Wizard1NextButtonClick(object sender, WizardNavigationEventArgs e)
        {
            var li0 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li0");
            var li1 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li1");
            var li2 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li2");
            var li3 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li3");
            var li4 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li4");
            var li5 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li5");

            li0.Attributes.Add("class", "");
            li1.Attributes.Add("class", "");
            li2.Attributes.Add("class", "");
            li3.Attributes.Add("class", "");
            li4.Attributes.Add("class", "");
            li5.Attributes.Add("class", "");

            if (CurrentInfraObjectId != 0)
            {
                if (Wizard1.ActiveStepIndex == 0)
                {
                    Wizard1.ActiveStepIndex = 1;
                    if (li0 != null)
                    {
                        li0.Attributes.Add("class", "complete");
                    }

                    if (li1 != null)
                    {
                        li1.Attributes.Add("class", "active");
                    }
                    WizardStep2.StepType = WizardStepType.Step;
                    CurrentStep = (Convert.ToInt32(Wizard1.ActiveStepIndex) + 1).ToString();
                    CompletedSteps = (Convert.ToInt32(Wizard1.ActiveStepIndex + 1)).ToString();

                    var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));

                    if (replicationtype == 28)
                    {
                        divDRhostname.Visible = true;
                        divPRhostname.Visible = true;

                    }
                    else
                    {
                        divDRhostname.Visible = false;
                        divPRhostname.Visible = false;

                    }
                    if (replicationtype == 130)
                    {
                        tdReplicationComponant.Visible = false;
                        ddlReplicationPr.Visible = false;
                        ddlReplicationDr.Visible = false;
                        RequiredFieldValidator10.Visible = false;
                        RequiredFieldValidator13.Visible = false;

                    }

                    switch (replicationtype)
                    {
                        case (int)ReplicationType.DB2HADR:
                            tdReplicationComponant.Visible = false;
                            tdProductionReplication.Visible = false;
                            tdDrReplication.Visible = false;
                            break;

                        case (int)ReplicationType.OracleCloudDataGuard:
                            tdReplicationComponant.Visible = false;
                            tdProductionReplication.Visible = false;
                            tdDrReplication.Visible = false;
                            break;

                        case (int)ReplicationType.OC_ExaDB:
                            tdReplicationComponant.Visible = false;
                            tdProductionReplication.Visible = false;
                            tdDrReplication.Visible = false;
                            break;

                        case (int)ReplicationType.DB2HADR9X:
                            tdReplicationComponant.Visible = false;
                            tdProductionReplication.Visible = false;
                            tdDrReplication.Visible = false;
                            break;

                        case (int)ReplicationType.VMWareDataSync:
                            //case (int)ReplicationType.IBMGlobalMirror:
                            tdDataBaseComponant.Visible = false;
                            tdProductionDataBase.Visible = false;
                            tdDrDatabase.Visible = false;
                            break;

                        case (int)ReplicationType.SAPHANADBReplication:
                            tdReplicationComponant.Visible = false;
                            tdProductionReplication.Visible = false;
                            tdDrReplication.Visible = false;

                            break;



                        case (int)ReplicationType.TPCR:
                            li2.Visible = true;
                            li3.Visible = false;
                            li4.Visible = false;
                            li5.Visible = false;
                            tdProductionDataBase.Visible = false;
                            tdDrDatabase.Visible = false;
                            tdDataBaseComponant.Visible = false;
                            RequiredFieldValidator9.Enabled = false;
                            RequiredFieldValidator12.Enabled = false;
                            break;

                        case (int)ReplicationType.RoboCopy:
                            li2.Visible = true;
                            li3.Visible = false;
                            li4.Visible = false;
                            li5.Visible = false;
                            tdProductionDataBase.Visible = false;
                            tdDrDatabase.Visible = false;
                            tdDataBaseComponant.Visible = false;
                            RequiredFieldValidator9.Enabled = false;
                            RequiredFieldValidator12.Enabled = false;
                            break;

                        case (int)ReplicationType.RSync:
                            li2.Visible = true;
                            li3.Visible = false;
                            li4.Visible = false;
                            li5.Visible = false;
                            tdProductionDataBase.Visible = false;
                            tdDrDatabase.Visible = false;
                            tdDataBaseComponant.Visible = false;
                            RequiredFieldValidator9.Enabled = false;
                            RequiredFieldValidator12.Enabled = false;
                            break;

                        case (int)ReplicationType.SQLNative2008:
                            //li2.Visible = true;
                            //li3.Visible = false;
                            //li4.Visible = false;
                            //li5.Visible = false;
                            //tdProductionDataBase.Visible = true;
                            //tdDrDatabase.Visible = true;
                            //tdDataBaseComponant.Visible = false;
                            //RequiredFieldValidator9.Enabled = false;
                            //RequiredFieldValidator12.Enabled = false;
                            divDRhostname.Visible = true;
                            divPRhostname.Visible = true;
                            break;

                        case (int)ReplicationType.Zerto:
                            li2.Visible = true;
                            li3.Visible = false;
                            li4.Visible = false;
                            li5.Visible = false;
                            tdProductionDataBase.Visible = false;
                            tdDrDatabase.Visible = false;
                            tdDataBaseComponant.Visible = false;
                            RequiredFieldValidator9.Enabled = false;
                            RequiredFieldValidator12.Enabled = false;
                            break;


                        case (int)ReplicationType.EMCSRDFCGAPPLICATION:
                            li2.Visible = true;
                            li3.Visible = false;
                            li4.Visible = false;
                            li5.Visible = false;
                            tdProductionDataBase.Visible = false;
                            tdDrDatabase.Visible = false;
                            tdDataBaseComponant.Visible = false;
                            RequiredFieldValidator9.Enabled = false;
                            RequiredFieldValidator12.Enabled = false;
                            break;


                        case (int)ReplicationType.OracleFullDBIBMGlobalMirror:
                            li2.Visible = true;
                            li3.Visible = false;
                            li4.Visible = false;
                            li5.Visible = false;

                            break;


                        case (int)ReplicationType.AzureKubernetes:
                            //li2.Visible = true;
                            //li3.Visible = false;
                            //li4.Visible = false;
                            //li5.Visible = false;
                            //tdProductionDataBase.Visible = false;
                            //tdDrDatabase.Visible = false;
                            //tdDataBaseComponant.Visible = false;
                            //RequiredFieldValidator9.Enabled = false;
                            //RequiredFieldValidator12.Enabled = false;

                            divDRhostname.Visible = false;
                            divPRhostname.Visible = false;
                            break;


                        case (int)ReplicationType.AzureCosmosDB:
                            //li2.Visible = true;
                            //li3.Visible = false;
                            //li4.Visible = false;
                            //li5.Visible = false;
                            //tdProductionDataBase.Visible = true;
                            //tdDrDatabase.Visible = true;
                            //tdDataBaseComponant.Visible = false;
                            //RequiredFieldValidator9.Enabled = false;
                            //RequiredFieldValidator12.Enabled = false;
                            divDRhostname.Visible = false;
                            divPRhostname.Visible = false;
                            break;

                        case (int)ReplicationType.OpenShift:
                            tdDrReplication.Visible = false;
                            tdClusterServer.Visible = false;
                            tdDrDatabase.Visible = false;
                            SGrReplicationDr.Visible = false;
                            serverId.InnerText = "OpenShiftClusterServer";
                            drserverId.InnerText = "OpenShiftApiServer";
                            tdDrReplication.Style.Add("display", "none");
                            tdProductionReplication.Style.Add("border", "0px");
                            break;

                    }
                }
                else if (Wizard1.ActiveStepIndex == 1)
                {
                    if (li0 != null)
                    {
                        li0.Attributes.Add("class", "complete");
                    }
                    if (li1 != null)
                    {
                        li1.Attributes.Add("class", "complete");
                    }
                    pnlLunsSummary.Visible = false;
                    pnlNativeLogsSummary.Visible = false;
                    pnlArchiveRedo.Visible = false;

                    Wizard1.ActiveStepIndex = 2;
                    WizardStep3.StepType = WizardStepType.Finish;
                    CurrentStep = (Convert.ToInt32(Wizard1.ActiveStepIndex) + 1).ToString();
                    CompletedSteps = (Convert.ToInt32(Wizard1.ActiveStepIndex + 1)).ToString();

                    Wizard1.ActiveStepIndex = 2;
                    WizardStep3.StepType = WizardStepType.Finish;
                    if (ddlReplicationType.SelectedItem.Value != "000")
                    {
                        var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                        replicationtype = (replicationtype == 31) ? 1 : replicationtype;

                        switch (replicationtype)
                        {
                            case (int)ReplicationType.DB2IBMGLobalmirror:
                            case (int)ReplicationType.IBMGlobalMirror:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlLunsSummary.Visible = true;
                                lvLunsList.Visible = true;
                                if (CurrentInfraObjectId > 0)
                                {
                                    //_InfraLuns = _facade.GetInfraObjectsLunsById(CurrentInfraObjectId);
                                    _InfraGMluns = _facade.GetInfraobjectGlobalMirrorLunsbyInfraobjectId(CurrentInfraObjectId);
                                    if (_InfraGMluns != null)
                                    {
                                        lvLunsList.DataSource = _InfraGMluns;
                                        lvLunsList.DataBind();
                                    }
                                    else
                                    {
                                        int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                        GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                        if (gm != null)
                                        {
                                            var luns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                            if (luns != null)
                                            {
                                                lvLunsList.DataSource = luns;
                                                lvLunsList.DataBind();
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                    GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                    if (gm != null)
                                    {
                                        var allluns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                        if (allluns != null)
                                        {
                                            lvLunsList.DataSource = allluns;
                                            lvLunsList.DataBind();
                                        }
                                    }
                                }
                                break;

                            case (int)ReplicationType.MySQLGlobalMirrorFullDB:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlLunsSummary.Visible = true;
                                lvLunsList.Visible = true;
                                if (CurrentInfraObjectId > 0)
                                {
                                    //_InfraLuns = _facade.GetInfraObjectsLunsById(CurrentInfraObjectId);--->commented

                                    //added on 27 NOV 2024
                                    _InfraGMluns = _facade.GetInfraobjectGlobalMirrorLunsbyInfraobjectId(CurrentInfraObjectId);
                                    if (_InfraLuns != null)
                                    {
                                        lvLunsList.DataSource = _InfraLuns;
                                        lvLunsList.DataBind();
                                    }
                                    else
                                    {
                                        int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                        GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                        if (gm != null)
                                        {
                                            var luns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                            if (luns != null)
                                            {
                                                lvLunsList.DataSource = luns;
                                                lvLunsList.DataBind();
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                    GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                    if (gm != null)
                                    {
                                        var allluns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                        if (allluns != null)
                                        {
                                            lvLunsList.DataSource = allluns;
                                            lvLunsList.DataBind();
                                        }
                                    }
                                }


                                break;


                            case (int)ReplicationType.ApplicationSolutionSVC:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.OracleDataGuard:
                            case (int)ReplicationType.AvamarReplication:
                            case (int)ReplicationType.AzureGatewayReplication:
                            case (int)ReplicationType.OracleCloudReplication:
                            case (int)ReplicationType.RackWareReplication:
                            case (int)ReplicationType.DellEMCCyberRecoverVaultReplication:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MysqlFullDBSVC:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.DataSync:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MSSQLServerNative:

                                if (li4 != null)
                                {
                                    li4.Attributes.Add("class", "active");
                                }
                                pnlNativeLogsSummary.Visible = true;
                                break;

                            case (int)ReplicationType.MSSCR:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.NetAppSnapMirror:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSRDF:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSRDFSGDB2:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;



                            case (int)ReplicationType.HITACHITrueCopy:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlLunsSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HITACHIUROracleFullDB:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlArchiveRedo.Visible = true;
                                break;

                            case (int)ReplicationType.VMWareGlobalMirror:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlLunsSummary.Visible = true;
                                lvLunsList.Visible = true;
                                if (CurrentInfraObjectId > 0)
                                {
                                    _InfraLuns = _facade.GetInfraObjectsLunsById(CurrentInfraObjectId);
                                    if (_InfraLuns != null)
                                    {
                                        lvLunsList.DataSource = _InfraLuns;
                                        lvLunsList.DataBind();
                                    }
                                    else
                                    {
                                        int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                        GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                        if (gm != null)
                                        {
                                            var luns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                            if (luns != null)
                                            {
                                                lvLunsList.DataSource = luns;
                                                lvLunsList.DataBind();
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                    GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                    if (gm != null)
                                    {
                                        var allluns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                        if (allluns != null)
                                        {
                                            lvLunsList.DataSource = allluns;
                                            lvLunsList.DataBind();
                                        }
                                    }
                                }
                                break;

                            case (int)ReplicationType.VMWareDataSync:
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.VMWareHitachiUR:
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.VMWareSnapMirror:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SqlServerDataSync:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                //pnlNativeLogsSummary.Visible = true;
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EnterPriseDB:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                //pnlNativeLogsSummary.Visible = true;
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.Postgres9X:
                            case (int)ReplicationType.Postgres10:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                //pnlNativeLogsSummary.Visible = true;
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.OracleWithDataSync:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.DB2HADR:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.OracleCloudDataGuard:
                            case (int)ReplicationType.OC_ExaDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HITACHIUROracleLogShipping:

                                if (li5 != null)
                                {
                                    li5.Attributes.Add("class", "active");
                                }
                                pnlArchiveRedo.Visible = true;
                                break;

                            case (int)ReplicationType.EMCSRDFOracleFullDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSRDFMSSQLFullDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.RPForVMWithMSSQLFullDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.ZertoMssqlFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSRDFOracleLogShipping:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MSExchangeDAG:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.DB2HADR9X:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MySQLNative:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SQLNative2008:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                divPRhostname.Visible = true;
                                divDRhostname.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EC2S3DataSync:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                RequiredFieldValidator11.Enabled = false;
                                RequiredFieldValidator13.Enabled = false;
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MSSqlNetAppSnapMirror:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MSSQLDoubleTakeFullDB:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                //pnlNativeLogsSummary.Visible = true;
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.OracleFullDBNetAppSnapMirror:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            //case (int)ReplicationType.MSSQLDataBaseMirror:

                            //    if (li2 != null)
                            //    {
                            //        li2.Attributes.Add("class", "active");
                            //    }
                            //    pnlSummary.Visible = true;
                            //    BindSummary();
                            //    break;

                            case (int)ReplicationType.EMCSRDFVMAXOracleFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;
                            case (int)ReplicationType.EMCSRDFDMXOracleFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSRDFVMAXMSSQLFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;
                            case (int)ReplicationType.EMCSRDFDMXMSSQLFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SyBaseWithDataSync:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.ApplicationeBDR:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SybaseWithSRS:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;
                            case (int)ReplicationType.ApplicationDoubleTake:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MIMIX:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HitachiOracleFullDBRac:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlArchiveRedo.Visible = true;
                                break;

                            case (int)ReplicationType.HitachiUrDB2FullDB:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlArchiveRedo.Visible = true;
                                break;

                            case (int)ReplicationType.HitachiUrMySqlFullDB:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlArchiveRedo.Visible = true;
                                break;

                            case (int)ReplicationType.EMCSRDFOracleRacFullDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSRDFSyBase:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HitachiSyabse:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlArchiveRedo.Visible = true;
                                break;

                            case (int)ReplicationType.HyperV:

                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.AppHitachiUr:

                                //if (li2 != null)
                                //{
                                //    li2.Attributes.Add("class", "active");
                                //}
                                //pnlSummary.Visible = true;
                                //BindSummary();
                                //break;
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlArchiveRedo.Visible = true;
                                break;

                            case (int)ReplicationType.MySqlNativeLogShipping:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.CloudantDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.GoldenGateRepli:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SVCGlobalMirrorORMetroMirror:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SVCGlobalMirrorOracleFullDBRac:
                            case (int)ReplicationType.OracleFullDBSVCGlobalMirror:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();

                                break;

                            case (int)ReplicationType.MSSQLDBMirroring:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SVCMSSQLFullDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();

                                break;


                            case (int)ReplicationType.VMWareWithSVC:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HitachiURMSSQLFullDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.GlobalMirrorMSSQLFullDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SRMVMware:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.DB2IBMXIVMIRROR:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlCgSummary.Visible = true;
                                lvCGlist.Visible = true;
                                if (CurrentInfraObjectId > 0)
                                {
                                    //_InfraCGName = _facade.GetInfraobjectCGNamebyInfraobjectId(CurrentInfraObjectId);
                                    _InfraCGName = _facade.GetCGDetailsByBaseReplicationId(CurrentInfraObject.PRReplicationId);

                                    foreach (var cg in _InfraCGName)
                                    {
                                        _InfravmName_temp = _facade.GetInfraobjectVolumeDetailsbyCGId(cg.Id);
                                        if (_InfravmName_temp != null)
                                        {
                                            if (_InfravmName_temp.Count > 0)
                                            {
                                                foreach (var gm in _InfravmName_temp)
                                                {
                                                    _InfravmName.Add(gm);
                                                }

                                            }
                                        }
                                    }
                                    if (_InfraCGName != null)
                                    {
                                        lvCGlist.DataSource = _InfraCGName;
                                        lvCGlist.DataBind();


                                    }
                                    else
                                    {
                                        int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                        XIVConfiguration gm = _facade.GetIBMXIVMirrorByReplicationId(replicationId);
                                        if (gm != null)
                                        {
                                            var luns = _facade.GetCGDetailsByXIVId(gm.Id);
                                            if (luns != null)
                                            {
                                                lvCGlist.DataSource = luns;
                                                lvCGlist.DataBind();
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                    XIVConfiguration gm = _facade.GetIBMXIVMirrorByReplicationId(replicationId);
                                    if (gm != null)
                                    {
                                        var allluns = _facade.GetCGDetailsByXIVId(gm.Id);
                                        if (allluns != null)
                                        {
                                            lvCGlist.DataSource = allluns;
                                            lvCGlist.DataBind();
                                        }
                                    }
                                }

                                break;

                            case (int)ReplicationType.NetAppSnapMirrorPostgresFullDB:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                //pnlNativeLogsSummary.Visible = true;
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.ApplicationXIVMIRROR:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlCgSummary.Visible = true;
                                lvCGlist.Visible = true;
                                if (CurrentInfraObjectId > 0)
                                {
                                    //_InfraCGName = _facade.GetInfraobjectCGNamebyInfraobjectId(CurrentInfraObjectId);
                                    _InfraCGName = _facade.GetCGDetailsByBaseReplicationId(CurrentInfraObject.PRReplicationId);

                                    foreach (var cg in _InfraCGName)
                                    {
                                        _InfravmName_temp = _facade.GetInfraobjectVolumeDetailsbyCGId(cg.Id);
                                        if (_InfravmName_temp != null)
                                        {
                                            if (_InfravmName_temp.Count > 0)
                                            {
                                                foreach (var gm in _InfravmName_temp)
                                                {
                                                    _InfravmName.Add(gm);
                                                }

                                            }
                                        }
                                    }
                                    if (_InfraCGName != null)
                                    {
                                        lvCGlist.DataSource = _InfraCGName;
                                        lvCGlist.DataBind();


                                    }
                                    else
                                    {
                                        int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                        XIVConfiguration gm = _facade.GetIBMXIVMirrorByReplicationId(replicationId);
                                        if (gm != null)
                                        {
                                            var luns = _facade.GetCGDetailsByXIVId(gm.Id);
                                            if (luns != null)
                                            {
                                                lvCGlist.DataSource = luns;
                                                lvCGlist.DataBind();
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                    XIVConfiguration gm = _facade.GetIBMXIVMirrorByReplicationId(replicationId);
                                    if (gm != null)
                                    {
                                        var allluns = _facade.GetCGDetailsByXIVId(gm.Id);
                                        if (allluns != null)
                                        {
                                            lvCGlist.DataSource = allluns;
                                            lvCGlist.DataBind();
                                        }
                                    }
                                }

                                break;

                            case (int)ReplicationType.EMCSRDFMysqlFullDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MaxDBWithDataSync:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.DRNET:
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.TPCR:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MSSQLAlwaysOn:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MongoDB:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                //pnlNativeLogsSummary.Visible = true;
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.RecoverPoint:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.RecoverPointOracleFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.RecoverPointMSSQLFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.RecoverPointMYSQLFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.DB2FullDBEMCRecoveryPoint:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HP3ParwithPostgressMSSQL:
                            case (int)ReplicationType.HP3ParwithApplication:
                            case (int)ReplicationType.HP3ParwithMongoFullDB:
                            case (int)ReplicationType.HP3PARMSSQLFULLDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MSSQLFullDBVVRReplication:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HP3ParwithESXI:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSRDFSGORACLEFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HP3PARORACLEFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.EMCSRDFSGAPPLICATION:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.EMCSRDFSTARORACLEFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.EMCSRDFSTARAPPLICATION:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSRDFMSQLFULLDBSG:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.ZertoOracleFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.EMCSRDFMSQLFULLDBSTAR:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.ZFSOracleFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.ZFSApplication:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.ZFSWithDB2:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.ZFSMaxFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCISilon:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EmcMirrorViewOracleFullDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EmcMirrorViewSybaseFullDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EmcMirrorViewApp:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.RoboCopy:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SybaseWithRSHADR:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EmcUnityApp:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.VeeamReplication:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.DB2FullDBSVC:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();

                                break;

                            case (int)ReplicationType.PostgressFullDBSVC:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();

                                break;

                            case (int)ReplicationType.EMCSRDFDB2FullDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSTARDB2FullDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.EMCSTARMYSQLFullDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.SAPHANADBReplication:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.VirtualeBDR:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.RSync:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.OracleWithRSync:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HuaweiApplication:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.HuaweiWithPostgresClusterFullDB:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            //case (int)ReplicationType.EMCSRDFSTARORACLEFULLDB:

                            //    if (li2 != null)
                            //    {
                            //        li2.Attributes.Add("class", "active");
                            //    }
                            //    pnlSummary.Visible = true;
                            //    BindSummary();
                            //    break;

                            //case (int)ReplicationType.EMCSRDFSTARAPPLICATION:

                            //    if (li2 != null)
                            //    {
                            //        li2.Attributes.Add("class", "active");
                            //    }
                            //    pnlSummary.Visible = true;
                            //    BindSummary();
                            //    break;

                            //case (int)ReplicationType.EMCSRDFMSQLFULLDBSG:

                            //    if (li2 != null)
                            //    {
                            //        li2.Attributes.Add("class", "active");
                            //    }
                            //    pnlSummary.Visible = true;
                            //    BindSummary();
                            //    break;

                            //case (int)ReplicationType.EMCSRDFMSQLFULLDBSTAR:

                            //    if (li2 != null)
                            //    {
                            //        li2.Attributes.Add("class", "active");
                            //    }
                            //    pnlSummary.Visible = true;
                            //    BindSummary();

                            case (int)ReplicationType.HP3PARMAXFULLDB:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.NutanixLeapReplication:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MySQLNetAppSnapMirror:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.ActiveDirectory:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.VmwareVsphereRepli:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.OceanstorMSSqlHuaweiDBReplication:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.NutanixProtectionDomainReplication:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.MariaDBGaleraCluster:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;
                            case (int)ReplicationType.RecoveryAzureSite:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.Zerto:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;




                            case (int)ReplicationType.EMCSRDFCGAPPLICATION:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.EMCSRDFCGORACLEFULLDB:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.RPForVMReplication:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.EMCDataDomainMTree:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.vSphereReplication:
                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.DB2FullDBMIMIX:

                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;


                            case (int)ReplicationType.AzureKubernetes:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.AzureCosmosDB:
                            case (int)ReplicationType.REDHAT_Virtulization:

                                if (li3 != null)
                                {
                                    li3.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                BindSummary();
                                break;

                            case (int)ReplicationType.OpenShift:
                                if (li2 != null)
                                {
                                    li2.Attributes.Add("class", "active");
                                }
                                pnlSummary.Visible = true;
                                tdDrDatabase.Visible = false;
                                SGrReplicationDr.Visible = false;
                                BindSummary();
                                break;



                        }
                    }
                    else
                    {
                        if (CurrentInfraObject.RecoveryType == 0)
                        {
                            pnlSummary.Visible = true;
                            BindSummary();
                        }

                    }
                }
            }
            else
            {
                Validate();
                if (!Page.IsValid)
                {
                    e.Cancel = true;
                }
                if (chkDRReady.Checked)
                {
                    Validate();
                    if (!Page.IsValid)
                    {
                        e.Cancel = true;
                    }
                    CurrentStep = (Convert.ToInt32(Wizard1.ActiveStepIndex) + 1).ToString();
                    CompletedSteps = (Convert.ToInt32(Wizard1.ActiveStepIndex + 1)).ToString();
                    if (Wizard1.ActiveStepIndex == 0)
                    {
                        Wizard1.ActiveStepIndex = 1;

                        if (li0 != null)
                        {
                            li0.Attributes.Add("class", "complete");
                        }

                        if (li1 != null)
                        {
                            li1.Attributes.Add("class", "active");
                        }
                        WizardStep2.StepType = WizardStepType.Step;

                        var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));

                        if (replicationtype == 28)
                        {
                            divDRhostname.Visible = true;
                            divPRhostname.Visible = true;

                        }
                        else
                        {
                            divDRhostname.Visible = false;
                            divPRhostname.Visible = false;

                        }
                        if (replicationtype == 130)
                        {
                            tdReplicationComponant.Visible = false;
                            ddlReplicationPr.Visible = false;
                            ddlReplicationDr.Visible = false;
                            RequiredFieldValidator10.Visible = false;
                            RequiredFieldValidator13.Visible = false;
                        }
                        if (replicationtype != 0)
                        {
                            //if (replicationtype == 25)
                            //{
                            //   // pnlSummary.Visible = true;
                            //    trSummaryReplication.Visible = false;
                            //   BindSummary();
                            //}
                            //else
                            //{
                            switch (replicationtype)
                            {
                                case (int)ReplicationType.DB2HADR:
                                    tdReplicationComponant.Visible = false;
                                    tdProductionReplication.Visible = false;
                                    tdDrReplication.Visible = false;
                                    break;

                                case (int)ReplicationType.OracleCloudDataGuard:
                                    tdReplicationComponant.Visible = false;
                                    tdProductionReplication.Visible = false;
                                    tdDrReplication.Visible = false;
                                    break;

                                case (int)ReplicationType.OC_ExaDB:
                                    tdReplicationComponant.Visible = false;
                                    tdProductionReplication.Visible = false;
                                    tdDrReplication.Visible = false;
                                    break;


                                case (int)ReplicationType.DB2HADR9X:
                                    tdReplicationComponant.Visible = false;
                                    tdProductionReplication.Visible = false;
                                    tdDrReplication.Visible = false;
                                    break;

                                case (int)ReplicationType.VMWareDataSync:
                                    tdDataBaseComponant.Visible = false;
                                    tdProductionDataBase.Visible = false;
                                    tdDrDatabase.Visible = false;
                                    break;

                                case (int)ReplicationType.SAPHANADBReplication:
                                    tdReplicationComponant.Visible = false;
                                    tdProductionReplication.Visible = false;
                                    tdDrReplication.Visible = false;
                                    break;
                                case (int)ReplicationType.VirtualeBDR:

                                    tdDataBaseComponant.Visible = true;
                                    tdProductionDataBase.Visible = true;
                                    tdDrDatabase.Visible = true;
                                    break;

                                case (int)ReplicationType.SQLNative2008:

                                    //tdProductionDataBase.Visible = true;
                                    //tdDrDatabase.Visible = true;
                                    //tdDataBaseComponant.Visible = false;
                                    //RequiredFieldValidator9.Enabled = false;
                                    //RequiredFieldValidator12.Enabled = false;
                                    //divDRhostname.Visible = true;
                                    //divPRhostname.Visible = true;

                                    tdProductionDataBase.Visible = true;
                                    tdDrDatabase.Visible = true;
                                    tdDataBaseComponant.Visible = true;
                                    //RequiredFieldValidator9.Enabled = false;
                                    //RequiredFieldValidator12.Enabled = false;
                                    divDRhostname.Visible = true;
                                    divPRhostname.Visible = true;
                                    break;

                                case (int)ReplicationType.MSSQLAlwaysOn:
                                    tdProductionDataBase.Visible = true;
                                    tdDrDatabase.Visible = true;
                                    tdDataBaseComponant.Visible = true;
                                    //RequiredFieldValidator9.Enabled = false;
                                    //RequiredFieldValidator12.Enabled = false;
                                    break;
                                case (int)ReplicationType.MSSQLDBMirroring:
                                    tdProductionDataBase.Visible = true;
                                    tdDrDatabase.Visible = true;
                                    tdDataBaseComponant.Visible = true;
                                    //RequiredFieldValidator9.Enabled = false;
                                    //RequiredFieldValidator12.Enabled = false;
                                    break;

                                case (int)ReplicationType.MongoDB:
                                    tdReplicationComponant.Visible = false;
                                    tdProductionReplication.Visible = false;
                                    tdDrReplication.Visible = false;
                                    break;

                                case (int)ReplicationType.EMCSRDFCGAPPLICATION:
                                    tdReplicationComponant.Visible = true;
                                    tdProductionReplication.Visible = true;
                                    tdDrReplication.Visible = true;
                                    break;

                                case (int)ReplicationType.OpenShift:
                                    tdDrReplication.Visible = false;
                                    tdClusterServer.Visible = false;
                                    tdDrDatabase.Visible = false;
                                    SGrReplicationDr.Visible = false;
                                    serverId.InnerText = "OpenShiftClusterServer";
                                    drserverId.InnerText = "OpenShiftApiServer";
                                    //tdDrReplication.Visible = false;
                                    tdDrReplication.Style.Add("display", "none");
                                    tdProductionReplication.Style.Add("border", "0px");
                                    break;


                            }
                            //  }
                        }
                        else
                        {
                            if (chkDRReady.Checked == false)
                            {
                                trDr.Visible = false;
                            }
                            else
                            {
                                trDr.Visible = true;
                            }
                            tdReplicationComponant.Visible = false;
                            tdProductionReplication.Visible = false;
                            //tdProductionDataBase.Visible = true; ;
                            tdDrDatabase.Visible = false;
                            tdDrReplication.Visible = false;

                            // tdDrReplication.Visible = false;
                        }
                    }
                    else if (Wizard1.ActiveStepIndex == 1)
                    {
                        if (li0 != null)
                        {
                            li0.Attributes.Add("class", "complete");
                        }
                        if (li1 != null)
                        {
                            li1.Attributes.Add("class", "complete");
                        }

                        pnlLunsSummary.Visible = false;
                        pnlNativeLogsSummary.Visible = false;
                        pnlArchiveRedo.Visible = false;

                        Wizard1.ActiveStepIndex = 2;
                        WizardStep3.StepType = WizardStepType.Finish;
                        if (ddlReplicationType.SelectedItem.Value == "000")
                        {
                            var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));

                            replicationtype = (replicationtype == 31) ? 1 : replicationtype;
                            trSummaryReplication.Visible = false;

                        }
                        if (ddlReplicationType.SelectedItem.Value != "000")
                        {
                            var replicationtype =
                               (int)(ReplicationType)
                                    (Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                            replicationtype = (replicationtype == 31) ? 1 : replicationtype;
                            switch (replicationtype)
                            {

                                case (int)ReplicationType.DB2IBMGLobalmirror:
                                case (int)ReplicationType.IBMGlobalMirror:

                                    //if (li3 != null)
                                    //{
                                    //    li3.Attributes.Add("class", "active");
                                    //}
                                    //pnlLunsSummary.Visible = true;
                                    //lvLunsList.Visible = true;
                                    //int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                    //GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                    //if (gm != null)
                                    //{
                                    //    var allluns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                    //    if (allluns != null)
                                    //    {
                                    //        lvLunsList.DataSource = allluns;
                                    //        lvLunsList.DataBind();
                                    //    }
                                    //}
                                    //break;
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlLunsSummary.Visible = true;
                                    lvLunsList.Visible = true;
                                    if (CurrentInfraObjectId > 0)
                                    {
                                        //_InfraLuns = _facade.GetInfraObjectsLunsById(CurrentInfraObjectId);
                                        _InfraGMluns = _facade.GetInfraobjectGlobalMirrorLunsbyInfraobjectId(CurrentInfraObjectId);
                                        if (_InfraGMluns != null)
                                        {
                                            lvLunsList.DataSource = _InfraGMluns;
                                            lvLunsList.DataBind();
                                        }
                                        else
                                        {
                                            int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                            GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                            if (gm != null)
                                            {
                                                var luns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                                if (luns != null)
                                                {
                                                    lvLunsList.DataSource = luns;
                                                    lvLunsList.DataBind();
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                        GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);
                                        if (gm != null)
                                        {
                                            var allluns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
                                            if (allluns != null)
                                            {
                                                lvLunsList.DataSource = allluns;
                                                lvLunsList.DataBind();
                                            }
                                        }
                                    }
                                    break;


                                case (int)ReplicationType.MySQLGlobalMirrorFullDB:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlLunsSummary.Visible = true;
                                    lvLunsList.Visible = true;
                                    int replicationId1 = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                    GlobalMirror gmmq = _facade.GetGlobalMirrorByReplicationId(replicationId1);
                                    if (gmmq != null)
                                    {
                                        var allluns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gmmq.Id);
                                        if (allluns != null)
                                        {
                                            lvLunsList.DataSource = allluns;
                                            lvLunsList.DataBind();
                                        }
                                    }
                                    break;

                                case (int)ReplicationType.ApplicationSolutionSVC:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.OracleDataGuard:
                                case (int)ReplicationType.AvamarReplication:
                                case (int)ReplicationType.AzureGatewayReplication:
                                case (int)ReplicationType.OracleCloudReplication:
                                case (int)ReplicationType.RackWareReplication:
                                case (int)ReplicationType.DellEMCCyberRecoverVaultReplication:
                                
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MysqlFullDBSVC:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.DataSync:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MSSQLServerNative:

                                    if (li4 != null)
                                    {
                                        li4.Attributes.Add("class", "active");
                                    }
                                    pnlNativeLogsSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MSSCR:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.NetAppSnapMirror:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDF:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFSGDB2:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HITACHITrueCopy:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlLunsSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HITACHIUROracleFullDB:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlArchiveRedo.Visible = true;
                                    break;

                                case (int)ReplicationType.SAPHANADBReplication:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.VMWareGlobalMirror:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlLunsSummary.Visible = true;
                                    lvLunsList.Visible = true;
                                    int vmreplicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                    GlobalMirror vmgm = _facade.GetGlobalMirrorByReplicationId(vmreplicationId);
                                    if (vmgm != null)
                                    {
                                        var allluns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(vmgm.Id);
                                        if (allluns != null)
                                        {
                                            lvLunsList.DataSource = allluns;
                                            lvLunsList.DataBind();
                                        }
                                    }
                                    break;

                                case (int)ReplicationType.VMWareDataSync:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.VMWareHitachiUR:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.VMWareSnapMirror:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.SqlServerDataSync:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    //pnlNativeLogsSummary.Visible = true;
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EnterPriseDB:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    //pnlNativeLogsSummary.Visible = true;
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;



                                case (int)ReplicationType.Postgres9X:
                                case (int)ReplicationType.Postgres10:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    //pnlNativeLogsSummary.Visible = true;
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;
                                case (int)ReplicationType.OracleWithDataSync:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.DB2HADR:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.OracleCloudDataGuard:
                                case (int)ReplicationType.OC_ExaDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HITACHIUROracleLogShipping:

                                    if (li5 != null)
                                    {
                                        li5.Attributes.Add("class", "active");
                                    }
                                    pnlArchiveRedo.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFOracleFullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;
                                case (int)ReplicationType.EMCSRDFMSSQLFullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.RPForVMWithMSSQLFullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.ZertoMssqlFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.EMCSRDFOracleLogShipping:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MSExchangeDAG:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.DB2DataSync:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.DB2HADR9X:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MySQLNative:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.SQLNative2008:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EC2S3DataSync:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    RequiredFieldValidator11.Enabled = false;
                                    RequiredFieldValidator13.Enabled = false;
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.Undefined:
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MSSqlNetAppSnapMirror:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MSSQLDoubleTakeFullDB:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    //pnlNativeLogsSummary.Visible = true;
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.OracleFullDBNetAppSnapMirror:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                //case (int)ReplicationType.MSSQLDataBaseMirror:

                                //    if (li2 != null)
                                //    {
                                //        li2.Attributes.Add("class", "active");
                                //    }
                                //    pnlSummary.Visible = true;
                                //    BindSummary();
                                //    break;

                                case (int)ReplicationType.EMCSRDFVMAXOracleFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;
                                case (int)ReplicationType.EMCSRDFDMXOracleFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFVMAXMSSQLFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;
                                case (int)ReplicationType.EMCSRDFDMXMSSQLFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.SyBaseWithDataSync:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.ApplicationeBDR:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.SybaseWithSRS:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;
                                case (int)ReplicationType.ApplicationDoubleTake:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MIMIX:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HitachiOracleFullDBRac:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlArchiveRedo.Visible = true;
                                    break;

                                case (int)ReplicationType.HitachiUrDB2FullDB:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlArchiveRedo.Visible = true;
                                    break;

                                case (int)ReplicationType.HitachiUrMySqlFullDB:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlArchiveRedo.Visible = true;
                                    break;

                                case (int)ReplicationType.EMCSRDFOracleRacFullDB:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFSyBase:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HitachiSyabse:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlArchiveRedo.Visible = true;
                                    break;

                                case (int)ReplicationType.HyperV:
                                    //Pranjali Added
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.AppHitachiUr:

                                    //if (li2 != null)
                                    //{
                                    //    li2.Attributes.Add("class", "active");
                                    //}
                                    //pnlSummary.Visible = true;
                                    //BindSummary();
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlArchiveRedo.Visible = true;
                                    break;
                                case (int)ReplicationType.MySqlNativeLogShipping:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.CloudantDB:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.GoldenGateRepli:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.SVCGlobalMirrorORMetroMirror:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.SVCGlobalMirrorOracleFullDBRac:
                                case (int)ReplicationType.OracleFullDBSVCGlobalMirror:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();

                                    break;

                                case (int)ReplicationType.MSSQLDBMirroring:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.SVCMSSQLFullDB:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();

                                    break;

                                case (int)ReplicationType.VMWareWithSVC:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HitachiURMSSQLFullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.GlobalMirrorMSSQLFullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFCGAPPLICATION:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.DB2IBMXIVMIRROR:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlCgSummary.Visible = true;
                                    lvCGlist.Visible = true;
                                    if (CurrentInfraObjectId > 0)
                                    {
                                        //_InfraCGName = _facade.GetInfraobjectCGNamebyInfraobjectId(CurrentInfraObjectId);
                                        _InfraCGName = _facade.GetCGDetailsByBaseReplicationId(CurrentInfraObject.PRReplicationId);

                                        foreach (var cg in _InfraCGName)
                                        {
                                            _InfravmName_temp = _facade.GetInfraobjectVolumeDetailsbyCGId(cg.Id);
                                            if (_InfravmName_temp != null)
                                            {
                                                if (_InfravmName_temp.Count > 0)
                                                {
                                                    foreach (var gm in _InfravmName_temp)
                                                    {
                                                        _InfravmName.Add(gm);
                                                    }

                                                }
                                            }
                                        }
                                        if (_InfraCGName != null)
                                        {
                                            lvCGlist.DataSource = _InfraCGName;
                                            lvCGlist.DataBind();


                                        }
                                        else
                                        {
                                            int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                            XIVConfiguration gm = _facade.GetIBMXIVMirrorByReplicationId(replicationId);
                                            if (gm != null)
                                            {
                                                var luns = _facade.GetCGDetailsByXIVId(gm.Id);
                                                if (luns != null)
                                                {
                                                    lvCGlist.DataSource = luns;
                                                    lvCGlist.DataBind();
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                        XIVConfiguration gm = _facade.GetIBMXIVMirrorByReplicationId(replicationId);
                                        if (gm != null)
                                        {
                                            var allluns = _facade.GetCGDetailsByXIVId(gm.Id);
                                            if (allluns != null)
                                            {
                                                lvCGlist.DataSource = allluns;
                                                lvCGlist.DataBind();
                                            }
                                        }
                                    }

                                    break;

                                case (int)ReplicationType.NetAppSnapMirrorPostgresFullDB:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    //pnlNativeLogsSummary.Visible = true;
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.ApplicationXIVMIRROR:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlCgSummary.Visible = true;
                                    lvCGlist.Visible = true;
                                    if (CurrentInfraObjectId > 0)
                                    {
                                        //_InfraCGName = _facade.GetInfraobjectCGNamebyInfraobjectId(CurrentInfraObjectId);
                                        _InfraCGName = _facade.GetCGDetailsByBaseReplicationId(CurrentInfraObject.PRReplicationId);

                                        foreach (var cg in _InfraCGName)
                                        {
                                            _InfravmName_temp = _facade.GetInfraobjectVolumeDetailsbyCGId(cg.Id);
                                            if (_InfravmName_temp != null)
                                            {
                                                if (_InfravmName_temp.Count > 0)
                                                {
                                                    foreach (var gm in _InfravmName_temp)
                                                    {
                                                        _InfravmName.Add(gm);
                                                    }

                                                }
                                            }
                                        }
                                        if (_InfraCGName != null)
                                        {
                                            lvCGlist.DataSource = _InfraCGName;
                                            lvCGlist.DataBind();


                                        }
                                        else
                                        {
                                            int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                            XIVConfiguration gm = _facade.GetIBMXIVMirrorByReplicationId(replicationId);
                                            if (gm != null)
                                            {
                                                var luns = _facade.GetCGDetailsByXIVId(gm.Id);
                                                if (luns != null)
                                                {
                                                    lvCGlist.DataSource = luns;
                                                    lvCGlist.DataBind();
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        int replicationId = Convert.ToInt32(ddlReplicationPr.SelectedItem.Value);
                                        XIVConfiguration gm = _facade.GetIBMXIVMirrorByReplicationId(replicationId);
                                        if (gm != null)
                                        {
                                            var allluns = _facade.GetCGDetailsByXIVId(gm.Id);
                                            if (allluns != null)
                                            {
                                                lvCGlist.DataSource = allluns;
                                                lvCGlist.DataBind();
                                            }
                                        }
                                    }

                                    break;

                                case (int)ReplicationType.SRMVMware:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFMysqlFullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MaxDBWithDataSync:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.DRNET:
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.TPCR:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MSSQLAlwaysOn:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MongoDB:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    //pnlNativeLogsSummary.Visible = true;
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.RecoverPoint:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.RecoverPointOracleFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.RecoverPointMSSQLFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.RecoverPointMYSQLFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.DB2FullDBEMCRecoveryPoint:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HP3ParwithApplication:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HP3ParwithPostgressMSSQL:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HP3PARMSSQLFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HP3ParwithMongoFullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MSSQLFullDBVVRReplication:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HP3ParwithESXI:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFSGORACLEFULLDB:


                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HP3PARORACLEFULLDB:


                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFCGORACLEFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;




                                case (int)ReplicationType.EMCSRDFSGAPPLICATION:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFSTARORACLEFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFSTARAPPLICATION:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFMSQLFULLDBSG:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSRDFMSQLFULLDBSTAR:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.ZertoOracleFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.ZFSOracleFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.ZFSMaxFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.ZFSWithDB2:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.ZFSApplication:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.EMCISilon:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EmcMirrorViewOracleFullDB:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EmcMirrorViewSybaseFullDB:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EmcMirrorViewApp:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.RoboCopy:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.SybaseWithRSHADR:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EmcUnityApp:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.VeeamReplication:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.DB2FullDBSVC:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();

                                    break;

                                case (int)ReplicationType.PostgressFullDBSVC:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();

                                    break;

                                case (int)ReplicationType.EMCSRDFDB2FullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSTARDB2FullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCSTARMYSQLFullDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.VirtualeBDR:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.RSync:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.VVRApplicationReplication:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.OracleWithRSync:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HuaweiApplication:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.HuaweiWithPostgresClusterFullDB:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.NutanixLeapReplication:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;
                                case (int)ReplicationType.MySQLNetAppSnapMirror:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                //case (int)ReplicationType.EMCSRDFSGAPPLICATION:

                                //    if (li2 != null)
                                //    {
                                //        li2.Attributes.Add("class", "active");
                                //    }
                                //    pnlSummary.Visible = true;
                                //    BindSummary();
                                //    break;

                                //case (int)ReplicationType.EMCSRDFSTARORACLEFULLDB:

                                //    if (li2 != null)
                                //    {
                                //        li2.Attributes.Add("class", "active");
                                //    }
                                //    pnlSummary.Visible = true;
                                //    BindSummary();
                                //    break;

                                //case (int)ReplicationType.EMCSRDFSTARAPPLICATION:

                                //    if (li2 != null)
                                //    {
                                //        li2.Attributes.Add("class", "active");
                                //    }
                                //    pnlSummary.Visible = true;
                                //    BindSummary();
                                //    break;

                                //case (int)ReplicationType.EMCSRDFMSQLFULLDBSG:

                                //    if (li2 != null)
                                //    {
                                //        li2.Attributes.Add("class", "active");
                                //    }
                                //    pnlSummary.Visible = true;
                                //    BindSummary();
                                //    break;

                                //case (int)ReplicationType.EMCSRDFMSQLFULLDBSTAR:

                                //    if (li2 != null)
                                //    {
                                //        li2.Attributes.Add("class", "active");
                                //    }
                                //    pnlSummary.Visible = true;
                                //    BindSummary();
                                //    break;

                                case (int)ReplicationType.HP3PARMAXFULLDB:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;
                                case (int)ReplicationType.ActiveDirectory:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.VmwareVsphereRepli:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.OceanstorMSSqlHuaweiDBReplication:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.NutanixProtectionDomainReplication:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.MariaDBGaleraCluster:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;
                                case (int)ReplicationType.RecoveryAzureSite:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.Zerto:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.RPForVMReplication:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.EMCDataDomainMTree:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.DB2FullDBMIMIX:

                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;


                                case (int)ReplicationType.AzureKubernetes:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.AzureCosmosDB:
                                case (int)ReplicationType.REDHAT_Virtulization:

                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.OpenShift:
                                    if (li3 != null)
                                    {
                                        li3.Attributes.Add("class", "active");
                                    }
                                    tdDrDatabase.Visible = false;
                                    pnlSummary.Visible = true;
                                    var drlabel = (Label)trSummaryReplication.FindControl("SGrReplicationDr");
                                    drlabel.Text = String.Empty;
                                    BindSummary();
                                    break;

                                case (int)ReplicationType.vSphereReplication:
                                    if (li2 != null)
                                    {
                                        li2.Attributes.Add("class", "active");
                                    }
                                    pnlSummary.Visible = true;
                                    BindSummary();
                                    break;

                            }
                        }
                        else
                        {
                            if (ddlSubBusinessType.SelectedValue == "15")
                            {
                                pnlSummary.Visible = true;
                                trSummaryReplication.Visible = false;
                                BindSummary();
                            }
                        }
                    }
                }
                else
                {
                    //spnDrReadyCheck.Visible = true;
                    e.Cancel = true;
                }
            }
        }

        protected void Wizard1PreviousButtonClick(object sender, WizardNavigationEventArgs e)
        {

            var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));


            if (replicationtype == 28)
            {
                divDRhostname.Visible = true;
                divPRhostname.Visible = true;

            }
            else
            {
                divDRhostname.Visible = false;
                divPRhostname.Visible = false;

            }

            var li0 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li0");
            var li1 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li1");
            var li2 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li2");
            var li3 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li3");
            var li4 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li4");
            var li5 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li5");

            li0.Attributes.Add("class", "");
            li1.Attributes.Add("class", "");
            li2.Attributes.Add("class", "");
            li3.Attributes.Add("class", "");
            li4.Attributes.Add("class", "");
            li5.Attributes.Add("class", "");
            if (Wizard1.ActiveStepIndex == 2)
            {
                if (li0 != null)
                {
                    li0.Attributes.Add("class", "complete");
                }
                if (li1 != null)
                {
                    li1.Attributes.Add("class", "active");
                }
                if (li2 != null)
                {
                    li2.Attributes.Add("class", "complete");
                }
                if (li3 != null)
                {
                    li3.Attributes.Add("class", "complete");
                }
                if (li4 != null)
                {
                    li4.Attributes.Add("class", "complete");
                }
                if (li5 != null)
                {
                    li5.Attributes.Add("class", "complete");
                }
            }
            if (Wizard1.ActiveStepIndex == 1)
            {
                if (li0 != null)
                {
                    li0.Attributes.Add("class", "active");
                }
                if (li1 != null)
                {
                    li1.Attributes.Add("class", "complete");
                }
                if (li2 != null)
                {
                    li2.Attributes.Add("class", "complete");
                }
                if (li3 != null)
                {
                    li3.Attributes.Add("class", "complete");
                }
                if (li4 != null)
                {
                    li4.Attributes.Add("class", "complete");
                }
                if (li5 != null)
                {
                    li5.Attributes.Add("class", "complete");
                }
            }
        }

        public Boolean checkInfraExistsById(int UId)
        {
            Boolean isInfrapresent = true;

            try
            {

                var getAllUserObjectList = Facade.GetUserInfraObjectByUserId(UId);

                IList<InfraObject> getAllinfraobject = Facade.GetAllInfraObjectByUserCompanyIdAndRole(_companyId, IsParentCompnay);

                if (getAllUserObjectList.Count > 0 && getAllUserObjectList != null)
                {

                    if (getAllUserObjectList.Count == getAllinfraobject.Count - 1)
                    {
                        isInfrapresent = true;
                    }
                    else
                    { isInfrapresent = false; }

                }
                else
                { isInfrapresent = false; }
            }
            catch
            {
                isInfrapresent = false;

            }
            return isInfrapresent;
        }

        protected void Wizard1FinishButtonClick(object sender, WizardNavigationEventArgs e)
        {
            Page.Validate();
            if (Page.IsValid && (ViewState["_token"] != null) && ValidateRequest("InfraObjectsConfiguration", UserActionType.CreateInfraObjects))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }

                else
                {

                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }
                    var currentTransactionType = TransactionType.Undefined;
                    if (CurrentInfraObjectId != 0)
                    {
                        try
                        {
                            _logger.Info("Current Infraobject ID :" + CurrentInfraObjectId.ToString());
                            currentTransactionType = TransactionType.Update;
                            var infraObject = new InfraObject();
                            BuildEntity(infraObject);
                            infraObject.UpdatorId = _currentLoggedUserId;

                            infraObject.Id = CurrentInfraObjectId;
                            infraObject.State = CurrentInfraObject.State;

                            _logger.Info("Updating Infraobject ID :" + infraObject.Id);

                            var infra = Facade.UpdateInfraObject(infraObject);
                            _logger.Info("End Updating Infraobject ID :" + infraObject.Id);
                            var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                            replicationtype = (replicationtype == 31) ? 1 : replicationtype;
                            switch (replicationtype)
                            {
                                case (int)ReplicationType.DB2IBMGLobalmirror:
                                case (int)ReplicationType.IBMGlobalMirror:
                                    //foreach (var item in lvLunsList.Items)
                                    //{
                                    //    var grLuns = new InfraObjectsLuns();
                                    //    grLuns.Id = Convert.ToInt32(((Label)item.FindControl("lblLunId")).Text);
                                    //    grLuns.InfraObjectId = infra.Id;
                                    //    grLuns.Luns = ((HtmlSelect)item.FindControl("ddl")).Value;
                                    //    grLuns.Logs = Convert.ToInt32(((HtmlSelect)item.FindControl("ddl")).Value);
                                    //    grLuns.AGroup = ((HtmlInputText)item.FindControl("groupA")).Value;
                                    //    grLuns.AMount = ((HtmlInputText)item.FindControl("mountA")).Value;
                                    //    grLuns.CreatorId = _currentLoggedUserId;
                                    //    _facade.UpdateInfraObjectsLuns(grLuns);
                                    //}

                                    foreach (var item in lvLunsList.Items)
                                    {
                                        //var grLuns = new InfraObjectsLuns();
                                        //grLuns.Id = Convert.ToInt32(((Label)item.FindControl("lblLunId")).Text);
                                        //grLuns.InfraObjectId = infra.Id;
                                        //grLuns.Luns = ((HtmlSelect)item.FindControl("ddl")).Value;
                                        //grLuns.Logs = Convert.ToInt32(((HtmlSelect)item.FindControl("ddl")).Value);
                                        //grLuns.AGroup = ((HtmlInputText)item.FindControl("groupA")).Value;
                                        //grLuns.AMount = ((HtmlInputText)item.FindControl("mountA")).Value;
                                        //grLuns.CreatorId = _currentLoggedUserId;
                                        //_facade.UpdateInfraObjectsLuns(grLuns);
                                        var infragmluns = new InfraobjectGlobalMirrorluns();
                                        infragmluns.InfraObjectId = infra.Id;
                                        infragmluns.FileTypeId = ((DropDownList)item.FindControl("ddlFileType")).SelectedValue.ToInteger();
                                        infragmluns.MountPointName = ((TextBox)item.FindControl("txtMountPointName")).Text.ToString();
                                        infragmluns.LVName = ((TextBox)item.FindControl("txtLVName")).Text.ToString();
                                        infragmluns.VGName = ((TextBox)item.FindControl("txtVGName")).Text.ToString();
                                        infragmluns.Id = ((Label)item.FindControl("lblLunId")).Text.ToInteger();
                                        int id = ((Label)item.FindControl("lblLunId")).Text.ToInteger();
                                        var infargm = _facade.UpdateInfraobjectGlobalMirrorLuns(infragmluns);

                                        var result = from i in luns
                                                     where (i.InfraLunsId == id)
                                                     select i;
                                        foreach (var items in result)
                                        {
                                            var infragmdetails = new InfraobjectGlobalMirrorLunsDetails();
                                            infragmdetails.Id = infargm.Id;
                                            infragmdetails.InfraLunsId = infargm.Id;

                                            infragmdetails.Hdisk = items.Hdisk.ToString();
                                            infragmdetails.PVID = items.PVID.ToString();
                                            infragmdetails.LUNID = items.LUNID.ToString();
                                            _facade.UpdateInfraobjectGlobalMirrorLuns_Details(infragmdetails);
                                        }
                                    }

                                    break;

                                case (int)ReplicationType.MSSQLServerNative:
                                    var msSqlNative = new SqlNative();
                                    msSqlNative.InfraObjectID = infra.Id;
                                    msSqlNative.BackupFolderName = txtBackup.Text;
                                    msSqlNative.CopyRestoreName = txtRestore.Text;
                                    msSqlNative.BackupFolderSharedName = txtBackupNetwork.Text;
                                    msSqlNative.CopyRestoreSharedName = txtRestoreNetwork.Text;
                                    msSqlNative.BackupInterval = Convert.ToInt32(txtbackuptime.Text);
                                    msSqlNative.CopyInterval = Convert.ToInt32(txtcopytime.Text);
                                    msSqlNative.RestoreInterval = Convert.ToInt32(txtrestoretime.Text);
                                    msSqlNative.UpdatorId = _currentLoggedUserId;
                                    _facade.UpdateSqlNative(msSqlNative);

                                    break;

                                case (int)ReplicationType.HITACHIUROracleFullDB:

                                    var hitachiluns = new HitachiUrLuns();
                                    hitachiluns.InfraObjectId = infra.Id;
                                    hitachiluns.ArchVGName = txtArchVGName.Text;
                                    hitachiluns.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachiluns.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachiluns.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachiluns.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachiluns.ArchTarget = txtArchTarget.Text;

                                    hitachiluns.RedoVGName = txtRedoVGName.Text;
                                    hitachiluns.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachiluns.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachiluns.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachiluns.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachiluns.RedoTarget = txtRedoTarget.Text;
                                    hitachiluns.UpdatorId = _currentLoggedUserId;

                                    _facade.UpdateHitachiUrLuns(hitachiluns);
                                    break;

                                case (int)ReplicationType.HitachiUrDB2FullDB:

                                    var hitachilunDb2 = new HitachiUrLuns();
                                    hitachilunDb2.InfraObjectId = infra.Id;
                                    hitachilunDb2.ArchVGName = txtArchVGName.Text;
                                    hitachilunDb2.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilunDb2.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilunDb2.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilunDb2.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilunDb2.ArchTarget = txtArchTarget.Text;

                                    hitachilunDb2.RedoVGName = txtRedoVGName.Text;
                                    hitachilunDb2.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilunDb2.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilunDb2.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilunDb2.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilunDb2.RedoTarget = txtRedoTarget.Text;
                                    hitachilunDb2.UpdatorId = _currentLoggedUserId;

                                    _facade.UpdateHitachiUrLuns(hitachilunDb2);
                                    break;

                                case (int)ReplicationType.HitachiUrMySqlFullDB:

                                    var hitachilunMySql = new HitachiUrLuns();
                                    hitachilunMySql.InfraObjectId = infra.Id;
                                    hitachilunMySql.ArchVGName = txtArchVGName.Text;
                                    hitachilunMySql.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilunMySql.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilunMySql.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilunMySql.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilunMySql.ArchTarget = txtArchTarget.Text;

                                    hitachilunMySql.RedoVGName = txtRedoVGName.Text;
                                    hitachilunMySql.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilunMySql.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilunMySql.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilunMySql.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilunMySql.RedoTarget = txtRedoTarget.Text;
                                    hitachilunMySql.UpdatorId = _currentLoggedUserId;

                                    _facade.UpdateHitachiUrLuns(hitachilunMySql);
                                    break;

                                case (int)ReplicationType.HITACHIUROracleLogShipping:

                                    var hitachiluns1 = new HitachiUrLuns();
                                    hitachiluns1.InfraObjectId = infra.Id;
                                    hitachiluns1.ArchVGName = txtArchVGName.Text;
                                    hitachiluns1.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachiluns1.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachiluns1.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachiluns1.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachiluns1.ArchTarget = txtArchTarget.Text;

                                    hitachiluns1.RedoVGName = txtRedoVGName.Text;
                                    hitachiluns1.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachiluns1.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachiluns1.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachiluns1.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachiluns1.RedoTarget = txtRedoTarget.Text;
                                    hitachiluns1.UpdatorId = _currentLoggedUserId;

                                    _facade.UpdateHitachiUrLuns(hitachiluns1);
                                    break;

                                case (int)ReplicationType.MySQLGlobalMirrorFullDB:
                                    //foreach (var item in lvLunsList.Items)
                                    //{
                                    //    var grLuns = new InfraObjectsLuns();
                                    //    grLuns.Id = Convert.ToInt32(((Label)item.FindControl("lblLunId")).Text);
                                    //    grLuns.InfraObjectId = infra.Id;
                                    //    grLuns.Luns = ((HtmlSelect)item.FindControl("ddl")).Value;
                                    //    grLuns.Logs = Convert.ToInt32(((HtmlSelect)item.FindControl("ddl")).Value);
                                    //    grLuns.AGroup = ((HtmlInputText)item.FindControl("groupA")).Value;
                                    //    grLuns.AMount = ((HtmlInputText)item.FindControl("mountA")).Value;
                                    //    grLuns.CreatorId = _currentLoggedUserId;
                                    //    _facade.UpdateInfraObjectsLuns(grLuns);
                                    //}
                                    foreach (var item in lvLunsList.Items)
                                    {
                                        //var grLuns = new InfraObjectsLuns();
                                        //grLuns.Id = Convert.ToInt32(((Label)item.FindControl("lblLunId")).Text);
                                        //grLuns.InfraObjectId = infra.Id;
                                        //grLuns.Luns = ((HtmlSelect)item.FindControl("ddl")).Value;
                                        //grLuns.Logs = Convert.ToInt32(((HtmlSelect)item.FindControl("ddl")).Value);
                                        //grLuns.AGroup = ((HtmlInputText)item.FindControl("groupA")).Value;
                                        //grLuns.AMount = ((HtmlInputText)item.FindControl("mountA")).Value;
                                        //grLuns.CreatorId = _currentLoggedUserId;
                                        //_facade.UpdateInfraObjectsLuns(grLuns);
                                        var infragmluns = new InfraobjectGlobalMirrorluns();
                                        infragmluns.InfraObjectId = infra.Id;
                                        infragmluns.FileTypeId = ((DropDownList)item.FindControl("ddlFileType")).SelectedValue.ToInteger();
                                        infragmluns.MountPointName = ((TextBox)item.FindControl("txtMountPointName")).Text.ToString();
                                        infragmluns.LVName = ((TextBox)item.FindControl("txtLVName")).Text.ToString();
                                        infragmluns.VGName = ((TextBox)item.FindControl("txtVGName")).Text.ToString();
                                        infragmluns.Id = ((Label)item.FindControl("lblLunId")).Text.ToInteger();
                                        int id = ((Label)item.FindControl("lblLunId")).Text.ToInteger();
                                        var infargm = _facade.UpdateInfraobjectGlobalMirrorLuns(infragmluns);

                                        var result = from i in luns
                                                     where (i.InfraLunsId == id)
                                                     select i;
                                        foreach (var items in result)
                                        {
                                            var infragmdetails = new InfraobjectGlobalMirrorLunsDetails();
                                            infragmdetails.Id = infargm.Id;
                                            infragmdetails.InfraLunsId = infargm.Id;

                                            infragmdetails.Hdisk = items.Hdisk.ToString();
                                            infragmdetails.PVID = items.PVID.ToString();
                                            infragmdetails.LUNID = items.LUNID.ToString();
                                            _facade.UpdateInfraobjectGlobalMirrorLuns_Details(infragmdetails);
                                        }
                                    }
                                    break;

                                case (int)ReplicationType.HitachiOracleFullDBRac:

                                    var hitachilun = new HitachiUrLuns();
                                    hitachilun.InfraObjectId = infra.Id;
                                    hitachilun.ArchVGName = txtArchVGName.Text;
                                    hitachilun.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilun.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilun.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilun.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilun.ArchTarget = txtArchTarget.Text;

                                    hitachilun.RedoVGName = txtRedoVGName.Text;
                                    hitachilun.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilun.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilun.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilun.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilun.RedoTarget = txtRedoTarget.Text;
                                    hitachilun.UpdatorId = _currentLoggedUserId;

                                    _facade.UpdateHitachiUrLuns(hitachilun);
                                    break;

                                case (int)ReplicationType.AppHitachiUr:

                                    var hitachilun3 = new HitachiUrLuns();
                                    hitachilun3.InfraObjectId = infra.Id;
                                    hitachilun3.ArchVGName = txtArchVGName.Text;
                                    hitachilun3.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilun3.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilun3.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilun3.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilun3.ArchTarget = txtArchTarget.Text;

                                    hitachilun3.RedoVGName = txtRedoVGName.Text;
                                    hitachilun3.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilun3.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilun3.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilun3.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilun3.RedoTarget = txtRedoTarget.Text;
                                    hitachilun3.UpdatorId = _currentLoggedUserId;

                                    _facade.UpdateHitachiUrLuns(hitachilun3);
                                    break;

                                case (int)ReplicationType.HitachiSyabse:

                                    var hitachilun2 = new HitachiUrLuns();
                                    hitachilun2.InfraObjectId = infra.Id;
                                    hitachilun2.ArchVGName = txtArchVGName.Text;
                                    hitachilun2.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilun2.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilun2.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilun2.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilun2.ArchTarget = txtArchTarget.Text;

                                    hitachilun2.RedoVGName = txtRedoVGName.Text;
                                    hitachilun2.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilun2.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilun2.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilun2.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilun2.RedoTarget = txtRedoTarget.Text;
                                    hitachilun2.UpdatorId = _currentLoggedUserId;

                                    _facade.UpdateHitachiUrLuns(hitachilun2);
                                    break;
                            }
                            string message = MessageInitials + " " + '"' + infraObject.Name + '"';
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));
                            ActivityLogger.AddLog1(LoggedInUserName, "InfraObjectConfifuration", UserActionType.UpdateInfraObjects, "The Infraobject  '" + infraObject.Name + "' has been Update successfully", LoggedInUserId, "IP Address" + IPAddress);
                            _logger.Info("Infra object Updated successfully " + "'" + infraObject.Name + "'" + " With User IP Address " + "'" + IPAddress + "'");

                        }

                        catch (CpException ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                            if (ex != null)
                            {
                                _logger.Error("CP exception while loading InfraObject Configuration in Wizard1FinishButtonClick method on InfraObject Configuration page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                                if (ex.InnerException != null)
                                    _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                                if (ex.StackTrace != null)
                                    _logger.Error("Exception details : " + ex.StackTrace.ToString());
                            }
                            ExceptionManager.Manage(ex, this);
                        }
                        catch (Exception ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                            {
                                ExceptionManager.Manage((CpException)ex.InnerException, this);
                            }
                            else
                            {
                                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while Updating data", ex);

                                ExceptionManager.Manage(customEx, this);
                            }
                        }
                    }
                    else
                    {
                        try
                        {
                            currentTransactionType = TransactionType.Save;
                            var infraObject = new InfraObject();
                            BuildEntity(infraObject);
                            infraObject.CreatorId = Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]); ;// _currentLoggedUserId;
                            infraObject.Id = CurrentInfraObjectId;
                            infraObject.State = InfraObjectState.Maintenance.ToString();
                            infraObject.ReplicationStatus = (int)(InfraObjectReplicationStatus.Maintenance);

                            var AlreadyExist = Facade.GetInfraObjectByName(txtName.Text);

                            if (AlreadyExist != null)
                            {
                                if (returnUrl.IsNotNullOrEmpty())
                                {
                                    Helper.Url.Redirect(returnUrl);
                                }
                                else
                                {
                                    return;
                                }
                            }


                            var Infra = Facade.AddInfraObject(infraObject);
                            if (CommandCenter.lstAllInfr != null)
                                CommandCenter.lstAllInfr.Add(Infra);

                            try
                            {
                                if (IsSuperAdmin)
                                {
                                    var userInfraObject = new UserInfraObject { UserId = _currentLoggedUserId };
                                    userInfraObject.InfraObjectId = Infra.Id;
                                    userInfraObject.CreatorId = _currentLoggedUserId;
                                    Facade.AddUseInfraObject(userInfraObject);

                                    var allUsers = Facade.GetAllUsers();

                                    var AllSuperAdmin = from i in allUsers where i.Role == UserRole.SuperAdmin && i.IsActive == 1 select i;

                                    foreach (var users in AllSuperAdmin)
                                    {
                                        if (users.Id != _currentLoggedUserId)
                                        {
                                            var userInfraObjectList = new UserInfraObject { UserId = users.Id };
                                            userInfraObjectList.InfraObjectId = Infra.Id;
                                            userInfraObjectList.CreatorId = _currentLoggedUserId;
                                            Facade.AddUseInfraObject(userInfraObjectList);
                                        }
                                    }

                                    var AllRoleByUser = from i in allUsers where i.CreatorId == _currentLoggedUserId && i.IsActive == 1 && i.Role != UserRole.SuperAdmin select i;

                                    foreach (var users in AllRoleByUser)
                                    {
                                        if (users.Id != _currentLoggedUserId)
                                        {
                                            if (checkInfraExistsById(users.Id))
                                            {

                                                var userInfraObjectList = new UserInfraObject { UserId = users.Id };
                                                userInfraObjectList.InfraObjectId = Infra.Id;
                                                userInfraObjectList.CreatorId = _currentLoggedUserId;
                                                Facade.AddUseInfraObject(userInfraObjectList);
                                            }
                                        }

                                    }

                                }
                                if (IsUserAdmin)
                                {
                                    if (checkInfraExistsById(_currentLoggedUserId))
                                    {

                                        var userInfraObject = new UserInfraObject { UserId = _currentLoggedUserId };
                                        userInfraObject.InfraObjectId = Infra.Id;
                                        userInfraObject.CreatorId = _currentLoggedUserId;
                                        Facade.AddUseInfraObject(userInfraObject);
                                    }

                                    var allUsers = Facade.GetAllUsers();

                                    var AllSuperAdmin = from i in allUsers where i.Id == _currentLoggedUserId && i.IsActive == 1 select i;

                                    foreach (var users in AllSuperAdmin)
                                    {
                                        int createdId = users.CreatorId;

                                        var userInfraObjectList = new UserInfraObject { UserId = createdId };
                                        userInfraObjectList.InfraObjectId = Infra.Id;
                                        userInfraObjectList.CreatorId = _currentLoggedUserId;
                                        Facade.AddUseInfraObject(userInfraObjectList);

                                    }



                                }

                            }
                            catch
                            { }
                            if (_isRac)
                            {
                                if (NodeList != "")
                                {
                                    var nodes = NodeList.Split(',');
                                    foreach (var node in nodes)
                                    {
                                        if (node != "")
                                        {
                                            var dbnodes = new GroupDatabaseNodes();
                                            dbnodes.Id = Convert.ToInt32(node);
                                            dbnodes.InfraObjectId = Infra.Id;
                                            _facade.UpdateInfraObjectID(dbnodes);
                                        }
                                    }
                                }
                                NodeList = "";
                            }
                            var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                            replicationtype = (replicationtype == 31) ? 1 : replicationtype;
                            switch (replicationtype)
                            {
                                case (int)ReplicationType.DB2IBMGLobalmirror:
                                case (int)ReplicationType.IBMGlobalMirror:
                                    //foreach (var item in lvLunsList.Items)
                                    //{
                                    //    var grLuns = new InfraObjectsLuns();
                                    //    grLuns.InfraObjectId = Infra.Id;
                                    //    grLuns.Luns = ((HtmlSelect)item.FindControl("ddl")).Value;
                                    //    grLuns.Logs = Convert.ToInt32(((HtmlSelect)item.FindControl("ddl")).Value);
                                    //    grLuns.AGroup = ((HtmlInputText)item.FindControl("groupA")).Value;
                                    //    grLuns.AMount = ((HtmlInputText)item.FindControl("mountA")).Value;
                                    //    grLuns.CreatorId = _currentLoggedUserId;
                                    //    _facade.AddInfraObjectsLuns(grLuns);
                                    //}
                                    foreach (var item in lvLunsList.Items)
                                    {
                                        //var grLuns = new InfraObjectsLuns();
                                        //grLuns.InfraObjectId = Infra.Id;
                                        //grLuns.Luns = ((HtmlSelect)item.FindControl("ddl")).Value;
                                        //grLuns.Logs = Convert.ToInt32(((HtmlSelect)item.FindControl("ddl")).Value);
                                        //grLuns.AGroup = ((HtmlInputText)item.FindControl("groupA")).Value;
                                        //grLuns.AMount = ((HtmlInputText)item.FindControl("mountA")).Value;
                                        //grLuns.CreatorId = _currentLoggedUserId;
                                        //_facade.AddInfraObjectsLuns(grLuns);
                                        var infragmluns = new InfraobjectGlobalMirrorluns();
                                        infragmluns.InfraObjectId = Infra.Id;
                                        infragmluns.FileTypeId = ((DropDownList)item.FindControl("ddlFileType")).SelectedValue.ToInteger();
                                        infragmluns.MountPointName = ((TextBox)item.FindControl("txtMountPointName")).Text.ToString();
                                        infragmluns.LVName = ((TextBox)item.FindControl("txtLVName")).Text.ToString();
                                        infragmluns.VGName = ((TextBox)item.FindControl("txtVGName")).Text.ToString();

                                        int id = ((Label)item.FindControl("lblLunId")).Text.ToInteger();
                                        var infargm = _facade.AddInfraobjectGlobalMirrorLuns(infragmluns);

                                        var result = from i in luns
                                                     where (i.InfraLunsId == id)
                                                     select i;
                                        foreach (var items in result)
                                        {
                                            var infragmdetails = new InfraobjectGlobalMirrorLunsDetails();
                                            infragmdetails.InfraLunsId = infargm.Id;
                                            infragmdetails.Hdisk = items.Hdisk.ToString();
                                            infragmdetails.PVID = items.PVID.ToString();
                                            infragmdetails.LUNID = items.LUNID.ToString();
                                            _facade.AddInfraobjectGlobalMirrorLuns_Details(infragmdetails);
                                        }
                                    }

                                    break;

                                case (int)ReplicationType.MSSQLServerNative:
                                    var msSqlNative = new SqlNative();
                                    msSqlNative.InfraObjectID = Infra.Id;
                                    msSqlNative.BackupFolderName = txtBackup.Text;
                                    msSqlNative.CopyRestoreName = txtRestore.Text;
                                    msSqlNative.BackupFolderSharedName = txtBackupNetwork.Text;
                                    msSqlNative.CopyRestoreSharedName = txtRestoreNetwork.Text;
                                    msSqlNative.BackupInterval = Convert.ToInt32(txtbackuptime.Text);
                                    msSqlNative.CopyInterval = Convert.ToInt32(txtcopytime.Text);
                                    msSqlNative.RestoreInterval = Convert.ToInt32(txtrestoretime.Text);
                                    msSqlNative.CreatorId = _currentLoggedUserId;
                                    _facade.AddSqlNativeTranslog(msSqlNative);
                                    break;

                                case (int)ReplicationType.HITACHIUROracleFullDB:

                                    var hitachiluns = new HitachiUrLuns();
                                    hitachiluns.InfraObjectId = Infra.Id;
                                    hitachiluns.ArchVGName = txtArchVGName.Text;
                                    hitachiluns.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachiluns.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachiluns.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachiluns.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachiluns.ArchTarget = txtArchTarget.Text;

                                    hitachiluns.RedoVGName = txtRedoVGName.Text;
                                    hitachiluns.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachiluns.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachiluns.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachiluns.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachiluns.RedoTarget = txtRedoTarget.Text;
                                    hitachiluns.CreatorId = _currentLoggedUserId;

                                    _facade.AddHitachiUrLuns(hitachiluns);
                                    break;

                                case (int)ReplicationType.HITACHIUROracleLogShipping:

                                    var hitachiluns1 = new HitachiUrLuns();
                                    hitachiluns1.InfraObjectId = Infra.Id;
                                    hitachiluns1.ArchVGName = txtArchVGName.Text;
                                    hitachiluns1.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachiluns1.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachiluns1.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachiluns1.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachiluns1.ArchTarget = txtArchTarget.Text;

                                    hitachiluns1.RedoVGName = txtRedoVGName.Text;
                                    hitachiluns1.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachiluns1.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachiluns1.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachiluns1.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachiluns1.RedoTarget = txtRedoTarget.Text;
                                    hitachiluns1.CreatorId = _currentLoggedUserId;

                                    _facade.AddHitachiUrLuns(hitachiluns1);
                                    break;

                                case (int)ReplicationType.MySQLGlobalMirrorFullDB:
                                    //foreach (var item in lvLunsList.Items)
                                    //{
                                    //    var grLuns = new InfraObjectsLuns();
                                    //    grLuns.InfraObjectId = Infra.Id;
                                    //    grLuns.Luns = ((HtmlSelect)item.FindControl("ddl")).Value;
                                    //    grLuns.Logs = Convert.ToInt32(((HtmlSelect)item.FindControl("ddl")).Value);
                                    //    grLuns.AGroup = ((HtmlInputText)item.FindControl("groupA")).Value;
                                    //    grLuns.AMount = ((HtmlInputText)item.FindControl("mountA")).Value;
                                    //    grLuns.CreatorId = _currentLoggedUserId;
                                    //    _facade.AddInfraObjectsLuns(grLuns);
                                    //}

                                    foreach (var item in lvLunsList.Items)
                                    {
                                        //var grLuns = new InfraObjectsLuns();
                                        //grLuns.InfraObjectId = Infra.Id;
                                        //grLuns.Luns = ((HtmlSelect)item.FindControl("ddl")).Value;
                                        //grLuns.Logs = Convert.ToInt32(((HtmlSelect)item.FindControl("ddl")).Value);
                                        //grLuns.AGroup = ((HtmlInputText)item.FindControl("groupA")).Value;
                                        //grLuns.AMount = ((HtmlInputText)item.FindControl("mountA")).Value;
                                        //grLuns.CreatorId = _currentLoggedUserId;
                                        //_facade.AddInfraObjectsLuns(grLuns);
                                        var infragmluns = new InfraobjectGlobalMirrorluns();
                                        infragmluns.InfraObjectId = Infra.Id;
                                        infragmluns.FileTypeId = ((DropDownList)item.FindControl("ddlFileType")).SelectedValue.ToInteger();
                                        infragmluns.MountPointName = ((TextBox)item.FindControl("txtMountPointName")).Text.ToString();
                                        infragmluns.LVName = ((TextBox)item.FindControl("txtLVName")).Text.ToString();
                                        infragmluns.VGName = ((TextBox)item.FindControl("txtVGName")).Text.ToString();

                                        int id = ((Label)item.FindControl("lblLunId")).Text.ToInteger();
                                        var infargm = _facade.AddInfraobjectGlobalMirrorLuns(infragmluns);

                                        var result = from i in luns
                                                     where (i.InfraLunsId == id)
                                                     select i;
                                        foreach (var items in result)
                                        {
                                            var infragmdetails = new InfraobjectGlobalMirrorLunsDetails();
                                            infragmdetails.InfraLunsId = infargm.Id;
                                            infragmdetails.Hdisk = items.Hdisk.ToString();
                                            infragmdetails.PVID = items.PVID.ToString();
                                            infragmdetails.LUNID = items.LUNID.ToString();
                                            _facade.AddInfraobjectGlobalMirrorLuns_Details(infragmdetails);
                                        }
                                    }
                                    break;

                                case (int)ReplicationType.HitachiOracleFullDBRac:

                                    var hitachilun = new HitachiUrLuns();
                                    hitachilun.InfraObjectId = Infra.Id;
                                    hitachilun.ArchVGName = txtArchVGName.Text;
                                    hitachilun.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilun.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilun.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilun.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilun.ArchTarget = txtArchTarget.Text;

                                    hitachilun.RedoVGName = txtRedoVGName.Text;
                                    hitachilun.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilun.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilun.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilun.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilun.RedoTarget = txtRedoTarget.Text;
                                    hitachilun.CreatorId = _currentLoggedUserId;

                                    _facade.AddHitachiUrLuns(hitachilun);
                                    break;

                                case (int)ReplicationType.HitachiUrDB2FullDB:

                                    var hitachilunDb2 = new HitachiUrLuns();
                                    hitachilunDb2.InfraObjectId = Infra.Id;
                                    hitachilunDb2.ArchVGName = txtArchVGName.Text;
                                    hitachilunDb2.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilunDb2.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilunDb2.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilunDb2.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilunDb2.ArchTarget = txtArchTarget.Text;

                                    hitachilunDb2.RedoVGName = txtRedoVGName.Text;
                                    hitachilunDb2.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilunDb2.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilunDb2.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilunDb2.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilunDb2.RedoTarget = txtRedoTarget.Text;
                                    hitachilunDb2.CreatorId = _currentLoggedUserId;

                                    _facade.AddHitachiUrLuns(hitachilunDb2);
                                    break;

                                case (int)ReplicationType.HitachiUrMySqlFullDB:

                                    var hitachilunMySql = new HitachiUrLuns();
                                    hitachilunMySql.InfraObjectId = Infra.Id;
                                    hitachilunMySql.ArchVGName = txtArchVGName.Text;
                                    hitachilunMySql.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilunMySql.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilunMySql.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilunMySql.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilunMySql.ArchTarget = txtArchTarget.Text;

                                    hitachilunMySql.RedoVGName = txtRedoVGName.Text;
                                    hitachilunMySql.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilunMySql.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilunMySql.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilunMySql.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilunMySql.RedoTarget = txtRedoTarget.Text;
                                    hitachilunMySql.CreatorId = _currentLoggedUserId;

                                    _facade.AddHitachiUrLuns(hitachilunMySql);
                                    break;

                                case (int)ReplicationType.AppHitachiUr:

                                    var hitachilun2 = new HitachiUrLuns();
                                    hitachilun2.InfraObjectId = Infra.Id;
                                    hitachilun2.ArchVGName = txtArchVGName.Text;
                                    hitachilun2.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilun2.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilun2.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilun2.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilun2.ArchTarget = txtArchTarget.Text;

                                    hitachilun2.RedoVGName = txtRedoVGName.Text;
                                    hitachilun2.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilun2.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilun2.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilun2.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilun2.RedoTarget = txtRedoTarget.Text;
                                    hitachilun2.UpdatorId = _currentLoggedUserId;

                                    _facade.AddHitachiUrLuns(hitachilun2);
                                    break;

                                case (int)ReplicationType.HitachiSyabse:

                                    var hitachilunss = new HitachiUrLuns();
                                    hitachilunss.InfraObjectId = Infra.Id;
                                    hitachilunss.ArchVGName = txtArchVGName.Text;
                                    hitachilunss.ArchMountPoint = txtArchMountPoint.Text;
                                    hitachilunss.ArchDevicetype = ddlarchdevicetype.SelectedItem.Value;
                                    hitachilunss.ArchHURTrueCopySource = txtArchHurTrueCopysource.Text;
                                    hitachilunss.ArchShadowimagePR = txtArchShadowImagePR.Text;
                                    hitachilunss.ArchTarget = txtArchTarget.Text;

                                    hitachilunss.RedoVGName = txtRedoVGName.Text;
                                    hitachilunss.RedoMountPoint = txtRedoMountPoint.Text;
                                    hitachilunss.RedoDeviceType = ddlRedoDevicetype.SelectedItem.Value;
                                    hitachilunss.RedoHURTrueCopySource = txtRedohurtruecopysource.Text;
                                    hitachilunss.RedoShadowimagePR = txtRedoshadowimagePR.Text;
                                    hitachilunss.RedoTarget = txtRedoTarget.Text;
                                    hitachilunss.CreatorId = _currentLoggedUserId;

                                    _facade.AddHitachiUrLuns(hitachilunss);
                                    break;
                            }
                            string message = MessageInitials + " " + '"' + infraObject.Name + '"';
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));
                            ActivityLogger.AddLog1(LoggedInUserName, "InfraObjectConfifuration", UserActionType.CreateInfraObjects, "The Infraobject  '" + infraObject.Name + "' has been added successfully", LoggedInUserId, "IP Address" + IPAddress);
                            _logger.Info("Infra object added successfully " + "'" + infraObject.Name + "'" + " With User IP Address " + "'" + IPAddress + "'");
                        }
                        catch (CpException ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                            if (ex != null)
                            {
                                _logger.Error("CP exception while loading InfraObject Configuration in Wizard1FinishButtonClick method on InfraObject Configuration page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                                if (ex.InnerException != null)
                                    _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                                if (ex.StackTrace != null)
                                    _logger.Error("Exception details : " + ex.StackTrace.ToString());
                            }
                            ExceptionManager.Manage(ex, this);
                        }
                        catch (Exception ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                            {
                                ExceptionManager.Manage((CpException)ex.InnerException, this);
                            }
                            else
                            {
                                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                                ExceptionManager.Manage(customEx, this);
                            }
                        }
                    }

                    //if (returnUrl.IsNotNullOrEmpty())
                    //{
                    //    var secureUrl = new SecureUrl(returnUrl);
                    //    if (secureUrl.QueryStrings.Count == 0)
                    //    {
                    //        secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty,
                    //            Constants.UrlConstants.Params.Message,
                    //            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials,
                    //                currentTransactionType));
                    //    }
                    //    else
                    //    {
                    //        secureUrl.AddParamater(Constants.UrlConstants.Params.Message,
                    //            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials,
                    //                currentTransactionType));
                    //    }
                    //    Helper.Url.Redirect(secureUrl);
                    //}

                    if (returnUrl.IsNotNullOrEmpty())
                    {

                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.InfraObjectId, CurrentInfraObject);

                        Helper.Url.Redirect(returnUrl);
                    }

                }
            }
        }

        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();


                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }

                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtCountryCode");
                //IgnoreIDs.Add("txtCountryCode2");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        private bool ValidWizard()
        {
            bool isValue = true;
            //if (lblName1.Text != string.Empty)
            //{
            //    isValue = true;
            //}
            //else
            //{
            //    isValue = false;
            //}
            return isValue;
        }

        protected void ValidationFunctionName(object source, ServerValidateEventArgs args)
        {
            if (CurrentInfraObjectId == 0)
            {
                var alGrps = _facade.GetAllInfraObject();
                if (alGrps != null)
                {
                    if (alGrps.Count > 0)
                    {
                        foreach (var g in alGrps)
                        {
                            if (g.Name.ToUpper() == txtName.Text.ToUpper())
                            {
                                args.IsValid = false;
                                txtName.Focus();
                                break;
                            }
                        }
                    }
                }
            }
        }

        protected void ValidateCheckBox(object source, ServerValidateEventArgs args)
        {
            args.IsValid = (chkDRReady.Checked);
        }


        private void BindSummary()
        {
            if (chkDRReady.Checked == false)
            {
                SGrTdServerDr.Visible = false;
                SGrDatabaseNearDr.Visible = false;
                tdReplicationD.Visible = false;
                SGrReplicationDr.Visible = false;
                trSummaryReplication.Visible = false;
                tdDRComponent.Visible = false;
                SGrTDServereDrDatabase.Visible = false;
            }
            else
            {
                SGrTdServerDr.Visible = true;
                SGrDatabaseNearDr.Visible = true;
                tdReplicationD.Visible = true;
                SGrReplicationDr.Visible = true;
                trSummaryReplication.Visible = true;
                tdDRComponent.Visible = true;
                SGrTDServereDrDatabase.Visible = true;
            }
            if (ddlSubBusinessType.SelectedValue == "15")
            {
                trSummaryReplication.Visible = false;
            }
            else
            {
                trSummaryReplication.Visible = true;
            }
            if (ddlSubBusinessType.SelectedValue == "25")
            {
                trSummaryReplication.Visible = false;
            }
            else
            {
                trSummaryReplication.Visible = true;
            }
            trSummaryCommand.Visible = false;
            trDrApplicationCheck.Visible = false;
            trDRMonitoringWorkflow.Visible = false;
            //trSummaryOutput.Visible = false;
            trSummaryDataBase.Visible = true;


            SGroupName.Text = txtName.Text;
            SGroupDesc.Text = txtDescription.Text;
            SGroupService.Text = ddlBusinessService.SelectedItem.Text;
            SGroupFunction.Text = ddlFunction.SelectedItem.Text;
            SGroupRepType.Text = ddlBusinessType.SelectedItem.Text;
            SGroupDrReady.Text = chkDRReady.Checked ? "Yes" : "No";
            SGroupRecoveryType.Text = ddlReplicationType.SelectedItem.Text;
            SGroupNearSite.Text = chkNearSite.Checked ? "Yes" : "No";
            SGroupCommand.Text = ddlMonitorWorkflow.SelectedItem.Text;//txtCommand.Text;
            if (SGroupCommand.Text == "- Select Workflow Name -")
            {
                SGroupCommand.Text = "NA";
            }

            lblCheckDrMonitor.Text = chkDrMonitorApplication.Checked.ToString();
            lblDrMonitorWorkflow.Text = ddlDRMonitorWorkflow.SelectedItem.Text;
            if (lblDrMonitorWorkflow.Text == "- Select Workflow Name -")
            {
                lblDrMonitorWorkflow.Text = "NA";
            }


            //SGroupOutput.Text = txtOutput.Text;
            SGroupPriority.Text = ddlPriority.SelectedItem.Text;

            SGroupIsQueueMonitor.Text = ChkIsQueueMonitor.Checked ? "Yes" : "No";

            SGroupIsPair.Text = ChkIspair.Checked ? "Yes" : "No";
            SGroupIsAssociate.Text = ChkIsAssociate.Checked ? "Yes" : "No";
            if (ChkIspair.Checked == true)
            {
                SGroupInfraID.Text = ddlPairinfraobjectid.SelectedItem.Text;
            }
            else
            {
                SGroupInfraID.Text = "No";
            }
            if (ChkIsAssociate.Checked == true)
            {
                SGroupAssociateID.Text = ddlPairinfraobjectid.SelectedItem.Text;
            }
            else
            {
                SGroupAssociateID.Text = "No";
            }

            SGrServerPr.Text = ddlServerPr.SelectedItem.Text;
            if (chkDRReady.Checked)
                SGrServerDr.Text = ddlServerDr.SelectedItem.Text;

            SGrReplicationPr.Text = ddlReplicationPr.SelectedItem.Text;
            SGrReplicationDr.Text = ddlReplicationDr.SelectedItem.Text;

            SGrDatabaseNearPr.Text = ddlDatabasePr.SelectedItem.Text;
            SGrDatabaseNearDr.Text = ddlDatabaseDr.SelectedItem.Text;

            if (chkIsCluster.Checked == true)
            {
                if (ddlCluster.SelectedValue == "1")
                {
                    trClusterServer.Visible = true;
                    SGClusterPR.Text = ddlClusterPR.SelectedItem.Text;
                    // SGClusterDR.Text = ddlClusterDR.SelectedItem.Text;
                }
                else
                {
                    trClusterServer.Visible = true;
                    SGClusterPR.Text = ddlClusterPR.SelectedItem.Text;
                    SGClusterDR.Text = ddlClusterDR.SelectedItem.Text;
                }
            }
            else
            {
                trClusterServer.Visible = false;
                SGClusterPR.Text = " ";
                SGClusterDR.Text = " ";
            }
            var replicationtype = (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
            if (replicationtype == 0)
            {
                trSummaryReplication.Visible = false;
            }
            //if (replicationtype == 25)
            //{
            //    trSummaryReplication.Visible = false;
            //    tdProductionReplication.Visible = false;
            //}
            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.Application).ToString())
            {
                trSummaryDataBase.Visible = false;
                trSummaryCommand.Visible = true;
                trDrApplicationCheck.Visible = true;
                trDRMonitoringWorkflow.Visible = true;
                //trSummaryOutput.Visible = true;
                if (replicationtype == (int)ReplicationType.SRMVMware)
                {
                    trSummaryReplication.Visible = false;

                    trSecondServer.Visible = true;
                    //lblPR.Text = ddlServerPr2.SelectedItem.Text;
                    //lblDr.Text = ddlServerDr2.SelectedItem.Text;

                    lblPR2.Text = ddlServerPr2.SelectedItem.Text;
                    lblDr2.Text = ddlServerDr2.SelectedItem.Text;
                }
                if (replicationtype == (int)ReplicationType.vSphereReplication)
                {
                    trSummaryReplication.Visible = true;

                    trSecondServer.Visible = true;
                    //lblPR.Text = ddlServerPr2.SelectedItem.Text;
                    //lblDr.Text = ddlServerDr2.SelectedItem.Text;

                    lblPR2.Text = ddlServerPr2.SelectedItem.Text;
                    lblDr2.Text = ddlServerDr2.SelectedItem.Text;
                }


            }
            if (replicationtype == (int)ReplicationType.VMWareDataSync || replicationtype == (int)ReplicationType.VMWareGlobalMirror || replicationtype == (int)ReplicationType.VMWareHitachiUR || replicationtype == (int)ReplicationType.VMWareSnapMirror || replicationtype == (int)ReplicationType.MSExchangeDAG || replicationtype == (int)ReplicationType.HyperV || replicationtype == (int)ReplicationType.VMWareWithSVC || replicationtype == (int)ReplicationType.HitachiURMSSQLFullDB || replicationtype == (int)ReplicationType.GlobalMirrorMSSQLFullDB || replicationtype == (int)ReplicationType.DRNET || replicationtype == (int)ReplicationType.NutanixLeapReplication || replicationtype == (int)ReplicationType.NutanixProtectionDomainReplication || replicationtype == (int)ReplicationType.REDHAT_Virtulization || replicationtype == (int)ReplicationType.OpenShift)
            {
                trSummaryDataBase.Visible = false;
            }
            if (replicationtype == (int)ReplicationType.DB2HADR || replicationtype == (int)ReplicationType.OracleCloudDataGuard || replicationtype == (int)ReplicationType.DB2HADR9X || replicationtype == (int)ReplicationType.MySQLNative || replicationtype == (int)ReplicationType.SqlServerDataSync || replicationtype == (int)ReplicationType.EnterPriseDB || replicationtype == (int)ReplicationType.Postgres9X || replicationtype == (int)ReplicationType.SAPHANADBReplication || replicationtype == (int)ReplicationType.Postgres10 || replicationtype == (int)ReplicationType.MongoDB || replicationtype == (int)ReplicationType.RPForVMReplication || replicationtype == (int)ReplicationType.OC_ExaDB)
            {
                trSummaryReplication.Visible = false;
            }
            if (replicationtype == (int)ReplicationType.MSExchangeDAG)
            {
                trSummaryExchangeMailBoxDAG.Visible = true;
                tdReplicationD.Visible = false;
            }
            if (replicationtype == (int)ReplicationType.EC2S3DataSync)
            {
                SGrServerDr.Text = "NA";
                SGrReplicationDr.Text = "NA";
            }

            //if (replicationtype == (int)ReplicationType.MSSQLDataBaseMirror)
            //{
            //    btnRacNodeSummary.Visible = false;
            //    SGrReplicationPr.Text = "NA";
            //    SGrReplicationDr.Text = "NA";
            //}
            if (replicationtype == (int)ReplicationType.ApplicationDoubleTake)
            {
                trSummaryCommand.Visible = false;
                trDRMonitoringWorkflow.Visible = false;
                trDrApplicationCheck.Visible = false;
            }
            if (replicationtype == (int)ReplicationType.TPCR)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }

            if (replicationtype == (int)ReplicationType.HP3ParwithESXI)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }

            if (replicationtype == (int)ReplicationType.RoboCopy)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }

            if (replicationtype == (int)ReplicationType.VeeamReplication)
            {
                trSummaryDataBase.Visible = false;
                trDrDR.Visible = false;
                tdDrDatabase.Visible = false;
                tdDrReplication.Visible = false;
                trDr.Visible = false;
                SGrTdServerDr.Visible = false;
                tdReplicationD.Visible = false;
                lblSummaryNearDr.Visible = false;
            }

            if (replicationtype == (int)ReplicationType.GoldenGateRepli)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }
            if (replicationtype == (int)ReplicationType.RSync)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }
            if (replicationtype == (int)ReplicationType.NutanixLeapReplication)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }
            if (replicationtype == (int)ReplicationType.ActiveDirectory)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }

            if (replicationtype == (int)ReplicationType.VmwareVsphereRepli || replicationtype == (int)ReplicationType.vSphereReplication)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }
            if (replicationtype == (int)ReplicationType.NutanixProtectionDomainReplication)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }
            //if (replicationtype == (int)ReplicationType.OracleWithRSync)
            //{
            //    trSummaryDataBase.Visible = false;
            //    tdProductionDataBase.Visible = false;
            //    tdDrDatabase.Visible = false;
            //}

            if (replicationtype == (int)ReplicationType.Zerto)
            {
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }

            if (replicationtype == (int)ReplicationType.RPForVMReplication)
            {
                trSummaryReplication.Visible = true;
                trSummaryDataBase.Visible = false;
                tdProductionDataBase.Visible = false;
                tdDrDatabase.Visible = false;
            }

            if (replicationtype == (int)ReplicationType.OpenShift)
            {
                SGrReplicationDr.Text = String.Empty;
                SGrReplicationDr.Visible = false;
                tdReplicationPr.Attributes.Add("colspan", "2");
                tdReplicationD.Visible = false;
                lblSummaryNearPr.InnerText = "OpenShift Cluster Server";
                lblSummaryNearDr.InnerText = "OpenShift API Server";
                tdDrReplication.Visible = false;
                tdProductionReplication.Style.Add("border", "0px");
            }


        }

        private void BuildEntity(InfraObject infraObject)
        {
            infraObject.Name = txtName.Text;
            infraObject.Description = txtDescription.Text;
            infraObject.BusinessServiceId = Convert.ToInt32(ddlBusinessService.SelectedValue);
            infraObject.BusinessFunctionId = Convert.ToInt32(ddlFunction.SelectedValue);
            infraObject.DRReady = chkDRReady.Checked;
            infraObject.Type = Convert.ToInt32(ddlBusinessType.SelectedValue);
            infraObject.SubType = Convert.ToInt32(ddlSubBusinessType.SelectedValue);
            infraObject.RecoveryType = Convert.ToInt32(ddlReplicationType.SelectedValue);

            infraObject.PRServerId2 = 0;
            infraObject.DRServerId2 = 0;

            infraObject.NearGroupId = chkNearSite.Checked ? 1 : 0;
            infraObject.Priority = Convert.ToInt32(ddlPriority.SelectedValue);

            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.Application).ToString())
            {
                infraObject.MonitoringWorkflow = Convert.ToInt32(ddlMonitorWorkflow.SelectedItem.Value);
                infraObject.DrMonitorApplicationCheck = chkDrMonitorApplication.Checked ? 1 : 0;
                infraObject.DrMonitoringWorkflow = Convert.ToInt32(ddlDRMonitorWorkflow.SelectedItem.Value);
                if (ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.SRMVMware).ToString() ||
                    ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.vSphereReplication).ToString())
                {
                    infraObject.PRServerId2 = Convert.ToInt32(ddlServerPr2.SelectedValue);
                    infraObject.DRServerId2 = Convert.ToInt32(ddlServerDr2.SelectedValue);
                }
            }

            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.DB).ToString())
            {
                if (ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.SQLNative2008).ToString())
                {
                    if (chkPRhostname.Checked == true)
                    {
                        infraObject.IsPRHostName = 1;
                    }
                    else
                    {
                        infraObject.IsPRHostName = 0;
                    }

                    if (chkDRhostname.Checked == true)
                    {
                        infraObject.IsDRHostName = 1;
                    }
                    else
                    {
                        infraObject.IsDRHostName = 0;
                    }
                }
            }

            infraObject.PRServerId = Convert.ToInt32(ddlServerPr.SelectedValue);
            infraObject.DRServerId = Convert.ToInt32(ddlServerDr.SelectedValue);
            infraObject.PRDatabaseId = Convert.ToInt32(ddlDatabasePr.SelectedValue);
            infraObject.DRDatabaseId = Convert.ToInt32(ddlDatabaseDr.SelectedValue);
            infraObject.PRReplicationId = Convert.ToInt32(ddlReplicationPr.SelectedValue);
            infraObject.DRReplicationId = Convert.ToInt32(ddlReplicationDr.SelectedValue);

            if (Convert.ToInt32(ddlReplicationType.SelectedValue) == (int)ReplicationType.OpenShift)
            {
                infraObject.DRReplicationId = Convert.ToInt32(ddlReplicationPr.SelectedValue);
            }
            else
            {
                infraObject.DRReplicationId = Convert.ToInt32(ddlReplicationDr.SelectedValue);
            }

            infraObject.IsActive = CurrentInfraObject.IsActive;

            if (chkPRhostname.Checked == true)
            {
                infraObject.IsPRHostName = 1;
            }
            else
            {
                infraObject.IsPRHostName = 0;
            }

            if (chkDRhostname.Checked == true)
            {
                infraObject.IsDRHostName = 1;
            }
            else
            {
                infraObject.IsDRHostName = 0;
            }

            if (ChkIsQueueMonitor.Checked == true)
            {
                infraObject.IsQueueMonitor = 1;
                //infraObject.IsAssociate = 0;
            }
            else
            {
                infraObject.IsQueueMonitor = 0;
                //infraObject.IsAssociate = 0;
            }

            if (ChkIspair.Checked == true)
            {
                infraObject.IsPair = 1;
                //infraObject.IsAssociate = 0;
            }
            else
            {
                infraObject.IsPair = 0;
                //infraObject.IsAssociate = 0;
            }

            if (ChkIsAssociate.Checked == true)
            {
                infraObject.IsAssociate = 1;
                //infraObject.IsPair = 0;
            }
            else
            {
                infraObject.IsAssociate = 0;
                //infraObject.IsPair = 0;
            }
            if (chkIsCluster.Checked == true)
            {
                infraObject.IsCluster = 1;
                if (ddlCluster.SelectedValue == "1")
                {
                    infraObject.ClusterPRServerId = Convert.ToInt32(ddlClusterPR.SelectedValue);
                    //infraObject.ClusterDRServerId = Convert.ToInt32(ddlClusterDR.SelectedValue);
                }
                else
                {
                    infraObject.ClusterPRServerId = Convert.ToInt32(ddlClusterPR.SelectedValue);
                    infraObject.ClusterDRServerId = Convert.ToInt32(ddlClusterDR.SelectedValue);
                }
                infraObject.ClusterName = ddlCluster.SelectedItem.Text;
            }

            else
            {
                infraObject.IsCluster = 0;
                infraObject.ClusterPRServerId = 0;
                infraObject.ClusterDRServerId = 0;
                infraObject.ClusterName = null;
            }
            infraObject.PairInfraObjectId = Convert.ToInt32(ddlPairinfraobjectid.SelectedValue);
            if (chkPrCloud.Checked)
                infraObject.IsPRCloud = 1;
            else
                infraObject.IsPRCloud = 0;

            if (chkDrCloud.Checked)
                infraObject.IsDRCloud = 1;
            else
                infraObject.IsDRCloud = 0;
        }

        protected void SelectedBusinessType()
        {
            tdDataBaseComponant.Visible = true;
            tdProductionDataBase.Visible = true;
            tdDrDatabase.Visible = true;
            divCommand.Visible = false;
            //divOutPut.Visible = false;
            string role = Convert.ToString(LoggedInUserRole);

            if (ddlBusinessType.SelectedItem != null)
            {
                if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.Application).ToString())
                {
                    divCommand.Visible = true;
                    //Utility.PopulateWorkflows(ddlMonitorWorkflow, true);
                    //Utility.PopulateWorkflows(ddlDRMonitorWorkflow, true);
                    //divOutPut.Visible = true;
                    //  Utility.PopulateServerByType(ddlServerPr, GroupServerType.PRAppServer.ToString(), "DR", true);
                    //Utility.PopulateServerByTypeAndRolewithCompanyId(ddlServerPr, "DR", LoggedInUserCompanyId, IsSuperAdmin, _isParent, true);
                    //if (chkDRReady.Checked)
                    //    // Utility.PopulateServerByType(ddlServerDr, GroupServerType.DRAppServer.ToString(), "PR", true);
                    //    Utility.PopulateServerByTypeAndRolewithCompanyId(ddlServerDr, "PR", LoggedInUserCompanyId, IsSuperAdmin, _isParent, true);

                    //var replicationtype =
                    //    (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                    //// Utility.PopulateReplicationBaseByType(ddlReplicationPr, replicationtype, true);
                    //Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, replicationtype, true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    //if (chkDRReady.Checked)
                    //    //  Utility.PopulateReplicationBaseByType(ddlReplicationDr, replicationtype, true);
                    //    Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, replicationtype, true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);


                    //Utility.PopulateWorkflowsByLogInId(ddlMonitorWorkflow, _isUserSuperAdmin == true ? UserRole.SuperAdmin.ToString() : UserRole.Administrator.ToString(), LoggedInUserId, true);
                    //Utility.PopulateWorkflowsByLogInId(ddlDRMonitorWorkflow, _isUserSuperAdmin == true ? UserRole.SuperAdmin.ToString() : UserRole.Administrator.ToString(), LoggedInUserId, true);
                    var workflowlist = _facade.GetWorkflowsByUserrole(Convert.ToBoolean(HttpContext.Current.Session["_isUserSuperAdmin"]) == true ? UserRole.SuperAdmin.ToString() : UserRole.Administrator.ToString(), Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]));

                    if (workflowlist != null)
                    {
                        var workflowListByOrder = from a in workflowlist orderby a.Name ascending select a;
                        ddlMonitorWorkflow.Items.Clear();
                        ddlMonitorWorkflow.DataSource = workflowListByOrder;
                        ddlMonitorWorkflow.DataTextField = "Name";
                        ddlMonitorWorkflow.DataValueField = "Id";
                        ddlMonitorWorkflow.DataBind();
                        ddlMonitorWorkflow.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName, "000"));

                        ddlDRMonitorWorkflow.Items.Clear();
                        ddlDRMonitorWorkflow.DataSource = workflowListByOrder;
                        ddlDRMonitorWorkflow.DataTextField = "Name";
                        ddlDRMonitorWorkflow.DataValueField = "Id";
                        ddlDRMonitorWorkflow.DataBind();
                        ddlDRMonitorWorkflow.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName, "000"));
                    }
                    else
                    {
                        ddlMonitorWorkflow.Items.Clear();
                        ddlMonitorWorkflow.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName, "000"));
                        ddlDRMonitorWorkflow.Items.Clear();
                        ddlDRMonitorWorkflow.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName, "000"));
                    }

                    if (LoggedInUserRole == UserRole.SuperAdmin)
                    {
                        Utility.PopulateServerByTypeAndRolewithCompanyId(ddlServerPr, "DR", LoggedInUserCompanyId, IsSuperAdmin, _isParent, true);
                        if (chkDRReady.Checked)
                            Utility.PopulateServerByTypeAndRolewithCompanyId(ddlServerDr, "PR", LoggedInUserCompanyId, IsSuperAdmin, _isParent, true);

                        var replicationtype =
                         (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                        Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, replicationtype, true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                        if (chkDRReady.Checked)
                            Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, replicationtype, true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);

                    }
                    else
                    {
                        #region Old Role Code
                        //var prServerdetails = Facade.GetServersListByUserinfraId(LoggedInUserId, LoggedInUserCompanyId, role);

                        //if (prServerdetails != null)
                        //{
                        //    ddlServerPr.DataSource = prServerdetails;
                        //    ddlServerPr.DataTextField = "Name";
                        //    ddlServerPr.DataValueField = "Id";
                        //    ddlServerPr.DataBind();
                        //    ddlServerPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
                        //    if (chkDRReady.Checked)
                        //    {
                        //        var DrrServerdetails = Facade.GetServersListByUserinfraId(LoggedInUserId, LoggedInUserCompanyId, role);
                        //        ddlServerDr.DataSource = DrrServerdetails;
                        //        ddlServerDr.DataTextField = "Name";
                        //        ddlServerDr.DataValueField = "Id";
                        //        ddlServerDr.DataBind();
                        //        ddlServerDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
                        //    }
                        //}
                        #endregion
                        Utility.PopulateServerByTypeAndAdminRole(ddlServerPr, "DR", LoggedInUserId, LoggedInUserCompanyId, role, true);
                        if (chkDRReady.Checked)
                            Utility.PopulateServerByTypeAndAdminRole(ddlServerDr, "PR", LoggedInUserId, LoggedInUserCompanyId, role, true);

                        var replidetails = Facade.GetInfraSummeryReplicationByLoginId(LoggedInUserId);
                        if (replidetails != null)
                        {

                            ddlReplicationPr.DataSource = replidetails;
                            ddlReplicationPr.DataTextField = "Name";
                            ddlReplicationPr.DataValueField = "Id";
                            ddlReplicationPr.DataBind();
                            ddlReplicationPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
                            if (chkDRReady.Checked)
                            {
                                ddlReplicationDr.DataSource = replidetails;
                                ddlReplicationDr.DataTextField = "Name";
                                ddlReplicationDr.DataValueField = "Id";
                                ddlReplicationDr.DataBind();
                                ddlReplicationDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
                            }
                        }

                    }
                    tdDataBaseComponant.Visible = false;
                    tdProductionDataBase.Visible = false;
                    tdDrDatabase.Visible = false;



                }
                if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.DB).ToString())
                {
                    divCommand.Visible = false;
                    //divOutPut.Visible = false;

                    tdDataBaseComponant.Visible = true;
                    tdProductionDataBase.Visible = true;
                    tdDrDatabase.Visible = true;

                    ////  Utility.PopulateServerByType(ddlServerPr, GroupServerType.PRDBServer.ToString(), "DR", true);
                    //Utility.PopulateServerByTypeAndRolewithCompanyId(ddlServerPr, "DR", LoggedInUserCompanyId, IsSuperAdmin, _isParent, true);
                    //if (chkDRReady.Checked)
                    //    //   Utility.PopulateServerByType(ddlServerDr, GroupServerType.DRDBServer.ToString(), "PR", true);
                    //    Utility.PopulateServerByTypeAndRolewithCompanyId(ddlServerDr, "PR", LoggedInUserCompanyId, IsSuperAdmin, _isParent, true);

                    //var replicationtype = (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                    //Utility.PopulateReplicationBaseByType(ddlReplicationPr, replicationtype, true);
                    //// Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, replicationtype,true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    //if (chkDRReady.Checked)
                    //    // Utility.PopulateReplicationBaseByType(ddlReplicationDr, replicationtype, true);
                    //    Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, replicationtype, true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);

                    if (LoggedInUserRole == UserRole.SuperAdmin)
                    {
                        Utility.PopulateServerByTypeAndRolewithCompanyId(ddlServerPr, "DR", LoggedInUserCompanyId, IsSuperAdmin, _isParent, true);
                        if (chkDRReady.Checked)
                            Utility.PopulateServerByTypeAndRolewithCompanyId(ddlServerDr, "PR", LoggedInUserCompanyId, IsSuperAdmin, _isParent, true);

                        var replicationtype = (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                        Utility.PopulateReplicationBaseByType(ddlReplicationPr, replicationtype, true);
                        if (chkDRReady.Checked)
                            Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, replicationtype, true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    }
                    else
                    {
                        #region Old Role Code
                        //var prServerdetails = Facade.GetServersListByUserinfraId(LoggedInUserId, LoggedInUserCompanyId, role);

                        //if (prServerdetails != null)
                        //{
                        //    ddlServerPr.DataSource = prServerdetails;
                        //    ddlServerPr.DataTextField = "Name";
                        //    ddlServerPr.DataValueField = "Id";
                        //    ddlServerPr.DataBind();
                        //    ddlServerPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
                        //    if (chkDRReady.Checked)
                        //    {
                        //        var DrrServerdetails = Facade.GetServersListByUserinfraId(LoggedInUserId, LoggedInUserCompanyId, role);
                        //        ddlServerDr.DataSource = DrrServerdetails;
                        //        ddlServerDr.DataTextField = "Name";
                        //        ddlServerDr.DataValueField = "Id";
                        //        ddlServerDr.DataBind();
                        //        ddlServerDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
                        //    }
                        //}
                        #endregion

                        Utility.PopulateServerByTypeAndAdminRole(ddlServerPr, "DR", LoggedInUserId, LoggedInUserCompanyId, role, true);
                        if (chkDRReady.Checked)
                            Utility.PopulateServerByTypeAndAdminRole(ddlServerDr, "PR", LoggedInUserId, LoggedInUserCompanyId, role, true);

                        var replidetails = Facade.GetInfraSummeryReplicationByLoginId(LoggedInUserId);
                        if (replidetails != null)
                        {
                            //var prreplidetails = (from p in replidetails where (p.CreatorId == LoggedInUserId) select p).ToList();
                            //if (prreplidetails != null)
                            //{
                            ddlReplicationPr.DataSource = replidetails;
                            ddlReplicationPr.DataTextField = "Name";
                            ddlReplicationPr.DataValueField = "Id";
                            ddlReplicationPr.DataBind();
                            //ddlReplicationPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
                            if (chkDRReady.Checked)
                            {
                                //var Drreplidetails = (from p in replidetails where (p.CreatorId == LoggedInUserId) select p).ToList();
                                ddlReplicationDr.DataSource = replidetails;
                                ddlReplicationDr.DataTextField = "Name";
                                ddlReplicationDr.DataValueField = "Id";
                                ddlReplicationDr.DataBind();
                                //ddlReplicationDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
                            }
                            //}

                        }

                        //var replicationtype = (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                        //Utility.PopulateReplicationBaseByType(ddlReplicationPr, replicationtype, true);
                        //if (chkDRReady.Checked)
                        //    Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, replicationtype, true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);

                    }
                }
                if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.Virtual).ToString())
                {
                    divCommand.Visible = false;
                    //divOutPut.Visible = false;

                    tdDataBaseComponant.Visible = false;
                    tdProductionDataBase.Visible = false;
                    tdDrDatabase.Visible = false;

                    //Utility.PopulateServerByType(ddlServerPr, GroupServerType.PRDBServer.ToString(), "DR", true);
                    //if (chkDRReady.Checked)
                    //    Utility.PopulateServerByType(ddlServerDr, GroupServerType.DRDBServer.ToString(), "PR", true);

                    //var replicationtype =
                    //    (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                    //Utility.PopulateReplicationBaseByType(ddlReplicationPr, replicationtype, true);
                    //if (chkDRReady.Checked)
                    //    Utility.PopulateReplicationBaseByType(ddlReplicationDr, replicationtype, true);

                    if (LoggedInUserRole == UserRole.SuperAdmin)
                    {
                        Utility.PopulateServerByType(ddlServerPr, GroupServerType.PRDBServer.ToString(), "DR", true);
                        if (chkDRReady.Checked)
                            Utility.PopulateServerByType(ddlServerDr, GroupServerType.DRDBServer.ToString(), "PR", true);

                        var replicationtype =
                            (ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                        Utility.PopulateReplicationBaseByType(ddlReplicationPr, replicationtype, true);
                        if (chkDRReady.Checked)
                            Utility.PopulateReplicationBaseByType(ddlReplicationDr, replicationtype, true);

                    }
                    else
                    {
                        #region Old Role Code
                        //var prServerdetails = Facade.GetServersListByUserinfraId(LoggedInUserId, LoggedInUserCompanyId, role);
                        //if (prServerdetails != null)
                        //{
                        //    ddlServerPr.DataSource = prServerdetails;
                        //    ddlServerPr.DataTextField = "Name";
                        //    ddlServerPr.DataValueField = "Id";
                        //    ddlServerPr.DataBind();
                        //    ddlServerPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
                        //    if (chkDRReady.Checked)
                        //    {
                        //        var DrrServerdetails = Facade.GetServersListByUserinfraId(LoggedInUserId, LoggedInUserCompanyId, role);
                        //        ddlServerDr.DataSource = DrrServerdetails;
                        //        ddlServerDr.DataTextField = "Name";
                        //        ddlServerDr.DataValueField = "Id";
                        //        ddlServerDr.DataBind();
                        //        ddlServerDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
                        //    }
                        //}
                        #endregion

                        Utility.PopulateServerByTypeAndAdminRole(ddlServerPr, "DR", LoggedInUserId, LoggedInUserCompanyId, role, true);
                        if (chkDRReady.Checked)
                            Utility.PopulateServerByTypeAndAdminRole(ddlServerDr, "PR", LoggedInUserId, LoggedInUserCompanyId, role, true);


                        var replidetails = Facade.GetInfraSummeryReplicationByLoginId(LoggedInUserId);
                        if (replidetails != null)
                        {
                            //var prreplidetails = (from p in replidetails where (p.CreatorId == LoggedInUserId) select p).ToList();
                            //if (prreplidetails != null)
                            //{
                            ddlReplicationPr.DataSource = replidetails;
                            ddlReplicationPr.DataTextField = "Name";
                            ddlReplicationPr.DataValueField = "Id";
                            ddlReplicationPr.DataBind();
                            ddlReplicationPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
                            if (chkDRReady.Checked)
                            {
                                //var Drreplidetails = (from p in replidetails where (p.CreatorId == LoggedInUserId) select p).ToList();
                                ddlReplicationDr.DataSource = replidetails;
                                ddlReplicationDr.DataTextField = "Name";
                                ddlReplicationDr.DataValueField = "Id";
                                ddlReplicationDr.DataBind();
                                ddlReplicationDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
                            }
                        }

                        //}

                    }

                }
            }

            if (ddlServerDr.Items.Count > 0 && ddlServerDr.Items[0].Value == "0")
            {
                ddlServerDr.Items[0].Value = "000";
            }
            else if (ddlServerDr.Items.Count <= 0)
            {
                ddlServerDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
            }

            if (ddlServerPr.Items.Count > 0 && ddlServerPr.Items[0].Value == "0")
            {
                ddlServerPr.Items[0].Value = "000";
            }
            else if (ddlServerPr.Items.Count <= 0)
            {
                ddlServerPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
            }

            if (ddlReplicationDr.Items.Count > 0 && ddlReplicationDr.Items[0].Value == "0")
            {
                ddlReplicationDr.Items[0].Value = "000";
            }
            else if (ddlReplicationDr.Items.Count <= 0)
            {
                ddlReplicationDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
            }

            if (ddlReplicationPr.Items.Count > 0 && ddlReplicationPr.Items[0].Value == "0")
            {
                ddlReplicationPr.Items[0].Value = "000";
            }
            else if (ddlReplicationPr.Items.Count <= 0)
            {
                ddlReplicationPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
            }

        }

        protected void SetReplicationType()
        {
            var li2 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li2");
            var li3 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li3");
            var li4 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li4");
            var li5 = (HtmlGenericControl)Wizard1.FindControl("HeaderContainer").FindControl("li5");
            if (ddlReplicationType.SelectedItem.Value != "000")
            {
                //tdDataBaseComponant.Visible = true;
                //tdProductionDataBase.Visible = true;
                //tdDrDatabase.Visible = true;

                var replicationtype =
                    (int)(ReplicationType)(Enum.Parse(typeof(ReplicationType), ddlReplicationType.SelectedItem.Value));
                if (ddlReplicationType.SelectedItem.Value == "22")
                {
                    replicationtype = 1;

                    // Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    if (chkDRReady.Checked)
                        //  Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                        Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    replicationtype = 22;
                }


                else if (ddlReplicationType.SelectedItem.Value == ((int)ReplicationType.AvamarReplication).ToString())
                {
                    Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    if (chkDRReady.Checked)
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                }
                else if (ddlReplicationType.SelectedItem.Value == "31")
                {
                    replicationtype = 1;

                    //Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    if (chkDRReady.Checked)
                        //  Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                        Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    replicationtype = 31;
                }
                else if (ddlReplicationType.SelectedItem.Value == "13")
                {
                    replicationtype = 3;

                    // Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    if (chkDRReady.Checked)
                        // Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                        Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    replicationtype = 13;
                }
                else if (ddlReplicationType.SelectedItem.Value == "62")
                {
                    tdReplicationComponant.InnerHtml = "SRM Server<span class='inactive'>*</span>";
                    tdProductionReplication.Visible = false;
                    ddlReplicationPr.Visible = false;

                    tdDrReplication.Visible = false;
                    ddlReplicationDr.Visible = false;

                    tdProductionSecondServer.Visible = true;
                    ddlServerPr2.Visible = true;
                    Utility.PopulateServerByType(ddlServerPr2, GroupServerType.PRAppServer.ToString(), "DR", true);

                    TdServerDRSecondServer.Visible = true;
                    ddlServerDr2.Visible = true;
                    Utility.PopulateServerByType(ddlServerDr2, GroupServerType.PRAppServer.ToString(), "PR", true);
                }
                else if (ddlReplicationType.SelectedItem.Value == "154")
                {

                    tdServerComponant.InnerHtml = "Server<span class='inactive'>*</span>";
                    SRMserver.InnerHtml = "SRM Server<span class='inactive'>*</span>";
                    //tdProductionReplication.Visible = false;
                    //ddlReplicationPr.Visible = false;

                    //tdDrReplication.Visible = false;
                    //ddlReplicationDr.Visible = false;

                    tdProductionSecondServer.Visible = true;
                    ddlServerPr2.Visible = true;
                    Utility.PopulateServerByType(ddlServerPr2, GroupServerType.PRAppServer.ToString(), "DR", true);

                    TdServerDRSecondServer.Visible = true;
                    ddlServerDr2.Visible = true;
                    Utility.PopulateServerByType(ddlServerDr2, GroupServerType.PRAppServer.ToString(), "PR", true);


                    tdProductionReplication.Visible = true;
                    ddlReplicationPr.Visible = true;
                    Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);

                    tdDrReplication.Visible = true;
                    ddlReplicationDr.Visible = true;
                    if (chkDRReady.Checked)
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);


                    //replicationtype = 148;

                    //tdReplicationComponant.InnerHtml = "SRM Server<span class='inactive'>*</span>";
                    //tdProductionReplication.Visible = false;
                    //ddlReplicationPr.Visible = false;

                    //tdDrReplication.Visible = false;
                    //ddlReplicationDr.Visible = false;

                    //tdProductionSecondServer.Visible = true;
                    //ddlServerPr2.Visible = true;
                    //Utility.PopulateServerByType(ddlServerPr2, GroupServerType.PRAppServer.ToString(), "DR", true);

                    //TdServerDRSecondServer.Visible = true;
                    //ddlServerDr2.Visible = true;
                    //Utility.PopulateServerByType(ddlServerDr2, GroupServerType.PRAppServer.ToString(), "PR", true);
                    ////tdProductionReplication.Visible = true;
                    ////ddlReplicationPr.Visible = true;
                    ////ddlReplicationDr.Visible = true;

                    ////tdProductionReplication.Visible = true;
                    ////tdDrReplication.Visible = true;
                    ////Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    ////if (chkDRReady.Checked)
                    ////    Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                }
                else if (ddlReplicationType.SelectedItem.Value == "134")
                {
                    Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    if (chkDRReady.Checked)
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                }

                else if (ddlReplicationType.SelectedItem.Value == "133")
                {
                    Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    if (chkDRReady.Checked)
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                }
                else if (ddlReplicationType.SelectedItem.Value == "64")
                {
                    Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    if (chkDRReady.Checked)
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                }
                else if (ddlReplicationType.SelectedItem.Value == "68")
                {
                    Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    if (chkDRReady.Checked)
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                }

                else if (ddlReplicationType.SelectedItem.Value == "106")
                {
                    tdDataBaseComponant.Visible = true;
                    tdProductionDataBase.Visible = true;
                    tdDrDatabase.Visible = true;
                    //Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    //if (chkDRReady.Checked)
                    //    Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);

                    Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    if (chkDRReady.Checked)
                        Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);

                }
                else if (ddlReplicationType.SelectedItem.Value == "122")
                {
                    var ServerList = Facade.GetAllServers();
                    if (ServerList != null)
                    {
                        var PrServerList = from a in ServerList where a.Type == "PRNutanixLeapServer" select a;
                        ddlServerPr.DataSource = PrServerList;
                        ddlServerPr.DataTextField = "Name";
                        ddlServerPr.DataValueField = "Id";
                        ddlServerPr.DataBind();
                        ddlServerPr.Items.Insert(0, new ListItem("- Select Production Server Name -", "000"));

                        var drServerList = from a in ServerList where a.Type == "DRNutanixLeapServer" select a;
                        ddlServerDr.DataSource = drServerList;
                        ddlServerDr.DataTextField = "Name";
                        ddlServerDr.DataValueField = "Id";
                        ddlServerDr.DataBind();
                        ddlServerDr.Items.Insert(0, new ListItem("- Select DR Server Name -", "000"));

                        Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);

                    }
                }

                else if (ddlReplicationType.SelectedItem.Value == "154")
                {
                    SRMserver.Visible = true;
                    tdServerComponant.InnerHtml = "Server<span class='inactive'>*</span>";
                    SRMserver.InnerHtml = "SRM Server<span class='inactive'>*</span>";
                    //tdProductionReplication.Visible = false;
                    //ddlReplicationPr.Visible = false;

                    //tdDrReplication.Visible = false;
                    //ddlReplicationDr.Visible = false;

                    tdProductionSecondServer.Visible = true;
                    ddlServerPr2.Visible = true;
                    Utility.PopulateServerByType(ddlServerPr2, GroupServerType.PRAppServer.ToString(), "DR", true);

                    TdServerDRSecondServer.Visible = true;
                    ddlServerDr2.Visible = true;
                    Utility.PopulateServerByType(ddlServerDr2, GroupServerType.PRAppServer.ToString(), "PR", true);


                    tdProductionReplication.Visible = true;
                    ddlReplicationPr.Visible = true;
                    Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);

                    tdDrReplication.Visible = true;
                    ddlReplicationDr.Visible = true;
                    if (chkDRReady.Checked)
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                }

                else if (ddlReplicationType.SelectedItem.Value == "129")
                {
                    var ServerList = Facade.GetAllServers();
                    if (ServerList != null)
                    {
                        var PrServerList = from a in ServerList where a.Type == "PRNutanixServer" select a;
                        ddlServerPr.DataSource = PrServerList;
                        ddlServerPr.DataTextField = "Name";
                        ddlServerPr.DataValueField = "Id";
                        ddlServerPr.DataBind();
                        ddlServerPr.Items.Insert(0, new ListItem("- Select Production Server Name -", "000"));

                        var drServerList = from a in ServerList where a.Type == "DRNutanixServer" select a;
                        ddlServerDr.DataSource = drServerList;
                        ddlServerDr.DataTextField = "Name";
                        ddlServerDr.DataValueField = "Id";
                        ddlServerDr.DataBind();
                        ddlServerDr.Items.Insert(0, new ListItem("- Select DR Server Name -", "000"));

                        Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);

                    }
                }
                else if (ddlReplicationType.SelectedItem.Value == "152")
                {
                    var ServerList = Facade.GetAllServers();
                    if (ServerList != null)
                    {
                        var PrServerList = from a in ServerList where a.Type == "PRRHVServer" select a;
                        ddlServerPr.DataSource = PrServerList;
                        ddlServerPr.DataTextField = "Name";
                        ddlServerPr.DataValueField = "Id";
                        ddlServerPr.DataBind();
                        ddlServerPr.Items.Insert(0, new ListItem("- Select Production Server Name -", "000"));

                        var drServerList = from a in ServerList where a.Type == "DRRHVServer" select a;
                        ddlServerDr.DataSource = drServerList;
                        ddlServerDr.DataTextField = "Name";
                        ddlServerDr.DataValueField = "Id";
                        ddlServerDr.DataBind();
                        ddlServerDr.Items.Insert(0, new ListItem("- Select DR Server Name -", "000"));

                        Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                        Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);

                    }
                }
                else if (ddlReplicationType.SelectedItem.Value == "90")
                {

                    var repListunsorted = Facade.GetEmcmirrorviewDetailsbyServerType123("PR");
                    if (repListunsorted != null && repListunsorted.Count > 0)
                    {
                        var repList = repListunsorted.OrderBy(o => o.Name).ToList();

                        ddlReplicationPr.DataSource = repList;
                        ddlReplicationPr.DataTextField = "Name";
                        ddlReplicationPr.DataValueField = "Id";
                        ddlReplicationPr.DataBind();
                        //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        //ddlReplicationPr.SelectedValue = "0";
                        ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));

                    }
                    else
                    {
                        //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -","0",true));
                        //ddlReplicationPr.SelectedValue = "0";
                        ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                        //ddlReplicationPr.SelectedValue = "- Select Replication Name -";
                    }


                    var repListunsortedDR = Facade.GetEmcmirrorviewDetailsbyServerType123("DR");
                    if (repListunsortedDR != null && repListunsortedDR.Count > 0)
                    {
                        var repListDR = repListunsortedDR.OrderBy(o => o.Name).ToList();

                        ddlReplicationDr.DataSource = repListDR;
                        ddlReplicationDr.DataTextField = "Name";
                        ddlReplicationDr.DataValueField = "Id";
                        ddlReplicationDr.DataBind();
                        //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        //ddlReplicationDr.SelectedValue = "0";
                        ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));

                    }
                    else
                    {
                        //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        //ddlReplicationDr.SelectedValue = "0";
                        ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                        // ddlReplicationDr.SelectedValue = "- Select Replication Name -";
                    }
                }

                else if (ddlReplicationType.SelectedItem.Value == "91")
                {

                    var repListunsorted = Facade.GetEmcmirrorviewDetailsbyServerType123("PR");
                    if (repListunsorted != null && repListunsorted.Count > 0)
                    {
                        var repList = repListunsorted.OrderBy(o => o.Name).ToList();


                        ddlReplicationPr.DataSource = repList;
                        ddlReplicationPr.DataTextField = "Name";
                        ddlReplicationPr.DataValueField = "Id";
                        ddlReplicationPr.DataBind();
                        // ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                        //ddlReplicationPr.SelectedValue = "0";

                    }
                    else
                    {

                        //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -","0",true));
                        //ddlReplicationPr.SelectedValue = "0";
                        ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                        // ddlReplicationPr.SelectedValue = "- Select Replication Name -";
                    }


                    var repListunsortedDR = Facade.GetEmcmirrorviewDetailsbyServerType123("DR");
                    if (repListunsortedDR != null && repListunsortedDR.Count > 0)
                    {
                        var repListDR = repListunsortedDR.OrderBy(o => o.Name).ToList();

                        ddlReplicationDr.DataSource = repListDR;
                        ddlReplicationDr.DataTextField = "Name";
                        ddlReplicationDr.DataValueField = "Id";
                        ddlReplicationDr.DataBind();
                        ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                        //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        //ddlReplicationDr.SelectedValue = "0";

                    }
                    else
                    {
                        //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        //ddlReplicationDr.SelectedValue = "0";
                        ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                        // ddlReplicationDr.SelectedValue = "- Select Replication Name -";
                    }
                }

                else if (ddlReplicationType.SelectedItem.Value == "92")
                {

                    var repListunsorted = Facade.GetEmcmirrorviewDetailsbyServerType123("PR");
                    if (repListunsorted != null && repListunsorted.Count > 0)
                    {
                        var repList = repListunsorted.OrderBy(o => o.Name).ToList();

                        ddlReplicationPr.DataSource = repList;
                        ddlReplicationPr.DataTextField = "Name";
                        ddlReplicationPr.DataValueField = "Id";
                        ddlReplicationPr.DataBind();
                        //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        //ddlReplicationPr.SelectedValue = "0";
                        ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));

                    }
                    else
                    {
                        //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        //ddlReplicationPr.SelectedValue = "0";
                        ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                        // ddlReplicationPr.SelectedValue = "- Select Replication Name -";
                    }


                    var repListunsortedDR = Facade.GetEmcmirrorviewDetailsbyServerType123("DR");
                    if (repListunsortedDR != null && repListunsortedDR.Count > 0)
                    {
                        var repListDR = repListunsortedDR.OrderBy(o => o.Name).ToList();

                        ddlReplicationDr.DataSource = repListDR;
                        ddlReplicationDr.DataTextField = "Name";
                        ddlReplicationDr.DataValueField = "Id";
                        ddlReplicationDr.DataBind();
                        //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                        //ddlReplicationDr.SelectedValue = "0";
                        ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));

                    }
                    else
                    {
                        //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -","0",true));
                        //ddlReplicationDr.SelectedValue = "0";
                        ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                        // ddlReplicationDr.SelectedValue = "- Select Replication Name -";
                    }
                }

                else if (ddlReplicationType.SelectedItem.Value == "153")
                {

                    var ServerList = Facade.GetAllServers();
                    if (ServerList != null)
                    {
                        var PrServerList = from a in ServerList where a.Type == "OPENSHIFTCLUSTERSERVER" select a;
                        ddlServerPr.DataSource = PrServerList;
                        ddlServerPr.DataTextField = "Name";
                        ddlServerPr.DataValueField = "Id";
                        ddlServerPr.DataBind();
                        ddlServerPr.Items.Insert(0, new ListItem("- Select Production Server Name -", "000"));

                        var drServerList = from a in ServerList where a.Type == "OPENSHIFTAPISERVER" select a;
                        ddlServerDr.DataSource = drServerList;
                        ddlServerDr.DataTextField = "Name";
                        ddlServerDr.DataValueField = "Id";
                        ddlServerDr.DataBind();
                        ddlServerDr.Items.Insert(0, new ListItem("- Select DR Server Name -", "000"));

                        var repListunsorted = Facade.GetAllReplicationBase();
                        if (repListunsorted != null && repListunsorted.Count > 0)
                        {
                            var repList = (from rep in repListunsorted where rep.Type.ToString() == "OpenShift" select rep).OrderBy(rep => rep.Name).ToList();


                            ddlReplicationPr.DataSource = repList;
                            ddlReplicationPr.DataTextField = "Name";
                            ddlReplicationPr.DataValueField = "Id";
                            ddlReplicationPr.DataBind();
                            //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                            //ddlReplicationPr.SelectedValue = "0";
                            ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));

                        }
                        else
                        {
                            //ddlReplicationPr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                            //ddlReplicationPr.SelectedValue = "0";
                            ddlReplicationPr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                            // ddlReplicationPr.SelectedValue = "- Select Replication Name -";
                        }


                        var repListunsortedDR = Facade.GetAllReplicationBase();
                        if (repListunsortedDR != null && repListunsortedDR.Count > 0)
                        {
                            var repListDR = (from rep in repListunsorted where rep.Type.ToString() == "Openshift" select rep).OrderBy(rep => rep.Name).ToList();

                            ddlReplicationDr.DataSource = repListDR;
                            ddlReplicationDr.DataTextField = "Name";
                            ddlReplicationDr.DataValueField = "Id";
                            ddlReplicationDr.DataBind();
                            //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -", "0", true));
                            //ddlReplicationDr.SelectedValue = "0";
                            ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));

                        }
                        else
                        {
                            //ddlReplicationDr.Items.Add(new ListItem("- Select Replication Name -","0",true));
                            //ddlReplicationDr.SelectedValue = "0";
                            ddlReplicationDr.Items.Insert(0, new ListItem("- Select Replication Name -", "000"));
                            // ddlReplicationDr.SelectedValue = "- Select Replication Name -";
                        }
                    }
                }

                else
                {
                    //Existing Code commented by meenakshi on 3-May-2018 10:47AM
                    ////////Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    //////Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    //////if (chkDRReady.Checked)
                    //////// Utility.PopulateReplicationBaseByType(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);
                    //////Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                    //Utility.PopulateReplicationBaseByType(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true);

                    if (IsSuperAdmin)
                    {
                        Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                        if (chkDRReady.Checked)
                            Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationDr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);

                    }
                    else
                    {
                        var PRreplilist = Facade.GetInfraSummeryReplicationByLoginId(_currentLoggedUserId);
                        if (PRreplilist != null && PRreplilist.Count > 0)
                        {
                            var repListPR = PRreplilist.OrderBy(o => o.Name).ToList();
                            ddlReplicationPr.DataSource = repListPR;
                            ddlReplicationPr.DataTextField = "Name";
                            ddlReplicationPr.DataValueField = "Id";
                            ddlReplicationPr.DataBind();
                        }
                        ddlReplicationPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
                        //Utility.PopulateReplicationBaseByTypeAndRolewithCompanyId(ddlReplicationPr, (ReplicationType)(Enum.Parse(typeof(ReplicationType), replicationtype.ToString())), true, LoggedInUserCompanyId, IsSuperAdmin, _isParent);
                        if (chkDRReady.Checked)
                        {
                            var DRreplilist = Facade.GetInfraSummeryReplicationByLoginId(_currentLoggedUserId);
                            if (DRreplilist != null && DRreplilist.Count > 0)
                            {
                                var repListDR = DRreplilist.OrderBy(o => o.Name).ToList();
                                ddlReplicationDr.DataSource = repListDR;
                                ddlReplicationDr.DataTextField = "Name";
                                ddlReplicationDr.DataValueField = "Id";
                                ddlReplicationDr.DataBind();
                            }
                            ddlReplicationDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
                        }
                    }
                }


                li2.Visible = false;
                li3.Visible = false;
                li4.Visible = false;
                li5.Visible = false;

                // tdProductionDataBase.Visible = false;
                //  tdDrDatabase.Visible = false;
                //tdProductionExchaneDAGMailBox.Visible = false;
                //tdDrExchaneDAGMailBox.Visible = false;
                //tdProductionReplication.Visible = false;
                //tdDrReplication.Visible = false;
                //tdReplicationComponant.Visible = false;
                //tdDataBaseComponant.Visible = false;
                //tdMailboxComponent.Visible = false;
                //trSummaryDataBase.Visible = false;
                //pnlSummary.Visible = false;
                //divCommand.Visible = false;
                //divDrMonitorWorkflow.Visible = false;
                //trDrDR.Visible = false;
                //trDr.Visible = false;

                switch (replicationtype)
                {
                    case (int)ReplicationType.OracleDataGuard:
                    case (int)ReplicationType.AvamarReplication:
                    case (int)ReplicationType.AzureGatewayReplication:
                    case (int)ReplicationType.OracleCloudReplication:
                    case (int)ReplicationType.RackWareReplication:
                    case (int)ReplicationType.DellEMCCyberRecoverVaultReplication:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.DataSync:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCSRDF:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;
                    case (int)ReplicationType.DB2IBMGLobalmirror:
                    case (int)ReplicationType.IBMGlobalMirror:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;

                        // ddlReplicationDr.Attributes.Add("disabled", "true");
                        //ddlDatabaseDr.Attributes.Add("disabled", "true");

                        //RequiredFieldValidator13.Enabled = false;
                        //RequiredFieldValidator12.Enabled = false;
                        //tdDataBaseComponant.Visible = false;
                        //tdProductionDataBase.Visible = false;
                        //tdDrDatabase.Visible = false;
                        break;

                    case (int)ReplicationType.HITACHITrueCopy:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.HITACHIUROracleFullDB:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        break;

                    case (int)ReplicationType.SqlServerDataSync:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        //tdReplicationComponant.Visible = false;
                        //tdProductionReplication.Visible = false;
                        //tdDrReplication.Visible = false;
                        break;

                    case (int)ReplicationType.EnterPriseDB:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdReplicationComponant.Visible = false;
                        tdProductionReplication.Visible = false;
                        tdDrReplication.Visible = false;

                        break;

                    case (int)ReplicationType.Postgres9X:
                    case (int)ReplicationType.Postgres10:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdReplicationComponant.Visible = false;
                        tdProductionReplication.Visible = false;
                        tdDrReplication.Visible = false;
                        break;

                    case (int)ReplicationType.MSSQLServerNative:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = true;
                        li5.Visible = false;
                        tdReplicationComponant.Visible = false;
                        tdProductionReplication.Visible = false;
                        tdDrReplication.Visible = false;
                        break;

                    case (int)ReplicationType.MSSCR:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.NetAppSnapMirror:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.OracleWithDataSync:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.DB2HADR:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.OracleCloudDataGuard:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.SAPHANADBReplication:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCSRDFCGAPPLICATION:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.HITACHIUROracleLogShipping:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        break;

                    case (int)ReplicationType.EMCSRDFOracleFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;
                    case (int)ReplicationType.EMCSRDFMSSQLFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;


                    case (int)ReplicationType.RPForVMWithMSSQLFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.RPForVMReplication:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                        
                    case (int)ReplicationType.ZertoMssqlFULLDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCSRDFOracleLogShipping:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.VMWareGlobalMirror:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        //tdDataBaseComponant.Visible = false;
                        //tdProductionDataBase.Visible = false;
                        //tdDrDatabase.Visible = false;
                        trSummaryDataBase.Visible = false;
                        break;

                    case (int)ReplicationType.VMWareDataSync:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdDataBaseComponant.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        trSummaryDataBase.Visible = false;

                        break;

                    case (int)ReplicationType.VMWareHitachiUR:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdDataBaseComponant.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        trSummaryDataBase.Visible = false;
                        break;

                    case (int)ReplicationType.MongoDB:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdReplicationComponant.Visible = false;
                        tdProductionReplication.Visible = false;
                        tdDrReplication.Visible = false;
                        break;

                    case (int)ReplicationType.VMWareSnapMirror:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdDataBaseComponant.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        trSummaryDataBase.Visible = false;
                        break;

                    case (int)ReplicationType.MSExchangeDAG:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        tdProductionExchaneDAGMailBox.Visible = true;
                        tdDrExchaneDAGMailBox.Visible = true;
                        tdProductionReplication.Visible = true;
                        tdDrReplication.Visible = false;
                        tdReplicationComponant.Visible = true;
                        tdDataBaseComponant.Visible = false;
                        tdMailboxComponent.Visible = true;
                        break;

                    case (int)ReplicationType.DB2DataSync:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.MySQLGlobalMirrorFullDB:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdDataBaseComponant.Visible = true;
                        tdProductionDataBase.Visible = true;
                        tdDrDatabase.Visible = true;



                        break;

                    case (int)ReplicationType.MySQLNative:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        pnlSummary.Visible = true;
                        tdDataBaseComponant.Visible = true;
                        tdProductionDataBase.Visible = true;
                        tdDrDatabase.Visible = true;
                        tdReplicationComponant.Visible = false;
                        tdProductionReplication.Visible = false;
                        tdDrReplication.Visible = false;
                        break;

                    case (int)ReplicationType.DB2HADR9X:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EC2S3DataSync:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        RequiredFieldValidator11.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;
                        ddlServerDr.Attributes.Add("disabled", "true");
                        ddlReplicationDr.Attributes.Add("disabled", "true");
                        //trDrDR.Visible = false;
                        //tdDrReplication.Visible = false;
                        break;

                    case (int)ReplicationType.MSSqlNetAppSnapMirror:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        //RequiredFieldValidator11.Enabled = false;
                        //RequiredFieldValidator12.Enabled = false;
                        //RequiredFieldValidator13.Enabled = false;
                        //ddlServerDr.Attributes.Add("disabled", "true");
                        //ddlReplicationDr.Attributes.Add("disabled", "true");
                        //ddlDatabaseDr.Attributes.Add("disabled","true");
                        break;
                    case (int)ReplicationType.MySQLNetAppSnapMirror:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;


                    case (int)ReplicationType.MSSQLDoubleTakeFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        //RequiredFieldValidator10.Enabled = true;
                        //RequiredFieldValidator13.Enabled = true;
                        //tdReplicationComponant.Visible = false;
                        //tdProductionReplication.Visible = false;
                        //tdDrReplication.Visible = false;
                        break;

                    case (int)ReplicationType.OracleFullDBNetAppSnapMirror:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    //case (int)ReplicationType.MSSQLDataBaseMirror:
                    //    li2.Visible = true;
                    //    li3.Visible = false;
                    //    li4.Visible = false;
                    //    li5.Visible = false;
                    //    RequiredFieldValidator10.Enabled = false;
                    //    RequiredFieldValidator13.Enabled = false;
                    //    ddlReplicationPr.Attributes.Add("disabled", "true");
                    //    ddlReplicationDr.Attributes.Add("disabled", "true");
                    //    break;

                    case (int)ReplicationType.EMCSRDFVMAXOracleFULLDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCSRDFDMXOracleFULLDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCSRDFVMAXMSSQLFULLDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCSRDFDMXMSSQLFULLDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.SyBaseWithDataSync:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.SybaseWithSRS:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.ApplicationDoubleTake:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        divCommand.Visible = false;
                        divDrMonitorWorkflow.Visible = false;
                        //RequiredFieldValidator10.Enabled = true;
                        //RequiredFieldValidator13.Enabled = true;
                        break;

                    case (int)ReplicationType.MIMIX:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        //RequiredFieldValidator10.Enabled = false;
                        //RequiredFieldValidator13.Enabled = false;
                        break;

                    case (int)ReplicationType.HitachiOracleFullDBRac:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        break;

                    case (int)ReplicationType.HitachiUrDB2FullDB:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        break;

                    case (int)ReplicationType.HitachiUrMySqlFullDB:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        break;

                    case (int)ReplicationType.EMCSRDFOracleRacFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCSRDFSyBase:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.HitachiSyabse:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        break;

                    case (int)ReplicationType.HyperV:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdDataBaseComponant.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        trSummaryDataBase.Visible = false;
                        break;

                    case (int)ReplicationType.AppHitachiUr:
                        //li2.Visible = true;
                        //li3.Visible = false;
                        //li4.Visible = false;
                        //li5.Visible = false;
                        //break;
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        break;


                    case (int)ReplicationType.MySqlNativeLogShipping:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        pnlSummary.Visible = true;
                        tdDataBaseComponant.Visible = true;
                        tdProductionDataBase.Visible = true;
                        tdDrDatabase.Visible = true;
                        tdReplicationComponant.Visible = true;
                        tdProductionReplication.Visible = true;
                        tdDrReplication.Visible = true;
                        break;

                    case (int)ReplicationType.CloudantDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        pnlSummary.Visible = true;
                        lblServerName.Text = "Load Balancer Node";


                        tdDataBaseComponant.Visible = true;
                        tdProductionDataBase.Visible = true;
                        tdDrDatabase.Visible = true;
                        tdReplicationComponant.Visible = true;
                        tdProductionReplication.Visible = true;
                        tdDrReplication.Visible = true;

                        //tdServerComponant.InnerText = "Load Balancer Node";
                        //tdServerComponant.Visible = true;

                        lblServerName.Visible = true;
                        tdClusterServer.Visible = false;

                        break;

                    case (int)ReplicationType.GoldenGateRepli:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        pnlSummary.Visible = true;
                        tdDataBaseComponant.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        tdReplicationComponant.Visible = true;
                        tdProductionReplication.Visible = true;
                        tdDrReplication.Visible = true;
                        trSummaryDataBase.Visible = false;
                        break;

                    case (int)ReplicationType.SVCGlobalMirrorORMetroMirror:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;
                    case (int)ReplicationType.SVCGlobalMirrorOracleFullDBRac:
                    case (int)ReplicationType.OracleFullDBSVCGlobalMirror:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.MSSQLDBMirroring:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;


                    case (int)ReplicationType.SVCMSSQLFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.VMWareWithSVC:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdDataBaseComponant.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        trSummaryDataBase.Visible = false;
                        break;

                    case (int)ReplicationType.HitachiURMSSQLFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.GlobalMirrorMSSQLFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.SRMVMware:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCSRDFMysqlFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.MaxDBWithDataSync:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;


                    case (int)ReplicationType.DRNET:
                        li2.Visible = false;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = true;
                        //RequiredFieldValidator11.Enabled = false;
                        //RequiredFieldValidator13.Enabled = false;
                        //ddlServerDr.Attributes.Add("disabled", "true");
                        //ddlReplicationDr.Attributes.Add("disabled", "true");

                        break;



                    case (int)ReplicationType.TPCR:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        tdDataBaseComponant.Visible = false;
                        RequiredFieldValidator9.Enabled = false;
                        RequiredFieldValidator12.Enabled = false;
                        break;

                    case (int)ReplicationType.EmcMirrorViewOracleFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        RequiredFieldValidator10.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;
                        break;

                    case (int)ReplicationType.EmcMirrorViewSybaseFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        RequiredFieldValidator10.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;
                        break;

                    case (int)ReplicationType.EmcMirrorViewApp:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        divCommand.Visible = false;
                        divDrMonitorWorkflow.Visible = false;
                        RequiredFieldValidator10.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;

                        break;

                    case (int)ReplicationType.RoboCopy:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        tdDataBaseComponant.Visible = false;
                        RequiredFieldValidator9.Enabled = false;
                        RequiredFieldValidator12.Enabled = false;
                        break;

                    case (int)ReplicationType.SybaseWithRSHADR:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EmcUnityApp:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        RequiredFieldValidator10.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;
                        break;

                    case (int)ReplicationType.VeeamReplication:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        trDrDR.Visible = false;
                        tdDrDatabase.Visible = false;
                        tdDrReplication.Visible = false;
                        trDr.Visible = false;
                        RequiredFieldValidator10.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;
                        lblServerName.Text = "VBR Server";
                        lblPRServer.Text = "VBR Server";

                        break;

                    case (int)ReplicationType.DB2FullDBSVC:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.PostgressFullDBSVC:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.ApplicationeBDR:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.RSync:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdProductionDataBase.Visible = false;
                        tdDrDatabase.Visible = false;
                        tdDataBaseComponant.Visible = false;
                        RequiredFieldValidator9.Enabled = false;
                        RequiredFieldValidator12.Enabled = false;
                        break;
                    case (int)ReplicationType.OracleWithRSync:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.HuaweiApplication:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.HuaweiWithPostgresClusterFullDB:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.NutanixLeapReplication:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;
                    case (int)ReplicationType.ActiveDirectory:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.VmwareVsphereRepli:
                        li2.Visible = false;
                        li3.Visible = true;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.OceanstorMSSqlHuaweiDBReplication:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.NutanixProtectionDomainReplication:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.MariaDBGaleraCluster:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        tdReplicationComponant.Visible = false;
                        break;

                    case (int)ReplicationType.RecoveryAzureSite:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        RequiredFieldValidator10.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;
                        break;

                    case (int)ReplicationType.Zerto:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        RequiredFieldValidator10.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;
                        break;

                    case (int)ReplicationType.OracleFullDBIBMGlobalMirror:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        break;

                    case (int)ReplicationType.EMCDataDomainMTree:
                        li2.Visible = true;
                        li3.Visible = false;
                        li4.Visible = false;
                        li5.Visible = false;
                        RequiredFieldValidator10.Enabled = false;
                        RequiredFieldValidator13.Enabled = false;
                        break;

                }
            }

            if (ddlServerDr.Items.Count > 0 && ddlServerDr.Items[0].Value == "0")
            {
                ddlServerDr.Items[0].Value = "000";
            }
            else if (ddlServerDr.Items.Count <= 0)
            {
                ddlServerDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
            }

            if (ddlServerPr.Items.Count > 0 && ddlServerPr.Items[0].Value == "0")
            {
                ddlServerPr.Items[0].Value = "000";
            }
            else if (ddlServerPr.Items.Count <= 0)
            {
                ddlServerPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
            }

            if (ddlReplicationDr.Items.Count > 0 && ddlReplicationDr.Items[0].Value == "0")
            {
                ddlReplicationDr.Items[0].Value = "000";
            }
            else if (ddlReplicationDr.Items.Count <= 0)
            {
                ddlReplicationDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
            }

            if (ddlReplicationPr.Items.Count > 0 && ddlReplicationPr.Items[0].Value == "0")
            {
                ddlReplicationPr.Items[0].Value = "000";
            }
            else if (ddlReplicationPr.Items.Count <= 0)
            {
                ddlReplicationPr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectReplicationName, "000"));
            }

            if (ddlServerDr2.Items.Count > 0 && ddlServerDr2.Items[0].Value == "0")
            {
                ddlServerDr2.Items[0].Value = "000";
            }
            else if (ddlServerDr2.Items.Count <= 0)
            {
                ddlServerDr2.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
            }

            if (ddlServerPr2.Items.Count > 0 && ddlServerPr2.Items[0].Value == "0")
            {
                ddlServerPr2.Items[0].Value = "000";
            }
            else if (ddlServerPr2.Items.Count <= 0)
            {
                ddlServerPr2.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "000"));
            }

            if (ddlDatabaseDr.Items.Count > 0 && ddlDatabaseDr.Items[0].Value == "0")
            {
                ddlDatabaseDr.Items[0].Value = "000";
            }
            else if (ddlDatabaseDr.Items.Count <= 0)
            {
                ddlDatabaseDr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBName, "000"));
            }

            if (ddlDatabasePr.Items.Count > 0 && ddlDatabasePr.Items[0].Value == "0")
            {
                ddlDatabasePr.Items[0].Value = "000";
            }
            else if (ddlDatabasePr.Items.Count <= 0)
            {
                ddlDatabasePr.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBName, "000"));
            }

        }

        protected void BindSqlNavtiveLogSummary()
        {
            if (CurrentInfraObjectId > 0)
            {
                var mssqllog = _facade.GetSqlNativeById(CurrentInfraObjectId);
                if (mssqllog != null)
                {
                    txtBackup.Text = mssqllog.BackupFolderName;
                    txtRestore.Text = mssqllog.CopyRestoreName;

                    txtBackupNetwork.Text = mssqllog.BackupFolderSharedName;
                    txtRestoreNetwork.Text = mssqllog.CopyRestoreSharedName;

                    txtbackuptime.Text = mssqllog.BackupInterval.ToString();
                    txtcopytime.Text = mssqllog.CopyInterval.ToString();
                    txtrestoretime.Text = mssqllog.RestoreInterval.ToString();
                }
            }
        }

        protected void BindArchiveRedoLogSummary()
        {
            if (CurrentInfraObjectId > 0)
            {
                var hitachilunsvalue = _facade.GetHitachiUrLunsByInfraObjectId(CurrentInfraObjectId);
                if (hitachilunsvalue != null)
                {
                    txtArchVGName.Text = hitachilunsvalue.ArchVGName;
                    txtArchMountPoint.Text = hitachilunsvalue.ArchMountPoint;
                    ddlarchdevicetype.SelectedValue = hitachilunsvalue.ArchDevicetype;
                    txtArchHurTrueCopysource.Text = hitachilunsvalue.ArchHURTrueCopySource;
                    txtArchShadowImagePR.Text = hitachilunsvalue.ArchShadowimagePR;
                    txtArchTarget.Text = hitachilunsvalue.ArchTarget;

                    txtRedoVGName.Text = hitachilunsvalue.RedoVGName;
                    txtRedoMountPoint.Text = hitachilunsvalue.RedoMountPoint;
                    ddlRedoDevicetype.SelectedValue = hitachilunsvalue.RedoDeviceType;
                    txtRedohurtruecopysource.Text = hitachilunsvalue.RedoHURTrueCopySource;
                    txtRedoshadowimagePR.Text = hitachilunsvalue.RedoShadowimagePR;
                    txtRedoTarget.Text = hitachilunsvalue.RedoTarget;
                }
            }
        }

        protected void BindRecoveryType()
        {
            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.DB).ToString())
            {
                Utility.PopulateSubBusinessType(ddlSubBusinessType, typeof(InfraObjectType), true);

            }

            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.Application).ToString())
            {
                Utility.PopulateSubAppBusinessType(ddlSubBusinessType, typeof(InfraObjectType), true);
            }

            if (ddlBusinessType.SelectedItem.Value == ((int)InfraObjectType.Virtual).ToString())
            {
                Utility.PopulateSubVirtualBusinessType(ddlSubBusinessType, typeof(InfraObjectType), true);
            }

            if (ddlSubBusinessType.Items.Count > 0 && ddlSubBusinessType.Items[0].Value == "0")
            {
                ddlSubBusinessType.Items[0].Value = "000";
            }

            RequiredFieldValidator21.Visible = true;
        }

        protected void BindSubBuisnessType()
        {
            if (ddlSubBusinessType.SelectedItem.Value != "000")
            {
                if (ddlSubBusinessType.SelectedValue == "15")
                {
                    // trDr.Visible = false;
                    trDr.Visible = true;
                    ddlReplicationType.Visible = false;
                    RequiredFieldValidator6.Visible = false;
                    tdProductionReplication.Visible = false;
                    tdProductionDataBase.Visible = false;
                    tdDrDatabase.Visible = false;
                    tdDrReplication.Visible = false;
                    lblrecoverytype.Visible = false;
                    tdReplicationComponant.Visible = false;
                    trSummaryReplication.Visible = false;
                    trRecoveryType.Visible = false;
                    ddlDatabasePr.Visible = false;
                    RequiredFieldValidator9.Visible = false;
                    ddlReplicationPr.Visible = false;
                    RequiredFieldValidator10.Visible = false;
                    tdProductionDataBase.Visible = false;
                    trSummaryReplication.Visible = false;

                }
                else
                {
                    ddlReplicationType.Visible = true;
                    RequiredFieldValidator6.Visible = true;
                    tdProductionReplication.Visible = true;
                    tdProductionDataBase.Visible = true;
                    tdDrDatabase.Visible = true;
                    tdDrReplication.Visible = true;
                    lblrecoverytype.Visible = true;
                    tdReplicationComponant.Visible = true;
                    trSummaryReplication.Visible = true;
                    trRecoveryType.Visible = true;
                    ddlDatabasePr.Visible = true;
                    RequiredFieldValidator9.Visible = true;
                    ddlReplicationPr.Visible = true;
                    RequiredFieldValidator10.Visible = true;
                    trSummaryReplication.Visible = true;

                    var recovrytype = FetchRecoveryType(Convert.ToInt32(ddlSubBusinessType.SelectedValue));

                    if (recovrytype != null)
                    {
                        ddlReplicationType.DataSource = recovrytype;

                        ddlReplicationType.DataTextField = "RecoveryType";
                        ddlReplicationType.DataValueField = "ID";
                        ddlReplicationType.DataBind();
                        ddlReplicationType.AddDefaultItemWithValue(
                         Constants.UIConstants.DropDownItemPleaseSelectReplicationName);

                        if (ddlReplicationType.Items.Count > 0 && ddlReplicationType.Items[0].Value == "0")
                        {
                            ddlReplicationType.Enabled = true;
                            ddlReplicationType.Items[0].Value = "000";
                        }
                    }
                }
            }
        }

        public static List<RecoveryTypeName> FetchRecoveryType(int id)
        {
            var baseActionType = new RecoveryTypeName();
            return baseActionType.FetchRecoveryType(id);
        }

        [WebMethod]
        public static string SaveNodes(string values)
        {
            try
            {
                if (values != "")
                {
                    var racpara = values.Split(',');
                    if (racpara.Length > 5)
                    {
                        var groupdbnodes = new GroupDatabaseNodes();
                        // groupdbnodes.InfraObjectId = 0;
                        //Server Component
                        groupdbnodes.PrServerId = Convert.ToInt32(racpara[0]);
                        groupdbnodes.DrServerId = Convert.ToInt32(racpara[1]);
                        //DataBase Component
                        groupdbnodes.PrDbseId = Convert.ToInt32(racpara[2]);
                        groupdbnodes.DrDbseId = Convert.ToInt32(racpara[3]);
                        //For Nodes
                        NodeList = "";
                        var nodescount = Convert.ToInt32(racpara[4]);
                        for (var i = 1; i <= nodescount - 1; i++)
                        {
                            var j = 4;
                            var nodedatapara = racpara[j + i];
                            var nodedata = nodedatapara.Split(':');
                            groupdbnodes.PrNodeId = Convert.ToInt32(nodedata[0]);
                            groupdbnodes.DrNodeId = Convert.ToInt32(nodedata[1]);
                            var nodedet = _facade.AddInfraObjectDatabaseNodes(groupdbnodes);
                            NodeList += nodedet.Id + ",";
                        }
                    }
                }
                return "Success ";
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);

                return ex.Message;
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, "Unhandeled exception occurred while inserting Application Group information", ex);

                ExceptionManager.Manage(bcms);

                return ex.Message;
            }
        }

        [WebMethod]
        public static string UpdateNodes(string curentid, string values)
        {
            try
            {
                var currentInfraId = Convert.ToInt32(curentid);
                _facade.DeleteInfraObjectDatabaseNodesByInfraObjectId(currentInfraId);
                if (values != "")
                {
                    NodeList = "";
                    var racpara = values.Split(',');
                    if (racpara.Length > 5)
                    {
                        var groupdbnodes = new GroupDatabaseNodes();
                        groupdbnodes.InfraObjectId = currentInfraId;
                        //Server Component
                        groupdbnodes.PrServerId = Convert.ToInt32(racpara[0]);
                        groupdbnodes.DrServerId = Convert.ToInt32(racpara[1]);
                        //DataBase Component
                        groupdbnodes.PrDbseId = Convert.ToInt32(racpara[2]);
                        groupdbnodes.DrDbseId = Convert.ToInt32(racpara[3]);
                        //For Nodes
                        var nodescount = Convert.ToInt32(racpara[4]);
                        for (var i = 1; i <= nodescount - 1; i++)
                        {
                            var j = 4;
                            var nodedatapara = racpara[j + i];
                            var nodedata = nodedatapara.Split(':');
                            groupdbnodes.PrNodeId = Convert.ToInt32(nodedata[0]);
                            groupdbnodes.DrNodeId = Convert.ToInt32(nodedata[1]);
                            var nodedet = _facade.AddInfraObjectDatabaseNodes(groupdbnodes);
                            NodeList += nodedet.Id + ",";
                        }
                    }
                }
                return "Success ";
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);

                return ex.Message;
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, "Unhandeled exception occurred while inserting Application Group information", ex);

                ExceptionManager.Manage(bcms);

                return ex.Message;
            }
        }

        [WebMethod]
        public static string GetNodesForSummary()
        {
            var nodesum = "";
            if (NodeList != "")
            {
                if (NodeList.Length == NodeList.LastIndexOf(','))
                {
                    NodeList = NodeList.Remove(NodeList.LastIndexOf(','));
                }
                var nodes = NodeList.Split(',');
                if (nodes.Any())
                {
                    var i = 1;
                    foreach (var node in nodes)
                    {
                        if (node != "")
                        {
                            var nodedet = _facade.GetInfraObjectDatabaseNodesById(Convert.ToInt32(node));
                            if (nodedet != null)
                            {
                                nodesum += "<tr><td style='width:5%;'>" + i + "</td><td style='width:35%;'><label title='" +
                                           nodedet.PrNodeId + "'>" + _facade.GetNodesById(nodedet.PrNodeId).Name +
                                           "</label></td><td style='width:35%;'><label title='" + nodedet.DrNodeId + "'>" +
                                           _facade.GetNodesById(nodedet.DrNodeId).Name + "</label></td></tr>";
                            }
                            i++;
                        }
                    }
                }
            }
            return nodesum;
        }

        [WebMethod]
        public static string GetNodesForEdit(string id)
        {
            if (id != "")
            {
                NodeList = "";
                var infnodes = _facade.GetInfraObjectDatabaseNodesByInfraobjectId(Convert.ToInt32(id));
                if (infnodes != null)
                {
                    if (infnodes.Count > 0)
                    {
                        foreach (var node in infnodes)
                        {
                            NodeList += node.Id + ",";
                        }
                    }
                }
            }
            var nodesum = "";
            if (NodeList != "")
            {
                NodeList = NodeList.Remove(NodeList.LastIndexOf(','));
                var nodes = NodeList.Split(',');
                if (nodes.Any())
                {
                    var i = 1;
                    foreach (var node in nodes)
                    {
                        var nodedet = _facade.GetInfraObjectDatabaseNodesById(Convert.ToInt32(node));
                        if (nodedet != null)
                        {
                            nodesum += "<tr><td style='width:5%;'>" + i + "</td><td style='width:35%;'><label title='" + nodedet.PrNodeId + "'>" + _facade.GetNodesById(nodedet.PrNodeId).Name + "</label></td><td style='width:35%;'><label title='" + nodedet.DrNodeId + "'>" + _facade.GetNodesById(nodedet.DrNodeId).Name + "</label></td><td style='vertical-align: middle;' class='center'><img id='imgDelete" + i + "' alt='Delete' src='../Images/icons/cross-circle.png'/></td></tr>";
                            i++;
                            //nodesum += "<tr><td style='width:15%;'>0</td><td style='width:36%;'><label title='" +
                            //           nodedet.PrNodeId + "'>" + _facade.GetNodesById(nodedet.PrNodeId).Name +
                            //           "</label></td><td><label title='" + nodedet.PrNodeId + "'>" +
                            //           _facade.GetNodesById(nodedet.DrNodeId).Name + "</label></td></tr>";
                        }
                    }
                }
            }
            return nodesum;
        }

        protected void lvLunsList_ItemCreated(object sender, ListViewItemEventArgs e)
        {
            var ddlAuthType = (DropDownList)e.Item.FindControl("ddlFileType");

            if (ddlAuthType != null)
            {
                EnumHelper.PopulateEnumIntoList(ddlAuthType, typeof(GlobalMirrorFiletype), "- Select FileType -");
            }
        }

        protected void lvmanual_ItemInserting(object sender, ListViewInsertEventArgs e)
        {
            var txtHdisk = (TextBox)e.Item.FindControl("HDiskName");
            var txtPVID = (TextBox)e.Item.FindControl("PVID");
            var txtLUNID = (TextBox)e.Item.FindControl("LUNID");

            //ListView lstmain =(ListView)((ListView)sender).Parent;
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lunsid = lstitem.FindControl("lblLunId") as Label;


            InfraobjectGlobalMirrorLunsDetails objInfralunsdetails = new InfraobjectGlobalMirrorLunsDetails();
            //objInfralunsdetails.InfraLunsId=l
            objInfralunsdetails.Hdisk = txtHdisk.Text.ToString().Trim();
            objInfralunsdetails.PVID = txtPVID.Text.ToString().Trim();
            objInfralunsdetails.LUNID = txtLUNID.Text.ToString().Trim();
            objInfralunsdetails.InfraLunsId = Convert.ToInt32(lunsid.Text);
            luns.Add(objInfralunsdetails);
            ListView lvmanual = (ListView)sender;
            Session["Infralunsdetails"] = luns;

            if (isupdate)
            {
                var filteredData = from i in luns
                                   where (i.InfraLunsId == Convert.ToInt32(lunsid.Text))
                                   select i;




                lvmanual.DataSource = filteredData;
                lvmanual.DataBind();
            }
            else
            {
                luns.Clear();
                _facade.AddInfraobjectGlobalMirrorLuns_Details(objInfralunsdetails);
                lvmanual.EditIndex = -1;
                var updatedlist = _facade.GetInfraobjectGlobalMirrorLunsDetailsbyInfraLunsId(lunsid.Text.ToInteger());
                lvmanual.DataSource = updatedlist;
                lvmanual.DataBind();
            }


        }

        protected void lvmanual_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            //Find label id of parent listview 
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lunsid = lstitem.FindControl("lblLunId") as Label;


            //find child listview
            ListView lvmanual = (ListView)sender;

            Label lblId = lvmanual.Items[e.NewEditIndex].FindControl("Id") as Label;
            var Id = lblId.Text;


            lvmanual.EditIndex = e.NewEditIndex;
            var filteredData = from i in luns
                               where (i.InfraLunsId == Convert.ToInt32(lunsid.Text))
                               select i;
            // ListView objlist = (ListView)this.Parent.FindControl("lvmanual");



            lvmanual.DataSource = filteredData;
            lvmanual.DataBind();

            var txthdisk = lvmanual.Items[e.NewEditIndex].FindControl("HDiskName") as TextBox;
            var txtPVId = lvmanual.Items[e.NewEditIndex].FindControl("PVID") as TextBox;
            var txtLUNId = lvmanual.Items[e.NewEditIndex].FindControl("LUNID") as TextBox;
        }

        protected void lvmanual_ItemCanceling(object sender, ListViewCancelEventArgs e)
        {
            //Find label id of parent listview 
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lunsid = lstitem.FindControl("lblLunId") as Label;

            //find child listview
            ListView lvmanual = (ListView)sender;
            lvmanual.EditIndex = -1;
            BindList(sender);


        }

        protected void BindList(object sender)
        {
            //list.DataSource = luns;
            //list.DataBind();
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lunsid = lstitem.FindControl("lblLunId") as Label;

            var filteredData = from i in luns
                               where (i.InfraLunsId == Convert.ToInt32(lunsid.Text))
                               select i;
            ListView lvmanual = (ListView)sender;
            lvmanual.DataSource = filteredData;
            lvmanual.DataBind();
        }

        protected void lvmanual_ItemUpdating(object sender, ListViewUpdateEventArgs e)
        {
            //Find label id of parent listview 
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lunsid = lstitem.FindControl("lblLunId") as Label;

            //find child listview
            ListView lvmanual = (ListView)sender;

            Label lblId = lvmanual.Items[e.ItemIndex].FindControl("Id") as Label;
            var Id = lblId.Text;

            var txthdisk = lvmanual.Items[e.ItemIndex].FindControl("HDiskName") as TextBox;
            var txtPVId = lvmanual.Items[e.ItemIndex].FindControl("PVID") as TextBox;
            var txtLUNId = lvmanual.Items[e.ItemIndex].FindControl("LUNID") as TextBox;

            InfraobjectGlobalMirrorLunsDetails objinfraglobaldetails = new InfraobjectGlobalMirrorLunsDetails();
            objinfraglobaldetails.Id = Id.ToInteger();
            objinfraglobaldetails.Hdisk = txthdisk.Text.ToString().Trim();
            objinfraglobaldetails.PVID = txtPVId.Text.ToString().Trim();
            objinfraglobaldetails.LUNID = txtLUNId.Text.ToString().Trim();
            objinfraglobaldetails.InfraLunsId = lunsid.Text.ToInteger();

            if (Session["Infralunsdetails"] != null)
            {


                luns = (IList<InfraobjectGlobalMirrorLunsDetails>)Session["Infralunsdetails"];
                luns.Insert(e.ItemIndex, objinfraglobaldetails);
                luns.RemoveAt(e.ItemIndex + 1);
                Session["Infralunsdetails"] = luns;
                lvmanual.EditIndex = -1;
                BindList(sender);
            }
            else
            {
                luns.Clear();
                objinfraglobaldetails.Id = Id.ToInteger();
                _facade.UpdateInfraobjectGlobalMirrorLuns_Details(objinfraglobaldetails);
                lvmanual.EditIndex = -1;
                var updatedlist = _facade.GetInfraobjectGlobalMirrorLunsDetailsbyInfraLunsId(lunsid.Text.ToInteger());
                lvmanual.DataSource = updatedlist;
                lvmanual.DataBind();
            }



        }

        protected void lvmanual_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            //Find label id of parent listview 
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lunsid = lstitem.FindControl("lblLunId") as Label;

            //find child listview
            ListView lvmanual = (ListView)sender;

            Label lblId = lvmanual.Items[e.ItemIndex].FindControl("Id") as Label;
            var Id = lblId.Text;

            if (Id != null)
            {
                if (isupdate)
                {

                    if (Session["Infralunsdetails"] != null)
                    {


                        luns = (IList<InfraobjectGlobalMirrorLunsDetails>)Session["Infralunsdetails"];

                        luns.RemoveAt(e.ItemIndex);
                        Session["Infralunsdetails"] = luns;
                        BindList(sender);
                    }

                }
                else
                {
                    _facade.DeleteInfraobjectGlobalMirrorLunsDetails(Id.ToInteger());
                    var updatedlist = _facade.GetInfraobjectGlobalMirrorLunsDetailsbyInfraLunsId(lunsid.Text.ToInteger());
                    lvmanual.DataSource = updatedlist;
                    lvmanual.DataBind();
                }
            }

        }

        protected void BindCGListItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (CurrentInfraObjectId > 0)
            {
                if (_InfraCGName != null)
                {

                    var l = _InfraCGName[e.Item.DataItemIndex];

                    //var ddlItem = e.Item.FindControl("ddl") as HtmlSelect;
                    DropDownList ddlCGtype = e.Item.FindControl("ddlCGName") as DropDownList;


                    ddlCGtype.SelectedValue = l.Id.ToString();



                    IList<InfraobjectVolumeDetails> list = _facade.GetInfraobjectVolumeDetailsbyCGId(l.Id);
                    if (list != null)
                    {
                        foreach (var item in list)
                        {
                            cgd.Add(item);
                        }
                    }
                    ListView childListv = e.Item.FindControl("lvCGmanual") as ListView;
                    childListv.DataSource = list;
                    childListv.DataBind();

                }
            }


            // To get Infraobject Gloabal mirror luns details listview in insert mode.
            ListView objlvManual = (ListView)e.Item.FindControl("lvCGmanual");
            // ListView objlist = (ListView) Page.FindControl("lvmanual");

        }

        protected void lvlvCGlist_ItemCreated(object sender, ListViewItemEventArgs e)
        {
            var ddlCGType = (DropDownList)e.Item.FindControl("ddlCGName");
            var repliId = ddlReplicationPr.SelectedValue;
            var cglist = Facade.GetCGDetailsByBaseReplicationId(Convert.ToInt32(repliId));
            if (ddlCGType != null)
            {
                ddlCGType.DataSource = cglist.ToList();
                ddlCGType.DataValueField = "id";
                ddlCGType.DataTextField = "PRCGName";
                ddlCGType.DataBind();
            }
        }

        protected void lvCGmanual_ItemInserting(object sender, ListViewInsertEventArgs e)
        {
            var txtPRVolumeName = (TextBox)e.Item.FindControl("PRVolumeName");
            var txtDRVolumeName = (TextBox)e.Item.FindControl("DRVolumeName");

            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lcgsid = lstitem.FindControl("lblCGId") as Label;

            InfraobjectVolumeDetails objInfralunsdetails = new InfraobjectVolumeDetails();
            objInfralunsdetails.PRVolumeName = (txtPRVolumeName.Text).Trim();
            objInfralunsdetails.DRVolumeName = (txtDRVolumeName.Text).Trim();
            objInfralunsdetails.CGId = Convert.ToInt32(lcgsid.Text);
            cgd.Add(objInfralunsdetails);
            ListView lvmanual1 = (ListView)sender;
            Session["Infralunsdetails"] = cgd;

            if (isupdate)
            {
                var filteredData = from i in cgd
                                   where (i.CGId == Convert.ToInt32(lcgsid.Text))
                                   select i;

                lvmanual1.DataSource = filteredData;
                lvmanual1.DataBind();
            }
            else
            {
                cgd.Clear();
                _facade.AddInfraobjectVolume_Details(objInfralunsdetails);
                lvmanual1.EditIndex = -1;
                var updatedlist = _facade.GetInfraobjectVolumeDetailsbyCGId(lcgsid.Text.ToInteger());
                lvmanual1.DataSource = updatedlist;
                lvmanual1.DataBind();
            }

        }

        protected void lvCGmanual_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            //Find label id of parent listview 
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lcgsid = lstitem.FindControl("lblCGId") as Label;

            //find child listview
            ListView lvCGmanual = (ListView)sender;

            Label lblId = lvCGmanual.Items[e.NewEditIndex].FindControl("Id") as Label;
            var Id = lblId.Text;

            lvCGmanual.EditIndex = e.NewEditIndex;
            var filteredData = from i in cgd
                               where (i.CGId == Convert.ToInt32(lcgsid.Text))
                               select i;
            lvCGmanual.DataSource = filteredData;
            lvCGmanual.DataBind();

            var txthprvolumename = lvCGmanual.Items[e.NewEditIndex].FindControl("PRVolumeName") as TextBox;
            var txthdrvolumename = lvCGmanual.Items[e.NewEditIndex].FindControl("DRVolumeName") as TextBox;
        }

        protected void lvCGmanual_ItemCanceling(object sender, ListViewCancelEventArgs e)
        {
            //Find label id of parent listview 
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lcgsid = lstitem.FindControl("lblCGId") as Label;

            //find child listview
            ListView lvCGmanual = (ListView)sender;
            lvCGmanual.EditIndex = -1;
            BindList1(sender);
        }

        protected void lvCGmanual_ItemUpdating(object sender, ListViewUpdateEventArgs e)
        {
            //Find label id of parent listview 
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lcgsid = lstitem.FindControl("lblCGId") as Label;
            //find child listview
            ListView lvCGmanual = (ListView)sender;

            Label lblId = lvCGmanual.Items[e.ItemIndex].FindControl("Id") as Label;
            var Id = lblId.Text;

            var txthPrvolumename = lvCGmanual.Items[e.ItemIndex].FindControl("PRVolumeName") as TextBox;
            var txthDrvolumename = lvCGmanual.Items[e.ItemIndex].FindControl("DRVolumeName") as TextBox;

            InfraobjectVolumeDetails objinfraglobaldetails = new InfraobjectVolumeDetails();
            objinfraglobaldetails.Id = Id.ToInteger();
            objinfraglobaldetails.PRVolumeName = txthPrvolumename.Text.ToString().Trim();
            objinfraglobaldetails.DRVolumeName = txthDrvolumename.Text.ToString().Trim();
            objinfraglobaldetails.CGId = lcgsid.Text.ToInteger();

            if (Session["Infralunsdetails"] != null)
            {
                cgd = (IList<InfraobjectVolumeDetails>)Session["Infralunsdetails"];
                cgd.Insert(e.ItemIndex, objinfraglobaldetails);
                cgd.RemoveAt(e.ItemIndex + 1);
                Session["Infralunsdetails"] = cgd;
                lvCGmanual.EditIndex = -1;
                BindList1(sender);
            }
            else
            {
                cgd.Clear();
                objinfraglobaldetails.Id = Id.ToInteger();
                _facade.UpdateInfraobjectVolume_Details(objinfraglobaldetails);
                lvCGmanual.EditIndex = -1;
                var updatedlist = _facade.GetInfraobjectVolumeDetailsbyCGId(lcgsid.Text.ToInteger());
                lvCGmanual.DataSource = updatedlist;
                lvCGmanual.DataBind();
            }
        }

        protected void lvCGmanual_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            //Find label id of parent listview 
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lcgsid = lstitem.FindControl("lblCGId") as Label;

            //find child listview
            ListView lvCGmanual = (ListView)sender;

            Label lblId = lvCGmanual.Items[e.ItemIndex].FindControl("Id") as Label;
            var Id = lblId.Text;

            if (Id != null)
            {
                if (isupdate)
                {
                    if (Session["Infralunsdetails"] != null)
                    {
                        cgd = (IList<InfraobjectVolumeDetails>)Session["Infralunsdetails"];
                        cgd.RemoveAt(e.ItemIndex);
                        Session["Infralunsdetails"] = cgd;
                        BindList1(sender);
                    }
                }
                else
                {
                    _facade.DeleteInfraobjectVolumeDetails(Id.ToInteger());
                    var updatedlist = _facade.GetInfraobjectVolumeDetailsbyCGId(lcgsid.Text.ToInteger());
                    lvCGmanual.DataSource = updatedlist;
                    lvCGmanual.DataBind();
                }
            }
        }

        protected void BindList1(object sender)
        {
            ListViewItem lstitem = (ListViewItem)((ListView)sender).NamingContainer;
            Label lcgsid = lstitem.FindControl("lblCGId") as Label;

            var filteredData = from i in cgd
                               where (i.CGId == Convert.ToInt32(lcgsid.Text))
                               select i;
            ListView lvCGmanual = (ListView)sender;
            lvCGmanual.DataSource = filteredData;
            lvCGmanual.DataBind();
        }

        protected void ChkIspair_CheckedChanged(object sender, EventArgs e)
        {
            //Added by Vijay


            if (ChkIspair.Checked == true)
            {
                ddlPairinfraobjectid.Enabled = true;
                ChkIsAssociate.Checked = false;
                lblPairInfra.InnerText = "Pair InfraObject ID";
                SGroupInfraID.Text = "Pair InfraObject ID";
                RequiredFieldValidator26.Enabled = true;
            }
            else
            {
                ddlPairinfraobjectid.SelectedValue = "000";
                ddlPairinfraobjectid.Enabled = false;
                RequiredFieldValidator26.Enabled = false;
                //ChkIsAssociate.Checked = true;
            }
        }

        protected void ChkIsAssociate_CheckedChanged(object sender, EventArgs e)
        {
            if (ChkIsAssociate.Checked == true)
            {
                ddlPairinfraobjectid.Enabled = true;
                ChkIspair.Checked = false;
                lblPairInfra.InnerText = "Associate InfraObject ID";
                SGroupInfraID.Text = "Associate InfraObject ID";
                RequiredFieldValidator26.Enabled = true;
            }
            else
            {
                ddlPairinfraobjectid.SelectedValue = "000";
                ddlPairinfraobjectid.Enabled = false;
                RequiredFieldValidator26.Enabled = false;
                //ChkIspair.Checked = true;
            }
        }

        //protected void chkCluster_CheckedChanged(object sender, EventArgs e)
        //{
        //    if (chkCluster.Checked)
        //    {
        //        ddlClusterProfileName.Visible = true;
        //        Utility.PopulateVeritasCluster(ddlClusterProfileName, true);
        //    }
        //    else
        //    {
        //        ddlClusterProfileName.Visible = false;
        //    }
        //}

        protected void chkIsCluster_CheckedChanged(object sender, EventArgs e)
        {
            if (chkIsCluster.Checked == true)
            {
                tdCluster.Visible = true;
                tdClusterPr.Visible = false;
                tdClusterDr.Visible = false;
            }
            else
            {
                tdCluster.Visible = false;
                tdClusterPr.Visible = false;
                tdClusterDr.Visible = false;
            }
        }

        protected void chkPrCloud_CheckedChanged(object sender, EventArgs e)
        {
            if (chkPrCloud.Checked)
                CurrentInfraObject.IsPRCloud = 1;
            else
                CurrentInfraObject.IsPRCloud = 0;
        }

        protected void chkDrCloud_CheckedChanged(object sender, EventArgs e)
        {
            if (chkDrCloud.Checked)
                CurrentInfraObject.IsDRCloud = 1;
            else
                CurrentInfraObject.IsDRCloud = 0;

        }
        //public static Control FindControlRecursive(this Control control, string id)
        //{
        //    if (control == null) return null;
        //    //try to find the control at the current level
        //    Control ctrl = control.FindControl(id);

        //    if (ctrl == null)
        //    {
        //        //search the children
        //        foreach (Control child in control.Controls)
        //        {
        //            ctrl = FindControlRecursive(child, id);

        //            if (ctrl != null) break;
        //        }
        //    }
        //    return ctrl;
        //}


        protected void ddlCluster_SelectedIndexChanged(object sender, EventArgs e)
        {
            //IList<Server> Serv_List = Facade.GetAllServers();
            ddlClusterDR.Items.Clear();
            ddlClusterPR.Items.Clear();

            //IList<VeritasCluster> GlobalClust_List_PR = new List<GlobalCluster>();
            //IList<GlobalCluster> GlobalClust_List_DR = new List<GlobalCluster>();

            //var GlobalClusterlist = Facade.GetAll_GlobalClusters();
            var VeritasClusterList = Facade.GetAllVeritasCluster();
            var HACMPClusterList = Facade.GetAllHACMPCluster();

            //var ServerList = Facade.GetAllServers();

            if (ddlCluster.SelectedValue == "1")
            {
                tdClusterPr.Visible = true;
                //tdClusterDr.Visible = true;


                if (VeritasClusterList != null)
                {
                    ddlClusterPR.DataSource = VeritasClusterList;
                    ddlClusterPR.DataTextField = "ClusteProfileName";
                    ddlClusterPR.DataValueField = "Id";
                    ddlClusterPR.DataBind();
                    ddlClusterPR.Items.Insert(0,
                        new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectClusterProfileName, "000"));

                    //Utility.PopulateVeritasCluster(ddlClusterPR, true);

                }



            }


            if (ddlCluster.SelectedValue == "2")
            {
                tdClusterPr.Visible = true;
                tdClusterDr.Visible = true;


                if (HACMPClusterList != null && HACMPClusterList.Count > 0)
                {
                    ddlClusterPR.DataSource = HACMPClusterList;
                    ddlClusterPR.DataTextField = "Name";
                    ddlClusterPR.DataValueField = "Id";
                    ddlClusterPR.DataBind();
                    ddlClusterPR.Items.Insert(0,
                        new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectClusterProfileName, "000"));

                    ddlClusterDR.DataSource = HACMPClusterList;
                    ddlClusterDR.DataTextField = "Name";
                    ddlClusterDR.DataValueField = "Id";
                    ddlClusterDR.DataBind();
                    ddlClusterDR.Items.Insert(0,
                        new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectClusterProfileName, "000"));

                }

            }
        }

        protected void chkPRhostname_CheckedChanged(object sender, EventArgs e)
        {

        }

        protected void chkDRhostname_CheckedChanged(object sender, EventArgs e)
        {

        }
    }
}
