﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "NutanixClusterDetailMonitoring", Namespace = "http://www.ContinuityPlatform.com/types")]

   public class NutanixClusterDetailMonitoring :BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string PRClusterName { get; set; }

        [DataMember]
        public string DRClusterName { get; set; }

        [DataMember]
        public string PRClusterVirtualIPAddress { get; set; }

        [DataMember]
        public string DRClusterVirtualIPAddress { get; set; }

        [DataMember]
        public string PRAOSVersion { get; set; }

        [DataMember]
        public string DRAOSVersion { get; set; }

        [DataMember]
        public string PRHYPERVISORVersion { get; set; }

        [DataMember]
        public string DRHYPERVISORVersion { get; set; }

        [DataMember]
        public string PRNCCVersion { get; set; }

        [DataMember]
        public string DRNCCVersion { get; set; }

        [DataMember]
        public string PRCVMIPAddress { get; set; }

        [DataMember]
        public string DRCVMIPAddress { get; set; }

        [DataMember]
        public DateTime CreateDate { get; set; }


        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }
        #endregion Properties
    }
}
