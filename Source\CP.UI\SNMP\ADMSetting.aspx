﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ADMSetting.aspx.cs" Inherits="CP.UI.SNMP.APMSetting" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
     <style>
        .chosen-select + .chosen-container {
            width: 48.5% !important;
            opacity: 1 !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <asp:UpdatePanel ID="updPnlScan" runat="server" UpdateMode="Always">
        <ContentTemplate>
            <div class="innerLR">
                <h3><span class="business-setting-icon vertical-sub"></span>
                    Application Dependency Mapping Settings</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Provider <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlType" runat="server" TabIndex="5" CssClass="chosen-select col-md-6"
                                            data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlType_SelectedIndexChanged">
                                            <asp:ListItem Value="0">- Select Provider - </asp:ListItem>
                                            <asp:ListItem Value="1">Third party </asp:ListItem>
                                            <asp:ListItem Value="2">SNMP  </asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvddlType" runat="server" ControlToValidate="ddlType" InitialValue="0" Display="Dynamic" CssClass="error" ErrorMessage="Select provider"></asp:RequiredFieldValidator>

                                    </div>
                                </div>
                                <asp:Panel class="form-group" ID="pnlpath" runat="server" Visible="False">
                                    <label class="col-md-3 control-label">
                                        Path <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtLocation" class="form-control" runat="server" TabIndex="2"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvtxtLocation" runat="server" ControlToValidate="txtLocation" Display="Dynamic" CssClass="error" ErrorMessage="Provide Path"></asp:RequiredFieldValidator>
                                    </div>
                                </asp:Panel>
                                <asp:Panel class="form-group" ID="pnlsnmp" runat="server" Visible="False">
                                    <label class="col-md-3 control-label">
                                        SNMP Community<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtSnmpCommunity" class="form-control" runat="server" TabIndex="2"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvtxtSnmpCommunity" runat="server" ControlToValidate="txtSnmpCommunity" Display="Dynamic" CssClass="error" ErrorMessage="Provide SNMP community"></asp:RequiredFieldValidator>
                                    </div>
                                </asp:Panel>

                            </div>
                        </div>
                        <hr class="separator" />
                        <div class="form-actions row">
                            <div class="col-xs-5">
                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
                                <asp:Label ID="lblProfileResult" runat="server" Text="&nbsp;"></asp:Label>
                               
                            </div>
                            <div class="col-xs-7 padding-none">      
                                 <div style="display: inline-block; width: 17.5%;">
                                    <asp:Label ID="lblSaveMsg" runat="server" Text="Saved successfully." ForeColor="Green" Visible="false"></asp:Label>
                                </div>                          
                                <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="15%" runat="server" Text="Save" OnClick="btnSave_Click" 
                                    TabIndex="11" />

                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </ContentTemplate>
    </asp:UpdatePanel>
</asp:Content>
