﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="Base24ReplicationConfig.ascx.cs"
    Inherits="CP.UI.Controls.Base24ReplicationConfig" %>

<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">Base24 Replication</h4>
        </div>
        <div class="widget-body">
            <div class="form-group">
                <label class="col-replication" for="txtName">
                    Process Name<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtProcessName" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtProcessName" runat="server" CssClass="error" ControlToValidate="txtProcessName"
                        Display="Dynamic" ErrorMessage="Please Enter Process Name"></asp:RequiredFieldValidator>
                </div>
            </div>

            <div class="form-group">
                <label class="col-replication" for="txtName">
                    Audit File Name<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtAuditFileName" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtAuditFileName" runat="server" CssClass="error" ControlToValidate="txtAuditFileName"
                        Display="Dynamic" ErrorMessage="Please Enter Audit File Name"></asp:RequiredFieldValidator>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="form-actions row">
    <div class="col-lg-3">
        <asp:Label ID="Label7" runat="server" Text="&nbsp;"></asp:Label>
    </div>
    <div class="col-lg-6" style="margin-left: 40.3%;">
        <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save" OnClick="btnSave_Click" />

        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server"
            Text="Cancel" CausesValidation="false" OnClick="btnCancel_Click" />
    </div>
</div>
