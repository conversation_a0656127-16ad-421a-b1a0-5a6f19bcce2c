﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ImportBFtoBFRules.aspx.cs" Inherits="CP.UI.ImpactAnalysis.ImportBFtoBFRules" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajaxToolkit" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title></title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/modernizr.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/ie.prototype.polyfill.js"></script>
    <script src="../Script/html5shiv.js"></script>
    <script type="text/javascript" src="../Script/jquery-ui.min.js "></script>
    <style type="text/css">
        .bootstrap-checkbox button.btn > span.cb-icon-check-empty, .bootstrap-checkbox.disabled button.btn:hover > span.cb-icon-check-empty {
            background-position: -25px -1px;
        }

        #gridimportcmdb table th [class^="cb-icon-"], #gridimportcmdb table th [class*=" cb-icon-"] {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-top: 1px;
            *margin-right: .3em;
            line-height: 16px;
            vertical-align: text-top;
            background-image: url("../Images/custom-chkbox-rdbtn-White.png");
            background-position: 16px 16px;
            background-repeat: no-repeat;
        }

        #gridimportcmdb table th .bootstrap-checkbox button.btn > span.cb-icon-check-empty,
        #gridimportcmdb table th .bootstrap-checkbox.disabled button.btn:hover > span.cb-icon-check-empty {
            background-position: -25px -1px;
        }

        #gridimportcmdb table th .bootstrap-checkbox button.btn > span.cb-icon-check,
        #gridimportcmdb table th .bootstrap-checkbox.disabled button.btn:hover > span.cb-icon-check,
        #gridimportcmdb table th .bootstrap-checkbox.disabled button.btn:active > span.cb-icon-check {
            background-position: -1px -1px;
        }

        #lstvw th {
            white-space: nowrap;
        }

        table#lstvw table td {
            border-top: 0px;
        }

        .fileuploadbg {
            width: 39%;
        }

        #UpdatePanel1 {
            display: inline-block;
        }

        #AsyncUpload_ctl02 input {
            width: 470px !important;
        }

        #ctl00_cphBody_selectauthenticationdrop {
            width: 84% !important;
        }

        .CMDBdetail1 {
            display: inline-block;
            width: 15.3%;
            text-align: right;
        }

        .CMDBdetail2 {
            display: inline-block;
            width: 83%;
            margin-left: 10px;
        }

        .text-danger {
            color: #ff0000;
        }

        #AsyncUpload {
            border: 0px !important;
        }

        AsyncUpload_ctl01 {
            width: 425px !important;
        }

        #AsyncUpload_ctl01 > input, .fileuploadbg input[type="text"] {
            width: 425px !important;
            cursor: pointer !important;
        }

        .fileuploadbg input[type="text"] {
            margin-top: -2px !important;
            cursor: pointer !important;
        }
    </style>

    <script type="text/javascript">
        var uploadError = false;
        var errorMessage;

        function onUploadSuccess(sender, args) {

            var theControl1 = document.getElementById("<%=btnImport.ClientID%>");
            theControl1.removeAttribute('disabled');
            var obj = document.getElementById("<%=lblErr.ClientID %>");
            obj.style.display = 'none';
        }

        function onUploadError(sender, args) {

            document.getElementById('<%=lblStatus1.ClientID%>').value = args.get_fileName();
            var imgname = document.getElementById('<%=lblStatus1.ClientID%>').value;
            var fields = imgname.split('.')
            var checkType = fields[1];


            if (checkType == "xls" || checkType == "xlsx") {

                var theControl1 = document.getElementById("<%=btnImport.ClientID%>");
                theControl1.removeAttribute('disabled');
                var obj = document.getElementById("<%=lblErr.ClientID %>");
                obj.style.display = 'none';
            }
            else {
                var theControl = document.getElementById("<%= btnImport.ClientID %>");
                theControl.setAttribute('disabled', 'disabled');
                var obj = document.getElementById("<%=lblErr.ClientID %>");
                obj.style.display = 'inline-block';

                var amount = document.getElementById("<%=lblErr.ClientID %>").innerText;
                amount = "Only csv/xls file allowed";

                document.getElementById("<%=lblErr.ClientID%>").innerHTML = amount;

                var obj1 = document.getElementById("<%=lblStatus.ClientID %>");
                obj1.style.display = 'none';
            }

        }

        $(window).load(function () {
            document.getElementById("<%=lblHeaderName.ClientID%>").innerHTML = sessionStorage.getItem("chkValue");
        });

        function openfileDialog() {
            $("#AsyncUpload_ctl02").click();
        }

        $(document).ready(function () {
            $('input[type="checkbox"]').checkbox();
            var dvmap = document.getElementById("<%=divMap.ClientID %>");
            dvmap.style.display = 'none';


            document.getElementById("<%=lblHeaderName.ClientID%>").innerHTML = sessionStorage.getItem("chkValue");

            $("#AsyncUpload").removeAttr("style");

            $(document).on('change', '[type=checkbox]', function (e) {

                var chkId = this.id;

                chkId = chkId.replace('lstvw_ctl01_', '');
                if (chkId != 'ctl00_cphBody_chkAuthenticationOption') {
                    if (this.checked) {

                        $.ajax({
                            type: "POST",
                            url: "ImportBFtoBFRules.aspx/MainFunctionTest",
                            data: "{'chkText':'" + chkId + "'}",
                            contentType: "application/json; charset=utf-8",
                            dataType: "json",
                            async: true,
                            success: function Success(data) {

                            }
                        });


                        document.getElementById("<%=lblHeaderName.ClientID%>").innerHTML = chkId;

                        document.getElementById("<%=hdActivityName.ClientID%>").innerHTML = chkId;

                        sessionStorage.setItem("chkValue", chkId);

                        document.getElementById("<%=lblHeaderName.ClientID%>").innerHTML = sessionStorage.getItem("chkValue");

                        var dvmap = document.getElementById("<%=divMap.ClientID %>");
                        dvmap.style.display = 'block';
                    }
                    else {
                        var dvmap = document.getElementById("<%=divMap.ClientID %>");
                        dvmap.style.display = 'none';
                    }
                }
                else {
                    if (this.checked) {

                    }
                    else {

                    }
                }

            });
        });


        $("#btnImport").click(function () {
            $('input[type="checkbox"]').checkbox();
        });


        function CancelClick() {
            return false;
        }
        function pageLoad() {
            $('input[type="checkbox"]').checkbox();
        }


    </script>
</head>
<body>
    <form id="form1" runat="server">

        <ajaxToolkit:ToolkitScriptManager ID="ScriptManager2" runat="Server" />

        <asp:UpdatePanel ID="UpdateMailPanel" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <div class="innerLR">
                    <h3>
                        <img src="../Images/report-icon.png" />
                        Import Rule : Data</h3>

                    <input type="hidden" id="hdActivityName" runat="server" />

                    <div class="widget widget-heading-simple widget-body-white margin-bottom-none">
                        <div class="widget-body">

                            <div class="row">
                                <div class="col-md-1" style="width: 5.2%"></div>
                                <div class="col-md-10 padding-none-LR" style="width: 89.6%">
                                    <asp:Label ID="Label1" runat="server" Text="Select File" Style="margin-right: 33px"></asp:Label>


                                    <ajaxToolkit:AsyncFileUpload ID="AsyncUpload" runat="server" OnClientUploadError="onUploadError"
                                        CompleteBackColor="#70a3cf" OnUploadedComplete="AsyncUpload_UploadedComplete" UploadingBackColor="#70a3cf" BackColor="white"
                                        ThrobberID="Image1" UploaderStyle="Modern" ErrorBackColor="#70a3cf" CssClass="fileuploadbg" BorderWidth="1"
                                        BorderColor="#CCCCCC" />

                                    <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Always">
                                        <ContentTemplate>
                                            <asp:Button runat="server" ID="BrowseButton" CssClass="btn btn-primary" Text="Browse" OnClientClick="openfileDialog()" OnClick="BrowseButton_Click" Style="margin-left: 10px; width: 100% !important; vertical-align: sub" />
                                            <ajaxToolkit:ConfirmButtonExtender ID="cnfrmExtndrBrowse" runat="server" ConfirmText="You have mapped servers columns,Do you want to discard changes?"
                                                TargetControlID="BrowseButton" OnClientCancel="CancelClick">
                                            </ajaxToolkit:ConfirmButtonExtender>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>



                                </div>
                                <div class="col-md-1" style="width: 5.2%"></div>
                            </div>
                            <div class="row" style="height: 20px;">
                                <div class="col-md-1" style="width: 5.2%"></div>
                                <div class="col-md-10 padding-none-LR" style="width: 89.6%">

                                    <asp:Label ID="lblStatus1" runat="server" Style="font-family: Arial; font-size: small;"></asp:Label>
                                    <asp:Label ID="lblStatus" runat="server" ForeColor="green" Style="display: none"></asp:Label>
                                    <asp:Label ID="lblErr" runat="server" Visible="true" ForeColor="Red" Style="margin-left: 92px;" />

                                </div>
                                <div class="col-md-1" style="width: 5.2%"></div>
                            </div>
                            <div class="row">
                                <div class="col-md-1" style="width: 5.2%"></div>
                                <div class="col-md-10 padding-none-LR" style="width: 89.6%">
                                    <div class="form-group">
                                        <asp:Button ID="btnImport" runat="server" CssClass="btn btn-primary" Style="margin-left: 8.5%;" Text="Import Excel Data" CausesValidation="false" OnClick="BtnImportCSVClick" Enabled="true" />
                                    </div>
                                </div>
                                <div class="col-md-1" style="width: 5.2%"></div>
                            </div>
                            <%-- <hr style="margin-top: 4px !important;" />--%>
                        </div>
                    </div>
                    <div id="divExcelData" style="display: none" runat="server">
                        <h3>
                            <img src="../Images/report-icon.png" />
                            Excel Data</h3>

                        <div class="widget widget-heading-simple widget-body-white margin-bottom-none">
                            <div class="widget-body">
                                <div class="row">
                                    <div class="col-md-1" style="width: 5.2%"></div>
                                    <div class="col-md-10 padding-none-LR" style="width: 89.6%">
                                        <div class="form-group">
                                            Select columns to map server properties
                                        </div>
                                    </div>
                                    <div class="col-md-1" style="width: 5.2%"></div>
                                </div>
                                <asp:UpdatePanel ID="updpnl" runat="server" UpdateMode="Always">
                                    <ContentTemplate>


                                        <div class="row">
                                            <div class="col-md-1" style="width: 5.2%"></div>
                                            <div id="gridimportcmdb" class="col-md-10 padding-none-LR" style="width: 89.6%; overflow-x: auto; margin-bottom: 10px;">

                                                <asp:GridView ID="lstvw" CssClass="dynamicTable tableTools table table-striped table-bordered table-condensed table-white margin-bottom-none"
                                                    runat="server" OnRowDataBound="gvData_DataBound" AllowPaging="true"
                                                    OnPageIndexChanging="OnPaging" PageSize="15">
                                                    <PagerSettings Mode="NumericFirstLast" Position="Bottom" PageButtonCount="3" PreviousPageImageUrl="<-Prev" NextPageImageUrl="Next->" />

                                                    <PagerStyle HorizontalAlign="Right" CssClass="btn-pagination2" />
                                                </asp:GridView>
                                            </div>
                                            <div class="col-md-1" style="width: 5.2%"></div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-1" style="width: 5.2%"></div>
                                            <div class="col-md-10 padding-none-LR" style="width: 89.6%">

                                                <div id="divMap" runat="server" style="display: none">
                                                    <div class="form-group margin-top">
                                                        <div class="CMDBdetail1">
                                                            <asp:Label ID="lblColName" runat="server" Text="Selected Column Name :" Style="" />
                                                        </div>
                                                        <div class="CMDBdetail2">
                                                            <asp:Label ID="lblHeaderName" runat="server" Style="font-weight: bold;" />
                                                        </div>
                                                    </div>
                                                    <div class="form-group margin-top">
                                                        <div class="CMDBdetail1">
                                                            <label id="lblHeaderNameNew"></label>
                                                            <asp:Label ID="lblMapto" runat="server" Text="Map to :" />
                                                        </div>
                                                        <div class="CMDBdetail2">
                                                            <asp:DropDownList ID="ddlServerFiledMap" CssClass="selectpicker" data-style="btn-default" runat="server" Width="30%">
                                                                <asp:ListItem Text="RuleCode" Value="0"></asp:ListItem>
                                                                <asp:ListItem Text="Parent Business Function" Value="1"></asp:ListItem>
                                                                <asp:ListItem Text="Parent BF Impact Type" Value="2"></asp:ListItem>
                                                                <asp:ListItem Text="Child Business Function" Value="3"></asp:ListItem>
                                                                <asp:ListItem Text="Child BF Impact Type" Value="4"></asp:ListItem>
                                                                <asp:ListItem Text="Effective Date" Value="5"></asp:ListItem>

                                                            </asp:DropDownList>
                                                            <asp:Button ID="btnSetMappingFileds" runat="server" CssClass="btn btn-primary" Width="10%" Text="Map" CausesValidation="false" OnClick="btnSetMappingFileds_Click" Style="margin-left: 10px; vertical-align: baseline" />
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="col-md-1" style="width: 5.2%"></div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-1" style="width: 5.2%"></div>
                                            <div class="col-md-10 padding-none-LR" style="width: 89.6%">
                                                <asp:ListView ID="lvMap" runat="server" DataKeyNames="MappedColumnName" OnItemDeleting="lvMap_ItemDeleting">
                                                    <LayoutTemplate>
                                                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white margin-bottom-none">
                                                            <thead>
                                                                <tr>
                                                                    <th style="width: 4%;" class="text-center">
                                                                        <span>
                                                                            <img src="../Images/settings-icon.png" height="18px" /></span>
                                                                    </th>
                                                                    <th style="width: 45%;">Excel Column Name
                                                                    </th>
                                                                    <th style="width: 45%;">Mapped Column Name
                                                                    </th>
                                                                    <th style="width: 6%;" class="text-center">Action
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </LayoutTemplate>
                                                    <%-- <EmptyDataTemplate>
                                        <div class="message warning align-center bold no-bottom-margin">
                                            <asp:Label ID="lblError" Text="No Column Mapped" runat="server" Visible="true"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>--%>
                                                    <ItemTemplate>
                                                        <tr>
                                                            <td class="text-center">
                                                                <%#Container.DataItemIndex+1 %>
                                                            </td>

                                                            <td>
                                                                <asp:Label ID="lblExcelClmnName" runat="server" Text='<%# Eval("ExcelColumnName") %>' />
                                                            </td>
                                                            <td>
                                                                <asp:Label ID="lblMappedClmnName" runat="server" Text='<%# Eval("MappedColumnName") %>' />
                                                            </td>
                                                            <td class="text-center">
                                                                <%-- <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" ToolTip="Edit" ImageUrl="../images/icons/pencil.png"
                                            ValidationGroup="IDT" CausesValidation="false" />--%>
                                                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" ToolTip="Delete"
                                                                    ImageUrl="../images/icons/cross-circle.png" ValidationGroup="IDT" CausesValidation="true" />
                                                                <%--  <asp:ImageButton ID="ImgTestConnection" runat="server" CommandName="TestConnection" ToolTip="Test Connection"
                                            ValidationGroup="IDT" CausesValidation="true" />--%>
                                                            </td>
                                                            <ajaxToolkit:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText='<%# "Are you sure want to delete " + Eval("ExcelColumnName") + " ? " %>'
                                                                TargetControlID="ImgDelete" OnClientCancel="CancelClick">
                                                            </ajaxToolkit:ConfirmButtonExtender>
                                                        </tr>
                                                    </ItemTemplate>
                                                </asp:ListView>
                                            </div>
                                            <div class="col-md-1" style="width: 5.2%"></div>
                                        </div>





                                        <hr class="separator" />

                                        <div class="row">
                                            <div class="col-md-1" style="width: 5.2%">
                                            </div>
                                            <div class="col-md-10 text-right padding-none-LR" style="width: 89.6%">
                                                <asp:Label ID="lblError" runat="server" Visible="true" ForeColor="Red" />
                                                <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="10%" runat="server"
                                                    Text="Save" OnClick="btnSave_Click"></asp:Button>
                                                <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="10%" runat="server" OnClick="Button3_Click"
                                                    Text="Cancel" />
                                                <%--<ajaxToolkit:ConfirmButtonExtender ID="cnfrmExtndrSave" runat="server" ConfirmText="Do you want to import server configurations from CMDB?"
                                        TargetControlID="btnSave" OnClientCancel="CancelClick">
                                    </ajaxToolkit:ConfirmButtonExtender>--%>
                                            </div>
                                            <div class="col-md-1" style="width: 5.2%">
                                            </div>
                                        </div>
                                        <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
                                        </asp:Panel>
                                        <asp:Panel ID="pnlImportSuccess" runat="server" Width="100%" Visible="false">

                                            <div class="modal" style="display: block;">
                                                <div class="modal-dialog" style="width: 400px;">
                                                    <div class="modal-content  widget-body-white" style="box-shadow: 0 1px 0 0 #dbdbdb;">
                                                        <div class="modal-header">
                                                            <h3 class="modal-title" style="font-size: 15px">Rules Import Status</h3>
                                                            <asp:LinkButton ID="lkbtnSMS" runat="server" ToolTip="Close window" class="close" OnClick="lkbtnSMS_Click">×</asp:LinkButton>
                                                        </div>
                                                        <div class="modal-body padding-none-LR">

                                                            <div class="col-md-12 form-horizontal uniformjs">
                                                                <div class="form-group">
                                                                    <div class="col-md-6">
                                                                        <img src="../Images/icons/document-excel.png" style="height: 16px; vertical-align: text-bottom;" />
                                                                        File Name
                                                                    </div>
                                                                    :
                                        <div class="col-md-6" style="width: 49%; float: right">
                                            <asp:Label ID="lblExcelFileName" runat="server" CssClass="text-primary tdword-wrap" Style="display: block; font-weight: bold">DemoServers.xls</asp:Label>
                                        </div>
                                                                </div>

                                                                <div class="form-group">
                                                                    <div class="col-md-6">
                                                                        <img src="../Images/icons/icon-total-count.png" style="height: 16px; vertical-align: text-bottom;" />
                                                                        Total Rules Records
                                                                    </div>
                                                                    :
                                        <div class="col-md-6" style="width: 49%; float: right">
                                            <asp:Label ID="lblTotalCount" runat="server" CssClass="text-primary" Style="font-weight: bold">1234</asp:Label>
                                        </div>
                                                                </div>

                                                                <div class="form-group">

                                                                    <div class="col-md-6">
                                                                        <img src="../Images/icons/icon-success-count.png" style="height: 16px; vertical-align: text-bottom;" />
                                                                        Successfully Imported

                                                                    </div>
                                                                    :
                                        <div class="col-md-6" style="width: 49%; float: right">
                                            <%--<asp:Label ID="Label3" CssClass="col-md-4" Text="Imported Count" runat="server"></asp:Label> --%>

                                            <asp:Label ID="lblImportedCount" runat="server" CssClass="text-success" Style="font-weight: bold">1234</asp:Label>
                                        </div>
                                                                </div>
                                                                <div class="form-group margin-bottom-none">
                                                                    <div class="col-md-6">
                                                                        <img src="../Images/icons/icon-failed-count.png" style="height: 16px; vertical-align: text-bottom;" />
                                                                        Failed
                                                                    </div>
                                                                    :
                                        <div class="col-md-6" style="width: 49%; float: right">

                                            <%--<asp:Label ID="Label5" CssClass="col-md-4" Text="Failed Count" runat="server"></asp:Label> --%>

                                            <asp:Label ID="lblFailedCount" runat="server" CssClass="text-danger" Style="font-weight: bold">12343</asp:Label>
                                        </div>
                                                                </div>
                                                            </div>

                                                        </div>


                                                    </div>


                                                    <div class="modal-footer" style="padding: 5px 5px; background-color: #ececec">
                                                        <div class="col-xs-4 text-left">
                                                        </div>
                                                        <div class="col-xs-8 text-right padding-none-LR">

                                                            <asp:Button ID="btnOk" runat="server" Text="Ok" CssClass="btn btn-primary" Style="padding: 4px 16px; font-size: 13px" OnClick="btnOk_Click" />

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </asp:Panel>

                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>

                    </div>


                </div>

            </ContentTemplate>
        </asp:UpdatePanel>
        <asp:UpdateProgress ID="UpdateProgress1" runat="server">
            <ProgressTemplate>
                <div id="imgLoading" class="loading-mask">
                    <span>Loading...</span>
                </div>
            </ProgressTemplate>
        </asp:UpdateProgress>
    </form>
</body>
<script src="../Script/jquery.AssignZoneModal.js" type="text/javascript"></script>
<script src="../Script/jquery.modal.js" type="text/javascript"></script>
<script src="../Script/bootstrap.min.js"></script>
<script src="../Script/jquery.slimscroll.min.js"></script>
<script src="../Script/jquery.cookie.js"></script>
<script src="../Script/bootstrap-select.js"></script>
<script src="../Script/bootstrap-select.init.js"></script>
<script src="../Script/Custom-chkbox-rdbtn.js"></script>
<script src="../Script/core.init.js"></script>
</html>
