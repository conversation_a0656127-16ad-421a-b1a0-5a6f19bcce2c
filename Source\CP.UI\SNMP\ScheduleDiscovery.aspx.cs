﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceProcess;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using log4net;
using CP.Helper;
using CP.UI.Controls;
using CP.Common.Shared;
using System.Diagnostics;
using System.Net;
using System.Web;
using System.Globalization;

namespace CP.UI.SNMP
{
    public partial class ScheduleDiscovery : BasePage  // System.Web.UI.Page
    {
        #region Variables

        private static IFacade _facade = new Facade();
        //private readonly ILog _logger = LogManager.GetLogger(typeof(ScheduleDiscovery));

        private static readonly ILog _logger = LogManager.GetLogger(typeof(SNMPDiscovery));
        public static string IPAddress = string.Empty;
       
        #endregion

        #region Events

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                txtProfileName.Value = "";
                inIPAddressFrom.Value = "";
                inIPAddressTo.Value = "";
              
            }

        }

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

            if (IsUserOperator || IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            if (IsUserCustom)
            {
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.FirstOrDefault(x => x.AccessMenuType == AccessManagerType.Configuration.ToString());
                    if (ObjAccess == null)
                    {
                        Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                        return;
                    }
                }
                else
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }

            }
            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 
        }

        protected void ValidationFunctionName(object source, ServerValidateEventArgs args)
        {

            var alprofDet = _facade.GetAllScheduledDiscoveryProfilesDetails();

            if (alprofDet != null)
            {
                if (alprofDet.Count > 0)
                {
                    foreach (var g in alprofDet)
                    {
                        if (g.ScheDiscProfileName.ToLower() == txtProfileName.Value.ToLower())
                        {
                            args.IsValid = false;
                            txtProfileName.Focus();
                            break;
                        }
                    }
                }
            }

        }

        #endregion

        protected void btnSaveDiscoverySchedule_Click(object sender, EventArgs e)
        {
            try
            {
                if (Page.IsValid && (ViewState["_token"] != null) && ValidateRequest("GridConfiguration", UserActionType.CreateGridConfiguration))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        //Helper.Url.Redirect(returnUrl);

                        Response.Redirect(returnUrl);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {

                        ScheduleDiscoveryProfDetails scheduleDiscoveryProfDetails = new ScheduleDiscoveryProfDetails();

                        scheduleDiscoveryProfDetails.ScheDiscProfileName = txtProfileName.Value;
                        scheduleDiscoveryProfDetails.HostFrom = inIPAddressFrom.Value;
                        scheduleDiscoveryProfDetails.HostTo = inIPAddressTo.Value;
                        scheduleDiscoveryProfDetails.OsFilter = !string.IsNullOrEmpty(inOsName.Value) ? inOsName.Value : "all";
                        scheduleDiscoveryProfDetails.AppFilter = !string.IsNullOrEmpty(inApp.Value) ? inApp.Value : "all";
                        scheduleDiscoveryProfDetails.IsScheduled = 0;

                        if (scheduleDiscoveryProfDetails != null)
                        {
                            var ScheDiscProfDetails = _facade.AddScheduleDiscoveryProfileDetails(scheduleDiscoveryProfDetails);
                            if (ScheDiscProfDetails != null)
                            {
                                lblSaveMsg.Visible = true;
                                var sc = new ServiceController("CPParallelService");

                                if (sc.Status.ToString() != "Stopped")
                                {
                                    sc.ExecuteCommand(142);
                                }
                                else
                                {
                                    _logger.Info("CP Parallel Service in Stopped state");
                                }
                            }
                            else
                            {
                                _logger.Info("ScheDiscProfDetails is null");
                            }

                        }


                    }

                }
                else
                {
                    return;
                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while saving scheduleDiscoveryProfDetails data", ex);
                ExceptionManager.Manage(cpException);
            }
        }

        #region Methods
        #endregion


        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();
                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }
    }
}