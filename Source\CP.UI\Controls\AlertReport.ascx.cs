﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using Gios.Pdf;
using SpreadsheetGear;
using System.IO;
using log4net;


namespace CP.UI.Controls
{
    public partial class AlertReport : BaseControl
    {

        private readonly ILog _logger = LogManager.GetLogger(typeof(AlertReport));

        IList<Alert> data = new List<Alert>();
        IList<Alert> data2 = new List<Alert>();
        List<string> addhour = new List<string>();
        BusinessService bs = new BusinessService();
        public int hour = 0;
        public string from = null;
        public string to = null;

        public override void PrepareView()
        {
            lblGroup.Visible = false;
            CalendarExtender1.EndDate = DateTime.Now.Date;
            CalendarExtender2.EndDate = DateTime.Now.Date;
            lblMsg.Visible = false;
            ddlHrs.Items.Insert(0, new ListItem("Select Hour", "0"));

            IList<BusinessService> businessServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);
            businessServiceList = businessServiceList.OrderBy(a => a.Name).ToList();
          
            if (businessServiceList != null)
            {
                CblstGroup.DataSource = businessServiceList;
                CblstGroup.DataTextField = "Name";
                CblstGroup.DataValueField = "Id";
                CblstGroup.DataBind();
                CblstGroup.Items.Insert(0, new ListItem("ALL", "0"));
                lblGroup.Visible = false;
                btnview.Visible = true;
            }
            else
            {
                lblGroup.Text = "Applications Not Available.";
                lblGroup.Visible = true;
                btnview.Visible = false;
            }

            Pan1.Visible = false;
            btnview.Visible = true;
            btnExcel.Visible = false;
            btnPdf.Visible = false;

        }

        protected void txtstart_TextChanged(object sender, EventArgs e)
        {
            try
            {
                lblMsg.Visible = false;
                Pan1.Visible = false;
                if (!string.IsNullOrEmpty(txtstart.Text))
                {
                    if (chkHrly.Checked)
                    {

                        if (Convert.ToDateTime(txtstart.Text) < DateTime.Today)
                        {
                            for (int i = 1; i <= 24; i++)
                            {
                                if (i <= 9)
                                {
                                    addhour.Add("0" + i.ToString());
                                }
                                else
                                {
                                    addhour.Add(i.ToString());
                                }
                            }
                            ddlHrs.DataSource = addhour;
                            ddlHrs.DataBind();
                        }
                        else
                        {
                            hour = DateTime.Now.Hour;
                            for (int i = 1; i <= hour; i++)
                            {
                                if (i <= 9)
                                {
                                    addhour.Add("0" + i.ToString());
                                }
                                else
                                {
                                    addhour.Add(i.ToString());
                                }
                            }
                            ddlHrs.DataSource = addhour;
                            ddlHrs.DataBind();
                        }

                    }


                    var enddt = Convert.ToDateTime(txtstart.Text);
                    //enddt.AddDays(1);
                    CalendarExtender2.StartDate = enddt;
                }
                txtend.Text = string.Empty;
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured on Start Date Text Changed for User Activity Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void txtend_TextChanged(object sender, EventArgs e)
        {
            lblMsg.Text = "";
            lblMsg.Visible = false;
            Pan1.Visible = false;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            //RegisterPostBackControl();
        }

        protected void ddlusename_SelectedIndexChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
            lblMsg.Text = "";
        }

        private void RegisterPostBackControl()
        {
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnview);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(txtstart);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(txtend);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnPdf);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnExcel);
        }

        protected void btnview_Click1(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("btnview_Click1 Event Execution Start.");


                lblMsg.Text = string.Empty;
                lblMsg.Visible = false;
                IList<BusinessService> selectedBSList = new List<BusinessService>();
                List<int> appValue = new List<int>();
                if (!ValidateGroup())
                {
                    return;
                }
                else
                {
                    lblGroup.Text = string.Empty;
                    lblGroup.Visible = false;
                }

                if (chkHrly.Checked)
                {
                    txtend.Text = txtstart.Text;
                    _logger.Info("selected hour for hourly report : " + ddlHrs.SelectedValue);
                    int hour1 = Convert.ToInt32(ddlHrs.SelectedValue);
                    if (hour1 >= 12)
                    {
                        to = hour1.ToString();
                    }
                    else
                    {
                        to = hour1.ToString();
                    }

                    hour1 = hour1 - 1;
                    if (hour1 >= 12)
                    {
                        from = hour1.ToString();
                    }
                    else
                    {
                        from = hour1.ToString();
                    }

                    appValue.Clear();
                    selectedBSList.Clear();
                    foreach (ListItem item in CblstGroup.Items)
                    {
                        if (item.Selected)
                        {
                            appValue.Add(Convert.ToInt32(item.Value));

                            if (Convert.ToInt32(item.Value) != 0 && (item.Text).ToLower() != "all")
                            {
                                BusinessService bs = new BusinessService();
                                bs.Id = Convert.ToInt32(item.Value);
                                bs.Name = item.Text;
                                selectedBSList.Add(bs);
                            }
                        }
                    }


                    if (appValue != null && appValue.Count > 0 && selectedBSList != null)
                    {
                        _logger.Info("appValue Count " + appValue.Count + " & selectedBSList count " + selectedBSList.Count);
                        if (appValue.Contains(0))
                        {
                            data = Facade.GetAlertsByBusServDate(0, txtstart.Text, txtend.Text);
                            _logger.Info("Selected All Applications.");
                        }
                        else
                        {
                            
                            IList<Alert> allalertslst = Facade.GetAlertsByBusServDate(0, txtstart.Text, txtend.Text);
                            _logger.Info("Not Selected All Applications.");
                            if (allalertslst != null && allalertslst.Count() > 0)
                            {
                                _logger.Info("allalertslst Count " + allalertslst.Count);
                                IList<Alert> appdata = new List<Alert>();
                                foreach (int appid in appValue)
                                {
                                    foreach (Alert a in allalertslst)
                                    {
                                        if (a.BusinessServiceID == appid)
                                        {
                                            appdata.Add(a);
                                        }
                                    }
                                }

                                if (appdata != null && appdata.Count > 0)
                                {
                                    data = appdata;
                                    _logger.Info("appdata Count " + appdata.Count);
                                }
                                else
                                {
                                    data = null;
                                    _logger.Info("appdata Is Null.");
                                }
                            }
                            else
                            {
                                data = null;
                                _logger.Info("allalertslst Is Null.");
                            }
                        }
                    }
                    else
                    {
                        data = null;
                        _logger.Info("appValue & selectedBSList are NULL.");
                    }


                    if (data != null && data.Count > 0)
                    {
                        foreach (var datal in data)
                        {
                            string hrr1 = Convert.ToDateTime(datal.CreateDate).Hour.ToString();
                            if (Convert.ToInt32(hour1) == Convert.ToInt32(hrr1))
                            {
                                data2.Add(datal);
                            }
                        }
                        data = data2;

                        _logger.Info("Total records got for selected hour for hourly report : " + data.Count);
                    }
                }
                else
                {
                    //data = Facade.GetAlertsByBusServDate(Convert.ToInt32(ddlBusinessService.SelectedValue), txtstart.Text, txtend.Text);

                    //Get All Checked Application Id List 

                    appValue.Clear();
                    selectedBSList.Clear();
                    foreach (ListItem item in CblstGroup.Items)
                    {
                        if (item.Selected)
                        {
                            appValue.Add(Convert.ToInt32(item.Value));

                            if (Convert.ToInt32(item.Value) != 0 && (item.Text).ToLower() != "all")
                            {
                                BusinessService bs = new BusinessService();
                                bs.Id = Convert.ToInt32(item.Value);
                                bs.Name = item.Text;
                                selectedBSList.Add(bs);
                            }
                        }
                    }


                    if (appValue != null && appValue.Count > 0 && selectedBSList != null)
                    {
                        _logger.Info("appValue Count " + appValue.Count + " & selectedBSList count " + selectedBSList.Count);

                        if (appValue.Contains(0))

                        {
                            DateTime enddate = Convert.ToDateTime(txtend.Text);
                            string g = enddate.AddDays(1).ToString("yyyy-MM-dd");
                            data = Facade.GetAlertsByBusServDate(0, txtstart.Text, g);
                            _logger.Info("All Applications Selected.");
                        }
                        else
                        {
                            DateTime enddate = Convert.ToDateTime(txtend.Text);
                            string g = enddate.AddDays(1).ToString("yyyy-MM-dd");
                            IList<Alert> allalertslst = Facade.GetAlertsByBusServDate(0, txtstart.Text, g);
                          //  string d  = txtend.Text;
                          //  DateTime enddate =Convert.ToDateTime(d);                        
                          // // enddate.AddDays(1);
                          //  string g = enddate.AddDays(1).ToString("yyyy-MM-dd");
                            
                            


                        
                            
                          ////  IList<Alert> allalertslst = Facade.GetAlertsByBusServDate(bs.Id, txtstart.Text, txtend.Text);


                          //  IList<Alert> allalertslst = Facade.GetAlertsByBusServDate(bs.Id, txtstart.Text, g);
                            _logger.Info("Not All Applications Selected.");
                            if (allalertslst != null && allalertslst.Count() > 0)
                            {
                                _logger.Info("allalertslst Count " + allalertslst.Count);
                                IList<Alert> appdata = new List<Alert>();
                                foreach (int appid in appValue)
                                {
                                    foreach (Alert a in allalertslst)
                                    {
                                        if (a.BusinessServiceID == appid)
                                        {
                                            appdata.Add(a);
                                        }
                                    }
                                }

                                if (appdata != null && appdata.Count > 0)
                                {
                                    data = appdata;
                                    _logger.Info("AppData Count " + appdata.Count);
                                }
                                else
                                {
                                    data = null;
                                    _logger.Info("AppData Count Is Null.");
                                }
                            }
                            else
                            {
                                _logger.Info("allalertslst Is Null.");
                                data = null;
                            }
                        }
                    }
                    else
                    {
                        data = null;
                        _logger.Info("appValue  & selectedBSList Is Null.");
                    }
                }


                //CblstGroup.Items.te

                if (data != null && data.Count > 0)
                {
                    lblMsg.Visible = false;
                    data = (from dt in data orderby dt.CreateDate select dt).ToList();
                    _logger.Info("data Count For Alert Report " + data.Count);
                    ExcelReport_BS(data, selectedBSList);

                }
                else
                {

                    lblMsg.Visible = true;
                    lblMsg.Text = " Records Not Found..! ";

                    _logger.Info("Records Not Found For Alert Report.");
                }

                _logger.Info("btnview_Click1 Event Execution Completed.");
            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occurred In btnView_Click Event, Exception Message : " + ex.Message);
                _logger.Info(Environment.NewLine);
                if (ex.InnerException != null)
                    _logger.Info("Exception Occurred In btnView_Click Event, InnerException Message : " + ex.InnerException.Message);
                _logger.Info(Environment.NewLine);
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled Exception Occurred While Generating Alert Report ", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void ddlBusinessService_SelectedIndexChanged(object sender, EventArgs e)
        {
            Pan1.Visible = false;
            lblMsg.Text = string.Empty;
            btnview.Visible = true;
        }

        protected void ChkHrsCheckedChanged(object sender, EventArgs e)
        {

            lblMsg.Visible = false;
            lblMsg.Text = "";
            if (chkHrly.Checked)
            {
                lbldate.Text = "Select Date";
                PanendDate.Visible = false;
                Panelhrs.Visible = true;
                txtend.Text = string.Empty;
                txtstart.Text = string.Empty;
            }
            else
            {
                lbldate.Text = "Start Date";
                PanendDate.Visible = true;
                Panelhrs.Visible = false;

                txtstart.Text = string.Empty;
                txtend.Text = string.Empty;

                ddlHrs.Items.Clear();
                ddlHrs.Items.Insert(0, new ListItem("Select Hour", "0"));
            }
        }


        private void ExcelReport_BS(IList<Alert> alertsList, IList<BusinessService> selectedBS)
        {
            try
            {
                _logger.Info("ExcelReport_BS Method Execution Start.");

                _logger.Info("====== Generating Alert Report EXCEL View ======");
                _logger.Info(Environment.NewLine);

                IWorkbookSet workbookSet = null;
                String ssFile = string.Empty;
                IWorkbook templateWorkbook = null;
                IWorksheet templateWorksheet = null;
                IRange _cells = null;

                workbookSet = Factory.GetWorkbookSet();
                ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
                templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                templateWorksheet = templateWorkbook.Worksheets[0];

                IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
                IWorksheet reportWorksheet = null;
                IWorksheet lastWorksheet = null;

                #region code
                //lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                //reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
                //try
                //{
                //    ExcelReport(reportWorksheet);
                //}
                //catch (Exception ex)
                //{
                //    _logger.Info("exception occured in ExcelReport(reportWorksheet); " + ex.Message);
                //    _logger.Info(Environment.NewLine);
                //    if (ex.InnerException != null)
                //        _logger.Info("exception occured in ExcelReport(reportWorksheet); " + ex.InnerException);
                //    _logger.Info(Environment.NewLine);
                //    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate ExcelReport(reportWorksheet); ", ex, Page);
                //    ExceptionManager.Manage(customEx);
                //}
                #endregion code

                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

                 //reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
                reportWorkbook.Worksheets["Sheet1"].Delete();

                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
                reportWorksheet.Name = "Table";

                _cells["A1"].ColumnWidth = 7;

                string startdt = Utility.getFormatedDate_New(txtstart.Text);//string startdt = Utility.getFormatedDate(txtstart.Text);
                string enddt = Utility.getFormatedDate_New(txtend.Text);//string enddt = Utility.getFormatedDate(txtend.Text);  

                if (CblstGroup.Items[0].Selected)
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 1960, 10, 120, 13);
                }
                else if (CblstGroup.Items[6].Selected)
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 1030, 10, 120, 13);
                   
                }
                else if (CblstGroup.Items[5].Selected )
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 810, 10, 120, 13);
                }
                 else
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 570, 10, 120, 13);
                }

                

                    string strlogo = LoggedInUserCompany.CompanyLogoPath;
                
                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    //reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 370, 10, 121, 13);
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 370, 10, 85, 38);
                }

                reportWorksheet.Cells["A1:F1"].RowHeight = 42;
                reportWorksheet.Cells["A2:F2"].RowHeight = 20;

                _cells["B6"].Formula = "From Date";
                _cells["B6"].Font.Bold = true;
                _cells["B6"].HorizontalAlignment = HAlign.Left;
                //txtend.Text
                //_cells["C6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtend.Text; 
                _cells["C6"].Formula = ":  " + txtstart.Text;  //Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");
                _cells["C6"].Font.Bold = true;
                _cells["C6"].HorizontalAlignment = HAlign.Left;


                _cells["B5"].Formula = "Report Generated Time";
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;

                //_cells["C5"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));// dateTime;
                _cells["C5"].Formula = ":  " + Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss");// dateTime;
                _cells["C5"].Font.Bold = true;
                _cells["C5"].HorizontalAlignment = HAlign.Left;

                _cells["B1:Z1"].ColumnWidth = 23;
                _cells["B1:Z1"].Font.Color = Color.Black;

                int row = 8;
                int i = 1;

                string[] xlColumn = {"C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", 
                                     "AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", 
                                     "BA", "BB", "BC", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BK", "BL", "BM", "BN", "BO", "BP", "BQ", "BR", "BS", "BT", "BU", "BV", "BW", "BX", "BY", "BZ",
                                     "CA", "CB", "CC", "CD", "CE", "CF", "CG", "CH", "CI", "CJ", "CK", "CL", "CM", "CN", "CO", "CP", "CQ", "CR", "CS", "CT", "CU", "CV", "CW", "CX", "CY", "CZ",
                                     "DA", "DB", "DC", "DD", "DE", "DF", "DG", "DH", "DI", "DJ", "DK", "DL", "DM", "DN", "DO", "DP", "DQ", "DR", "DS", "DT", "DU", "DV", "DW", "DX", "DY", "DZ",
                                     "EA", "EB", "EC", "ED", "EE", "EF", "EG", "EH", "EI", "EJ", "EK", "EL", "EM", "EN", "EO", "EP", "EQ", "ER", "ES", "ET", "EU", "EV", "EW", "EX", "EY", "EZ",
                                     "FA", "FB", "FC", "FD", "FE", "FF", "FG", "FH", "FI", "FJ", "FK", "FL", "FM", "FN", "FO", "FP", "FQ", "FR", "FS", "FT", "FU", "FV", "FW", "FX", "FY", "FZ",
                                     "GA", "GB", "FC", "GD", "GE", "GF", "GG", "GH", "GI", "GJ", "GK", "GL", "GM", "GN", "GO", "GP", "GQ", "GR", "GS", "GT", "GU", "GV", "GW", "GX", "GY", "GZ",
                                     "HA", "HB", "HC", "HD", "HE", "HF", "HG", "HH", "HI", "HJ", "HK", "HL", "HM", "HN", "HO", "HP", "HQ", "HR", "HS", "HT", "HU", "HV", "HW", "HX", "HY", "HZ" };
                int column = 0;

                _cells["B" + row.ToString()].Formula = "TYPES OF ALERTS";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;

                var AllAlertDetails = alertsList;//Facade.GetAlertsByBusServDate(Convert.ToInt32(ddlBusinessService.SelectedValue), txtstart.Text, txtend.Text);

                if (AllAlertDetails != null && AllAlertDetails.Count > 0)
                {
                    _logger.Info("AllAlertDetails Is Not Null & Count " + AllAlertDetails.Count);
                }
                else
                {
                    _logger.Info("AllAlertDetails Is  Null & Count Is Zero.");
                }

                //var infraList = (from temp in data select new { temp.InfraObjectName }).Distinct();

                var alertTypeList = (from temp in AllAlertDetails select new { temp.Type }).Distinct();


                var bs = (from temp_1 in selectedBS select new { temp_1.Id, temp_1.Name }).Distinct(); ;//(from temp_1 in data select new { temp_1.BusinessServiceID, temp_1.BusinessServiceName }).Distinct();

                var allBS = bs.OrderBy(s => s.Name);
                var businessSer = (from temp in allBS select new { temp }).Distinct();

                //int infraCount = infraList.Count();
                int alertTypeCount = alertTypeList.Count();

                if (alertTypeCount > 0)
                {
                    _logger.Info("Total Alert Count alertTypeCount Is " + alertTypeCount);
                }
                else
                {
                    _logger.Info("Total Alert Count alertTypeCount Is Zero.");
                }

                int businessSerCount = businessSer.Count();

                if (businessSerCount > 0)
                {
                    column = businessSerCount;
                    _logger.Info("Total businessSerCount For Alert Reprot " + businessSerCount);
                }
                else
                {
                    _logger.Info("Total businessSerCount For Alert Reprot Is Zero.");
                }

                //if (ddlBusinessService.SelectedValue != "0")   // 1 BS Selected
                //{
                //    column = 1;
                //}
                //else   // All BS Selected
                //{
                //    column = businessSerCount;
                //}



                //if (businessSerCount < 6)
                if (alertTypeCount < 6)
                {
                    _logger.Info("Total alertTypeCount For Alert Reprot Is alertTypeCount " + alertTypeCount);

                    //_cells["B8:" + xlColumn[column - 1] + "8"].Interior.Color = Color.FromArgb(255, 204, 153);
                    _cells["B8:" + xlColumn[alertTypeCount - 1] + "8"].Interior.Color = Color.FromArgb(255, 204, 153);
                    _cells["B3:G3"].Merge();
                    _cells["B3:G3"].Font.Bold = true;
                    _cells["B3:G3"].Font.Size = 11;
                    _cells["B3:G3"].HorizontalAlignment = HAlign.Center;
                    _cells["B4:G4"].Merge();
                    _cells["B4:G4"].Font.Bold = false;
                    _cells["B4:G4"].Font.Size = 10;
                    _cells["B4:G4"].HorizontalAlignment = HAlign.Center;
                    if (chkHrly.Checked)
                    {
                        //_cells["B3:F3"].Merge();
                        _cells["B3:F3"].Formula = "Hourly Alert Report";
                        _cells["B3:F3"].HorizontalAlignment = HAlign.Center;
                        //_cells["B4:F4"].Merge();
                        _cells["B4:F4"].Formula = "Alerts as on :" + txtstart.Text + " between " + from + " to " + to + " Hrs";
                        _cells["B4:F4"].HorizontalAlignment = HAlign.Center;

                        _logger.Info("Hourly CheckBox Cheked.");
                    }
                    else
                    {
                        //_cells["B3:F3"].Merge();
                        _cells["B3:F3"].Formula = "Alert Report";
                        _cells["B3:F3"].HorizontalAlignment = HAlign.Center;

                        _logger.Info("Hourly CheckBox Not Cheked.");
                    }
                    _cells["E3"].HorizontalAlignment = HAlign.Center;
                    _cells["E3"].Font.Bold = true;
                    _cells["B3:G6"].Interior.Color = Color.FromArgb(79, 129, 189);

                    _cells["D3"].HorizontalAlignment = HAlign.Center;
                    _cells["D3"].VerticalAlignment = VAlign.Top;
                    _cells["B3:G3"].Font.Size = 11;
                    _cells["B5:G8"].Font.Size = 10;
                    _cells["B3:G6"].Font.Color = Color.White;
                    _cells["B3:G6"].Font.Name = "Cambria";

                    //_cells["G6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtstart.Text;
                    _cells["G6"].Formula = ":  " + txtend.Text;
                    _cells["G6"].HorizontalAlignment = HAlign.Left;

                    //_cells["F5"].Formula = "Business Service Name : ";
                    //_cells["F5"].Font.Bold = true;
                    //_cells["F5"].HorizontalAlignment = HAlign.Right;

                   // _cells["G5"].Formula = "Infra Object Name : " + businessSer;
                    //_cells["G5"].Formula = ;
                    //_cells["G5"].Font.Bold = true;
                    //_cells["G5"].HorizontalAlignment = HAlign.Left;


                    _cells["F6"].Formula = "To Date";
                    _cells["F6"].Font.Bold = true;
                    _cells["F6"].HorizontalAlignment = HAlign.Left;

                    _logger.Info("Alert Report Header Design Completed.");
                }
                else
                {
                    _logger.Info("Total alert Type Count For Alert Reprot Is alertTypeCount " + alertTypeCount);
                    //_cells["B8:" + xlColumn[column - 1] + "8"].Interior.Color = Color.FromArgb(255, 204, 153);

                    //_cells["B3:" + xlColumn[column - 1] + "3"].Merge();
                    _cells["B8:" + xlColumn[alertTypeCount - 1] + "8"].Interior.Color = Color.FromArgb(255, 204, 153);

                    _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].Merge();
                    if (chkHrly.Checked)
                    {
                        // _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].Merge();
                        _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].HorizontalAlignment = HAlign.Center;
                        _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].Formula = "Hourly Alert Report";
                        _cells["B4:" + xlColumn[alertTypeCount - 1] + "4"].Merge();
                        _cells["B4:" + xlColumn[alertTypeCount - 1] + "4"].HorizontalAlignment = HAlign.Center;
                        _cells["B4:" + xlColumn[alertTypeCount - 1] + "4"].Formula = "Alerts as on :" + txtstart.Text + " between " + from + " to " + to + " Hrs";
                        //_cells["B3:F3"].Formula = "Hourly Alert Report";
                        //_cells["B4:F4"].Formula = "Alerts as on :" + txtstart.Text + " between " + from + " to " + to + " Hrs";
                        _logger.Info("Report Type Is Hourly Alert Report.");
                    }
                    else
                    {
                        //_cells["B3:F3"].Formula = "Alert Report";
                        // _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].Merge();
                        _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].HorizontalAlignment = HAlign.Center;
                        _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].Formula = "Alert Report";

                        _logger.Info("Report Type Is Only Alert Report.");
                    }
                    _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].HorizontalAlignment = HAlign.Center;
                    _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].Font.Bold = true;
                    _cells["B3:" + xlColumn[alertTypeCount - 1] + "6"].Interior.Color = Color.FromArgb(79, 129, 189);

                    _cells["D3"].HorizontalAlignment = HAlign.Center;
                    _cells["D3"].VerticalAlignment = VAlign.Top;
                    _cells["B3:" + xlColumn[alertTypeCount - 1] + "3"].Font.Size = 11;
                    _cells["B5:" + xlColumn[alertTypeCount - 1] + "8"].Font.Size = 10;
                    _cells["B3:" + xlColumn[alertTypeCount - 1] + "6"].Font.Color = Color.White;
                    _cells["B3:" + xlColumn[alertTypeCount - 1] + "6"].Font.Name = "Cambria";

                    //_cells[xlColumn[alertTypeCount - 1] + "6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtend.Text;
                    _cells[xlColumn[alertTypeCount - 1] + "6"].Formula = ":  " + txtend.Text;
                    _cells[xlColumn[alertTypeCount - 1] + "6"].Font.Bold = true;
                    _cells[xlColumn[alertTypeCount - 1] + "6"].HorizontalAlignment = HAlign.Left;

                    //_cells[xlColumn[alertTypeCount - 2] + "5"].Formula = "Business Service Name : " + ddlBusinessService.SelectedItem.Text;
                    //_cells[xlColumn[alertTypeCount - 2] + "5"].Font.Bold = true;
                    //_cells[xlColumn[alertTypeCount - 2] + "5"].HorizontalAlignment = HAlign.Left;

                    _cells[xlColumn[alertTypeCount - 2] + "6"].Formula = "To Date";
                    _cells[xlColumn[alertTypeCount - 2] + "6"].Font.Bold = true;
                    _cells[xlColumn[alertTypeCount - 2] + "6"].HorizontalAlignment = HAlign.Left;

                    _logger.Info("Alert Report Header Creation Completed.");

                }
                //int br = alertTypeCount + 8;
                //IRange range = reportWorksheet.Cells["B8:" + xlColumn[column - 1] + br];
                int br = column + 8;
                IRange range = reportWorksheet.Cells["B8:" + xlColumn[alertTypeCount - 1] + br];
                IBorder border_B = range.Borders[BordersIndex.EdgeBottom];
                IBorder border_T = range.Borders[BordersIndex.EdgeTop];
                IBorder border_L = range.Borders[BordersIndex.EdgeLeft];
                IBorder border_R = range.Borders[BordersIndex.EdgeRight];
                border_B.LineStyle = LineStyle.Continous;
                border_B.Color = Color.Black;
                border_B.Weight = BorderWeight.Thin;
                border_T.LineStyle = LineStyle.Continous;
                border_T.Color = Color.Black;
                border_T.Weight = BorderWeight.Thin;
                border_L.LineStyle = LineStyle.Continous;
                border_L.Color = Color.Black;
                border_L.Weight = BorderWeight.Thin;
                border_R.LineStyle = LineStyle.Continous;
                border_R.Color = Color.Black;
                border_R.Weight = BorderWeight.Thin;

                //IRange range_ = reportWorksheet.Cells["B8:" + xlColumn[column - 1] + "8"];
                IRange range_ = reportWorksheet.Cells["B8:" + xlColumn[alertTypeCount - 1] + "8"];
                IBorder border = range_.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.Black;
                border.Weight = BorderWeight.Thin;

                _logger.Info("Alert Report Table Desgin Completed.");

                _logger.Info("Alert Type Mapping Start For Alert Report.");

                if (alertTypeList != null)
                {

                    int colvalue = 0;
                    foreach (var t in alertTypeList)
                    {
                        string ndx = xlColumn[colvalue] + row.ToString();
                        _cells[ndx].Formula = t.Type;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].ColumnWidth = 27;
                        _cells[ndx].Borders.Color = Color.Black;
                        _cells[ndx].WrapText = true;
                        colvalue++;
                    }
                }
                _logger.Info("Alert Type Mapping Completed For Alert Report.");

                //string[] xlColumn_ = { "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", 
                //                     "AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", 
                //                     "BA", "BB", "BC", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BK", "BL", "BM", "BV" };

                string[] xlColumn_ = {"C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", 
                                     "AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", 
                                     "BA", "BB", "BC", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BK", "BL", "BM", "BN", "BO", "BP", "BQ", "BR", "BS", "BT", "BU", "BV", "BW", "BX", "BY", "BZ",
                                     "CA", "CB", "CC", "CD", "CE", "CF", "CG", "CH", "CI", "CJ", "CK", "CL", "CM", "CN", "CO", "CP", "CQ", "CR", "CS", "CT", "CU", "CV", "CW", "CX", "CY", "CZ",
                                     "DA", "DB", "DC", "DD", "DE", "DF", "DG", "DH", "DI", "DJ", "DK", "DL", "DM", "DN", "DO", "DP", "DQ", "DR", "DS", "DT", "DU", "DV", "DW", "DX", "DY", "DZ",
                                     "EA", "EB", "EC", "ED", "EE", "EF", "EG", "EH", "EI", "EJ", "EK", "EL", "EM", "EN", "EO", "EP", "EQ", "ER", "ES", "ET", "EU", "EV", "EW", "EX", "EY", "EZ",
                                     "FA", "FB", "FC", "FD", "FE", "FF", "FG", "FH", "FI", "FJ", "FK", "FL", "FM", "FN", "FO", "FP", "FQ", "FR", "FS", "FT", "FU", "FV", "FW", "FX", "FY", "FZ",
                                     "GA", "GB", "FC", "GD", "GE", "GF", "GG", "GH", "GI", "GJ", "GK", "GL", "GM", "GN", "GO", "GP", "GQ", "GR", "GS", "GT", "GU", "GV", "GW", "GX", "GY", "GZ",
                                     "HA", "HB", "HC", "HD", "HE", "HF", "HG", "HH", "HI", "HJ", "HK", "HL", "HM", "HN", "HO", "HP", "HQ", "HR", "HS", "HT", "HU", "HV", "HW", "HX", "HY", "HZ" };
                int column_ = 0;

                #region ddloldcode

                //if (ddlBusinessService.SelectedValue != "0")
                //{
                //    _logger.Info("Business Service Selected Value For Alert Report " + ddlBusinessService.SelectedValue);

                //    if (data != null && data.Count > 0)
                //    {
                //        _logger.Info("data Is Not Null & Count " + data.Count);
                //        var infr = Facade.GetInfraObjectByBusinessServiceId(Convert.ToInt32(ddlBusinessService.SelectedValue));

                //        //if (businessSer != null && alertTypeList != null)
                //        //{
                //        //    string ndx = xlColumn[0] + row.ToString();
                //        //    _cells[ndx].Formula = ddlBusinessService.SelectedItem.Text.ToString();
                //        //    _cells[ndx].Font.Bold = true;
                //        //    _cells[ndx].HorizontalAlignment = HAlign.Center;
                //        //    _cells[ndx].Borders.Color = Color.Black;
                //        //    column++;
                //        //}

                //        if (businessSer != null && alertTypeList != null)
                //        {
                //            string ndx = "B9";//xlColumn[0] + row.ToString();
                //            _cells[ndx].Formula = ddlBusinessService.SelectedItem.Text.ToString();
                //            _cells[ndx].Font.Bold = true;
                //            _cells[ndx].HorizontalAlignment = HAlign.Left;
                //            _cells[ndx].Borders.Color = Color.Black;
                //            _cells[ndx].WrapText = true;
                //            _cells[ndx].ColumnWidth = 27;
                //            //column++;

                //            _logger.Info("businessSer & alertTypeList Not Null.");
                //        }
                //        else
                //        {
                //            _logger.Info("businessSer & alertTypeList Is Null.");
                //        }

                //        int r = 9;
                //        int c = 0;
                //        int typecolumn = 0;
                //        if (alertTypeList != null)
                //        {
                //            _logger.Info("alertTypeList Is Not Null.");

                //            foreach (var alt in alertTypeList)
                //            {
                //                int k = 0;
                //                int totalCount = 0;
                //                if (infr != null)
                //                {
                //                    _logger.Info("infr Is Not Null.");

                //                    foreach (var inf in infr)
                //                    {
                //                        //string ndx = xlColumn_[column_] + r.ToString();
                //                        string ndx = xlColumn_[typecolumn] + r.ToString();
                //                        int cnt = (from temp in alertsList
                //                                   where temp.InfraObjectId == inf.Id && temp.Type == alt.Type
                //                                   select temp).Count();

                //                        totalCount = totalCount + Convert.ToInt32(cnt);
                //                        _cells[ndx].Formula = totalCount.ToString(); ;
                //                        _cells[ndx].Font.Bold = false;
                //                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                //                        _cells[ndx].Borders.Color = Color.Black;
                //                        _cells[ndx].ColumnWidth = 27;
                //                        k++;
                //                        if (k == infr.Count)
                //                        {
                //                            //r++;
                //                            //column_++;
                //                            typecolumn++;
                //                        }
                //                    }
                //                }
                //                else
                //                {
                //                    _logger.Info("infr Is  Null.");
                //                    // string null_Ndx = xlColumn_[column_] + r.ToString();
                //                    string null_Ndx = xlColumn_[typecolumn] + r.ToString();
                //                    _cells[null_Ndx].Formula = "0";
                //                    _cells[null_Ndx].Font.Bold = false;
                //                    _cells[null_Ndx].HorizontalAlignment = HAlign.Center;
                //                    _cells[null_Ndx].Borders.Color = Color.Black;
                //                    _cells[null_Ndx].ColumnWidth = 27;
                //                    //r++;
                //                    //column_++;
                //                    typecolumn++;
                //                }

                //            }
                //            _logger.Info("Alert Count Printed For Report.");
                //            //column_++;
                //            r++;
                //        }
                //        else
                //        {
                //            _logger.Info("alertTypeList Is Null.");
                //            //column_++;
                //            r++;
                //        }
                //        //}
                //    }
                //    else
                //    {
                //        _logger.Info("data Is  Null & Count Is Zero.");
                //        string ndx = "B9";//xlColumn[0] + row.ToString();
                //        _cells[ndx].Formula = ddlBusinessService.SelectedItem.Text.ToString();
                //        _cells[ndx].Font.Bold = true;
                //        _cells[ndx].HorizontalAlignment = HAlign.Center;
                //        _cells[ndx].Borders.Color = Color.Black;
                //        _cells[ndx].ColumnWidth = 27;

                //        //column++;

                //        int r = 9;
                //        int c = 0;

                //        if (alertTypeList != null)
                //        {
                //            foreach (var alt in alertTypeList)
                //            {
                //                int k = 0;
                //                int totalCount = 0;

                //                string null_Ndx = xlColumn_[column_] + r.ToString();
                //                _cells[null_Ndx].Formula = "0";
                //                _cells[null_Ndx].Font.Bold = false;
                //                _cells[null_Ndx].HorizontalAlignment = HAlign.Center;
                //                _cells[null_Ndx].Borders.Color = Color.Black;
                //                _cells[null_Ndx].ColumnWidth = 27;
                //                //r++;
                //                column_++;
                //            }
                //        }
                //    }
                //}//END IF
                //else   // All Business Service
                //{
                #endregion ddloldcode

                _logger.Info("Alert Report Creation For All Business Service.");

                if (alertsList != null && alertsList.Count > 0)
                {
                    _logger.Info("alertsList Is Not Null & Count " + alertsList.Count + " For All Business Service.");

                    if (businessSer != null && alertTypeList != null)
                    {
                        _logger.Info("businessSer & alertTypeList Is Not Null.");
                        //int colvalue = 0;
                        int a = 9;
                        foreach (var t in businessSer)
                        {
                            // string ndx = xlColumn[colvalue] + row.ToString();   
                            string ndx = "B" + Convert.ToString(a);
                            _cells[ndx].Formula = t.temp.Name; //t.temp.BusinessServiceName;  
                            _cells[ndx].Font.Bold = true;
                            _cells[ndx].HorizontalAlignment = HAlign.Left;
                            _cells[ndx].Borders.Color = Color.Black;
                            _cells[ndx].WrapText = true;
                            _cells[ndx].ColumnWidth = 27;
                            //colvalue++;
                            a++;
                        }
                    }
                    else
                    {
                        _logger.Info("businessSer & alertTypeList Is Null.");
                    }

                    int bsrow = 9;
                    foreach (var bsloop in businessSer)
                    {
                        var infr = Facade.GetInfraObjectByBusinessServiceId(bsloop.temp.Id);  //Facade.GetInfraObjectByBusinessServiceId(bsloop.temp.BusinessServiceID);  
                        int r = 9;
                        int c = 0;

                        int type = 0;
                        if (alertTypeList != null)
                        {
                            foreach (var alt in alertTypeList)
                            {
                                int k = 0;
                                int totalCount = 0;
                                if (infr != null)
                                {
                                    foreach (var inf in infr)
                                    {
                                        //string ndx = xlColumn_[column_] + r.ToString();
                                        string ndx = xlColumn_[type] + bsrow.ToString();

                                        int cnt = (from temp in alertsList
                                                   where temp.InfraObjectId == inf.Id && temp.Type == alt.Type
                                                   select temp).Count();

                                        totalCount = totalCount + Convert.ToInt32(cnt);
                                        _cells[ndx].Formula = totalCount.ToString(); ;
                                        _cells[ndx].Font.Bold = false;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        _cells[ndx].Borders.Color = Color.Black;
                                        _cells[ndx].ColumnWidth = 27;
                                        k++;
                                        if (k == infr.Count)
                                        {
                                            //r++;
                                            //column_++;
                                            type++;
                                        }
                                    }
                                }
                                else
                                {
                                    //string null_Ndx = xlColumn_[column_] + r.ToString();
                                    string null_Ndx = xlColumn_[type] + bsrow.ToString();
                                    _cells[null_Ndx].Formula = "0";
                                    _cells[null_Ndx].Font.Bold = false;
                                    _cells[null_Ndx].HorizontalAlignment = HAlign.Center;
                                    _cells[null_Ndx].Borders.Color = Color.Black;
                                    _cells[null_Ndx].ColumnWidth = 27;
                                    //r++;
                                    //column_++;
                                    type++;
                                }

                            }
                            //column_++;
                            //r++;
                            bsrow++;
                        }
                        else
                        {
                            //column_++;
                            //r++;
                            bsrow++;
                        }
                    }
                    _logger.Info("Alert Type Count Is Printed For All Business Service.");
                }
                else
                {
                    _logger.Info("alertsList Is  Null & Count Is Zero For All Business Service.");
                }


                //}//else end for all bs

                #region GraphCode
                //SpreadsheetGear.IWorksheetWindowInfo windowInfo = reportWorksheet.WindowInfo;


                //SpreadsheetGear.IRange dataRange = reportWorksheet.Cells["B8:" + xlColumn[column - 1] + br];
                //if (ddlBusinessService.SelectedValue != "0")  // 1 BS Selected
                //{
                //    dataRange = reportWorksheet.Cells["B8:" + "C" + br];
                //}
                //else    // All BS Selected
                //{
                //    dataRange = reportWorksheet.Cells["B8:" + xlColumn[column - 1] + br];
                //}
                ////double left = windowInfo.ColumnToPoints(50.0);
                ////double top = windowInfo.RowToPoints(50.0);
                ////double right = windowInfo.ColumnToPoints(50.0);
                ////double bottom = windowInfo.RowToPoints(50.0);
                //// Add a chart to the worksheet's shape collection.
                //// NOTE: Calculate coordinates in points (72 points per inch)
                ////SpreadsheetGear.Shapes.IShape shape =

                //// SpreadsheetGear.Shapes.IShape shape;
                //// reportWorksheet.Shapes.AddChart(550.0, 90.0, 4 * 72, 3 * 50);

                //// reportWorksheet.Shapes.AddChart(250.0, 250.0, 10 * 72, 5 * 50);
                //int alertTypeCount_ = 8;
                //if (alertTypeCount <= 7)
                //{
                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 250.0, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;
                //}
                //else if (alertTypeCount > 7 && alertTypeCount <= 14)
                //{

                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 360.0, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;

                //}

                //else if (alertTypeCount > 14 && alertTypeCount <= 21)
                //{

                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 470, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;

                //}

                //else if (alertTypeCount > 21 && alertTypeCount <= 28)
                //{

                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 580, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;

                //}
                //else if (alertTypeCount > 28 && alertTypeCount <= 35)
                //{

                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 690, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;

                //}

                //else if (alertTypeCount > 35 && alertTypeCount <= 42)
                //{

                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 760, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;

                //}
                //else if (alertTypeCount > 42 && alertTypeCount <= 49)
                //{

                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 880, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;

                //}
                //else if (alertTypeCount > 49 && alertTypeCount <= 56)
                //{

                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 1000, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;

                //}

                //else
                //{

                //    SpreadsheetGear.Shapes.IShape shape = reportWorksheet.Shapes.AddChart(45, 1120, 14 * 72, 7 * 50);
                //    SpreadsheetGear.Charts.IChart chart = shape.Chart;
                //    chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                //    chart.ChartType = SpreadsheetGear.Charts.ChartType.Line;
                //    chart.ChartGroups[0].GapWidth = 50;
                //    chart.HasTitle = true;
                //    chart.ChartTitle.Text = "Alert Details";
                //    chart.ChartTitle.Font.Size = 12;
                //}
                #endregion GraphCode

                reportWorksheet.ProtectContents = true;
                OpenExcelFile(reportWorkbook);

                _logger.Info("====== Alert Report Generated ======");
                _logger.Info(Environment.NewLine);
                _logger.Info("ExcelReport_BS Method Execution Completed.");
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ExcelReport_BS Method, Error Message " + ex.Message);
                _logger.Info(Environment.NewLine);

                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ExcelReport_BS Method, InnerException Message " + ex.InnerException.Message);

                _logger.Info(Environment.NewLine);
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled Exception Occured In  ExcelReport_BS Method Of Alert Report ", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }


        public void ExcelReport(IWorksheet reportWorksheet)
        {
            //var data = GetAlertDetails();

            //data = Facade.GetAlertsByBusServDate(Convert.ToInt32(ddlBusinessService.SelectedValue), txtstart.Text, txtend.Text);
            IRange _cells = null;
            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;

            if (chkHrly.Checked)
            {
                reportWorksheet.Name = "Hourly Alert Report";
            }
            else
            {
                reportWorksheet.Name = "Alert Report";
            }

            _cells["A1"].ColumnWidth = 7;

            _cells["B3:H3"].Merge();
            _cells["B4:H4"].Merge();
            _cells["B4:H4"].Font.Bold = false;
            _cells["B4:H4"].Font.Size = 10;
            _cells["B4:H4"].HorizontalAlignment = HAlign.Center;
            if (chkHrly.Checked)
            {
                _cells["B3:G3"].Formula = "Hourly Alert Report";
                _cells["B4:G4"].Formula = "Alerts as on :" + txtstart.Text + " between " + from + " to " + to + " Hrs";

                //if (chkHrly.Checked)
                //{
                //    _cells["B3:F3"].Formula = "Hourly Alert Report";
            }
            else
            {
                _cells["B3:H3"].Formula = "Alert Report";
                
            }
            //_cells["B3:F3"].Formula = "Alert Report";
            _cells["B3:H3"].HorizontalAlignment = HAlign.Center;
            _cells["B3:H3"].Font.Bold = true;
            _cells["B3:H6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:H3"].Font.Size = 11;
            _cells["B5:H8"].Font.Size = 10;
            _cells["B3:H8"].Font.Color = Color.White;
            _cells["B3:H8"].Font.Name = "Cambria";

            string startdt = Utility.getFormatedDate_New(txtstart.Text);//string startdt = Utility.getFormatedDate(txtstart.Text);
            string enddt = Utility.getFormatedDate_New(txtend.Text);//string enddt = Utility.getFormatedDate(txtend.Text);            

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 520, 10, 120, 13);
            string strlogo = LoggedInUserCompany.CompanyLogoPath;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 370, 10, 121, 13);
            }

            reportWorksheet.Cells["A1:H1"].RowHeight = 27;

            _cells["B6"].Formula = "From Date";
            _cells["B6"].Font.Bold = true;
            _cells["B6"].HorizontalAlignment = HAlign.Left;
            //txtstart.Text
            _cells["C6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");

            _cells["C6"].Font.Bold = true;
            _cells["C6"].HorizontalAlignment = HAlign.Left;

            // var dateTime = DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss");
            _cells["B5"].Formula = "Report Generated Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            _cells["C5"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));// dateTime;
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;

            //_cells["F5"].Formula = "Business Service Name : ";
            //_cells["F5"].Font.Bold = true;
            //_cells["F5"].HorizontalAlignment = HAlign.Right;

            //_cells["G5"].Formula = ddlBusinessService.SelectedItem.Text;
            //_cells["G5"].Font.Bold = true;
            //_cells["G5"].HorizontalAlignment = HAlign.Left;

            _cells["F6"].Formula = "To Date" + " :  ";
            _cells["F6"].Font.Bold = true;
            _cells["F6"].HorizontalAlignment = HAlign.Right;

            _cells["G6"].NumberFormat = "@";
            _cells["G6"].Formula = Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");
            _cells["G6"].Font.Bold = true;
            _cells["G6"].HorizontalAlignment = HAlign.Left;

            //_cells["G6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtend.Text;
            //_cells["G6"].Font.Bold = true;
            //_cells["G6"].HorizontalAlignment = HAlign.Left;

            int row = 8;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "Sr.No.";
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
            _cells["B8:H8"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B8:H8"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "Type";
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
            productionlog.WrapText = true;

            _cells["D" + row.ToString()].Formula = "Severity";
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["E" + row.ToString()].Formula = "Description";
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

            //_cells["F" + row.ToString()].Formula = "Job Name";
            //_cells["F" + row.ToString()].Font.Bold = true;
            //_cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["F" + row.ToString()].Formula = "IPAddress";
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["G" + row.ToString()].Formula = "Host Name";
            _cells["G" + row.ToString()].Font.Bold = true;
            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["H" + row.ToString()].Formula = "Create Date";
            _cells["H" + row.ToString()].Font.Bold = true;
            _cells["H" + row.ToString()].HorizontalAlignment = HAlign.Left;



            row++;
            int dataCount = 0;
            int xlRow = 9;

            foreach (var rp in data)
            {
                dataCount++;
                int column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H" };
                xlRow++;

                string ndx = xlColumn[column] + row.ToString();
                _cells[ndx + ":" + "H" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;

                _cells[ndx].Formula = i.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                i++;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.Type != null ? rp.Type : "NA";
                _cells[ndx].WrapText = true;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.Severity != null ? rp.Severity : "NA";
                _cells[ndx].WrapText = true;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                _cells[ndx].WrapText = true;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.UserDefinedMessage != null ? rp.UserDefinedMessage : "NA";
                _cells[ndx].WrapText = true;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 50;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;


                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.IPAddress != null && rp.IPAddress != "" ? rp.IPAddress : "NA";
                _cells[ndx].WrapText = true;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 15;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                //ndx = xlColumn[column] + row.ToString();
                //_cells[ndx].Formula = rp.JobName != null ? rp.JobName : "NA";
                //_cells[ndx].WrapText = true;
                //_cells[ndx].Font.Size = 10;
                //_cells[ndx].ColumnWidth = 23;
                //_cells[ndx].Font.Color = Color.Black;
                //_cells[ndx].HorizontalAlignment = HAlign.Left;
                //_cells[ndx].WrapText = true;
                //column++;


                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.HostName != null && rp.HostName != "" ? rp.HostName : "NA";
                _cells[ndx].WrapText = true;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 15;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].EntireColumn.NumberFormat = "@";
                _cells[ndx].Formula = rp.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(rp.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                _cells[ndx].WrapText = true;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;



                row++;
            }


            int finalCount = dataCount + 10;
            _cells["B" + finalCount].Formula = "NA : Not Available";
            _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
            _cells["B" + finalCount].Font.Name = "Cambria";
            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;
        }

        private void OpenExcelFile(IWorkbook workbook)
        {

            try
            {
                _logger.Info("OpenExcelFile Method Execution Start For Alert Report.");

                Response.Clear();
                Response.ContentType = "application/vnd.ms-excel";
                Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
                var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
                if (chkHrly.Checked)
                {
                    str = "Hourly_AlertReport" + str + ".xls";
                }
                else
                {
                    str = "Alert Report" + str + ".xls";
                }
                string finalstr = !String.IsNullOrWhiteSpace(str) && str.Length >= 40 ? str.Substring(0, 40) : str;
                str = finalstr + ".xls";
                workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

                string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";

                string myUrl = baseUrl + "ExcelFiles/" + str;


                // string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
                // string myUrl = reportPath + "/ExcelFiles/" + str;
                //var myUrl = "/ExcelFiles/" + str;
                var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=License Utilization Report');";
                ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In OpenExcelFile Method, Error Message " + ex.Message);

                if (ex.InnerException != null)
                {
                    _logger.Error("Exception Occurred In OpenExcelFile Method, InnerException Message " + ex.InnerException.Message);
                }
            }
        }
         

protected void CblstGroupSelectedIndexChanged(object sender, EventArgs e)
        {
            lblGroup.Visible = false;
            lblMsg.Text = string.Empty;
            lblMsg.Visible = false;
            string result = Request.Form["__EVENTTARGET"];
            string[] checkedBox = result.Split('$');
            int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);

            if (checkedIndex == 0)
            {
                if (CblstGroup.Items[0].Selected)
                {
                    for (int i = 1; i < CblstGroup.Items.Count; i++)
                    {
                        CblstGroup.Items[i].Selected = true;
                        CblstGroup.Items[i].Enabled = true;
                    }
                }
                else
                {
                    for (int i = 1; i < CblstGroup.Items.Count; i++)
                    {
                        CblstGroup.Items[i].Selected = false;
                        CblstGroup.Items[i].Enabled = true;
                    }
                }
            }
            else
            {
                CheckBoxListAllSelected(CblstGroup);
            }

        }

        private void CheckBoxListAllSelected(CheckBoxList grpOrAppList)
        {
            int selectedKwt = 0;

            for (int i = 1; i < grpOrAppList.Items.Count; i++)
            {
                if (grpOrAppList.Items[i].Selected)
                {
                    selectedKwt++;
                }
                grpOrAppList.Items[i].Enabled = true;
            }

            if (selectedKwt == grpOrAppList.Items.Count - 1)
            {
                grpOrAppList.Items[0].Selected = true;
            }
            else
            {
                grpOrAppList.Items[0].Selected = false;
            }
        }

        private bool ValidateGroup()
        {
            var selectedGroupItem = Utility.GetSelectedItem(CblstGroup);

            if (selectedGroupItem.Count == 0)
            {
                lblGroup.Text = "Please Select Application Name";
                lblGroup.Visible = true;
                return false;
            }
            lblGroup.Text = string.Empty;
            lblGroup.Visible = false;
            return true;
        }
    }
}