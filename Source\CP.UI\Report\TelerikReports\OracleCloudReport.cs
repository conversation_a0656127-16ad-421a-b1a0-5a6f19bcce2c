namespace CP.UI.Report.TelerikReports
{
    using CP.Common.DatabaseEntity;
    using log4net;
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Data;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;

    /// <summary>
    /// Summary description for MssqlDatalagReport.
    /// </summary>
    public partial class OracleCloudReport : Telerik.Reporting.Report
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(OracleCloudReport));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();

        public OracleCloudReport()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }
        private void showtable()
        {
            try
            {
                var dataTable = new DataTable();

                string strDate = Convert.ToDateTime(this.ReportParameters["iStartDate"].Value).ToString("yyyy-MM-dd");
                string endDate = Convert.ToDateTime(this.ReportParameters["iEndDate"].Value).ToString("yyyy-MM-dd");
                int infraObjectId = Convert.ToInt32(this.ReportParameters["iInfraId"].Value);

                IList<OracleCloudInstanceLevelMonitoring> Oraclecloud_list = new List<OracleCloudInstanceLevelMonitoring>();
                Oraclecloud_list = Facade.GetOracleCloudInstanceLevelGetBYDate(infraObjectId, strDate, endDate);

                if (Oraclecloud_list != null && Oraclecloud_list.Count > 0)
                {
                    dataTable.Columns.Add("Sr.No.");
                    dataTable.Columns.Add("CompartmentName");
                    dataTable.Columns.Add("InstanceName");
                    dataTable.Columns.Add("State");
                    dataTable.Columns.Add("Shape");
                    dataTable.Columns.Add("AvailabilityDomain");
                    dataTable.Columns.Add("FaultDomain");
                    dataTable.Columns.Add("CreateDate");
                    _logger.Info("Data Mapping Start For Report.");

                   

                    int i = 1;
                    foreach (OracleCloudInstanceLevelMonitoring vsmoni in Oraclecloud_list)
                    {
                        DataRow dr = dataTable.NewRow();
                        dr["Sr.No."] = i.ToString();
                        dr["CompartmentName"] = vsmoni.CompartmentName != null ? vsmoni.CompartmentName : "N/A";
                        dr["InstanceName"] = vsmoni.InstanceName != null ? vsmoni.InstanceName : "N/A";

                        dr["State"] = vsmoni.State != null ? vsmoni.State : "N/A";
                        dr["Shape"] = vsmoni.Shape != null ? vsmoni.Shape : "N/A";
                        dr["AvailabilityDomain"] = vsmoni.AvailabilityDomain != null ? vsmoni.AvailabilityDomain : "N/A";
                        dr["FaultDomain"] = vsmoni.FaultDomain != null ? vsmoni.FaultDomain : "N/A";



                        dr["CreateDate"] = Utility.Formatdate(Convert.ToDateTime(vsmoni.CreateDate).ToString("MM-dd-yyyy HH:mm:ss"));
                     //   dr["DataLag"] = vsmoni.CreateDate != null ? vsmoni.CreateDate : "N/A";
                        i++;
                        dataTable.Rows.Add(dr);

                    }
                }
                this.DataSource = dataTable;
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method, InnerException Message " + ex.InnerException.Message);
            }
        }



        private void OracleCloudReport_NeedDataSource(object sender, EventArgs e)
        {
            showtable();

        }

        
    }
}