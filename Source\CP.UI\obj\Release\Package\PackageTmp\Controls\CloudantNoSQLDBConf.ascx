﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="CloudantNoSQLDBConf.ascx.cs" Inherits="CP.UI.Controls.CloudantNoSQLDBConf" %>

<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">Cloudant No SQL</h4>
        </div>
        <div class="widget-body">
            <div class="form-group">
                <label class="col-md-3 control-label">
                    DB Path <span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtDBPath" CssClass="form-control" runat="server"></asp:TextBox> 
                     <asp:RequiredFieldValidator ID="rfvDBPath" CssClass="error" ControlToValidate="txtDBPath" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter DB Path"></asp:RequiredFieldValidator>
                </div>
            </div> 

             <div class="form-group">
                <label class="col-md-3 control-label">
                    DB Name <span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtdbName" CssClass="form-control" runat="server"></asp:TextBox> 
                     <asp:RequiredFieldValidator ID="rfvtxtclouddbName" CssClass="error" ControlToValidate="txtdbName" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter Database SID"></asp:RequiredFieldValidator>
                       <asp:Label ID="lblErr" runat="server" ForeColor="Red"
                                            Text="Invalid License Key" Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                </div>
            </div> 

             <div class="form-group">
                <label class="col-md-3 control-label">
                   Local Balancer Node URL <span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtLocalBalanceNodeURL" CssClass="form-control" runat="server"></asp:TextBox> 
                     <asp:RequiredFieldValidator ID="rfvLocalBalancerNode" CssClass="error" ControlToValidate="txtLocalBalanceNodeURL" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter Local Balancer Node URL"></asp:RequiredFieldValidator>
                    
                </div>
            </div> 

             <div class="form-group">
                <label class="col-md-3 control-label">
                  Username <span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtUserName" CssClass="form-control" runat="server"></asp:TextBox> 
                     <asp:RequiredFieldValidator ID="rfvtxtusername" CssClass="error" ControlToValidate="txtUserName" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter UserName"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="rfeUser" runat="server" CssClass="error" ControlToValidate="txtUserName"
                        ErrorMessage="Enter Valid User Name" ValidationGroup="dbConfig" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                        Display="Dynamic"></asp:RegularExpressionValidator>
                </div>
            </div> 

             <div class="form-group">
                <label class="col-md-3 control-label">
                  Password <span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtPassword" CssClass="form-control chk-caps" runat="server" TextMode="Password"></asp:TextBox> 
                     <asp:RequiredFieldValidator ID="rfvtxtpassword" CssClass="error" ControlToValidate="txtPassword" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter Password"></asp:RequiredFieldValidator>
                    <span class="caps-error">Caps Lock is ON.</span>

                </div>
            </div> 
        </div>
    </div>
</div>
