﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ParallelDROperation", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ParallelDROperation : BaseEntity
    {
        #region Properties

        [DataMember]
        public DateTime StartTime { get; set; }

        [DataMember]
        public DateTime EndTime { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public WorkflowExecutionType ActionMode { get; set; }

        [DataMember]
        public int ProfileId { get; set; }

        [DataMember]
        public string ProfileName { get; set; }

        [DataMember]
        public int ActionsRunning { get; set; }

        [DataMember]
        public int ActionsSuccess { get; set; }

        [DataMember]
        public int ActionsError { get; set; }

        [DataMember]
        public int ActionsSkip { get; set; }

        [DataMember]
        public int ProfileCreatorId { get; set; }

        [DataMember]
        public int UserId { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public int ProfileIsLoad { get; set; }

        #endregion Properties
    }
}