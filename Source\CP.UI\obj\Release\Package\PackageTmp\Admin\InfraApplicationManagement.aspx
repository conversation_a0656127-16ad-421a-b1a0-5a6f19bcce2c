﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="InfraApplicationManagement.aspx.cs" Inherits="CP.UI.Admin.InfraApplicationManagement"
    Title="Continuity Patrol :: Infra Object Application Management" %>

<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript">
        function CancelClick() {
            return false;
        }

    </script>

    <style type="text/css">
        #ui-datepicker-div {
            font-size: 80%;
            position: absolute !important;
            z-index: 999999 !important;
        }

        .ui-timepicker-div .ui-widget-header {
            margin-bottom: 8px;
        }

        .ui-timepicker-div dl {
            text-align: left;
        }

            .ui-timepicker-div dl dt {
                height: 25px;
            }

            .ui-timepicker-div dl dd {
                margin: -25px 10px 10px 65px;
            }

        .ui-timepicker-div td {
            font-size: 90%;
        }

        .ui-tpicker-grid-label {
            background: none;
            border: none;
            margin: 0;
            padding: 0;
        }

        .ui-datepicker {
            width: 16% !important;
        }

        .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
            cursor: default !important;
        }

        .style1 {
            height: 29px;
        }

        .small-table td {
            padding: 2px !important;
            font-size: 12px !important;
        }

            .small-table td input {
                padding: 2px;
                font-size: 12px !important;
                width: 80%;
                margin-top: 2px;
            }

            .small-table td label {
                padding: 2px;
                font-size: 12px !important;
            }

        #ctl00_cphBody_rdTimeIntervalPR td {
            border-top: 0px;
        }
    </style>
    <link href="../App_Themes/CPTheme/jquery-ui-1.8.16.custom.css" rel="stylesheet"
        type="text/css" />

    <script type="text/javascript" src="../Script/jquery-ui-1.8.16.custom.min.js"></script>

    <script type="text/javascript" src="../Script/jquery-ui-timepicker-addon.js"></script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <div class="innerLR">

        <h3>
            <img src="../Images/infra-icon.png">
            Infra Application Management</h3>


        <asp:UpdatePanel ID="upApplicationMange" runat="server" UpdateMode="Conditional">
            <ContentTemplate>


                <div class="widget">
                    <div class="widget-head">
                        <asp:Label ID="lblInfraObjectApplicationName" runat="server" CssClass="heading" Text="" Style="padding-left: 5px !important"></asp:Label>

                    </div>
                    <div class="widget-body">

                        <table class="table table-striped table-bordered table-condensed" id="tblMaintainence" width="100%" style="display: table;">
                            <thead>
                                <tr>
                                    <th class="grid-27">Component</th>
                                    <th class="grid-26">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td>
                                        <span id="Span27" class="host-icon" runat="server"></span>
                                        <asp:Label ID="lblPRHost" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <span id="Span1" class="host-icon" runat="server"></span>
                                        <asp:Label ID="lblDRHost" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td>
                                        <span id="Span2" class="health-up" runat="server"></span>
                                        <asp:Label ID="lblPRIP" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td>
                                        <span id="Span3" class="health-up" runat="server"></span>
                                        <asp:Label ID="lblDRIP" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr id="trapppath" runat="server" visible="false">
                                    <td>App Path
                                    </td>
                                    <td colspan="2">
                                        <asp:Label ID="lblMonitoringWorkflow" runat="server" Text="Label"></asp:Label>
                                    </td>
                                </tr>
                                <tr runat="server" id="trMaintainence" visible="false">
                                    <td colspan="3" class="message success font_8">
                                        <asp:Label ID="lblReasonMaintenance" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="tblZetroReplicationDetails" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Replication Monitor
                                    </th>
                                    <th class="col-md-4">Protected Site
                                    </th>
                                    <th>Recovery Site
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>VPG Name
                                    </td>
                                    <td class="text-indent"><span id="Span161" runat="server" class="icon-computer">&nbsp;</span>
                                        <asp:Label ID="lblPRVPGRep" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span162" runat="server" class="icon-computer">&nbsp;</span>
                                        <asp:Label ID="lblDRVPGRep" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>VPG Site Type
                                    </td>
                                    <td><span id="Span163" runat="server" class="site-icon">&nbsp;</span>
                                        <asp:Label ID="lblPRVPGType" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span164" runat="server" class="site-icon">&nbsp;</span>
                                        <asp:Label ID="lblDRVPGType" runat="server"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>

                    </div>
                </div>

            </ContentTemplate>
        </asp:UpdatePanel>
        <asp:Panel ID="panelMaintenance" runat="server" Style="display: none" Width="450px" Height="480px">
            <div class="modal" style="display: block;">
                <div class="modal-dialog" style="width: 675px;">
                    <div class="modal-content  widget-body-white">
                        <div class="modal-header">

                            <asp:Label ID="lblmaintenance" runat="server"></asp:Label>
                            <asp:LinkButton ID="LinkButton5" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label">Reason</label>
                                        <div class="col-md-9">
                                            <textarea id="txtReason" rows="3" cols="40" class="form-control" style="width: 64%"></textarea>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label ">Mode <span class="inactive">*</span></label>

                                        <div class="col-md-9">
                                            <select id="ddlMode" class="col-md-8" style="width: 65% !important; padding-right: 5px; padding-left: 5px;">
                                                <option value="0">-Select Mode-</option>
                                                <%-- <option value="1">Auto</option>--%>
                                                <option value="2">Manual</option>
                                            </select>
                                            <span class="inactive">*</span>
                                            <asp:Label ID="lblMode" runat="server"></asp:Label>
                                        </div>
                                    </div>

                                    <div class="form-group" id="idUnlock" style="display: none">

                                        <label class="col-md-3 control-label">Unlock Time</label>
                                        <div class="col-md-9">
                                            <input type="text" name="txtTime" id="txtTime" readonly="readonly" class="form-control" style="width: 64%" />

                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="col-md-6">
                            </div>
                            <div class="col-md-9" style="margin-left: 3px">
                                <input id="btnActionSetSave" type="button" value="OK" class="btn btn-primary" style="width: 20%" />
                                <input id="btnCancleMaintaince" type="button" value="Cancel" class="btn btn-default" style="width: 20%" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>


        <asp:Panel ID="panelScheduleworkflow" runat="server" Style="display: none" Width="550px">
            <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                <ContentTemplate>
                    <div class="modal" style="display: block;">
                        <div class="modal-dialog" style="width: 800px; height: auto;">
                            <div class="modal-content  widget-body-white">
                                <div class="modal-header">
                                    <h3 class="modal-title">Infraobject Scheduled Workflow for
                                                    <asp:Label ID="lblName" runat="server" Text="" CssClass="h3" Style="top: 0px; font-style: normal;" ForeColor="White"></asp:Label></h3>
                                    <asp:LinkButton ID="LinkButton6" runat="server" ToolTip="Close window" OnClick="CloseClick"
                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>

                                </div>
                                <%--<iframe id="iframe5" src="InfraobjectScheduleWorkflow.aspx" class="border-none" style="width: 100%; height: 650px; max-height: 516px; overflow: auto;" runat="server" />--%>

                                <asp:HtmlIframe id="HtmlIframe1" src="InfraobjectScheduleWorkflow.aspx" class="border-none" style="width: 100%; height: 650px; max-height: 516px; overflow: auto;" runat="server">
                                </asp:HtmlIframe>
                            </div>


                        </div>
                    </div>

                </ContentTemplate>
            </asp:UpdatePanel>

        </asp:Panel>


        <div class="widget">
            <div class="widget-head">
            </div>
            <div class="widget-body">
                <table class="table table-striped table-bordered table-condensed" id="tblMaintainenceManage" width="100%">
                    <tr>
                        <td style="width: 441px;">Manage Infra Object
                        </td>
                        <td colspan="2" class="align-right">
                            <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenance" runat="server" TargetControlID="btnMaint"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                                PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="bg"
                                CancelControlID="btnCancleMaintaince">
                            </TK1:ModalPopupExtender>
                            <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenanceAll" runat="server" TargetControlID="btnMaintenanceAll"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                                PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="bg"
                                CancelControlID="btnCancleMaintaince">
                            </TK1:ModalPopupExtender>
                            <TK1:ModalPopupExtender ID="ModalPopupExtenderLock" runat="server" TargetControlID="btnLock"
                                RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                                PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="bg"
                                CancelControlID="btnCancleMaintaince">
                            </TK1:ModalPopupExtender>
                            <asp:Button ID="btnActive" runat="server" CssClass="btn btn-primary" Text="Active" Width="15%" />
                            <asp:Button ID="btnActiveAll" runat="server" CssClass="btn btn-primary" Text="ActiveAll" Width="15%" />
                            <asp:Button ID="btnLock" runat="server" CssClass="btn btn-primary" Text="Lock" Width="15%" OnClick="btnLock_Click" />
                            <asp:Button ID="btnMaint" runat="server" CssClass="btn btn-primary" Text="Maintenance" Width="15%" />
                            <asp:Button ID="btnMaintenanceAll" runat="server" Text="MaintenanceAll" CssClass="btn btn-primary" Width="15%" />
                        </td>
                    </tr>
                    <tr id="TrDROpe" style="display: none">
                        <td style="width: 441px;">DR Operations
                        </td>
                        <td colspan="2" class="align-right">

                            <asp:Button ID="btnSwitchOver" runat="server" CssClass="btn btn-primary" Text="SwitchOver" Width="15%" OnClick="BtnSwitchOverClick" />
                            <asp:Button ID="btnSwitchBack" runat="server" CssClass="btn btn-primary" Text="SwitchBack" Width="15%" OnClick="BtnSwitchBackClick"
                                Enabled="false" />
                            <asp:Button ID="btnFailOver" runat="server" CssClass="btn btn-primary" Text="Failover" Width="15%" OnClick="BtnFailOverClick"
                                Enabled="false" />
                            <asp:Button ID="btnFailBack" runat="server" CssClass="btn btn-primary" Text="Failback" Width="15%" OnClick="BtnFailBackClick"
                                Enabled="false" />
                            <asp:Button ID="btnCustom" runat="server" CssClass="btn btn-primary" Text="Custom" Width="15%" OnClick="BtnCustomClick"
                                Enabled="false" />
                        </td>
                    </tr>

                    <tr>
                        <td>Manage Options
                        </td>
                        <td colspan="2" class="align-right">
                            <div class="btn-group">
                                <%--  <TK1:ModalPopupExtender ID="ModalPopupExtenderInfraobjectScheduleWorkflow" runat="server" 
                                    RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelScheduleworkflow"
                                    PopupDragHandleControlID="panelScheduleworkflow" Drag="false" BackgroundCssClass="bg">
                                </TK1:ModalPopupExtender>--%>
                                <button data-toggle="dropdown" id="btnManageOptions" class="btn btn-primary dropdown-toggle" type="button">
                                    Options
							    <span class="caret" style="border-width: 7px 4px 0 !important; margin-left: 55px;"></span>
                                </button>
                                <ul class="dropdown-menu pull-left">
                                    <li>

                                        <asp:LinkButton ID="btnSchduleWF" runat="server" Text="Schedule Workflow" Width="100%" OnClick="btnSchduleWFClick" /></li>
                                    <li>

                                        <asp:LinkButton ID="lnkBtnViewAlert" runat="server" Text="View Alert" Width="100%" OnClick="lnkBtnViewAlertClick" /></li>
                                    <li>
                                        <asp:LinkButton ID="lnkEnableDSMonitoring" runat="server" Text="Enable Diskspace Monitoring" Width="100%" OnClick="lnkBtnEnableDSMonitoring_Click" /></li>

                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <asp:Label ID="lblErrorMessage" runat="server" Visible="false" Text=""></asp:Label>
                        </td>
                    </tr>
                </table>

                <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
                </asp:Panel>
                <asp:Panel ID="pnlEnableDSMonitoring" runat="server" Width="100%" Visible="false">
                    <div class="modal" style="display: block; z-index: 9999999;">
                        <div class="modal-dialog" style="width: 800px;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3 class="modal-title">Enable Diskspace Monitoring</h3>
                                    <asp:LinkButton ID="LnkbtnCloseEnableDSMonitoring" runat="server" ToolTip="Close window" OnClick="LnkbtnCloseEnableDSMonitoring_Click"
                                        CausesValidation="False" class="close" CommandName="Close">x</asp:LinkButton>
                                </div>

                                <asp:UpdatePanel ID="pnlUpdate_enableDSMonitoring" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <br />
                                        <asp:Label ID="lblEnableDSMonitoringMsg" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>
                                        <br />
                                        <div class="modal-body">
                                            <table id="tblCommonComponent" class="table table-white small-table margin-bottom-none" width="100%" runat="server">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 22%">Disk Info
                                                        </th>
                                                        <th style="width: 39%">Production Server
                                                        </th>
                                                        <th style="width: 39%">DR Server
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <label>
                                                                Volume Name / Mountpoint<span class="inactive">*</span></label>
                                                        </td>
                                                        <td>
                                                            <asp:TextBox ID="txtPRVolumeName" CssClass="form-control" runat="server" CausesValidation="true" ValidationGroup="validateEnableDSMonitoring"></asp:TextBox>
                                                            <asp:RequiredFieldValidator ID="rfvPRVolumeName" runat="server" ErrorMessage="Please Enter Volume Name(s) for PR" CssClass="error"
                                                                ControlToValidate="txtPRVolumeName" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"></asp:RequiredFieldValidator>
                                                        </td>
                                                        <td>
                                                            <asp:TextBox ID="txtDRVolumeName" CssClass="form-control" runat="server" ValidationGroup="validateEnableDSMonitoring"></asp:TextBox>
                                                            <asp:RequiredFieldValidator ID="rfvDRVolumeName" runat="server" ErrorMessage="Please Enter Volume Name(s) for DR" CssClass="error"
                                                                ControlToValidate="txtDRVolumeName" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"></asp:RequiredFieldValidator>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <td>
                                                            <label>
                                                                Threshold<span class="inactive">*</span></label>
                                                        </td>
                                                        <td>
                                                            <asp:TextBox ID="txtPRThreshold" CssClass="form-control" runat="server" ValidationGroup="validateEnableDSMonitoring"></asp:TextBox>
                                                            <asp:RequiredFieldValidator ID="rfvPRThreshold" runat="server" ErrorMessage="Please Enter THreshold for PR" CssClass="error"
                                                                ControlToValidate="txtPRThreshold" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"></asp:RequiredFieldValidator>
                                                        </td>
                                                        <td>
                                                            <asp:TextBox ID="txtDRThreshold" CssClass="form-control" runat="server" ValidationGroup="validateEnableDSMonitoring"></asp:TextBox>
                                                            <asp:RequiredFieldValidator ID="rfvDRThreshold" runat="server" ErrorMessage="Please Enter THreshold for DR" CssClass="error"
                                                                ControlToValidate="txtDRThreshold" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"></asp:RequiredFieldValidator>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <td>
                                                            <label>Time Interval<span class="inactive">*</span></label>
                                                        </td>
                                                        <td>
                                                            <asp:Panel ID="PanelIntervalPR" runat="server">
                                                                <asp:RadioButtonList ID="rdTimeIntervalPR" runat="server" RepeatDirection="Horizontal"
                                                                    CssClass="dropbox text-indent" OnSelectedIndexChanged="rdTimeIntervalPR_SelectedIndexChanged"
                                                                    AutoPostBack="True" ValidationGroup="validateEnableDSMonitoring">
                                                                    <asp:ListItem>Minute(s)</asp:ListItem>
                                                                    <asp:ListItem>Hour(s)</asp:ListItem>
                                                                    <asp:ListItem>Day(s)</asp:ListItem>
                                                                </asp:RadioButtonList>
                                                                <asp:Label ID="lbltimeintervalErrormessagePR" runat="server" Visible="false" Text="Select Interval Time"></asp:Label>
                                                                <asp:RequiredFieldValidator ID="rfvTimeIntervalPR" runat="server" ControlToValidate="rdTimeIntervalPR" Display="Dynamic" ValidationGroup="validateEnableDSMonitoring"
                                                                    CssClass="error" ErrorMessage="Select PR Diskspace Monitor Time Interval In Either Minutes, Hours or Days"></asp:RequiredFieldValidator>
                                                            </asp:Panel>
                                                        </td>

                                                        <td>
                                                            <asp:Panel ID="PanelIntervalDR" runat="server" Visible="false">
                                                                <asp:RadioButtonList ID="rdTimeIntervalDR" runat="server" RepeatDirection="Horizontal"
                                                                    CssClass="dropbox text-indent" OnSelectedIndexChanged="rdTimeIntervalDR_SelectedIndexChanged"
                                                                    AutoPostBack="True" ValidationGroup="validateEnableDSMonitoring">
                                                                    <asp:ListItem>Minute(s)</asp:ListItem>
                                                                    <asp:ListItem>Hour(s)</asp:ListItem>
                                                                    <asp:ListItem>Day(s)</asp:ListItem>
                                                                </asp:RadioButtonList>
                                                                <asp:Label ID="lbltimeintervalErrormessageDR" runat="server" Visible="false" Text="Select Interval Time"></asp:Label>
                                                                <asp:RequiredFieldValidator ID="rfvTimeIntervalDR" runat="server" ControlToValidate="rdTimeIntervalDR" Display="Dynamic" ValidationGroup=""
                                                                    CssClass="error" ErrorMessage="Select DR Diskspace Monitor Time Interval In Either Minutes, Hours or Days"></asp:RequiredFieldValidator>
                                                            </asp:Panel>
                                                        </td>

                                                    </tr>

                                                    <tr id="setTimeTR" runat="server" visible="false">
                                                        <td>
                                                            <label>Set Time<span class="inactive">*</span></label>
                                                        </td>
                                                        <td>
                                                            <asp:Panel ID="Panel_MinuitePR" runat="server" Visible="False">
                                                                <div>
                                                                    <asp:Label ID="lblEveryMinuitePR" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                                    <asp:TextBox ID="txteveryminuitePR" runat="server" CssClass="form-control" Style="width: 15%;"
                                                                        MaxLength="4" CausesValidation="True"></asp:TextBox>
                                                                    <asp:Label ID="lblminutesPR" runat="server" Text="Minute(s)" CssClass="margin-right"></asp:Label>
                                                                    <asp:RegularExpressionValidator ID="REVEveryminuitePR" runat="server" CssClass="error"
                                                                        ControlToValidate="txteveryminuitePR" ErrorMessage="Please Enter only Numbers" Display="Dynamic"
                                                                        ValidationExpression="\d+" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                                    <asp:RequiredFieldValidator ID="rfvtxteveryminuitePR" runat="server" Enabled="true"
                                                                        ControlToValidate="txteveryminuitePR" Display="Dynamic" ForeColor="Red"
                                                                        CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </asp:Panel>

                                                            <asp:Panel ID="Panel_HourlyPR" runat="server" Visible="false">
                                                                <div>
                                                                    <asp:Label ID="lblEveryHourlyPR" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                                    <asp:TextBox ID="txteveryhourPR" runat="server" CssClass="form-control" Style="width: 15%;"
                                                                        MaxLength="2"></asp:TextBox>

                                                                    <asp:Label ID="lblhoursPR" runat="server" Text="Hour(s)" CssClass="margin-right"></asp:Label>
                                                                    <asp:TextBox ID="txteveryhourlyminuitePR" runat="server" CssClass="form-control" Style="width: 15%;" AutoPostBack="true"
                                                                        MaxLength="2" OnTextChanged="txteveryhourlyminuitePR_TextChanged"></asp:TextBox>
                                                                    <asp:Label ID="lblhourlyminutesPR" runat="server" Text="Minute(s)"></asp:Label>
                                                                    <asp:RequiredFieldValidator ID="rfvtxteveryhourPR" runat="server" Enabled="true" ControlToValidate="txteveryhourPR"
                                                                        Display="Dynamic" Font-Size="8" ForeColor="Red" CssClass="error" ErrorMessage="Enter hours and  minutes"></asp:RequiredFieldValidator>
                                                                    <asp:RequiredFieldValidator ID="rfvtxteveryhourlyminuitePR" runat="server" Enabled="true"
                                                                        ControlToValidate="txteveryhourlyminuitePR" Display="Dynamic" ForeColor="Red"
                                                                        CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                                    <asp:RegularExpressionValidator ID="revtxteveryhourPR" runat="server" CssClass="error" Display="Dynamic"
                                                                        ControlToValidate="txteveryhourPR" ErrorMessage="Enter only Numbers" ValidationExpression="\d+"
                                                                        Font-Size="9pt"></asp:RegularExpressionValidator>
                                                                    <asp:RangeValidator ID="rngPR" runat="server" CssClass="error" ControlToValidate="txteveryhourlyminuitePR" Display="Dynamic"
                                                                        ErrorMessage="Minutes Should Be Less Than 60" Type="Integer" Enabled="false" MaximumValue="60" MinimumValue="0"></asp:RangeValidator>
                                                                    <asp:RegularExpressionValidator ID="regexpfornumericPR" runat="server" CssClass="error" Enabled="false"
                                                                        ControlToValidate="txteveryhourlyminuitePR" ErrorMessage="Enter Numeric Only" Display="Dynamic"
                                                                        ValidationExpression="^[0-99]*$" SetFocusOnError="True" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                                </div>
                                                            </asp:Panel>

                                                            <asp:Panel ID="Panel_DailyPR" runat="server" Visible="false">
                                                                <div>
                                                                    <asp:Label ID="lblEverydailyPR" runat="server" Text="Every" CssClass="margin-right"></asp:Label>
                                                                    <asp:TextBox ID="txteverydailyPR" runat="server" CssClass="form-control" Style="width: 18%; margin-left: 17px;"></asp:TextBox>
                                                                    <asp:Label ID="lbldaysPR" runat="server" Text="Day(s)" CssClass="margin-right"></asp:Label>
                                                                    <br />
                                                                    <br />
                                                                    <asp:Label ID="lblstartTimePR" runat="server" CssClass="pull-left margin-right margin-top" Text="StartTime "></asp:Label>
                                                                    <asp:DropDownList ID="ddlhoursPR" runat="server" CssClass="selectpicker col-xs-3" data-style="btn-default">
                                                                        <asp:ListItem>00</asp:ListItem>
                                                                        <asp:ListItem>01</asp:ListItem>
                                                                        <asp:ListItem>02</asp:ListItem>
                                                                        <asp:ListItem>03</asp:ListItem>
                                                                        <asp:ListItem>04</asp:ListItem>
                                                                        <asp:ListItem>05</asp:ListItem>
                                                                        <asp:ListItem>06</asp:ListItem>
                                                                        <asp:ListItem>07</asp:ListItem>
                                                                        <asp:ListItem>08</asp:ListItem>
                                                                        <asp:ListItem>09</asp:ListItem>
                                                                        <asp:ListItem>10</asp:ListItem>
                                                                        <asp:ListItem>11</asp:ListItem>
                                                                        <asp:ListItem>12</asp:ListItem>
                                                                        <asp:ListItem>13</asp:ListItem>
                                                                        <asp:ListItem>14</asp:ListItem>
                                                                        <asp:ListItem>15</asp:ListItem>
                                                                        <asp:ListItem>16</asp:ListItem>
                                                                        <asp:ListItem>17</asp:ListItem>
                                                                        <asp:ListItem>18</asp:ListItem>
                                                                        <asp:ListItem>19</asp:ListItem>
                                                                        <asp:ListItem>20</asp:ListItem>
                                                                        <asp:ListItem>21</asp:ListItem>
                                                                        <asp:ListItem>22</asp:ListItem>
                                                                        <asp:ListItem>23</asp:ListItem>
                                                                    </asp:DropDownList>
                                                                    <asp:Label ID="lblhrPR" runat="server" CssClass="pull-left margin-right margin-top" Text="Hr"></asp:Label>
                                                                    <asp:DropDownList ID="ddlminutesPR" runat="server" CssClass="selectpicker col-xs-3" data-style="btn-default">
                                                                        <asp:ListItem>00</asp:ListItem>
                                                                        <asp:ListItem>01</asp:ListItem>
                                                                        <asp:ListItem>02</asp:ListItem>
                                                                        <asp:ListItem>03</asp:ListItem>
                                                                        <asp:ListItem>04</asp:ListItem>
                                                                        <asp:ListItem>05</asp:ListItem>
                                                                        <asp:ListItem>06</asp:ListItem>
                                                                        <asp:ListItem>07</asp:ListItem>
                                                                        <asp:ListItem>08</asp:ListItem>
                                                                        <asp:ListItem>09</asp:ListItem>
                                                                        <asp:ListItem>10</asp:ListItem>
                                                                        <asp:ListItem>11</asp:ListItem>
                                                                        <asp:ListItem>12</asp:ListItem>
                                                                        <asp:ListItem>13</asp:ListItem>
                                                                        <asp:ListItem>14</asp:ListItem>
                                                                        <asp:ListItem>15</asp:ListItem>
                                                                        <asp:ListItem>16</asp:ListItem>
                                                                        <asp:ListItem>17</asp:ListItem>
                                                                        <asp:ListItem>18</asp:ListItem>
                                                                        <asp:ListItem>19</asp:ListItem>
                                                                        <asp:ListItem>20</asp:ListItem>
                                                                        <asp:ListItem>21</asp:ListItem>
                                                                        <asp:ListItem>22</asp:ListItem>
                                                                        <asp:ListItem>23</asp:ListItem>
                                                                        <asp:ListItem>24</asp:ListItem>
                                                                        <asp:ListItem>25</asp:ListItem>
                                                                        <asp:ListItem>26</asp:ListItem>
                                                                        <asp:ListItem>27</asp:ListItem>
                                                                        <asp:ListItem>28</asp:ListItem>
                                                                        <asp:ListItem>29</asp:ListItem>
                                                                        <asp:ListItem>30</asp:ListItem>
                                                                        <asp:ListItem>31</asp:ListItem>
                                                                        <asp:ListItem>32</asp:ListItem>
                                                                        <asp:ListItem>33</asp:ListItem>
                                                                        <asp:ListItem>34</asp:ListItem>
                                                                        <asp:ListItem>35</asp:ListItem>
                                                                        <asp:ListItem>36</asp:ListItem>
                                                                        <asp:ListItem>37</asp:ListItem>
                                                                        <asp:ListItem>38</asp:ListItem>
                                                                        <asp:ListItem>39</asp:ListItem>
                                                                        <asp:ListItem>40</asp:ListItem>
                                                                        <asp:ListItem>41</asp:ListItem>
                                                                        <asp:ListItem>42</asp:ListItem>
                                                                        <asp:ListItem>43</asp:ListItem>
                                                                        <asp:ListItem>44</asp:ListItem>
                                                                        <asp:ListItem>45</asp:ListItem>
                                                                        <asp:ListItem>46</asp:ListItem>
                                                                        <asp:ListItem>47</asp:ListItem>
                                                                        <asp:ListItem>48</asp:ListItem>
                                                                        <asp:ListItem>49</asp:ListItem>
                                                                        <asp:ListItem>50</asp:ListItem>
                                                                        <asp:ListItem>51</asp:ListItem>
                                                                        <asp:ListItem>52</asp:ListItem>
                                                                        <asp:ListItem>53</asp:ListItem>
                                                                        <asp:ListItem>54</asp:ListItem>
                                                                        <asp:ListItem>55</asp:ListItem>
                                                                        <asp:ListItem>56</asp:ListItem>
                                                                        <asp:ListItem>57</asp:ListItem>
                                                                        <asp:ListItem>58</asp:ListItem>
                                                                        <asp:ListItem>59</asp:ListItem>
                                                                    </asp:DropDownList>
                                                                    <asp:Label ID="lblminPR" runat="server" CssClass="pull-left margin-right margin-top" Text="Min "></asp:Label>
                                                                    <asp:RegularExpressionValidator ID="revdaysPR" runat="server" CssClass="error" ControlToValidate="txteverydailyPR" Display="Dynamic"
                                                                        ErrorMessage="Please Enter only Numbers" ValidationExpression="\d+"></asp:RegularExpressionValidator>
                                                                    <asp:RequiredFieldValidator ID="rfveverydailyPR" runat="server" ControlToValidate="txteverydailyPR"
                                                                        CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days" ForeColor="Red"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </asp:Panel>

                                                            <asp:CustomValidator runat="server" Display="Dynamic" ID="cvSetTimePR" ValidationGroup="validateEnableDSMonitoring"
                                                                CssClass="error" OnServerValidate="cvSetTimePRValidator_ServerValidate"></asp:CustomValidator>

                                                        </td>
                                                        <td></td>
                                                        <td id="tdSetTImeDR" runat="server" visible="false">
                                                            <asp:Panel ID="Panel_MinuiteDR" runat="server" Visible="false">
                                                                <div>
                                                                    <asp:Label ID="lblEveryMinuiteDR" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                                    <asp:TextBox ID="txteveryminuiteDR" runat="server" CssClass="form-control" Style="width: 15%;"
                                                                        MaxLength="4" CausesValidation="True"></asp:TextBox>
                                                                    <asp:Label ID="lblminutesDR" runat="server" Text="Minute(s)" CssClass="margin-right"></asp:Label>
                                                                    <asp:RegularExpressionValidator ID="REVEveryminuiteDR" runat="server" CssClass="error"
                                                                        ControlToValidate="txteveryminuiteDR" ErrorMessage="Please Enter only Numbers" Display="Dynamic"
                                                                        ValidationExpression="\d+" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                                    <asp:RequiredFieldValidator ID="rfvtxteveryminuiteDR" runat="server" Enabled="true"
                                                                        ControlToValidate="txteveryminuiteDR" Display="Dynamic" ForeColor="Red"
                                                                        CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </asp:Panel>

                                                            <asp:Panel ID="Panel_HourlyDR" runat="server" Visible="false">
                                                                <div>
                                                                    <asp:Label ID="lblEveryHourlyDR" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                                    <asp:TextBox ID="txteveryhourDR" runat="server" CssClass="form-control" Style="width: 15%;"
                                                                        MaxLength="2"></asp:TextBox>
                                                                    <asp:Label ID="lblhoursDR" runat="server" Text="Hour(s)" CssClass="margin-right"></asp:Label>
                                                                    <asp:TextBox ID="txteveryhourlyminuiteDR" runat="server" CssClass="form-control" Style="width: 15%;" AutoPostBack="true"
                                                                        MaxLength="2" OnTextChanged="txteveryhourlyminuiteDR_TextChanged"></asp:TextBox>
                                                                    <asp:Label ID="lblhourlyminutesDR" runat="server" Text="Minute(s)"></asp:Label>
                                                                    <asp:RequiredFieldValidator ID="rfvtxteveryhourDR" runat="server" Enabled="true" ControlToValidate="txteveryhourDR"
                                                                        Display="Dynamic" Font-Size="8" ForeColor="Red" CssClass="error" ErrorMessage="Enter hours and  minutes"></asp:RequiredFieldValidator>
                                                                    <asp:RequiredFieldValidator ID="rfvtxteveryhourlyminuiteDR" runat="server" Enabled="true"
                                                                        ControlToValidate="txteveryhourlyminuiteDR" Display="Dynamic" ForeColor="Red"
                                                                        CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                                    <asp:RegularExpressionValidator ID="revtxteveryhourDR" runat="server" CssClass="error" Display="Dynamic"
                                                                        ControlToValidate="txteveryhourDR" ErrorMessage="Enter only Numbers" ValidationExpression="\d+"
                                                                        Font-Size="9pt"></asp:RegularExpressionValidator>
                                                                    <asp:RangeValidator ID="rngDR" runat="server" CssClass="error" ControlToValidate="txteveryhourlyminuiteDR" Display="Dynamic"
                                                                        ErrorMessage="Minutes Should Be Less Than 60" Type="Integer" Enabled="false" MaximumValue="60" MinimumValue="0"></asp:RangeValidator>
                                                                    <asp:RegularExpressionValidator ID="regexpfornumericDR" runat="server" CssClass="error" Enabled="false"
                                                                        ControlToValidate="txteveryhourlyminuiteDR" ErrorMessage="Enter Numeric Only" Display="Dynamic"
                                                                        ValidationExpression="^[0-99]*$" SetFocusOnError="True" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                                </div>
                                                            </asp:Panel>

                                                            <asp:Panel ID="Panel_DailyDR" runat="server" Visible="false">
                                                                <div>
                                                                    <asp:Label ID="lblEverydailyDR" runat="server" Text="Every" CssClass="margin-right"></asp:Label>
                                                                    <asp:TextBox ID="txteverydailyDR" runat="server" CssClass="form-control" Style="width: 16%; margin-left: 17px;"></asp:TextBox>
                                                                    <asp:Label ID="lbldaysDR" runat="server" Text="Day(s)" CssClass="margin-right"></asp:Label>
                                                                    <br />
                                                                    <br />
                                                                    <asp:Label ID="lblstartTimeDR" runat="server" CssClass="pull-left margin-right margin-top" Text="StartTime "></asp:Label>
                                                                    <asp:DropDownList ID="ddlhoursDR" runat="server" CssClass="selectpicker col-xs-3" data-style="btn-default" Style="width: 16%">
                                                                        <asp:ListItem>00</asp:ListItem>
                                                                        <asp:ListItem>01</asp:ListItem>
                                                                        <asp:ListItem>02</asp:ListItem>
                                                                        <asp:ListItem>03</asp:ListItem>
                                                                        <asp:ListItem>04</asp:ListItem>
                                                                        <asp:ListItem>05</asp:ListItem>
                                                                        <asp:ListItem>06</asp:ListItem>
                                                                        <asp:ListItem>07</asp:ListItem>
                                                                        <asp:ListItem>08</asp:ListItem>
                                                                        <asp:ListItem>09</asp:ListItem>
                                                                        <asp:ListItem>10</asp:ListItem>
                                                                        <asp:ListItem>11</asp:ListItem>
                                                                        <asp:ListItem>12</asp:ListItem>
                                                                        <asp:ListItem>13</asp:ListItem>
                                                                        <asp:ListItem>14</asp:ListItem>
                                                                        <asp:ListItem>15</asp:ListItem>
                                                                        <asp:ListItem>16</asp:ListItem>
                                                                        <asp:ListItem>17</asp:ListItem>
                                                                        <asp:ListItem>18</asp:ListItem>
                                                                        <asp:ListItem>19</asp:ListItem>
                                                                        <asp:ListItem>20</asp:ListItem>
                                                                        <asp:ListItem>21</asp:ListItem>
                                                                        <asp:ListItem>22</asp:ListItem>
                                                                        <asp:ListItem>23</asp:ListItem>
                                                                    </asp:DropDownList>
                                                                    <asp:Label ID="lblhrDR" runat="server" CssClass="pull-left margin-right margin-top" Text="Hr"></asp:Label>
                                                                    <asp:DropDownList ID="ddlminutesDR" runat="server" CssClass="selectpicker col-xs-3"
                                                                        data-style="btn-default" Style="width: 16%">
                                                                        <asp:ListItem>00</asp:ListItem>
                                                                        <asp:ListItem>01</asp:ListItem>
                                                                        <asp:ListItem>02</asp:ListItem>
                                                                        <asp:ListItem>03</asp:ListItem>
                                                                        <asp:ListItem>04</asp:ListItem>
                                                                        <asp:ListItem>05</asp:ListItem>
                                                                        <asp:ListItem>06</asp:ListItem>
                                                                        <asp:ListItem>07</asp:ListItem>
                                                                        <asp:ListItem>08</asp:ListItem>
                                                                        <asp:ListItem>09</asp:ListItem>
                                                                        <asp:ListItem>10</asp:ListItem>
                                                                        <asp:ListItem>11</asp:ListItem>
                                                                        <asp:ListItem>12</asp:ListItem>
                                                                        <asp:ListItem>13</asp:ListItem>
                                                                        <asp:ListItem>14</asp:ListItem>
                                                                        <asp:ListItem>15</asp:ListItem>
                                                                        <asp:ListItem>16</asp:ListItem>
                                                                        <asp:ListItem>17</asp:ListItem>
                                                                        <asp:ListItem>18</asp:ListItem>
                                                                        <asp:ListItem>19</asp:ListItem>
                                                                        <asp:ListItem>20</asp:ListItem>
                                                                        <asp:ListItem>21</asp:ListItem>
                                                                        <asp:ListItem>22</asp:ListItem>
                                                                        <asp:ListItem>23</asp:ListItem>
                                                                        <asp:ListItem>24</asp:ListItem>
                                                                        <asp:ListItem>25</asp:ListItem>
                                                                        <asp:ListItem>26</asp:ListItem>
                                                                        <asp:ListItem>27</asp:ListItem>
                                                                        <asp:ListItem>28</asp:ListItem>
                                                                        <asp:ListItem>29</asp:ListItem>
                                                                        <asp:ListItem>30</asp:ListItem>
                                                                        <asp:ListItem>31</asp:ListItem>
                                                                        <asp:ListItem>32</asp:ListItem>
                                                                        <asp:ListItem>33</asp:ListItem>
                                                                        <asp:ListItem>34</asp:ListItem>
                                                                        <asp:ListItem>35</asp:ListItem>
                                                                        <asp:ListItem>36</asp:ListItem>
                                                                        <asp:ListItem>37</asp:ListItem>
                                                                        <asp:ListItem>38</asp:ListItem>
                                                                        <asp:ListItem>39</asp:ListItem>
                                                                        <asp:ListItem>40</asp:ListItem>
                                                                        <asp:ListItem>41</asp:ListItem>
                                                                        <asp:ListItem>42</asp:ListItem>
                                                                        <asp:ListItem>43</asp:ListItem>
                                                                        <asp:ListItem>44</asp:ListItem>
                                                                        <asp:ListItem>45</asp:ListItem>
                                                                        <asp:ListItem>46</asp:ListItem>
                                                                        <asp:ListItem>47</asp:ListItem>
                                                                        <asp:ListItem>48</asp:ListItem>
                                                                        <asp:ListItem>49</asp:ListItem>
                                                                        <asp:ListItem>50</asp:ListItem>
                                                                        <asp:ListItem>51</asp:ListItem>
                                                                        <asp:ListItem>52</asp:ListItem>
                                                                        <asp:ListItem>53</asp:ListItem>
                                                                        <asp:ListItem>54</asp:ListItem>
                                                                        <asp:ListItem>55</asp:ListItem>
                                                                        <asp:ListItem>56</asp:ListItem>
                                                                        <asp:ListItem>57</asp:ListItem>
                                                                        <asp:ListItem>58</asp:ListItem>
                                                                        <asp:ListItem>59</asp:ListItem>
                                                                    </asp:DropDownList>
                                                                    <asp:Label ID="lblminDR" runat="server" CssClass="pull-left margin-right margin-top" Text="Min "></asp:Label>
                                                                    <asp:RegularExpressionValidator ID="revdaysDR" runat="server" CssClass="error" ControlToValidate="txteverydailyDR" Display="Dynamic"
                                                                        ErrorMessage="Please Enter only Numbers" ValidationExpression="\d+"></asp:RegularExpressionValidator>
                                                                    <asp:RequiredFieldValidator ID="rfveverydailyDR" runat="server" ControlToValidate="txteverydailyDR"
                                                                        CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days" ForeColor="Red"></asp:RequiredFieldValidator>
                                                                </div>
                                                            </asp:Panel>

                                                        </td>
                                                    </tr>

                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="modal-footer" style="padding: 5px;">
                                            <div class="text-right">
                                                <asp:Button ID="btnSubmit" runat="server" Text="Submit" CssClass="btn btn-primary" Width="12%" Style="font-size: 12px"
                                                    OnClick="BtnSubmit_Click" ValidationGroup="validateEnableDSMonitoring" Visible="True" />
                                                <asp:Button ID="btnDelete" runat="server" Text="Delete" CssClass="btn btn-primary" Width="12%" Style="font-size: 12px"
                                                    OnClick="BtnDelete_Click" ValidationGroup="validateEnableDSMonitoring" Visible="false" />
                                                <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="btnDelete"
                                                    ConfirmText="" OnClientCancel="CancelClick">
                                                </TK1:ConfirmButtonExtender>
                                                <asp:Button ID="btnClose" runat="server" Text="Close" CssClass="btn btn-default" Style="font-size: 12px"
                                                    Width="12%" CausesValidation="False" OnClick="btnClose_Click" />
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                    </div>
                </asp:Panel>
            </div>
        </div>
    </div>
    <script src="../Script/InfraObjectApplicationMaintenance.js" type="text/javascript"></script>
    <script src="../Script/validation.js"></script>
    <script src="../Script/AlertModal.js"></script>
    <script src="../Script/Helper.js"></script>
</asp:Content>

