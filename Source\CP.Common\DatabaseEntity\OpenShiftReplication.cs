﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    public class OpenShiftReplication : BaseEntity
    {
        private ReplicationBase _replicationBase = new ReplicationBase();

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string ProjectNameSpaceName { get; set; }

        [DataMember]
        public string StatefulSetName { get; set; }

        [DataMember]
        public string ReplicaSetName { get; set; }

        [DataMember]
        public string DeploymentSetName { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }
        }

        [DataMember]
        public string OpenShiftProjectName { get; set; }
    }
}

