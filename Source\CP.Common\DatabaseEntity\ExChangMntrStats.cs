﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ExChangMntrStats", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ExChangMntrStats : BaseEntity
    {
        #region Properties

        //[DataMember]
        //public int Id { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int DatabaseId { get; set; }

        [DataMember]
        public string PRIPAddress { get; set; }

        [DataMember]
        public string DRIPAddress { get; set; }

        [DataMember]
        public string PRMailboxDatabase { get; set; }

        [DataMember]
        public string DRMailboxDatabase { get; set; }

        [DataMember]
        public string PRMailboxDBStatus { get; set; }

        [DataMember]
        public string DRMailboxDBStatus { get; set; }

        //[DataMember]
        //public string PRMailboxDatabaseStatus { get; set; }

        //[DataMember]
        //public string DRMailboxDatabaseStatus { get; set; }

        [DataMember]
        public string PRContentIndexState { get; set; }

        [DataMember]
        public string DRContentIndexState { get; set; }

        [DataMember]
        public string PRContentIndexErrorMessage { get; set; }

        [DataMember]
        public string DRContentIndexErrorMessage { get; set; }

        [DataMember]
        public string PRCopyQueueLength { get; set; }

        [DataMember]
        public string DRCopyQueueLength { get; set; }

        [DataMember]
        public string PRReplayQueueLength { get; set; }

        [DataMember]
        public string DRReplayQueueLength { get; set; }

        [DataMember]
        public string PRLatestAvailableLogTime { get; set; }

        [DataMember]
        public string DRLatestAvailableLogTime { get; set; }

        [DataMember]
        public string PRLastReplayedLogTime { get; set; }

        [DataMember]
        public string DRLastReplayedLogTime { get; set; }

        [DataMember]
        public string PRLatestFullBackupTime { get; set; }

        [DataMember]
        public string DRLatestFullBackupTime { get; set; }

        [DataMember]
        public string PRLastLogGenerated { get; set; }

        [DataMember]
        public string DRLastLogGenerated { get; set; }

        [DataMember]
        public string PRLastLogCopied { get; set; }

        [DataMember]
        public string DRLastLogCopied { get; set; }

        [DataMember]
        public string PRLastLogReplayed { get; set; }

        [DataMember]
        public string DRLastLogReplayed { get; set; }

        [DataMember]
        public string PRLastCopiedLogTime { get; set; }

        [DataMember]
        public string DRLastCopiedLogTime { get; set; }

        [DataMember]
        public string CurrentDataLag { get; set; }

        public string InfraObjectName
        {
            get;
            set;
        }
        public string BusinessServiceName
        {
            get;
            set;
        }
      
        #endregion Properties
    }
}
