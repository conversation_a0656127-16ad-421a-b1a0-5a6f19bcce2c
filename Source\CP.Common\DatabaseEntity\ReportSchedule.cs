﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ReportSchedule", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ReportSchedule : BaseEntity
    {
        #region Properties

        //Id, ReportName, InfraObjectId, FromDate, ToDate, BusinessServiceId, ParallelDrOpId, ApplicationId,
        //ReceiverId, ScheduleTime, Time, CreateDate, IsActive

        [DataMember]
        public int Id
        {
            get;
            set;
        }

        [DataMember]
        public string ReportName
        {
            get;
            set;
        }

        [DataMember]
        public string ReportType
        {
            get;
            set;
        }

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string FromDate
        {
            get;
            set;
        }

        [DataMember]
        public string ToDate
        {
            get;
            set;
        }

        [DataMember]
        public int BusinessServiceId
        {
            get;
            set;
        }

        [DataMember]
        public int ParallelDrOpId
        {
            get;
            set;
        }

        [DataMember]
        public int AppId
        {
            get;
            set;
        }

        [DataMember]
        public int JobId
        {
            get;
            set;
        }

        [DataMember]
        public string ReceiverId
        {
            get;
            set;
        }

        [DataMember]
        public string ScheduleTime
        {
            get;
            set;
        }

        [DataMember]
        public string Time
        {
            get;
            set;
        }

        [DataMember]
        public int IsActive
        {
            get;
            set;
        }

        [DataMember]
        public int CompanyId
        {
            get;
            set;
        }

        [DataMember]
        public int BusinessFunctionId
        {
            get;
            set;
        }
        [DataMember]
        public string Frequency
        {
            get;
            set;
        }

        [DataMember]
        public int IsSchedule
        {
            get;
            set;
        }

        #endregion Properties
    }
}