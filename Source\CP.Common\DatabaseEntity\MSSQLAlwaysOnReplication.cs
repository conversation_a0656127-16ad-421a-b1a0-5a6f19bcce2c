﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    public class MSSQLAlwaysOnReplication : BaseEntity
    {
        private ReplicationBase _replicationBase = new ReplicationBase();

        #region Properties

        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string GroupName { get; set; }

        [DataMember]
        public string GroupRole { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }
        }

        [DataMember]
        public int Listenerflag { get; set; }

        [DataMember]
        public string ListenerName { get; set; } 

        #endregion Properties
    }
}
