namespace CP.UI.Report.TelerikReports
{
    using CP.Common.DatabaseEntity;
    using log4net;
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Data;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;

    /// <summary>
    /// Summary description for MssqlDatalagReport.
    /// </summary>
    public partial class Postgres_DataLagReport : Telerik.Reporting.Report
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(Postgres_DataLagReport));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();

        public Postgres_DataLagReport()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }
        private void showtable()
        {
            try
            {
                var dataTable = new DataTable();

                string strDate = Convert.ToDateTime(this.ReportParameters["iStartDate"].Value).ToString("yyyy-MM-dd");
                string endDate = Convert.ToDateTime(this.ReportParameters["iEndDate"].Value).ToString("yyyy-MM-dd");
                int infraObjectId = Convert.ToInt32(this.ReportParameters["iInfraId"].Value);

                IList<Postgre9xMonitorStatus> postgreslist = new List<Postgre9xMonitorStatus>();
                postgreslist = Facade.GetPostgre9xMonitorStatusSqlLogsByDate(infraObjectId, strDate, endDate);

                if (postgreslist != null && postgreslist.Count > 0)
                {
                    //dataTable.Columns.Add("Sr.No.");
                    //dataTable.Columns.Add("Replication Status PR");
                    //dataTable.Columns.Add("Replication Status DR");
                    //dataTable.Columns.Add("Last Log Receive Location");
                    //dataTable.Columns.Add("Last Log Reply Location");
                    //dataTable.Columns.Add("DataLag (hh:mm:ss)");
                    //dataTable.Columns.Add("TimeStamp");

                    dataTable.Columns.Add("Sr.No.");
                    dataTable.Columns.Add("ReplicationStatusPR");
                    dataTable.Columns.Add("ReplicationStatusDR");
                    dataTable.Columns.Add("Last_xlog_receive_location");
                    dataTable.Columns.Add("Last_xlog_replay_location");
                    dataTable.Columns.Add("DataLag_HHMMSS");
                    dataTable.Columns.Add("CreateDate");

                    _logger.Info("Data Mapping Start For Report.");
                    int i = 1;
                    foreach (Postgre9xMonitorStatus vsmoni in postgreslist)
                    {
                        DataRow dr = dataTable.NewRow();
                        dr["Sr.No."] = i.ToString();
                        dr["ReplicationStatusPR"] = vsmoni.ReplicationStatusPR != null ? vsmoni.ReplicationStatusPR : "N/A";
                        dr["ReplicationStatusDR"] = vsmoni.ReplicationStatusDR != null ? vsmoni.ReplicationStatusDR : "N/A";

                        dr["Last_xlog_receive_location"] = vsmoni.Last_xlog_receive_location != null ? vsmoni.Last_xlog_receive_location : "N/A";
                        dr["Last_xlog_replay_location"] = vsmoni.Last_xlog_replay_location != null ? vsmoni.Last_xlog_replay_location : "N/A";
                        dr["DataLag_HHMMSS"] = vsmoni.DataLag_HHMMSS != null ? vsmoni.DataLag_HHMMSS : "N/A";
                        dr["CreateDate"] = vsmoni.CreateDate != null ? Convert.ToString(vsmoni.CreateDate) : "N/A";

                        // InfraObject InfraObj = Facade.GetInfraObjectById(Convert.ToInt32(ddlGroup.SelectedValue));
                        InfraObject InfraObj = Facade.GetInfraObjectById(infraObjectId);
                        BusinessFunction businessFtn = Facade.GetBusinessFunctionById(InfraObj != null ? InfraObj.BusinessFunctionId : 0);
                        TimeSpan conDatalag = TimeSpan.FromSeconds(Convert.ToDouble(businessFtn.ConfiguredRPO));


                        dr["DataLag_HHMMSS"] = vsmoni.DataLag_HHMMSS != null ? vsmoni.DataLag_HHMMSS : "N/A";
                        i++;
                        dataTable.Rows.Add(dr);

                    }
                }
                this.DataSource = dataTable;
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method, InnerException Message " + ex.InnerException.Message);
            }
        }

       

        private void Postgres_DataLagReport_NeedDataSource(object sender, EventArgs e)
        {
            showtable();

        }

        
    }
}