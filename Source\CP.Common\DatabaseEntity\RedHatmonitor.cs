﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;


namespace CP.Common.DatabaseEntity
{
    public class RedHatmonitor : BaseEntity
    {
        public int InfraObjectId { get; set; }
        public string PRRedHatVirtualizationManagerIP { get; set; }
        public string PRRedHatVirtualizationManagerHostname { get; set; }
        public string PRStorageDomainName { get; set; }
        public string PRStorageDomainStatus { get; set; }
        public string PRStorageDomainType { get; set; }
        public string PRDatacenterNameStatus { get; set; }
        public string PRHostnameUnregisteredstoragedomainImport { get; set; }
        public string PRStorageDomainStatusUnregisteredstoragedomain { get; set; }
        public string PRTotalVirtualMachineCount { get; set; }
        public string PRRegisteredVirtualMachineCount { get; set; }
        public string PRUnRegisteredVirtualMachineCount { get; set; }
        public string PRRegisteredVirtualMachineNamestatus { get; set; }

        public string DRRedHatVirtualizationManagerIP { get; set; }
        public string DRRedHatVirtualizationManagerHostname { get; set; }
        public string DRStorageDomainName { get; set; }
        public string DRStorageDomainStatus { get; set; }
        public string DRStorageDomainType { get; set; }
        public string DRDatacenterNameStatus { get; set; }
        public string DRHostnameUnregisteredstoragedomainImport { get; set; }
        public string DRStorageDomainStatusUnregisteredstoragedomain { get; set; }
        public string DRTotalVirtualMachineCount { get; set; }
        public string DRRegisteredVirtualMachineCount { get; set; }
        public string DRUnRegisteredVirtualMachineCount { get; set; }
        public string DRRegisteredVirtualMachineNamestatus { get; set; }

        public string ApplyLag { get; set; }

    }
}
