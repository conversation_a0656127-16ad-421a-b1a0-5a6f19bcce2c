﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="HANADatabaseConfig.ascx.cs" Inherits="CP.UI.Controls.HANADatabaseConfig" %>

<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">HANA DB</h4>
        </div>
        <div class="widget-body">
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Database SID<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtDatabaseSid" CssClass="form-control" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator4" ControlToValidate="txtDatabaseSid" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Database SID"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="rfeOracleSID" runat="server" CssClass="error" ControlToValidate="txtDatabaseSid"
                        ErrorMessage="Enter Valid Database SID" ValidationGroup="dbConfig" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                        Display="Dynamic"></asp:RegularExpressionValidator>
                    <asp:Label ID="lblErr" runat="server" ForeColor="Red"
                        Text="Invalid License Key" Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                </div>
            </div>

              <div class="form-group">
                <label class="col-md-3 control-label">
                    Instance Number<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtInstanceNo" CssClass="form-control" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvInstanceNo" ControlToValidate="txtInstanceNo" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Instance Number"></asp:RequiredFieldValidator>
                     <asp:RegularExpressionValidator ID="revInstanceNo" runat="server" ControlToValidate="txtInstanceNo" CssClass="error"
                        ValidationGroup="dbConfig" ErrorMessage="Valid Numbers only" ValidationExpression="[0-9]{2,6}"></asp:RegularExpressionValidator>
                </div>
            </div>

              <div class="form-group">
                <label class="col-md-3 control-label">
                    Host Name<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtHostName" CssClass="form-control" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvHostName" ControlToValidate="txtHostName" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Host Name"></asp:RequiredFieldValidator>
                   <%-- <asp:RegularExpressionValidator ID="RegularExpressionValidator2" runat="server" CssClass="error" ControlToValidate="txtDatabaseSid"
                        ErrorMessage="Enter Valid Database SID" ValidationGroup="dbConfig" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                        Display="Dynamic"></asp:RegularExpressionValidator>
                    <asp:Label ID="Label2" runat="server" ForeColor="Red"
                        Text="Invalid License Key" Visible="False" Display="dynamic" CssClass="error"></asp:Label>--%>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-3 control-label">
                    Username<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtUserName" CssClass="form-control" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" ControlToValidate="txtUserName" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter UserName"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="rfeUser" runat="server" CssClass="error" ControlToValidate="txtUserName"
                        ErrorMessage="Enter Valid User Name" ValidationGroup="dbConfig" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                        Display="Dynamic"></asp:RegularExpressionValidator>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Password<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtPassword" CssClass="form-control chk-caps" runat="server" TextMode="Password"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" ControlToValidate="txtPassword" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Password"></asp:RequiredFieldValidator>
                    <span class="caps-error">Caps Lock is ON.</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Port<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtPort" runat="server" CssClass="form-control" MaxLength="6"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator3" ControlToValidate="txtPort" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Port"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="revPhone1" runat="server" ControlToValidate="txtPort" CssClass="error"
                        ValidationGroup="dbConfig" ErrorMessage="Valid Numbers only" ValidationExpression="[0-9]{2,6}"></asp:RegularExpressionValidator>
                </div>
            </div>
        </div>
    </div>
</div>
