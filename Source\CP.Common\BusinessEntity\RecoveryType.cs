using CP.Common.Shared;
using System.Collections.Generic;
using System.Linq;
using CP.Common.Shared;



namespace CP.Common.BusinessEntity
{
    public class RecoveryTypeName
    {
        //public int c { get; set; }
        public string RecoveryType { get; set; }

        public int ID { get; set; }

        public int TypeId { get; set; }

        private static IEnumerable<RecoveryTypeName> LoadData()
        {
            return new List<RecoveryTypeName>
            {
                new RecoveryTypeName {TypeId = 8, ID = 1, RecoveryType = "Application-IBM-GlobalMirror"},
                new RecoveryTypeName {TypeId = 6, ID = 1, RecoveryType = "Oracle Logshipping - IBM-GlobalMirror"},
                new RecoveryTypeName {TypeId = 4, ID = 2, RecoveryType = "Oracle Dataguard"},
                new RecoveryTypeName {TypeId = 9, ID = 3, RecoveryType = "Application - DataSync Replication"},
                new RecoveryTypeName {TypeId = 4, ID = 5, RecoveryType = "MSExchange - SCR"},
                new RecoveryTypeName {TypeId = 2, ID = 6, RecoveryType = "Netapp SnapMirror"},
                new RecoveryTypeName {TypeId = 8, ID = 6, RecoveryType = "Application-NetAppSnapMirror"},
                new RecoveryTypeName {TypeId = 8, ID = 7, RecoveryType = "Application-EMC-SRDF"},
                new RecoveryTypeName {TypeId = 5, ID = 9, RecoveryType = "Oracle Full DB - HITACHI-UR"},
                new RecoveryTypeName {TypeId = 12,ID = 10, RecoveryType = "VMware - DataSync Replication"},
                new RecoveryTypeName {TypeId = 11,ID = 11, RecoveryType = "VMware - IBM-GlobalMirror"},
                new RecoveryTypeName {TypeId = 11,ID = 12, RecoveryType = "VMware - NetAppSnapMirror"},
                new RecoveryTypeName {TypeId = 7, ID = 13, RecoveryType = "MSSQL-DataSync"},
                new RecoveryTypeName {TypeId = 7, ID = 14, RecoveryType = "Oracle with DataSync"},
                new RecoveryTypeName {TypeId = 4, ID = 15, RecoveryType = "IBM-DB2 HADR"},
                new RecoveryTypeName {TypeId = 5, ID = 16, RecoveryType = "Storage - Oracle Log Shipping - Hitachi UR"},
                new RecoveryTypeName {TypeId = 5, ID = 17, RecoveryType = "Storage - Oracle Full Db - Emc Srdf"},
                new RecoveryTypeName {TypeId = 5, ID = 18, RecoveryType = "Storage - Oracle LOG Shipping - Emc Srdf"},
                new RecoveryTypeName {TypeId = 11,ID = 19, RecoveryType = "VMware- HITACHI-UR"},
                new RecoveryTypeName {TypeId = 4, ID = 20, RecoveryType = "ExchangeDAG"},
                new RecoveryTypeName {TypeId = 7, ID = 21, RecoveryType = "DB2-DataSync"},
                new RecoveryTypeName {TypeId = 5, ID = 22, RecoveryType = "MySQL Full DB - IBM-GlobalMirror"},
                new RecoveryTypeName {TypeId = 4, ID = 25, RecoveryType = "EnterPriseDB"},
                new RecoveryTypeName {TypeId = 5, ID = 26, RecoveryType = "MS-SQL Full DB - EMC SRDF"},
                new RecoveryTypeName {TypeId = 4, ID = 27, RecoveryType = "Postgres 9.X"},
                //Added by meenakshi
                new RecoveryTypeName {TypeId = 4, ID = 28, RecoveryType = "MS-SQL2kX"},
                new RecoveryTypeName {TypeId = 9, ID = 29, RecoveryType = "Application-EC2S3 DataSync Replication"},
                new RecoveryTypeName {TypeId = 5, ID = 30, RecoveryType = "MSSQL - NetAppSnapMirror"},
                new RecoveryTypeName {TypeId = 5, ID = 31, RecoveryType = "Oracle Full DB - IBM-GlobalMirror"},
                new RecoveryTypeName {TypeId = 16,ID = 32, RecoveryType = "MS-SQL - Doubletake (Full DB)"},
                new RecoveryTypeName {TypeId = 16,ID = 33, RecoveryType = "Oracle - Doubletake (Full DB)"},
                new RecoveryTypeName {TypeId = 17,ID = 34, RecoveryType = "Application - DoubleTake"},
                new RecoveryTypeName {TypeId = 5, ID = 35 ,RecoveryType = "Oracle Full DB - NetApp-SnapMirror"},
                //new RecoveryTypeName {TypeId = 4, ID = 36, RecoveryType = "MSSQL-DataBase-Mirror"},
                new RecoveryTypeName {TypeId = 5, ID = 37, RecoveryType = "Oracle FULL DB - EMC SRDF VMAX"},
                new RecoveryTypeName {TypeId = 5, ID = 38, RecoveryType = "Oracle FULL DB - EMC SRDF DMX"},
                new RecoveryTypeName {TypeId = 5, ID = 39, RecoveryType = "MSSQL FULL DB - EMC SRDF VMAX"},
                new RecoveryTypeName {TypeId = 5, ID = 40, RecoveryType = "MSSQL FULL DB - EMC SRDF DMX"},
                new RecoveryTypeName {TypeId = 7, ID = 41, RecoveryType = "SyBase With DataSync"},
                new RecoveryTypeName {TypeId = 13,ID = 42, RecoveryType = "Application - MIMIX"},
                new RecoveryTypeName {TypeId = 5, ID = 43, RecoveryType = "Oracle Full DB - SVC-GlobalMirror"},
                new RecoveryTypeName {TypeId = 8, ID = 44, RecoveryType = "SVC - GlobalMirror OR Metro Mirror"},
                new RecoveryTypeName {TypeId = 5, ID = 45, RecoveryType = "Hitachi-OracleFullDB-Rac"},
                new RecoveryTypeName {TypeId = 5, ID = 46, RecoveryType = "EmcSrdf-OracleRac-FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 47, RecoveryType = "EmcSrdf-SyBase"},
                new RecoveryTypeName {TypeId = 5, ID = 48, RecoveryType = "Hitachi-SyBase"},
                new RecoveryTypeName {TypeId = 18,ID = 49, RecoveryType = "Hyper-V Replication"},
                new RecoveryTypeName {TypeId = 8, ID = 50, RecoveryType = "Application-HitachiUr"},
                new RecoveryTypeName {TypeId = 4, ID = 51, RecoveryType = "MySql-Native Log Shipping"},
                new RecoveryTypeName {TypeId = 4, ID = 52, RecoveryType = "MSSQL-DB Mirroring"},
                new RecoveryTypeName {TypeId=  5, ID = 53, RecoveryType="SVC-MSSQL FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 54, RecoveryType="DB2-IBMGLOBALMIRROR"},
                new RecoveryTypeName {TypeId = 11,ID = 55, RecoveryType = "VMWare with SVC"},
                new RecoveryTypeName {TypeId = 5, ID = 56, RecoveryType = "Storage - MSSQLFullDB - HitachiUR"},
                new RecoveryTypeName {TypeId = 5, ID = 57, RecoveryType = "Storage - MSSQLFullDB - GlobalMirror"},
                new RecoveryTypeName {TypeId = 5, ID = 58,RecoveryType = "DB2IBM-XIV Mirror"},
                new RecoveryTypeName {TypeId = 8, ID = 59, RecoveryType = "Application-XIV MIRROR"},
                new RecoveryTypeName {TypeId = 5, ID = 60, RecoveryType = "Storage - NetApp SnapMirrorFullDB - Postgres9.X"},
                new RecoveryTypeName {TypeId = 5, ID = 61, RecoveryType = "SVC-GlobalMirror OracleFullDB-Rac"},
                new RecoveryTypeName {TypeId = 13,ID = 62, RecoveryType = "Application - SRM VMware"},
                new RecoveryTypeName {TypeId = 5, ID = 63, RecoveryType = "EmcSrdf-Mysql-FullDB"},
                new RecoveryTypeName {TypeId = 7, ID = 64, RecoveryType = "MaxDB with DataSync"},
                new RecoveryTypeName {TypeId = 19,ID = 65, RecoveryType = "Sybase-SRS"},
                new RecoveryTypeName {TypeId = 17,ID = 66, RecoveryType = "Application - eBDR"},
                new RecoveryTypeName {TypeId = 14,ID = 67, RecoveryType = "Application DR-Net Replication"},
                new RecoveryTypeName {TypeId = 17,ID = 68, RecoveryType = "TPCR"},
                new RecoveryTypeName {TypeId = 4, ID = 69, RecoveryType = "MSSQL - AlwaysOn"},
                new RecoveryTypeName {TypeId = 8, ID = 70, RecoveryType = "Application-Recovery-Point"},
                new RecoveryTypeName {TypeId = 5, ID = 71, RecoveryType = "RecoverPoint-Oracle-FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 72, RecoveryType = "RecoverPoint-MSSql-FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 73, RecoveryType = "RecoverPoint-MySql-FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 74, RecoveryType = "MSSQL FullDB-HP3PAR"},
                new RecoveryTypeName {TypeId = 8, ID = 75, RecoveryType = "HP3Par with Application"},
                new RecoveryTypeName {TypeId = 5, ID = 76, RecoveryType = "HP3Par with Postgress FullDB"}, 
                new RecoveryTypeName {TypeId = 5, ID = 77, RecoveryType = "Oracle FULL DB - EMCSRDF - SG"},
                new RecoveryTypeName {TypeId = 5, ID = 78, RecoveryType = "Oracle FULL DB - EMCSRDF - Star"},
                new RecoveryTypeName {TypeId = 8, ID = 79, RecoveryType = "Application- EMC- SRDF - SG"},
                new RecoveryTypeName {TypeId = 8, ID = 80, RecoveryType = "Application- EMC- SRDF - Star"},
                new RecoveryTypeName {TypeId = 5, ID = 81, RecoveryType = "MSSQL - FullDB - EMCSRDF - SG"},
                new RecoveryTypeName {TypeId = 5, ID = 82, RecoveryType = "MSSQL - FullDB - EMCSRDF - Star"},
                new RecoveryTypeName {TypeId = 5, ID = 83, RecoveryType = "MSSQL FullDB-VVR Replication"},
                new RecoveryTypeName {TypeId = 20,ID = 84, RecoveryType = "HP3Par with ESXI"},
                new RecoveryTypeName {TypeId = 5, ID = 85, RecoveryType = "ZFS-Oracle-FullDB"},
                new RecoveryTypeName {TypeId = 8, ID = 86, RecoveryType = "ZFS-Application"},
                new RecoveryTypeName {TypeId = 5, ID = 87, RecoveryType = "Storage - DB2 - ZFS"},
                new RecoveryTypeName {TypeId = 5, ID = 88, RecoveryType = "ZFS-Max-FullDB"},
                new RecoveryTypeName {TypeId = 8, ID = 89, RecoveryType = "EMC ISILON SYNCIQ Multi-Site"},
                new RecoveryTypeName {TypeId = 5, ID = 90, RecoveryType = "Emc - MirrorView - Oracle - FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 91, RecoveryType = "Emc - MirrorView - Sybase - FullDB"},
                new RecoveryTypeName {TypeId = 8, ID = 92, RecoveryType = "Emc - MirrorView - Application"},
                new RecoveryTypeName {TypeId = 17,ID = 93, RecoveryType = "RoboCopy"},
                new RecoveryTypeName {TypeId = 19,ID = 94, RecoveryType = "Sybase-RS-HADR"},
                new RecoveryTypeName {TypeId = 8, ID = 95, RecoveryType = "Application-EMC Unity(UniSphere)"},
                new RecoveryTypeName {TypeId = 5, ID = 96, RecoveryType="DB2FullDB SVC"},
                new RecoveryTypeName {TypeId = 5, ID = 97, RecoveryType="Postgress FullDB SVC"},
                new RecoveryTypeName {TypeId = 5, ID = 98, RecoveryType = "HP3Par with MongoFullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 99, RecoveryType = "EMCSRDF-DB2FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 100, RecoveryType = "DB2 FullDB-EMC-Recovery Point"},
                new RecoveryTypeName {TypeId = 5, ID = 101, RecoveryType = "EMC-STAR-DB2FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 102, RecoveryType = "EMC-STAR-MYSQLFullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 103, RecoveryType = "Hitachi-Ur-DB2FullDB"},
                new RecoveryTypeName {TypeId = 5, ID = 104, RecoveryType = "Hitachi-Ur-MySqlFullDB"},
                new RecoveryTypeName {TypeId = 4, ID = 105, RecoveryType = "HANADB Replication"},
                new RecoveryTypeName {TypeId = 21,ID = 106, RecoveryType = "eBDR Replication Vmware to Vmware"},
                new RecoveryTypeName {TypeId = 4, ID = 107, RecoveryType = "Golden Gate Replication"},
                new RecoveryTypeName {TypeId = 17,ID = 108, RecoveryType = "RSync"},
                new RecoveryTypeName {TypeId = 5, ID = 109, RecoveryType = "ORACLE FullDB-HP3PAR"},
                new RecoveryTypeName {TypeId = 4, ID = 110, RecoveryType = "Postgres 10.X"},
                new RecoveryTypeName {TypeId = 17,ID = 112, RecoveryType = "Veeam Replication"},
                new RecoveryTypeName {TypeId = 14,ID = 113, RecoveryType = "VVRAppllication Replication"},
                new RecoveryTypeName {TypeId = 22,ID = 114, RecoveryType = "Oracle with RSync"},
                new RecoveryTypeName {TypeId = 4, ID = 115, RecoveryType = "Native Replication - Cloudant - DB"},
                new RecoveryTypeName {TypeId = 5, ID = 117, RecoveryType = "DB2-NetApp SnapMirror"},
                new RecoveryTypeName {TypeId = 5, ID = 118, RecoveryType = "HP3PAR MAX FULLDB"},
                new RecoveryTypeName {TypeId = 8, ID = 119, RecoveryType = "Application-Huwai -Storage"},
                new RecoveryTypeName {TypeId = 5, ID = 121, RecoveryType = "Storage -PostgresCluster -FullDB -Huawei"},
                new RecoveryTypeName {TypeId = 23,ID = 122, RecoveryType = "Nutanix Leap Replication"},
                new RecoveryTypeName {TypeId = 5, ID = 123,  RecoveryType="Mysql-FullDB-SVC"},
                new RecoveryTypeName {TypeId = 8, ID = 124,  RecoveryType="Application Solution - SVC"},
                new RecoveryTypeName {TypeId = 5, ID = 125, RecoveryType = "Storage - DB2 - EMCSRDF - SG"},
                new RecoveryTypeName {TypeId = 5, ID = 126, RecoveryType = "MYSQL - NetAppSnapMirror"},
                new RecoveryTypeName {TypeId = 13,ID = 127, RecoveryType = "Application - ActiveDirectory"},
                new RecoveryTypeName {TypeId = 5, ID = 128, RecoveryType = "Storage - MSSQL FullDB - Huawei"},
                new RecoveryTypeName {TypeId = 24,ID = 129, RecoveryType = "Nutanix Protection Domain Replication"},
                new RecoveryTypeName {TypeId = 25,ID = 130, RecoveryType = "MariaDB Galera Cluster"},
                new RecoveryTypeName {TypeId = 8, ID = 131, RecoveryType = "Azure Site Recovery"},
                new RecoveryTypeName {TypeId = 17,ID = 132, RecoveryType = "Zerto"},
                new RecoveryTypeName {TypeId = 8, ID = 133, RecoveryType = "Application- EMC- SRDF - CG"},
                new RecoveryTypeName {TypeId = 5, ID = 134, RecoveryType = "Oracle FULL DB - EMCSRDF - CG"},
                new RecoveryTypeName {TypeId = 4, ID = 135, RecoveryType = "MongoDB"},
                new RecoveryTypeName {TypeId = 10,ID = 136, RecoveryType = "Vmware Vsphere Replication"},
                new RecoveryTypeName {TypeId = (int)InfraObjectType.ApplicationNativeReplication, ID = 137, RecoveryType = Helper.EnumHelper.GetDescription(ReplicationType.AzureGatewayReplication)},
                new RecoveryTypeName {TypeId = 26, ID = 138, RecoveryType = "RPforVMReplication"},
                new RecoveryTypeName {TypeId = 8, ID = 139, RecoveryType = "Oracle Cloud Replication"},
                new RecoveryTypeName {TypeId = 8, ID = 140, RecoveryType = "EMCMTree" },
			    new RecoveryTypeName {TypeId = (int)InfraObjectType.ApplicationStorageReplication, ID = 141, RecoveryType = Helper.EnumHelper.GetDescription(ReplicationType.AvamarReplication)},
                new RecoveryTypeName {TypeId = 5, ID = 142, RecoveryType = "Storage - Oracle FULL DB - Zerto"},
                new RecoveryTypeName {TypeId = 5, ID = 143, RecoveryType = "Storage - MSSQL FULLDB - Zerto"},
                //new RecoveryTypeName {TypeId = 5, ID = 144, RecoveryType = "RPForVM With MSSQL FullDB"},
                new RecoveryTypeName {TypeId = 8, ID = 145, RecoveryType = "Rackware Replication"},
                new RecoveryTypeName {TypeId = 5, ID = 146, RecoveryType = "DB2FullDBMIMIX"},
                new RecoveryTypeName {TypeId = 8, ID = 147, RecoveryType = "DellEMCCyberRecoverVault Replication"},
                new RecoveryTypeName {TypeId = 4, ID = 149, RecoveryType = "OracleCloudDataGuard"},
              
               new RecoveryTypeName {TypeId = 4, ID = 150, RecoveryType = "Azure Kubernetes"},
               new RecoveryTypeName {TypeId = 4, ID = 151, RecoveryType = "Azure CosmosDB"},
                  new RecoveryTypeName {TypeId = 27, ID = 152, RecoveryType = "REDHAT_Virtulization"},
                  new RecoveryTypeName {TypeId = 28, ID = 153, RecoveryType = "OpenShift"},
                  new RecoveryTypeName {TypeId = 13, ID = 154, RecoveryType = "VSphere Replication"},
                  new RecoveryTypeName {TypeId = 4, ID = 155, RecoveryType = "Oracle Cloud ExaDB DataGuard"},

            };
        }

        public List<RecoveryTypeName> FetchRecoveryType(int id)
        {
            return (from p in LoadData()
                    orderby p.RecoveryType
                    where p.TypeId == id
                    select p).ToList();
        }
        public List<RecoveryTypeName> FetchRecoveryTypeName(int id, int typeId)
        {
            return (from p in LoadData()
                    orderby p.RecoveryType
                    where p.TypeId == typeId && p.ID == id
                    select p).ToList();
        }
        public string RecoveryTypebyId(int id, int type)
        {
            string result = "";
            var recoverytype =
                from p in LoadData()
                where p.ID == id && p.TypeId == type
                select new { p.RecoveryType };
            foreach (var anonymous in recoverytype)
            {
                result = anonymous.RecoveryType;
            }
            return result;
        }
    }
}
