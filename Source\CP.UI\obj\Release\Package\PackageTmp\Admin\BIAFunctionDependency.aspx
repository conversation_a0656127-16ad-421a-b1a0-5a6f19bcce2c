﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="BIAFunctionDependency.aspx.cs" Inherits="CP.UI.Admin.BIAFunctionDependency"
    Title="Continuity Patrol : Business Functions Impact Dependency " %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <script src="../Script/biafunctionsdependency.js" type="text/javascript"></script>
    <script src="../Script/d3.min.js" type="text/javascript"></script>
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script type="text/javascript" src="../Script/jquery-ui.min.js "></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <style type="text/css">
        .node circle {
            cursor: pointer;
            fill: #fff;
            stroke: steelblue;
            stroke-width: 1.5px;
        }

        .node text {
            font-size: 11px;
        }

        path.link {
            fill: none;
            stroke: #40d0a7;
            stroke-width: 1.5px;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <a id="btnReset" class="reset-icon margin-top" title="Back to initial zoom position"></a>
        <div id="body">
        </div>
        <script type="text/javascript">
            $(document).ready(function () {
                treeShow();
            });
        </script>
    </form>
</body>
</html>
