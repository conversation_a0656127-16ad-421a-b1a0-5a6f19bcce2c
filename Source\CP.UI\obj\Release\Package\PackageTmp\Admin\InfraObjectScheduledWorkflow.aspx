﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="InfraObjectScheduledWorkflow.aspx.cs" Inherits="CP.UI.InfraObjectScheduledWorkflow" Title="Continuity Patrol :: Admin-InfraObjectScheduledWorkflow" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/jquery.combobox/style.css" rel="stylesheet" />

    <link href="../App_Themes/CPTheme/jquery.simple-dtpicker.css" type="text/css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.timepicker.css" type="text/css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <%-- <script src="Script/jquery.min.js"></script>
    <script src="Script/jquery-migrate.min.js"></script>
    <script src="Script/less.min.js"></script>--%>
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script type="text/javascript" src="../Script/jquery.combobox.js"></script>
    <script src="../Script/Login.js" type="text/javascript"></script>
    <script src="../Script/MaskedPassword.js"></script>
    <script src="../Script/EncryptDecrypt.js"></script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="../Script/jquery.simple-dtpicker.js" type="text/javascript"></script>
    <script src="../Script/jquery.timepicker.min.js" type="text/javascript"></script>

    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
    </script>

    <style>
        .chosen-select + .chosen-container {
            width: 48% !important;
            opacity: 1 !important;
        }

        .combobox_selector {
            width: 240px !important;
        }
    </style>
    <style type="text/css">
        .bootstrap-select[class*="col"] .btn {
            width: 99% !important;
        }

        .col-md-6 {
            padding-right: 0px !important;
            width: 48.5% !important;
        }

        .widget.widget-heading-simple > .widget-head {
            height: 34px !important;
        }

        tr > td:first-child {
            font-weight: normal;
            /*vertical-align: top;*/
        }
    </style>
    <style type="text/css">
        #ctl00_cphBody_pnlDomain #ctl00_cphBody_combobox1 {
            width: 225px;
        }

        .st_div .combobox_button {
            left: 225px !important;
        }

        .st_div .log-ext3 i:before {
            font: 16px 'Glyphicons Regular' !important;
            left: 4px !important;
            top: 11px !important;
        }

        .st_div {
            position: relative;
            margin-top: -15px;
        }
    </style>
    <style type="text/css">
        .btn-group.bootstrap-select {
            width: 85%;
        }

        .btn.dropdown-toggle.clearfix.btn-default {
            width: 100%;
        }

        .btn-group.bootstrap-select.col-xs-2 {
            width: 18%;
            padding-right: 11px !important;
        }

        .infraScheduleScroll .mCSB_inside > .mCSB_container {
            margin-right: 0px !important;
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function () {
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();

            $(".infraScheduleScroll").mCustomScrollbar({
                axis: "y",
                //setHeight: "200px",
                //autoDraggerLength: false,
                //advanced: {
                //    updateOnContentResize: true,
                //    autoExpandHorizontalScroll: true
                //}
            });

        });
        function bindDelete(win) {
            var values = $("#ddlScript :selected").val();
            $.ajax({
                type: "POST",
                url: "InfraobjectScheduleWorkflow.aspx/DeleteSCPLScript",
                data: '{values: "' + values + '" }',
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                success: function Success(data) {
                    getDelete(data.d);
                }

            });


            CloseModel(win);

        }
        function getDelete(win) {
            if (win == 1) {

                location.pathname = location.pathname.replace(/(.*)\/[^/]*/, "$1/" + "InfraobjectScheduleWorkflow.aspx");


            }
        }
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
            var k = 0;
            //if (k == i)
            //    $("#ddlScript :selected").text() = "CPSL Script";
            $('#btnCPSLDelete').click(function () {
                k = 1;
                var values = $("#ddlScript :selected").val();
                var scripname = $("#ddlScript :selected").text();
                if (values > 1) {
                    OpenAlertModelLoad(' Do you want to Delete ' + scripname, bindDelete);
                    //}
                }

            });

            $('[id$=txtDateTime]').appendDtpicker();
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();

            $(".infraScheduleScroll").mCustomScrollbar({
                axis: "y",
                //setHeight: "200px",
                //autoDraggerLength: false,
                //advanced: {
                //    updateOnContentResize: true,
                //    autoExpandHorizontalScroll: true
                //}
            });
        }
    </script>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
    <%--<script type="text/javascript">
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>--%>
    <%-- <style type="text/css">
        #ctl00_cphBody_ddlRepType_chosen {
            width: 48% !important;
            opacity: 1 !important;
        }
    </style>--%>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <div class="innerLR">
        <h3>
            <img src="../Images/user-icon.png">
            Infra Object Schedule Workflow</h3>


        <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <asp:Panel ID="PanelAuthenticate" runat="server">
                    <div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="row">
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="txtDescription">
                                            Infra Object Name <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlInfraObjectName" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" Style="width: 469px;" AutoPostBack="true" OnSelectedIndexChanged="ddlInfraObjectName_SelectedIndexChanged">
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="ddlInfraObjectName"
                                                Display="Dynamic" CssClass="error" ErrorMessage="Select Infra Object." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="txtDescription">
                                            Select Action <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlActions" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" Style="width: 467px;" AutoPostBack="true" OnSelectedIndexChanged="ddlActions_IndexChanged">
                                                <asp:ListItem Value="0">Select Action</asp:ListItem>
                                                <%--<asp:ListItem Value="1" Text="CPSL Script"></asp:ListItem>--%>
                                                <asp:ListItem Value="2" Text="WorkFlow"></asp:ListItem>

                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="rvddlActions" runat="server" ControlToValidate="ddlActions"
                                                Display="Dynamic" CssClass="error" ErrorMessage="Select Workflow." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                    <div class="form-group" id="workflowtypediv" visible="false" runat="server">
                                        <label class="col-md-3 control-label" for="ddlType">
                                            Workflow Type <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="wrkflwtype" runat="server" TabIndex="3" AutoPostBack="true"
                                                CssClass="selectpicker col-md-6" data-style="btn-default" OnSelectedIndexChanged="wrkflwtype_SelectedIndexChanged">
                                                <asp:ListItem Value="0">Select Workflow Type</asp:ListItem>
                                                <asp:ListItem Value="1">Monitor</asp:ListItem>
                                                <asp:ListItem Value="2">DR Ready</asp:ListItem>
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator6" runat="server" ControlToValidate="wrkflwtype"
                                                Display="Dynamic" CssClass="error" ErrorMessage="Select Workflow Type" InitialValue="0"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                    <div class="form-group" runat="server" id="dvWorkFlow" visible="false">
                                        <label class="col-md-3 control-label" for="txtDescription">
                                            Select Workflow <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlSelectWorkflow" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" Style="width: 467px;" AutoPostBack="true" OnSelectedIndexChanged="ddlSelectWorkflowIndexChanged">
                                            </asp:DropDownList>
                                            <%--<asp:RequiredFieldValidator ID="rvWorkflow" runat="server" ControlToValidate="ddlSelectWorkflow"
                                                Display="Dynamic" CssClass="error" ErrorMessage="Select Workflow." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>
                                    <div class="form-group" runat="server" id="dvCPLScript">
                                        <label class="col-md-3 control-label" for="txtDescription">
                                            Select Script <span class="inactive">*</span></label>
                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlScript" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlScript_IndexChanged">
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="rvddlScript" runat="server" ControlToValidate="ddlScript"
                                                Display="Dynamic" CssClass="error" ErrorMessage="Select Script." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                    <div runat="server" id="dvCPSlBox" visible="false">
                                        <div class="form-group">
                                            <label class="col-md-3 control-label">Script Name</label>
                                            <div class="col-md-9">
                                                <asp:TextBox ID="txtScriptName" runat="server" CssClass="form-control"> </asp:TextBox>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-md-3 control-label">Script</label>
                                            <div class="col-md-9">
                                                <asp:TextBox ID="txtScript" runat="server" CssClass="form-control" TextMode="MultiLine"> </asp:TextBox>
                                            </div>
                                        </div>
                                    </div>
                                    <div runat="server" id="SchedulingInfraObject" visible="false">
                                        <div class="form-group">
                                            <label class="col-md-3 control-label" for="txtDescription">
                                                Scheduling <span class="inactive">*</span></label>
                                            <div class="col-md-9">
                                                <asp:DropDownList ID="ddlScheduling" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlSchedulingIndexChanged">
                                                    <asp:ListItem Value="0" Text="Select"></asp:ListItem>
                                                    <asp:ListItem Value="1" Text="Once"></asp:ListItem>
                                                    <asp:ListItem Value="2" Text="Cycle"></asp:ListItem>
                                                </asp:DropDownList>


                                                <asp:RequiredFieldValidator ID="rvScheduling" runat="server" ControlToValidate="ddlScheduling"
                                                    Display="Dynamic" CssClass="error" ErrorMessage="Select Scheduling." Font-Size="9pt" InitialValue="0"></asp:RequiredFieldValidator>

                                            </div>
                                        </div>
                                        <div class="form-group" visible="false" runat="server" id="divCal">
                                            <label class="col-md-3 control-label">Select Date-Time</label>
                                            <div class="col-md-9">
                                                <asp:TextBox ID="txtDateTime" runat="server" CssClass="form-control" Width="48.5%"> </asp:TextBox>
                                            </div>
                                        </div>
                                    </div>
                                    <div runat="server" id="timeMgmt" visible="false">
                                        <div class="form-group">
                                            <label class="col-md-3">
                                                Time Interval <span class="inactive">*</span>
                                            </label>
                                            <div class="col-md-9">
                                                <asp:Panel ID="PanelInterval" runat="server">
                                                    <asp:RadioButtonList ID="rdTimeInterval" runat="server" RepeatDirection="Horizontal"
                                                        CssClass="dropbox text-indent" OnSelectedIndexChanged="rdTimeInterval_SelectedIndexChanged"
                                                        AutoPostBack="True">
                                                        <asp:ListItem>Minute(s)</asp:ListItem>
                                                        <asp:ListItem>Hour(s)</asp:ListItem>
                                                        <asp:ListItem>Day(s)</asp:ListItem>
                                                    </asp:RadioButtonList>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="rdTimeInterval"
                                                        Display="Dynamic" CssClass="error" ErrorMessage="Select Interval Time" Font-Size="9pt"></asp:RequiredFieldValidator>
                                                    <asp:Label ID="lbltimeintervalErrormessage" runat="server" Visible="false" Text="Select Interval Time"></asp:Label>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="rdTimeInterval" Display="Dynamic" ValidationGroup="Infra"
                                                        CssClass="error" ErrorMessage="Select Time Interval In Either Minutes, Hours or Days"></asp:RequiredFieldValidator>
                                                </asp:Panel>
                                            </div>
                                        </div>

                                        <asp:Panel ID="Panel_Minuite" runat="server" Visible="false">
                                            <div class="form-group">
                                                <label class="col-md-3">
                                                    Set Time
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:Label ID="lblEveryMinuite" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                    <asp:TextBox ID="txteveryminuite" runat="server" CssClass="form-control" Style="width: 12%;"
                                                        MaxLength="4" CausesValidation="True"></asp:TextBox>
                                                    <asp:Label ID="lblminutes" runat="server" Text="Minute(s)" CssClass="margin-right"></asp:Label>
                                                    <asp:RegularExpressionValidator ID="REVEveryminuite" runat="server" CssClass="error"
                                                        ControlToValidate="txteveryminuite" ErrorMessage="Please Enter only Numbers" Display="Dynamic"
                                                        ValidationExpression="\d+" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                    <asp:RequiredFieldValidator ID="rfvtxteveryminuite" runat="server" Enabled="true"
                                                        ControlToValidate="txteveryminuite" Display="Dynamic" ForeColor="Red"
                                                        CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                        </asp:Panel>


                                        <asp:Panel ID="Panel_Hourly" runat="server" Visible="false">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Set Time
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:Label ID="lblEveryHourly" runat="server" CssClass="margin-right" Text="Every"></asp:Label>
                                                    <asp:TextBox ID="txteveryhour" runat="server" CssClass="form-control" Style="width: 12%;"
                                                        MaxLength="2"></asp:TextBox>

                                                    <asp:Label ID="lblhours" runat="server" Text="Hour(s)" CssClass="margin-right"></asp:Label>
                                                    <asp:TextBox ID="txteveryhourlyminuite" runat="server" CssClass="form-control" Style="width: 12%;" AutoPostBack="true"
                                                        MaxLength="2" OnTextChanged="txteveryhourlyminuite_TextChanged"></asp:TextBox>
                                                    <asp:Label ID="lblhourlyminutes" runat="server" Text="Minute(s)"></asp:Label>
                                                    <asp:RequiredFieldValidator ID="rfvtxteveryhour" runat="server" Enabled="true" ControlToValidate="txteveryhour"
                                                        Display="Dynamic" Font-Size="8" ForeColor="Red" CssClass="error" ErrorMessage="Enter hours and  minutes"></asp:RequiredFieldValidator>
                                                    <%--<asp:RequiredFieldValidator ID="rfvtxteveryhourlyminuite" runat="server" Enabled="true"
                                                        ControlToValidate="txteveryhourlyminuite" Display="Dynamic" ForeColor="Red"
                                                        CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>--%>
                                                    <asp:RegularExpressionValidator ID="revtxteveryhour" runat="server" CssClass="error"
                                                        ControlToValidate="txteveryhour" ErrorMessage="Enter only Numbers" ValidationExpression="\d+"
                                                        Font-Size="9pt"></asp:RegularExpressionValidator>
                                                    <asp:RangeValidator ID="rng" runat="server" CssClass="error" ControlToValidate="txteveryhourlyminuite" Display="Dynamic"
                                                        ErrorMessage="Minutes Should Be Less Than 60" Type="Integer" Enabled="false" MaximumValue="60" MinimumValue="0"></asp:RangeValidator>
                                                    <asp:RegularExpressionValidator ID="regexpfornumeric" runat="server" CssClass="error" Enabled="false"
                                                        ControlToValidate="txteveryhourlyminuite" ErrorMessage="Enter Numeric Only" Display="Dynamic"
                                                        ValidationExpression="^[0-99]*$" SetFocusOnError="True" Font-Size="9pt"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>
                                        </asp:Panel>

                                        <asp:Panel ID="Panel_Daily" runat="server" Visible="false">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label" style="vertical-align: top">
                                                    Set Time
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:Label ID="lblEverydaily" runat="server" Text="Every" CssClass="margin-right"></asp:Label>
                                                    <asp:TextBox ID="txteverydaily" runat="server" CssClass="form-control" Style="width: 16%; margin-left: 21px;"></asp:TextBox>
                                                    <asp:Label ID="lbldays" runat="server" Text="Day(s)" CssClass="margin-right"></asp:Label>
                                                    <br />
                                                    <br />
                                                    <asp:Label ID="lblstartTime" runat="server" CssClass="pull-left margin-right margin-top" Text="StartTime "></asp:Label>
                                                    <asp:DropDownList ID="ddlhours" runat="server" CssClass="selectpicker col-xs-2" data-style="btn-default" Style="width: 16%">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:Label ID="lblhr" runat="server" CssClass="pull-left margin-right margin-top" Text="Hr"></asp:Label>
                                                    <asp:DropDownList ID="ddlminutes" runat="server" CssClass="selectpicker col-xs-2"
                                                        data-style="btn-default" Style="width: 16%">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                        <asp:ListItem>24</asp:ListItem>
                                                        <asp:ListItem>25</asp:ListItem>
                                                        <asp:ListItem>26</asp:ListItem>
                                                        <asp:ListItem>27</asp:ListItem>
                                                        <asp:ListItem>28</asp:ListItem>
                                                        <asp:ListItem>29</asp:ListItem>
                                                        <asp:ListItem>30</asp:ListItem>
                                                        <asp:ListItem>31</asp:ListItem>
                                                        <asp:ListItem>32</asp:ListItem>
                                                        <asp:ListItem>33</asp:ListItem>
                                                        <asp:ListItem>34</asp:ListItem>
                                                        <asp:ListItem>35</asp:ListItem>
                                                        <asp:ListItem>36</asp:ListItem>
                                                        <asp:ListItem>37</asp:ListItem>
                                                        <asp:ListItem>38</asp:ListItem>
                                                        <asp:ListItem>39</asp:ListItem>
                                                        <asp:ListItem>40</asp:ListItem>
                                                        <asp:ListItem>41</asp:ListItem>
                                                        <asp:ListItem>42</asp:ListItem>
                                                        <asp:ListItem>43</asp:ListItem>
                                                        <asp:ListItem>44</asp:ListItem>
                                                        <asp:ListItem>45</asp:ListItem>
                                                        <asp:ListItem>46</asp:ListItem>
                                                        <asp:ListItem>47</asp:ListItem>
                                                        <asp:ListItem>48</asp:ListItem>
                                                        <asp:ListItem>49</asp:ListItem>
                                                        <asp:ListItem>50</asp:ListItem>
                                                        <asp:ListItem>51</asp:ListItem>
                                                        <asp:ListItem>52</asp:ListItem>
                                                        <asp:ListItem>53</asp:ListItem>
                                                        <asp:ListItem>54</asp:ListItem>
                                                        <asp:ListItem>55</asp:ListItem>
                                                        <asp:ListItem>56</asp:ListItem>
                                                        <asp:ListItem>57</asp:ListItem>
                                                        <asp:ListItem>58</asp:ListItem>
                                                        <asp:ListItem>59</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:Label ID="lblmin" runat="server" CssClass="pull-left margin-right margin-top" Text="Min "></asp:Label>
                                                    <asp:RegularExpressionValidator ID="revdays" runat="server" CssClass="error" ControlToValidate="txteverydaily" Display="Dynamic"
                                                        ErrorMessage="Please Enter only Numbers" ValidationExpression="\d+"></asp:RegularExpressionValidator>
                                                    <asp:RequiredFieldValidator ID="rfveverydaily" runat="server" ControlToValidate="txteverydaily"
                                                        CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days" ForeColor="Red"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                        </asp:Panel>

                                    </div>
                                    <hr class="separator" />
                                    <div class="form-actions row">
                                        <div class="col-lg-5 text-right padding-none-LR">
                                            <asp:Label ID="lblMessage" runat="server"></asp:Label>
                                            <asp:HiddenField ID="hdnSCPlName" runat="server" />
                                        </div>
                                        <div class="col-lg-7">

                                            <asp:Button ID="btnAddToSchedule" runat="server" Text="Add" CssClass="btn btn-primary" OnClick="btnAddToSchedule_Click" CommandArgument="Save" Width="15%" Style="margin-left: 14px;" />

                                            <asp:Button ID="btnCPSLDelete" runat="server" Text="Delete" CssClass="btn btn-primary" Visible="false" CausesValidation="false" Width="15%" />
                                            <%--   <input type="button" id="btnCPSLDelete" class="btn btn-primary" visible="false" value="Delete" />--%>
                                            <%-- <TK1:ConfirmButtonExtender ID="ConfirmsDelete" runat="server" TargetControlID="btnCPSLDelete"
                                                            ConfirmText='<%# "Are you sure want to delete " + hdnSCPlName.Value + " ? " %>'
                                                            OnClientCancel="CancelClick">
                                                        </TK1:ConfirmButtonExtender>OnClick="btnCPSLDelete_Click"--%>

                                            <asp:Button ID="btnCancel" runat="server" Text="Cancel" CssClass="btn btn-default" OnClick="btnCancel_Click" CausesValidation="false" Width="15%" />
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <h3>Workflow List</h3>
                        <div class="widget widget-heading-simple widget-body-white">
                            <div class="widget-body">
                                <div runat="server" id="dvlvWorkFlow" visible="false">

                                    <asp:ListView ID="lvInfraobjectSchedule" runat="server" OnItemEditing="lvInfraobjectScheduleItemEditing"
                                        OnItemDeleting="lvInfraobjectScheduleItemDeleting" OnPreRender="lvInfraobjectSchedulePreRender" OnItemDataBound="lvInfraobjectSchedule_ItemDataBound" DataKeyNames="Id">

                                        <LayoutTemplate>
                                            <table class="table table-striped table-bordered table-condensed margin-bottom-none" style="table-layout: fixed" width="100%">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 4%;" class="text-center">
                                                            <span>
                                                                <img src="../Images/icons/replication_list.png" /></span>
                                                        </th>
                                                        <th style="width: 50%;">Workflow Name
                                                        </th>
                                                        <th style="width: 20%;">Execution Schedule
                                                        </th>
                                                        <th style="width: 12%;" class="text-center">Enable
                                                        </th>
                                                        <th style="width: 12%;" class="text-center">Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                            </table>
                                            <div class="infraScheduleScroll" style="max-height: 200px">
                                                <table class="table table-striped table-bordered table-condensed margin-bottom-none" style="table-layout: fixed" width="100%">
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </div>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td style="width: 4%;" class="text-center">
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                                    <asp:Label ID="lblSchdulTypes" runat="server" Text='<%# Eval("ScheduleType")  %>' Visible="false" />
                                                </td>
                                                <td style="width: 50%;" class="tdword-wrap">
                                                    <asp:Label ID="lblName" runat="server" Text='<%# Eval("WorkFlowName") %>' />
                                                </td>
                                                <td style="width: 20%;">
                                                    <asp:Label ID="lblScheduleTime" runat="server" Text='<%# Eval("ScheduleTime") %>' />
                                                </td>
                                                <td style="width: 12%;" class="text-center">
                                                    <asp:CheckBox ID="chkEnable" runat="server" Checked='<%# Convert.ToBoolean(Eval("IsEnable")) %>'
                                                        OnCheckedChanged="chkEnable_CheckedChanged" AutoPostBack="true"></asp:CheckBox>
                                                </td>
                                                <td style="width: 12%;" class="text-center">
                                                    <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                        ToolTip="Edit" ImageUrl="../images/icons/pencil.png" CausesValidation="false" />
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" CausesValidation="false" />
                                                    <asp:LinkButton ID="lnkBtnHistory" runat="server" ToolTip="History" CssClass="icon-state" Style="vertical-align: top; margin-left: 2px;" CommandArgument='<%# Eval("Id") %>' OnCommand="lnkBtnHistory_Command" CausesValidation="false"></asp:LinkButton>
                                                </td>
                                                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                    ConfirmText='<%# "Are you sure want to delete " + Eval("WorkFlowName") + " ? " %>'
                                                    OnClientCancel="CancelClick">
                                                </TK1:ConfirmButtonExtender>
                                            </tr>
                                        </ItemTemplate>
                                        <EmptyDataTemplate>
                                            <div class="message warning align-center bold">
                                                <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                            </div>
                                        </EmptyDataTemplate>
                                    </asp:ListView>

                                </div>

                                <div runat="server" id="dvlvCPSL">

                                    <asp:ListView ID="lvCPSL" runat="server" OnItemEditing="lvCPSLItemEditing"
                                        OnItemDeleting="lvCPSLItemDeleting" OnPreRender="lvCPSLPreRender" OnItemDataBound="lvCPSL_ItemDataBound" DataKeyNames="Id">

                                        <LayoutTemplate>
                                            <table class="table table-striped table-bordered table-condensed margin-bottom-none" style="table-layout: fixed" width="100%">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 4%;" class="text-center">
                                                            <span>
                                                                <img src="../Images/icons/replication_list.png" /></span>
                                                        </th>
                                                        <th style="width: 50%;">Name
                                                        </th>
                                                        <th style="width: 20%;">Schedule
                                                        </th>
                                                        <th style="width: 12%;" class="text-center">Enable
                                                        </th>
                                                        <th style="width: 12%;" class="text-center">Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                            </table>
                                            <div class="infraScheduleScroll" style="max-height: 200px">
                                                <table class="table table-striped table-bordered table-condensed margin-bottom-none" style="table-layout: fixed" width="100%">
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </div>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td style="width: 4%;" class="text-center">
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                                    <asp:Label ID="lblCpslID" runat="server" Text='<%# Eval("CPSLId") %>' Visible="false" />
                                                    <asp:Label ID="lblCPSLTypes" runat="server" Text='<%# Eval("CPSLType")  %>' Visible="false" />
                                                </td>
                                                <td style="width: 50%;" class="tdword-wrap">
                                                    <asp:Label ID="lblName" runat="server" Text='<%# Eval("CPSLNames") %>' />
                                                </td>
                                                <td style="width: 20%;">
                                                    <asp:Label ID="lblCPSLTime" runat="server" Text='<%# Eval("CPSLTime") %>' />
                                                </td>
                                                <td style="width: 12%;" class="text-center">
                                                    <asp:CheckBox ID="chkCPSLEnable" runat="server" Checked='<%# Convert.ToBoolean(Eval("IsEnable")) %>'
                                                        OnCheckedChanged="chkCPSLEnable_CheckedChanged" AutoPostBack="true"></asp:CheckBox>
                                                </td>
                                                <td style="width: 12%;" class="text-center">
                                                    <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                        ToolTip="Edit" ImageUrl="../images/icons/pencil.png" CausesValidation="false" />
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" CausesValidation="false" />
                                                    <asp:LinkButton ID="lnkBtnHistory" runat="server" ToolTip="History" CssClass="interval-icon text-right" Style="vertical-align: top; margin-left: 2px;" CausesValidation="false"></asp:LinkButton>
                                                </td>
                                                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                    ConfirmText='<%# "Are you sure want to delete " + Eval("CPSLNames") + " ? " %>'
                                                    OnClientCancel="CancelClick">
                                                </TK1:ConfirmButtonExtender>
                                            </tr>
                                        </ItemTemplate>
                                        <EmptyDataTemplate>
                                            <div class="message warning align-center bold">
                                                <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                            </div>
                                        </EmptyDataTemplate>
                                    </asp:ListView>

                                </div>

                                <div runat="server" id="Div1" visible="false">

                                    <asp:ListView ID="ListView1" runat="server">

                                        <LayoutTemplate>
                                            <table class="table table-striped table-bordered table-condensed margin-bottom-none" style="table-layout: fixed" width="100%">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 4%;" class="txet-center">
                                                            <span>
                                                                <img src="../Images/icons/replication_list.png" /></span>
                                                        </th>
                                                        <th style="width: 50%;">Workflow Name
                                                        </th>
                                                        <th style="width: 20%;">Execution Schedule
                                                        </th>
                                                        <th style="width: 12%;" class="txet-center">Enable
                                                        </th>
                                                        <th style="width: 12%;" class="txet-center">Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                            </table>
                                            <div class="infraScheduleScroll" style="max-height: 200px">
                                                <table class="table table-striped table-bordered table-condensed margin-bottom-none" style="table-layout: fixed" width="100%">
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </div>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td style="width: 4%;" class="txet-center"></td>
                                                <td style="width: 50%;" class="tdword-wrap"></td>
                                                <td style="width: 20%;"></td>
                                                <td style="width: 12%;" class="txet-center"></td>
                                                <td style="width: 12%;" class="txet-center"></td>

                                            </tr>
                                        </ItemTemplate>
                                        <EmptyDataTemplate>
                                            <div class="message warning align-center bold">
                                            </div>
                                        </EmptyDataTemplate>
                                    </asp:ListView>

                                </div>
                            </div>
                        </div>
                </asp:Panel>

                <asp:Panel ID="Panel_History" runat="server" Width="100%" Visible="false">
                    <asp:UpdatePanel ID="UpdatePanelhistory" runat="server" UpdateMode="Conditional">
                        <ContentTemplate>
                            <div class="modal bg" style="display: block;">
                                <div class="modal-dialog" style="width: 70%;">
                                    <div class="modal-content  widget-body-white">
                                        <div class="modal-header">
                                            <h3 class="modal-title">Infra Object Scheduled Workflow History</h3>
                                            <asp:LinkButton ID="lkbtnHistory" runat="server" ToolTip="Close window" OnClick="lkbtnHistory_Click" CausesValidation="false"
                                                class="close" CommandName="Close">×</asp:LinkButton>
                                        </div>
                                        <div class="modal-body">

                                            <asp:ListView ID="listsceduleworkflowstatus" runat="server" Style="width: 100%;" OnItemDataBound="listsceduleworkflowstatus_ItemDataBound">

                                                <LayoutTemplate>
                                                    <table class="table table-striped table-bordered table-condensed small-table" style="width: 100%; margin-bottom: 0;">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 6%;" class="text-center">
                                                                    <span>
                                                                        <img src="../Images/icons/replication_list.png" /></span>
                                                                </th>
                                                                <th style="width: 19.7%;">Workflow Name
                                                                </th>
                                                                <th style="width: 19.7%;">Infraobject
                                                                </th>
                                                                <th style="width: 24.5%;">Action Name
                                                                </th>
                                                                <th style="width: 9.9%;">Status
                                                                </th>
                                                                <th style="width: 9.6%;">RTO
                                                                </th>
                                                                <%--<th class="text-center" style="width: 8.7%;">Action
                                                                </th>--%>
                                                            </tr>
                                                        </thead>
                                                    </table>
                                                    <div class="infraScheduleScroll" style="max-height: 200px">
                                                        <table class="table table-striped table-bordered table-condensed small-table" style="width: 100%;">
                                                            <tbody>
                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </LayoutTemplate>
                                                <ItemTemplate>
                                                    <tr id="trCompletedTasks" runat="server">
                                                        <td style="width: 6%;" class="text-center">
                                                            <asp:Label ID="Label5" runat="server" Text='<%# Eval("ScheduleId") %>' Visible="false" />
                                                            <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>                                                            
                                                        </td>
                                                        <td style="width: 20%;">
                                                            <div class="small-table-tdword-wrap">
                                                                <asp:Label ID="lblName" runat="server" Text='<%# Eval("WorkflowName") %>' ToolTip='<%# Eval("WorkflowName") %>' />
                                                            </div>
                                                        </td>
                                                        <td style="width: 20%;">
                                                            <div class="small-table-tdword-wrap">
                                                                <asp:Label ID="Label1" runat="server" Text='<%# Eval("InfraObjectName") %>' ToolTip='<%# Eval("InfraObjectName") %>' />
                                                            </div>
                                                        </td>
                                                        <td style="width: 25%;">
                                                            <div class="small-table-tdword-wrap">
                                                                <asp:Label ID="Label2" runat="server" Text='<%# Eval("CurrentActionName") %>' ToolTip='<%# Eval("CurrentActionName") %>' />
                                                            </div>
                                                        </td>
                                                        <td style="width: 10%;">
                                                            <asp:Label ID="Label3" runat="server" Text='<%# Eval("Status") %>' />
                                                        </td>
                                                        <td style="width: 10%;">
                                                            <asp:Label ID="Label4" runat="server" Text='<%# Eval("RTO") %>' />
                                                        </td>
                                                       <%-- <td class="text-center" style="width: 8%;">
                                                            <asp:LinkButton ID="lnkBtnHistoryreport" runat="server" ToolTip="History Report" CssClass="icon-log" Style="vertical-align: top; margin-left: 2px;" CommandArgument='<%# Eval("ScheduleId") %>' OnCommand="lnkBtnHistoryreport_Command" CausesValidation="false"></asp:LinkButton>
                                                        </td>--%>
                                                    </tr>
                                                </ItemTemplate>
                                                <EmptyDataTemplate>
                                                    <div class="message warning align-center bold">
                                                        <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                                    </div>
                                                </EmptyDataTemplate>
                                            </asp:ListView>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </asp:Panel>
                <asp:Panel ID="pnlPrivType" runat="server" Style="margin: auto;" Visible="false">
                    <div class="modal bg" style="display: block;">
                        <div class="modal-dialog" style="width: 500px;">
                            <div class="modal-content  widget-body-white">
                                <div class="modal-header">
                                    <asp:LinkButton ID="lnkbtnClose" class="close" runat="server" CommandName="Close" OnClick="lnkbtnClose_Click"> ×</asp:LinkButton>
                                    <h3 class="modal-title">User Authentication</h3>
                                </div>
                                <div class="modal-body">
                                    <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                        <ContentTemplate>
                                            <div class="col-md-12 form-horizontal uniformjs">
                                                <%--<object data="PrivlegeManagement.aspx" type="text/html" width="470" height="320" style="overflow: hidden"></object>--%>

                                                <div class="col-md-12 form-group">
                                                    <div class="col-md-4">
                                                        <label>User Name</label>
                                                        <span style="color: red">*</span>
                                                    </div>
                                                    <div class="col-md-8">
                                                        <input type="hidden" id="UserEncrypt" runat="server" />
                                                        <asp:TextBox ID="UserName" Width="100%" runat="server" autocomplete="off" CssClass="form-control" placeholder="Username" onfocus="cleartext()" onblur="getUserNameHash(this)"></asp:TextBox>
                                                        <asp:RequiredFieldValidator ID="UserNameRequired" runat="server" ControlToValidate="UserName" Display="Dynamic" ErrorMessage="Required." ToolTip="User Name is required." CssClass="error"
                                                            ValidationGroup="LoginUser"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-md-12 form-group">
                                                    <div class="col-md-4">
                                                        <label>Password</label>
                                                        <span style="color: red">*</span>
                                                    </div>
                                                    <div class="col-md-8">

                                                        <input type="hidden" id="PassEncyptHidden" runat="server" />
                                                        <input type="hidden" id="hdfStaticGuid" runat="server" />
                                                        <asp:TextBox ID="Password" runat="server" EnableViewState="false" TextMode="Password" autocomplete="off" CssClass="form-control" placeholder="Password" ReadOnly="false" onblur="getPasswordHash(this)" onfocus="clearControlData(this)" Width="100%"></asp:TextBox>

                                                        <asp:RequiredFieldValidator ID="PasswordRequired" runat="server" ControlToValidate="Password" Display="Dynamic" ErrorMessage="Required." CssClass="error"
                                                            ToolTip="Password is required." ValidationGroup="LoginUser">

                                                        </asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-ms-12 form-group">
                                                    <div class="col-md-4"></div>
                                                    <div class="col-md-8">
                                                        <asp:CheckBox ID="chkActiveDirectory" runat="server" AutoPostBack="true" OnCheckedChanged="chkActiveDirectory_CheckedChanged" />

                                                        <asp:Label ID="Label2" Text="AD" runat="server" CssClass="padding padding-none-TB"></asp:Label>
                                                    </div>
                                                </div>
                                                <div class="clearfix"></div>
                                                <asp:Panel ID="pnlDomain" CssClass="col-md-12 form-group" runat="server" Visible="false">
                                                    <div class="col-md-4">
                                                        <label>
                                                            <asp:Label ID="lbldomainName" runat="server" Text="Domain"></asp:Label><span style="color: red">*</span></label>
                                                    </div>
                                                    <div class="col-md-8 st_div">
                                                        <div class="input-icon left">
                                                            <span class="glyphicons globe log-ext3"><i></i></span>
                                                            <div>
                                                                <input id="combobox1" type="text" runat="server" visible="false" placeholder="Enter Domain" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </asp:Panel>
                                            </div>
                                            <div>
                                                <asp:Label ID="labelSDErrormessage" runat="server" CssClass="error" Visible="false"></asp:Label>
                                            </div>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </div>
                                <div class="modal-footer">
                                    <div class="col-xs-5 text-left" style="padding-top: 10px;">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required Fields</span>
                                    </div>
                                    <div class="col-xs-7">
                                        <asp:Button ID="btnSave" CssClass="btn btn-primary" runat="server" Text="Login" CommandArgument="Login"
                                            TabIndex="18" OnClick="btnAddToSchedule_Click" />
                                        <asp:Button ID="btnsavdel" CssClass="btn btn-primary" Visible="false" runat="server" Text="Login" CommandArgument="Login"
                                            TabIndex="18" CausesValidation="false" OnClick="btnsavdel_Click" />
                                        <asp:Button ID="Button1" CssClass="btn btn-block btn-inverse" runat="server"
                                            Text="Close" CausesValidation="False" TabIndex="19" OnClick="Button1_Click" Style="width: 25%; display: inline-block;" />

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>
            </ContentTemplate>
        </asp:UpdatePanel>


    </div>
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            //$(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
         
    </script>

    <script type="text/javascript">

        function callscroll() {
            
        }

        function binddropdown() {
            $.ajax({
                type: "POST",
                url: "InfraObjectScheduledWorkflow.aspx/DiscoverDomains",
                data: "{}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                success: function Success(data) {
                    getBindValue(data.d);
                }
            });
        }

        function getBindValue(value) {
            var data = value.split(":");
            for (var i = 0; i < data.length; i++) {
                var volume = data[i].split(",");
                var text = volume[0];
                jQuery(function () {
                    jQuery('[id$=combobox1]').combobox([
                            text
                    ]);
                    $('[id$=combobox1]').val(volume[0]);

                });
            }
        }

        function cleartext() {

            var input = document.createElement('input');
            input.value = $('[id$=UserEncrypt]').val();
            if (input.value != "" && input.value != null && $('[id$=ctl00_cphBody_UserName]').val() != "") {
                $('[id$=ctl00_cphBody_UserName]').val(getOrignalData(input, $('#ctl00_cphBody_hdfStaticGuid').val()));
            }
            $('[id$=UserEncrypt]').val("");
            $('[id$=txtcaptcha]').val("");
            $('[id$=ctl00_cphBody_Password]').val("");
            $('[id$=passHidden]').val("");
            $('[id$=ctl00_cphBody_PassEncyptHidden]').val("");
        }

    </script>
</asp:Content>
