﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "NutanixLeapReplication", Namespace = "http://www.ContinuityPlatform.com/types")]

   public class NutanixLeapReplication :BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string LeapRecoveryPlanName { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }

        #endregion
    }
}
