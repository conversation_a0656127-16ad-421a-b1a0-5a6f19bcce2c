﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "SmtpConfiguration", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class SmtpConfiguration : BaseEntity
    {
        #region Properties

        [DataMember]
        public string SmtpHost { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public string Email { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public bool EnableSSL { get; set; }

        [DataMember]
        public bool IsBodyHTML { get; set; }

        [DataMember]
        public string Password { get; set; }

        #endregion Properties
    }
}