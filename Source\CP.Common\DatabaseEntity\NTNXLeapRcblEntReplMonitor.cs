﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "NTNXLeapRcblEntReplMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class NTNXLeapRcblEntReplMonitor : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string PRMostRecentLocalRecoveryPoint { get; set; }

        [DataMember]
        public string DRMostRecentLocalRecoveryPoint { get; set; }

        [DataMember]
        public string PROldestLocalRecoveryPoint { get; set; }

        [DataMember]
        public string DROldestLocalRecoveryPoint { get; set; }

        [DataMember]
        public string PRNumberOfLocalRecoveryPoints { get; set; }

        [DataMember]
        public string DRNumberOfLocalRecoveryPoints { get; set; }

        [DataMember]
        public string DataLagPerEntity { get; set; }

        [DataMember]
        public string DataLagMAx { get; set; }

        [DataMember]
        public DateTime CreateDate { get; set; }

        [DataMember]
        public DateTime UpdateDate { get; set; }


        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }
        #endregion Properties
    }
}
