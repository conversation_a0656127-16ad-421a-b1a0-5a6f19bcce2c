﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="BIAFunctionsReport.aspx.cs" Inherits="CP.UI.Admin.BIAFunctionsReport"
    Title="Continuity Patrol :: Functions Business Report Impact Analysis " %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript">
        
        function openRadWindow(Url) {
            window.radopen(Url, "TelRadWindow");
        }
      

        function pageLoad() {
            $('#businessimactdiv').slimScroll({
                height: '125px'
            });

        }
      
    </script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <telerik:RadWindowManager ID="RadWindowManager1" runat="server" IconUrl="~/Images/icons/alerts/sign_add.png">
        <Windows>
            <telerik:RadWindow ID="TelRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="520"
                Width="600" Skin="Metro" />
        </Windows>
    </telerik:RadWindowManager>
    <div class="innerLR">
        <h3>Business Functions Impact Analysis</h3>        
        <asp:UpdatePanel ID="udpmain" runat="server">
            <Triggers>
                <asp:AsyncPostBackTrigger ControlID="btnShowDependency" />
                <asp:AsyncPostBackTrigger ControlID="btnViewDependency" />
            </Triggers>
            <ContentTemplate>
                <div class="widget">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal">
                                <div class="form-group margin-bottom-none">
                                    <div class="col-md-4 padding-none-LR">
                                        <label class="col-md-4 control-label">What-If Analysis</label>
                                        <div class="col-md-8 padding-none-LR">
                                            <asp:DropDownList ID="ddlbusinessfunction" runat="server" CssClass="selectpicker col-md-12" data-style="btn-default">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <label class="col-md-1 control-label">Impact</label>
                                        <div class="col-md-3 padding-none-LR">
                                            <asp:DropDownList ID="ddlImactType" runat="server" CssClass="selectpicker col-md-12" data-style="btn-default">
                                            </asp:DropDownList>
                                        </div>
                                        <label class="col-md-1 control-label">RTO</label>
                                        <div class="col-md-3 padding-none-LR">
                                            <asp:DropDownList ID="ddlRTO" runat="server" CssClass="selectpicker col-md-12" data-style="btn-default">
                                            </asp:DropDownList>
                                        </div>
                                        <div class="text-right">
                                            <asp:Button ID="btnShowDependency" runat="server" Text="Show Impact" OnClick="btnShowDependency_Click"
                                                CssClass=" btn btn-primary" />
                                            <asp:Button ID="btnViewDependency" runat="server" Text="View Dependency" OnClick="btnViewDependency_Click"
                                                CssClass=" btn btn-primary" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">

                    <div class="col-md-8">
                        <div class="widget widget-scroll" data-scroll-height="125px">
                            <div class="widget-head">
                                <h4 class=" heading">Business Function Impact</h4>
                            </div>
                            <div class="widget-body">
                                <asp:ListView ID="rptImpactDetails" runat="server">
                                    <LayoutTemplate>
                                        <table class="table table-bordered table-striped table-white margin-bottom-none">
                                            <thead>
                                                <tr>
                                                    <th style="width: 41.6667%">Business Function Name </th>
                                                    <th style="width: 33.3333%">Impact</th>
                                                    <th>RTO</th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="scroll" data-scroll-height="125px" id="businessimactdiv">
                                            <table class="table table-bordered table-striped table-white">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td style="width: 41.6667%">
                                                <asp:Label ID="Label2" runat="server"
                                                    Text='<%#Eval("ApplicationName").ToString()%>'></asp:Label></td>
                                            <td style="width: 33.3333%">
                                                <asp:Label ID="Label3" runat="server" CssClass='<%#SetImpactColor(Eval("ImpactSummary"))%>'
                                                    Text='<%#Eval("ImpactSummary").ToString() %>'></asp:Label></td>
                                            <td>
                                                <span class="icon-Time"></span>
                                                <asp:Label ID="Label4" runat="server"
                                                    Text='<%#Eval("RTO").ToString() %>'></asp:Label></td>

                                        </tr>

                                       
                                    </ItemTemplate>
                                </asp:ListView>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="relativeWrap">
                            <div class="widget-stats widget-stats-2 widget-stats-gray widget-stats-easy-pie" style="height: 220px;">
                                <h3>Total Financial Impact</h3>

                                <div class="informer">
                                    <a href="../Report/Financial_ImpactRPT.aspx" class="informera text-center">
                                        <span class="fa fa-dollar text-primary text-large"></span>
                                        <asp:Label ID="lblCountOfImpact" runat="server" Text="" CssClass="text-large"></asp:Label>
                                    </a>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
                <div class="relativeWrap">
                    <div class="widget">
                        <div class="widget-head">
                            <h4 class=" heading">Financial Impact by Category</h4>
                        </div>
                        <div class="widget-body text-center">
                            <div class="informer">
                                <span class="informera">
                                    <asp:Label ID="lblHeading1" runat="server" Text="" CssClass="text"></asp:Label>
                                </span>
                                <span class="caption">
                                    <span class="fa fa-dollar text-larger"></span>
                                    <asp:Label ID="lblPenalties" runat="server" Text="" Font-Size="21px"></asp:Label>
                                </span>
                            </div>
                            <div class="informer">
                                <span class="informera">
                                    <asp:Label ID="lblHeading2" runat="server" Text="" CssClass="text"></asp:Label>
                                </span>
                                <span class="caption">
                                    <span class="fa fa-dollar text-larger"></span>
                                    <asp:Label ID="lblProductLoss" runat="server" Text="" Font-Size="21px"></asp:Label>
                                </span>
                            </div>
                            <div class="informer">
                                <span class="informera">

                                    <asp:Label ID="lblHeading3" runat="server" Text="" CssClass="text"></asp:Label>
                                </span>
                                <span class="caption">
                                    <span class="fa fa-dollar text-larger"></span>
                                    <asp:Label ID="lblContractualLiability" runat="server" Text="" Font-Size="21px"></asp:Label>
                                </span>
                            </div>
                            <div class="informer">
                                <span class="informera">

                                    <asp:Label ID="lblHeading4" runat="server" Text="" CssClass="text"></asp:Label>
                                </span>
                                <span class="caption">
                                    <span class="fa fa-dollar text-larger"></span>
                                    <asp:Label ID="lblEmployeeMorale" runat="server" Text="" Font-Size="21px"></asp:Label>
                                </span>
                            </div>
                            <div class="informer">
                                <span class="informera">

                                    <asp:Label ID="lblHeading5" runat="server" Text="" CssClass="text"></asp:Label>
                                </span>
                                <span class="caption">
                                    <span class="fa fa-dollar text-larger"></span>
                                    <asp:Label ID="lblServiceAvailability" runat="server" Text="" Font-Size="21px"></asp:Label>
                                </span>
                            </div>
                            <div class="informer">
                                <span class="informera">
                                    <asp:Label ID="lblHeading6" runat="server" Text="" CssClass="text"></asp:Label>
                                </span>
                                <span class="caption">
                                    <span class="fa fa-dollar text-larger"></span>
                                    <asp:Label ID="lblVendorRelation" runat="server" Text="" Font-Size="21px"></asp:Label>
                                </span>
                            </div>
                            <div class="informer">
                                <span class="informera">
                                    <asp:Label ID="lblHeading7" runat="server" Text="" CssClass="text"></asp:Label>
                                </span>
                                <span class="caption">
                                    <span class="fa fa-dollar text-larger"></span>
                                    <asp:Label ID="lblBrandvalue" runat="server" Text="" Font-Size="21px"></asp:Label>
                                </span>
                            </div>
                            <div class="informer">
                                <span class="informera">

                                    <asp:Label ID="lblHeading8" runat="server" Text="" CssClass="text"></asp:Label>
                                </span>
                                <span class="caption">
                                    <span class="fa fa-dollar text-larger"></span>
                                    <asp:Label ID="lblLegalandRegularity" runat="server" Text="" Font-Size="21px"></asp:Label>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="relativeWrap">
                    <div class="widget">
                        <div class="widget-head">
                            <h4 class=" heading">Financial Impact Summary</h4>
                        </div>
                        <div class="widget-body">
                            <div class="row">
                                <div class="col-md-6 text-center">
                                    <label class="text-uppercase">
                                        Financial Impact Distribution (24Hrs)</label>
                                    <telerik:RadHtmlChart runat="server" ID="RadChartFinancialImpDistribution" Width="500px" Height="400px"
                                        Transitions="true">
                                        <Appearance>
                                            <FillStyle BackgroundColor="White"></FillStyle>
                                        </Appearance>
                                        <ChartTitle>
                                            <Appearance Align="Center" BackgroundColor="White" Position="Top">
                                            </Appearance>
                                        </ChartTitle>
                                        <Legend>
                                            <Appearance BackgroundColor="White" Position="Bottom" Visible="true">
                                            </Appearance>
                                        </Legend>
                                        <PlotArea>
                                            <Appearance>
                                                <FillStyle BackgroundColor="White"></FillStyle>
                                            </Appearance>
                                            <Series>
                                                <telerik:PieSeries>
                                                    <LabelsAppearance Position="Center" DataFormatString="{0} %">
                                                    </LabelsAppearance>
                                                    <TooltipsAppearance DataFormatString="{0} %">
                                                    </TooltipsAppearance>
                                                    <SeriesItems>
                                                    </SeriesItems>
                                                </telerik:PieSeries>
                                            </Series>
                                        </PlotArea>
                                    </telerik:RadHtmlChart>
                                </div>
                                <div class="col-md-6 text-center">
                                    <label class="text-uppercase ">
                                        Impact Type Distribution In <span class="fa fa-dollar text-regular"></span>
                                    </label>
                                    <telerik:RadHtmlChart runat="server" ID="RadChartImpTypeDistribution" Width="500px" Height="400px"
                                        Visible="true" Transitions="True">
                                        <PlotArea>
                                            <Series>
                                            </Series>
                                            <YAxis>
                                                <TitleAppearance Text="" />
                                                <MinorGridLines Visible="false" />
                                                <MajorGridLines Visible="false" />
                                            </YAxis>
                                            <XAxis>
                                                <MajorGridLines Visible="false" />
                                                <MinorGridLines Visible="false" />
                                            </XAxis>
                                        </PlotArea>
                                        <ChartTitle>
                                        </ChartTitle>
                                        <Legend>
                                            <Appearance Position="Bottom" />
                                        </Legend>
                                    </telerik:RadHtmlChart>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
                <div class="relativeWrap">
                    <div class="widget">
                        <div class="widget-head">
                            <h4 class=" heading">Application Impact Summary</h4>
                        </div>
                        <div class="widget-body">
                            <div class="row">
                                <div class="col-md-6 text-center">
                                    <label class="text-uppercase ">
                                        Impact Analysis In <span class="fa fa-dollar text-regular"></span>
                                    </label>
                                    <telerik:RadHtmlChart runat="server" ID="RadChartImpactAnalysis" Width="500px" Height="400px">
                                        <ChartTitle Text="">
                                            <Appearance Align="left" Position="Top">
                                            </Appearance>
                                        </ChartTitle>
                                        <Legend>
                                            <Appearance Position="bottom" Visible="true">
                                            </Appearance>
                                        </Legend>
                                        <PlotArea>
                                            <Appearance>
                                                <FillStyle></FillStyle>
                                            </Appearance>
                                            <Series>
                                                <telerik:PieSeries>
                                                    <LabelsAppearance Position="Center" DataFormatString="{0} %">
                                                    </LabelsAppearance>
                                                    <TooltipsAppearance DataFormatString="{0} %">
                                                    </TooltipsAppearance>
                                                    <SeriesItems>
                                                    </SeriesItems>
                                                </telerik:PieSeries>
                                            </Series>
                                        </PlotArea>
                                    </telerik:RadHtmlChart>
                                </div>
                                <div class="col-md-6 text-center">
                                    <label class="text-center text-uppercase">
                                        Financial Cost in <span class="fa fa-dollar text-regular"></span>
                                    </label>
                                    <telerik:RadHtmlChart runat="server" ID="RadChartFinancialCost" Width="500px" Height="400px"
                                        Visible="true" Align="bottom" Legend-Appearance-Position="Bottom">
                                        <PlotArea>
                                            <Series>
                                            </Series>
                                            <XAxis>
                                                <MinorGridLines Visible="false" />
                                                <MajorGridLines Visible="false" />
                                            </XAxis>
                                            <YAxis>
                                                <MinorGridLines Visible="false" />
                                                <MajorGridLines Visible="false" />
                                            </YAxis>
                                        </PlotArea>
                                    </telerik:RadHtmlChart>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
