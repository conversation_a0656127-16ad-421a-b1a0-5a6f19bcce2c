﻿using System;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Web;
using System.Web.Security;
using System.Collections.Generic;
using System.Linq;
using CP.Common.DatabaseEntity;
using System.Net;
using System.Globalization;
using log4net;

namespace CP.UI
{
    public partial class SiteConfiguration : SiteBasePageEditor
    {

        public static string IPAddress = string.Empty;

        private readonly ILog _logger = LogManager.GetLogger(typeof(SiteConfiguration));

        #region Properties

        public override string MessageInitials
        {
            get { return "Site"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Site.SiteList;
                }
                return string.Empty;
            }
        }

        #endregion Properties

        #region Method

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

            if (IsUserOperator || IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            if (IsUserCustom)
            {
                IList<AccessManagerCustom> lstAccess = Facade.GetAccessManagerByUserId(LoggedInUserId);
                if (lstAccess != null)
                {
                    var ObjAccess = lstAccess.FirstOrDefault(x => x.AccessMenuType == AccessManagerType.Configuration.ToString()
                               || (x.AccessMenuType == AccessManagerType.View.ToString() && x.AccessSubMenuType == 4));
                    if (ObjAccess == null)
                    {
                        Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                        return;
                    }
                }
                else
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }

            }
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

            txtName.Attributes.Add("onblur", "ValidatorValidate(" + rfvName.ClientID + ")");
            txtLocation.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
            ddlCompanyName.Attributes.Add("onblur", "ValidatorValidate(" + rfvddlCompanyName.ClientID + ")");
            ddlSiteStatus.Attributes.Add("onblur", "ValidatorValidate(" + rfvddlSiteStatus.ClientID + ")");
            ddlType.Attributes.Add("onblur", "ValidatorValidate(" + rfvType.ClientID + ")");
            txtSiteIncharge.Attributes.Add("onblur", "ValidatorValidate(" + rfvSic.ClientID + ")");
            txtAddress.Attributes.Add("onblur", "ValidatorValidate(" + rqdAddress.ClientID + ")");
            txtEmail.Attributes.Add("onblur", "ValidatorValidate(" + rfvEmail.ClientID + ")");
            txtMobile.Attributes.Add("onblur", "ValidatorValidate(" + rfvMobile.ClientID + ")");

            //if (IsUserOperator || IsUserManager)
            //{
            //    Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);
            //}

            Utility.SelectMenu(Master, "Module2");

            // Utility.PopulateCompanyByCompanyIdAndRole(ddlCompanyName, LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);

            if (IsSuperAdmin)
            {
                var companylist = Facade.GetCompanyProfileByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
                ddlCompanyName.DataSource = companylist;
                ddlCompanyName.DataTextField = "Name";
                ddlCompanyName.DataValueField = "Id";
                ddlCompanyName.DataBind();
                ddlCompanyName.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectCompanyName, "000"));
            }
            else
            {

                IList<CompanyProfile> companylist = Facade.GetAllCompanyProfiles();
                IList<CompanyProfile> companylistfinal = null;
                var companylist1 = from n in companylist
                                   where n.CompanyInformation.CreatorId == Convert.ToInt32(Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"])) || Convert.ToInt32(n.CompanyInformation.CompanyId) == Convert.ToInt32(LoggedInUserCompanyId)
                                   select n;
                companylistfinal = companylist1.ToList();

                var sites = Facade.GetSiteByUserId(Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]));
                if (sites != null && sites.Count != 0)
                {
                    var ids = sites.Select(x => x.CompanyId).Distinct();
                    foreach (var c in ids)
                    {
                        foreach (var cmp in companylist)
                        {
                            if (cmp.Id == c && !companylistfinal.Any(f => f.Id == c))
                            {
                                companylistfinal.Add(cmp);
                            }
                        }
                    }
                }

                if (companylistfinal != null)
                {
                    ddlCompanyName.DataSource = companylistfinal;
                    ddlCompanyName.DataTextField = "Name";
                    ddlCompanyName.DataValueField = "Id";
                    ddlCompanyName.DataBind();
                    ddlCompanyName.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectCompanyName, "000"));
                }

            }

            EnumHelper.PopulateEnumIntoList(ddlSiteStatus, typeof(SiteStatus), " - Select Site Status - ");

            EnumHelper.PopulateEnumIntoList(ddlType, typeof(SiteType), " - Select Site Type - ");

            PrepareEditView();

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 
        }

        public override void PrepareEditView()
        {
            if (CurrentSiteId > 0)
            {
                BindControlValues();

                btnSave.Text = "Update";
            }
        }

        private void BindControlValues()
        {
            txtName.Text = CurrentSite.Name;

            txtLocation.Text = CurrentSite.Location;

            ddlCompanyName.SelectedValue = CurrentSite.CompanyId.ToString();

            ddlSiteStatus.SelectedIndex = (int)CurrentSite.Status;

            ddlType.SelectedIndex = (int)CurrentSite.Type;

            txtSiteIncharge.Text = CurrentSite.SiteInchargeInfo.InchargeName;

            txtAddress.Text = CurrentSite.SiteInchargeInfo.Address;

            txtEmail.Text = CurrentSite.SiteInchargeInfo.Email;

            if (!string.IsNullOrEmpty(CurrentSite.SiteInchargeInfo.Mobile))
            {
                if (CurrentSite.SiteInchargeInfo.Mobile.Contains("-"))
                {
                    var splitMobile = CurrentSite.SiteInchargeInfo.Mobile.Split('-');
                    txtMbCountryCode.Text = splitMobile[0];
                    txtMobile.Text = splitMobile[1];
                }
                else
                    txtMobile.Text = CurrentSite.SiteInchargeInfo.Mobile;
            }
        }

        public override void SaveEditor()
        {
            try
            {
                if (CurrentEntity.IsNew)
                {
                    CurrentEntity.CreatorId = LoggedInUserId;
                    CurrentEntity.UpdatorId = LoggedInUserId;
                    CurrentEntity.SiteInchargeInfo.CreatorId = LoggedInUserId;
                    CurrentEntity.SiteInchargeInfo.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.AddSite(CurrentEntity);
                    ActivityLogger.AddLog1(LoggedInUserName, "Site", UserActionType.CreateSite, "The Site '" + CurrentEntity.Name + "' was added to the site table", LoggedInUserId, IPAddress);
                    _logger.Info("Site created successfully " + "'" + "The Company DisplayName '" + CurrentEntity.Name + "'" + " With User IP Address " + "'" + IPAddress + "'");
                }
                else
                {
                    CurrentEntity.UpdatorId = LoggedInUserId;
                    CurrentEntity.SiteInchargeInfo.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.UpdateSite(CurrentEntity);
                    ActivityLogger.AddLog1(LoggedInUserName, "Site", UserActionType.UpdateSite, "The Site '" + CurrentEntity.Name + "' was updated to the site table", LoggedInUserId, IPAddress);
                    _logger.Info("Site Updated successfully " + "'" + "The Company DisplayName '" + CurrentEntity.Name + "'" + " With User IP Address " + "'" + IPAddress + "'");
                }
            }
            catch (CpException ex)
            {
                if (ex != null)
                {

                    _logger.Error("CP exception while Creating Updating Site in  SaveEditor method on SiteConfiguration page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        _logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                ExceptionManager.Manage(ex);
            }
        }

        public override void BuildEntities()
        {
            CurrentEntity.Name = txtName.Text;
            CurrentEntity.Location = txtLocation.Text;
            CurrentEntity.CompanyId = Convert.ToInt32(ddlCompanyName.SelectedValue);
            CurrentEntity.Status = (SiteStatus)ddlSiteStatus.SelectedIndex;
            CurrentEntity.Type = (SiteType)ddlType.SelectedIndex;

            CurrentEntity.SiteInchargeInfo.InchargeName = txtSiteIncharge.Text;
            CurrentEntity.SiteInchargeInfo.Address = txtAddress.Text;
            CurrentEntity.SiteInchargeInfo.Email = txtEmail.Text;
            CurrentEntity.SiteInchargeInfo.Mobile = txtMbCountryCode.Text + "-" + txtMobile.Text;
            CurrentEntity.SiteInchargeInfo.IsActive = 1;
        }

        private bool CheckSiteNameExist()
        {
            return Facade.IsExistSiteByName(txtName.Text);
        }

        #endregion Method

        #region Event

        protected void BtnSaveClick(object sender, EventArgs e)
        {
            if (Page.IsValid && (ViewState["_token"] != null) && Page.IsValid && !CheckSiteNameExist1() && ValidateRequest("Site", UserActionType.CreateSite))
            {


                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }

                else
                {
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }
                    var submitButton = (Button)sender;
                    string buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }

                    try
                    {
                        if (currentTransactionType != TransactionType.Undefined)
                        {
                            BuildEntities();
                            StartTransaction();
                            SaveEditor();
                            EndTransaction();
                        }
                        string message = MessageInitials + " " + '"' + CurrentEntity.Name + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                currentTransactionType));
                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, this);
                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, this);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, this);
                        }
                    }
                    if (returnUrl.IsNotNullOrEmpty())
                    {

                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.SiteId, CurrentEntity.Id);

                        Helper.Url.Redirect(returnUrl);
                    }

                    //if (returnUrl.IsNotNullOrEmpty())
                    //{
                    //    Helper.Url.Redirect(new SecureUrl(returnUrl));
                    //}
                }
            }
        }

        //protected void BtnSaveClick(object sender, EventArgs e)
        //{
        //    if (Page.IsValid && ValidateRequest("Site",UserActionType.CreateSite) )
        //    {
        //        string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
        //        if (returnUrl.IsNullOrEmpty())
        //        {
        //            returnUrl = ReturnUrl;
        //        }
        //        var submitButton = (Button)sender;
        //        string buttionText = " " + submitButton.Text.ToLower() + " ";
        //        var currentTransactionType = TransactionType.Undefined;
        //        if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
        //        {
        //            currentTransactionType = TransactionType.Save;
        //        }
        //        else if (buttionText.Contains(" update "))
        //        {
        //            currentTransactionType = TransactionType.Update;
        //        }

        //        try
        //        {
        //            if (currentTransactionType != TransactionType.Undefined && !CheckSiteNameExist())
        //            {
        //                BuildEntities();
        //                StartTransaction();
        //                SaveEditor();
        //                EndTransaction();
        //            }
        //            string message = MessageInitials + " " + '"' + CurrentEntity.Name + '"';
        //            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
        //                                                                                    currentTransactionType));
        //        }
        //        catch (CpException ex)
        //        {
        //            InvalidateTransaction();

        //            returnUrl = Request.RawUrl;

        //            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

        //            ExceptionManager.Manage(ex, this);
        //        }
        //        catch (Exception ex)
        //        {
        //            InvalidateTransaction();

        //            returnUrl = Request.RawUrl;

        //            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

        //            if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
        //            {
        //                ExceptionManager.Manage((CpException)ex.InnerException, this);
        //            }
        //            else
        //            {
        //                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

        //                ExceptionManager.Manage(customEx, this);
        //            }
        //        }
        //        if (returnUrl.IsNotNullOrEmpty())
        //        {
        //            Helper.Url.Redirect(new SecureUrl(returnUrl));
        //        }
        //    }
        //}

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Site.SiteList + "?Listitems=" + "Cancel");
        }

        protected void TxtNameTextChanged(object sender, EventArgs e)
        {
            if (txtName.Text != string.Empty)
            {
                lblSiteName.Text = CheckSiteNameExist() ? "Site Name is Not Avaliable" : "";
                if (lblSiteName.Text != string.Empty)
                {
                    txtLocation.Focus();
                    btnSave.Enabled = false;
                }
                else
                {
                    txtLocation.Focus();
                    btnSave.Enabled = true;
                    btnSave.CssClass = "btn btn-primary";
                }
            }
        }

        private bool CheckSiteNameExist1()
        {
            if (CurrentEntity.Id > 0)
            {
                if (txtName.Text.Equals(CurrentEntity.Name))
                {
                    return false;
                }
            }

            return Facade.IsExistSiteByName(txtName.Text);
        }

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                IgnoreIDs.Add("txtMbCountryCode");
                IgnoreIDs.Add("txtAddress");
                IgnoreIDs.Add("txtEmail");
                // IgnoreIDs.RemoveAll(x=>System.Text.RegularExpressions.Regex.IsMatch(, "[=+-<>]");)
                var list = allTextBoxesOnThePage.Where(item => !(IgnoreIDs.Contains(item.ID)) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId, IPAddress);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId, IPAddress);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((ViewState["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}

        #endregion Event
    }
}