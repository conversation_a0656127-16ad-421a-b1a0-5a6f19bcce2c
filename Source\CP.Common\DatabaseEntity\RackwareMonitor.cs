﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RackwareMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
  public class RackwareMonitor : BaseEntity
    {

        #region Properties

        [DataMember]
        public int id { get; set; }
        [DataMember]
        public int InfraObjectId { get; set; }

        //[DataMember]
        //public string RMMServerIPAddress { get; set; }
        [DataMember]
        public string RMMServerBuildVersion { get; set; }
        [DataMember]
        public String WaveName { get; set; }
        [DataMember]
        public string WaveAutoprovision { get; set; }
        [DataMember]
        public string WaveAssociatedDRPolicyName { get; set; }
        [DataMember]
        public string WaveAssociatedDRPolicyState { get; set; }
        [DataMember]
        public string WaveStatus { get; set; }
        [DataMember]
        public string LastRepliTime_Started { get; set; }
        [DataMember]
        public string LastRepliTime_Ended { get; set; }
        [DataMember]
        public string LastRepliTime_TotalElapsed { get; set; }
        [DataMember]
        public string DataLag { get; set; }
        [DataMember]
        public string TotalMachinesinWaves { get; set; }

        [DataMember]
        public string SuccessfulWave { get; set; }
        [DataMember]
        public string FailedWave { get; set; }
        [DataMember]
        public string ActiveWave { get; set; }
        [DataMember]
        public string IdleWave { get; set; }
        [DataMember]
        public string CreateDate { get; set; }


        public string InfraObjectName
        {
            get;
            set;
        }
        public string Server
        {
            get;
            set;
        }
        #endregion Properties

    }
}
