﻿
function renderAjaxDataForIncident(inputString) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'inputString': inputString }),
        url: "AllComponentWhatIfAnalysis.aspx/NodeRelationConverterForIncidents",
        success: function (msg) {
            var value = msg.d.split('#');
            var JsonObj = eval('(' + value[0] + ')');
            showIncidentTree(JsonObj);
        }
    });
}

function showIncidentTree(JsonObj) {

    $("#divbsView").css("display", "none");
    $("#divIncView").css("display", "block");
    $('#divemptyFunctionPopup').css("display", "none");
    //$('#body').html("");
    $("[id$=Incbody]").html("");
    i = 0,
    root;
    var duration = 750;
    var viewerWidth = 920;
    var viewerHeight = 280;

    ShowBreadCrum(JsonObj.name);


    function zoom() {
        vis.attr("transform", "translate(" + d3.event.translate + ")scale(" + d3.event.scale + ")");
    }

 
    function resetZoom() {
        d3.select("[id$=Incbody]").select("svg").select("g")
       .transition().duration(750)
       .attr("transform", "translate(150,30) scale(0.85)");
    };
    d3.select("#btnReset").on("click", resetZoom);
    d3.select("#btnReload").attr("style", "display:none");

    function centerNode(source) {
        scale = zoomListener.scale();
        x = -source.y0;
        y = -source.x0;
        x = 0;
        //y = y * scale + viewerHeight / 2; 
        y = 0;
        d3.select('g').transition()
        .duration(duration)
        .attr("transform", "translate(" + x + "," + y + ")scale(" + scale + ")");
        zoomListener.scale(scale);
        zoomListener.translate([x, y]);
    }


    var zoomListener = d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom);

    var tree = d3.layout.tree()
    .size([viewerHeight, viewerWidth]);

    var diagonal = d3.svg.diagonal()
    .projection(function (d) { return [d.y, d.x]; });

    var vis = d3.select("[id$=Incbody]").append("svg:svg")
    .attr("width", viewerWidth)
    .attr("height", viewerHeight)
    .call(d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom))
    .append("g")
    //zoomListener.translate([100, 50]).scale(0.85);
    .attr("transform", "translate(150,30)scale(0.80)");

    // var root1 = { "name": "CRM1", "children": [{ "name": "UpStream", "children": [{ "name": "BF1" }, { "name": "BF2" }, { "name": "BF3" }, { "name": "BF4" }, { "name": "BF5" }, { "name": "BF6" }, { "name": "BF7" }] }, { "name": "DownStream", "children": [{ "name": "BF8" }, { "name": "BF9" }, { "name": "BF10" }, { "name": "BF11" }, { "name": "BF12" }, { "name": "BF13" }, { "name": "BF14" }, { "name": "BF15" }, { "name": "BF16" }] }, ] };


    var root = JsonObj;
    root.x0 = viewerHeight / 2;
    root.y0 = 0;

    function toggleAll(d) {
        if (d.children) {
            d.children.forEach(toggleAll);
            toggle(d);
        }
    }
   
    //root.children.forEach(toggleAll);
    update(root);
    //centerNode(root);


    function update(source) {
        var duration = d3.event && d3.event.altKey ? 5000 : 500;

       
        var nodes = tree.nodes(root).reverse();

      
        nodes.forEach(function (d) { d.y = d.depth * 160; });

       
        var node = vis.selectAll("g.node")
        .data(nodes, function (d) { return d.id || (d.id = ++i); });

       
        var nodeEnter = node.enter().append("svg:g")
        .attr("class", "node")
        .attr("transform", function (d) { return "translate(" + source.y0 + "," + source.x0 + ")"; })
        .on("click", function (d) { toggle(d); update(d); });

        nodeEnter.append("svg:circle")
        .attr("r", 1e-6)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeEnter.append("svg:text")
        .attr("x", function (d) { return d.children || d._children ? 15 : -35; })
        .attr("dy", function (d) { return d.children || d._children ? "-2.2em" : "-2.2em"; })
        .attr("text-anchor", function (d) { return d.children || d._children ? "end" : "start"; })
        .text(function (d) { return returnTextAnchor(d); })
        //.style("fill", "rgb(255,0,0)");
        .attr("class", function (d) { return returnTextAnchorClass(d); });


    
        nodeEnter.append("svg:image")
                .attr("xlink:href", function (d) { return d.logo; })
                .attr("x", function (d) { return d.children || d._children ? -20 : -20; })
                .attr("y", function (d) { return d.children || d._children ? "-1.9em" : "-1.9em"; })
                .attr("height", function (d) { return d.logoheight || 42; })
                .attr("width", function (d) { return d.logowidth || 42; });

   
        var nodeUpdate = node.transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + d.y + "," + d.x + ")"; });

        nodeUpdate.select("circle")
        .attr("r", 4.5)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeUpdate.select("text")
        .style("fill-opacity", 1);

      
        var nodeExit = node.exit().transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + source.y + "," + source.x + ")"; })
        .remove();

        nodeExit.select("circle")
        .attr("r", 1e-6);

        nodeExit.select("text")
        .style("fill-opacity", 1e-6);

      
        var link = vis.selectAll("path.link")
        .data(tree.links(nodes), function (d) { return d.target.id; });


        link.enter().insert("svg:path", "g")
        .attr("class", "link")
        .style("stroke", function (d) { return d.target.level; })
        .attr("d", function (d) {
            var o = { x: source.x0, y: source.y0 };
            return diagonal({ source: o, target: o });
        })
        .transition()
        .duration(duration)
        .attr("d", diagonal);

       
        link.transition()
        .duration(duration)
        .attr("d", diagonal);

       
        link.exit().transition()
        .duration(duration)
        .attr("d", function (d) {
            var o = { x: source.x, y: source.y };
            return diagonal({ source: o, target: o });
        })
        .remove();

       
        nodes.forEach(function (d) {
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }

  
    function toggle(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
    }
}


function ShowBreadCrum(breadCrum) {
    $("#spnBreadCrum").text(breadCrum);
}


function returnTextAnchor(d) {

    var textAnchor = null;

    if (d.ImpactType == "None") {
        textAnchor = d.name;
    }
    else if (d.ImpactType == "ündefined") {
        textAnchor = d.name;
    }
    else {
        textAnchor = d.name + "(" + d.ImpactType + ")";

    }
    return textAnchor;
}


function returnTextAnchorClass(d) {

    var textAnchorClass = null;

    if (d.ImpactType == "None") {
        textAnchorClass = "impact-text-black-bold";
    }
    else if (d.ImpactType == "ündefined") {
        textAnchorClass = "impact-text-black-bold";
    }

    else if (d.ImpactType == "Total Impact") {
        textAnchorClass = "impact-text-red";
    }
    else if (d.ImpactType == "Major Impact") {
        textAnchorClass = "impact-text-orange";
    }
    else if (d.ImpactType == "Partial Impact") {
        textAnchorClass = "impact-text-yellow";
    }
    else {
        textAnchorClass = "impact-text-black-bold";
    }

    return textAnchorClass;
}

$(".scroll-pane ").mCustomScrollbar({
    axis: "y",
    setHeight: "359px",
    advanced: {
        updateOnContentResize: true,
        autoExpandHorizontalScroll: true
    }
});
function pageLoad() {
   
    $(".scroll-pane ").mCustomScrollbar({
        axis: "y",
        setHeight: "359px",
        advanced: {
            updateOnContentResize: true,
            autoExpandHorizontalScroll: true
        }

    });
};
