﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ServicesMonitorOverview.aspx.cs" Inherits="CP.UI.Admin.ServicesMonitorOverview" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style type="text/css">
       
       
        </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">


    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <link href="../App_Themes/CPTheme/lc_switch.css" rel="stylesheet" />
    <script src="../Script/d3.min.js"></script>
    <%--  <script src="../Script/ServiceTreeDesignProcessMoniter.js"></script>--%>
    <script src="../Script/lc_switchOverview.js"></script>
    <script src="../Script/jquery-ui.js"></script>
    <script src="../Script/ServicesMonitorOverview.js"></script>
    <%--<script src="../Script/ServiceMonitor.js" type="text/javascript"></script>--%>
    <style type="text/css">
        .dspnone {
            display: none;
        }

        .activeService {
            border-left: 4px solid #4a8bc2 !important;
        }

        .accordian-design #accordion {
            padding-left: 0px;
            margin-bottom: 0px;
            min-height: 477px;
            background-color: #f5f5f5;
            /*border-left:1px solid #ddd;*/
            /*border-right:1px solid #ddd;*/
        }

            .accordian-design #accordion h3 {
                line-height: 32px;
                height: 40px;
                cursor: pointer;
                border-radius: 0px;
                background-color: transparent;
                padding-left: 0px;
                border-bottom: 1px solid #ddd;
                border-right: none;
                border-left: none;
                font-size: 15px;
                margin-top: 0px;
            }

        .accordian-design .ui-accordion .ui-accordion-content {
            border-radius: 0px;
        }

        .accordian-design #accordion h3 > a {
            text-decoration: none;
            overflow: hidden;
            color: #4a8bc2;
        }

        .ui-widget-content {
            border: none;
            border-bottom: 1px solid #ddd;
        }

        .accordian-design .cio-profile-icon {
            margin: 4px 5px 0 10px;
        }

        .accordian-design .ui-accordion .ui-accordion-content {
            padding: 1em 0em 1em 1.2em;
            border-top: 0;
            overflow: auto;
            border-radius: 0px;
            background-color: #ddd;
            height: auto !important;
        }



        .accordian-design .ui-accordion-content a {
            color: #4a8bc2;
            font-size: 13px;
            margin-bottom: 5px;
            display: inline-block;
            text-overflow: ellipsis;
            padding-right: 5px;
            white-space: nowrap;
            width: 100%;
            cursor: pointer;
        }

        .accordian-design .ui-state-active {
            border-right: none !important;
        }

        .accordian-design .subservice {
            padding: 0px;
            margin: 0px;
        }

            .accordian-design .subservice li {
                margin-bottom: 12px;
            }

        .subservice li span {
            margin-top: 0px;
        }

        .subservice li i {
            margin: 1px 5px 0 10px !important;
        }

        #inner-content-div {
            border-bottom: none;
        }

        h3 span.rfl {
            font-style: normal;
            font-size: 100%;
            display: inline-block;
            text-overflow: ellipsis;
            padding-right: 5px;
            white-space: nowrap;
            width: 75%;
            color: #4a8bc2;
        }

        .subservice li a i + span {
            width: 74%;
            display: inline-block;
            overflow-x: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: top;
        }

        .slimScrollDiv {
            height: 0px !important;
        }

        .numpop {
            background-color: rgb(229, 105, 1);
            border-radius: 50%;
            color: rgb(255, 255, 255);
            font-size: 11px;
            margin-bottom: 0;
            margin-left: 7px;
            padding: 0 5px;
            position: absolute;
            right: 0;
            top: -14px;
        }

        .orange-incidentalert {
            background-color: #E56901;
        }

        .red-incidentalert {
            background-color: #FF0104;
        }

        .yellow-incidentalert {
            background-color: #EAC200;
            color: #000;
        }

        .service-icon {
            background-image: url("../Images/ServiceLogo/all-services-blue_icon.png");
            background-repeat: no-repeat;
            display: inline-block;
            height: 13px;
            margin: 10px 5px 0 10px;
            vertical-align: top;
            width: 16px;
        }

        /*.ui-accordion-header-active*/
        .activeService {
            border-left: 4px solid #4a8bc2 !important;
        }

        .notifyscrolltable td span {display:inline-block;}
        .notifyscrolltable td a{color:rgb(51, 51, 51);text-decoration:none;cursor:auto;}
        .ui-accordion-header-icon {
            display:none;
        }
    </style>


    <div class="innerLR">
        <h3 style="display: inline-block;">
            <img src="../Images/ServiceLogo/process-designer_icon.png">
            Business Service </h3>

        <div class="totalservice">
            <%-- <asp:UpdatePanel ID="UpdateServiceCount" runat="server" UpdateMode="Conditional">
                <ContentTemplate>--%>

            <%-- <asp:Label ID="labelservices" runat="server" Text=""></asp:Label>--%>
            <%-- <span id="lblservice"></span>--%>
            <%--                </ContentTemplate>
            </asp:UpdatePanel>--%>
        </div>

        <!--Form start here -->
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body padding-none">
                <div class="row" style="margin-left: 0px ! important; margin-right: 0px ! important;">
                    <div class="col-md-12 form-horizontal uniformjs padding-none-LR">

                        <div class="col-md-12 padding-none">
                            <asp:UpdatePanel ID="upchkAutoRefresh" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <div class="col-md-3 padding-none accordian-design" style="width: 19.6%;">
                                        <div class="cioselect" style="background-color: #4a8bc2; padding: 5px; display: none;">
                                            <asp:DropDownList ID="ddlServiceProfile" runat="server" CssClass="selectpicker" data-style="btn-default"
                                                AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                        <div id="accordion">
                                            <%--load Service Tree On left side--%>
                                        </div>
                                    </div>
                                    <div class="col-md-9 padding-none" style="width: 80.4%;">
                                        <div class="service_tab">
                                            <ul id="tabs" style="float: left;">
                                                <!-- Tabs go here -->
                                                <%-- <li><a class="tab";  id="default" href="#">CRM</a>&nbsp;<a href='#' class='remove'>x</a></li>--%>
                                            </ul>

                                            <div class="auto-refresh">
                                                <asp:CheckBox ID="chkbxAutoRefresh" runat="server" CssClass="lcs_check" autocomplete="off" Enabled="true" AutoPostBack="true" />

                                                <%--<input type="checkbox" name="check-3" value="6" class="lcs_check lcs_tt1" checked="checked" autocomplete="off" />--%>
                                     &nbsp;Auto Refresh Every&nbsp;
                                                <asp:DropDownList ID="timeInter1" runat="server" onchange="getrefreshval(this);" Enabled="false">
                                                    <asp:ListItem Text="1" Value="120000"></asp:ListItem>
                                                    <asp:ListItem Text="2" Value="180000"></asp:ListItem>
                                                    <asp:ListItem Text="3" Value="240000"></asp:ListItem>
                                                    <asp:ListItem Text="4" Value="300000"></asp:ListItem>
                                                    <asp:ListItem Text="5" Value="300000"></asp:ListItem>
                                                </asp:DropDownList>

                                                &nbsp;Min
                                            </div>
                                        </div>


                                        <div class="outerservicedivnew" style="height: 477px;">
                                            <div class="outerservicediv">
                                            </div>
                                        </div>

                                        <asp:UpdatePanel ID="updmain" runat="server" UpdateMode="Conditional">
                                            <ContentTemplate>
                                                <asp:HiddenField runat="server" ID="hdnActiveServiceId" Value="" />
                                                <asp:HiddenField runat="server" ID="hdnSrId" Value="" />
                                                <div id="inner-content-div">
                                                    <!-- Business Service Repeater  -->
                                                <%--    <asp:Timer ID="Timer1" runat="server" OnTick="GetData" Interval="60000" />
                                                    <asp:Button ID="Button1" runat="server" Text="Button" Style="display: none" OnClick="Button1_Click" />
                                                    <asp:Repeater runat="server" ID="rptServiceMonitor" OnItemDataBound="rptServiceMonitor_ItemDataBound">
                                                        <HeaderTemplate>
                                                        </HeaderTemplate>
                                                        <ItemTemplate>

                                                             <div id="div_service" class='<%#Eval("ActiveService") %>' onclick='<%#"CallMe("+Eval("Id")+");"  %>' style="">
                                                                    <asp:Label ID="lblId" runat="server" Text='<%#Eval("Id") %>' Visible="false"> </asp:Label>
                                                                    <asp:Label ID="lblServiceId" runat="server" Text='<%#Eval("ServiceId") %>' Visible="false"> </asp:Label>
                                                                    <asp:Label ID="lblServerName" runat="server" CssClass='<%#Eval("ImpactColor") %>' Text='<%#Eval("ServiceName") %>'> </asp:Label>
                                                                    <p>
                                                                        <i class="ImpactType_Icon"></i>
                                                                        <asp:Label ID="lblImpacttypeHeader" runat="server" Text="Impact Type "></asp:Label>
                                                                        :
                                                <asp:Label ID="lblImpacttype" runat="server" Text='<%#Eval("ImpactType") %>'></asp:Label>
                                                                    </p>
                                                                    <p>
                                                                        <i class="ImpactTime_Icon"></i>
                                                                        <asp:Label ID="lblImpactTimeHeader" runat="server" Text="Impact Time "></asp:Label>
                                                                        :
                                                <asp:Label ID="lblImpactTime" runat="server" Text='<%#Eval("ImpactTime") %>'></asp:Label>
                                                                    </p>
                                                                    <p>
                                                                        <i class="ImpactDownTime_Icon"></i>
                                                                        <asp:Label ID="lblImpactDownTimeHeader" runat="server" Text="Impact Down Time "></asp:Label>
                                                                        :
                                                <asp:Label ID="lblImpactDownTime" runat="server" Text='<%#Eval("ImpactDownTime") %>' ToolTip="Days.Hours:Minutes:Seconds"></asp:Label>
                                                                    </p>

                                                                    <div class="servicebtn">
                                                                        <ul>
                                                                            <li></li>
                                                                            <li></li>
                                                                            <li></li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                        </ItemTemplate>
                                                        <FooterTemplate>
                                                        </FooterTemplate>
                                                    </asp:Repeater>--%>
                                                </div>
                                            </ContentTemplate>
                                        </asp:UpdatePanel>
                                    </div>
                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>
                            <!-- Business Service tree-->


                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="divServiceModal" class="modal" style="display: none;"></div>
        <div class="modal fade" id="myModal" role="dialog">
            <div class="modal-dialog">

                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">
                            &times;     
                            <h4 class="modal-title">Modal Header</h4>
                    </div>
                    <div class="modal-body">
                        <p>Some text in the modal.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>

            </div>
        </div>

        
          

    </div>    

    <script type="text/javascript">





        $(document).ready(function () {
            // $("#accordion").accordion();

            // setInterval(function () { $('.numpop').each(function () { $(this).css('visibility', $(this).css('visibility') === 'hidden' ? '' : 'hidden'); $(this).parent().addClass("cursor-pointer"); }); }, 1100);

            $("#sm-content-div, ").mCustomScrollbar({
                axis: "y",
            });
            $(".service-diagram").mCustomScrollbar({
                axis: "y",
            });
            //$(".outerservicediv").mCustomScrollbar({
            //    axis: "y",
            //});
            $('#ctl00_cphBody_chkbxAutoRefresh').lc_switch();
        });

        function pageLoad() {
            $(function () {
                $("[data-toggle='tooltip']").tooltip();
            });
            $("#sm-content-div").mCustomScrollbar({
                axis: "y",
            });
            //$(".outerservicediv").mCustomScrollbar({
            //    axis: "y",
            //});
            $(".service-diagram").mCustomScrollbar({
                axis: "y",
            });

            $('#ctl00_cphBody_chkbxAutoRefresh').lc_switch();
            $('.lcs_switch').click(function () {
                if ($(this).hasClass('lcs_on')) {
                    $("[id$='timeInter1']").prop('disabled', true);
                    $(".lcs_cursor").attr("title", "Off");
                }
                else if ($(this).hasClass('lcs_off')) {
                    $("[id$='timeInter1']").prop('disabled', false);
                    $(".lcs_cursor").attr("title", "On");
                }
            })
        }

        function CallMe(Id) {
            $('.lcs_switch').removeClass('lcs_on').addClass('lcs_off');
            $("[id$='timeInter1']").prop('disabled', true);
            $("#ctl00_cphBody_hdnActiveServiceId").val(Id);
            $("#ctl00_cphBody_chkbxAutoRefresh").prop("checked", false);
            $("#ctl00_cphBody_Button1").click();

        }
        function OnSuccess(response, userContext, methodName) {
            alert(response);
        }

        function UpdateTime(ActiveId) {

            $("#ctl00_cphBody_hdnActiveServiceId").val(ActiveId);
        }



        function ShowServiceProfile() {

            RenderServiceProfile();
        }

        function OnClickProfile(Id) {

            renderProfile(Id);

        }


        function serviceAlertPopup(ProfileId, ServiceId) {

            CallToServicepopup(ProfileId, ServiceId);
        }

        function OnClickProfileService(ServiceId, ProfileId) {

            LoadServiceFlow(ServiceId, ProfileId);

        }




    </script>
  <div class='loadingmessage' style='display:none'>
       <div id="imgLoading1" class="loading-mask">
                <span>Loading...</span>
            </div>
</div>

</asp:Content>
