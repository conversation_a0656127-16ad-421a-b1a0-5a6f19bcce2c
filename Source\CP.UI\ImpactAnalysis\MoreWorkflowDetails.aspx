﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="MoreWorkflowDetails.aspx.cs" Inherits="CP.UI.ImpactAnalysis.MoreWorkflowDetails" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title runat="server" id="usefortitle"></title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Analytics.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Analytics2.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/modernizr.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/ie.prototype.polyfill.js"></script>
    <script src="../Script/html5shiv.js"></script>
    <script type="text/javascript" src="../Script/jquery-ui.min.js "></script>
</head>
<body>
    <form id="form1" runat="server">
        <telerik:RadScriptManager runat="server" ID="RadScriptManager1" />
        <div class="innerLR">

            <asp:Panel ID="pnl_1_BIAWorkflowfailureDetailsPFwise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="upPnlBIAWorkflowfailureDetailsPFwise" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Workflow Profile</label>
                                            <asp:DropDownList ID="ddlWorkflowProfile" CssClass="col-md-8" runat="server" OnSelectedIndexChanged="ddlWorkflowProfile_SelectedIndexChanged" AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAWorkflowfailureDetailsPFwise" runat="server" OnPagePropertiesChanged="lvBIAWorkflowfailureDetailsPFwise_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Workflow Name
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Fail
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">Success
                                                            </th>
                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("WorkFlowName") %>' ToolTip='<%# Eval("WorkFlowName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("Totalworkflow") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Aborted") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("CompletedWithSuccess") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager5" runat="server" PagedControlID="lvBIAWorkflowfailureDetailsPFwise">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager6" runat="server" PagedControlID="lvBIAWorkflowfailureDetailsPFwise" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_1_More_BIAWorkflowfailureDetailsPFwise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label9" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAWorkflowfailureDetailsPFwise_More" runat="server" OnPagePropertiesChanged="lvBIAWorkflowfailureDetailsPFwise_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 26%;">Profile
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 10%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 10%;">Fail
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 10%;">Success
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Total Workflow
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Fail Workflow
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">% Fail Workflow
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 26%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("TotalProfileRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("ProfileAborted") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("ProfileSuccess") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="Label10" runat="server" Text='<%# Eval("TotalWorkFlow") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="Label11" runat="server" Text='<%# Eval("Fail") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="Label12" runat="server" Text='<%# Eval("FailPercentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager11" runat="server" PagedControlID="lvBIAWorkflowfailureDetailsPFwise_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager12" runat="server" PagedControlID="lvBIAWorkflowfailureDetailsPFwise_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_2_WorkflowFailuStatsByWorkType" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label22" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="upd_WorkflowFailuStatsByWorkType" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Workflow Type</label>
                                            <asp:DropDownList ID="ddlWorkflowtype" CssClass="col-md-8" runat="server" OnSelectedIndexChanged="ddlWorkflowtype_SelectedIndexChanged" AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvWorkflowFailuStatsByWorkType" runat="server" OnPagePropertiesChanged="lvWorkflowFailuStatsByWorkType_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Profile
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 15%;">Total Workflow
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Fail
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Success
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">% Fail
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblProfileName" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="Label23" CssClass="" runat="server" Text='<%# Eval("TotalProfileRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 15%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("Totalworkflow") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Fail") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Success") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="Label24" runat="server" Text='<%# Eval("FailPercentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager21" runat="server" PagedControlID="lvWorkflowFailuStatsByWorkType">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager22" runat="server" PagedControlID="lvWorkflowFailuStatsByWorkType" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_2_More_WorkflowFailuStatsByWorkType" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label25" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel6" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">
                                        <asp:ListView ID="lvWorkflowFailuStatsByWorkType_More" runat="server" OnPagePropertiesChanged="lvWorkflowFailuStatsByWorkType_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Workflow Type
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 16%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 16%;">Fail
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 16%;">Success
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 16%;">% Fail
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblProfileName" CssClass="blt_12" runat="server" Text='<%# Eval("WorkflowType") %>' ToolTip='<%# Eval("WorkflowType") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 16%;">
                                                        <asp:Label ID="Label23" CssClass="" runat="server" Text='<%# Eval("TotalWorkFlow") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 16%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Fail") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 16%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Success") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 16%;">
                                                        <asp:Label ID="Label24" runat="server" Text='<%# Eval("FailPercentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager23" runat="server" PagedControlID="lvWorkflowFailuStatsByWorkType_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager24" runat="server" PagedControlID="lvWorkflowFailuStatsByWorkType_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_3_WorkflowByHumInterventinsWithActionCount" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label1" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpnlWorkflowByHumInterventinsWithActionCount" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Workflow Profile</label>
                                            <asp:DropDownList ID="ddlProfileWorkflowByHumInterventinsWithActionCount" CssClass="col-md-8" runat="server" OnSelectedIndexChanged="ddlProfileWorkflowByHumInterventinsWithActionCount_SelectedIndexChanged" AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvWorkflowByHumInterventinsWithActionCount" runat="server" OnPagePropertiesChanged="lvWorkflowByHumInterventinsWithActionCount_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Workflow Name
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 13%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 13%;">Skip
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 13%;">Abort
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 13%;">Retry
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 13%;">Total
                                                            </th>
                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("TotalWorkflowRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Skip") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Abort") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label2" runat="server" Text='<%# Eval("Retry") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label3" runat="server" Text='<%# Eval("TotalHumenIntervention") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvWorkflowByHumInterventinsWithActionCount">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvWorkflowByHumInterventinsWithActionCount" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_3_More_WorkflowByHumInterventinsWithActionCount" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">

                                        <asp:ListView ID="lvWorkflowByHumInterventinsWithActionCount_More" runat="server" OnPagePropertiesChanged="lvWorkflowByHumInterventinsWithActionCount_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 26%;">Profile
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 10%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 11%;">Total Workflow
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 11%;">Skip Action
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 11%;">Abort Action
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 11%;">Retry Action
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 11%;">Total 
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label13" CssClass="" runat="server" Text='<%# Eval("Totalrun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("TotalWorkflowRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Skip") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Abort") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label2" runat="server" Text='<%# Eval("Retry") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label3" runat="server" Text='<%# Eval("TotalHumenIntervention") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager13" runat="server" PagedControlID="lvWorkflowByHumInterventinsWithActionCount_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager14" runat="server" PagedControlID="lvWorkflowByHumInterventinsWithActionCount_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_4_WorkflowByHumInterventinAndSuccess" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label4" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpnlWorkflowByHumInterventinAndSuccess" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Workflow Profile</label>
                                            <asp:DropDownList ID="ddlWorkflowByHumInterventinAndSuccess" CssClass="col-md-8" runat="server" OnSelectedIndexChanged="ddlWorkflowByHumInterventinAndSuccess_SelectedIndexChanged" AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvWorkflowByHumInterventinAndSuccess" runat="server" OnPagePropertiesChanged="lvWorkflowByHumInterventinAndSuccess_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Workflow Name
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 21%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Human Interventions
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 24%;">Without Human Interventions
                                                            </th>

                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 21%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("TotalWorkflowRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 20%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Abort") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 24%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Retry") %>' />
                                                    </td>

                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager3" runat="server" PagedControlID="lvWorkflowByHumInterventinAndSuccess">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager4" runat="server" PagedControlID="lvWorkflowByHumInterventinAndSuccess" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_4_More_WorkflowByHumInterventinAndSuccess" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label14" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">

                                        <asp:ListView ID="lvWorkflowByHumInterventinAndSuccess_More" runat="server" OnPagePropertiesChanged="lvWorkflowByHumInterventinAndSuccess_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Profile
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 10%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 10%;">Total Workflow
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 15%;">Human Interventions
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 15%;">Without Human Interventions
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 15%;">% Human Interventions
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 15%;">% Without Human Interventions
                                                            </th>

                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 12%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label15" CssClass="" runat="server" Text='<%# Eval("TotalRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("TotalWorkflowRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 15%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Retry") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 15%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Abort") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 15%;">
                                                        <asp:Label ID="Label16" CssClass="" runat="server" Text='<%# Eval("Totalhumenintervention") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 15%;">
                                                        <asp:Label ID="Label17" runat="server" Text='<%# Eval("Percentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager15" runat="server" PagedControlID="lvWorkflowByHumInterventinAndSuccess_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager16" runat="server" PagedControlID="lvWorkflowByHumInterventinAndSuccess_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_5_WorkflowDetailsWothinAndOutOfRTO" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label5" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="Upd_WorkflowDetailsWothinAndOutOfRTO" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Workflow Profile</label>
                                            <asp:DropDownList ID="ddlWorkflowDetailsWothinAndOutOfRTO" CssClass="col-md-8" runat="server" OnSelectedIndexChanged="ddlWorkflowDetailsWothinAndOutOfRTO_SelectedIndexChanged" AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvWorkflowDetailsWothinAndOutOfRTO" runat="server" OnPagePropertiesChanged="lvWorkflowDetailsWothinAndOutOfRTO_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Workflow Name
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 21%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Within RTO
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 24%;">Out Of RTO
                                                            </th>

                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 21%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("TotalWorkflowRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 20%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Abort") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 24%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Retry") %>' />
                                                    </td>

                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager7" runat="server" PagedControlID="lvWorkflowDetailsWothinAndOutOfRTO">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager8" runat="server" PagedControlID="lvWorkflowDetailsWothinAndOutOfRTO" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_5_More_WorkflowDetailsWothinAndOutOfRTO" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label18" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">
                                        <asp:ListView ID="lvWorkflowDetailsWothinAndOutOfRTO_More" runat="server" OnPagePropertiesChanged="lvWorkflowDetailsWothinAndOutOfRTO_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Profile
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 16%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 17%">Total Workflow
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 16%;">Within RTO
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 16%;">Out Of RTO
                                                            </th>

                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 16%;">
                                                        <asp:Label ID="Label19" CssClass="" runat="server" Text='<%# Eval("Run") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 17%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("TotalWorkflow") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 16%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("CompletedWithinRTO") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 16%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("CompletedOutOfRTO") %>' />
                                                    </td>

                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager17" runat="server" PagedControlID="lvWorkflowDetailsWothinAndOutOfRTO_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager18" runat="server" PagedControlID="lvWorkflowDetailsWothinAndOutOfRTO_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_6_wfAndactincntcomploutRTOByProfile" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label6" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="upd_wfAndactincntcomploutRTOByProfile" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Workflow Profile</label>
                                            <asp:DropDownList ID="ddlwfAndactincntcomploutRTOByProfile" CssClass="col-md-8" runat="server" OnSelectedIndexChanged="ddlwfAndactincntcomploutRTOByProfile_SelectedIndexChanged" AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvWfAndactincntcomploutRTOByProfile" runat="server" OnPagePropertiesChanged="lvWfAndactincntcomploutRTOByProfile_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 27%;">Workflow Name
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 21%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Action Completed Within RTO
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 24%;">Action Completed Out Of RTO
                                                            </th>

                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 21%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("Totalworkflow") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 20%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("ProfileSuccess") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 24%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Profileaborted") %>' />
                                                    </td>

                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager9" runat="server" PagedControlID="lvWfAndactincntcomploutRTOByProfile">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager10" runat="server" PagedControlID="lvWfAndactincntcomploutRTOByProfile" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_6_More_wfAndactincntcomploutRTOByProfile" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label20" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel5" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">
                                        <asp:ListView ID="lvWfAndactincntcomploutRTOByProfile_More" runat="server" OnPagePropertiesChanged="lvWfAndactincntcomploutRTOByProfile_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">Profile
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 13%;">Run
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 13%;">Total Workflow
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Action Completed Within RTO
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Action Completed Out Of RTO
                                                            </th>

                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 25%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblWorkFlowtype" CssClass="blt_12" runat="server" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label21" CssClass="" runat="server" Text='<%# Eval("TotalProfileRun") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 13%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("TotalWorkFlow") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 20%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("ProfileSuccess") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 20%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("ProfileAborted") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager19" runat="server" PagedControlID="lvWfAndactincntcomploutRTOByProfile_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager20" runat="server" PagedControlID="lvWfAndactincntcomploutRTOByProfile_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

        </div>
    </form>
</body>
</html>
