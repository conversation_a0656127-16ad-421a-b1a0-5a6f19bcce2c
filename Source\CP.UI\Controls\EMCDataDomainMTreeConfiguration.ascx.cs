﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class EMCDataDomainMTreeConfiguration : ReplicationControl
    {

        #region Variable

        private DropDownList _ddlSiteId = new DropDownList();
      //  public static string IPAddress = string.Empty;
        private TextBox _txtReplicationName = new TextBox();
        public string sitetype;
        private DropDownList _ddlReplicationType = new DropDownList();

        #endregion Variable

        #region Properties


        public int baseReplicationId = 0;

        private EMCDataDomainMTree _Mtree;

        private static readonly IFacade _facade = new Facade();


        public EMCDataDomainMTree CurrentEntity
        {

            get { return _Mtree ?? (_Mtree = new EMCDataDomainMTree()); }
            set
            {
                _Mtree = value;
            }
        }

        public string MessageInitials
        {
            get { return "MTree"; }
        }


        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }


        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        #endregion Properties


        public override void PrepareView()
        {

           // string hostName1 = Dns.GetHostName();
           // IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();
            ddlPrServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvPRServer.ClientID + ")");
            ddlDrServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvDrServer.ClientID + ")");
            txtPRDomainIP.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRDomainIP.ClientID + ")");
            txtDRDomainIP.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRDomainIP.ClientID + ")");
            txtPRuname.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRuname.ClientID + ")");
            txtDRuname.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRuname.ClientID + ")");
            txtPRpwd.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRpwd.ClientID + ")");
            txtDRpwd.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRpwd.ClientID + ")");
            txtPRsrcpath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRsrcpath.ClientID + ")");
            txtDRsrcpath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRsrcpath.ClientID + ")");
            txtPRsrcfqdnpath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRsrcfqdnpath.ClientID + ")");
            txtDRsrcfqdnpath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRsrcfqdnpath.ClientID + ")");
          //  txtPRdespath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRdespath.ClientID + ")");
          //  txtDRdespath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRdespath.ClientID + ")");
          //  txtPRdesfqdnpath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRdesfqdnpath.ClientID + ")");
          //  txtDRdesfqdnpath.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRdesfqdnpath.ClientID + ")");
            txtPRMtree.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRMtree.ClientID + ")");
            txtDRMtree.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRMtree.ClientID + ")");

            Session.Remove("MTreeRepliId");

            //Utility.PopulateServerByTypeAndRole(ddlPrServer, "EMCPRServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            //Utility.PopulateServerByTypeAndRole(ddlDrServer, "EMCDRServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);

            Utility.PopulateServerByTypeAndRolewithCompanyId(ddlPrServer, "EMCPRServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            Utility.PopulateServerByTypeAndRolewithCompanyId(ddlDrServer, "EMCDRServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);

            LoadData();
            PrepareEditView();
        }

        private void PrepareEditView()
        {
            //CurrentMTree.Id = Convert.ToInt32(Session["MTreeRepliId"]);
            if (CurrentMTree != null)// && CurrentAzure.Id > 0)
            {
                //CurrentEntity = CurrentMTree;
                //txtPRuname.Text = CurrentEntity.DataDomainPRServerIP;
                //txtDRuname.Text = CurrentEntity.DataDomainPRServerUserNamePassword;
                //txtPRpwd.Text = CurrentEntity.SourceMtreeReplicationpath;
                //txtDRpwd.Text = CurrentEntity.SourceMtreeReplicationFullFQDNPath;
                //txtPRPathSrc.Text = CurrentEntity.DestinationMtreeReplicationpath;
                //txtDRPathSrc.Text = CurrentEntity.DestinationMtreeReplicationFullFQDNPath;
                //txtPRPathSrcfqdn.Text = CurrentEntity.DestinationMtreeReplicationpath;
                //txtDRPathSrcfqdn.Text = CurrentEntity.DestinationMtreeReplicationFullFQDNPath;
                //txtPRPathDes.Text = CurrentEntity.DestinationMtreeReplicationpath;
                //txtDRPathDes.Text = CurrentEntity.DestinationMtreeReplicationFullFQDNPath;
                //txtPRPathDesfqdn.Text = CurrentEntity.DestinationMtreeReplicationpath;
                //txtDRPathDesfqdn.Text = CurrentEntity.DestinationMtreeReplicationFullFQDNPath;
                //txtPRMtree.Text = CurrentEntity.PRMTreeName;
                //txtDRMtree.Text = CurrentEntity.DRMTreeName;
                //btnSave.Text = "Update";

                CurrentEntity = CurrentMTree;
                Session["MTreeRepliId"] = CurrentEntity;
                ddlPrServer.SelectedValue = Convert.ToString(CurrentEntity.DomainPRServerId);
                ddlDrServer.SelectedValue = Convert.ToString(CurrentEntity.DomainDRServerId);
                txtPRDomainIP.Text = CurrentEntity.PRDataDomainServerIP;
                txtDRDomainIP.Text = CurrentEntity.DRDataDomainServerIP;
                txtPRuname.Text = CurrentEntity.PRDataDomainServerUserName;
                txtDRuname.Text = CurrentEntity.DRDataDomainServerUserName;
                txtPRpwd.Text = CurrentEntity.PRDataDomainServerPassword;
                txtDRpwd.Text = CurrentEntity.DRDataDomainServerPassword;
                txtPRsrcpath.Text = CurrentEntity.PRSourceMtreeReplicationpath;
                txtDRsrcpath.Text = CurrentEntity.DRDestinationMtreeReplicationpath;
                txtPRsrcfqdnpath.Text = CurrentEntity.PRSourceMtreeReplicationFullFQDNPath;
                txtDRsrcfqdnpath.Text = CurrentEntity.DRDestinationMtreeReplicationFullFQDNPath;
                //txtPRdespath.Text = CurrentEntity.PRDestinationMtreeReplicationpath;
                //txtDRdespath.Text = CurrentEntity.DRDestinationMtreeReplicationpath;
                //txtPRdesfqdnpath.Text = CurrentEntity.PRDestinationMtreeReplicationFullFQDNPath;
                //txtDRdesfqdnpath.Text = CurrentEntity.DRDestinationMtreeReplicationFullFQDNPath;
                txtPRMtree.Text = CurrentEntity.PRMTreeName;
                txtDRMtree.Text = CurrentEntity.DRMTreeName;

                DrServerDetails();
                PrServerDetails();


                btnSave.Text = "Update";
            }
        }

        private void LoadData()
        {
            if (CurrentMimix != null)
            {
                CurrentEntity = CurrentMTree;
                Session["mtree"] = CurrentEntity;
                ddlPrServer.SelectedValue = Convert.ToString(CurrentEntity.DomainPRServerId);
                ddlDrServer.SelectedValue = Convert.ToString(CurrentEntity.DomainDRServerId);
                txtPRDomainIP.Text = CurrentEntity.PRDataDomainServerIP;
                txtDRDomainIP.Text = CurrentEntity.DRDataDomainServerIP;
                txtPRuname.Text = CurrentEntity.PRDataDomainServerUserName;
                txtDRuname.Text = CurrentEntity.DRDataDomainServerUserName;
                txtPRpwd.Text = CurrentEntity.PRDataDomainServerPassword;
                txtDRpwd.Text = CurrentEntity.DRDataDomainServerPassword;
                txtPRsrcpath.Text = CurrentEntity.PRSourceMtreeReplicationpath;
                txtDRsrcpath.Text = CurrentEntity.DRDestinationMtreeReplicationpath;
                txtPRsrcfqdnpath.Text = CurrentEntity.PRSourceMtreeReplicationFullFQDNPath;
                txtDRsrcfqdnpath.Text = CurrentEntity.DRDestinationMtreeReplicationFullFQDNPath;
                //txtPRdespath.Text = CurrentEntity.PRDestinationMtreeReplicationpath;
                //txtDRdespath.Text = CurrentEntity.DRDestinationMtreeReplicationpath;
                //txtPRdesfqdnpath.Text = CurrentEntity.PRDestinationMtreeReplicationFullFQDNPath;
                //txtDRdesfqdnpath.Text = CurrentEntity.DRDestinationMtreeReplicationFullFQDNPath;
                txtPRMtree.Text = CurrentEntity.PRMTreeName;
                txtDRMtree.Text = CurrentEntity.DRMTreeName;
                btnSave.Text = "Update";
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }
            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }
            // Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }
            if (Page.IsValid)
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }

                try
                {
                    if (currentTransactionType != TransactionType.Undefined)
                    {

                        StartTransaction();
                        BuildEntities();
                        SaveEditor();
                        ReplicationName.Text = string.Empty;
                        EndTransaction();
                        CurrentEntity.ReplicationBase.Name = Session["MtreeRepliname"].ToString();
                        string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                currentTransactionType));
                        btnSave.Enabled = false;
                    }
                }

                catch (CpException ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                    {
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);
                    }
                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, Page);
                    }
                }
                if (returnUrl.IsNotNullOrEmpty())
                {
                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                    Helper.Url.Redirect(secureUrl);
                }
            }
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }
            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        private void SaveEditor()
        {
            if (btnSave.Text == "Save")

            {

                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddMTree(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "Mimix", UserActionType.CreateReplicationComponent, "The MTree Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);

            }
            else
            {
                CurrentEntity.Id = CurrentMTree.Id;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateMTree(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "Mimix", UserActionType.UpdateReplicationComponent, "The MTree Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        private void BuildEntities()
        {
            if (Session["mtree"] != null)
            {
                CurrentEntity = (EMCDataDomainMTree)Session["mtree"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            Session["MtreeRepliname"] = CurrentEntity.ReplicationBase.Name;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            //CurrentEntity.DataDomainPRServerIP = txtprserip.Text;
            //CurrentEntity.DataDomainPRServerUserNamePassword = txtpruname.Text;
            //CurrentEntity.SourceMtreeReplicationpath = txtsrcpath.Text; ;
            //CurrentEntity.SourceMtreeReplicationFullFQDNPath = txtsrcfqdn.Text;
            //CurrentEntity.DestinationMtreeReplicationpath = txtdespath.Text;
            //CurrentEntity.DestinationMtreeReplicationFullFQDNPath = txtdesfqdn.Text;
            //CurrentEntity.PRMTreeName = txtPRMtreename.Text;
            //CurrentEntity.DRMTreeName = txtDRMtreename.Text;
            CurrentEntity.DomainPRServerId = Convert.ToInt32(ddlPrServer.SelectedValue);

            CurrentEntity.DomainDRServerId = Convert.ToInt32(ddlDrServer.SelectedValue);

            CurrentEntity.PRDataDomainServerIP = txtPRDomainIP.Text;
            CurrentEntity.DRDataDomainServerIP = txtDRDomainIP.Text;
            CurrentEntity.PRDataDomainServerUserName = txtPRuname.Text; ;
            CurrentEntity.DRDataDomainServerUserName = txtDRuname.Text;
            CurrentEntity.PRDataDomainServerPassword = txtPRpwd.Text;
            CurrentEntity.DRDataDomainServerPassword = txtDRpwd.Text;
            CurrentEntity.PRSourceMtreeReplicationpath = txtPRsrcpath.Text;
            CurrentEntity.DRDestinationMtreeReplicationpath = txtDRsrcpath.Text;
            CurrentEntity.PRSourceMtreeReplicationFullFQDNPath = txtPRsrcfqdnpath.Text;
            CurrentEntity.DRDestinationMtreeReplicationFullFQDNPath = txtDRsrcfqdnpath.Text;
            //CurrentEntity.PRDestinationMtreeReplicationpath = txtPRdespath.Text;
            //CurrentEntity.DRDestinationMtreeReplicationpath = txtDRdespath.Text;
            //CurrentEntity.PRDestinationMtreeReplicationFullFQDNPath = txtPRdesfqdnpath.Text;
            //CurrentEntity.DRDestinationMtreeReplicationFullFQDNPath = txtDRdesfqdnpath.Text;
            CurrentEntity.PRMTreeName = txtPRMtree.Text;
            CurrentEntity.DRMTreeName = txtDRMtree.Text;

        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        protected void DdlPrServerSelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlPrServer.SelectedIndex > 0)
                PrServerDetails();
            else
            {
                txtPRDomainIP.Enabled = false;
                txtPRpwd.Enabled = false;
                txtPRuname.Enabled = false;
                txtPRDomainIP.Text = string.Empty;
                txtPRpwd.Text = "";
                txtPRpwd.Attributes["value"] = "";
                txtPRuname.Text = string.Empty;
            }
        }

        protected void DdlDrServerSelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlDrServer.SelectedIndex > 0)
            {
                DrServerDetails();
            }
            else
            {
                txtDRDomainIP.Enabled = false;
                txtDRpwd.Enabled = false;
                txtDRuname.Enabled = false;
                txtDRDomainIP.Text = string.Empty;
                txtDRpwd.Text = "";
                txtDRpwd.Attributes["value"] = "";
                txtDRuname.Text = string.Empty;

            }
        }

        

        private void PrServerDetails()
        {
                   
            if (ddlPrServer.SelectedValue != "00")
            {
                var server = Facade.GetServerById(ddlPrServer.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtPRDomainIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtPRuname.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                    txtPRpwd.Attributes["value"] = Utility.IsMD5EncryptedString(server.SSHPassword) ? server.SSHPassword : CryptographyHelper.Md5Encrypt(server.SSHPassword);
                }
                DisableServer("PR");
            }
            else
            {
                txtPRDomainIP.Text = string.Empty;
                txtPRuname.Text = string.Empty;
                txtPRpwd.Attributes["value"] = string.Empty;
            }
        }

        private void DisableServer(string server)
        {
            if (server == "PR")
            {
                txtPRDomainIP.Enabled = false;
                txtPRuname.Enabled = false;
                txtPRpwd.Enabled = false;
            }
            else
            {
                txtDRDomainIP.Enabled = false;
                txtDRuname.Enabled = false;
                txtDRpwd.Enabled = false;
            }
        }

        private void DrServerDetails()
        {
            if (ddlDrServer.SelectedValue != "00")
            {
                var server = Facade.GetServerById(ddlDrServer.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtDRDomainIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtDRuname.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                    txtDRpwd.Attributes["value"] = Utility.IsMD5EncryptedString(server.SSHPassword) ? server.SSHPassword : CryptographyHelper.Md5Encrypt(server.SSHPassword);
                }
                DisableServer("DR");
            }
            else
            {
                txtDRDomainIP.Text = string.Empty;
                txtDRuname.Text = string.Empty;
                txtDRpwd.Attributes["value"] = string.Empty;
            }
        }


        public static void GetServer(ListControl lstCompany)
        {
            lstCompany.DataSource = _facade.GetAllServers();
            lstCompany.DataTextField = "Name";
            lstCompany.DataValueField = "Id";
            lstCompany.DataBind();
            lstCompany.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "0"));
        }

    }
}