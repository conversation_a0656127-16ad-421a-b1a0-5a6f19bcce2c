﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="WorkflowProfileManagement.aspx.cs" Inherits="CP.UI.Admin.WorkflowProfileManagement"
    Title="Workflow Profile Management" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />



    <style type="text/css">
        .dropdown-menu ul {
            max-height: 205px !important;
        }

        .bootstrap-select[class*="col"] .btn {
            width: 99% !important;
        }

        .col-md-6 {
            padding-right: 0px !important;
            width: 48.5% !important;
        }

        .full_dropdown .chosen-select + .chosen-container {
            width: 100% !important;
        }
    </style>

    <style>
        .chosen-select + .chosen-container {
            width: 48.5% !important;
            opacity: 1 !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <asp:HiddenField ID="hdnGuid" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
            <ContentTemplate>


                <%--<ul class="breadcrumb show">
                    <li>You are here</li>
                    <li><a class="glyphicons list"><i></i>IT Orchestration</a></li>
                    <li class="divider"></li>
                    <li>Workflow Profile Management</li>
                </ul>--%>

                <h3>
                    <img src="../Images/worflow_new.png" />
                    Workflow Profile Management</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <asp:Label ID="lblsuccess" runat="server" ForeColor="Green" Visible="false"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3">
                                        Load Profile</label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlLoadPorfile" runat="server" CssClass="chosen-select col-md-6"
                                            data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlLoadPorfile_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:Button ID="BtnNew" runat="server" Text="New" CssClass="btn btn-primary" Width="15%"
                                            Enabled="true" Visible="true" OnClick="BtnNewClick" CausesValidation="false" />
                                        <asp:Button ID="BtnDeleteProfile" runat="server" Text="Delete" Visible="false" CssClass="btn btn-primary"
                                            Width="15%" OnClick="BtnDeleteProfileClick" Enabled="true" />
                                        <TK1:ConfirmButtonExtender ID="ConfirmProfileDelete" runat="server" TargetControlID="BtnDeleteProfile"
                                            ConfirmText="Are you sure you want to delete selected profile?" OnClientCancel="CancelClick">
                                        </TK1:ConfirmButtonExtender>
                                    </div>
                                </div>

                                <asp:Panel runat="server" CssClass="form-group" ID="panle1" Style="margin-left: -2px !important" Visible="false">
                                    <div class="form-group">
                                        <label class="col-md-3">
                                            Create Profile</label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtDescription" runat="server" class="form-control" Text="" Enabled="False" Width="47.5%"
                                                ValidationGroup="NameValidate" AutoPostBack="True" MaxLength="100" OnTextChanged="txtDescription_TextChanged"></asp:TextBox>
                                            <asp:RequiredFieldValidator ID="rfvCheckEmpty" runat="server" CssClass="error" ControlToValidate="txtDescription"
                                                ErrorMessage="*" ValidationGroup="NameValidate" Display="Dynamic"></asp:RequiredFieldValidator>
                                            <asp:RegularExpressionValidator ID="rgcheckName" runat="server" Display="Dynamic" CssClass="error"
                                                ControlToValidate="txtDescription" ErrorMessage="InValid Profile Name(Only @ - _ $ # special characters allowed)"
                                                ValidationGroup="NameValidate" ValidationExpression="^[a-zA-Z0-9,.@-_$#-]+$"></asp:RegularExpressionValidator>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3">
                                            Password</label>
                                        <div class="col-md-9">
                                            <asp:UpdatePanel runat="server" ID="updatePnlPass">
                                                <ContentTemplate>
                                                    <asp:TextBox ID="txtPassword" runat="server" OnTextChanged="TxtPasswordTextChanged" CssClass="form-control  chk-capsp" TextMode="Password" Width="47.5%" onfocus="ClearVal(this);" AutoPostBack="true"
                                                        ValidationGroup="NameValidate"></asp:TextBox>
                                                    <a title=" * Password must have minimum 8 characters with atleast one upper-case alphabet, one numeric character & one special character."
                                                        class="error">
                                                        <img class="vertical-align" src="../Images/icons/icon-tooltipQ.png"></a>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" CssClass="error" ControlToValidate="txtPassword"
                                                        ErrorMessage="Please Enter Password" ValidationGroup="NameValidate" Display="Dynamic"></asp:RequiredFieldValidator>

                                                        <span class="caps-error">Caps Lock is ON.</span>
                                                    <asp:Label ID="lblPass" runat="server" ForeColor="Red" Text="Password is not proper format" Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                                                    <%--<asp:RegularExpressionValidator ID="revPwd" runat="server" ControlToValidate="txtPassword" CssClass="error"
                                                ErrorMessage="Password is not proper format" ValidationExpression="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,15}$"
                                                Display="Dynamic"></asp:RegularExpressionValidator>--%>

                                                    <asp:Button ID="btnChangePassword" runat="server" CssClass="btn btn-primary keys12" title="Change Password" Style="padding-left: 25px;"
                                                        Enabled="true" Visible="true" CausesValidation="false" OnClick="btnChangePassword_Click" />

                                                </ContentTemplate>
                                                <Triggers>
                                                    <asp:AsyncPostBackTrigger ControlID="txtPassword" EventName="TextChanged" />
                                                </Triggers>
                                            </asp:UpdatePanel>
                                        </div>
                                    </div>

                                    <%--<div class="form-group" style="display: none;">--%>
                                    <div class="form-group">
                                        <label class="col-md-3">
                                            Confirm Password</label>
                                        <div class="col-md-9">
                                            <asp:TextBox ID="txtConfirm" runat="server" CssClass="form-control chk-capspc" TextMode="Password" Width="47.5%" onfocus="ClearVal(this);" ValidationGroup="NameValidate"></asp:TextBox>
                                            <a title=" * Password must have minimum 8 characters with atleast one upper-case alphabet, one numeric character & one special character."
                                                class="error">
                                                <img class="vertical-align" src="../Images/icons/icon-tooltipQ.png"></a>
                                              <span class="caps-error">Caps Lock is ON.</span>


                                            <%-- <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ControlToValidate="txtConfirm" CssClass="error"
                                                ErrorMessage="Password is not proper format" ValidationExpression="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,15}$"
                                                Display="Dynamic"></asp:RegularExpressionValidator>--%>
                                            <asp:CompareValidator ID="CompareValidator1" runat="server" ControlToValidate="txtConfirm" CssClass="error" ControlToCompare="txtPassword" ErrorMessage="Confirm password does not match with Password" ToolTip="Password must be the same" />
                                        </div>
                                    </div>
                                </asp:Panel>
                                <asp:Panel runat="server" CssClass="form-group" ID="panle" Visible="False">

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="col-md-11 full_dropdown">
                                                <div class="col-md-3">
                                                    <asp:DropDownList ID="DdlBusinessService" runat="server" CssClass="chosen-select col-md-12"
                                                        data-style="btn-default" OnSelectedIndexChanged="DdlBusinessServiceSelectedIndexChanged"
                                                        AutoPostBack="True">
                                                    </asp:DropDownList>
                                                </div>

                                                <div class="col-md-3">
                                                    <asp:DropDownList ID="DdlBusinessFunction" runat="server" CssClass="chosen-select col-md-12"
                                                        data-style="btn-default" OnSelectedIndexChanged="DdlBusinessFunctionSelectedIndexChanged"
                                                        AutoPostBack="True">
                                                    </asp:DropDownList>
                                                </div>

                                                <div class="col-md-3">
                                                    <asp:DropDownList ID="DdlInfraObject" runat="server" CssClass="chosen-select col-md-12"
                                                        data-style="btn-default" OnSelectedIndexChanged="DdlInfraObjectSelectedIndexChanged"
                                                        AutoPostBack="True">
                                                    </asp:DropDownList>
                                                </div>

                                                <div class="col-md-3">
                                                    <asp:DropDownList ID="ddlWorkflow" runat="server" CssClass="chosen-select col-md-12"
                                                        data-style="btn-default" OnSelectedIndexChanged="ddlWorkflowSelectedIndexChanged"
                                                        AutoPostBack="True">
                                                    </asp:DropDownList>
                                                </div>
                                            </div>

                                            <div class="col-md-1" style="padding-top: 9px;">
                                                <asp:ImageButton ID="BtnAdd" ImageUrl="../Images/icons/plus-circle.png" Enabled="False"
                                                    ToolTip="Add" ValidationGroup="NameValidate" CausesValidation="True" CssClass=" text-right"
                                                    OnClick="BtnAddClick" runat="server" />
                                            </div>
                                        </div>
                                    </div>
                                </asp:Panel>
                                <%-- <asp:Panel ID="panelListView" runat="server">--%>
                                <asp:ListView ID="lvGroup" runat="server" OnItemDeleting="lvGroup_ItemDeleting" OnItemDataBound="lvGroup_ItemDataBound">
                                    <LayoutTemplate>
                                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white"
                                            style="margin-bottom: 0; margin: 14px; width: 98%">
                                            <thead>
                                                <tr>
                                                    <th style="width: 5%">S.No
                                                    </th>
                                                    <th style="width: 20%">Business Service
                                                    </th>
                                                    <th style="width: 20%">Business Function
                                                    </th>
                                                    <th style="width: 20%">InfraObject
                                                    </th>
                                                    <th style="width: 20%;">Workflow
                                                    </th>
                                                    <th>&nbsp;&nbsp;Action
                                                    </th>
                                                </tr>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                            </thead>
                                        </table>
                                        <%--<div class="parallelScroll">
                                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white"
                                                    style="table-layout: fixed;" id="lvGrouptbl">
                                                    <tbody>
                                                    </tbody>
                                                </table>
                                            </div>--%>
                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td class="th table-check-cell" style="width: 5%">
                                                <%#Container.DataItemIndex+1%>
                                            </td>
                                            <td style="width: 20%;">
                                                <div class="tdword-wrap">
                                                    <asp:Label ID="Label1" runat="server" Text='<%#GetBusinessServiceName(Eval("InfraObjectId")) %>' />
                                                </div>
                                            </td>
                                            <td style="width: 20%;">
                                                <div class="tdword-wrap">
                                                    <asp:Label ID="Label3" runat="server" Text='<%#GetBusinessFunctionName(Eval("InfraObjectId")) %>' />
                                                </div>
                                            </td>
                                            <td style="width: 20%">
                                                <div class="tdword-wrap">
                                                    <asp:Label ID="lblId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                    <asp:Label ID="lbGroupName" Visible="true" runat="server" Text='<%# Eval("InfraObjectName") %>' />
                                                </div>
                                            </td>
                                            <td style="width: 20%;">
                                                <div class="tdword-wrap">
                                                    <asp:Label ID="Label2" runat="server" Text='<%# Eval("WorkflowName") %>' />
                                                </div>
                                            </td>
                                            <td>
                                                <asp:ImageButton ID="ImgDelete" runat="server" Visible="true" Enabled="true" CommandName="Delete"
                                                    AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                    ConfirmText='<%# "Are you sure you want to delete " + Eval("WorkflowName") + " ? " %>'
                                                    OnClientCancel="CancelClick">
                                                </TK1:ConfirmButtonExtender>
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                    <EmptyDataTemplate>
                                        <tr>
                                            <td>
                                                <div class="message warning align-center bold no-margin no-bottom-margin alertred alert-danger" style="background-color: #B94A48; border-color: #EED3D7; color: #FFFFFF; text-align: center;">
                                                    <asp:Label ID="pageResult" runat="server" Text="No Records" Style="text-align: center; width: 98% !important;"></asp:Label>
                                                </div>
                                            </td>
                                        </tr>
                                    </EmptyDataTemplate>
                                </asp:ListView>
                                <%--</asp:Panel>--%>
                            </div>

                            <%--   <hr />--%>
                            <div>
                                <div class="col-md-3">
                                    <asp:Label ID="lblError" runat="server" ForeColor="Red"></asp:Label>
                                    <asp:Label ID="lblMessage" runat="server" ForeColor="Red" Visible="False"></asp:Label>
                                    <asp:Label ID="lblSOWrkflwMsg" runat="server" ForeColor="Red" Visible="False"></asp:Label>
                                </div>
                                <div class="col-md-8 text-right">
                                    <asp:Button ID="BtnSaveProfile" runat="server" Text="Save Profile" Enabled="False"
                                        Visible="False" CssClass="btn btn-primary" Width="20%" OnClick="BtnSaveProfileClick"
                                        ValidationGroup="NameValidate" CausesValidation="True" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
                </asp:Panel>
                <asp:Panel ID="pnlActionDeletion" runat="server" Visible="false">
                    <div class="modal" style="display: block;" aria-hidden="false">
                        <div class="modal-dialog" style="width: 400px;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3 class="modal-title">Confirmation</h3>
                                    <asp:LinkButton ID="Lkbtnclosesmtphost" runat="server" ToolTip="Close window" OnClick="Lkbtnclosesmtphost_Click"
                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-12 text-center">
                                            <div class="form-group">
                                                <asp:Label ID="lblActionDeletion" runat="server" Text="Test"></asp:Label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                </asp:Panel>
                <asp:Panel ID="Panel_confirmpassword" runat="server" Width="100%" Visible="false">

                    <div class="modal" style="display: block;">
                        <div class="modal-dialog" style="width: 535px;">
                            <div class="modal-content  widget-body-white">
                                <div class="modal-header">
                                    <h3 class="modal-title">Change Password</h3>
                                    <asp:LinkButton ID="LkbtncloseChangepass" runat="server" ToolTip="Close window"
                                        CausesValidation="False" class="close" CommandName="Close" OnClick="LkbtncloseChangepass_Click"> ×</asp:LinkButton>
                                </div>
                                <div class="modal-body">
                                    <asp:Label ID="lblsmtpsavemessage" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>
                                    <asp:UpdatePanel ID="UpdatePanel_confirmpassword" runat="server" UpdateMode="Conditional">
                                        <ContentTemplate>
                                            <div class="">
                                                <div class="col-md-12 form-horizontal uniformjs">


                                                    <div id="MsgSuccess" class="alert-success success-msg fade in" runat="server">

                                                        <a href="#" class="close" data-dismiss="alert">&times;</a>

                                                        Password Changed Successfully.

                                                    </div>
                                                    <div id="MsgError" class="alert-danger error-data fade in" runat="server">

                                                        <a href="#" class="close" data-dismiss="alert">&times;</a>

                                                        Invalid Password.

                                                    </div>



                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label " style="width: 38%">
                                                            <input type="hidden" id="hdfStaticGuid" runat="server" />
                                                            <asp:HiddenField ID="HiddenField1" runat="server" />
                                                            <asp:Label ID="Label5" runat="server" Text="Old Password"></asp:Label>
                                                            <span class="inactive">*</span>
                                                        </label>
                                                        <div class="col-md-8" style="width: 62%">
                                                            <asp:TextBox ID="txtOldPassword" runat="server" CssClass="form-control" TextMode="Password" Width="96%" TabIndex="1"
                                                                OnTextChanged="txtOldPassword_TextChanged" onblur="getHashData(this)" onfocus="clearText(this)" AutoPostBack="true" autocomplete="off"></asp:TextBox>

                                                            <asp:Label ID="lblError1" runat="server" ForeColor="Red"
                                                                Visible="False" Display="dynamic" CssClass="error"></asp:Label>

                                                            <asp:Label ID="lblPasswordless" runat="server" Text="Enter Password" Visible="False"></asp:Label>
                                                            <asp:RequiredFieldValidator ID="rfvoldPass" runat="server" CssClass="error" ErrorMessage="*"
                                                                ControlToValidate="txtOldPassword" Display="Dynamic" ValidationGroup="p1"></asp:RequiredFieldValidator>

                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label " style="width: 38%">
                                                            <asp:Label ID="Label4" runat="server" Text="New Password"></asp:Label>
                                                            <span class="inactive">*</span>
                                                        </label>
                                                        <div class="col-md-8" style="width: 62%">
                                                            <asp:TextBox ID="txtNewPassword" runat="server" CssClass="form-control" TextMode="Password" Width="96%" TabIndex="2"
                                                                OnTextChanged="txtNewPassword_TextChanged" onblur="getHashData(this)" onfocus="clearText(this)" AutoPostBack="true" autocomplete="off"></asp:TextBox>
                                                            <asp:Label ID="Label6" runat="server" Text="Enter Password" Visible="False"></asp:Label>
                                                             <asp:Label ID="lblError9" runat="server" ForeColor="Red"
                                                                Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                                                            <asp:RequiredFieldValidator ID="rfvnewPass" runat="server" CssClass="error" ErrorMessage="*"
                                                                ControlToValidate="txtNewPassword" Display="Dynamic" ValidationGroup="p1"></asp:RequiredFieldValidator>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 control-label " style="width: 38%">
                                                            <asp:Label ID="Label7" runat="server" Text="Confirm New Password"></asp:Label>
                                                            <span class="inactive">*</span>
                                                        </label>
                                                        <div class="col-md-8" style="width: 62%">
                                                            <asp:TextBox ID="txtConfNewPassword" runat="server" CssClass="form-control" TextMode="Password" Width="96%" TabIndex="3"
                                                                OnTextChanged="txtConfNewPassword_TextChanged" onblur="getHashData(this)" onfocus="clearText(this)" AutoPostBack="true" autocomplete="off"></asp:TextBox>
                                                            <asp:Label ID="Label8" runat="server" Text="Enter Password" Visible="False"></asp:Label>
                                                            <asp:RequiredFieldValidator ID="rfvConfnewPass" runat="server" CssClass="error" ErrorMessage="*"
                                                                ControlToValidate="txtConfNewPassword" Display="Dynamic" ValidationGroup="p1"></asp:RequiredFieldValidator>
                                                            <asp:Label ID="lblMessage1" runat="server" ForeColor="Red" Visible="False"></asp:Label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </div>

                                <div class="modal-footer">
                                    <div class="col-xs-4 text-left">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                Fields</span>
                                    </div>
                                    <div class="col-xs-8 text-right">
                                        <asp:Button ID="btnChange" runat="server" Text="Change Password" CssClass="btn btn-primary" Width="52%"
                                            ValidationGroup="p1" Visible="True" OnClick="btnChange_Click" />
                                        <asp:Button ID="btnCancel" runat="server" Text="Cancel" CssClass="btn btn-default"
                                            Width="22%" CausesValidation="False" OnClick="btnCancel_Click" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </asp:Panel>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>

    <script src="../Script/chosen.jquery.js"></script>
    <script type="text/javascript">
        function getHashData(control) {
            control.value = genrateUserNameHash(control, $('#ctl00_cphBody_hdfStaticGuid').val());
        }


        function CancelClick() {
            return false;
        }
        function genrateUserNameHashOnBlur(control) {
            control.value = genrateUserNameHash(control, $("#ctl00_cphBody_hdnGuid").val());
        }

        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
            //$("select.new-select").select2({
            //});

            //$("select.new-select").select2({
            //});
            setTimeout(function () {
                $(".success-msg,.error-data ").fadeOut('slow');
            }, 4000);

        }

        function fdeout() {
            // alert();
            // $(".chngepassbg").hide();
            setTimeout(function () {
                $("#ctl00_cphBody_modelbg,#ctl00_cphBody_Panel_confirmpassword").fadeOut('slow');
            }, 4000);
            
            
        }


        function clearText(control) {
            control.value = "";

        }

    </script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
    </script>

    <script>
        function pageLoad() {
            /*For Password and confirm password field chk-capsp and chk-capspc */
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });

            $(window).bind("capsOn", function (event) {
                if ($(".chk-capspc:focus").length > 0) {
                    $(".chk-capspc").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-capspc").nextAll(".caps-error").hide();
            });
            $(".chk-capspc").bind("focusout", function (event) {
                $(".chk-capspc").nextAll(".caps-error").hide();
            });
            $(".chk-capspc").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-capspc").nextAll(".caps-error").show();
                }
            });
            $(window).capslockstate();

            /*For Password and confirm password field chk-capsp and chk-capspc */


            $(window).bind("capsOn", function (event) {
                if ($(".chk-capsp:focus").length > 0) {
                    $(".chk-capsp").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-capsp").nextAll(".caps-error").hide();
            });
            $(".chk-capsp").bind("focusout", function (event) {
                $(".chk-capsp").nextAll(".caps-error").hide();
            });
            $(".chk-capsp").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-capsp").nextAll(".caps-error").show();
                }
            });
            $(window).capslockstate();
        }
   </script>
</asp:Content>
