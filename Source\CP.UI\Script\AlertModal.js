﻿function openModal(headerMessage, content, hideButtonIds, saveEvent, updateEvent) {
    $.modal({
        content: content,
        title: headerMessage,
        maxWidth: 300,
        buttons: {
            'Save': saveEvent,
            'Reset': function (win) { ResetModelPopUpField('MainModelPopupDiv'); },
            'Update': updateEvent,
            'Cancel': function (win) { CloseModel(win); },
            'Close': function (win) { CloseModel(win); }
        }
    });

    var buttonIds = hideButtonIds.split(":");
    for (var i = 0; i < buttonIds.length; i++) {
        $("button").each(function () {
            if ($(this).html() == buttonIds[i]) {
                $(this).hide();
            }
        });
    }
}

function JobCreation(content, yesMethod) {
    $.modal({
        content: '<div> ' +
                '<div class="text-indent">' + content + ' </div>' +
                '</div>',
        title: 'Create Job',
        maxWidth: 300,
        buttons: {
            'Yes': function (win) { yesMethod(win); },
            'No': function (win) { CloseModel(win); }
        }
    });
}

function OpenAlertModelConform(content, yesMethod, NoMethod) {
    $.modal({
        content: '<div class="row"> ' +
                    '<div class="col-md-12 form-horizontal uniformjs">' +
                    '<div class="col-md-3 text-right"><img src="../images/icons/Warning.png"/></div> ' +
                '<div class="col-md-9 padding-none-LR" style="padding-top: 5px;">' + content + ' </div>' +
                '</div></div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'No': NoMethod
        }
    });
}

function OpenAlertModelActionGroup(content, yesMethod) {
    $.modal1({
        content: '<div> ' +
                '<div class="text-indent">' + content + ' </div>' +
                '</div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'No': function (win) { CloseModelEnd(win); }
        }
    });
}

function OpenAlertModelConfirmation(content, yesMethod) {
    $.modal({
        content: '<div> ' +
                '<div class="float-left"><img src="../../images/icons/Warning.png"/></div> ' +
                '<div class="text-indent">' + content + ' </div>' +
                '</div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'No': function (win) { CloseModel(win); }
        }
    });
}

function OpenAlertModelLoad(content, yesMethod) {
    $.modal({
        content: '<div class="row"> ' +
                '<div class="col-md-12 form-horizontal uniformjs">' + content + ' </div>' +
                '</div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'No': function (win) { CloseModel(win); }
        }
    });
}

function OpenAlertModelForLoadWorkflow(content, yesMethod, deleteWorkflow) {
    $.modal({
        content: '<div class="row"> ' +
                '<div class="col-md-12 form-horizontal uniformjs">' + content + ' </div>' +
                '</div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'Delete': deleteWorkflow,
            'No': function (win) { CloseModel(win); }
        }
    });
}
function OpenAlertModelForLoadWorkflowWitCustom(content, yesMethod) {
    $.modal({
        content: '<div class="row"> ' +
                '<div class="col-md-12 form-horizontal uniformjs">' + content + ' </div>' +
                '</div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            //  'Delete': deleteWorkflow,
            'No': function (win) { CloseModel(win); }
        }
    });
}

function OpenAlertModelAlert(content) {
    $.modal1({
        content: '<div class="row"> ' +
                    '<div class="col-md-12 form-horizontal uniformjs">' + content + ' </div>' +
                '</div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Ok': function (win) { CloseModelEnd(win); }
        }
    });
}

//function OpenAlertModelAlertTestScript(content) {
//    $.modal1({
//        content: '<div class="row"> ' +
//                    '<div class="col-md-12 form-horizontal uniformjs">' + content + ' </div>' +
//                '</div>',
//        title: 'Confirmation',
//        maxWidth: 250,
//        buttons: {
//            'Ok': function (win) { CloseModelEnd(win); }
//        }
//    });
//}

function ViewAlertModel(content, header, width, ok) {
    $.modal({
        content: content,
        title: header,
        maxWidth: width,
        buttons: {
            Ok: function (win) { CloseModel(win); }
        }
    });
}

function OpenAlertModelworkflow(content) {
    $.modal({
        content:
        content,
        title: 'Workflow',
        maxWidth: 300,
        buttons: {
            'Ok': function (win) { CloseModel(win); }
        }
    });
}


function CloseModel(win) {
    win.closeModal();
}

function CloseModelEnd(win) {
    win.closeModal1();

}

function ReloadParentCloseModal(win) {
    //win.opener.location.reload(true);
    win.closeModal();
}

function OpenAlertModel1(header, content) {
    $.modal({
        content:
        content,
        title: header,
        maxWidth: 300,
        buttons: {
            'Ok': function (win) { CloseModel(win); }
        }
    });
}


function ResetModelPopUpField(id) {
    var dropdownId = "";
    var radioId = "";
    var chkboxId = "";

    $('[id$=' + id + '] input').val('');
    $('[id$=' + id + '] select').each(function () {
        dropdownId = $(this).attr('id');
        $("#" + dropdownId).val($("#" + dropdownId + " option:first").val());
    });
    $('[id$=' + id + '] input:checkbox,[id$=' + id + '] input:radio').each(function () {
        radioId = $(this).attr('id');
        $("#" + radioId).attr('checked', false);
    });
}
function OnError(result) {
    if(result.d!=undefined)
    alert(result.d);
}
function ppp() {
    $.modal({
        content: '<div> ' +
                '<div class="float-left"><img src="../../images/icons/Warning.png"/></div> ' +
                '<div class="text-indent">' + content + ' </div>' +
                '</div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'No': NoMethod
        }
    });
}

function OpenAlertModelJobAlert(content) {
    $.modal({
        content: '<div> ' +
                '<div class="float-left"><img src="../../images/icons/Warning.png"/></div> ' +
                '<div class="text-indent">' + content + ' </div>' +
                '</div>',
        title: 'Confirmation',
        width: 300,
        buttons: {
            'Ok': function (win) { CloseModel(win); }
        }
    });
}

function openModalForInfraObjectJob(headerMessage, content, hideButtonIds, saveEvent, updateEvent) {
    $.modal({
        content: content,
        title: headerMessage,
        width: 300,
        buttons: {
            'Save': saveEvent,
            'Reset': function (win) { ResetModelPopUpField('MainModelPopupDiv'); },
            'Update': updateEvent,
            'Cancel': function (win) { CloseModel(win); },
            'Close': function (win) { CloseModel(win); }
        }
    });

    var buttonIds = hideButtonIds.split(":");
    for (var i = 0; i < buttonIds.length; i++) {
        $("button").each(function () {
            if ($(this).html() == buttonIds[i]) {
                $(this).hide();
            }
        });
    }
}

function OpenModelPopupForRunBook(content) {
    $.modal({
        content: '<div> ' +
                '<div class="float-left"><img src="../../images/icons/Warning.png"/></div> ' +
                '<div class="text-indent">' + content + ' </div>' +
                '</div>',
        title: 'Run Book',
        width: 700,
        buttons: {
            'Ok': function (win) { CloseModel(win); }
        }
    });
}

function OpenDeAttachGroupWorkflow(content, deleteRelation) {
    $.modal({
        content: '<div> ' +
                '<div class="text-indent">' + content + ' </div>' +
                '</div>',
        title: 'De-Attach Group',
        maxWidth: 300,
        buttons: {
            'De-Attach': deleteRelation,
            'No': function (win) { CloseModel(win); }
        }
    });
}
function OpenManageGroupWorkflowPopUp(content) {
    $.modal({
        content: '<div> ' +
                '<div class="float-left"><img src="../../images/icons/Warning.png"/></div> ' +
                '<div class="text-indent">' + content + ' </div>' +
                '</div>',
        title: 'Manage Group Workflow',
        maxWidth: 300,
        height: 150
    });
}

function openAlertMechanismModal(headerMessage, content, hideButtonIds, saveEvent, updateEvent) {
    $.modal({
        content: content,
        title: headerMessage,
        width: 800,
        height: 150,
        buttons: {
            'OK': saveEvent,
            'Reset': function (win) { ResetModelPopUpField('MainModelPopupDiv'); },
            'Update': updateEvent,
            'Cancel': function (win) { CloseModel(win); },
            'Close': function (win) { CloseModel(win); }
        }
    });

    var buttonIds = hideButtonIds.split(":");
    for (var i = 0; i < buttonIds.length; i++) {
        $("button").each(function () {
            if ($(this).html() == buttonIds[i]) {
                $(this).hide();
            }
        });
    }
}


function OpenAlertModelCopyWF(content, yesMethod,title) {
    $.modal({
        content: '<div class="row"> ' +
                '<div class="col-md-12 form-horizontal uniformjs">' + content + ' </div>' +
                '</div>',
        title: title,
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'No': function (win) { CloseModel(win); }
        }
    });
}

function OpenAlertModelXMLimport(content, yesMethod) {
    $.modal({
        content: '<div class="row"> ' +
                '<div class="col-md-12 form-horizontal uniformjs">' + content + ' </div>' +
                '</div>',
        title: 'Export Workflow as File',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'No': function (win) { CloseModel(win); }
        }
    });
}

function OpenWorkflowHistory(content, wfname) {
    $.modal({
        content: '<div class=""> ' +
                    '<div class="col-md-12 form-horizontal uniformjs padding-none">' + content + ' </div>' +
                '</div>',
        title: 'Workflow History : ' + wfname,
        maxWidth: 300,
        buttons: {
            'Ok': function (win) { CloseModel(win); }
        }
    });
}

function OpenAlertRestoreModelLoadworkflow(content, yesMethod) {
    $.modal({
        content: '<div class="col-md-12 uniformjs padding-none">' + content + ' </div>',
        title: 'Confirmation',
        maxWidth: 300,
        buttons: {
            'Yes': yesMethod,
            'No': function (win) { CloseModel(win); }
        }
    });
}