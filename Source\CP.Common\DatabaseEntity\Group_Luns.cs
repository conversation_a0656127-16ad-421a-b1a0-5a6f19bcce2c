﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Group_Luns", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class Group_Luns : BaseEntity
    {
        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string Luns { get; set; }

        [DataMember]
        public int Logs { get; set; }

        [DataMember]
        public string AGroup { get; set; }
               
        [DataMember]
        public string BGroup { get; set; }

        [DataMember]
        public string CGroup { get; set; }

        [DataMember]
        public string DGroup { get; set; }

        [DataMember]
        public string EGroup { get; set; }

        [DataMember]
        public string FGroup { get; set; }

        #endregion Properties
    }
}
