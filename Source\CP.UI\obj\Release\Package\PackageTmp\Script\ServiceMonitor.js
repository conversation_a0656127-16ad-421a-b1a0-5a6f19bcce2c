﻿// Starts =====================
var globalstring = null;
var JsonForFullView = null;
var isbtnReloadHide = false;
var level = 0;
var lastRightClickNode = null;
var newJSON = "";
var apptype = "";



var globalTime = null;
var globalIndex = 1;

// set interval
var globalTime = setInterval(function () { businessServiceRotate() }, 60000);


function businessServiceRotate() {

    // if  the service is only one then set golbalIndex = 0; 
    if ($(".profileul li").length == 1) {
        globalIndex = 0;
    }

    var ServiceList = $(".profileul li .dspnone");

    if (ServiceList.length > 0) {

        //Render Business Service Tree
        var BusinessServices = ServiceList[globalIndex].innerText.split("_");
        renderService(BusinessServices[0], "S", BusinessServices[1])


        //remove the slider of last active service
        if (globalIndex > 0) {
            var lastService = $(".profileul li")[globalIndex - 1];
            $(lastService).removeClass("activeService");
        }
        else {

            var lastService = $(".profileul li")[ServiceList.length - 1];
            $(lastService).removeClass("activeService");

        }

        //Set slider to  Active Service
        var Service = $(".profileul li")[globalIndex];
        $(Service).addClass("activeService");

        globalIndex++;

        if (globalIndex == ServiceList.length) {
            globalIndex = 0;
        }
    }
}

//called this function when user click on Service
function OnClickService(bsId, ProfileId, control) {
    abortTimer();
    $("#timeInter").prop('disabled', true);
    addTab(bsId, ProfileId, control);


}

//set latest seleced time from dropdown
function getval(sel) {
    abortTimer();
    globalTime = setInterval(function () { businessServiceRotate() }, sel.value);
}



$(document).ready(function () {

    //when click on tab
    $('#tabs a.tab').live('click', function () {
        // Get the tab name

        var ServiceProfileId = $(this).attr("id").split("_");

        // hide all other tabs
        $("#tabs li").removeClass("current");

        // show current tab
        $(this).parent().addClass("current");

        //Render Business Tree
        renderService(ServiceProfileId[0], "S", ServiceProfileId[1])
    });

    // when click on tab remove icon
    $("#tabs a.remove").live("click", function () {

        // restart the Tree rotation while close the last tab
        if ($("#tabs li").length == 1) {

            // here setting timer interval 
            abortTimer();
            globalTime = setInterval(function () { businessServiceRotate() }, $("#timeInter").attr("value"));

            //switch enable
            $(".lcs_switch").addClass("lcs_on");
            $(".lcs_switch").removeClass("lcs_disabled lcs_off");
            $("#timeInter").prop('disabled', false);

            // get first list id
            var previousTabId = $('#tabs li:first-child').find(".tab").attr("id")
            // after remove the render previus tab id tree
            if (previousTabId != "undefined") {
                var ServiceProfileId = previousTabId.split("_");
                renderService(ServiceProfileId[0], "S", ServiceProfileId[1])
            }

            //set active slider of service
            globalIndex = 0;
            var lastService = $(".profileul li")[globalIndex];
            $(lastService).addClass("activeService");

        }
        else {

            // get last list id
            var previousTabId = $('#tabs li:last-child').find(".tab").attr("id")
            // after remove the render previus tab id tree
            if (previousTabId != "undefined") {
                var ServiceProfileId = previousTabId.split("_");
                renderService(ServiceProfileId[0], "S", ServiceProfileId[1])
            }
        }

        //Remove the tab
        $(this).parent().remove();

        // if there is no current tab and if there are still tabs left, show the first one
        if ($("#tab li.current").length == 0 && $("#tabs li").length > 0) {
            // find the first tab
            var firsttab = $("#tabs li:last-child");
            firsttab.addClass("current");

        }
    });

});


//add tab
function addTab(id, pfid, cntrol) {

    var serviceName = cntrol.text.replace(/\s+/g, '');
    // var ServiceId = serviceName + "_" + id + "_" + pfid;
    var ServiceId = id + "_" + pfid;


    //If tab already exist in the list, return
    if ($("#" + ServiceId).length != 0) {
        return;
    }

    //If tab count is five then remove first tab
    if ($("#tabs li").length == 5) {
        $('#tabs li:first-child').find(".tab").parent().remove();
    }
    // hide other tabs
    $("#tabs li").removeClass("current");
    //$("#tabContent div").hide();

    // add new tab and related content
    $("#tabs").append("<li class='current'><a class='tab' id='" + ServiceId + "' href='#'>" + serviceName + "</a>&nbsp;<a href='#' class='remove'>x</a></li>");
    //$("#tabContent").append(str);

    //disable switch button while addding first tab
    if ($("#tabs li").length == 1) {
        $(".lcs_switch").removeClass("lcs_on");
        $(".lcs_switch").addClass("lcs_disabled lcs_off");
    }

    //remove the active slider of service
    $(".profileul li").removeClass("activeService");

    //render bs  tree
    renderService(id, "S", pfid);

}

// to be called when you want to stop the timer
function abortTimer() {
    clearInterval(globalTime);
}


//=====================Busines Service Tree=========================
function renderService(id, apptype, profileId) {
    //isNewProfile = false;
    if (apptype == "S") {
        $.ajax({
            type: "POST",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: true,
            data: JSON.stringify({ 'id': id, 'apptype': apptype, 'profileId': profileId }),
            url: "ServicesMonitor.aspx/GetServiceJsonfromDB",
            success: function (msg) {
                var value = msg.d.split('#');
                globalstring = value[1];
                var JsonObj = eval('(' + value[0] + ')');
                JsonForFullView = JsonObj;
                treeShow(JsonObj);
                return true;
            },
            failure: function () {
                return false;
            }
        });
    }
}


//draw BIT daigram
function treeShow(root1) {
    d3.select("svg").remove();
    i = 0, root;
    var duration = 750;
    var viewerWidth = 1035;
    var viewerHeight = 800;
    //var viewerWidth = 600;
    //var viewerHeight = 500;
    //Define the zoom function for the zoomable tree

    function zoom() {
        vis.attr("transform", "translate(" + d3.event.translate + ")scale(" + d3.event.scale + ")");
    }

    // Function to reset Zoom level
    function resetZoom() {
        d3.select("#bsbody3").select("svg").select("g")
       .transition().duration(750)
       .attr("transform", root1.name.length < 6 ? "translate(30,30)scale(0.85)" : "translate(90,30)scale(0.85)");
    };
    d3.select("#btnReset").on("click", resetZoom);

    // Function to center node when clicked/dropped so node doesn't get lost when collapsing/moving with large amount of children.
    function centerNode(source) {
        scale = zoomListener.scale();
        x = -source.y0;
        y = -source.x0;
        x = 0;
        //y = y * scale + viewerHeight / 2; // Commented - Kuntesh Thakker - 7-04-2014 as it disturbs the RTO RPO graph design
        y = 0;
        d3.select('g').transition()
        .duration(duration)
        .attr("transform", "translate(" + x + "," + y + ")scale(" + scale + ")");
        zoomListener.scale(scale);
        zoomListener.translate([x, y]);
    }

    // define the zoomListener which calls the zoom function on the "zoom" event constrained within the scaleExtents
    var zoomListener = d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom);

    var tree = d3.layout.tree()
    .size([viewerHeight, viewerWidth]);

    var diagonal = d3.svg.diagonal()
    .projection(function (d) {
        return [d.y, d.x];
    });

    var vis = d3.select("#bsbody3").append("svg:svg")
    .attr("width", viewerWidth)
    .attr("height", viewerHeight)
    .call(d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom)).on("dblclick.zoom", null)
    .append("g")
    //zoomListener.translate([100, 50]).scale(0.85);
    .attr("transform", root1.name.length < 6 ? "translate(30,30)scale(0.85)" : "translate(90,30)scale(0.85)");

    var root = root1;
    root.x0 = viewerHeight / 2;
    root.y0 = 0;

    //toggle node
    //function toggleAll(d) {
    //    if (d.children) {
    //        d.children.forEach(toggleAll);
    //        toggleOnLoad(d);
    //    }
    //}
    // Initialize the display to show a few nodes.
    //root.children.forEach(toggleAll);
    update(root);
    //centerNode(root);

    //Update node
    function update(source) {
        var duration = d3.event && d3.event.altKey ? 5000 : 500;

        // Compute the new tree layout.
        var nodes = tree.nodes(root).reverse();

        // Normalize for fixed-depth.
        nodes.forEach(function (d) { d.y = d.depth * 180; });

        // Update the nodes…
        var node = vis.selectAll("g.node")
        .data(nodes, function (d) { return d.id || (d.id = ++i); });

        // Enter any new nodes at the parent's previous position.
        var nodeEnter = node.enter().append("svg:g")
        //.attr("opacity", function (d) { return hidenodeonload(d.name, d.ImpactType); })
        .attr("class", "node")
        .attr("transform", function (d) { return "translate(" + source.y0 + "," + source.x0 + ")"; })
        .on("click", function (d) { GetJsonFromNodeRelation(d); toggleOnClick(d); update(d); });

        nodeEnter.append("svg:circle")
        .attr("r", 1e-6)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        //if (level == 0) {
        nodeEnter.append("svg:text")
       .attr("x", function (d) { return returnx(d); })
       .attr("dy", function (d) { return returny(d); })
       .attr("text-anchor", function (d) { return returnTextAnchor(d); })
       .attr("class", function (d) { return returnTextAnchorClass2(d.name, d.ImpactType); })
       .text(function (d) {

           if (d.name == undefined)
               return "";
           if (d.name.indexOf("$") != -1) {
               //var lastIndex = d.name.lastIndexOf("$");
               return d.name.substring(d.name.lastIndexOf("$") + 1);
           }

           else {
               return d.name.substring(d.name.substring(d.name.lastIndexOf('@') + 1));
           }
       })
    .style("fill-opacity", 1e-6);

        nodeEnter.append("svg:image")
                .attr("xlink:href", function (d) {
                    //if (d.logo == "") {

                    if (d.name != undefined) {

                        var iscap = d.name.substring(d.name.lastIndexOf('@') + 1, d.name.lastIndexOf('$'))
                        if (iscap.indexOf("_") >= 0) {
                            apppre = iscap.split("_")[0] + "_";
                            if (apppre.indexOf("^") >= 0) {
                                apppre = apppre.split('^')[1];
                            }
                        }

                        if (apppre.indexOf("IS") >= 0)
                            return GetNodeImgPathByName2("IS", d.ImpactType);
                        else if (apppre.indexOf("F_") >= 0)
                            return GetNodeImgPathByName2("F", d.ImpactType);
                        else if (apppre.indexOf("I_") >= 0)
                            return GetNodeImgPathByName2("I", d.ImpactType);
                        else if (apppre.indexOf("S_") >= 0)
                            return GetNodeImgPathByName2("S", d.ImpactType);
                        else if (apppre.indexOf("ICSV_") >= 0)
                            return GetNodeImgPathByName2("ICSV", d.ImpactType);
                        else if (apppre.indexOf("ICD_") >= 0)
                            return GetNodeImgPathByName2("ICD", d.ImpactType);
                        else if (apppre.indexOf("ICR_") >= 0)
                            return GetNodeImgPathByName2("ICR", d.ImpactType);
                        else if (apppre.indexOf("ICSM_") >= 0)
                            return GetNodeImgPathByName2("ICSM", d.ImpactType);
                        else if (apppre.indexOf("ICSS_") >= 0)
                            return GetNodeImgPathByName2("ICSS", d.ImpactType);
                        else if (apppre.indexOf("Q_") >= 0)
                            return GetNodeImgPathByName2("Q", d.ImpactType);
                        else if (apppre.indexOf("NQ_") >= 0)
                            return GetNodeImgPathByName2("NQ", d.ImpactType);
                        else
                            return GetNodeImgPathByName2("");

                    }
                    //}
                    //else
                    //    return d.logo;

                })
                .attr("x", function (d) { return d.children || d._children ? -6 : -6; })
                .attr("y", function (d) { return d.children || d._children ? GetRadius2(d.name, d.ImpactType) : GetRadius2(d.name, d.ImpactType); }) // d._children ? "-0.5em" : "-0.5em"; })
                .attr("height", function (d) {
                    return d.logoheight || GetNodeImgHightByName(d.name, d.ImpactType);
                })
                .attr("width", function (d) {
                    return d.logowidth || GetNodeImgWidthByName(d.name, d.ImpactType);
                })
                .on("contextmenu", function (d, nodes, links) {
                    apptype = "";
                    lastRightClickNode = d;
                    var linkToPage = "";
                    linkToPage = "../ImpactAnalysis/ImpactConfiguration.aspx";
                    if (d.name != "") {
                        if (d.name.indexOf('@') >= 0) {
                            var Apptype = d.name.substring(d.name.lastIndexOf('@') + 1)
                            if (Apptype.split('_')[0] == "S")
                                apptype = "S";
                        }
                        else {
                            if (d.name.split('$')[0].split('_')[0] == "S")
                                apptype = "S";
                        }
                    }

                    if (apptype == "S" && apptype.lastIndexOf("S") >= 0) {
                        context.attach(("svg image"), [
                        {
                            text: "Add Service", action: function (e) {
                                //clickNewService = false;
                                openAddServicePopup(d.name);

                            }
                        }
                        ,
                        {
                            //text: "Add/Update Impact", action: function (e) {
                            //    openAddupdateRulesPopup(d, node, lastRightClickNode);
                            //}
                            text: "Add/Update Impact ", href: "javascript:void(0);", action: function (e) {

                                var FinalNodeValue = lastRightClickNode.name;
                                var lastRightClickNode_ParentId;
                                var IDChildNameArr;
                                var lastRightClickNodeId;
                                var RightClickChildAppType;
                                if (isEmpty(FinalNodeValue)) {
                                    var charCount = count_Char(FinalNodeValue, '@');

                                    if (charCount > 0) {
                                        var strChildresult = FinalNodeValue.substring(FinalNodeValue.lastIndexOf('@') + 1);
                                        var strParentresult2 = FinalNodeValue.split('@');
                                        if (strParentresult2.length > 0) {
                                            for (var i = 0; i < strParentresult2.length; i++) {
                                                var index = strParentresult2[i];
                                                if (isEmpty(index)) {
                                                    var cCount = count_Char(index, '_');
                                                    if (cCount > 0) {
                                                        lastRightClickNode_ParentId = index.split('_')[1];
                                                        break;
                                                    }

                                                }

                                            }
                                        }
                                        var strchildArray = strChildresult.split('$');

                                        if (strchildArray.length > 0) {
                                            for (var i = 0; i < strchildArray.length; i++) {
                                                var indexVal = strchildArray[i];
                                                if (isEmpty(indexVal)) {
                                                    var cCount = count_Char(index, '_');
                                                    if (cCount > 0) {
                                                        IDChildNameArr = indexVal.split('_');
                                                        if (IDChildNameArr.length > 0) {
                                                            lastRightClickNodeId = IDChildNameArr[1];
                                                            RightClickChildAppType = IDChildNameArr[0];

                                                            break;
                                                        }
                                                    }
                                                }
                                            }

                                        }
                                        if (RightClickChildAppType == 'S') {
                                            openRadWindow(linkToPage + "?" + "RuleType=BStoBs" + "&" + "ParentBsId=" + lastRightClickNode_ParentId + "&" + "ChildBSId=" + lastRightClickNodeId);
                                        }
                                        if (RightClickChildAppType == 'IS') {
                                            var ParentInfraPRorDRNode = lastRightClickNode.parent;
                                            var ParentInfraNode = lastRightClickNode.parent.parent;
                                            var ParentBFNode = lastRightClickNode.parent.parent.parent;
                                            var ParentBsNode = lastRightClickNode.parent.parent.parent.parent;
                                            var INfraCompId; var INfraObjectId; var BfId; var BSId; var IPComp;
                                            if (isEmpty(ParentInfraPRorDRNode.name)) {
                                                IPComp = GetInfraComponent(lastRightClickNode.name);
                                                INfraCompId = GetInfraCompId(ParentInfraPRorDRNode.name);
                                                INfraObjectId = GetInfraObjectId(ParentInfraNode.name);
                                                BfId = GetBusinessBFId(ParentBFNode.name);
                                                BSId = GetBusinessBSId(ParentBsNode.name);
                                            }
                                            openInfratoBSRadWindow(linkToPage + "?" + "INfraCompId=" + INfraCompId + "&" + "INfraObjectId=" + INfraObjectId + "&" + "BFId=" + BfId + "&" + "BSId=" + BSId + "&" + "IPComp=" + IPComp);

                                        }

                                    }

                                }


                            }
                        }
                        ,
                        {
                            text: "Delete Service", href: "javascript:void(0);", action: function (e) {
                                deleteServiceNode();
                            }
                        }

                        ]);

                        $('[id$=bsbody]').on("contextmenu", true);
                    }

                });



        // Transition nodes to their new position.
        var nodeUpdate = node.transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + d.y + "," + d.x + ")"; });

        nodeUpdate.select("circle")
        .attr("r", 0)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeUpdate.select("text")
        .style("fill-opacity", 1);

        // Transition exiting nodes to the parent's new position.
        var nodeExit = node.exit().transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + source.y + "," + source.x + ")"; })
        .remove();

        nodeExit.select("circle")
        .attr("r", 1e-6);

        nodeExit.select("text")
        .style("fill-opacity", 1e-6);

        // Update the links…
        var link = vis.selectAll("path.link")
        .data(tree.links(nodes), function (d) { return d.target.id; });

        // Enter any new links at the parent's previous position.
        link.enter().insert("svg:path", "g")
        //.attr("class", function (d) { return linkColor(d); })

        .attr("class", "link")
        //.attr("opacity", function (d) { return hideLinkload(d); })

        .style("stroke", function (d) { return linkColor(d); })
        .attr("d", function (d) {
            var o = { x: source.x0, y: source.y0 };
            return diagonal({ source: o, target: o });
        })
        .transition()
        .duration(duration)
        .attr("d", diagonal);

        // Transition links to their new position.
        link.transition()
        .duration(duration)
        .attr("d", diagonal);

        // Transition exiting nodes to the parent's new position.
        link.exit().transition()
        .duration(duration)
        .attr("d", function (d) {
            var o = { x: source.x, y: source.y };
            return diagonal({ source: o, target: o });
        })
        .remove();

        // Stash the old positions for transition.
        nodes.forEach(function (d) {
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }

    // Toggle children when page loads.
    function toggleOnLoad(d) {
        if (d.children) {
            if (d.hide == "hide") {
                d._children = d.children;
                d.children = null;
            }
        } else {
            d.children = d._children;
            d._children = null;
        }
    }

    //For Link Color change
    function linkColor(d) {

        if (d != undefined) {
            var StrSource = d.source.ImpactType;
            var StrTarget = d.target.ImpactType;

            var Str = StrSource + "_" + StrTarget;

            switch (Str) {
                case "PIBS_NABS":
                    return "#1b75bd";
                    break;
                case "PIBS_PIBS":
                    return "#ffb406";
                    break;
                case "PIBS_MIBS":
                    return "#fb5a0c";
                    break;
                case "PIBS_TIBS":
                    return "#FF0000";
                    break;
                case "MIBS_NABS":
                    return "#1b75bd";
                    break;
                case "MIBS_MIBS":
                    return "#fb5a0c";
                    break;
                case "MIBS_PIBS":
                    return "#ffb406";
                    break;
                case "MIBS_TIBS":
                    return "#FF0000";
                    break;
                case "TIBS_NABS":
                    return "#1b75bd";
                    break;
                case "TIBS_TIBS":
                    return "#FF0000";
                    break;
                case "TIBS_PIBS":
                    return "#ffb406";
                    break;
                case "TIBS_MIBS":
                    return "#fb5a0c";
                    break;
                case "NABS_NABS":
                    return "#1b75bd";
                    break;
                case "NABS_PIBS":
                    return "#1b75bd";
                    break;
                case "NABS_MIBS":
                    return "#1b75bd";
                    break;
                case "NABS_TIBS":
                    return "#1b75bd";
                    break;
                case "PIBS_NABF":
                    return "#1b75bd";
                    break;
                case "PIBS_PIBF":
                    return "#ffb406";
                    break;
                case "PIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "PIBS_TIBF":
                    return "#FF0000";
                    break;
                case "MIBS_NABF":
                    return "#1b75bd";
                    break;
                case "MIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "MIBS_PIBF":
                    return "#ffb406";
                    break;
                case "MIBS_TIBF":
                    return "#FF0000";
                    break;
                case "TIBS_NABF":
                    return "#1b75bd";
                    break;
                case "TIBS_TIBF":
                    return "#FF0000";
                    break;
                case "TIBS_PIBF":
                    return "#ffb406";
                    break;
                case "TIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "NABS_NABF":
                    return "#1b75bd";
                    break;
                case "NABS_PIBF":
                    return "#1b75bd";
                    break;
                case "NABS_MIBF":
                    return "#1b75bd";
                    break;
                case "NABS_TIBF":
                    return "#1b75bd";
                    break;
                case "PIBF_NABF":
                    return "#1b75bd";
                    break;
                case "PIBF_PIBF":
                    return "#ffb406";
                    break;
                case "PIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "PIBF_TIBF":
                    return "#ff0000";
                    break;
                case "MIBF_NABF":
                    return "#1b75bd";
                    break;
                case "MIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "MIBF_PIBF":
                    return "#ffb406";
                    break;
                case "MIBF_TIBF":
                    return "#FF0000";
                    break;
                case "TIBF_NABF":
                    return "#1b75bd";
                    break;
                case "TIBF_TIBF":
                    return "#FF0000";
                    break;
                case "TIBF_PIBF":
                    return "#ffb406";
                    break;
                case "TIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "NABF_NABF":
                    return "#1b75bd";
                    break;
                case "NABF_PIBF":
                    return "#1b75bd";
                    break;
                case "NABF_MIBF":
                    return "#1b75bd";
                    break;
                case "NABF_TIBF":
                    return "#1b75bd";
                    break;
                case "PIBF_NAI":
                    return "#1b75bd";
                    break;
                case "PIBF_PII":
                    return "#ffb406";
                    break;
                case "PIBF_MII":
                    return "#fb5a0c";
                    break;
                case "PIBF_TII":
                    return "#ff0000";
                    break;
                case "MIBF_NAI":
                    return "#1b75bd";
                    break;
                case "MIBF_MII":
                    return "#fb5a0c";
                    break;
                case "MIBF_PII":
                    return "#ffb406";
                    break;
                case "MIBF_TII":
                    return "#FF0000";
                    break;
                case "TIBF_NAI":
                    return "#1b75bd";
                    break;
                case "TIBF_TII":
                    return "#FF0000";
                    break;
                case "TIBF_PII":
                    return "#ffb406";
                    break;
                case "TIBF_MII":
                    return "#fb5a0c";
                    break;
                case "NABF_NAI":
                    return "#1b75bd";
                    break;
                case "NABF_PII":
                    return "#1b75bd";
                    break;
                case "NABF_MII":
                    return "#1b75bd";
                    break;
                case "NABF_TII":
                    return "#1b75bd";
                    break;
                case "TII_TIICSV":
                    return "#ff0000";
                    break;
                case "TII_NAICSV":
                    return "#1b75bd";
                case "NAI_TIICSV":
                    return "#1b75bd";
                    break;
                case "NAI_NAICSV":
                    return "#1b75bd";
                    break;
                case "TII_TIICD":
                    return "#FF0000";
                    break;
                case "TII_NAICD":
                    return "#1b75bd";
                case "NAI_TIICD":
                    return "#1b75bd";
                    break;
                case "NAI_NAICD":
                    return "#1b75bd";
                    break;
                case "TII_TIICR":
                    return "#FF0000";
                    break;
                case "TII_NAICR":
                    return "#1b75bd";
                case "NAI_TIICR":
                    return "#1b75bd";
                    break;
                case "NAI_NAICR":
                    return "#1b75bd";
                    break;
                case "NAI_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICSV_TIIS":
                    return "#FF0000";
                    break;
                case "TIICSV_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICSV_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICSV_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICD_TIIS":
                    return "#FF0000";
                    break;
                case "TIICD_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICD_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICD_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICR_TIIS":
                    return "#FF0000";
                    break;
                case "TIICR_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICR_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICR_NAIS":
                    return "#1b75bd";
                    break;
                case "NABF_NAICSV":
                    return "#1b75bd";
                    break;
                case "PIBF_NAICSV":
                    return "#1b75bd";
                    break;
                case "TII_TIICSM":
                    return "#FF0000";
                    break;
                case "TIICSM_TIICSS":
                    return "#FF0000";
                    break;
                case "TIQ_TIQQ":
                    return "#FF0000";
                    break;
                case "TII_TIQ":
                    return "#FF0000";
                    break;
                default:

                    return "#1b75bd";
            }
        }
    }

    //Toggle children when node is clicked.
    function toggleOnClick(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
        // If the node has a parent, then collapse its child nodes
        // except for this clicked node.
        if (level == 1) {
            if (d.parent) {
                if (d.children) {
                    d.parent.children.forEach(function (element) {
                        if (d != element) {
                            collapse(element);
                        }
                    });
                }
            }
        }
    }

    //return x value for node
    function returnx(d) {
        var xPos = null;
        if (d.hide == "hide") {
            xPos = 30;
        }
        else if (d.children && d.hide != "hide") {
            xPos = 30;
        }
        else {
            xPos = 30;
        }
        return xPos;
    }

    //return y value for node
    function returny(d) {
        var yPos = null;
        if (d.hide == "hide") {
            yPos = ".35em";
        }
        else if (d.children && d.hide != "hide") {
            yPos = "-2em"
        }
        else {
            yPos = ".35em";
        }
        return yPos;
    }

    //return TextAnchor for node
    function returnTextAnchor(d) {
        var textAnchor = null;
        if (d.hide == "hide") {
            textAnchor = "start";
        }
        else if (d.children && d.hide != "hide") {
            textAnchor = "end";
        }
        else {
            textAnchor = "start";
        }
        return textAnchor;
    }

    // Gets Json for infraObjects details from node relation
    function GetJsonFromNodeRelation(d) {
        if (d.hide == "hide" && level == 0) {
            level = level + 1;
            var requiredClickedName = null;
            if (d.logo)
                requiredClickedName = d.name + "/Hide/logo/red" + ":";
            else
                requiredClickedName = d.name + "/Hide" + ":";

            var splitNodeRelation = globalstring.split(";");
            if (splitNodeRelation.length > 0) {
                for (var i = 0; i < splitNodeRelation.length; i++) {
                    var index = splitNodeRelation[i].indexOf(requiredClickedName);
                    if (index == 0) {
                        var infraObjectRel = splitNodeRelation[i];
                        var splitInfraObjectRel = infraObjectRel.split(":");
                        var componentArray = splitInfraObjectRel[1].split(",");
                        if (componentArray.length > 0) {
                            for (var j = 0 ; j < componentArray.length; j++) {
                                var requiredComponentName = componentArray[j] + ":";
                                for (var k = i; k < splitNodeRelation.length; k++) {
                                    var componentArrayIndex = splitNodeRelation[k].indexOf(requiredComponentName)
                                    if (componentArrayIndex == 0) {
                                        infraObjectRel = infraObjectRel + ";" + splitNodeRelation[k];
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                var requiredNodeRelation = infraObjectRel;
                if (requiredNodeRelation != null) {
                    $('#bsbody3').html("");
                    renderAjaxDataForInfraObjects(requiredNodeRelation);
                    isbtnReloadHide = true;
                    d3.select("#btnReload").attr("style", "display:inline-block");
                }
            }
        }
    }

    // Resets tree to default full view
    function backToFullView() {
        $('#bsbody3').html("");
        level = 0;
        if (isbtnReloadHide)
            d3.select("#btnReload").attr("style", "display:none");
        treeShow(JsonForFullView);
    }
    d3.select("#btnReload").on("click", backToFullView);

    //collapse other node while expanding current clicked/selected node
    function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d._children.forEach(collapse);
            d.children = null;
        }
    }
}
// Ends ======================


///================== Return Node textAnchor class as per type===================

function returnTextAnchorClass2(name, impacttype) {
    if (name != null) {
        var iscap = name.substring(name.lastIndexOf('@') + 1, name.lastIndexOf('$'))
        if (iscap.indexOf("_") >= 0) {
            apppre = iscap.split("_")[0];
            if (apppre.indexOf("^") >= 0) {
                apppre = apppre.split('^')[1];
            }
        }

        var Str = apppre + "_" + impacttype;

        switch (Str) {

            case "S_PIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_MIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_TIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_NABS":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_PIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_MIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_TIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_NABF":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_PII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_MII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_TII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_NAI":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSV_TIICSV":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSV_NAICSV":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICD_TIICD":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICD_NAICD":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICR_TIICR":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICR_NAICR":
                return textAnchorClass = "impact-text-black";
                break;
            case "IS_TIIS":
                return textAnchorClass = "impact-text-red";
                break;
            case "IS_NAIS":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSM_NAICSM":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSM_TIICSM":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSS_NAICSS":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSS_TIICSS":
                return textAnchorClass = "impact-text-red";
                break;
            case "NQ_TIQQ":
                return textAnchorClass = "impact-text-red";
                break;
            default:
                return textAnchorClass = "impact-text-black";
                break;
        }
    }
}

///================== Hide Node as per impact type===================

function hidenodeonload(name, impacttype) {
    if (name != null) {
        var strName;
        if (name.indexOf("^") >= 0) {
            var iscap = name.split('^');
            strName = iscap[1].split("$")[0]
        }
        else {
            strName = name.substring(name.lastIndexOf('@') + 1, name.indexOf('$'))
        }


        var splitArra = strName.split('_');

        var Str = splitArra[0] + "_" + impacttype;
        switch (Str) {
            case "S_PIBS":
                return 1;
                break;
            case "S_MIBS":
                return 1;
                break;
            case "S_TIBS":
                return 1;
                break;
            case "S_NABS":
                return 1;
                break;
            case "F_PIBF":
                return 1;
                break;
            case "F_MIBF":
                return 1;
                break;
            case "F_TIBF":
                return 1;
                break;
            case "F_NABF":
                return 0;
                break;
            case "I_PII":
                return 1;
                break;
            case "I_MII":
                return 1;
                break;
            case "I_TII":
                return 1;
                break;
            case "I_NAI":
                return 0;
                break;
            case "ICSV_TIICSV":
                return 1;
                break;
            case "ICSV_NAICSV":
                return 0;
                break;
            case "ICD_TIICD":
                return 1;
                break;
            case "ICD_NAICD":
                return 0;
                break;
            case "ICR_TIICR":
                return 1;
                break;
            case "ICR_NAICR":
                return 0;
                break;
            case "IS_TIIS":
                return 1;
                break;
            case "IS_NAIS":
                return 0;
                break


        }
    }

}

///================== Hide Link(Line) as per impact type===================

function hideLinkload(d) {
    if (d != null) {
        var StrSource = d.source.ImpactType;
        var StrTarget = d.target.ImpactType;

        var Str = StrSource + "_" + StrTarget;


        switch (Str) {

            case "PIBS_NABS":
                return 1;
                break;
            case "PIBS_PIBS":
                return 1;
                break;
            case "PIBS_MIBS":
                return 1;
                break;
            case "PIBS_TIBS":
                return 1;
                break;
            case "MIBS_NABS":
                return 1;
                break;
            case "MIBS_MIBS":
                return 1;
                break;
            case "MIBS_PIBS":
                return 1;
                break;
            case "MIBS_TIBS":
                return 1;
                break;
            case "TIBS_NABS":
                return 1;
                break;
            case "TIBS_TIBS":
                return 1;
                break;
            case "TIBS_PIBS":
                return 1;
                break;
            case "TIBS_MIBS":
                return 1;
                break;
            case "NABS_NABS":
                return 1;
                break;
            case "NABS_PIBS":
                return 1;
                break;
            case "NABS_MIBS":
                return 1;
                break;
            case "NABS_TIBS":
                return 1;
                break;
            case "PIBS_NABF":
                return 0;
                break;
            case "PIBS_PIBF":
                return 1;
                break;
            case "PIBS_MIBF":
                return 1;
                break;
            case "PIBS_TIBF":
                return 1;
                break;
            case "MIBS_NABF":
                return 0;
                break;
            case "MIBS_MIBF":
                return 1;
                break;
            case "MIBS_PIBF":
                return 1;
                break;
            case "MIBS_TIBF":
                return 1;
                break;
            case "TIBS_NABF":
                return 0;
                break;
            case "TIBS_TIBF":
                return 1;
                break;
            case "TIBS_PIBF":
                return 1;
                break;
            case "TIBS_MIBF":
                return 1;
                break;
            case "NABS_NABF":
                return 0;
                break;
            case "NABS_PIBF":
                return 1;
                break;
            case "NABS_MIBF":
                return 1;
                break;
            case "NABS_TIBF":
                return 1;
                break;
            case "PIBF_NABF":
                return 0;
                break;
            case "PIBF_PIBF":
                return 1;
                break;
            case "PIBF_MIBF":
                return 1;
                break;
            case "PIBF_TIBF":
                return 1;
                break;
            case "MIBF_NABF":
                return 0;
                break;
            case "MIBF_MIBF":
                return 1;
                break;
            case "MIBF_PIBF":
                return 1;
                break;
            case "MIBF_TIBF":
                return 1;
                break;
            case "TIBF_NABF":
                return 0;
                break;
            case "TIBF_TIBF":
                return 1;
                break;
            case "TIBF_PIBF":
                return 1;
                break;
            case "TIBF_MIBF":
                return 1;
                break;
            case "NABF_NABF":
                return 0;
                break;
            case "NABF_PIBF":
                return 1;
                break;
            case "NABF_MIBF":
                return 1;
                break;
            case "NABF_TIBF":
                return 1;
                break;
            case "PIBF_NAI":
                return 0;
                break;
            case "PIBF_PII":
                return 1;
                break;
            case "PIBF_MII":
                return 1;
                break;
            case "PIBF_TII":
                return 1;
                break;
            case "MIBF_NAI":
                return 0;
                break;
            case "MIBF_MII":
                return 1;
                break;
            case "MIBF_PII":
                return 1;
                break;
            case "MIBF_TII":
                return 1;
                break;
            case "TIBF_NAI":
                return 0;
                break;
            case "TIBF_TII":
                return 1;
                break;
            case "TIBF_PII":
                return 1;
                break;
            case "TIBF_MII":
                return 1;
                break;
            case "NABF_NAI":
                return 0;
                break;
            case "NABF_PII":
                return 1;
                break;
            case "NABF_MII":
                return 1;
                break;
            case "NABF_TII":
                return 1;
                break;
            case "TII_TIICSV":
                return 1;
                break;
            case "TII_NAICSV":
                return 0;
            case "NAI_TIICSV":
                return 1;
                break;
            case "NAI_NAICSV":
                return 0;
                break;
            case "TII_TIICD":
                return 1;
                break;
            case "TII_NAICD":
                return 0;
            case "NAI_TIICD":
                return 1;
                break;
            case "NAI_NAICD":
                return 0;
                break;
            case "TII_TIICR":
                return 1;
                break;
            case "TII_NAICR":
                return 0;
            case "NAI_TIICR":
                return 1;
                break;
            case "NAI_NAICR":
                return 0;
                break;
            case "NAI_NAIS":
                return 0;
                break;
            case "TIICSV_TIIS":
                return 1;
                break;
            case "TIICSV_NAIS":
                return 0;
                break;
            case "NAICSV_TIIS":
                return 1;
                break;
            case "NAICSV_NAIS":
                return 0;
                break;
            case "TIICD_TIIS":
                return 1;
                break;
            case "TIICD_NAIS":
                return 0;
                break;
            case "NAICD_TIIS":
                return 1;
                break;
            case "NAICD_NAIS":
                return 0;
                break;
            case "TIICR_TIIS":
                return 1;
                break;
            case "TIICR_NAIS":
                return 0;
                break;
            case "NAICR_TIIS":
                return 1;
                break;
            case "NAICR_NAIS":
                return 0;
                break;
            case "NABF_NAICSV":
                return 0;
                break;
            case "PIBF_NAICSV":
                return 1;
                break;


        }
    }
}


///================== Return Node Image as per type===================
function GetNodeImgPathByName2(name, impacttype) {
    var Str = name + "_" + impacttype;
    switch (Str) {

        case "S_PIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_yellow.png";
            break;
        case "S_MIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_orange.png";
            break;
        case "S_TIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_red.png";
            break;
        case "S_NABS":
            return "../Images/ServiceLogo/bussinessservice_icon_green.png";
            break;
        case "F_PIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_yellow.png";
            break;
        case "F_MIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_orange.png";
            break;
        case "F_TIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_red.png";
            break;
        case "F_NABF":
            return "../Images/ServiceLogo/bussinessfunction_icon_green.png";
            break;
        case "I_PII":
            return "../Images/ServiceLogo/infraobject_icon_yellow.png";
            break;
        case "I_MII":
            return "../Images/ServiceLogo/infraobject_icon_orange.png";
            break;
        case "I_TII":
            return "../Images/ServiceLogo/infraobject_icon_red.png";
            break;
        case "I_NAI":
            return "../Images/ServiceLogo/infraobject_icon_green.png";
            break;
        case "ICSV_TIICSV":
            return "../Images/ServiceLogo/ServerImpacted_Icon.png";
            break;
        case "ICSV_NAICSV":
            return "../Images/ServiceLogo/Server_Icon.png";
            break;
        case "ICD_TIICD":
            return "../Images/ServiceLogo/DatabaseImpacted_Icon.png";
            break;
        case "ICD_NAICD":
            return "../Images/ServiceLogo/Database_Icon.png";
            break;
        case "ICR_TIICR":
            return "../Images/ServiceLogo/ReplicationImpacted_Icon.png";
            break;
        case "ICR_NAICR":
            return "../Images/ServiceLogo/Replication_Icon.png";
            break;
        case "IS_TIIS":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "IS_NAIS":
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
        case "ICSM_NAICSM":
            return "../Images/ServiceLogo/Service_Cog_Green1.png";
            break;
        case "ICSM_TIICSM":
            return "../Images/ServiceLogo/Service_Cog_Red1.png";
            break;
        case "ICSS_NAICSS":
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
        case "ICSS_TIICSS":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "S_TIICSS":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "Q_TIQ":
            return "../Images/ServiceLogo/Queue1_Icon_Red.png";
            break;
        case "Q_NAQ":
            return "../Images/ServiceLogo/Queue1_Icon.png";
            break;
        case "NQ_NAQQ":
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
        case "NQ_TIQQ":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "Q_TIQQ":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        default:
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
    }
}


//====================Get Node img Hight =========================
function GetNodeImgHightByName(name, impactType) {
    if (name != null) {
        var iscap = name.substring(name.lastIndexOf('@') + 1, name.lastIndexOf('$'))
        if (iscap.indexOf("_") >= 0) {
            apppre = iscap.split("_")[0];
            if (apppre.indexOf("^") >= 0) {
                apppre = apppre.split('^')[1];
            }
        }

        var Str = apppre + "_" + impactType;

        switch (Str) {
            case "S_PIBS":
                return 47;
                break;
            case "S_MIBS":
                return 47;
                break;
            case "S_TIBS":
                return 47;
                break;
            case "S_NABS":
                return 47;
                break;
            case "F_PIBF":
                return 33;
                break;
            case "F_MIBF":
                return 33;
                break;
            case "F_TIBF":
                return 33;
                break;
            case "F_NABF":
                return 32;
                break;
            case "I_PII":
                return 33;
                break;
            case "I_MII":
                return 33;
                break;
            case "I_TII":
                return 33;
                break;
            case "I_NAI":
                return 32;
                break;
            case "ICSV_TIICSV":
                return 22;
                break;
            case "ICSV_NAICSV":
                return 22;
                break;
            case "ICD_TIICD":
                return 20;
                break;
            case "ICD_NAICD":
                return 20;
                break;
            case "ICR_TIICR":
                return 18;
                break;
            case "ICR_NAICR":
                return 18;
                break;
            case "Q_NAQ":
                return 18;
                break;
            case "Q_TIQ":
                return 18;
                break;
            case "NQ_NAQQ":
                return 14;
                break;
            case "NQ_TIQQ":
                return 14;
                break;
            case "IS_TIIS":
                return 14;
                break;
            case "IS_NAIS":
                return 14;
                break;
            case "ICSM_NAICSM":
                return 18;
                break;
            case "ICSM_TIICSM":
                return 18;
                break;
            case "ICSS_NAICSS":
                return 14;
                break;
            case "ICSS_TIICSS":
                return 14;
                break;
            default:
                return 14;
                break;
        }

    }
}

//====================Get Node img Width =========================
function GetNodeImgWidthByName(name, impactType) {
    if (name != null) {
        var iscap = name.substring(name.lastIndexOf('@') + 1, name.lastIndexOf('$'))
        if (iscap.indexOf("_") >= 0) {
            apppre = iscap.split("_")[0];
            if (apppre.indexOf("^") >= 0) {
                apppre = apppre.split('^')[1];
            }
        }

        var Str = apppre + "_" + impactType;
        switch (Str) {
            case "S_PIBS":
                return 53;
                break;
            case "S_MIBS":
                return 53;
                break;
            case "S_TIBS":
                return 53;
                break;
            case "S_NABS":
                return 47;
                break;
            case "F_PIBF":
                return 33;
                break;
            case "F_MIBF":
                return 33;
                break;
            case "F_TIBF":
                return 33;
                break;
            case "F_NABF":
                return 32;
                break;
            case "I_PII":
                return 33;
                break;
            case "I_MII":
                return 33;
                break;
            case "I_TII":
                return 33;
                break;
            case "I_NAI":
                return 32;
                break;
            case "ICSV_TIICSV":
                return 18;
                break;
            case "ICSV_NAICSV":
                return 18;
                break;
            case "ICD_TIICD":
                return 16;
                break;
            case "ICD_NAICD":
                return 16;
                break;
            case "ICR_TIICR":
                return 18;
                break;
            case "ICR_NAICR":
                return 18;
                break;
            case "IS_TIIS":
                return 14;
                break;
            case "IS_NAIS":
                return 14;
                break;
            case "ICSM_NAICSM":
                return 18;
                break;
            case "ICSM_TIICSM":
                return 18;
                break;
            case "ICSS_NAICSS":
                return 14;
                break;
            case "ICSS_TIICSS":
                return 14;
                break;
            case "Q_NAQ":
                return 18;
                break;
            case "Q_TIQ":
                return 18;
                break;
            case "NQ_NAQQ":
                return 14;
                break;
            case "NQ_TIQQ":
                return 14;
                break;
            default:
                return 14;
                break;
        }


    }
}

//====================Get Node img Hight =========================
function GetRadius2(name, impactType) {
    if (name != null) {


        var iscap = name.substring(name.lastIndexOf('@') + 1, name.lastIndexOf('$'))
        if (iscap.indexOf("_") >= 0) {
            apppre = iscap.split("_")[0];
            if (apppre.indexOf("^") >= 0) {
                apppre = apppre.split('^')[1];
            }
        }

        var Str = apppre + "_" + impactType;

        switch (Str) {
            case "S_PIBS":
                return "-1.6em";
                break;
            case "S_MIBS":
                return "-1.6em";
                break;
            case "S_TIBS":
                return "-1.6em";
                break;
            case "S_NABS":
                return "-1.6em";
                break;
            case "F_PIBF":
                return "-1.2em";
                break;
            case "F_MIBF":
                return "-1.2em";
                break;
            case "F_TIBF":
                return "-1.2em";
                break;
            case "F_NABF":
                return "-1.2em";
                break;
            case "I_PII":
                return "-1.2em";
                break;
            case "I_MII":
                return "-1.2em";
                break;
            case "I_TII":
                return "-1.2em";
                break;
            case "I_NAI":
                return "-1.2em";
                break;
            case "ICSV_TIICSV":
                return "-0.9em";
                break;
            case "ICSV_NAICSV":
                return "-0.9em";
                break;
            case "ICD_TIICD":
                return "-0.9em";
                break;
            case "ICD_NAICD":
                return "-0.9em";
                break;
            case "ICR_TIICR":
                return "-0.9em";
                break;
            case "ICR_NAICR":
                return "-0.9em";
                break;
            case "IS_TIIS":
                return "-0.5em";
                break;
            case "IS_NAIS":
                return "-0.5em";
                break;
            case "ICSM_NAICSM":
                return "-0.9em";
                break;
            case "ICSM_TIICSM":
                return "-0.9em";
                break;
            case "ICSS_NAICSS":
                return "-0.5em";
                break;
            case "ICSS_TIICSS":
                return "-0.5em";
                break;
            case "NQ_NAQQ":
                return "-0.5em";
                break;
            case "NQ_TIQQ":
                return "-0.5em";
                break;
            case "Q_NAQ":
                return "-0.9em";
                break;
            case "Q_TIQ":
                return "-0.9em";
                break;

            default:
                return "-0.5em";
                break;
        }

    }
}








