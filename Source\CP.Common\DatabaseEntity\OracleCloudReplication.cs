﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "OracleCloudReplication", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class OracleCloudReplication : BaseEntity
    {
        private ReplicationBase _replicationBase = new ReplicationBase();

        #region Properties

        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string TenancyName { get; set; }

        [DataMember]
        public string OCID { get; set; }

        [DataMember]
        public string UserId { get; set; }

        [DataMember]
        public string HomeRegion { get; set; }

        [DataMember]
        public string CompartmentName { get; set; }

        [DataMember]
        public string InstanceName { get; set; }

        [DataMember]
        public string IsInstanceRoot { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }
        }

        #endregion Properties
    }
}
