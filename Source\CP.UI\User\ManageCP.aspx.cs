﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.ExceptionHandler;
using log4net;
using CP.Common.Shared;
using CP.UI.Code.Base;
using CP.Helper;

namespace CP.UI
{
    public partial class ManageCP : BasePage
    {

        private readonly ILog _logger = LogManager.GetLogger(typeof(Login));
        
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected override void OnLoad(EventArgs e)
        {
            Utility.setDBParameterPrefix();

            if (!IsPostBack)
            {
                try
                {
                    
                }
                catch (Exception ex)
                {
                    var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                    _logger.Info("Exception Occured while populating company names :" + ex.InnerException.Message);
                    ExceptionManager.Manage(bcms);
                }
            }
        }

        protected void lvUser_ItemEditing(object sender, ListViewEditEventArgs e)
        {
           
        }

        protected void lvUser_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton unlock = e.Item.FindControl("ImgUnlock") as ImageButton;
            Label lblStatus = e.Item.FindControl("lblStatus") as Label;

            if (lblStatus.Text == "0")
                lblStatus.Text = "Locked";
            else if(lblStatus.Text == "1")
                lblStatus.Text = "Active";
            
        }

        protected void lvUser_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "Click")
            {
                ImageButton unlock = e.Item.FindControl("ImgUnlock") as ImageButton;
                Label lblUserID = e.Item.FindControl("lblUserID") as Label;
                Label lblLoginName = e.Item.FindControl("lblLoginName") as Label;

                hdnUserName.Value = lblLoginName.Text;
                hdnUserId.Value = lblUserID.Text;
                modelbg.Visible = true;
                pnlUnlock.Visible = true;
            }
        }

        protected void lvUser_PreRender(object sender, EventArgs e)
        {            
            PopulateListView();
        }

        public void PopulateListView()
        {
            var userList = Facade.GetAlUsers();
            if (userList != null && userList.Count > 0)
            {
                var lockedusers = (from a in userList where a.IsActive == 0 && a.Role == UserRole.SuperAdmin select a).ToList();
                if (lockedusers != null && lockedusers.Count > 0)
                {
                    lvUser.Items.Clear();
                    lvUser.DataSource = lockedusers;
                    lvUser.DataBind();
                }
                else
                {
                    lvUser.Items.Clear();
                    lvUser.DataSource = null;
                    lvUser.DataBind();
                }
            }
            else
            {
                lvUser.Items.Clear();
                lvUser.DataSource = null;
                lvUser.DataBind();
            }
        }

        protected void BtnUnlock_Click(object sender, EventArgs e)
        {
            bool unlockstatus = false;
            string unlock = "PT" + hdnUserName.Value.Trim() + System.DateTime.Now.ToString("ddMMyy") + "UNLOCK";

            try
            {

                if (!string.IsNullOrEmpty(txtUserCredential.Text))
                {
                    if (CryptographyHelper.Md5Decrypt(txtUserCredential.Text).Equals(unlock))
                    {
                        unlockstatus = Facade.UnLockUserAccount(Convert.ToInt32(hdnUserId.Value));
                    }
                    else
                    {
                        lblUnlockUserMsg.Text = "Provided User credential does not match with unlock code";
                        lblUnlockUserMsg.ForeColor = System.Drawing.Color.DarkRed;
                        unlockstatus = false;
                    }
                }
                else
                {
                    lblUnlockUserMsg.Text = "Credential to unlock super user is not provided";
                    lblUnlockUserMsg.ForeColor = System.Drawing.Color.DarkRed;
                    unlockstatus = false;
                }
            }
            catch (Exception ex)
            {
                lblUnlockUserMsg.Text = "Error while unlocking User";
                lblUnlockUserMsg.ForeColor = System.Drawing.Color.DarkRed;
            }

            if (unlockstatus)
            {
                lblUnlockUserMsg.Text = "User has been unlocked successfully";
                lblUnlockUserMsg.ForeColor = System.Drawing.Color.Green;
            }
            lblUnlockUserMsg.Visible = true;
        }

        protected void LkbtnCloseUnlockUser_Click(object sender, EventArgs e)
        {
            ClearField();
            modelbg.Visible = false;
            pnlUnlock.Visible = false;
        }

        protected void btnClose_Click(object sender, EventArgs e)
        {
            ClearField();
            modelbg.Visible = false;
            pnlUnlock.Visible = false;
        }

        public void ClearField()
        {
            hdnUserName.Value = string.Empty;
            hdnUserId.Value = string.Empty;
            txtUserCredential.Text = string.Empty;
            lblUnlockUserMsg.Text = string.Empty;
            lblUnlockUserMsg.Visible = false;
        }
        
        public override void PrepareView()
        {

        }

    }
}