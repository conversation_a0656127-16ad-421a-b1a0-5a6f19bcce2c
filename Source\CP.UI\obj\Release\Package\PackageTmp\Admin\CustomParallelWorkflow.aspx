﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CustomParallelWorkflow.aspx.cs"
    Inherits="CP.UI.Admin.CustomParallelWorkflow" %>

<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<!DOCTYPE html>
<html>
<head id="Head1" runat="server">
    <title>Continuity Patrol :: CustomParallelWorkflow</title>
    
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/jquery.slimscroll.min.js"></script>
    <script src="../Script/CustomParallelWorkflow.js"></script>
    <script src="../Script/timeliner.js"></script>
    <script src="../Script/colorbox.js"></script>
    <link href="../App_Themes/CPTheme/screen.css" rel="stylesheet" />
    
</head>
<body>
    <div id="scrolling_div" style="height:470px;overflow-y: scroll;">
    <form id="form1" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnablePartialRendering="True">
        </asp:ScriptManager>
        <asp:UpdatePanel ID="Updatepanel3" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
            <Triggers>
                <asp:AsyncPostBackTrigger ControlID="Timerworkflow" EventName="Tick" />
            </Triggers>
            <ContentTemplate>
                <asp:Timer runat="server" ID="Timerworkflow" Interval="5000" OnTick="UpdateTimerTick"
                    Enabled="false" />
                <div class="modal-body">
                    <div class="row layout-timeline">
                        <div class="center">
                            <asp:Label ID="lblWorkflowNameInfra" runat="server" CssClass="text-bold text-primary"></asp:Label>
                        </div>
                        <div class="container">
                            <div class="innerAll">
                                <asp:ListView ID="lvworkflow" Visible="true" runat="server" OnItemCommand="lvworkflowCommandClick">
                                    <LayoutTemplate>

                                        <div id="timelineContainer_2" class="timelineContainer">
                                            <div class="timelineToggle">
                                                <p><a class="expandAll">+</a></p>
                                            </div>
                                            <ul class="timeline">
                                                <span class="date box-generic">Start</span>
                                                <asp:PlaceHolder ID="itemPlaceholder" runat="server"></asp:PlaceHolder>
                                                <span class="date box-generic" style="padding: 3px 9px ! important;">End</span>
                                            </ul>
                                        </div>
                                    </LayoutTemplate>
                                    <ItemTemplate>

                                        <li class="active">
                                            <div>
                                                <span class="type glyphicons">
                                                    <asp:Image ID="Image1" runat="server" ImageUrl='<%# ImageActionType(Eval("Type")) %>' Width="20px" />
                                                </span>
                                                <span></span>
                                            </div>
                                            <div class="media margin-none">
                                                <div class="timelineMajor">
                                                    <dl class="timelineMinor">
                                                        <div class="media-body">
                                                            <div class="widget widget-heading-simple widget-body-white margin-none">
                                                                <div class="widget-body">
                                                                    <dt id='<%# Eval("Id")%>'>
                                                                        <a class="pull-left tdword-wrap text-left" style="line-height: 20px; margin-top: -10px;width:96%;">
                                                                            <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id")%>' Visible="False"></asp:Label>
                                                                            <asp:Label ID="lblSrNo" runat="server" Text=' <%#Container.DataItemIndex+1+". " %>'></asp:Label>
                                                                            <asp:Label ID="actionName" runat="server" Text='<%# Eval("Name") %>' ToolTip='<%# Eval("Name") %>'></asp:Label></a>                                                                    </dt>
                                                                    <span id="Span1" runat="server" class='<%# ImageActiveProcess(Eval("Description")) %>' data-count="<%#Container.DataItemIndex+1 %>"><i></i></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <dd class="timelineEvent" id='<%# Eval("EX")%>' style="display: none;">
                                                            <div class="widget widget-heading-simple widget-body-white margin-none">
                                                                <div class="widget-body" style="margin-top: 5px;">
                                                                    <div class=" row form-horizontal uniformjs">
                                                                        <div class="col-xs-12">
                                                                            <div class="col-xs-2">
                                                                                <asp:Image ID="imgType" runat="server" ImageUrl='<%# ImageActionType(Eval("Type")) %>' Width="20px" />
                                                                            </div>
                                                                            <div class="col-xs-10">
                                                                                <div class="form-group">
                                                                                    <div class="col-xs-1">
                                                                                        <span class="glyphicons history"><i></i></span>
                                                                                    </div>
                                                                                    <div class="col-xs-3 text-left padding-none-LR">
                                                                                        <asp:Label runat="server" ID="lblStartTime" CssClass="text-small" Text="Start Time"></asp:Label>
                                                                                    </div>
                                                                                    <div class="col-xs-7 text-left padding-none-LR">
                                                                                        :
                                                                                        <asp:Label runat="server" ID="Label1" CssClass="label label-success" Text='<%# GetRunningStatus(Eval("StartTime")) %>'></asp:Label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="form-group">
                                                                                    <div class="col-xs-1">
                                                                                        <span class="glyphicons history"><i></i></span>
                                                                                    </div>
                                                                                    <div class="col-xs-3 text-left padding-none-LR">
                                                                                        <asp:Label runat="server" ID="lblEndTime" CssClass="text-small" Text="End Time"></asp:Label>
                                                                                    </div>
                                                                                    <div class="col-xs-7 text-left padding-none-LR">
                                                                                        :
                                                                                        <asp:Label runat="server" ID="Label3" CssClass="label label-success" Text='<%# GetRunningStatus(Eval("EndTime")) %>'></asp:Label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="form-group">
                                                                                    <div class="col-xs-1">
                                                                                        <span class="glyphicons history"><i></i></span>
                                                                                    </div>
                                                                                    <div class="col-xs-3 text-left padding-none-LR">
                                                                                        <asp:Label runat="server" ID="Label14" CssClass="text-small" Text="Total Time"></asp:Label>
                                                                                    </div>
                                                                                    <div class="col-xs-7 text-left padding-none-LR">
                                                                                        :
                                                                                        <asp:Label runat="server" ID="Label15" CssClass="label label-success" Text='<%# Eval("TotalTime") %>'></asp:Label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="form-group">
                                                                                    <div class="col-xs-1">
                                                                                        <span class="glyphicons history"><i></i></span>
                                                                                    </div>
                                                                                    <div class="col-xs-3 text-left padding-none-LR">
                                                                                        <asp:Label runat="server" ID="Label16" CssClass="text-small" Text="Est Time"></asp:Label>
                                                                                    </div>
                                                                                    <div class="col-xs-7 text-left padding-none-LR">
                                                                                        :
                                                                                        <asp:Label runat="server" ID="Label17" CssClass="label label-success" Text='<%#ChangeTimeFormate(Eval("RTO")) %>'></asp:Label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="form-group">
                                                                                    <div class="col-xs-1">
                                                                                        <span class="glyphicons history"><i></i></span>
                                                                                    </div>
                                                                                    <div class="col-xs-3 text-left padding-none-LR">
                                                                                        <asp:Label runat="server" ID="Label2" CssClass="text-small" Text="Status"></asp:Label>
                                                                                    </div>
                                                                                    <div class="col-xs-7 text-left padding-none-LR">
                                                                                        :
                                                                                        <asp:Label runat="server" ID="lblStatu" CssClass='<%# BgActions(Eval("Description")) %>' Text='<%# Eval("Description") %>'></asp:Label>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <asp:Panel ID="pnlError" Visible='<%# MessageInfo(Eval("Description")) %>' runat="server">
                                                                        <div style="margin-top: 10px; margin-bottom: 10px; border: 1px solid #CCCCCC; border-radius: 5px;">
                                                                            <asp:Label ID="lblErrorMessage" runat="server" Text='<%# Eval("ParallelWorkflowMessage") %>' CssClass="error text-small text-left padding"></asp:Label>
                                                                        </div>
                                                                    </asp:Panel>
                                                                </div>
                                                            </div>
                                                            <br class="clear">
                                                        </dd>
                                                       
                                                    </dl>
                                                </div>
                                            </div>
                                        </li>
                                    </ItemTemplate>
                                    <EmptyDataTemplate>
                                    </EmptyDataTemplate>
                                </asp:ListView>

                                <div id="divmessage" visible="True" class="message messagescroll" runat="server">
                                    <asp:Label ID="lblActionMessage" Visible="true" runat="server" Text=""></asp:Label>
                                </div>
                                <asp:HiddenField ID="HiddenField1" runat="server" />
                            </div>
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </form>
        </div>
</body>
   
</html>
