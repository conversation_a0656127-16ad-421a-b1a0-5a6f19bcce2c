﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Workflow", Namespace = "www.ContinuityPlatform.com")]
    public class Workflow : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Xml { get; set; }

        [DataMember]
        public int WorkflowType { get; set; }

        [DataMember]
        public string ActionIds { get; set; }

        //History
        [DataMember]
        public double Version { get; set; }

        [DataMember]
        public string OldSaveActionIds { get; set; }

        [DataMember]
        public string User { get; set; }

        [DataMember]
        public string VersionType { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public int OldActionId { get; set; }

        [DataMember]
        public double ActionVersion { get; set; }

        [DataMember]
        public int WorkflowId { get; set; }

        [DataMember]
        public int IsLock { get; set; }

        #endregion Properties
    }
}