﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="CIODashboard.aspx.cs" Inherits="CP.UI.CIODashboard" %>

<%@ Register Src="~/Controls/SeriveRecoveryEfficiency.ascx" TagPrefix="uc1" TagName="SeriveRecoveryEfficiency" %>
<%@ Register Src="~/Controls/SeriveRecoveryEffectiveness.ascx" TagPrefix="uc1" TagName="SeriveRecoveryEffectiveness" %>
<%@ Register Src="~/Admin/DashboardContols/DownBusinessServices.ascx" TagName="DownBusinessServices" TagPrefix="uc1" %>
<%@ Register Src="~/Admin/DashboardContols/ComponentFailureDetails.ascx" TagName="ComponentFailureDetails" TagPrefix="uc2" %>
<%@ Register Src="~/Controls/AlertsByMonth.ascx" TagPrefix="uc1" TagName="AlertsByMonth" %>
<%@ Register Src="~/Controls/AvgDataLag.ascx" TagPrefix="uc1" TagName="AvgDataLag" %>
<%@ Register Src="~/Controls/IncidentByMonth.ascx" TagPrefix="uc1" TagName="IncidentByMonth" %>
<%@ Register Src="~/Controls/ServicesMeetingRTO.ascx" TagName="ServicesMeetingRTO" TagPrefix="uc3" %>
<%@ Register Src="~/Controls/ServiceWithoutDRInfra.ascx" TagName="ServiceWithoutDRInfra" TagPrefix="uc5" %>
<%@ Register Src="~/Controls/ServicesNeverRecovered.ascx" TagName="ServicesNeverRecovered" TagPrefix="uc6" %>
<%@ Register Src="~/Controls/RPOSLAbreachbyMonth.ascx" TagPrefix="uc1" TagName="RPOSLAbreachbyMonth" %>
<%@ Register Src="~/Controls/RpoSlaBreachSixMonth.ascx" TagPrefix="uc1" TagName="RpoSlaBreachSixMonth" %>
<%@ Register Src="~/Controls/ActionIntervention.ascx" TagPrefix="ucActionInter" TagName="ActionIntervention" %>
<%@ Register Src="~/Controls/DrillsExecution.ascx" TagName="DrillsExecution" TagPrefix="uc7" %>
<%@ Register Src="~/Controls/CurrentStatus.ascx" TagPrefix="uc1" TagName="CurrentStatus" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="cioinner">
        <div class="row">
            <div class="col-md-12 form-horizontal uniformjs">

                <div class="col-md-4 ciofirstrowpadding">
                    <uc1:SeriveRecoveryEfficiency runat="server" ID="SeriveRecoveryEfficiency" />

                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc6:ServicesNeverRecovered ID="ServicesNeverRecovered1" runat="server" />
                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Services/Apps Never Recovered</div>
                                <asp:Label ID="Label2" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
                                <span class="subhead">(Current Month)</span>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 10px;">
                                    <div class="col-md-6 padding-none-LR">
                                        <div>
                                            <asp:Label ID="Label19" runat="server" CssClass="count" Text="12"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label28" runat="server" CssClass="" Text="Services"></asp:Label>

                                    </div>
                                    <div class="col-md-6 padding-none-LR text-right">
                                        <div>
                                            <asp:Label ID="Label20" runat="server" CssClass="count" Text="32"></asp:Label>
                                        </div>

                                        <asp:Label ID="Label29" runat="server" CssClass="" Text="Apps"></asp:Label>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/bar-graph-01.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <asp:Label ID="lblLeg1" runat="server" CssClass="leg1"></asp:Label>
                                    <span class="legtext">Services</span>
                                    <asp:Label ID="Label47" runat="server" CssClass="leg2"></asp:Label>
                                    <span class="legtext">Apps</span>
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton1" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc2:ComponentFailureDetails ID="ComponentFailureDetails1" runat="server" />
                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Component Failure Details</div>
                                <asp:Label ID="Label3" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
                                <span class="subhead">(Current Month)</span>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 28px;">
                                    <asp:Label ID="Label17" runat="server" CssClass="count" Text="32"></asp:Label>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/bar-graph-02.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <img src="../Images/CIO/cogwheel_icon.png" />
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton2" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc1:AlertsByMonth runat="server" ID="AlertsByMonth" />
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc1:CurrentStatus runat="server" ID="CurrentStatus" />
                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="cioheader" style="height: 47px;">
                                <div class="title" style="">Current Status</div>
                            </div>
                            <div class="ciocontent" style="padding:10px 0px">
                                
                                <div class="col-md-12 padding-none-LR" style="display: table;">
                                 <table class="table" id="tblCurrentStatus">
                                     <tbody>
                                         <tr>
                                             <td style="width:80%">Service Breaching RPO SLA</td>
                                             <td class="text-center" style="width:20%">18</td>
                                         </tr>
                                         <tr>
                                             <td style="width:80%">App Breaching RPO SLA</td>
                                             <td class="text-center" style="width:20%">21</td>
                                         </tr>
                                         <tr>
                                             <td style="width:80%">Active Alerts</td>
                                             <td class="text-center" style="width:20%">14</td>
                                         </tr>
                                         <tr>
                                             <td style="width:80%">Active Incidents</td>
                                             <td class="text-center" style="width:20%">13</td>
                                         </tr>
                                         <tr>
                                             <td style="width:80%">Avg. RPO</td>
                                             <td class="text-center" style="width:20%">9</td>
                                         </tr>
                                         
                                     </tbody>

                                 </table>
                                </div>
                            </div>
                             
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <img src="../Images/CIO/tick_icon.png" />
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton4" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>

            </div>
        </div>

        <div class="row">
            <div class="col-md-12 form-horizontal uniformjs">

                <div class="col-md-4 ciofirstrowpadding">
                    <uc1:SeriveRecoveryEffectiveness runat="server" ID="SeriveRecoveryEffectiveness" />
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc5:ServiceWithoutDRInfra ID="ServiceWithoutDRInfra1" runat="server"></uc5:ServiceWithoutDRInfra>
                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Services/Apps Without DR Infra</div>
                                <asp:Label ID="Label7" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
                                <span class="subhead">(Current Month)</span>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 10px;">
                                    <div class="col-md-6 padding-none-LR">
                                        <div>
                                            <asp:Label ID="Label21" runat="server" CssClass="count" Text="12"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label30" runat="server" CssClass="" Text="Services"></asp:Label>
                                    </div>
                                    <div class="col-md-6 padding-none-LR text-right">
                                        <div>
                                            <asp:Label ID="Label22" runat="server" CssClass="count" Text="32"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label31" runat="server" CssClass="" Text="Apps"></asp:Label>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/bar-graph-04.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <asp:Label ID="Label48" runat="server" CssClass="leg1"></asp:Label>
                                    <span class="legtext">Services</span>
                                    <asp:Label ID="Label49" runat="server" CssClass="leg2"></asp:Label>
                                    <span class="legtext">Apps</span>
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton6" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc3:ServicesMeetingRTO ID="ServicesMeetingRTO1" runat="server" />

                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Services/Apps</div>
                                <asp:Label ID="Label8" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
                                <span class="subhead">(Current Month)</span>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 10px;">
                                    <div class="col-md-6 padding-none-LR">
                                        <div>
                                            <asp:Label ID="Label23" runat="server" CssClass="count" Text="11"></asp:Label>
                                        </div>
                                        <span>Meeting RTO</span>
                                    </div>
                                    <div class="col-md-6 padding-none-LR text-right">
                                        <div>
                                            <asp:Label ID="Label24" runat="server" CssClass="count" Text="25"></asp:Label>
                                        </div>
                                        <span>Not Meeting RTO</span>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/bar-graph-05.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-10 padding-none-LR">
                                    <asp:Label ID="Label50" runat="server" CssClass="leg1"></asp:Label>
                                    <span class="legtext">Meet RTO</span>
                                    <asp:Label ID="Label51" runat="server" CssClass="leg2"></asp:Label>
                                    <span class="legtext">Not Meet RTO</span>
                                </div>
                                <div class="col-md-2 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton7" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc1:RPOSLAbreachbyMonth runat="server" ID="RPOSLAbreachbyMonth" />
                    <%-- <div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Service Health Past 6 Months</div>
                                <asp:Label ID="Label9" runat="server" CssClass="subhead" Text="By Week"></asp:Label>
                                <div class="col-md-12 padding-none-LR" style="display: table;">
                                    <asp:Label ID="Label15" runat="server" CssClass="count" Text="48"></asp:Label>
                                    <asp:Label ID="Label27" runat="server" CssClass="count" Text="Mins" Style="font-size: 14px;"></asp:Label>
                                </div>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 28px;">
                                    <span class="subhead">Current Week</span>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/bar-graph-06.png" />
                                </div>

                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <img src="../Images/CIO/health_icon.png" />
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton8" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc7:DrillsExecution ID="DrillsExecution1" runat="server" />
                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Drills Execution History</div>
                                <div class="subhead">Trend By Month</div>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 10px;">
                                    <div class="col-md-7 padding-none-LR">
                                        <div>
                                            <asp:Label ID="Label25" runat="server" CssClass="count" Text="38"></asp:Label>
                                        </div>
                                        <span>YTD (Total Count)</span>
                                    </div>
                                    <div class="col-md-5 padding-none-LR text-right">
                                        <div>
                                            <asp:Label ID="Label26" runat="server" CssClass="count" Text="12"></asp:Label>
                                        </div>
                                        <span>July-16</span>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/bar-graph-07.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <img src="../Images/CIO/history_icon.png" />
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton9" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>

            </div>
        </div>

        <div class="row">
            <div class="col-md-12 form-horizontal uniformjs">

                <div class="col-md-4 ciofirstrowpadding">
                    <uc1:AvgDataLag runat="server" ID="AvgDataLag" />
                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Avg. Datalag Trend</div>
                                <div class="subhead">Past 6 Months</div>
                                <div class="">
                                    <asp:Label ID="Label32" runat="server" CssClass="count" Text="18"></asp:Label>
                                    <asp:Label ID="Label33" runat="server" CssClass="count" Text="Mins" Style="font-size: 14px;"></asp:Label>
                                </div>

                                <asp:Label ID="Label11" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
                                <span class="subhead">(Current Month)</span>
                                <div class="text-center lngraph">
                                    <img src="../Images/CIO/line-graph-03.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <img src="../Images/CIO/graph-leg.png" style="vertical-align: super;" />
                                    <span class="legtext">Avg. Service Recovery Efficiency</span>
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton10" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>s
                        </div>
                    </div>--%>
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <ucActionInter:ActionIntervention runat="server" ID="ActionIntervention1" />
                    <%-- <div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Services Meeting RTO (w/o Human Intervention v/s with Human Intervention)</div>
                                <asp:Label ID="Label12" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
                                <span class="subhead">(Current Month)</span>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 3px;">
                                    <div class="col-md-6 padding-none-LR">
                                        <div>
                                            <asp:Label ID="Label10" runat="server" CssClass="count" Text="12"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label34" runat="server" CssClass="" Text="With HI"></asp:Label>
                                    </div>
                                    <div class="col-md-6 padding-none-LR text-right">
                                        <div>
                                            <asp:Label ID="Label35" runat="server" CssClass="count" Text="32"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label36" runat="server" CssClass="" Text="Without HI"></asp:Label>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/bar-graph-08.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <asp:Label ID="Label52" runat="server" CssClass="leg1"></asp:Label>
                                    <span class="legtext">With HI</span>
                                    <asp:Label ID="Label53" runat="server" CssClass="leg2"></asp:Label>
                                    <span class="legtext">Without HI</span>
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton11" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc1:RpoSlaBreachSixMonth runat="server" ID="RpoSlaBreachSixMonth" />
                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Services/Apps RPO SLA Breach</div>
                                <asp:Label ID="Label13" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
                                <span class="subhead">(Current Month)</span>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 12px;">
                                    <div class="col-md-6 padding-none-LR">
                                        <div>
                                            <asp:Label ID="Label37" runat="server" CssClass="count" Text="12"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label38" runat="server" CssClass="" Text="Services"></asp:Label>
                                    </div>
                                    <div class="col-md-6 padding-none-LR text-right">
                                        <div>
                                            <asp:Label ID="Label39" runat="server" CssClass="count" Text="32"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label40" runat="server" CssClass="" Text="Apps"></asp:Label>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/bar-graph-09.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <asp:Label ID="Label54" runat="server" CssClass="leg1"></asp:Label>
                                    <span class="legtext">Services</span>
                                    <asp:Label ID="Label55" runat="server" CssClass="leg2"></asp:Label>
                                    <span class="legtext">Apps</span>
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton12" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc1:IncidentByMonth runat="server" ID="IncidentByMonth" />
                </div>
                <div class="col-md-2 ciofirstrowpadding">
                    <uc1:DownBusinessServices ID="DownBusinessServices1" runat="server"></uc1:DownBusinessServices>
                    <%--<div class="widget widget-heading-simple widget-body-white">
                        <div class="widget-body">
                            <div class="ciocontent">
                                <div class="title">Currently Not DR Ready</div>
                                <asp:Label ID="Label16" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
                                <span class="subhead">(Current Month)</span>
                                <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 10px;">
                                    <div class="col-md-6 padding-none-LR">
                                        <div>
                                            <asp:Label ID="Label43" runat="server" CssClass="count" Text="12"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label44" runat="server" CssClass="" Text="Services"></asp:Label>
                                    </div>
                                    <div class="col-md-6 padding-none-LR text-right">
                                        <div>
                                            <asp:Label ID="Label45" runat="server" CssClass="count" Text="32"></asp:Label>
                                        </div>
                                        <asp:Label ID="Label46" runat="server" CssClass="" Text="Apps"></asp:Label>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <img src="../Images/CIO/pie-chart.png" />
                                </div>
                            </div>
                            <div class="ciofooter">
                                <div class="col-md-9 padding-none-LR">
                                    <asp:Label ID="Label56" runat="server" CssClass="leg1"></asp:Label>
                                    <span class="legtext">Services</span>
                                    <asp:Label ID="Label57" runat="server" CssClass="leg3"></asp:Label>
                                    <span class="legtext">Apps</span>
                                </div>
                                <div class="col-md-3 padding-none-LR text-right">
                                    <asp:ImageButton ID="ImageButton14" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />
                                </div>
                            </div>
                        </div>
                    </div>--%>
                </div>

            </div>
        </div>

    </div>
     <script type="text/javascript">
        
        $(document).click(function (e) {           
           
             $('[data-toggle="popover"]').each(function () {
                 //the 'is' for buttons that trigger popups
                 //the 'has' for icons within a button that triggers a popup
                 if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                     //$(this).popover('hide');                     
                     var bsPopover = $(this).data('bs.popover'); // Here's where the magic happens                     
                     if (bsPopover) {
                         bsPopover.hide();                        
                     }
                 }                 
            });
            
        });
        function OpenPopup(str) {
            //  alert("hi");
            window.open(str, null, '_blank', 'height=600,width=400,status=yes');
        }
     </script>

</asp:Content>
