﻿using CP.BusinessFacade;
using CP.Common.Shared;
using CP.Helper;
using SnmpSharpNet;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;
using System.Xml.Linq;
using System.Net;
using System.Globalization;
using log4net;

namespace CP.UI.SNMP
{
    public partial class NetworkDiscoveryForm : BasePage//System.Web.UI.Page
    {

        public static string IPAddress = string.Empty;

        private static readonly ILog _logger = LogManager.GetLogger(typeof(NetworkDiscoveryForm));

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Page.IsPostBack)
            {
                txtIPAddressFrom.Text = "**************";
                txtIPAddressTo.Text = "**************";
            }
        }

        protected void btnScanNetwork_Click(object sender, EventArgs e)
        {
            if (ValidateRequest("NetworkDiscovery", UserActionType.NetworkDiscovery))
              GetAgents();
        }

        private void GetAgentDetails(string strHost, ref string strOsName, ref string strApps, ref string strVersion)
        {
            try
            {
                // Create An instance of the Process class responsible for starting the newly process.
                System.Diagnostics.Process process1 = new System.Diagnostics.Process();
                // Set the filename name of the file you want to execute/open
               // process1.StartInfo.FileName = @"C:\NMAP\nmap.exe";
               // process1.StartInfo.Arguments = @" -sVC -O -oX C:\NMAP\CPDiscovery.xml " + strHost;
                process1.StartInfo.FileName = @"C:\NMAP\nmap.exe";
                process1.StartInfo.Arguments = @" -O -oX C:\NMAP\CPDiscovery.xml " + strHost;
                process1.StartInfo.CreateNoWindow = true;
                process1.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                //nmap -sVC -O -T4 -oX

                // Start the process without blocking the current thread
                process1.Start();
                
             
                // you may wait until finish that executable
                process1.WaitForExit();

               // string output = process1.StandardOutput.ReadToEnd();
                //or you can wait for a certain time interval 
               // Thread.Sleep(2000);//Assume within 20 seconds it will finish processing. 
                process1.Close();

                ParseVMXMLDetails(@"C:\NMAP\CPDiscovery.xml", ref  strOsName, ref strApps, ref strVersion);
            }
            catch (Exception ex)
            {

            }
        }

        public void ParseVMXMLDetails(string xmlFilePath, ref string strOsName, ref string strApps, ref string strVersion)
        {
            strApps = string.Empty;
            strVersion = string.Empty;

            var xdoc = XDocument.Load(xmlFilePath);

            try
            {
                string xmlText = string.Empty;

                var _Service = xdoc.Descendants().Elements("service")
                               .Select(y => new
                               {
                                   Name = y.Attribute("name").Value,
                                  // Product = y.Attribute("product").Value
                               });

                foreach (var addr in _Service)
                {
                    if ((addr.Name.ToLower().Contains("sql") || addr.Name.ToLower().Contains("oracle")))
                        strApps += addr.Name + ", ";
                }
            }
            catch (Exception ex)
            {

            }

            try
            {
                var _osfamily = xdoc.Descendants().Elements("osclass")
                              .Select(y => new
                              {
                                  OSFamily = y.Attribute("osfamily").Value,
                                  Vendor = y.Attribute("vendor").Value
                              });

                foreach (var osdetails in _osfamily)
                {
                    strOsName = osdetails.OSFamily;
                }
            }
            catch
            {

            }
        }
        static void ExecuteCommand(string command)
        {
            try
            {
                int exitCode;
                ProcessStartInfo processInfo;
                Process process;

                processInfo = new ProcessStartInfo("cmd.exe", "/c " + command);
                processInfo.CreateNoWindow = false;
                processInfo.UseShellExecute = false;

                // *** Redirect the output ***
                processInfo.RedirectStandardError = true;
                processInfo.RedirectStandardOutput = true;

                process = Process.Start(processInfo);
                process.WaitForExit();

                // *** Read the streams ***
                string output = process.StandardOutput.ReadToEnd();
                string error = process.StandardError.ReadToEnd();

                exitCode = process.ExitCode;

               

                process.Close();
            }
            catch (Exception ex)
            {
         
            }
        }

        private void GetAgents()
        {

            string strMask = string.Empty;
            int iStart = Convert.ToInt32(txtIPAddressFrom.Text.Substring(txtIPAddressFrom.Text.LastIndexOf(".") + 1));
            int iEnd = Convert.ToInt32(txtIPAddressTo.Text.Substring(txtIPAddressTo.Text.LastIndexOf(".") + 1));
            strMask = txtIPAddressTo.Text.Substring(0, txtIPAddressTo.Text.LastIndexOf(".") + 1);
            var dt = new DataTable();
            dt.Columns.Add("ID", typeof(int));
            dt.Columns.Add("IPAddress", typeof(string));
            dt.Columns.Add("OSType", typeof(string));
            dt.Columns.Add("Application", typeof(string));
            dt.Columns.Add("Version", typeof(string));
            dt.Columns.Add("ImageURL", typeof(string));


            dt.Rows.Clear();
            DateTime dtStartTime = DateTime.Now;
            GetAgents(iStart, iEnd, strMask, ref dt);
            DateTime dtEndTime = DateTime.Now;

            lblTime.Text = GetCompletionTime(dtStartTime.ToString(), dtEndTime.ToString());
            lblCount.Text = dt.Rows.Count.ToString();


            gvNetworkDevices.DataSource = dt;
            gvNetworkDevices.DataBind();

        }

        public static string GetCompletionTime(string strNotificationDate, string strCompletionDate)
        {
            StringBuilder sbtime = new StringBuilder();
            if (!(string.IsNullOrEmpty(strNotificationDate) || string.IsNullOrEmpty(strCompletionDate)))
            {
                DateTime dtNotificationDate = DateTime.Parse(strNotificationDate);
                DateTime dtCompletionDate = DateTime.Parse(strCompletionDate);

                TimeSpan diffdate = dtCompletionDate - dtNotificationDate;

                if (diffdate.Days > 0)
                {
                    sbtime.Append(diffdate.Days + " Day(s) ");
                }
                if (diffdate.Hours > 0)
                {
                    sbtime.Append(diffdate.Hours + " Hour(s) ");
                }
                if (diffdate.Minutes > 0)
                {
                    sbtime.Append(diffdate.Minutes + " Minute(s) ");
                }
                if (diffdate.Seconds > 0)
                {
                    sbtime.Append(diffdate.Seconds + " Second(s)");
                }
            }
            return sbtime.ToString();
        }
        public void GetAgents(int iStart, int iEnd, string Mask, ref DataTable dt)
        {
            for (int i = iStart; i <= iEnd; i++)
            {
                string strIpAddress = Mask + i.ToString(CultureInfo.InvariantCulture);

                string strOSName = "NA";
                string strApps = "NA";
                string strVersion = "NA";
                string strImpageURL = "icon-unknown-device";

                if (!IsHostAccessible(strIpAddress)) continue;

                try
                {
                    GetAgentDetails(strIpAddress, ref strOSName, ref strApps, ref strVersion);

                    DataRow dr = dt.NewRow();
                    dr["ID"] = dt.Rows.Count + 1;
                    dr["IPAddress"] = strIpAddress;
                    dr["OSType"] = strOSName;
                    dr["Application"] = strApps.Substring(0, strApps.LastIndexOf(',')).Trim();
                    dr["Version"] = strVersion;
                    dr["ImageURL"] = strImpageURL;
                    dt.Rows.Add(dr);
                }
                catch (Exception ex)
                {

                }
            }
        }
        public static bool IsHostAccessible(string hostNameOrAddress)
        {
            Ping ping = new Ping();

            PingReply reply = ping.Send(hostNameOrAddress, 100);
            return reply.Status == IPStatus.Success;
        }

        public string GetdeviceType2(string strGetString, ref string strImpageURL)
        {
            string strReturnString = string.Empty;

            switch (strGetString)
            {
                case "*******.*******.*******.3":
                    {
                        strImpageURL = "icon-aix";
                        strReturnString = "Aix";
                        break;
                    }
                case "*******.4.1.2021.250.10":
                    {
                        strImpageURL = "icon-linux";
                        strReturnString = "Linux";
                        break;

                    }
                case "*******.4.1.789.2.1":
                    {
                        strImpageURL = "icon-storageDR";
                        strReturnString = "Storage";
                        break;
                    }

                case "*******.4.1.1588.*******":
                    {
                        //old //*******.4.1.9.6.1.82.48.1
                        //new //*******.4.1.1588.*******
                        strImpageURL = "icon-switch";
                        strReturnString = "Switch";
                        break;
                    }

                case "*******.4.1.11.2.3.9.1":
                    {
                        strImpageURL = "icon-printer";
                        strReturnString = "Printer";
                        break;
                    }
                case "*******.4.1.311.1.1.3.1.1":
                    {
                        strImpageURL = "icon-Windows";
                        strReturnString = "WorkStation";
                        break;
                    }
                case "*******.4.1.311.1.1.3.1.2":
                    {
                        strImpageURL = "server-icon";
                        strReturnString = "WindowsServer";
                        break;
                    }
                default:
                    {
                        strImpageURL = "icon-unknown-device";
                        strReturnString = "Unknown";
                        break;
                    }
            }

            return strReturnString;
        }


        private static string GetSnmp(string OID, string AgentIP, string Community)
        {
            StringBuilder sb = new StringBuilder();
            UdpTarget target = null;
            string strReturnString = string.Empty;

            try
            {
                // SNMP community name
                OctetString community = new OctetString(Community);

                // Define agent parameters class
                AgentParameters param = new AgentParameters(community);
                // Set SNMP version to 1 (or 2)
                param.Version = SnmpVersion.Ver1;
                // Construct the agent address object
                // IpAddress class is easy to use here because
                //  it will try to resolve constructor parameter if it doesn't
                //  parse to an IP address
                IpAddress agent = new IpAddress(AgentIP);

                // Construct target
                target = new UdpTarget((IPAddress)agent, 161, 2000, 1);
                // Pdu class used for all requests
                Pdu pdu = new Pdu(PduType.Get);

                pdu.VbList.Add(OID);

                // Make SNMP request
                SnmpV1Packet result = (SnmpV1Packet)target.Request(pdu, param);

                // If result is null then agent didn't reply or we couldn't parse the reply.
                if (result != null)
                {
                    // ErrorStatus other then 0 is an error returned by 
                    // the Agent - see SnmpConstants for error definitions
                    if (result.Pdu.ErrorStatus != 0)
                    {
                        // agent reported an error with the request
                        // sb.Append("Error in SNMP reply. Error: " + result.Pdu.ErrorStatus + " index: " + result.Pdu.ErrorIndex);
                        strReturnString = "SNMP is not Enabled";
                        _logger.Info("SNMP is not Enabled");
                    }
                    else
                    {
                        // Reply variables are returned in the same order as they were added
                        //  to the VbList
                        // sb.Append(GetNameByOid(result.Pdu.VbList[0].Oid.ToString()) + " : " + SnmpConstants.GetTypeName(result.Pdu.VbList[0].Value.Type) + " : " + result.Pdu.VbList[0].Value.ToString());
                        strReturnString = result.Pdu.VbList[0].Value.ToString();
                    }
                }
                else
                {
                    // sb.Append("No response received from SNMP agent.");
                }

            }
            catch (Exception ex)
            {
                strReturnString = "SNMP is not Enabled"; // sb.Append("Error Occured: " + ex.Message);
            }
            finally
            {
                if (target != null)
                    target.Close();
            }

            return strReturnString;
        }
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId,IPAddress);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog1(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId,IPAddress);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
    }
}