﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
     [Serializable]
     [DataContract(Name = "PostgresSqlClusterMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class PostgresSqlClusterMonitor : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string PRClusterName { get; set; }

        [DataMember]
        public string DRClusterName { get; set; }

        [DataMember]
        public string PRCurrentOwnerNode { get; set; }

        [DataMember]
        public string DRCurrentOwnerNode { get; set; }

        [DataMember]
        public string PRALLNODESTATUS { get; set; }

        [DataMember]
        public string DRALLNODESTATUS { get; set; }

        [DataMember]
        public string PRDBVersion { get; set; }

        [DataMember]
        public string DRDBVersion { get; set; }

        [DataMember]
        public string PRALLRESOURCESTATUS { get; set; }

        [DataMember]
        public string DRALLRESOURCESTATUS { get; set; }

        [DataMember]
        public string PRCurrClusterStatus { get; set; }

        [DataMember]
        public string DRCurrClusterStatus { get; set; }

        [DataMember]
        public string PRResourceGroupName1 { get; set; }

        [DataMember]
        public string DRResourceGroupName1 { get; set; }

        [DataMember]
        public string PRRGNameStatusNode1 { get; set; }
        [DataMember]
        public string DRRGNameStatusNode1 { get; set; }

        [DataMember]
        public string PRResourceGroupName2 { get; set; }
        [DataMember]
        public string DRResourceGroupName2 { get; set; }

        [DataMember]
        public string PRRGNameStatusNode2 { get; set; }
        [DataMember]
        public string DRRGNameStatusNode2 { get; set; }

        [DataMember]
        public string PRResourceGroupName3 { get; set; }
        [DataMember]
        public string DRResourceGroupName3 { get; set; }

        [DataMember]
        public string PRRGNameStatusNode3 { get; set; }
        [DataMember]
        public string DRRGNameStatusNode3 { get; set; }

        #endregion
    }
 }

