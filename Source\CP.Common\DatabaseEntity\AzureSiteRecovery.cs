﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "AzureSiteRecovery", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class AzureSiteRecovery : BaseEntity
    {

        #region Member Variables

        public  ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region properties

        [DataMember]
        public int ReplicationId { get; set; }
        [DataMember]
        public string ResourceGrpname { get; set; }
        [DataMember]
        public string VirtualMachName { get; set; }
        [DataMember]
        public string PublicIPAddress { get; set; }
        [DataMember]
        public string RecServicesVaultName { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }


        #endregion properties


    }
}
