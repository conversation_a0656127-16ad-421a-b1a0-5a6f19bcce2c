﻿
var validNavigation = false;

function endSession() {
   
    //alert("bye");
    //killSession();
}

function wireUpEvents() {
   
    window.onbeforeunload = function () {
        if (!validNavigation) {
            endSession();
        }
    }

   
    $(document).bind('keypress', function (e) {
        alert(e.keyCode);
        if (e.keyCode == 116) {
            validNavigation = true;
        }
    });

  
    $("a").bind("click", function () {
        validNavigation = true;
    });

   
    $("form").bind("submit", function () {
        validNavigation = true;
    });

   
    $("input[type=submit]").bind("click", function () {
        validNavigation = true;
    });
}

function killSession() {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: {},
        url: "CommandCenter.aspx/KillSession",
        success: function (msg) {
            alert("killed session");
        }
    });
}


$(document).ready(function () {
    wireUpEvents();
});