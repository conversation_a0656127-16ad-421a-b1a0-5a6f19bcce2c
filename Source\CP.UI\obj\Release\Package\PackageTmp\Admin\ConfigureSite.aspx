﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ConfigureSite.aspx.cs" Inherits="CP.UI.Admin.ConfigureSite" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR CommandCenter">
        <div class="innerT">
            <h2>
                <i class="icon-worldwide text-primary margin-right"></i>Command Center
          
            </h2>
        </div>
        <div class="clearfix">
        </div>

        <div class="widget widget-heading-simple widget-body-white ">
            <div class="widget-body" style="height: 500px;">
                <div class="innerT">
                    <div class="container-960">
                       
                        <div class="hero-unit well">
                            <div class="row">
                                <div class="center padding">
                                    <div class="col-md-3 text-right" style="width: 22%; top: -5px;">
                                        <a class="glyphicons warning_sign" href="error.html?lang=en">
                                            <i></i>
                                        </a>
                                    </div>
                                    <div id="dvmessagewithClick" class="col-md-9" runat="server">
                                        No Site Configured.<br />
                                       
                                    </div>
                                    <div id="dvmessage" class="col-md-9" runat="server">
                                        No Site Configured.<br />
                                         Please <a href="../Site/SiteConfiguration.aspx">Click Here</a> to Configure Site.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
