<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="AvamarReplicationConfiguration.ascx.cs" Inherits="CP.UI.Controls.AvamarReplicationConfiguration" %>

<div id="Div1" runat="server" class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">AvamarReplication Configuration</h4>
        </div>
        <div class="widget-body">
               
			 <div class="form-group">
                <label class="col-md-3 control-label" for="txtAvamarDataDomainName" id="AvamarDataDomainName" runat="server">
                   Avamar Domain Name Path<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtAvamarDataDomainName" runat="server" class="form-control" autocomplete="off"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvAvamarDataDomainName" CssClass="error" runat="server" ControlToValidate="txtAvamarDataDomainName"
                        ErrorMessage="Enter AvamarDomainNamePath" Display="Dynamic"></asp:RequiredFieldValidator>                 
                </div>
            </div>

	

            <div class="form-actions row">
                <div class="col-md-3">                    
                 </div>
                <div class="col-md-6" style="margin-left: 40.3%;">
                    <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save" OnClick="btnSave_Click" />
                    <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server" Text="Cancel" CausesValidation="false" OnClick="btnCancel_Click"/>
                </div>
            </div>
        </div>
    </div>
</div>
