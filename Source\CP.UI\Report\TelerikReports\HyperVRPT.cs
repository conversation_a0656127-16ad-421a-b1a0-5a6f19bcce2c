namespace CP.UI.Report.TelerikReports
{
    using System;
    using System.ComponentModel;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;
    using BusinessFacade;
    using DataAccess;
    using Common.DatabaseEntity;
    using System.Collections.Generic;
    using System.Collections;
    using System.Linq;
    using System.Configuration;
    using System.Data;
    using System.Globalization;
    using System.IO;
    using System.Web;
    using System.Web.UI;
    using System.Web.UI.WebControls;
    using CP.ExceptionHandler;
    using CP.UI.Controls.ReportClients;
    using Gios.Pdf;
    using log4net;
    using SpreadsheetGear;
    using CP.Common.BusinessEntity;

    /// <summary>
    /// Summary description for MimixDatalg.
    /// </summary>
    public partial class HyperVRPT : Telerik.Reporting.Report
    {
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();
        public HyperVRPT()
        {

            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }


        private void ShowTable()
        {
            int iInfraObjId = Convert.ToInt32(this.ReportParameters["iInfraObjId"].Value);
            string strDate = (this.ReportParameters["iStartDate"].Value).ToString();
            string endDate = (this.ReportParameters["iEndDate"].Value).ToString();
         //   string endDate = (this.ReportParameters["iVMName"].Value).ToString();

            var table = new DataTable();
            table.Columns.Add("SrNo");
            table.Columns.Add("ReplicationState");
            table.Columns.Add("PrimarySecondaryserver");
            table.Columns.Add("PrimarySecondaryReplicaServer");
            table.Columns.Add("SizeReplicated");
            table.Columns.Add("LastSynchronized");
            table.Columns.Add("DataLag");
            table.Columns.Add("TimeStamp");
             

            //IList<MimixDatalag> MimixDag = Facade.GetMimixDatalagByDate(iInfraObjId, strDate, endDate);
            IList<HyperVDetails> hyperv = Facade.GetAllHyperVDetailsMonitorsByDate(iInfraObjId, strDate, endDate);

            if (hyperv != null && hyperv.Count > 0)
            {
                int i = 1;
                foreach (var emc in hyperv)
                {
                    DataRow dr = table.NewRow();

                    dr["SrNo"] = i.ToString();

                    string _prrepll = "";
                    string _drrepll = "";

                    if (!string.IsNullOrEmpty(emc.PRReplicationState))
                    {

                        _prrepll = emc.PRReplicationState + "\n\n";
                    }
                    else
                    {

                        _prrepll = "NA" + "\n\n";
                    }

                    if (!string.IsNullOrEmpty(emc.DRReplicationState))
                    {

                        _drrepll = emc.DRReplicationState;
                    }
                    else
                    {

                        _drrepll = "NA";
                    }
                    string _nnww = _prrepll + _drrepll;
                    dr["ReplicationState"] =  Utility.putnewline(_nnww, 25) ;


                    string _prprimser = "";
                    string _drprimser = "";

                    if (!string.IsNullOrEmpty(emc.PRCurrentPrimaryServer))
                    {

                        _prprimser = emc.PRCurrentPrimaryServer + "\n";
                    }
                    else
                    {

                        _prprimser = "NA" + "\n";
                    }

                    if (!string.IsNullOrEmpty(emc.DRCurrentPrimaryServer))
                    {

                        _drprimser = emc.DRCurrentPrimaryServer;
                    }
                    else
                    {

                        _drprimser = "NA";
                    }
                    string _nnw = _prprimser + _drprimser;
                    dr["PrimarySecondaryserver"] = Utility.putnewline(_nnw, 25);


                    string _prrepserv = "";
                    string _drrepserv = "";

                    if (!string.IsNullOrEmpty(emc.PRCurrentReplicaServer))
                    {

                        _prrepserv = emc.PRCurrentReplicaServer + "\n";
                    }
                    else
                    {

                        _prrepserv = "NA" + "\n";
                    }

                    if (!string.IsNullOrEmpty(emc.DRCurrentReplicaServer))
                    {

                        _drrepserv = emc.DRCurrentReplicaServer;
                    }
                    else
                    {

                        _drrepserv = "NA";
                    }
                    string _nww = _prrepserv + _drrepserv;
                    dr["PrimarySecondaryReplicaServer"] = Utility.putnewline(_nww, 25);


                    string _prrepsz = "";
                    string _drrepsz = "";

                    if (!string.IsNullOrEmpty(emc.PRSizeReplicated))
                    {

                        _prrepsz = emc.PRSizeReplicated + "\n\n";
                    }
                    else
                    {

                        _prrepsz = "NA" + "\n\n";
                    }

                    if (!string.IsNullOrEmpty(emc.DRSizeReplicated))
                    {

                        _drrepsz = emc.DRSizeReplicated;
                    }
                    else
                    {

                        _drrepsz = "NA";
                    }
                    string ntww = _prrepsz + _drrepsz;
                    dr["SizeReplicated"] =   Utility.putnewline( ntww, 10) ;

                    
                    
                    dr["LastSynchronized"] = !string.IsNullOrEmpty(emc.LastSynchronized) ? Utility.putnewline(emc.LastSynchronized, 17) : "NA";
                
                   // dr["DataLag"] = "NA";
                    //DataRow _dr = table.NewRow();
                   //_dr["SrNo"] = "";

                    //_dr["ReplicationState"] = !string.IsNullOrEmpty(emc.DRReplicationState) ? Utility.putnewline(emc.DRReplicationState, 20) : "NA";
                    //_dr["PrimarySecondaryserver"] = !string.IsNullOrEmpty(emc.DRCurrentPrimaryServer) ? Utility.putnewline(emc.DRCurrentPrimaryServer, 20) : "NA";
                    //_dr["PrimarySecondaryReplicaServer"] = !string.IsNullOrEmpty(emc.DRCurrentReplicaServer) ? Utility.putnewline(emc.DRCurrentReplicaServer, 20) : "NA";
                    //_dr["SizeReplicated"] = !string.IsNullOrEmpty(emc.DRSizeReplicated) ? Utility.putnewline(emc.DRSizeReplicated, 10) : "NA";
                    //_dr["LastSynchronized"] = !string.IsNullOrEmpty(emc.LastSynchronized) ? Utility.putnewline(emc.LastSynchronized, 17) : "NA";

                   
                    InfraObject InfraObj = Facade.GetInfraObjectById(Convert.ToInt32(iInfraObjId));
                    BusinessFunction businessFtn = Facade.GetBusinessFunctionById(InfraObj != null ? InfraObj.BusinessFunctionId : 0);
                    TimeSpan conDatalag = TimeSpan.FromSeconds(Convert.ToDouble(businessFtn.ConfiguredRPO));

                    if (!string.IsNullOrEmpty(emc.HyperVDatalag ))
                    {
                        string[] split = emc.HyperVDatalag.Split(':');
                        if (split[0].Contains('.'))
                        {
                            string[] splitFinal = split[0].Split('.');
                            split[0] = splitFinal[1];
                        }

                        bool isHealth = Utility.GetReportDatlagHealth(emc.HyperVDatalag, businessFtn.ConfiguredRPO);

                        if (isHealth)
                        {
                            dr["DataLag"] = emc.HyperVDatalag;
                        }
                        else
                        {
                            dr["DataLag"] = emc.HyperVDatalag;

                        }
                    }
                    else
                    {
                        dr["DataLag"] = "NA";
                    }

                    dr["TimeStamp"] = emc.CreateDate != DateTime.MinValue ? Convert.ToString(emc.CreateDate) : "NA";

                    table.Rows.Add(dr);

                    //_dr["TimeStamp"] = emc.CreateDate != DateTime.MinValue ? Convert.ToString(emc.CreateDate) : "NA";

                    i++;
                   // table.Rows.Add(_dr);

                }
            }
            this.DataSource = table;
        }

        private void HyperVRPT_NeedDataSource(object sender, EventArgs e)
        {
            ShowTable();
        }


        //private void HyperVRPTNew_NeedDataSource(object sender, EventArgs e)
        //{
        //    ShowTable();
        //}
    }
}