﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using Gios.Pdf;
using log4net;
using SpreadsheetGear;
using CP.Common.Shared;


namespace CP.UI
{
    public partial class ApplicationGroupReport : BaseControl
    {
        #region Variable

        private IWorkbookSet workbookSet = null;
        private string ssFile = null;
        private IWorkbook templateWorkbook = null;
        private IWorksheet templateWorksheet = null;
        private IRange cells = null;
        private static int countData;

        private readonly ILog _logger = LogManager.GetLogger(typeof(ApplicationGroupReport));
        public string str = string.Empty;
        public string finaltime = string.Empty;
        public string datalagOutput = string.Empty;
        public bool Check = false;
        public ArrayList drHealth = new ArrayList();

        #endregion Variable

        #region Methods

        private void RegisterPostBackControl()
        {
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnPdfSave);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnPdf);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnExcel);
        }


        #region before Add  Maintenance

        //private void prepareExcelfile()
        //{
        //    _logger.Info("======Generating Business Service Summary Report EXCEL View ======");
        //    _logger.Info(Environment.NewLine);
        //    IList<BusinessService> applicationGroups = new List<BusinessService>();
        //    var getcurrentuserDetails = Facade.GetUserById(LoggedInUserId);
        //    var appData = Facade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, LoggedInUserCompany.IsParent, getcurrentuserDetails.InfraObjectAllFlag);
        //    var groupgdata = Facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);


        //    workbookSet = Factory.GetWorkbookSet();
        //    ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
        //    templateWorkbook = workbookSet.Workbooks.Open(ssFile);
        //    templateWorksheet = templateWorkbook.Worksheets[0];

        //    IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
        //    IWorksheet reportWorksheet = null;
        //    IWorksheet lastWorksheet = null;

        //    lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
        //    reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
        //    reportWorkbook.Worksheets["Sheet1"].Delete(); // Default Worksheet

        //    cells = reportWorksheet.Cells;
        //    reportWorksheet.WindowInfo.DisplayGridlines = false;
        //    reportWorksheet.Name = "Business Services Summary";

        //    cells["D3"].Formula = "Business Services Summary Report";
        //    cells["D3"].Font.Color = Color.White;
        //    cells["D3"].Font.Size = 11;
        //    cells["D3"].Font.Bold = true;
        //    cells["D3"].ColumnWidth = 30;
        //    cells["D3"].HorizontalAlignment = HAlign.Right;
        //    cells["D3"].VerticalAlignment = VAlign.Top;

        //    var DateTme = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
        //    cells["B5"].Formula = "Report Generated Time : " + DateTme;
        //    cells["B5"].Font.Color = Color.White;
        //    cells["B5"].Font.Size = 10;
        //    cells["B5"].Font.Bold = true;
        //    cells["B5"].HorizontalAlignment = HAlign.Left;
        //    cells["B5"].VerticalAlignment = VAlign.Top;
        //    cells["B3:F5"].Interior.Color = Color.FromArgb(79, 129, 189);

        //    cells["A1"].ColumnWidth = 7;

        //    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 45, 15, 120, 12);
        //    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 585, 15, 120, 13);
        //    string strlogo = LoggedInUserCompany.CompanyLogoPath;

        //    if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
        //    {
        //        reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 310, 15, 121, 13);
        //    }



        //    //if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
        //    //{
        //    //    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 280, 15, 121, 13);
        //    //}

        //    reportWorksheet.Cells["A1:G1"].RowHeight = 27;

        //    IRange range = reportWorksheet.Cells["B7:F7"];
        //    IBorder border = range.Borders[BordersIndex.EdgeBottom];
        //    border.LineStyle = LineStyle.Continous;
        //    border.Color = Color.Black;
        //    border.Weight = BorderWeight.Thin;

        //    int row = 7;
        //    int i = 1;

        //    cells["B" + row.ToString()].Formula = "Sr.No.";
        //    cells["B" + row.ToString()].Font.Color = Color.White;
        //    cells["B" + row.ToString()].ColumnWidth = 17;
        //    cells["B" + row.ToString()].Font.Bold = true;
        //    cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //    cells["B" + row.ToString() + ":" + "F" + row.ToString()].Interior.Color = Color.FromArgb(79, 129, 189);
        //    cells["B3:F7"].Font.Name = "Cambria";
        //    cells["B" + row.ToString() + ":" + "F" + row.ToString()].Font.Size = 10;

        //    cells["C" + row.ToString()].Formula = "Business Service Names";
        //    cells["C" + row.ToString()].Font.Color = Color.White;
        //    cells["C" + row.ToString()].ColumnWidth = 30;
        //    cells["C" + row.ToString()].Font.Bold = true;
        //    cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //    var appgrpname = cells.Range["C" + row.ToString()].EntireRow;
        //    appgrpname.WrapText = true;

        //    cells["D" + row.ToString()].Formula = "Up";
        //    cells["D" + row.ToString()].Font.Color = Color.White;
        //    cells["D" + row.ToString()].ColumnWidth = 30;
        //    cells["D" + row.ToString()].Font.Bold = true;
        //    cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //    cells["E" + row.ToString()].Formula = "Down";
        //    cells["E" + row.ToString()].Font.Color = Color.White;
        //    cells["E" + row.ToString()].ColumnWidth = 25;
        //    cells["E" + row.ToString()].Font.Bold = true;
        //    cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //    cells["F" + row.ToString()].Formula = "DR Health";
        //    cells["F" + row.ToString()].Font.Color = Color.White;
        //    cells["F" + row.ToString()].ColumnWidth = 25;
        //    cells["F" + row.ToString()].Font.Bold = true;
        //    cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;


        //    row++;


        //    foreach (var app in appData)
        //    {
        //        var appl = new BusinessService();
        //        int healthStatus = 0;
        //        int groupcount = 0;

        //        int avilcnt = 0;
        //        appl.Name = app.Name;

        //        foreach (var groupse in groupgdata)
        //        {
        //            if (groupse.BusinessServiceId == app.Id)
        //            {
        //                groupcount++;

        //                bool isAvaliablity = CheckAvailablity(groupse);

        //                if (isAvaliablity)
        //                {
        //                    avilcnt++;
        //                }
        //            }

        //        }

        //        if (groupcount != 0)
        //        {
        //            if (avilcnt == 0)
        //            {
        //                int down = groupcount - avilcnt;
        //                appl.Up = avilcnt;
        //                appl.Down = down;
        //                // appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);
        //                appl.Health = "Critical";

        //                //int down = groupcount - avilcnt;
        //                //appl.Up = avilcnt;
        //                //appl.Down = down;
        //                //// appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);
        //                //appl.Health = healthStatus >= down ? "Strength" : "Critical";
        //            }
        //            else
        //            {
        //                int down = groupcount - avilcnt;
        //                appl.Up = avilcnt;
        //                appl.Down = down;
        //                //  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
        //                appl.Health = avilcnt == groupcount ? "Strength" : "Critical";


        //                //int down = groupcount - avilcnt;
        //                //appl.Up = avilcnt;
        //                //appl.Down = down;

        //                ////  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
        //                //appl.Health = avilcnt >= down ? "Strength" : "Critical";
        //            }
        //        }
        //        else
        //        {
        //            appl.Availabilty = "NA";
        //            appl.Health = "NA";
        //        }
        //        applicationGroups.Add(appl);
        //    }

        //    countData = applicationGroups.Count();

        //    int dataCount = applicationGroups.Count;
        //    _logger.Info("======" + applicationGroups.Count + " Records Retrieve for Business Service Summary Report ======");
        //    _logger.Info(Environment.NewLine);
        //    foreach (var addData in applicationGroups)
        //    {
        //        int Column = 0;
        //        string[] xlColumn = { "B", "C", "D", "E", "F" };

        //        string Ndx = xlColumn[Column] + row.ToString();
        //        cells[Ndx + ":" + "F" + row.ToString()].Interior.Color = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
        //        cells[Ndx].Formula = i.ToString();
        //        cells[Ndx].Font.Size = 10;
        //        cells[Ndx].ColumnWidth = 17;
        //        cells[Ndx].Font.Color = Color.Black;
        //        cells[Ndx].HorizontalAlignment = HAlign.Left;
        //        i++;
        //        Column++;

        //        Ndx = xlColumn[Column] + row.ToString();
        //        cells[Ndx].Formula = addData.Name;
        //        cells[Ndx].Font.Size = 10;
        //        cells[Ndx].ColumnWidth = 30;
        //        cells[Ndx].Font.Color = Color.Black;
        //        cells[Ndx].HorizontalAlignment = HAlign.Left;

        //        var appnme = cells.Range[Ndx].EntireRow;
        //        appnme.WrapText = true;
        //        Column++;

        //        Ndx = xlColumn[Column] + row.ToString();
        //        cells[Ndx].Formula = addData.Up.ToString();
        //        cells[Ndx].Font.Size = 10;
        //        cells[Ndx].ColumnWidth = 30;
        //        cells[Ndx].Font.Color = Color.Black;
        //        cells[Ndx].HorizontalAlignment = HAlign.Left;
        //        Column++;

        //        Ndx = xlColumn[Column] + row.ToString();
        //        cells[Ndx].Formula = addData.Down.ToString();
        //        cells[Ndx].Font.Size = 10;
        //        cells[Ndx].ColumnWidth = 30;
        //        cells[Ndx].Font.Color = Color.Black;
        //        cells[Ndx].HorizontalAlignment = HAlign.Left;
        //        Column++;

        //        Ndx = xlColumn[Column] + row.ToString();

        //        if (addData.Health.ToString() == "Strength")
        //        {
        //            cells[Ndx].Formula = "Not Affected";
        //            // cells[cellInd].Font.Color = Color.Green;
        //        }
        //        else if (addData.Health.ToString() == "Critical")
        //        {
        //            cells[Ndx].Formula = "Affected";
        //            // cells[cellInd].Font.Color = Color.Red;
        //        }
        //        else
        //        {
        //            cells[Ndx].Formula = "NA";
        //        }


        //        cells[Ndx].Font.Size = 10;
        //        cells[Ndx].ColumnWidth = 30;
        //        cells[Ndx].Font.Color = Color.Black;
        //        cells[Ndx].HorizontalAlignment = HAlign.Left;


        //        row++;
        //    }

        //    //int col = 8;
        //    //foreach (var arrVal in drHealth)
        //    //{
        //    //    string cellInd = "F" + col;
        //    //    if (arrVal.ToString() == "Strength")
        //    //    {
        //    //        cells[cellInd].Formula = "Not Affected";
        //    //       // cells[cellInd].Font.Color = Color.Green;
        //    //    }
        //    //    else if (arrVal.ToString() == "Critical")
        //    //    {
        //    //        cells[cellInd].Formula = "Affected";
        //    //       // cells[cellInd].Font.Color = Color.Red;
        //    //    }
        //    //    else
        //    //    {
        //    //        cells[cellInd].Formula = "NA";
        //    //    }
        //    //    cells[cellInd].Font.Bold = true;
        //    //    cells[cellInd].Font.Size = 10;
        //    //    cells[cellInd].ColumnWidth = 25;
        //    //    cells[cellInd].HorizontalAlignment = HAlign.Left;
        //    //    col++;
        //    //}

        //    int finalCount = dataCount + 9;

        //    cells["B" + finalCount].Formula = "NA : Not Available";
        //    cells["B" + finalCount].Font.Color = Color.Black;
        //    cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
        //    cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;
        //    cells["B" + finalCount + ":" + "D" + finalCount].Font.Name = "Cambria";

        //    //cells["C" + finalCount].Formula = "DR Health Up : ↑";
        //    //cells["C" + finalCount].Font.Bold = true;
        //    //cells["C" + finalCount].Font.Color = Color.Green;
        //    //cells["C" + finalCount].HorizontalAlignment = HAlign.Left;

        //    //cells["D" + finalCount].Formula = "DR Health Down : ↓";
        //    //cells["D" + finalCount].Font.Bold = true;
        //    //cells["D" + finalCount].Font.Color = Color.Red;
        //    //cells["D" + finalCount].HorizontalAlignment = HAlign.Left;

        //    reportWorksheet.ProtectContents = true;
        //    OpenExcelFile(reportWorkbook);
        //    _logger.Info("====== Business Service Summary EXCEL Report generated ======");
        //    _logger.Info(Environment.NewLine);
        //}
        //private void ShowTable()
        //{
        //    _logger.Info("======Generating Business Service Summary Report HTML View ======");
        //    _logger.Info(Environment.NewLine);
        //    IList<BusinessService> applicationGroups = new List<BusinessService>();
        //    var getcurrentuserDetails = Facade.GetUserById(LoggedInUserId);
        //    var appData = Facade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, LoggedInUserCompany.IsParent, getcurrentuserDetails.InfraObjectAllFlag);
        //    var groupgdata = Facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);

        //    foreach (var app in appData)
        //    {
        //        var appl = new BusinessService();
        //        int healthStatus = 0;
        //        int groupcount = 0;

        //        int avilcnt = 0;
        //        appl.Name = app.Name;

        //        foreach (var groupse in groupgdata)
        //        {
        //            if (groupse.BusinessServiceId == app.Id)
        //            {
        //                groupcount++;

        //                bool isAvaliablity = CheckAvailablity(groupse);

        //                if (isAvaliablity)
        //                {
        //                    avilcnt++;
        //                }
        //            }

        //        }

        //        if (groupcount != 0)
        //        {
        //            if (avilcnt == 0)
        //            {
        //                int down = groupcount - avilcnt;
        //                appl.Up = avilcnt;
        //                appl.Down = down;
        //                // appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);
        //                appl.Health = "Critical";

        //                //int down = groupcount - avilcnt;
        //                //appl.Up = avilcnt;
        //                //appl.Down = down;
        //                //// appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);
        //                //appl.Health = healthStatus >= down ? "Strength" : "Critical";
        //            }
        //            else
        //            {
        //                int down = groupcount - avilcnt;
        //                appl.Up = avilcnt;
        //                appl.Down = down;
        //                //  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
        //                appl.Health = avilcnt == groupcount ? "Strength" : "Critical";

        //                //int down = groupcount - avilcnt;
        //                //appl.Up = avilcnt;
        //                //appl.Down = down;
        //                ////  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
        //                //appl.Health = avilcnt >= down ? "Strength" : "Critical";
        //            }
        //        }
        //        else
        //        {
        //            appl.Availabilty = "NA";
        //            appl.Health = "NA";
        //        }
        //        applicationGroups.Add(appl);
        //    }

        //    countData = applicationGroups.Count();

        //    if (countData <= 0)
        //    {
        //        Pan1.Visible = false;
        //        lbldatemsg.Visible = true;
        //        lbldatemsg.Text = "No Record Found";
        //        return;
        //    }

        //    DataTable d1t = new DataTable();
        //    d1t.Columns.Add("Sr.No.");
        //    d1t.Columns.Add("Business Service Names");
        //    d1t.Columns.Add("Up");
        //    d1t.Columns.Add("Down");
        //    d1t.Columns.Add("DR Health");

        //    for (int rowCtr = 1; rowCtr <= 1; rowCtr++)
        //    {
        //        TableRow Trow = new TableRow();
        //        Trow.Font.Size = 9;
        //        tbl.Rows.Add(Trow);
        //        for (int cellCtr = 0; cellCtr < 5; cellCtr++)
        //        {
        //            if (cellCtr == 0)
        //            {
        //                TableCell CellFzero = new TableCell();
        //                CellFzero.Text = "Sr.No.";
        //                CellFzero.Height = 35;
        //                CellFzero.CssClass = "RowStyleHeaderNo bold";
        //                Trow.Cells.Add(CellFzero);
        //            }
        //            else if (cellCtr == 1)
        //            {
        //                TableCell CellFone = new TableCell();
        //                CellFone.Text = "Business Service Names";
        //                CellFone.Height = 35;
        //                CellFone.CssClass = "rowStyleHeader bold";
        //                Trow.Cells.Add(CellFone);
        //            }
        //            else if (cellCtr == 2)
        //            {
        //                TableCell StImgId = new TableCell();
        //                StImgId.Text = "Up";
        //                StImgId.Height = 35;
        //                StImgId.CssClass = "rowStyleHeader bold";
        //                Trow.Cells.Add(StImgId);
        //            }
        //            else if (cellCtr == 3)
        //            {
        //                TableCell StImgId = new TableCell();
        //                StImgId.Text = "Down";
        //                StImgId.Height = 35;
        //                StImgId.CssClass = "rowStyleHeader bold";
        //                Trow.Cells.Add(StImgId);
        //            }
        //            else
        //            {
        //                TableCell tCell = new TableCell();
        //                tCell.Text = "DR Health";
        //                tCell.Height = 35;
        //                tCell.CssClass = "rowStyleHeader bold";
        //                Trow.Cells.Add(tCell);
        //            }
        //        }
        //    }

        //    // List<string> healthValues = new List<string>();

        //    int i = 1;

        //    _logger.Info("======" + applicationGroups.Count + " Records Retrieve for Business Service Summary Report ======");
        //    _logger.Info(Environment.NewLine);
        //    foreach (var addData in applicationGroups)
        //    {
        //        for (int rowCtr = 1; rowCtr <= 1; rowCtr++)
        //        {
        //            DataRow dr = d1t.NewRow();

        //            TableRow Trow = new TableRow();
        //            Trow.Height = 25;
        //            Trow.BackColor = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
        //            tbl.Rows.Add(Trow);

        //            TableCell Number = new TableCell();
        //            Number.Text = i.ToString();
        //            Number.CssClass = "RowStyleNo";
        //            Trow.Cells.Add(Number);

        //            TableCell AppGrpnme = new TableCell();
        //            AppGrpnme.Text = addData.Name != null ? Convert.ToString(addData.Name) : "NA";
        //            AppGrpnme.CssClass = "rowStyle1";
        //            Trow.Cells.Add(AppGrpnme);

        //            TableCell Up = new TableCell();
        //            Up.Text = addData.Up != 0 ? Convert.ToString(addData.Up) : "0";
        //            Up.CssClass = "rowStyle1";
        //            Trow.Cells.Add(Up);


        //            TableCell Down = new TableCell();
        //            Down.Text = addData.Down != 0 ? Convert.ToString(addData.Down) : "0";
        //            Down.CssClass = "rowStyle1";
        //            Trow.Cells.Add(Down);

        //            TableCell Health = new TableCell();
        //            Health.CssClass = "rowStyle1";

        //            if (addData.Health != null && addData.Health == "Strength")
        //            {
        //                dr["DR Health"] = "Not Affected";
        //                Health.Text = "Not Affected";//<img src=../images/icons/up-arrow.png>
        //            }
        //            else if (addData.Health != null && addData.Health == "Critical")
        //            {
        //                dr["DR Health"] = "Affected";
        //                Health.Text = "Affected";//<img src=../images/icons/down-arrow.png>
        //            }
        //            else
        //            {
        //                dr["DR Health"] = "NA";
        //                Health.Text = "NA";
        //            }



        //            Trow.Cells.Add(Health);


        //            dr["Sr.No."] = i.ToString();
        //            dr["Business Service Names"] = addData.Name != null ? Convert.ToString(addData.Name) : "NA";
        //            dr["Up"] = addData.Up != null ? Convert.ToString(addData.Up) : "0";
        //            dr["Down"] = addData.Down != null ? Convert.ToString(addData.Down) : "0";

        //            // healthValues.Add(addData.Health);
        //            i++;
        //            d1t.Rows.Add(dr);
        //        }
        //    }

        //    _logger.Info("======Generating Business Service Summary Report PDF View ======");
        //    _logger.Info(Environment.NewLine);
        //    PdfDocument myPdfDocument = new PdfDocument(PdfDocumentFormat.InCentimeters(21, 29.7));
        //    PdfTable myPdfTable = myPdfDocument.NewTable(new Font("Verdana", 7), countData, 5, 4);
        //    myPdfTable.ImportDataTable(d1t);
        //    myPdfTable.HeadersRow.SetColors(Color.White, Color.FromArgb(79, 129, 189));
        //    myPdfTable.SetBorders(Color.Black, 0.1, BorderType.None);
        //    myPdfTable.HeadersRow.SetContentAlignment(ContentAlignment.MiddleLeft);
        //    myPdfTable.SetColors(Color.Black, Color.White, Color.FromArgb(219, 229, 241));
        //    myPdfTable.SetColumnsWidth(new int[] { 4, 8, 4, 3, 6 });
        //    myPdfTable.SetContentAlignment(ContentAlignment.MiddleLeft);

        //    PdfImage LogoImage =
        //        myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"));
        //    PdfImage LogoBcms = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"));

        //    string strlogo = LoggedInUserCompany.CompanyLogoPath.ToString();

        //    PdfImage complogo = null;

        //    if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
        //    {
        //        complogo = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(strlogo));
        //    }


        //    PdfTextArea pta = new PdfTextArea(new Font("Verdana", 9, FontStyle.Bold), Color.Black
        //                                      , new PdfArea(myPdfDocument, 0, 40, 595, 80),
        //                                      ContentAlignment.MiddleCenter, "Business Services Summary Report");

        //    PdfTextArea reportTime = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
        //           , new PdfArea(myPdfDocument, 48, 10, 175, 190), ContentAlignment.MiddleLeft, "Report Generated Time : " + DateTime.Now.ToString("dd-MMM-yyyy HH:mm"));

        //    var notavailable = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
        //             , new PdfArea(myPdfDocument, 190, 10, 100, 190), ContentAlignment.MiddleRight, "NA : Not Available");

        //    //var healthup = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
        //    //         , new PdfArea(myPdfDocument, 216, 10, 150, 190), ContentAlignment.MiddleRight, "DR Health Up");

        //    //var healthdown = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
        //    //         , new PdfArea(myPdfDocument, 255, 10, 200, 190), ContentAlignment.MiddleRight, "DR Health Down");

        //    var logoHealthDownW = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/down-arrow.jpg"));
        //    var logoHealthUpW = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/up-arrow.jpg"));

        //    var logoHealthDownG = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/down-arrow-grey-bg1.jpg"));
        //    var logoHealthUpG = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/up-arrow-grey-bg1.jpg"));

        //    var logoHealthNAG = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/NA-2.jpg"));
        //    var logoHealthNAW = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/NA-1.jpg"));

        //    int pgNo = 1;
        //    while (!myPdfTable.AllTablePagesCreated)
        //    {
        //        PdfPage newPdfPage = myPdfDocument.NewPage();
        //        PdfTablePage newPdfTablePage = myPdfTable.CreateTablePage(new PdfArea(myPdfDocument, 48, 110, 500, 670));

        //        PdfTextArea pageNumber = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
        //                                                 , new PdfArea(myPdfDocument, 50, 0, 450, 1600),
        //                                                 ContentAlignment.MiddleRight,
        //                                                 "Page Number :   " + pgNo++.ToString());

        //        newPdfPage.Add(LogoImage, 460, 25, 180);
        //        newPdfPage.Add(LogoBcms, 50, 25, 110);

        //        if (complogo != null)
        //            newPdfPage.Add(complogo, 290, 25, 120);

        //        newPdfPage.Add(newPdfTablePage);
        //        newPdfPage.Add(pta);

        //        newPdfPage.Add(notavailable);
        //        //newPdfPage.Add(healthup);
        //        //newPdfPage.Add(healthdown);

        //        //newPdfPage.Add(logoHealthUpW, 369, 99, 119);
        //        //newPdfPage.Add(logoHealthDownW, 459, 99, 120);

        //        newPdfPage.Add(reportTime);
        //        newPdfPage.Add(pageNumber);

        //        //for (int index = newPdfTablePage.FirstRow; index <= newPdfTablePage.LastRow; index++)
        //        //{
        //        //    var xaxis = newPdfTablePage.CellArea(index, 3).PosX + 35;
        //        //    var yaxis = newPdfTablePage.CellArea(index, 3).PosY + 3;
        //        //    var xdrhlth = newPdfTablePage.CellArea(index, 3).PosX + 29;

        //        //    if (healthValues[index] == "Critical")
        //        //    {
        //        //        newPdfPage.Add(index % 2 == 0 ? logoHealthDownW : logoHealthDownG, xaxis, yaxis);
        //        //        drHealth.Add("Critical");
        //        //    }
        //        //    else if (healthValues[index] == "Strength")
        //        //    {
        //        //        newPdfPage.Add(index % 2 == 0 ? logoHealthUpW : logoHealthUpG, xaxis, yaxis);
        //        //        drHealth.Add("Strength");
        //        //    }
        //        //    else
        //        //    {
        //        //        newPdfPage.Add(index % 2 == 0 ? logoHealthNAW : logoHealthNAG, xdrhlth, yaxis);
        //        //        drHealth.Add("NA");
        //        //    }
        //        //}
        //        newPdfPage.SaveToDocument();

        //    }

        //    if (Check == true)
        //    {
        //        str = DateTime.Now.ToString().Replace("/", "");
        //        str = str.Replace(":", "");
        //        str = str.Substring(0, str.Length - 5);
        //        str = System.Text.RegularExpressions.Regex.Replace(str, @"\s", "");
        //        str = "BusinessServiceSummaryReport" + str + ".pdf";
        //        myPdfDocument.SaveToFile(HttpContext.Current.Server.MapPath(@"~/PdfFiles/" + str));
        //        //string myUrl = "/PdfFiles/" + str;
        //        string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
        //        string myUrl = reportPath + "/PdfFiles/" + str;
        //        string fullURL = "window.open('" + myUrl +
        //                         "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Business Service Summary Report');";
        //        ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        //        _logger.Info("====== Business Service Summary PDF Report generated ======");
        //        _logger.Info(Environment.NewLine);
        //    }
        //    _logger.Info("====== Business Service Summary HTML Report generated ======");
        //    _logger.Info(Environment.NewLine);
        //}

        #endregion



        private void prepareExcelfile()
        {

            IList<BusinessService> applicationGroups = new List<BusinessService>();
            var getcurrentuserDetails = Facade.GetUserById(LoggedInUserId);
            var appData = Facade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, LoggedInUserCompany.IsParent, getcurrentuserDetails.InfraObjectAllFlag);
            // var groupgdata = Facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
            IList<InfraObject> groupgdata = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, getcurrentuserDetails.Role, IsParentCompnay, getcurrentuserDetails.InfraObjectAllFlag);

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
            reportWorkbook.Worksheets["Sheet1"].Delete(); // Default Worksheet

            cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "Business Services Summary";

            cells["E3"].Formula = "Business Services Summary Report";
            cells["E3"].Font.Color = Color.White;
            cells["E3"].Font.Size = 11;
            cells["E3"].Font.Bold = true;
            cells["E3"].ColumnWidth = 30;
            cells["E3"].HorizontalAlignment = HAlign.Right;
            cells["E3"].VerticalAlignment = VAlign.Top;

           
            cells["B4"].Formula = "Logged in User :" + " " + (LoggedInUser.LoginName != null ? LoggedInUser.LoginName : "NA");
            cells["B4"].Font.Color = Color.White;
            cells["B4"].Font.Size = 10;
            cells["B4"].Font.Bold = true;
            cells["B4"].HorizontalAlignment = HAlign.Left;
            cells["B4"].VerticalAlignment = VAlign.Top;
            cells["B3:H5"].Interior.Color = Color.FromArgb(79, 129, 189);



            var DateTme = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
            cells["B5"].Formula = "Report Generated Time : " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));  //DateTme;
            cells["B5"].Font.Color = Color.White;
            cells["B5"].Font.Size = 10;
            cells["B5"].Font.Bold = true;
            cells["B5"].HorizontalAlignment = HAlign.Left;
            cells["B5"].VerticalAlignment = VAlign.Top;
            cells["B3:H5"].Interior.Color = Color.FromArgb(79, 129, 189);

            cells["A1"].ColumnWidth = 7;

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 45, 15, 120, 12);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 680, 15, 120, 13);

            string strlogo = LoggedInUserCompany.CompanyLogoPath;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 370, 15, 121, 13);
            }



            //if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            //{
            //    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 280, 15, 121, 13);
            //}

            reportWorksheet.Cells["A1:H1"].RowHeight = 27;

            IRange range = reportWorksheet.Cells["B7:H7"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            int row = 7;
            int i = 1;

            cells["B" + row.ToString()].Formula = "Sr.No.";
            cells["B" + row.ToString()].Font.Color = Color.White;
            cells["B" + row.ToString()].ColumnWidth = 17;
            cells["B" + row.ToString()].Font.Bold = true;
            cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
            cells["B" + row.ToString() + ":" + "H" + row.ToString()].Interior.Color = Color.FromArgb(79, 129, 189);
            cells["B3:H7"].Font.Name = "Cambria";
            cells["B" + row.ToString() + ":" + "F" + row.ToString()].Font.Size = 10;

            cells["C" + row.ToString()].Formula = "Business Service Names";
            cells["C" + row.ToString()].Font.Color = Color.White;
            cells["C" + row.ToString()].ColumnWidth = 30;
            cells["C" + row.ToString()].Font.Bold = true;
            cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

            var appgrpname = cells.Range["C" + row.ToString()].EntireRow;
            appgrpname.WrapText = true;

            cells["D" + row.ToString()].Formula = "Business Service Description";
            cells["D" + row.ToString()].Font.Color = Color.White;
            cells["D" + row.ToString()].ColumnWidth = 30;
            cells["D" + row.ToString()].Font.Bold = true;
            cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

            cells["E" + row.ToString()].Formula = "Up";
            cells["E" + row.ToString()].Font.Color = Color.White;
            cells["E" + row.ToString()].ColumnWidth = 30;
            cells["E" + row.ToString()].Font.Bold = true;
            cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

            cells["F" + row.ToString()].Formula = "Down";
            cells["F" + row.ToString()].Font.Color = Color.White;
            cells["F" + row.ToString()].ColumnWidth = 25;
            cells["F" + row.ToString()].Font.Bold = true;
            cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

            cells["G" + row.ToString()].Formula = "Maintenance";
            cells["G" + row.ToString()].Font.Color = Color.White;
            cells["G" + row.ToString()].ColumnWidth = 25;
            cells["G" + row.ToString()].Font.Bold = true;
            cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;

            cells["H" + row.ToString()].Formula = "DR Health";
            cells["H" + row.ToString()].Font.Color = Color.White;
            cells["H" + row.ToString()].ColumnWidth = 25;
            cells["H" + row.ToString()].Font.Bold = true;
            cells["H" + row.ToString()].HorizontalAlignment = HAlign.Left;


            row++;


            foreach (var app in appData)
            {
                var appl = new BusinessService();
                int healthStatus = 0;
                int groupcount = 0;
                int maintcount = 0;
                int avilcnt = 0;
                appl.Name = app.Name;
                appl.Description = app.Description;

                foreach (var groupse in groupgdata)
                {
                    if (groupse.BusinessServiceId == app.Id)
                    {

                        groupcount++;

                        if (groupse.State == "Maintenance")
                        {
                            maintcount++;
                        }
                        else
                        {
                            bool isAvaliablity = CheckAvailablity(groupse);

                            if (isAvaliablity)
                            {
                                avilcnt++;
                            }
                        }

                    }

                }

                if (groupcount != 0)
                {
                    if (avilcnt == 0)
                    {
                        int down = groupcount - (avilcnt + maintcount);
                        appl.Up = avilcnt;
                        appl.Down = down;
                        appl.Maintenance = maintcount;
                        // appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);

                        if (groupcount == maintcount)
                            appl.Health = "NA";
                        else
                            appl.Health = "Critical";
                        //int down = groupcount - avilcnt;
                        //appl.Up = avilcnt;
                        //appl.Down = down;
                        //// appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);
                        //appl.Health = healthStatus >= down ? "Strength" : "Critical";
                    }
                    else
                    {
                        int down = groupcount - (avilcnt + maintcount);
                        appl.Up = avilcnt;
                        appl.Down = down;
                        appl.Maintenance = maintcount;
                        //  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
                        // appl.Health = avilcnt == groupcount ? "Strength" : "Critical";

                        if (groupcount == maintcount)
                            appl.Health = "NA";
                        else if (down == 0 && groupcount != maintcount)
                            appl.Health = "Strength";
                        else if (down > 0)
                            appl.Health = "Critical";

                        //int down = groupcount - avilcnt;
                        //appl.Up = avilcnt;
                        //appl.Down = down;
                        //                        //  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
                        //appl.Health = avilcnt >= down ? "Strength" : "Critical";
                    }
                }
                else
                {
                    appl.Availabilty = "NA";
                    appl.Health = "NA";
                }
                applicationGroups.Add(appl);
            }

            countData = applicationGroups.Count();

            int dataCount = applicationGroups.Count;

            foreach (var addData in applicationGroups)
            {
                int Column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H" };

                string Ndx = xlColumn[Column] + row.ToString();
                cells[Ndx + ":" + "H" + row.ToString()].Interior.Color = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                cells[Ndx].Formula = i.ToString();
                cells[Ndx].Font.Size = 10;
                cells[Ndx].ColumnWidth = 17;
                cells[Ndx].Font.Color = Color.Black;
                cells[Ndx].HorizontalAlignment = HAlign.Left;
                i++;
                Column++;

                Ndx = xlColumn[Column] + row.ToString();
                cells[Ndx].Formula = addData.Name;
                cells[Ndx].Font.Size = 10;
                cells[Ndx].ColumnWidth = 30;
                cells[Ndx].Font.Color = Color.Black;
                cells[Ndx].HorizontalAlignment = HAlign.Left;

                var appnme = cells.Range[Ndx].EntireRow;
                appnme.WrapText = true;
                Column++;

                Ndx = xlColumn[Column] + row.ToString();
                cells[Ndx].Formula = addData.Description;
                cells[Ndx].Font.Size = 10;
                cells[Ndx].ColumnWidth = 30;
                cells[Ndx].Font.Color = Color.Black;
                cells[Ndx].HorizontalAlignment = HAlign.Left;
                Column++;

                Ndx = xlColumn[Column] + row.ToString();
                cells[Ndx].Formula = addData.Up.ToString();
                cells[Ndx].Font.Size = 10;
                cells[Ndx].ColumnWidth = 30;
                cells[Ndx].Font.Color = Color.Black;
                cells[Ndx].HorizontalAlignment = HAlign.Left;
                Column++;

                Ndx = xlColumn[Column] + row.ToString();
                cells[Ndx].Formula = addData.Down.ToString();
                cells[Ndx].Font.Size = 10;
                cells[Ndx].ColumnWidth = 30;
                cells[Ndx].Font.Color = Color.Black;
                cells[Ndx].HorizontalAlignment = HAlign.Left;
                Column++;

                Ndx = xlColumn[Column] + row.ToString();
                cells[Ndx].Formula = addData.Maintenance.ToString();
                cells[Ndx].Font.Size = 10;
                cells[Ndx].ColumnWidth = 30;
                cells[Ndx].Font.Color = Color.Black;
                cells[Ndx].HorizontalAlignment = HAlign.Left;
                Column++;

                Ndx = xlColumn[Column] + row.ToString();

                if (addData.Health.ToString() == "Strength")
                {
                    cells[Ndx].Formula = "Not Affected";
                    // cells[cellInd].Font.Color = Color.Green;
                }
                else if (addData.Health.ToString() == "Critical")
                {
                    cells[Ndx].Formula = "Affected";
                    // cells[cellInd].Font.Color = Color.Red;
                }
                else
                {
                    cells[Ndx].Formula = "NA";
                }


                cells[Ndx].Font.Size = 10;
                cells[Ndx].ColumnWidth = 30;
                cells[Ndx].Font.Color = Color.Black;
                cells[Ndx].HorizontalAlignment = HAlign.Left;


                row++;
            }

            //int col = 8;
            //foreach (var arrVal in drHealth)
            //{
            //    string cellInd = "F" + col;
            //    if (arrVal.ToString() == "Strength")
            //    {
            //        cells[cellInd].Formula = "Not Affected";
            //       // cells[cellInd].Font.Color = Color.Green;
            //    }
            //    else if (arrVal.ToString() == "Critical")
            //    {
            //        cells[cellInd].Formula = "Affected";
            //       // cells[cellInd].Font.Color = Color.Red;
            //    }
            //    else
            //    {
            //        cells[cellInd].Formula = "NA";
            //    }
            //    cells[cellInd].Font.Bold = true;
            //    cells[cellInd].Font.Size = 10;
            //    cells[cellInd].ColumnWidth = 25;
            //    cells[cellInd].HorizontalAlignment = HAlign.Left;
            //    col++;
            //}

            int finalCount = dataCount + 9;

            cells["B" + finalCount].Formula = "NA : Not Available";
            cells["B" + finalCount].Font.Color = Color.Black;
            cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
            cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;
            cells["B" + finalCount + ":" + "D" + finalCount].Font.Name = "Cambria";

            //cells["C" + finalCount].Formula = "DR Health Up : ↑";
            //cells["C" + finalCount].Font.Bold = true;
            //cells["C" + finalCount].Font.Color = Color.Green;
            //cells["C" + finalCount].HorizontalAlignment = HAlign.Left;

            //cells["D" + finalCount].Formula = "DR Health Down : ↓";
            //cells["D" + finalCount].Font.Bold = true;
            //cells["D" + finalCount].Font.Color = Color.Red;
            //cells["D" + finalCount].HorizontalAlignment = HAlign.Left;

            reportWorksheet.ProtectContents = true;
            OpenExcelFile(reportWorkbook);
        }

        private void ShowTable()
        {
            _logger.Info("======Generating Business Service Summary Report HTML View ======");
            _logger.Info(Environment.NewLine);
            IList<BusinessService> applicationGroups = new List<BusinessService>();
            var getcurrentuserDetails = Facade.GetUserById(LoggedInUserId);
            var appData = Facade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, LoggedInUserCompany.IsParent, getcurrentuserDetails.InfraObjectAllFlag);
            //var groupgdata = Facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
            IList<InfraObject> groupgdata = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, getcurrentuserDetails.Role, IsParentCompnay, getcurrentuserDetails.InfraObjectAllFlag);
            if (appData == null || groupgdata == null)
            {
                btnPdfSave.Visible = true;
                Pan1.Visible = false;
                lbldatemsg.Text = "No Record Found";
                lbldatemsg.Visible = true;
                _logger.Info("No infraObject or Business service are assigned");
                return;
            }
            foreach (var app in appData)
            {
                var appl = new BusinessService();
                int healthStatus = 0;
                int groupcount = 0;
                int maintcount = 0;

                int avilcnt = 0;
                appl.Name = app.Name;
                appl.Description = app.Description;

                //bool IsAppBS = false;
                //IList<InfraObject> bs_groups = null;
                //IList<InfraObject> bs_appgroups = null;

                //bs_groups = Facade.GetInfraObjectByBusinessServiceId(Convert.ToInt32(app.Id));
                //bs_appgroups = (from data in bs_groups where data.Type == 1 select data).ToList();

                //if (bs_groups.Count == bs_appgroups.Count)
                //    IsAppBS = true;

              
                foreach (var groupse in groupgdata)
                {
                    if (groupse.BusinessServiceId == app.Id)
                    {
                        groupcount++;

                        if (groupse.State == "Maintenance")
                        {
                            maintcount++;
                        }
                        else
                        {
                            //if (!IsAppBS)
                            //{
                            bool isAvaliablity = CheckAvailablity(groupse);

                            if (isAvaliablity)
                            {
                                avilcnt++;
                            }
                            //}
                            //else
                            //{
                            //    avilcnt++;
                            //}
                        }

                    }

                }

                if (groupcount != 0)
                {
                    if (avilcnt == 0)
                    {
                        int down = groupcount - (avilcnt + maintcount);
                        appl.Up = avilcnt;
                        appl.Down = down;
                        appl.Maintenance = maintcount;
                        // appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);
                        //  appl.Health = "Critical";

                        if (groupcount == maintcount)
                            appl.Health = "NA";
                        else
                            appl.Health = "Critical";

                        //int down = groupcount - avilcnt;
                        //appl.Up = avilcnt;
                        //appl.Down = down;
                        //// appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avaliblitycount, " down : " + down, " Total : " + groupcount);
                        //appl.Health = healthStatus >= down ? "Strength" : "Critical";
                    }
                    else
                    {

                        int down = groupcount - (avilcnt + maintcount);
                        appl.Up = avilcnt;
                        appl.Down = down;
                        appl.Maintenance = maintcount;
                        //  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
                        //   appl.Health = avilcnt == groupcount ? "Strength" : "Critical";
                        if (groupcount == maintcount)
                            appl.Health = "NA";
                        else if (down == 0 && groupcount != maintcount)
                            appl.Health = "Strength";
                        else if (down > 0)
                            appl.Health = "Critical";

                        //int down = groupcount - avilcnt;
                        //appl.Up = avilcnt;
                        //appl.Down = down;

                        ////  appl.Availabilty = string.Format("({0},{1},{2})", " up : " + avilcnt, " down : " + down, " Total : " + groupcount);
                        //appl.Health = avilcnt >= down ? "Strength" : "Critical";
                    }
                }
                else
                {
                    appl.Availabilty = "NA";
                    appl.Health = "NA";
                }
                applicationGroups.Add(appl);
            }

            countData = applicationGroups.Count();

            if (countData <= 0)
            {
                btnPdfSave.Visible = true;
                Pan1.Visible = false;
                lbldatemsg.Text = "No Record Found";
                lbldatemsg.Visible = true;

                return;
            }

            DataTable d1t = new DataTable();
            d1t.Columns.Add("Sr.No.");
            d1t.Columns.Add("Business Service Names");
            d1t.Columns.Add("Business Service Description");
            d1t.Columns.Add("Up");
            d1t.Columns.Add("Down");
            d1t.Columns.Add("Maintenance");
            d1t.Columns.Add("DR Health");

            for (int rowCtr = 1; rowCtr <= 1; rowCtr++)
            {
                TableRow Trow = new TableRow();
                Trow.Font.Size = 9;
                tbl.Rows.Add(Trow);
                for (int cellCtr = 0; cellCtr < 7; cellCtr++)
                {
                    if (cellCtr == 0)
                    {
                        TableCell CellFzero = new TableCell();
                        CellFzero.Text = "Sr.No.";
                        CellFzero.Height = 35;
                        CellFzero.CssClass = "RowStyleHeaderNo bold";
                        Trow.Cells.Add(CellFzero);
                    }
                    else if (cellCtr == 1)
                    {
                        TableCell CellFone = new TableCell();
                        CellFone.Text = "Business Service Names";
                        CellFone.Height = 35;
                        CellFone.CssClass = "rowStyleHeader bold";
                        Trow.Cells.Add(CellFone);
                    }
                    else if (cellCtr == 2)
                    {
                        TableCell StImgIdbs = new TableCell();
                        StImgIdbs.Text = "Business Service Description";
                        StImgIdbs.Height = 35;
                        StImgIdbs.CssClass = "rowStyleHeader bold";
                        Trow.Cells.Add(StImgIdbs);
                    }


                    else if (cellCtr == 3)
                    {
                        TableCell StImgId = new TableCell();
                        StImgId.Text = "Up";
                        StImgId.Height = 35;
                        StImgId.CssClass = "rowStyleHeader bold";
                        Trow.Cells.Add(StImgId);
                    }
                    else if (cellCtr == 4)
                    {
                        TableCell StImgId = new TableCell();
                        StImgId.Text = "Down";
                        StImgId.Height = 35;
                        StImgId.CssClass = "rowStyleHeader bold";
                        Trow.Cells.Add(StImgId);
                    }
                    else if (cellCtr == 5)
                    {
                        TableCell StImgId = new TableCell();
                        StImgId.Text = "Maintenance";
                        StImgId.Height = 35;
                        StImgId.CssClass = "rowStyleHeader bold";
                        Trow.Cells.Add(StImgId);
                    }
                    else
                    {
                        TableCell tCell = new TableCell();
                        tCell.Text = "DR Health";
                        tCell.Height = 35;
                        tCell.CssClass = "rowStyleHeader bold";
                        Trow.Cells.Add(tCell);
                    }
                }
            }

            // List<string> healthValues = new List<string>();

            int i = 1;
            _logger.Info("======" + applicationGroups.Count + " Records Retrieve for Business Service Summary Report ======");
            _logger.Info(Environment.NewLine);
            foreach (var addData in applicationGroups)
            {
                for (int rowCtr = 1; rowCtr <= 1; rowCtr++)
                {
                    DataRow dr = d1t.NewRow();

                    TableRow Trow = new TableRow();
                    Trow.Height = 25;
                    Trow.BackColor = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                    tbl.Rows.Add(Trow);

                    TableCell Number = new TableCell();
                    Number.Text = i.ToString();
                    Number.CssClass = "RowStyleNo";
                    Trow.Cells.Add(Number);

                    TableCell AppGrpnme = new TableCell();
                    AppGrpnme.Text = addData.Name != null ? Convert.ToString(addData.Name) : "NA";
                    AppGrpnme.CssClass = "rowStyle1";
                    Trow.Cells.Add(AppGrpnme);

                    TableCell AppGrpnmebs = new TableCell();
                    AppGrpnmebs.Text = addData.Description != null ? Convert.ToString(addData.Description) : "NA";
                    AppGrpnmebs.CssClass = "rowStyle1";
                    Trow.Cells.Add(AppGrpnmebs);


                    TableCell Up = new TableCell();
                    Up.Text = addData.Up != 0 ? Convert.ToString(addData.Up) : "0";
                    Up.CssClass = "rowStyle1";
                    Trow.Cells.Add(Up);


                    TableCell Down = new TableCell();
                    Down.Text = addData.Down != 0 ? Convert.ToString(addData.Down) : "0";
                    Down.CssClass = "rowStyle1";
                    Trow.Cells.Add(Down);

                    TableCell Maintenance = new TableCell();
                    Maintenance.Text = addData.Maintenance != 0 ? Convert.ToString(addData.Maintenance) : "0";
                    Maintenance.CssClass = "rowStyle1";
                    Trow.Cells.Add(Maintenance);

                    TableCell Health = new TableCell();
                    Health.CssClass = "rowStyle1";

                    if (addData.Health != null && addData.Health == "Strength")
                    {
                        dr["DR Health"] = "Not Affected";
                        Health.Text = "Not Affected";//<img src=../images/icons/up-arrow.png>
                    }
                    else if (addData.Health != null && addData.Health == "Critical")
                    {
                        dr["DR Health"] = "Affected";
                        Health.Text = "Affected";//<img src=../images/icons/down-arrow.png>
                    }
                    else
                    {
                        dr["DR Health"] = "NA";
                        Health.Text = "NA";
                    }



                    Trow.Cells.Add(Health);


                    dr["Sr.No."] = i.ToString();
                    dr["Business Service Names"] = addData.Name != null ? Convert.ToString(addData.Name) : "NA";
                    dr["Business Service Description"] = addData.Description != null ? Convert.ToString(addData.Description) : "NA";
                    dr["Up"] = addData.Up != null ? Convert.ToString(addData.Up) : "0";
                    dr["Down"] = addData.Down != null ? Convert.ToString(addData.Down) : "0";
                    dr["Maintenance"] = addData.Maintenance != null ? Convert.ToString(addData.Maintenance) : "0";

                    // healthValues.Add(addData.Health);
                    i++;
                    d1t.Rows.Add(dr);
                }
            }

            _logger.Info("======Generating Business Service Summary Report PDF View ======");
            _logger.Info(Environment.NewLine);
            PdfDocument myPdfDocument = new PdfDocument(PdfDocumentFormat.InCentimeters(21, 29.7));
            PdfTable myPdfTable = myPdfDocument.NewTable(new Font("Verdana", 7), countData, 7, 4);
            myPdfTable.ImportDataTable(d1t);
            myPdfTable.HeadersRow.SetColors(Color.White, Color.FromArgb(79, 129, 189));
            myPdfTable.SetBorders(Color.Black, 0.1, BorderType.None);
            myPdfTable.HeadersRow.SetContentAlignment(ContentAlignment.MiddleLeft);
            myPdfTable.SetColors(Color.Black, Color.White, Color.FromArgb(219, 229, 241));
            myPdfTable.SetColumnsWidth(new int[] { 4, 7, 7, 3, 3, 4, 4 });
            myPdfTable.SetContentAlignment(ContentAlignment.MiddleLeft);

            PdfImage LogoImage =
                myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"));
            PdfImage LogoBcms = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"));

            string strlogo = LoggedInUserCompany.CompanyLogoPath.ToString();

            PdfImage complogo = null;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                complogo = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(strlogo));
            }

            PdfTextArea pta = new PdfTextArea(new Font("Verdana", 9, FontStyle.Bold), Color.Black
                                   , new PdfArea(myPdfDocument, 0, 40, 595, 80),
                                   ContentAlignment.MiddleCenter, "Business Services Summary Report");


           

            PdfTextArea reportTime = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                   , new PdfArea(myPdfDocument, 48, 10, 200, 190), ContentAlignment.MiddleLeft, "Report Generated Time : " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")));
            //DateTime.Now.ToString("dd-MMM-yyyy HH:mm"));

            var notavailable = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                     , new PdfArea(myPdfDocument, 230, 10, 100, 190), ContentAlignment.MiddleRight, "NA : Not Available");

            PdfTextArea reportTime1 = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                , new PdfArea(myPdfDocument, 48, 10, 200, 190), ContentAlignment.BottomRight, "Logged in User :" + " " + (LoggedInUser.LoginName != null ? LoggedInUser.LoginName : "NA"));

            //var healthup = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
            //         , new PdfArea(myPdfDocument, 216, 10, 150, 190), ContentAlignment.MiddleRight, "DR Health Up");

            //var healthdown = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
            //         , new PdfArea(myPdfDocument, 255, 10, 200, 190), ContentAlignment.MiddleRight, "DR Health Down");

            var logoHealthDownW = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/down-arrow.jpg"));
            var logoHealthUpW = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/up-arrow.jpg"));

            var logoHealthDownG = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/down-arrow-grey-bg1.jpg"));
            var logoHealthUpG = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/up-arrow-grey-bg1.jpg"));

            var logoHealthNAG = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/NA-2.jpg"));
            var logoHealthNAW = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/images/icons/NA-1.jpg"));

            int pgNo = 1;
            while (!myPdfTable.AllTablePagesCreated)
            {
                PdfPage newPdfPage = myPdfDocument.NewPage();
                PdfTablePage newPdfTablePage = myPdfTable.CreateTablePage(new PdfArea(myPdfDocument, 48, 110, 500, 670));

                PdfTextArea pageNumber = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                                                         , new PdfArea(myPdfDocument, 50, 0, 450, 1600),
                                                         ContentAlignment.MiddleRight,
                                                         "Page Number :   " + pgNo++.ToString());

                newPdfPage.Add(LogoImage, 460, 25, 180);
                newPdfPage.Add(LogoBcms, 50, 25, 110);
                if (complogo != null)
                    newPdfPage.Add(complogo, 270, 25, 120);

                newPdfPage.Add(newPdfTablePage);
                newPdfPage.Add(pta);

                newPdfPage.Add(notavailable);
                //newPdfPage.Add(healthup);
                //newPdfPage.Add(healthdown);

                //newPdfPage.Add(logoHealthUpW, 369, 99, 119);
                //newPdfPage.Add(logoHealthDownW, 459, 99, 120);

                newPdfPage.Add(reportTime);
                newPdfPage.Add(pageNumber);

                //for (int index = newPdfTablePage.FirstRow; index <= newPdfTablePage.LastRow; index++)
                //{
                //    var xaxis = newPdfTablePage.CellArea(index, 3).PosX + 35;
                //    var yaxis = newPdfTablePage.CellArea(index, 3).PosY + 3;
                //    var xdrhlth = newPdfTablePage.CellArea(index, 3).PosX + 29;

                //    if (healthValues[index] == "Critical")
                //    {
                //        newPdfPage.Add(index % 2 == 0 ? logoHealthDownW : logoHealthDownG, xaxis, yaxis);
                //        drHealth.Add("Critical");
                //    }
                //    else if (healthValues[index] == "Strength")
                //    {
                //        newPdfPage.Add(index % 2 == 0 ? logoHealthUpW : logoHealthUpG, xaxis, yaxis);
                //        drHealth.Add("Strength");
                //    }
                //    else
                //    {
                //        newPdfPage.Add(index % 2 == 0 ? logoHealthNAW : logoHealthNAG, xdrhlth, yaxis);
                //        drHealth.Add("NA");
                //    }
                //}
                newPdfPage.SaveToDocument();

            }

            if (Check == true)
            {
                var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
                str = "Business Service Summary Report_" + "_" + str + ".pdf";
                string filePath = Server.MapPath(@"~/PdfFiles/" + str);
                //string finalstr = !String.IsNullOrWhiteSpace(str) && str.Length >= 30 ? str.Substring(0, 30) : str;
                //str = finalstr + ".xls";
                //workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

                //string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";
                //string myUrl = "/PdfFiles/" + str;
                myPdfDocument.SaveToFile(filePath);
                //string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
                ////string myUrl = reportPath + "/PdfFiles/" + str;
                ////string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Server Report');";
                ////ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
                ////lblMsg.Text = String.Empty;
                //string pdfUrl = reportPath + "/PdfFiles/" + str;

                // Opening the PDF in a new window
                string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";
                string myUrl = baseUrl + "PdfFiles/" + str;
                //string script = "window.open('" + pdfUrl + "', '_blank')";
                var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Business Service Summary Report');";
                ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);

                
            }
            _logger.Info("====== Business Service Summary HTML Report generated ======");
            _logger.Info(Environment.NewLine);
        }

        private void OpenExcelFile(IWorkbook workbook)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = " BusinessServiceSummaryReport" + str + ".xls";
            string finalstr = !String.IsNullOrWhiteSpace(str) && str.Length >= 40 ? str.Substring(0, 40) : str;
            str = finalstr + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";

            string myUrl = baseUrl + "ExcelFiles/" + str;


            // string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            // string myUrl = reportPath + "/ExcelFiles/" + str;
            //var myUrl = "/ExcelFiles/" + str;
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=License Utilization Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }


        private bool CheckAvailablity(CP.Common.DatabaseEntity.InfraObject groupse)
        {
            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(groupse != null ? groupse.BusinessFunctionId : 0);

            if (groupse.RecoveryType == (int)ReplicationType.MSSQLDoubleTakeFullDB || groupse.RecoveryType == (int)ReplicationType.ApplicationDoubleTake || groupse.RecoveryType == (int)ReplicationType.OracleDoubleTakeFullDB)
            {
                MSSqlDoubletek getbyGrpid = Facade.GetCurrentMSSqlDoubletekHealthByInfraId(groupse.Id);
                var Business_Fun = Facade.GetBusinessFunctionByInfraObjectId(groupse.Id);
                double DataLagInBye = Convert.ToDouble(Business_Fun.DataLagInByte);

                double MirrorByteRemain = 0;
                double DataLagInBytemb = (DataLagInBye / 1024) / 1024;
                double MirrorByteRemainMB = -1;

                if (getbyGrpid != null)
                {
                    if (getbyGrpid.MirrorBytesRemaining != null && getbyGrpid.MirrorBytesRemaining != "NA")
                    {
                        MirrorByteRemain = Convert.ToDouble(getbyGrpid.MirrorBytesRemaining);
                        MirrorByteRemainMB = (MirrorByteRemain / 1024) / 1024;
                    }

                    if (DataLagInBye >= MirrorByteRemain)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            else if (groupse.RecoveryType == (int)ReplicationType.Undefined)
            {
                return true;
            }

            BusinessServiceRPOInfo dataLag = Facade.GetCurrentRPOInfraInfobyInfraObjectId(groupse.Id, groupse.BusinessFunctionId);
            if (dataLag != null)
            {
                bool isHealth = Utility.GetReportDatlagHealth(dataLag.CurrentRPO, businessFtn != null ? businessFtn.ConfiguredRPO : "00:00:00");

                if (dataLag.CurrentRPO.Contains('.'))
                {
                    return false;
                }
                else
                {
                    if (isHealth)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            return false;
        }



        private void DeleteRecords()
        {
            if (!Directory.Exists(Server.MapPath(@"~/PdfFiles")))
            {
                Directory.CreateDirectory(Server.MapPath(@"~/PdfFiles"));
            }
            var directory = new DirectoryInfo(Server.MapPath(@"~/PdfFiles"));

            foreach (FileInfo file in directory.GetFiles())
            {
                file.Delete();
            }
        }

        private void DeleteExcelFiles()
        {
            if (!Directory.Exists(Server.MapPath(@"~/ExcelFiles")))
            {
                Directory.CreateDirectory(Server.MapPath(@"~/ExcelFiles"));
            }
            var directory = new DirectoryInfo(Server.MapPath(@"~/ExcelFiles"));

            foreach (FileInfo file in directory.GetFiles())
            {
                file.Delete();
            }
        }

        #endregion Methods

        #region Events

        protected void btnPdfSave_Click(object sender, EventArgs e)
        {
            try
            {
                Pan1.Visible = true;
              
                ShowTable();
                btnPdfSave.Visible = false;
                _logger.DebugFormat("{0} - Generate Business Service summary report - {1}", HostAddress, LoggedInUserName);
            }
            catch (CpException ex)
            {
                _logger.Error("CpException Occurred In btnPdfSave_Click Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("CpException Occurred In btnPdfSave_Click Event, InnerException Message " + ex.InnerException.Message);
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In btnPdfSave_Click Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In btnPdfSave_Click Event, InnerException Message " + ex.InnerException.Message);
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void btnPdf_Click(object sender, EventArgs e)
        {
            try
            {
                Pan1.Visible = true;
                Check = true;
              
                ShowTable();
                btnPdfSave.Visible = false;
            }
            catch (CpException ex)
            {
                _logger.Error("CpException Occurred In btnPdf_Click Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("CpException Occurred In btnPdf_Click Event, InnerException Message " + ex.InnerException.Message);
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In btnPdf_Click Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In btnPdf_Click Event, InnerException Message " + ex.InnerException.Message);
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void btnExcel_Click(object sender, EventArgs e)
        {
            try
            {
                Pan1.Visible = true;
               
                ShowTable();
                prepareExcelfile();
                btnPdfSave.Visible = false;
                // ExcelReport();
            }
            catch (CpException ex)
            {
                _logger.Error("CpException Occurred In btnExcel_Click Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("CpException Occurred In btnExcel_Click Event, InnerException Message " + ex.InnerException.Message);
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In btnExcel_Click Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In btnExcel_Click Event, InnerException Message " + ex.InnerException.Message);
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            btnPdfSave.Visible = true;
            //RegisterPostBackControl();
        }

        public override void PrepareView()
        {
            // Utility.PopulateGroup(ddlGroup, true);
            DeleteRecords();
            DeleteExcelFiles();
        }

        #endregion Events
    }
}