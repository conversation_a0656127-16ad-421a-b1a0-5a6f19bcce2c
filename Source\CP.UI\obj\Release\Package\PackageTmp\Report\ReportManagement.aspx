﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="ReportManagement.aspx.cs" Inherits="CP.UI.ReportManagement" Title="Continuity Patrol :: Report-ReportManagement" %>

<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<%@ Register Src="~/Controls/DataLagReport.ascx" TagName="DataLagReports" TagPrefix="uc1" %>
<%@ Register Src="../Controls/GroupConfigurationReport.ascx" TagName="GroupConfiguration"
    TagPrefix="uc1" %>
<%@ Register Src="../Controls/GlobalMirrorReplicationReport.ascx" TagName="GlobalMirrorReplication"
    TagPrefix="uc1" %>
<%@ Register Src="../Controls/GroupSummaryReport.ascx" TagName="GroupSummaryReport"
    TagPrefix="uc1" %>
<%@ Register Src="../Controls/ApplicationGroupReport.ascx" TagName="ApplicationGroupReport"
    TagPrefix="uc1" %>
<%@ Register Src="../Controls/GlobalMirrorCGFormation.ascx" TagName="GlobalMirrorCGFormation"
    TagPrefix="uc1" %>
<%@ Register Src="../Controls/DatalagReport24Hrs.ascx" TagName="DatalagReport24Hrs"
    TagPrefix="uc1" %>
<%@ Register Src="../Controls/NewDataLag24Hours.ascx" TagName="NewDataLag24Hours"
    TagPrefix="uc1" %>
<%@ Register Src="../Controls/24HrsStatusReport.ascx" TagName="Status24hrs" TagPrefix="uc1" %>

<%@ Register Src="~/Controls/24HrsDatalagStatusReport.ascx" TagName="Datalagstatus"
    TagPrefix="uc1" %>

<%@ Register Src="~/Controls/ParallelDrOperationReport.ascx" TagName="ParallelDROperationReport"
    TagPrefix="uc1" %>
<%@ Register Src="~/Controls/DRDrillReportExcel.ascx" TagName="DRDrillExcelReport"
    TagPrefix="uc1" %>

<%@ Register Src="../Controls/FastcopyMonitorReport.ascx" TagName="FastcopyMonitorReport"
    TagPrefix="uc1" %>

<%@ Register Src="../Controls/ApplicationDiscovery.ascx" TagName="ApplicationDiscovery"
    TagPrefix="uc1" %>

<%@ Register Src="../Controls/RPMonitoringReport.ascx" TagName="RPMonitorReport" TagPrefix="uc1" %>

<%@ Register Src="../Controls/ApplicationdependancyReport.ascx" TagName="ApplicationdependancyReport"
    TagPrefix="uc1" %>

<%@ Register Src="../Controls/SMSReportlist.ascx" TagName="SMSReport" TagPrefix="uc1" %>
<%@ Register Src="../Controls/EmailReportlist.ascx" TagName="EmailReport" TagPrefix="uc1" %>

<%@ Register Src="~/Controls/DRHealthReport.ascx" TagName="DRHealthReport" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/UserActivityDetail.ascx" TagName="UserActivityDetailReport" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/AuditReport.ascx" TagName="AuditReport" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/SLAReport.ascx" TagName="SLAReport" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/MontlySummaryReport.ascx" TagName="MontlySummaryReport" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/ImportCMDBReport.ascx" TagName="ImportCMDBReport"
    TagPrefix="uc1" %>

<%@ Register Src="~/Controls/MultiserverprofilemoniReport.ascx" TagName="MultiserverprofilemoniReport"
    TagPrefix="uc1" %>
<%@ Register Src="~/Controls/VMCenterReport.ascx" TagName="VMCenterReport"
    TagPrefix="uc1" %>

<%@ Register Src="~/Controls/DRReaddynesssummary.ascx" TagName="DRReaddynesssummary" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/DRReddynesslog.ascx" TagName="DRReddynesslog" TagPrefix="uc1" %>

<%@ Register Src="~/Controls/GGReplicationReportHourly.ascx" TagName="GGReplicationReportHourly" TagPrefix="uc1" %>

<%@ Register Src="~/Controls/DRReadyRPT.ascx" TagName="DRReadyRPT" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/RTOReport.ascx" TagPrefix="uc1" TagName="RTOReport" %>

<%@ Register Src="~/Controls/RPOSLADeviationReport.ascx" TagName="RPOSLADeviationReports" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/AlertReport.ascx" TagPrefix="uc1" TagName="AlertReport" %>
<%@ Register Src="~/Controls/LicenseUtilizationRPT.ascx" TagPrefix="uc1" TagName="LicenseUtilizationRPT" %>




<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style type="text/css">
        .bootstrap-select[class*="col"] .btn {
            width: 99% !important;
        }

        .col-md-6 {
            padding-right: 0px !important;
            width: 48.5% !important;
        }

        .widget.widget-heading-simple > .widget-head {
            height: 34px !important;
        }

        tr > td:first-child {
            font-weight: normal;
            /*vertical-align: top;*/
        }
    </style>

    
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
     <style>
        .chosen-select + .chosen-container {
            width: 48.5% !important;
            opacity: 1 !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

                <div class="col-md-12" style="padding-bottom: 45px; padding-left: 0px; padding-right: 0px;">
                    <div class="col-md-6" style="padding-left: 0;">
                        <h3>
                            <img src="../Images/report-icon.png" />
                            Generate Reports</h3>
                    </div>
                    <div class="col-md-6 text-right" style="padding-right: 0; padding-top: 10px;">
                        <asp:Button ID="btnSchReport" runat="server" CssClass="btn" Width="20%" Text="Schedule Report" OnClick="btnSchReport_Click" Style="font-weight: normal; color: #428BCA;" />
                    </div>
                </div>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Select Category <span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlReport" runat="server" AutoPostBack="True" OnSelectedIndexChanged="DdlReportSelectedIndexChanged"
                                            CssClass="chosen-select col-md-6" data-style="btn-default">
                                            <%-- <asp:ListItem Selected="True" Value="1">RPO SLA Report</asp:ListItem>
                                            <asp:ListItem Value="2">InfraObject Configuration Report</asp:ListItem>
                                            <asp:ListItem Value="3">Global Mirror Replication Report</asp:ListItem>
                                            <asp:ListItem Value="4">InfraObject Summary Report</asp:ListItem>
                                            <asp:ListItem Value="5">Business Service Summary Report</asp:ListItem>
                                            <asp:ListItem Value="6">GlobalMirror CG Formation Report</asp:ListItem>
                                            <asp:ListItem Value="8">DataLag Status Report</asp:ListItem>
                                            <asp:ListItem Value="15">DR Drill Report</asp:ListItem>
                                            <asp:ListItem Value="18">DataSync Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="20">User Activity Report</asp:ListItem>
                                            <asp:ListItem Value="23">Application Discovery</asp:ListItem>
                                            <asp:ListItem Value="25">SMS Report</asp:ListItem>
                                            <asp:ListItem Value="26">Email Report</asp:ListItem>
                                            <asp:ListItem Value="27">Application Dependency Report</asp:ListItem>
                                            <asp:ListItem Value="28">VCenter Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="29">MultiServer Profile Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="30">CMDB Import Report</asp:ListItem>
                                             <asp:ListItem Value="31">DR Readiness Execution Log</asp:ListItem>
                                            <asp:ListItem Value="32">DR Readiness Summary</asp:ListItem>--%>
                                            <asp:ListItem Value="0" Selected="True">-- Select Category --</asp:ListItem>
                                            <asp:ListItem Value="1">Application Dependency Report</asp:ListItem>
                                            <asp:ListItem Value="2">Application Discovery</asp:ListItem>
                                            <asp:ListItem Value="3">Business Service Summary Report</asp:ListItem>
                                            <asp:ListItem Value="4">CMDB Import Report</asp:ListItem>
                                            <asp:ListItem Value="5">DataLag Status Report</asp:ListItem>
                                            <asp:ListItem Value="6">DataSync Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="7">DR Drill Report</asp:ListItem>
                                            <asp:ListItem Value="8">DR Readiness Execution Log</asp:ListItem>
                                            <%--<asp:ListItem Value="9">DR Readiness Summary</asp:ListItem>--%>
                                            <asp:ListItem Value="10">Email Report</asp:ListItem>
                                            <asp:ListItem Value="21">GoldenGate Replication Status Report</asp:ListItem>
                                            <asp:ListItem Value="11">GlobalMirror CG Formation Report</asp:ListItem>
                                            <asp:ListItem Value="12">Global Mirror Replication Report</asp:ListItem>
                                            <asp:ListItem Value="13">InfraObject Configuration Report</asp:ListItem>
                                            <asp:ListItem Value="14">InfraObject Summary Report</asp:ListItem>
                                            <asp:ListItem Value="15">MultiServer Profile Monitor Report</asp:ListItem>
                                             <asp:ListItem Value="16">RPO SLA Report</asp:ListItem>
                                            <asp:ListItem Value="20">RP Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="17">SMS Report</asp:ListItem>
                                            <asp:ListItem Value="18">User Activity Report</asp:ListItem>
                                            <asp:ListItem Value="19">VCenter Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="22">DR Ready Report</asp:ListItem>
                                            <asp:ListItem Value="23">RTO Report</asp:ListItem>
                                            <asp:ListItem Value="25">RPO SLA Deviation Report</asp:ListItem>

                                             <asp:ListItem Value="24">Alert Report</asp:ListItem>
                                            <asp:ListItem Value="26">License Utilization Report</asp:ListItem>

                                        </asp:DropDownList>
                                        <%--<asp:RequiredFieldValidator ID="rfvCategory" runat="server" InitialValue="0" ErrorMessage="Select Anyone Category"
                                            ControlToValidate="ddlReport" Display="Dynamic"></asp:RequiredFieldValidator>--%>
                                    </div>

                                </div>
                                <uc1:DataLagReports ID="ucDataLagReport" runat="server" Visible="false"></uc1:DataLagReports>
                                <uc1:GroupConfiguration ID="ucGroupConfiguration" runat="server" Visible="false"></uc1:GroupConfiguration>
                                <uc1:GlobalMirrorReplication ID="ucGlobalMirrorReplication" runat="server" Visible="false"></uc1:GlobalMirrorReplication>
                                <uc1:GroupSummaryReport ID="ucMediationGroup" runat="server" Visible="false"></uc1:GroupSummaryReport>
                                <uc1:ApplicationGroupReport ID="ucApplicationGroup" runat="server" Visible="false"></uc1:ApplicationGroupReport>
                                <uc1:GlobalMirrorCGFormation ID="ucGlobalMirrorCGFormation" runat="server" Visible="false" />
                                <uc1:DatalagReport24Hrs ID="ucDatalagReport24Hrs" runat="server" Visible="false" />
                                <uc1:NewDataLag24Hours ID="ucNewDataLag24Hours" runat="server" Visible="false" />
                                <uc1:Status24hrs ID="ucStatus24" runat="server" Visible="False" />
                                <uc1:ApplicationDiscovery ID="ucApplicationDiscovery" runat="server" Visible="false" />
                                <uc1:Datalagstatus ID="ucDatalagstatus" runat="server" Visible="false" />
                                <uc1:EmailReport ID="ucEmailReport" runat="server" Visible="False" />
                                <uc1:SMSReport ID="ucSMSReport" runat="server" Visible="False" />
                                <uc1:ParallelDROperationReport ID="ucParallelDROperationReport" runat="server" Visible="false" />
                                <uc1:DRDrillExcelReport ID="ucDRDrillExcelReport" runat="server" Visible="false" />
                                <uc1:ApplicationdependancyReport ID="ucApplicationdependancyReport" runat="server" Visible="true" />
                                <uc1:FastcopyMonitorReport ID="FastcopyMonitorReport" runat="server" Visible="false" />
                                <uc1:DRHealthReport ID="ucDRHealthReport" runat="server" Visible="false" />
                                <uc1:UserActivityDetailReport ID="UserActivityDetailReport" runat="server" Visible="false" />
                                <uc1:AuditReport ID="AuditReport" runat="server" Visible="false" />
                                <uc1:SLAReport ID="SLAReport" runat="server" Visible="false" />
                                <uc1:MontlySummaryReport ID="MontlySummaryReport" runat="server" Visible="false" />
                                <uc1:VMCenterReport ID="ucVMCenterReport" runat="server" Visible="false" />
                                <uc1:MultiserverprofilemoniReport ID="ucMultiserverprofilemoniReport" runat="server" Visible="false" />
                                <uc1:ImportCMDBReport ID="ucImportCMDBReport" runat="server" Visible="false" />
                                <uc1:DRReddynesslog ID="ucDRReddynesslog" runat="server" Visible="false" />
                                <uc1:DRReaddynesssummary ID="ucDRReaddynesssummary" runat="server" Visible="false" />
                                <uc1:RPMonitorReport ID="ucRPMonitor" runat="server" Visible="false" />
                                <uc1:GGReplicationReportHourly ID="ucGGReplicationReportHourly" runat="server" Visible="false" />
                                <uc1:DRReadyRPT ID="ucDRReadyRPT" runat="server" Visible="false" />

                                <uc1:RTOReport runat="server" ID="RTOReport"  Visible="false"  />

                                  <uc1:RPOSLADeviationReports ID="ucRPOSLADeviationReports" runat="server" Visible="false"></uc1:RPOSLADeviationReports>

                                <uc1:AlertReport ID="AlertReport" runat="server"  Visible="false" />    
                                <uc1:LicenseUtilizationRPT  ID="unLicenseUtilizationRPT" runat="server"  Visible="false" />

                                <div class="col-xs-4" style="display: none;">
                                    
                                    <asp:Label ID="lblProfileResult" runat="server" Text="&nbsp;"></asp:Label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <asp:Panel ID="panelCustom" runat="server" Width="450px" Height="550px" Style="margin: auto; display: none">
                    <asp:UpdatePanel ID="Updatepanel3" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True"
                        EnableViewState="False">
                        <ContentTemplate>
                            <div class="modal" style="display: block;">
                                <div class="modal-dialog" style="width: 888px">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <asp:LinkButton ID="lnkbtnClose" runat="server" CssClass="close" OnClick="CloseGroupWorkflowClick"
                                                CommandName="Close"> ×</asp:LinkButton>
                                            <h3 class="modal-title">Report Scheduler</h3>
                                        </div>
                                        <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                            <Triggers>
                                                <asp:AsyncPostBackTrigger ControlID="btnSchReport" EventName="Click" />
                                            </Triggers>
                                            <ContentTemplate>

                                               <%-- <iframe id="iframe1" src="../Report/ReportScheduler.aspx" frameborder="0"
                                                    width="866px" height="455px" runat="server" style="background-color: transparent;" />--%>

                                                  <asp:HtmlIframe id="iframe1" src="../Report/ReportScheduler.aspx" frameborder="0"
                                                    width="866px" height="455px" runat="server" style="background-color: transparent;">
                                                </asp:HtmlIframe>

                                            </ContentTemplate>
                                        </asp:UpdatePanel>


                                    </div>
                                </div>
                            </div>

                        </ContentTemplate>
                    </asp:UpdatePanel>
                </asp:Panel>
                <asp:Button runat="server" ID="HiddenForModal" Style="display: none" />
                <TK1:ModalPopupExtender ID="ModalPopupExtenderCustom" runat="server" TargetControlID="HiddenForModal"
                    RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelCustom"
                    PopupDragHandleControlID="panelCustom" Drag="true" BackgroundCssClass="bg">
                </TK1:ModalPopupExtender>
            </ContentTemplate>

        </asp:UpdatePanel>

    </div>
</asp:Content>
