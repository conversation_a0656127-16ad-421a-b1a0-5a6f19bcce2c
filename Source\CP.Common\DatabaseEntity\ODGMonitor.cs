﻿using CP.Common.Base;
using System;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    public class ActiveODGCluster : BaseEntity
    {
        public string ClusterName { get; set; }
        public string ClusterWareActiveVersion { get; set; }
        public string OHASStatus { get; set; }
        public string CRSStatus { get; set; }
        public string CSSStatus { get; set; }

        public string EVMStatus { get; set; }
        public string ClusterListener { get; set; }
        public string SCANStatus { get; set; }
        public string ScanListenerStatus { get; set; }

        public int InfraID { get; set; }
        public string PRDRType { get; set; }
        public int GroupNodeId { get; set; }
    }

    [Serializable]
    public class ActiveODGMultiTenancy : BaseEntity
    {
        public string CDB { get; set; }
        public string Containers { get; set; }
        public string PDBS { get; set; }
        public string PRDRType { get; set; }
        public int InfraID { get; set; }
    }

    [Serializable]
    public class ActiveODGPluggable : BaseEntity
    {
        public string DBId { get; set; }

        public string PRPDBName { get; set; }
        public string PRConnectionId { get; set; }
        public string PRPDBId { get; set; }
        public string PRPDBMode { get; set; }

        public string PRLogging { get; set; }
        public string PRForceLogging { get; set; }
        public decimal PRPDBSize { get; set; }
        public string PRRecoveryStatus { get; set; }

        public string DRPDBName { get; set; }
        public string DRConnectionId { get; set; }
        public string DRPDBId { get; set; }
        public string DRPDBMode { get; set; }

        public string DRLogging { get; set; }
        public string DRForceLogging { get; set; }
        public decimal DRPDBSize { get; set; }
        public string DRRecoveryStatus { get; set; }

        public int InfraID { get; set; }

    }

    [Serializable]
    public class ActiveODGDatabaseDetails : BaseEntity
    {
        public int InfraID { get; set; }

        public string Database_name { get; set; }
        public string Database_uniquename { get; set; }
        public string Instancename { get; set; }
        public string Instanceid { get; set; }
        public DateTime Instance_startuptime { get; set; }

        public DateTime Database_createdtime { get; set; }
        public string Database_version { get; set; }
        public string Database_role { get; set; }
        public string Openmode { get; set; }
        public string Database_incarnation { get; set; }

        public string Reset_logschange { get; set; }
        public string Reset_logsmode { get; set; }
        public string Control_filetype { get; set; }
        public string Control_filename { get; set; }
        public string Currentscn { get; set; }

        public string Parameterfile { get; set; }
        public string Archive_mode { get; set; }
        public string Flashback_on { get; set; }

        public string Platform_name { get; set; }
        public string Dbsize { get; set; }

        public string Db_create_file_dest { get; set; }
        public string Db_file_name_convert { get; set; }
        public string Db_create_online_log_dest1 { get; set; }
        public string Log_file_name_convert { get; set; }
        public string Db_recovery_file_dest { get; set; }

        public string Db_recovery_file_dest_size { get; set; }
        public string Db_flashback_retention_target { get; set; }
        public string Services { get; set; }
        public string PRDRType { get; set; }
    }

    [Serializable]
    public class ActiveODGReplDetails : BaseEntity
    {
        public int InfraID { get; set; }

        public string Active_DG_Enabled { get; set; }
        public string Archive_Dest_Location { get; set; }
        public string Protection_mode { get; set; }
        public string Transmit_mode { get; set; }
        public string Recovery_mode { get; set; }

        public string Affirm { get; set; }
        public string Archiver { get; set; }
        public string Archivelog_compression { get; set; }
        public string Delay_mins { get; set; }
        public string Log_sequence { get; set; }

        public string Dg_broker_status { get; set; }
        public string Remote_login_passwordfile { get; set; }
        public string Standby_file_management { get; set; }
        public string Standby_redo_logs { get; set; }
        public string Transport_lag { get; set; }

        public string Apply_lag { get; set; }
        public string Apply_finish_time { get; set; }
        public string Estimated_startup_time { get; set; }
        public string Force_logging { get; set; }
        public string Archive_generation_hourly { get; set; }

        public string Switchover_status { get; set; }
        public string Log_archive_config { get; set; }
        public string Fal_server { get; set; }
        public string Fal_client { get; set; }
        public string Dataguard_status { get; set; }
        public string Recoverystatus { get; set; }

        public string PRDRTYPE { get; set; }

        public int GroupNodeId { get; set; }
    }

    [Serializable]
    public class ActiveODGInstanceDetails : BaseEntity
    {
        public int InfraID { get; set; }
        public string InstanceName { get; set; }
        public int InstanceId { get; set; }
        public DateTime InstanceStartUpTime { get; set; }
        public string OpenMode { get; set; }
        public bool IsClusterDatabase  { get; set; }
        public string PrDrType { get; set; }
        public int GroupNodeId { get; set; }

        public string ControlFileName { get; set; }
        public string ParemeterFile { get; set; }
        public string PlatformName { get; set; }

    }

    [Serializable]
    public class ActiveODGASMDetails : BaseEntity
    {
        public int InfraID { get; set; }
        public int SRNO { get; set; }
        public string Name { get; set; }
        public string State { get; set; }
        public string Type { get; set; }
        public string TotalMB { get; set; }
        public string FreeMB { get; set; }
        public decimal Used { get; set; }
        public string PRDRType { get; set; }
        public int GroupNodeId { get; set; }
    }

    [Serializable]
    public class ActiveODGTNSServiceDetails : BaseEntity
    {
        public int InfraID { get; set; }
        public string PRService { get; set; }
        public string DRService { get; set; }

    }

    [Serializable]
    public class ADOGArchiveLogCountHourly : BaseEntity
    {
        public int InfraID { get; set; }
        public DateTime ArciveDate{ get; set; }
        public int hour00 { get; set; }
        public int hour01 { get; set; }
        public int hour02 { get; set; }
        public int hour03 { get; set; }
        public int hour04 { get; set; }
        public int hour05 { get; set; }
        public int hour06 { get; set; }
        public int hour07 { get; set; }
        public int hour08 { get; set; }
        public int hour09 { get; set; }
        public int hour10 { get; set; }
        public int hour11 { get; set; }

        public int hour12 { get; set; }
        public int hour13 { get; set; }
        public int hour14 { get; set; }
        public int hour15 { get; set; }
        public int hour16 { get; set; }
        public int hour17 { get; set; }
        public int hour18 { get; set; }
        public int hour19 { get; set; }
        public int hour20 { get; set; }
        public int hour21 { get; set; }
        public int hour22 { get; set; }
        public int hour23 { get; set; }
    }

    [Serializable]
    public class ADOGArchiveLogsizeHourly : ADOGArchiveLogCountHourly
    {

    }

    [Serializable]
    public class ADOGArchiveLogsizeWeekly : BaseEntity
    {
        public int InfraID { get; set; }
        public DateTime ArciveDate { get; set; }
        public int hour00 { get; set; }
        public int hour01 { get; set; }
        public int hour02 { get; set; }
        public int hour03 { get; set; }
        public int hour04 { get; set; }
        public int hour05 { get; set; }
        public int hour06 { get; set; }
    }

    [Serializable]
    public class ADOGReplicationNonODGDetails : BaseEntity
    {
        public int InfraID { get; set; }
        
        public string PRArchiveDestLocation { get; set; }
        public int PRDatabaseIncarnation { get; set; }
        public string PRLogSequence { get; set; }
        public string PRStandbyFileManagement { get; set; }
        public string PRForce_Logging { get; set; }

        public string DRArchiveDestLocation { get; set; }
        public int DRDatabaseIncarnation { get; set; }
        public string DRLogSequence { get; set; }
        public string DRStandbyFileManagement { get; set; }
        public string DRForce_Logging { get; set; }
    }
}
