﻿using System;
using System.Runtime.Serialization;

using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{

    [Serializable]
    [DataContract(Name = "EMCSRDFCGMonitoring", Namespace = "http://www.ContinuityPlatform.com/types")]
   
    public class EMCSRDFCGMonitoring :BaseEntity
    {
        [DataMember]
        public int InfraobjectId { get; set; }

        [DataMember]
        public string DGroupName { get; set; }

        [DataMember]
        public string DGType { get; set; }

        [DataMember]
        // public string DGSummetrixID { get; set; }
        public string DGSymmetrixID { get; set; }

        [DataMember]
        public string RemoteSymmetrixID { get; set; }

        [DataMember]
        public string RdfRaGroupNumber { get; set; }

        [DataMember]
        public string State { get; set; }

        [DataMember]
        public string PendingTracks { get; set; }

        [DataMember]
        public string DataLag { get; set; }

        [DataMember]
        public string InfraObjectName { get; set; }
    }
}
