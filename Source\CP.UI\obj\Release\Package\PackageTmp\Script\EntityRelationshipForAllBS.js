﻿/*  This js made by Ram - 25-04-2014 for Dashboard - Command Center UI -Business-IT relationship View  */

// This ajax call Renders Business-IT Relationship Daigram
// <author> <PERSON> - 25-04-2014 </author>
// Starts =====================
var globalstringdata = null;
var JsonForFullViewdata = null;
var isbtnReloadHide1 = false;
var leveldata = 0;
var GroupServiceIds = "";
//Renders first full view tree
function renderAjaxDataAllBSInfratoBf(businessid, level1, breadCrum) {

    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'businessid': businessid }),
        url: "../Admin/DesignProcessMoniter.aspx/LoadRuleServiceById",
        success: function (msg) {
            var value = msg.d.split('#');
            globalstringdata = value[1];
            var JsonObj = eval('(' + value[0] + ')');
            JsonForFullViewdata = JsonObj;
            var type = "BS";
            // alert(type);
            context.destroy(("svg image"), [{}]);
            treeShow1(JsonObj, level1, type, breadCrum);
        }
    });
}

//function renderAjaxDataAllBSInfratoBf1(businessid, level1, breadCrum, _isGroupServiceID) {

//                      
//    GroupServiceIds = _isGroupServiceID;

//    $.ajax({
//        type: "POST",
//        contentType: "application/json; charset=utf-8",
//        dataType: "json",
//        async: true,
//        data: JSON.stringify({ 'businessid': businessid }),
//         url: "../Admin/DesignProcessMoniter.aspx/LoadRuleServiceById",
//        success: function (msg) {
//            var value = msg.d.split('#');
//            globalstringdata = value[1];
//            var JsonObj = eval('(' + value[0] + ')');
//            JsonForFullViewdata = JsonObj;
//            var type = "BS";
//            // alert(type);
//            context.destroy(("svg image"), [{}]);
//            treeShow1(JsonObj, level1, type, breadCrum);
//        }
//    });
//}


function renderAjaxDataAllBSInfratoBf1(businessid, level1, breadCrum) {

    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'businessid': businessid }),
        url: "ConfigureBIARule.aspx/NodeRelationConverterForBS",
        success: function (msg) {
            var value = msg.d.split('#');
            globalstringdata = value[1];
            var JsonObj = eval('(' + value[0] + ')');
            JsonForFullViewdata = JsonObj;
            var type = "BS";
            context.destroy(("svg image"), [{}]);
           // treeShow1(JsonObj, level1, type, breadCrum);
            treeShowAllBSInfratoBf(JsonObj, level1, type, breadCrum);
        }
    });
}



function treeShowAllBSInfratoBf(root1, level1, type, breadCrum) {
    $("#divbsView").css("display", "block");
    $("#divIncView").css("display", "none");
    $('#divemptyFunctionPopup').css("display", "none");
    $('#bsbody').html("");

    i = 0, root;
    level = parseInt(level1);
    var duration = 750;
    var viewerWidth = 1338;
    var viewerHeight = 260;

    ShowBreadCrum1(breadCrum);


    function zoom() {
        vis.attr("transform", "translate(" + d3.event.translate + ")scale(" + d3.event.scale + ")");
    }


    function resetZoom() {
        d3.select("[id$=bsbody2]").select("svg").select("g")
       .transition().duration(750)
       .attr("transform", root1.name.length < 10 ? "translate(30,30)scale(0.90)" : "translate(90,30)scale(0.90)");
    };
    d3.select("#btnReset").on("click", resetZoom);


    function centerNode1(source) {
        scale = zoomListener.scale();
        x = -source.y0;
        y = -source.x0;
        x = 0;
        //y = y * scale + viewerHeight / 2; 
        y = 0;
        d3.select('g').transition()
        .duration(duration)
        .attr("transform", "translate(" + x + "," + y + ")scale(" + scale + ")");
        zoomListener.scale(scale);
        zoomListener.translate([x, y]);
    }


    var zoomListener = d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom);

    var tree = d3.layout.tree()
    .size([viewerHeight, viewerWidth]);

    var diagonal = d3.svg.diagonal()
    .projection(function (d) { return [d.y, d.x]; });

    var vis = d3.select("[id$=bsbody2]").append("svg:svg")
    .attr("width", viewerWidth)
    .attr("height", viewerHeight)
    .call(d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom)).on("dblclick.zoom", null)
    .append("g")
    //zoomListener.translate([100, 50]).scale(0.85);
    .attr("transform", root1.name.length < 10 ? "translate(30,30)scale(0.90)" : "translate(90,30)scale(0.90)");

    var root = root1;
    root.x0 = viewerHeight / 2;
    root.y0 = 0;


    function toggleAll1(d) {
        if (d.children) {
            d.children.forEach(toggleAll1);
            toggleOnLoad1(d);
        }
    }

    root.children.forEach(toggleAll1);
    update1(root);
    //centerNode1(root);


    function update1(source) {
        var duration = d3.event && d3.event.altKey ? 5000 : 500;

        var nodes = tree.nodes(root).reverse();


        nodes.forEach(function (d) { d.y = d.depth * 180; });


        var node = vis.selectAll("g.node")
        .data(nodes, function (d) { return d.id || (d.id = ++i); });


        var nodeEnter = node.enter().append("svg:g")
        .attr("class", "node")
        .attr("transform", function (d) { return "translate(" + source.y0 + "," + source.x0 + ")"; })
        //.on("click", function (d) { GetJsonFromNodeRelation(d); toggleOnClick1(d); update1(d); });
        .on("click", function (d) { toggleOnClick1(d); update1(d); });

        nodeEnter.append("svg:circle")
        .attr("r", 1e-6)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        if (level == 0) {
            nodeEnter.append("svg:text")
           .attr("x", function (d) { return returnx(d); })
           .attr("dy", function (d) { return returny(d); })
           .attr("text-anchor", function (d) { return returnTextAnchor(d); })
           .text(function (d) { return d.name.split('/')[0]; })
           .attr("class", function (d) { return returnTextAnchorClass(d); })
           .style("fill-opacity", 1e-6);


        }
        else {
            nodeEnter.append("svg:text")
           .attr("x", function (d) { return d.children || d._children ? 15 : 10; })
           .attr("dy", function (d) { return d.children || d._children ? "-0.5em" : ".35em"; })
           .attr("text-anchor", function (d) { return d.children || d._children ? "end" : "start"; })
           .text(function (d) { return d.name.split('/')[0]; })
           .attr("class", function (d) { return returnTextAnchorClass(d); })
           .style("fill-opacity", 1e-6);
        }

        nodeEnter.append("svg:image")
                .attr("xlink:href", function (d) { return d.logo; })
                .attr("x", function (d) { return d.children || d._children ? -6 : -6; })
                .attr("y", function (d) { return d.children || d._children ? "-0.5em" : "-0.5em"; })
                .attr("height", function (d) { return d.logoheight || 12; })
                .attr("width", function (d) { return d.logowidth || 12; })
                .attr("oncontextmenu", function (d) { return false; });




        var nodeUpdate = node.transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + d.y + "," + d.x + ")"; });

        nodeUpdate.select("circle")
        .attr("r", 5.0)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeUpdate.select("text")
        .style("fill-opacity", 1);


        var nodeExit = node.exit().transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + source.y + "," + source.x + ")"; })
        .remove();

        nodeExit.select("circle")
        .attr("r", 1e-6);

        nodeExit.select("text")
        .style("fill-opacity", 1e-6);


        var link = vis.selectAll("path.link")
        .data(tree.links(nodes), function (d) { return d.target.id; });


        link.enter().insert("svg:path", "g")
        .attr("class", "link")
        .style("stroke", function (d) { return d.target.level; })
        .attr("d", function (d) {
            var o = { x: source.x0, y: source.y0 };
            return diagonal({ source: o, target: o });
        })
        .transition()
        .duration(duration)
        .attr("d", diagonal);


        link.transition()
        .duration(duration)
        .attr("d", diagonal);


        link.exit().transition()
        .duration(duration)
        .attr("d", function (d) {
            var o = { x: source.x, y: source.y };
            return diagonal({ source: o, target: o });
        })
        .remove();


        nodes.forEach(function (d) {
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }
    function returnTextAnchorClass(d) {

        var textAnchorClass = "";

        if (d.ImpactType == "None") {
            textAnchorClass = "impact-text-black-bold";
        }
        if (d.ImpactType == "NoImpactCWR") {
            textAnchorClass = "impact-text-black-bold";
        }
        else if (d.ImpactType == "ündefined") {
            textAnchorClass = "impact-text-black-bold";
        }
        else if (d.ImpactType == "Partial ImpactBS") {
            textAnchorClass = "impact-text-black-bold";
        }
        else if (d.ImpactType == "Major ImpactBS") {
            textAnchorClass = "impact-text-black-bold";
        }
        else if (d.ImpactType == "Total ImpactBS") {
            textAnchorClass = "impact-text-black-bold";
        }
        else if (d.ImpactType == "Total Impact") {
            textAnchorClass = "impact-text-red";
        }
        else if (d.ImpactType == "Major Impact") {
            textAnchorClass = "impact-text-orange";
        }
        else if (d.ImpactType == "Partial Impact") {
            textAnchorClass = "impact-text-yellow";
        }
        else if (d.ImpactType == "Faint") {
            // textAnchorClass = "impact-text-none";
            textAnchorClass = "impact-text-black-bold";
        }

        return textAnchorClass;
    }

    function toggleOnLoad1(d) {
        if (d.children) {
            if (d.hide == "hide") {
                d._children = d.children;
                d.children = null;
            }
        } else {
            d.children = d._children;
            d._children = null;
        }
    }


    function toggleOnClick1(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }

        //if (level == 1) {
        if (d.parent) {
            if (d.children) {
                d.parent.children.forEach(function (element) {
                    if (d != element) {
                        collapse(element);
                    }
                });
            }
        }
        // }
    }


    function returnx(d) {
        var xPos = null;
        if (d.ImpactType == "Total Impact") {
            xPos = 10;
        }
        else if (d.ImpactType == "Partial ImpactBS") {
            xPos = -10;
        }
        else if (d.ImpactType == "Major ImpactBS") {
            xPos = -10;
        }
        else if (d.ImpactType == "Total ImpactBS") {
            xPos = -10;
        }
        else if (d.ImpactType == "Partial Impact") {
            xPos = 10;
        }
        else if (d.ImpactType == "Major Impact") {
            xPos = 10;
        }
        else if (d.hide == "hide") {
            xPos = 10;
        }
        else if (d.children && d.hide != "hide") {
            xPos = 15;
        }
        else {
            xPos = 10;
        }
        return xPos;
    }


    function returny(d) {
        var yPos = null;
        if (d.ImpactType == "Total Impact") {
            yPos = ".35em";
        }
        else if (d.ImpactType == "Partial Impact") {
            yPos = ".35em";
        }
        else if (d.ImpactType == "Major Impact") {
            yPos = ".35em";
        }
        else if (d.ImpactType == "Partial ImpactBS") {
            yPos = ".4em";
        }
        else if (d.ImpactType == "Major ImpactBS") {
            yPos = ".4em";
        }
        else if (d.ImpactType == "Total ImpactBS") {
            yPos = ".35em";
        }
        else if (d.hide == "hide") {
            yPos = ".35em";
        }
        else if (d.children && d.hide != "hide") {
            yPos = "-1em"
        }
        else {
            yPos = "-1em";
        }
        return yPos;
    }


    function returnTextAnchor(d) {
        var textAnchor = null;
        if (d.ImpactType == "Total Impact") {
            textAnchor = "start";
        }
        else if (d.ImpactType == "Partial ImpactBS") {
            textAnchor = "end";
        }
        else if (d.ImpactType == "Major ImpactBS") {
            textAnchor = "end";
        }
        else if (d.ImpactType == "Total ImpactBS") {
            textAnchor = "end";
        }
        else if (d.ImpactType == "Partial Impact") {
            textAnchor = "start";
        }
        else if (d.ImpactType == "Major Impact") {
            textAnchor = "start";
        }
        else if (d.hide == "hide") {
            textAnchor = "start";
        }
        else if (d.children && d.hide != "hide") {
            textAnchor = "end";
        }
        else {
            textAnchor = "start";
        }
        return textAnchor;
    }


    function GetJsonFromNodeRelation(d) {
        if (d.hide == "hide" && level == 0) {
            level = 1;
            var requiredClickedName = null;
            if (d.logo)
                requiredClickedName = d.name + "/Hide/logo/red" + ":";
            else
                requiredClickedName = d.name + "/Hide" + ":";

            var splitNodeRelation = globalstring3.split(";");
            if (splitNodeRelation.length > 0) {
                for (var i = 0; i < splitNodeRelation.length; i++) {
                    var index = splitNodeRelation[i].indexOf(requiredClickedName);
                    if (index == 0) {
                        var infraObjectRel = splitNodeRelation[i];
                        var splitInfraObjectRel = infraObjectRel.split(":");
                        var componentArray = splitInfraObjectRel[1].split(",");
                        if (componentArray.length > 0) {
                            for (var j = 0 ; j < componentArray.length; j++) {
                                var requiredComponentName = componentArray[j] + ":";
                                for (var k = i; k < splitNodeRelation.length; k++) {
                                    var componentArrayIndex = splitNodeRelation[k].indexOf(requiredComponentName)
                                    if (componentArrayIndex == 0) {
                                        infraObjectRel = infraObjectRel + ";" + splitNodeRelation[k];
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                var requiredNodeRelation = infraObjectRel;
                if (requiredNodeRelation != null) {
                    $('[id$=bsbody2]').html("");
                    renderAjaxDataForInfraObjects(requiredNodeRelation, level, "BFIO", breadCrum);
                    isbtnReloadHide3 = true;
                    d3.select("#btnReload").attr("style", "display:inline-block");
                }
            }
        }
    }


    function backToFullView() {
        $('[id$=bsbody2]').html("");
        level = 0;
        if (isbtnReloadHide3)
            d3.select("#btnReload").attr("style", "display:none");
        treeShowBStoBs(JsonForFullView3, level);
    }
    d3.select("#btnReload").on("click", backToFullView);


    function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d._children.forEach(collapse);
            d.children = null;
        }
    }
}




//Renders second infraobject components tree
function renderAjaxDataForInfraObjects(infraObjectNodeRelation, commonLevel, type, breadCrum) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'infraObjectNodeRelation': infraObjectNodeRelation }),
        url: "EntityImpactRelationshipForm.aspx/NodeRelationConverterForInfraObjects",
        success: function (msg) {
            var JsonObj = eval('(' + msg.d + ')');
            if (type == "BF" || type == "IO")
                JsonForFullViewdata = JsonObj;
            //alert(type);
            treeShow1(JsonObj, commonLevel, type, breadCrum);
        }
    });
}

//draw BIT daigram

function treeShow1(root1, level1, type, breadCrum) {
    $("#divbsView").css("display", "block");
    $("#divIncView").css("display", "none");
    $('#divemptyFunctionPopup').css("display", "none");
    $('#bsbody').html("");
    i = 0, root;
    leveldata = parseInt(level1);
    var duration = 750;
    var viewerWidth = 1295;
    var viewerHeight = 280;

    ShowBreadCrum1(breadCrum);
    //Define the zoom function for the zoomable tree

    function zoom() {
        vis.attr("transform", "translate(" + d3.event.translate + ")scale(" + d3.event.scale + ")");
    }

    // Function to reset Zoom level
    function resetZoom() {
        d3.select("[id$=bsbody2]").select("svg").select("g")
       .transition().duration(750)
       .attr("transform", root1.name.length < 10 ? "translate(30,30)scale(0.90)" : "translate(90,30)scale(0.90)");
    };
    d3.select("#btnReset").on("click", resetZoom);

    // Function to center node when clicked/dropped so node doesn't get lost when collapsing/moving with large amount of children.
    function centerNode1(source) {
        scale = zoomListener.scale();
        x = -source.y0;
        y = -source.x0;
        x = 0;
        //y = y * scale + viewerHeight / 2; // Commented - Kuntesh Thakker - 7-04-2014 as it disturbs the RTO RPO graph design
        y = 0;
        d3.select('g').transition()
        .duration(duration)
        .attr("transform", "translate(" + x + "," + y + ")scale(" + scale + ")");
        zoomListener.scale(scale);
        zoomListener.translate([x, y]);
    }

    // define the zoomListener which calls the zoom function on the "zoom" event constrained within the scaleExtents
    var zoomListener = d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom);

    var tree = d3.layout.tree()
    .size([viewerHeight, viewerWidth]);

    var diagonal = d3.svg.diagonal()
    .projection(function (d) { return [d.y, d.x]; });

    var vis = d3.select("[id$=bsbody2]").append("svg:svg")
    .attr("width", viewerWidth)
    .attr("height", viewerHeight)
    .call(d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom)).on("dblclick.zoom", null)
    .append("g")
    //zoomListener.translate([100, 50]).scale(0.85);
    .attr("transform", root1.name.length < 10 ? "translate(30,30)scale(0.90)" : "translate(90,30)scale(0.90)");

    var root = root1;
    root.x0 = viewerHeight / 2;
    root.y0 = 0;

    //toggle node
    function toggleAll1(d) {
        if (d.children) {
            d.children.forEach(toggleAll1);
            toggleOnLoad1(d);
        }
    }
    // Initialize the display to show a few nodes.
    root.children.forEach(toggleAll1);
    update1(root);
    //centerNode1(root);

    //update1 node
    function update1(source) {
        var duration = d3.event && d3.event.altKey ? 5000 : 500;

        // Compute the new tree layout.
        var nodes = tree.nodes(root).reverse();

        // Normalize for fixed-depth.
        nodes.forEach(function (d) { d.y = d.depth * 180; });

        // update1 the nodes…
        var node = vis.selectAll("g.node")
        .data(nodes, function (d) { return d.id || (d.id = ++i); });

        // Enter any new nodes at the parent's previous position.
        var nodeEnter = node.enter().append("svg:g")
        .attr("class", "node")
        .attr("transform", function (d) { return "translate(" + source.y0 + "," + source.x0 + ")"; })
        .on("click", function (d) { toggleOnClick1(d); update1(d); });

        nodeEnter.append("svg:circle")
        .attr("r", 1e-6)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeEnter.append("svg:text")
     .attr("x", function (d) { return returnx1(d); })
     .attr("dy", function (d) { return returny1(d); })
     .attr("text-anchor", function (d) { return returnTextAnchor1(d); })
     .attr("class", function (d) { return returnTextAnchorClass1(d.name, d.ImpactType); })
     .text(function (d) {
         if (d.name == undefined)
             return "";
         if (d.name.indexOf("$") != -1)
             return d.name.split("$")[1];

         else
             return d.name.substring(d.name.substring(d.name.lastIndexOf('@') + 1));
     })

        //Add an image to the node (if any)
        nodeEnter.append("svg:image")
                .attr("xlink:href", function (d) {
                    var apppre = d.name.substring(d.name.lastIndexOf('@') + 1, d.name.lastIndexOf('$'))

                    if (apppre.indexOf("IS") >= 0)
                        return GetNodeImgPathByName1("IS", d.ImpactType);
                    else if (apppre.indexOf("F_") >= 0)
                        return GetNodeImgPathByName1("F", d.ImpactType);
                    else if (apppre.indexOf("I_") >= 0)
                        return GetNodeImgPathByName1("I", d.ImpactType);
                    else if (apppre.indexOf("S_") >= 0)
                        return GetNodeImgPathByName1("S", d.ImpactType);
                    else if (apppre.indexOf("ICSV_") >= 0)
                        return GetNodeImgPathByName1("ICSV", d.ImpactType);
                    else if (apppre.indexOf("ICD_") >= 0)
                        return GetNodeImgPathByName1("ICD", d.ImpactType);
                    else if (apppre.indexOf("ICR_") >= 0)
                        return GetNodeImgPathByName1("ICR", d.ImpactType);
                    else
                        return GetNodeImgPathByName1("");
                })
                .attr("x", function (d) { return d.children || d._children ? -6 : -6; })
                //.attr("y", function (d) { return d.children || d._children ? "-0.9em" : "-0.9em"; })
               .attr("y", function (d) { return d.children || d._children ? GetRadius(d.name, d.ImpactType) : GetRadius(d.name, d.ImpactType); }) // d._children ? "-0.5em" : "-0.5em"; })

                .attr("height", function (d) {
                    return d.logoheight || GetNodeImgHightByName1(d.name, d.ImpactType);
                })
                .attr("width", function (d) {
                    return d.logowidth || GetNodeImgWidthByName1(d.name, d.ImpactType);
                })
             .on("contextmenu", function (d, nodes, links) {
                 apptype = d.name.split('$')[0].split('_')[0];
                 var lastRightClickNodein = d;
                 var linkToPage = "";
                 linkToPage = "../ImpactAnalysis/ImpactConfiguration.aspx";

                 if (apptype == "I" && apptype.lastIndexOf("I") >= 0) {
                     context.attach(("svg image"), [
                     {
                         text: "Add/Update Impact ", href: "javascript:void(0);", action: function (e) {
                             var lstNode = d;
                             var parentbfId = d.parent.name.split('$')[0].split('_')[1];
                             var parentbsId = d.parent.parent.name.split('$')[0].split('_')[1];
                             var infrsId = d.name.split('$')[0].split('_')[1];
                             var infraChildIds = "";
                             var IPComp = "";
                             var IsINfraLength;


                             openInfratoBSRadWindow(linkToPage + "?" + "INfraObjectId=" + infrsId + "&" + "BFId=" + parentbfId + "&" + "BSId=" + parentbsId + "&" + "GroupServiceId=" + GroupServiceIds);

                         }
                     }

                     ]);
                 }
                 else {
                     context.destroy("svg image");

                 }

             });


        // Transition nodes to their new position.
        var nodeUpdate = node.transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + d.y + "," + d.x + ")"; });

        nodeUpdate.select("circle")
        .attr("r", 0)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeUpdate.select("text")
        .style("fill-opacity", 1);

        // Transition exiting nodes to the parent's new position.
        var nodeExit = node.exit().transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + source.y + "," + source.x + ")"; })
        .remove();

        nodeExit.select("circle")
        .attr("r", 1e-6);

        nodeExit.select("text")
        .style("fill-opacity", 1e-6);

        // update1 the links…
        var link = vis.selectAll("path.link")
        .data(tree.links(nodes), function (d) { return d.target.id; });

        // Enter any new links at the parent's previous position.
        link.enter().insert("svg:path", "g")

        .attr("class", "link")
        .style("stroke", function (d) { return linkColor(d); })
        .attr("d", function (d) {
            var o = { x: source.x0, y: source.y0 };
            return diagonal({ source: o, target: o });
        })
        .transition()
        .duration(duration)
        .attr("d", diagonal);

        // Transition links to their new position.
        link.transition()
        .duration(duration)
        .attr("d", diagonal);

        // Transition exiting nodes to the parent's new position.
        link.exit().transition()
        .duration(duration)
        .attr("d", function (d) {
            var o = { x: source.x, y: source.y };
            return diagonal({ source: o, target: o });
        })
        .remove();

        // Stash the old positions for transition.
        nodes.forEach(function (d) {
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }

    // Toggle children when page loads.
    function toggleOnLoad1(d) {

        switch (d.ImpactType) {

            case "PII":
                d._children = d.children;
                d.children = null;
                break;
            case "MII":
                d._children = d.children;
                d.children = null;
                break;
            case "TII":
                d._children = d.children;
                d.children = null;
                break;
            case "NAI":
                d._children = d.children;
                d.children = null;
                break;
            case "TIICSV":
                d._children = d.children;
                d.children = null;
                break;

            case "NAICSV":
                d._children = d.children;
                d.children = null;
                break;

            case "TIICD":
                d._children = d.children;
                d.children = null;
                break;

            case "NAICD":
                d._children = d.children;
                d.children = null;
                break;

            case "TIICR":
                d._children = d.children;
                d.children = null;
                break;

            case "NAICR":
                d._children = d.children;
                d.children = null;
                break;

        }

    }


    //Toggle children when node is clicked.
    function toggleOnClick1(d) {

        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {

            d.children = d._children;
            d._children = null;
        }
        // If the node has a parent, then collapse its child nodes
        // except for this clicked node.
        if (leveldata == 1) {
            if (d.parent) {
                if (d.children) {
                    d.parent.children.forEach(function (element) {
                        if (d != element) {
                            collapse(element);
                        }
                    });
                }
            }
        }
    }

    //return x value for node
    function returnx1(d) {
        var xPos = null;
        if (d.hide == "hide") {
            xPos = 30;
        }
        else if (d.children && d.hide != "hide") {
            xPos = 30;
        }
        else {
            xPos = 30;
        }
        return xPos;
    }

    //return y value for node
    function returny1(d) {
        var yPos = null;
        if (d.hide == "hide") {
            yPos = ".35em";
        }
        else if (d.children && d.hide != "hide") {
            yPos = "-2em"
        }
        else {
            yPos = ".35em";
        }
        return yPos;
    }

    //For Link Color change
    function linkColor(d) {

        if (d != undefined) {
            var StrSource = d.source.ImpactType;
            var StrTarget = d.target.ImpactType;

            var Str = StrSource + "_" + StrTarget;

            switch (Str) {
                case "PIBS_NABS":
                    return "#1b75bd";
                    break;
                case "PIBS_PIBS":
                    return "#ffb406";
                    break;
                case "PIBS_MIBS":
                    return "#fb5a0c";
                    break;
                case "PIBS_TIBS":
                    return "#FF0000";
                    break;

                case "MIBS_NABS":
                    return "#1b75bd";
                    break;
                case "MIBS_MIBS":
                    return "#fb5a0c";
                    break;
                case "MIBS_PIBS":
                    return "#ffb406";
                    break;
                case "MIBS_TIBS":
                    return "#FF0000";
                    break;

                case "TIBS_NABS":
                    return "#1b75bd";
                    break;
                case "TIBS_TIBS":
                    return "#FF0000";
                    break;
                case "TIBS_PIBS":
                    return "#ffb406";
                    break;
                case "TIBS_MIBS":
                    return "#fb5a0c";
                    break;

                case "NABS_NABS":
                    return "#1b75bd";
                    break;
                case "NABS_PIBS":
                    return "#1b75bd";
                    break;
                case "NABS_MIBS":
                    return "#1b75bd";
                    break;
                case "NABS_TIBS":
                    return "#1b75bd";
                    break;
                case "PIBS_NABF":
                    return "#1b75bd";
                    break;

                case "PIBS_PIBF":
                    return "#ffb406";
                    break;
                case "PIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "PIBS_TIBF":
                    return "#FF0000";
                    break;
                case "MIBS_NABF":
                    return "#1b75bd";
                    break;
                case "MIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "MIBS_PIBF":
                    return "#ffb406";
                    break;
                case "MIBS_TIBF":
                    return "#FF0000";
                    break;
                case "TIBS_NABF":
                    return "#1b75bd";
                    break;
                case "TIBS_TIBF":
                    return "#FF0000";
                    break;
                case "TIBS_PIBF":
                    return "#ffb406";
                    break;
                case "TIBS_MIBF":
                    return "#fb5a0c";
                    break;
                case "NABS_NABF":
                    return "#1b75bd";
                    break;
                case "NABS_PIBF":
                    return "#1b75bd";
                    break;
                case "NABS_MIBF":
                    return "#1b75bd";
                    break;
                case "NABS_TIBF":
                    return "#1b75bd";
                    break;
                case "PIBF_NABF":
                    return "#1b75bd";
                    break;
                case "PIBF_PIBF":
                    return "#ffb406";
                    break;
                case "PIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "PIBF_TIBF":
                    return "#ff0000";
                    break;
                case "MIBF_NABF":
                    return "#1b75bd";
                    break;
                case "MIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "MIBF_PIBF":
                    return "#ffb406";
                    break;
                case "MIBF_TIBF":
                    return "#FF0000";
                    break;
                case "TIBF_NABF":
                    return "#1b75bd";
                    break;
                case "TIBF_TIBF":
                    return "#FF0000";
                    break;
                case "TIBF_PIBF":
                    return "#ffb406";
                    break;
                case "TIBF_MIBF":
                    return "#fb5a0c";
                    break;
                case "NABF_NABF":
                    return "#1b75bd";
                    break;
                case "NABF_PIBF":
                    return "#1b75bd";
                    break;
                case "NABF_MIBF":
                    return "#1b75bd";
                    break;
                case "NABF_TIBF":
                    return "#1b75bd";
                    break;
                case "PIBF_NAI":
                    return "#1b75bd";
                    break;
                case "PIBF_PII":
                    return "#ffb406";
                    break;
                case "PIBF_MII":
                    return "#fb5a0c";
                    break;
                case "PIBF_TII":
                    return "#ff0000";
                    break;
                case "MIBF_NAI":
                    return "#1b75bd";
                    break;
                case "MIBF_MII":
                    return "#fb5a0c";
                    break;
                case "MIBF_PII":
                    return "#ffb406";
                    break;
                case "MIBF_TII":
                    return "#FF0000";
                    break;
                case "TIBF_NAI":
                    return "#1b75bd";
                    break;
                case "TIBF_TII":
                    return "#FF0000";
                    break;
                case "TIBF_PII":
                    return "#ffb406";
                    break;
                case "TIBF_MII":
                    return "#fb5a0c";
                    break;
                case "NABF_NAI":
                    return "#1b75bd";
                    break;
                case "NABF_PII":
                    return "#1b75bd";
                    break;
                case "NABF_MII":
                    return "#1b75bd";
                    break;
                case "NABF_TII":
                    return "#1b75bd";
                    break;
                case "TII_TIICSV":
                    return "#ff0000";
                    break;
                case "TII_NAICSV":
                    return "#1b75bd";
                case "NAI_TIICSV":
                    return "#1b75bd";
                    break;
                case "NAI_NAICSV":
                    return "#1b75bd";
                    break;

                case "TII_TIICD":
                    return "#FF0000";
                    break;
                case "TII_NAICD":
                    return "#1b75bd";
                case "NAI_TIICD":
                    return "#1b75bd";
                    break;
                case "NAI_NAICD":
                    return "#1b75bd";
                    break;
                case "TII_TIICR":
                    return "#FF0000";
                    break;
                case "TII_NAICR":
                    return "#1b75bd";
                case "NAI_TIICR":
                    return "#1b75bd";
                    break;
                case "NAI_NAICR":
                    return "#1b75bd";
                    break;
                case "NAI_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICSV_TIIS":
                    return "#FF0000";
                    break;
                case "TIICSV_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICSV_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICSV_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICD_TIIS":
                    return "#FF0000";
                    break;
                case "TIICD_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICD_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICD_NAIS":
                    return "#1b75bd";
                    break;
                case "TIICR_TIIS":
                    return "#FF0000";
                    break;
                case "TIICR_NAIS":
                    return "#1b75bd";
                    break;
                case "NAICR_TIIS":
                    return "#1b75bd";
                    break;
                case "NAICR_NAIS":
                    return "#1b75bd";
                    break;
                case "NABF_NAICSV":
                    return "#1b75bd";
                    break;
                case "PIBF_NAICSV":
                    return "#1b75bd";
                    break;
                default:
                    return "#1b75bd";
            }
        }
    }

    //====================Get Node img Hight =========================
    function GetNodeImgHightByName1(name, impactType) {
        if (name != null) {
            var strName;
            if (name.indexOf("^") >= 0) {
                var iscap = name.split('^');
                strName = iscap[1].split("$")[0]
            }
            else {
                strName = name.substring(name.lastIndexOf('@') + 1, name.indexOf('$'))
            }
            var splitArra = strName.split('_');

            var Str = splitArra[0] + "_" + impactType;

            switch (Str) {
                case "S_PIBS":
                    return 47;
                    break;
                case "S_MIBS":
                    return 47;
                    break;
                case "S_TIBS":
                    return 47;
                    break;
                case "S_NABS":
                    return 47;
                    break;
                case "F_PIBF":
                    return 33;
                    break;
                case "F_MIBF":
                    return 33;
                    break;
                case "F_TIBF":
                    return 33;
                    break;
                case "F_NABF":
                    return 32;
                    break;
                case "I_PII":
                    return 33;
                    break;
                case "I_MII":
                    return 33;
                    break;
                case "I_TII":
                    return 33;
                    break;
                case "I_NAI":
                    return 32;
                    break;
                case "ICSV_TIICSV":
                    return 22;
                    break;
                case "ICSV_NAICSV":
                    return 22;
                    break;
                case "ICD_TIICD":
                    return 20;
                    break;
                case "ICD_NAICD":
                    return 20;
                    break;
                case "ICR_TIICR":
                    return 18;
                    break;
                case "ICR_NAICR":
                    return 18;
                    break;
                case "IS_TIIS":
                    return 14;
                    break;
                case "IS_NAIS":
                    return 14;
                    break
                default:
                    return 14;
                    break;
            }

        }
    }

    //====================Get Node img Width =========================
    function GetNodeImgWidthByName1(name, impactType) {
        if (name != null) {
            var strName;
            if (name.indexOf("^") >= 0) {
                var iscap = name.split('^');
                strName = iscap[1].split("$")[0]
            }
            else {
                strName = name.substring(name.lastIndexOf('@') + 1, name.indexOf('$'))
            }


            var splitArra = strName.split('_');

            var Str = splitArra[0] + "_" + impactType;

            switch (Str) {
                case "S_PIBS":
                    return 53;
                    break;
                case "S_MIBS":
                    return 53;
                    break;
                case "S_TIBS":
                    return 53;
                    break;
                case "S_NABS":
                    return 47;
                    break;
                case "F_PIBF":
                    return 33;
                    break;
                case "F_MIBF":
                    return 33;
                    break;
                case "F_TIBF":
                    return 33;
                    break;
                case "F_NABF":
                    return 32;
                    break;
                case "I_PII":
                    return 33;
                    break;
                case "I_MII":
                    return 33;
                    break;
                case "I_TII":
                    return 33;
                    break;
                case "I_NAI":
                    return 32;
                    break;
                case "ICSV_TIICSV":
                    return 18;
                    break;
                case "ICSV_NAICSV":
                    return 18;
                    break;
                case "ICD_TIICD":
                    return 16;
                    break;
                case "ICD_NAICD":
                    return 16;
                    break;
                case "ICR_TIICR":
                    return 18;
                    break;
                case "ICR_NAICR":
                    return 18;
                    break;
                case "IS_TIIS":
                    return 14;
                    break;
                case "IS_NAIS":
                    return 14;
                    break
                default:
                    return 14;
                    break;
            }


        }
    }

    //====================Get Node img Hight =========================
    function GetRadius(name, impactType) {
        if (name != null) {
            var strName;
            if (name.indexOf("^") >= 0) {
                var iscap = name.split('^');
                strName = iscap[1].split("$")[0]
            }
            else {
                strName = name.substring(name.lastIndexOf('@') + 1, name.indexOf('$'))
            }

            var splitArra = strName.split('_');

            var Str = splitArra[0] + "_" + impactType;

            switch (Str) {
                case "S_PIBS":
                    return "-1.6em";
                    break;
                case "S_MIBS":
                    return "-1.6em";
                    break;
                case "S_TIBS":
                    return "-1.6em";
                    break;
                case "S_NABS":
                    return "-1.6em";
                    break;
                case "F_PIBF":
                    return "-1.2em";
                    break;
                case "F_MIBF":
                    return "-1.2em";
                    break;
                case "F_TIBF":
                    return "-1.2em";
                    break;
                case "F_NABF":
                    return "-1.2em";
                    break;
                case "I_PII":
                    return "-1.2em";
                    break;
                case "I_MII":
                    return "-1.2em";
                    break;
                case "I_TII":
                    return "-1.2em";
                    break;
                case "I_NAI":
                    return "-1.2em";
                    break;
                case "ICSV_TIICSV":
                    return "-0.9em";
                    break;
                case "ICSV_NAICSV":
                    return "-0.9em";
                    break;
                case "ICD_TIICD":
                    return "-0.9em";
                    break;
                case "ICD_NAICD":
                    return "-0.9em";
                    break;
                case "ICR_TIICR":
                    return "-0.9em";
                    break;
                case "ICR_NAICR":
                    return "-0.9em";
                    break;
                case "IS_TIIS":
                    return "-0.5em";
                    break;
                case "IS_NAIS":
                    return "-0.5em";
                    break

                default:
                    return "-0.5em";
                    break;
            }

        }
    }


    // Gets Json for infraObjects details from node relation
    function GetJsonFromNodeRelation(d) {
        if (d.hide == "hide" && leveldata == 0) {
            leveldata = 1;
            var requiredClickedName = null;
            if (d.logo)
                requiredClickedName = d.name + "/Hide/logo/red" + ":";
            else
                requiredClickedName = d.name + "/Hide" + ":";

            var splitNodeRelation = globalstringdata.split(";");
            if (splitNodeRelation.length > 0) {
                for (var i = 0; i < splitNodeRelation.length; i++) {
                    var index = splitNodeRelation[i].indexOf(requiredClickedName);
                    if (index == 0) {
                        var infraObjectRel = splitNodeRelation[i];
                        var splitInfraObjectRel = infraObjectRel.split(":");
                        var componentArray = splitInfraObjectRel[1].split(",");
                        if (componentArray.length > 0) {
                            for (var j = 0 ; j < componentArray.length; j++) {
                                var requiredComponentName = componentArray[j] + ":";
                                for (var k = i; k < splitNodeRelation.length; k++) {
                                    var componentArrayIndex = splitNodeRelation[k].indexOf(requiredComponentName)
                                    if (componentArrayIndex == 0) {
                                        infraObjectRel = infraObjectRel + ";" + splitNodeRelation[k];
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                var requiredNodeRelation = infraObjectRel;
                if (requiredNodeRelation != null) {
                    $('[id$=bsbody2]').html("");
                    renderAjaxDataForInfraObjects(requiredNodeRelation, leveldata, "BFIO", breadCrum);
                    isbtnReloadHide1 = true;
                    d3.select("#btnReload").attr("style", "display:inline-block");
                }
            }
        }
    }

    // Resets tree to default full view
    function backToFullView() {
        $('[id$=bsbody2]').html("");
        leveldata = 0;
        if (isbtnReloadHide1)
            d3.select("#btnReload").attr("style", "display:none");
        treeShow1(JsonForFullViewdata, leveldata);
    }
    d3.select("#btnReload").on("click", backToFullView);

    //collapse other node while expanding current clicked/selected node
    function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d._children.forEach(collapse);
            d.children = null;
        }
    }
}

// Ends ======================

//Clears svg if no graph to render
function clearGraph(type, breadCrum) {

    $("#spnBreadCrum").text(breadCrum);

    $("[id$=bsbody2]").html("");

    $("#divbsView").css("display", "none");
    $("#divIncView").css("display", "none");

    $('#divemptyFunctionPopup').css("display", "block");

    if (type == "BF")
        // $("#divemptyFunctionPopup").text("Need to configure InfraObjects");
        $("#divemptyFunctionPopup").html('<div class="row well center "><div class="col-md-3"><a class="glyphicons warning_sign pull-left"><i></i></a>' +
                           '</div><div class="col-md-9">No InfraObjects Configured for this Business Function.<br>' +
                            'Please <a href="../Group/InfraObjectsConfiguration.aspx">Click Here</a> to Configure Infra Objects.' +
                           '</div></div>');

    else if (type == "BS") {
        $("#divemptyFunctionPopup").html('<div class="row well center "><div class="col-md-3"><a class="glyphicons warning_sign pull-left"><i></i></a>' +
                           '</div><div class="col-md-9">No Business Functions Configured for this Business Service.<br>' +
                            'Please <a href="../BusinessFunction/BusinessFunctionConfig.aspx">Click Here</a> to Configure Business Functions Or Click On Another Business Service .' +
                           '</div></div>');
    }
    else if (type == "IO") {
        $("#divemptyFunctionPopup").html('<div class="row well center "><div class="col-md-3"><a class="glyphicons warning_sign pull-left"><i></i></a>' +
                           '</div><div class="col-md-9">No InfraObject Components Configured for this Infra Object.<br>' +
                            'Please <a href="../Component/ServerConfiguration.aspx">Click Here</a> to Configure Server Infra Component.' +
                           '</div></div>');
    }
}

//Show current breadCrum
function ShowBreadCrum1(breadCrum) {
    $("#spnBreadCrum").text(breadCrum);
}

//Added Custom Scroll for top pane
$(".scroll-pane ").mCustomScrollbar({
    axis: "yx",
    setHeight: "280px",
    advanced: {
        updateOnContentResize: true,
        autoExpandHorizontalScroll: true
    },

});
//Added Custom Scroll for bottom pane
$(".scroll-pane-bottom").mCustomScrollbar({
    axis: "yx",
    setHeight: "180px",
    advanced: {
        updateOnContentResize: true,
        autoExpandHorizontalScroll: true
    },

});

// on resize of panes changing height of scroll dynamically
$(document).ready(function () {
    paneScrollWH();
    var paneHeight = "";
    var bottompaneHeight = "";
    function paneScrollWH() {
        paneHeight = $("div[id$=RadPane1]").height();
        bottompaneHeight = $("div[id$=RadPane6]").height();
        $(".scroll-pane").css("height", (paneHeight - 50));
        $(".scroll-pane-bottom").css("height", (bottompaneHeight - 50));
    }
    $(".rspResizeBar,.rspResizeBarHorizontal, [id*=RadPane]").click(function () {
        paneScrollWH();

    });
    $(".rspResizeBar,.rspResizeBarHorizontal,[id*=RadPane]").hover(function () {
        paneScrollWH();
    }, function () {

        paneScrollWH();
    });
});


//Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);

///================== Return Node Image as per type===================
function GetNodeImgPathByName1(name, impacttype) {

    var Str = name + "_" + impacttype;
    switch (Str) {

        case "S_PIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_yellow.png";
            break;
        case "S_MIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_orange.png";
            break;
        case "S_TIBS":
            return "../Images/ServiceLogo/bussinessservice_icon_red.png";
            break;
        case "S_NABS":
            return "../Images/ServiceLogo/bussinessservice_icon_green.png";
            break;
        case "F_PIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_yellow.png";
            break;
        case "F_MIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_orange.png";
            break;
        case "F_TIBF":
            return "../Images/ServiceLogo/bussinessfunction_icon_red.png";
            break;
        case "F_NABF":
            return "../Images/ServiceLogo/bussinessfunction_icon_green.png";
            break;
        case "I_PII":
            return "../Images/ServiceLogo/infraobject_icon_yellow.png";
            break;
        case "I_MII":
            return "../Images/ServiceLogo/infraobject_icon_orange.png";
            break;
        case "I_TII":
            return "../Images/ServiceLogo/infraobject_icon_red.png";
            break;
        case "I_NAI":
            return "../Images/ServiceLogo/infraobject_icon_green.png";
            break;
        case "ICSV_TIICSV":
            return "../Images/ServiceLogo/ServerImpacted_Icon.png";
            break;
        case "ICSV_NAICSV":
            return "../Images/ServiceLogo/Server_Icon.png";
            break;
        case "ICD_TIICD":
            return "../Images/ServiceLogo/DatabaseImpacted_Icon.png";
            break;
        case "ICD_NAICD":
            return "../Images/ServiceLogo/Database_Icon.png";
            break;
        case "ICR_TIICR":
            return "../Images/ServiceLogo/ReplicationImpacted_Icon.png";
            break;
        case "ICR_NAICR":
            return "../Images/ServiceLogo/Replication_Icon.png";
            break;
        case "IS_TIIS":
            return "../Images/ServiceLogo/Component_Red_Icon.png";
            break;
        case "IS_NAIS":
            return "../Images/ServiceLogo/Component_Icon.png";
            break

        default:
            return "../Images/ServiceLogo/Component_Icon.png";
            break;
    }
}


///=================================


//return TextAnchor for node
function returnTextAnchor1(d) {
    var textAnchor = null;
    if (d.hide == "hide") {
        textAnchor = "start";
    }
    else if (d.children && d.hide != "hide") {
        textAnchor = "end";
    }
    else {
        textAnchor = "start";
    }
    return textAnchor;
}
// Ends ======================
///================== Return Node textAnchor class as per type===================

function returnTextAnchorClass1(name, impacttype) {

    if (name != null) {
        var strName = name.substring(name.lastIndexOf('@') + 1, name.indexOf('$'))
        var splitArra = strName.split('_');

        var Str = splitArra[0] + "_" + impacttype;

        switch (Str) {

            case "S_PIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_MIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_TIBS":
                return textAnchorClass = "impact-text-black";
                break;
            case "S_NABS":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_PIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_MIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_TIBF":
                return textAnchorClass = "impact-text-black";
                break;
            case "F_NABF":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_PII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_MII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_TII":
                return textAnchorClass = "impact-text-black";
                break;
            case "I_NAI":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSV_TIICSV":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICSV_NAICSV":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICD_TIICD":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICD_NAICD":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICR_TIICR":
                return textAnchorClass = "impact-text-black";
                break;
            case "ICR_NAICR":
                return textAnchorClass = "impact-text-black";
                break;
            case "IS_TIIS":
                return textAnchorClass = "impact-text-red";
                break;
            case "IS_NAIS":
                return textAnchorClass = "impact-text-black";
                break
            default:
                return textAnchorClass = "impact-text-black";
                break;
        }
    }
}

// Gets Json for infraObjects details from node relation
function GetJsonFromNodeRelation1(d) {
    if (d.hide == "hide" && leveldata == 0) {
        leveldata = leveldata + 1;
        var requiredClickedName = null;
        if (d.logo)
            requiredClickedName = d.name + "/Hide/logo/red" + ":";
        else
            requiredClickedName = d.name + "/Hide" + ":";

        var splitNodeRelation = globalstringdata.split(";");
        if (splitNodeRelation.length > 0) {
            for (var i = 0; i < splitNodeRelation.length; i++) {
                var index = splitNodeRelation[i].indexOf(requiredClickedName);
                if (index == 0) {
                    var infraObjectRel = splitNodeRelation[i];
                    var splitInfraObjectRel = infraObjectRel.split(":");
                    var componentArray = splitInfraObjectRel[1].split(",");
                    if (componentArray.length > 0) {
                        for (var j = 0 ; j < componentArray.length; j++) {
                            var requiredComponentName = componentArray[j] + ":";
                            for (var k = i; k < splitNodeRelation.length; k++) {
                                var componentArrayIndex = splitNodeRelation[k].indexOf(requiredComponentName)
                                if (componentArrayIndex == 0) {
                                    infraObjectRel = infraObjectRel + ";" + splitNodeRelation[k];
                                    break;
                                }
                            }
                        }
                        break;
                    }
                }
            }
            var requiredNodeRelation = infraObjectRel;
            if (requiredNodeRelation != null) {
                $('#bsbody2').html("");
                renderAjaxDataForInfraObjects(requiredNodeRelation);
                isbtnReloadHide1 = true;
                d3.select("#btnReload").attr("style", "display:inline-block");
            }
        }
    }
}

function isEmpty(val) {
    return (val === undefined || val == null || val.length <= 0) ? false : true;
}
function count_Char(input, compareChar) {
    var counter = 0;

    for (var i = 0; i < input.length; i++) {
        var index_of_sub = input.indexOf(compareChar, i);

        if (index_of_sub > -1) {
            counter++;
            i = index_of_sub;
        }
    }
    return counter;
}

