﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="MoreAlertDetails.aspx.cs" Inherits="CP.UI.ImpactAnalysis.MoreAlertDetails" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">

<head id="Head1" runat="server">
    <title runat="server" id="usefortitle"></title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Analytics.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Analytics2.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/modernizr.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/ie.prototype.polyfill.js"></script>
    <script src="../Script/html5shiv.js"></script>
    <script type="text/javascript" src="../Script/jquery-ui.min.js "></script>
</head>

<body>

    <form id="form1" runat="server">
        <telerik:RadScriptManager runat="server" ID="RadScriptManager1" />
        <div class="innerLR">

            <asp:Panel ID="pnl_1_BIAAlertCountBusinessServiceWise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="upPnlBIAAlertCountBusinessServiceWise" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Business Service</label>
                                            <asp:DropDownList ID="ddlAlertBS" CssClass="col-md-8" runat="server" OnSelectedIndexChanged="ddlAlertBS_SelectedIndexChanged1" AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAAlertCountBusinessServiceWiseDetails" runat="server" OnPagePropertiesChanged="lvBIAAlertCountBusinessServiceWiseDetails_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Alert Type
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Company ID
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Company Name
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Total Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">Percentage
                                                            </th>
                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblAlerttype" CssClass="blt_12" runat="server" Text='<%# Eval("Alertype") %>' ToolTip='<%# Eval("Alertype") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label5" CssClass="" runat="server" Text='<%# Eval("Companyid") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label6" CssClass="" runat="server" Text='<%# Eval("CompanyName") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("AlertCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("TotalCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Percentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager5" runat="server" PagedControlID="lvBIAAlertCountBusinessServiceWiseDetails">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager6" runat="server" PagedControlID="lvBIAAlertCountBusinessServiceWiseDetails" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_1_More_BIAAlertCountBusinessServiceWise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label9" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAAlertCountBusinessServiceWise_More" runat="server" OnPagePropertiesChanged="lvBIAAlertCountBusinessServiceWise_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">B.S. Name
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Total Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">Percentage
                                                            </th>
                                                        </tr>
                                                    </thead>

                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblAlerttype" CssClass="blt_12" runat="server" Text='<%# Eval("BusinessService") %>' ToolTip='<%# Eval("BusinessService") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("AlertCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("TotalCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Percentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager11" runat="server" PagedControlID="lvBIAAlertCountBusinessServiceWise_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager12" runat="server" PagedControlID="lvBIAAlertCountBusinessServiceWise_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_2_BIAAlertCountBusinessFunctionWise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label4" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="upPnlBIAAlertCountBusinessFunctionWise" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Business Function</label>
                                            <asp:DropDownList ID="ddlAlertBF" CssClass="col-md-8" runat="server" AutoPostBack="true">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAAlertCountBusinessFunctionWiseDetails" runat="server"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Alert Type
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Company Name
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Total Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">Percentage
                                                            </th>
                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblAlerttype" CssClass="blt_12" runat="server" Text='<%# Eval("Alertype") %>' ToolTip='<%# Eval("Alertype") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label6" CssClass="" runat="server" Text='<%# Eval("CompanyName") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("AlertCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("TotalCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Percentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager9" runat="server" PagedControlID="lvBIAAlertCountBusinessFunctionWiseDetails">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager10" runat="server" PagedControlID="lvBIAAlertCountBusinessFunctionWiseDetails" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_2_More_BIAAlertCountBusinessFunctionWise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label1" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAAlertCountBusinessFunctionWise_More" runat="server" OnPagePropertiesChanged="lvBIAAlertCountBusinessFunctionWise_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Business Function
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Alerts Count
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Total Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">% Percentage
                                                            </th>
                                                        </tr>
                                                    </thead>

                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblAlerttype" CssClass="blt_12" runat="server" Text='<%# Eval("BusinessFunction") %>' ToolTip='<%# Eval("BusinessService") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("AlertCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("TotalCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Percentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvBIAAlertCountBusinessFunctionWise_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvBIAAlertCountBusinessFunctionWise_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_3_BIAAlertCountComponentWise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label10" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="upBIAAlertCountComponentWise" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Component</label>
                                            <asp:DropDownList ID="ddlComponentWise" CssClass="col-md-8" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlComponentWise_SelectedIndexChanged">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAAlertCountComponentWise" runat="server" DataKeyNames="Id" OnPagePropertiesChanged="lvBIAAlertCountComponentWise_PagePropertiesChanged">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Alert Type
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Total Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">Percentage
                                                            </th>
                                                        </tr>
                                                    </thead>



                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>

                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblAlerttype" CssClass="blt_12" runat="server" Text='<%# Eval("Alertype") %>' ToolTip='<%# Eval("Alertype") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("AlertCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("TotalCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Percentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager13" runat="server" PagedControlID="lvBIAAlertCountComponentWise">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager14" runat="server" PagedControlID="lvBIAAlertCountComponentWise" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_3_More_BIAAlertCountComponentWise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label2" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAAlertCountComponentWise_More" runat="server" OnPagePropertiesChanged="lvBIAAlertCountComponentWise_More_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Alert Type
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Alerts Count
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Total Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">% Percentage
                                                            </th>
                                                        </tr>
                                                    </thead>

                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblAlerttype" CssClass="blt_12" runat="server" Text='<%# Eval("Alertype") %>' ToolTip='<%# Eval("Alertype") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("AlertCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("TotalCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Percentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager3" runat="server" PagedControlID="lvBIAAlertCountComponentWise_More">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager4" runat="server" PagedControlID="lvBIAAlertCountComponentWise_More" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_4_BIAAlertCountCategoryWise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label22" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="upd_BIAAlertCountCategoryWise" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4">Select Alert Category</label>
                                            <asp:DropDownList ID="ddlAlertCategory" CssClass="col-md-8" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlAlertCategory_SelectedIndexChanged">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAAlertCountCategoryWise" runat="server" DataKeyNames="Id" OnPagePropertiesChanged="lvBIAAlertCountCategoryWise_PagePropertiesChanged">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Alert Type
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 15%;">Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Total Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 12%;">Percentage
                                                            </th>

                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblCategory" CssClass="blt_12" runat="server" Text='<%# Eval("Alertype") %>' ToolTip='<%# Eval("Alertype") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 15%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("Alertcountt") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("Total") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 12%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("TotalPercentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager21" runat="server" PagedControlID="lvBIAAlertCountCategoryWise">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager22" runat="server" PagedControlID="lvBIAAlertCountCategoryWise" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

            <asp:Panel ID="pnl_4_More_BIAAlertCountCategoryWise" runat="server">

                <div class="widget widget-heading-simple widget-body-white  margin-bottom-none" style="margin-top: 5px;">

                    <div class="widget-body">

                        <asp:Label ID="Label3" runat="server" Visible="False"></asp:Label>
                        <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">

                                        <asp:ListView ID="lvBIAAlertCountCategoryWiseMore" runat="server" OnPagePropertiesChanged="lvBIAAlertCountCategoryWiseMore_PagePropertiesChanged"
                                            DataKeyNames="Id">
                                            <LayoutTemplate>
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th class="comTbl text-center" style="width: 8%;">Sr. No.
                                                            </th>
                                                            <th class="comTbl" style="width: 27%;">Alert Category
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Alerts Count
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 20%;">Total Alerts
                                                            </th>
                                                            <th class="comTbl text-center" style="width: 25%;">% Percentage
                                                            </th>
                                                        </tr>
                                                    </thead>

                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td class="text-center" style="width: 8%;">
                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                        <%#Container.DataItemIndex+1 %>
                                                    </td>
                                                    <td style="width: 27%;">
                                                        <div class="tdwordwrap">
                                                            <asp:Label ID="lblAlerttype" CssClass="blt_12" runat="server" Text='<%# Eval("Category") %>' ToolTip='<%# Eval("Category") %>' />
                                                        </div>
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label8" CssClass="" runat="server" Text='<%# Eval("AlertCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="Label7" CssClass="" runat="server" Text='<%# Eval("TotalCount") %>' />
                                                    </td>
                                                    <td class="text-center" style="width: 10%;">
                                                        <asp:Label ID="lblActioncount2" runat="server" Text='<%# Eval("Percentage") %>' />
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                            <EmptyDataTemplate>
                                                <div class="message warning align-center bold no-bottom-margin">
                                                    <asp:Label ID="lblError" Text="No Record Found" CssClass="error" runat="server" Visible="true"></asp:Label>
                                                </div>
                                            </EmptyDataTemplate>
                                        </asp:ListView>
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <asp:DataPager ID="dataPager7" runat="server" PagedControlID="lvBIAAlertCountCategoryWiseMore">
                                                    <Fields>
                                                        <asp:TemplatePagerField>
                                                            <PagerTemplate>
                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                <br />
                                                            </PagerTemplate>
                                                        </asp:TemplatePagerField>
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <asp:DataPager ID="dataPager8" runat="server" PagedControlID="lvBIAAlertCountCategoryWiseMore" PageSize="10">
                                                    <Fields>
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                            NumericButtonCssClass="btn-pagination" />
                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                    </Fields>
                                                </asp:DataPager>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>

                </div>
            </asp:Panel>

        </div>
    </form>

</body>
</html>
