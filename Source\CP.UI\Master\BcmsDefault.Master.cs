﻿using System.Web;
using System.Web.Security;
using CP.Common.DatabaseEntity;
using CP.BusinessFacade;
using System;
using System.Configuration;

namespace CP.UI.Master
{
    public partial class BcmsDefault : BaseMasterPage
    {
        protected void Page_Load(object sender, System.EventArgs e)
        {
            IFacade facade = new Facade();

            
            
              if (LoggedInUser != null)
            {
                if (ConfigurationManager.AppSettings["IsMultiuser"].Equals("false"))
                {
                    User user = facade.GetUserById(LoggedInUser.Id);
                    if (user != null)
                    {
                        if (!user.SessionId.Equals(Session.SessionID))
                        {

                            facade.LogoutUser();
                            Session.Clear();
                            Session.Abandon();
                            Session.RemoveAll();

                            if (Request.Cookies["ASP.NET_SessionId"] != null)
                            {
                                Response.Cookies["ASP.NET_SessionId"].Value = string.Empty;
                                Response.Cookies["ASP.NET_SessionId"].Expires = System.DateTime.Now.AddMonths(-20);
                            }

                            if (Request.Cookies["AuthToken"] != null)
                            {
                                Response.Cookies["AuthToken"].Value = string.Empty;
                                Response.Cookies["AuthToken"].Expires = DateTime.Now.AddMonths(-20);
                            }

                            if (Response.Cookies[".ASPXAUTH"] != null)
                            {
                                Response.Cookies[".ASPXAUTH"].Value = string.Empty;
                                Response.Cookies[".ASPXAUTH"].Expires = DateTime.Now.AddMonths(-20);
                            }

                            Response.Redirect("~/Login.aspx");
                        }

                    }
                }

                HttpContext.Current.Response.Cookies[".ASPXAUTH"].Value = HttpContext.Current.Request.Cookies[".ASPXAUTH"].Value;
                HttpContext.Current.Response.Cookies[".ASPXAUTH"].Domain = ConfigurationManager.AppSettings["SessionCookiesDomain"].ToString();
                HttpContext.Current.Response.Cookies[".ASPXAUTH"].Path = ConfigurationManager.AppSettings["SessionCookiesPath"].ToString();

                HttpContext.Current.Response.Cookies["AuthToken"].Value = HttpContext.Current.Request.Cookies["AuthToken"].Value;
                HttpContext.Current.Response.Cookies["AuthToken"].Domain = ConfigurationManager.AppSettings["SessionCookiesDomain"].ToString();
                HttpContext.Current.Response.Cookies["AuthToken"].Path = ConfigurationManager.AppSettings["SessionCookiesPath"].ToString();


                HttpContext.Current.Response.Cookies["ASP.NET_SessionId"].Value = HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value;
                HttpContext.Current.Response.Cookies["ASP.NET_SessionId"].Domain = ConfigurationManager.AppSettings["SessionCookiesDomain"].ToString();
                HttpContext.Current.Response.Cookies["ASP.NET_SessionId"].Path = ConfigurationManager.AppSettings["SessionCookiesPath"].ToString();
              
            }
        }
        public override void PrepareView()
        {
            if (LoggedInUserName == "Anonymous")
            {

                HttpContext.Current.Response.Cache.SetCacheability(HttpCacheability.NoCache);
                HttpContext.Current.Response.Cache.SetNoServerCaching();
                HttpContext.Current.Response.Cache.SetNoStore();
                Session.Abandon();
                Response.Redirect(CP.Common.Shared.Constants.UrlConstants.Urls.Common.LoginPage);
            }
        }


        private const string AntiXsrfTokenKey = "__AntiXsrfToken";
        private const string AntiXsrfUserNameKey = "__AntiXsrfUserName";
        private string _antiXsrfTokenValue;

        protected void Page_Init(object sender, EventArgs e)
        {
            //First, check for the existence of the Anti-XSS cookie
            var requestCookie = Request.Cookies[AntiXsrfTokenKey];
            Guid requestCookieGuidValue;

            //If the CSRF cookie is found, parse the token from the cookie.
            //Then, set the global page variable and view state user
            //key. The global variable will be used to validate that it matches in the view state form field in the Page.PreLoad
            //method.
            if (requestCookie != null
            && Guid.TryParse(requestCookie.Value, out requestCookieGuidValue))
            {
                //Set the global token variable so the cookie value can be
                //validated against the value in the view state form field in
                //the Page.PreLoad method.
                _antiXsrfTokenValue = requestCookie.Value;

                //Set the view state user key, which will be validated by the
                //framework during each request
                Page.ViewStateUserKey = _antiXsrfTokenValue;
            }
            //If the CSRF cookie is not found, then this is a new session.
            else
            {
                //Generate a new Anti-XSRF token
                _antiXsrfTokenValue = Guid.NewGuid().ToString("N");

                //Set the view state user key, which will be validated by the
                //framework during each request
                Page.ViewStateUserKey = _antiXsrfTokenValue;

                //Create the non-persistent CSRF cookie
                var responseCookie = new HttpCookie(AntiXsrfTokenKey)
                {
                    //Set the HttpOnly property to prevent the cookie from
                    //being accessed by client side script
                    HttpOnly = true,

                    //Add the Anti-XSRF token to the cookie value
                    Value = _antiXsrfTokenValue
                };

                //If we are using SSL, the cookie should be set to secure to
                //prevent it from being sent over HTTP connections
                if (FormsAuthentication.RequireSSL &&
                Request.IsSecureConnection)
                    responseCookie.Secure = true;

                //Add the CSRF cookie to the response
                Response.Cookies.Set(responseCookie);
            }

            Page.PreLoad += master_Page_PreLoad;
        }

        protected void master_Page_PreLoad(object sender, EventArgs e)
        {
            //During the initial page load, add the Anti-XSRF token and user
            //name to the ViewState
            if (!IsPostBack)
            {
                //Set Anti-XSRF token
                ViewState[AntiXsrfTokenKey] = Page.ViewStateUserKey;

                //If a user name is assigned, set the user name
                ViewState[AntiXsrfUserNameKey] =
                Context.User.Identity.Name ?? String.Empty;
            }
            //During all subsequent post backs to the page, the token value from
            //the cookie should be validated against the token in the view state
            //form field. Additionally user name should be compared to the
            //authenticated users name
            else
            {
                //Validate the Anti-XSRF token
                if ((string)ViewState[AntiXsrfTokenKey] != _antiXsrfTokenValue
                || (string)ViewState[AntiXsrfUserNameKey] !=
                (Context.User.Identity.Name ?? String.Empty))
                {
                    throw new InvalidOperationException("Validation of Anti-XSRF token failed.");
                }
            }
        }


    }
}