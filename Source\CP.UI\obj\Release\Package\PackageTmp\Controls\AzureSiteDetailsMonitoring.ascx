﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="AzureSiteDetailsMonitoring.ascx.cs" Inherits="CP.UI.Controls.AzureSiteDetailsMonitoring" %>
<div id="divddl" runat="server" visible="false">
    <span class="heading">
        <asp:Label ID="lblreplicvationmode" runat="server" Text="" Visible="false"></asp:Label>
    </span>
    <%-- <asp:DropDownList ID="ddlreplicationEMCSRDFSG" runat="server" CssClass="selectpicker pull-right" AutoPostBack="true" OnSelectedIndexChanged="ddlreplicationEMCSRDFSG_SelectedIndexChanged">
            
        </asp:DropDownList>--%>
</div>
<div class="widget">
    <div class="widget-head">
        <asp:Label ID="lbl_VmName" runat="server" Text="VM Monitoring Summary" Style="margin-left:10px;"></asp:Label>
    </div>
    <div class="widget-body innerAll inner-2x">
        <table id="tblVM_Monitoring" class="table table-striped table-condensed table-bordered table-responsive" width="100%" runat="server">
            <thead>
                <tr>
                    <th class="col-md-4"></th>
                    <th class="col-md-4">Protected Site
                    </th>
                    <th class="col-md-4">Recovery Site
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Virtual Machine Name
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="icon-computer"></asp:Label>
                        <asp:Label ID="lblPRVMNm" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="icon-drcomputer"></asp:Label>
                        <asp:Label ID="lblDRVMNm" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>Location Name
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="site-icon"></asp:Label>
                        <asp:Label ID="lblPRLocationNm" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="site-icon"></asp:Label>
                        <asp:Label ID="lblDRLocationNm" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>Recovery Services Vault Name
                    </td>
                    <td>
                        <asp:Label ID="iconlblprfastmanaged" runat="server" CssClass="icon-logrecovery"></asp:Label>
                        <asp:Label ID="lblPRVaultNm" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label ID="iconlbldrfastmanaged" runat="server" CssClass="icon-logrecovery"></asp:Label>
                        <asp:Label ID="lblDRVaultNm" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>Replication Provider
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                        <asp:Label ID="lblPRProvider" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                        <asp:Label ID="lblDRProvider" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>Allowed Operations
                    </td>
                    <td>
                        <asp:Label ID="iconlblprcompressionenabled" runat="server" CssClass="Active"></asp:Label>
                        <asp:Label ID="lblPRAllowedOperation" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label ID="iconlbldrcompressionenabled" runat="server" CssClass="Active"></asp:Label>
                        <asp:Label ID="lblDRAllowedOperation" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>Active Location State
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="Active"></asp:Label>
                        <asp:Label ID="lblPRActiveLoc" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="Active"></asp:Label>
                        <asp:Label ID="lblDRActiveLoc" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>Public IP Address
                    </td>
                    <td>
                        <asp:Label id="healthPR141" runat="server" ></asp:Label>
                        <asp:Label ID="PRIPAddress" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label id="healthDR142" runat="server" ></asp:Label>
                        <asp:Label ID="DRIPAddress" runat="server"></asp:Label>
                    </td>
                </tr>

            </tbody>
        </table>
    </div>
</div>

<div class="widget">
    <div class="widget-head">
        <asp:Label ID="lbl_RepliName" runat="server" Text="Replication Monitoring" Style="margin-left:10px;"></asp:Label>
    </div>
    <div class="widget-body innerAll inner-2x">
        <table id="tblAzureReplication" class="table table-striped table-condensed table-bordered table-responsive" width="100%" runat="server">
            <thead>
                <tr>
                    <th class="col-md-4"></th>
                    <th class="col-md-4">Protected Site
                    </th>
                    <th class="col-md-4">Recovery Site
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Replication Health
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="health-up"></asp:Label>
                        <asp:Label ID="lblPR_RepliHealth" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="health-up"></asp:Label>
                        <asp:Label ID="lblDR_RepliHealth" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>Status
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="Active"></asp:Label>
                        <asp:Label ID="lblPRStat" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="Active"></asp:Label>
                        <asp:Label ID="lblDRStat" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>RPO
                    </td>
                    <td>
                        <asp:Label ID="Label5" runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblPR_RPO" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label ID="Label7" runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblDR_RPO" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="3"><strong>Latest Recovery Points</strong>
                    </td>
                </tr>
                <tr>
                    <td>Crash-Consistent
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblPRCrash" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblDRCrash" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>App-Consistent
                    </td>
                    <td>
                        <asp:Label ID="Label11" runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblPRApp" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label ID="Label13" runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblDRApp" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td>Last Successful Failover Time
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblPR_FailoverTime" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblDR_FailoverTime" runat="server"></asp:Label>
                    </td>
                </tr>

                <tr>
                    <td>Last Successful Test Failover Time
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblPR_TestTime" runat="server"></asp:Label>
                    </td>
                    <td>
                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                        <asp:Label ID="lblDR_TestTime" runat="server"></asp:Label>
                    </td>
                </tr>

            </tbody>
        </table>
    </div>
</div>
<%--</div>
</div>--%>
