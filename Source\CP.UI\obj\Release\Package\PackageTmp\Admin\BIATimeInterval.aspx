﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="BIATimeInterval.aspx.cs" Inherits="CP.UI.Admin.BIATimeInterval" %>



<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<!DOCTYPE html >
<html>
<head id="Head1" runat="server">
    <title>Continuity Patrol :: Time Interval</title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.simple-dtpicker.css" type="text/css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.timepicker.css" type="text/css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />

    <style type="text/css">
        .control-label {
            font-size: 12px !important;
        }

        .form-control, .btn-group.bootstrap-select.col-xs-12 button {
            padding: 4px;           
        }
        table#dlTimeIterval td {
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">

        <asp:ScriptManager ID="ScriptManager1" runat="server" ScriptMode="Release">
        </asp:ScriptManager>
        <script type="text/javascript">
            var xPos, yPos;
            Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
            Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
            function BeginRequestHandler(sender, args) {
                if ($("#SwitchScrollbar").length > 0) {
                    xPos = window.$get('SwitchScrollbar').scrollLeft;
                    yPos = window.$get('SwitchScrollbar').scrollTop;
                }
            }
            function EndRequestHandler(sender, args) {
                if ($("#SwitchScrollbar").length > 0) {
                    window.$get('SwitchScrollbar').scrollLeft = xPos;
                    window.$get('SwitchScrollbar').scrollTop = yPos;
                }
            }
           
        </script>

        <div class="innerLR innerT" style="background-color: #fff;">
            <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
                <ContentTemplate>

                    <div class="col-xs-12 form-horizontal uniformjs">
                        <div class="form-group">
                            <label class="control-label pull-left">Time Interval</label>
                            <div class="col-xs-2">
                                <asp:TextBox ID="txtStartInterval" runat="server" Width="75%"
                                    autocomplete="off" TabIndex="1" CssClass="form-control"  />
                               <%-- <asp:RequiredFieldValidator ID="rfStartInterval" runat="server" ControlToValidate="txtStartInterval" ValidationGroup="t"
                                    ErrorMessage="Enter Start Interval." Display="dynamic" CssClass="error"></asp:RequiredFieldValidator>--%>
                                <asp:RegularExpressionValidator ID="rfStartInterval" runat="server" ErrorMessage="Enter number only" CssClass="error" 
                                        ControlToValidate="txtStartInterval" ValidationGroup ="t" ValidationExpression="^([0-9]*,?)*$">
                                </asp:RegularExpressionValidator>
                                <asp:RequiredFieldValidator runat="server" id="ReqStartInterval" controltovalidate="txtStartInterval" errormessage="Please enter Start Interval" ValidationGroup ="t" CssClass="error" />

                                <%--<asp:RegularExpressionValidator ID="RegularExpressionValidator1"
                                    ControlToValidate="txtStartInterval"
                                    ValidationExpression="\d+"
                                    Display="dynamic"
                                    ValidationGroup="t"
                                    EnableClientScript="true"
                                    ErrorMessage="Please enter numbers only"
                                    runat="server" 
                                    CssClass="error"/>--%>
                            </div>
                            <div class="col-xs-2 padding-none-LR">
                                <asp:DropDownList ID="ddltmeinterval" runat="server" TabIndex="2"
                                    CssClass="selectpicker col-xs-12" data-style="btn-default">

                                    <asp:ListItem Value="1">Hours</asp:ListItem>
                                    <asp:ListItem Value="2">Days</asp:ListItem>

                                </asp:DropDownList>

                            </div>
                            <label class="control-label pull-left">To</label>
                            <div class="col-xs-2">
                                <asp:TextBox ID="txtEndInterval" runat="server" AutoPostBack="false" Width="75%" TabIndex="3"
                                    autocomplete="off" CssClass="form-control"/>
                                <%--<asp:RequiredFieldValidator ID="rfEndInterval" runat="server" ControlToValidate="txtEndInterval" ValidationGroup="t"
                                    ErrorMessage="Enter End Interval." Display="dynamic" CssClass="error"></asp:RequiredFieldValidator>--%>
                                <asp:RegularExpressionValidator ID="rfEndInterval" runat="server" ErrorMessage="Enter number only" CssClass="error" 
                                        ControlToValidate="txtEndInterval" ValidationGroup ="t" ValidationExpression="^([0-9]*,?)*$">
                                </asp:RegularExpressionValidator>
                                <asp:RequiredFieldValidator runat="server" id="reqEndInterval" controltovalidate="txtEndInterval" errormessage="Please enter End Interval." ValidationGroup ="t" CssClass="error" />
                            </div>
                            <div class="col-xs-2 padding-none-LR">
                                <asp:DropDownList ID="ddltmeinterval2" runat="server" TabIndex="4" OnSelectedIndexChanged="ddltmeinterval2_SelectedIndexChanged" AutoPostBack="True"
                                    CssClass="selectpicker col-xs-12" data-style="btn-default">

                                    <asp:ListItem Value="1">Hours</asp:ListItem>
                                    <asp:ListItem Value="2">Days</asp:ListItem>

                                </asp:DropDownList>


                                <asp:HiddenField ID="hdnt" runat="server" Value="" />
                            </div>
                           
                            <div class="pull-left">
                                <asp:Button ID="btnSave" CssClass="btn btn-primary" runat="server" Text="Save" TabIndex="5" OnClick="BtnSaveClick" ValidationGroup="t" />
                                <asp:Button ID="btnCancel" CssClass="btn btn-default" runat="server" Text="Cancel" TabIndex="6" CausesValidation="False" EnableViewState="False" OnClick="BtnCancelClick" />
                            </div>

                        </div>
                        <div class="row">
                             <asp:Label ID="lblMessage" runat="server" ForeColor="Green"></asp:Label>
                           <%-- <asp:CustomValidator ID="CustomValidator1" runat="server" ValidationGroup="t"
                                OnServerValidate="TimeInterval_Validate" CssClass="col-xs-12 label label-danger"
                                ControlToValidate="txtEndInterval" Display="Dynamic"
                                ErrorMessage="Time interval already Available.">
                            </asp:CustomValidator>--%>
                        </div>
                        <div class="row">
                            <asp:UpdatePanel ID="upDatalist" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <asp:DataList ID="dlTimeIterval" runat="server" OnEditCommand="dlTimeIterval_EditCommand" OnDeleteCommand="dlTimeIterval_DeleteCommand" GridLines="None" RepeatDirection="Vertical" RepeatColumns="4" CssClass="table" Width="100%">

                                <HeaderStyle BackColor="#4a8bc2" ForeColor="#ffffff" HorizontalAlign="Left" VerticalAlign="Middle" />
                                <HeaderTemplate>
                                    Time Interval
                                </HeaderTemplate>
                                <ItemTemplate>
                                            <%--<div style="display: inline-block; width: 78%;">--%>
                                            <asp:Label ID="lbltimeintervalID" runat="server" Text='<%# Eval("TId") %>' Visible="false"></asp:Label>
                                            <asp:Label ID="lblTime" runat="server" Text='<%# Eval("TimeIntervalText").ToString().Trim() %>'></asp:Label>
                                            <%--</div>--%>
                                            <%-- <div style="display: inline-block; width: 18%;vertical-align:middle">--%>
                                            <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit" Enabled='<%# CheckAttach(Eval("TId")) %>'
                                                ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                            <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete" Enabled='<%# CheckAttach(Eval("TId")) %>'
                                                ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png"/>
                                            <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ImgDelete"
                                                ConfirmText='<%# "Please confirm if you want to Delete " + Eval("TimeIntervalText") +" " + "Interval?" %>'>
                                            </TK1:ConfirmButtonExtender>
                                            <%--</div>--%>
                                </ItemTemplate>
                            </asp:DataList>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>
                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>

    </form>
    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script src="../Script/jquery.simple-dtpicker.js" type="text/javascript"></script>
    <script src="../Script/jquery.timepicker.min.js" type="text/javascript"></script>
    <script src="../Script/BusinessFunctionRPOSpan.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();

            $(".infraScheduleScroll").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
                autoDraggerLength: false,
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });

        });
        function pageLoad() {
            $('[id$=txtDateTime]').appendDtpicker();
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();

            $(".infraScheduleScroll").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
                autoDraggerLength: false,
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });
        }
    </script>


</body>
</html>

