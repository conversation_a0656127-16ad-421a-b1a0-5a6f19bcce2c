﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseMssqlMonitor", Namespace = "http://www.BCMS.com/types")]

    public class DatabaseMssqlMonitor : BaseEntity
    {
        #region Properties        

        [DataMember]
        public string PRIpAddress { get; set; }

        [DataMember]
        public string DRIpAddress { get; set; }

        [DataMember]
        public string PRHostName { get; set; }

        [DataMember]
        public string DRHostName { get; set; }

        [DataMember]
        public string PRAuthType { get; set; }

        [DataMember]
        public string DRAuthType { get; set; }

        [DataMember]
        public string PRInstanceName { get; set; }

        [DataMember]
        public string DRInstanceName { get; set; }

        [DataMember]
        public string PRInstancePort { get; set; }

        [DataMember]
        public string DRInstancePort { get; set; }

        [DataMember]
        public string PRIsClusterd { get; set; }

        [DataMember]
        public string DRIsClusterd { get; set; }

        [DataMember]
        public string PRReplicationDB { get; set; }

        [DataMember]
        public string DRReplicationDB { get; set; }

        [DataMember]
        public string PRDBNameWithStatus { get; set; }

        [DataMember]
        public string DRDBNameWithStatus { get; set; }

        [DataMember]
        public string PRDBNameWithPath { get; set; }

        [DataMember]
        public string DRDBNameWithPath { get; set; }

        [DataMember]
        public string PRClusterResStatus { get; set; }

        [DataMember]
        public string DRClusterResStatus { get; set; }

        [DataMember]
        public string PRWinServRunningStatus { get; set; }

        [DataMember]
        public string DRWinServRunningStatus { get; set; }

        [DataMember]
        public string PRExcludeDatabaseList { get; set; }

        [DataMember]
        public string DRExcludeDatabaseList { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]

        public DateTime CreateDate { get; set; }

        #endregion Properties
    }
}

