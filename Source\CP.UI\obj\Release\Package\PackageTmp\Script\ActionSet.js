﻿$("[id$=ddlActionSetList]").change(function() {
        var value = $("[id$=ddlActionSetList]").val();
        hideDivError();
        DisableActionSetButton();
        $("[id$=ddlLoadProperty]").val("00").attr('selected', true);
        if (value != "00" && value != "000") {
            EnableImage();
        	DisableActionSetElement();
            $.ajax({
                type: "POST",
                url: "WorkflowConfiguration.aspx/MainFunction",
                data: "{'args':'GetActionSetProperty','parameter':'" + value + "'}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(msg) {
                    PopulateActionSet(msg.d);
                    GetValuePropertyActionSet();
                }
            });
            $("#divActionSet").show(500);
            hidePropertyObj();
        } else {
            DisableImage();
            for (var i = 0; i < ddlActivitySetObj.length; i++) {
                if (i < 2) {
                    $("#" + ddlActivitySetObj[i] + "").val("");
                }
                if (i == 2) {
                    //  $("#" +ddlActivitySetObj[i]+"").val( "00" ).attr('selected',true);
                }
                if (i == 3) {
                    $("#" + ddlActivitySetObj[i] + "").find('option').remove();
                }
            }
            if (value == "000") {
                $("#divActionSet").show(500);
				EnableActionSetElement();
				EnableActionSetButton();
                hidePropertyObj();
				GetValuePropertyActionSet();
            } else {
                $("#divActionSet").hide(500);
            }
        }
    });


    function CallPropertySetFunction(result) {
        $.ajax({
            type: "POST",
            url: "WorkflowConfiguration.aspx/MainFunction",
            data: "{'args':'GetActionSetProperty','parameter':'" + result + "'}",
            contentType: "application/json; charset=utf-8",

            dataType: "json",

            success: function(msg) {
                PopulateActionSet(msg.d);
            }
        });
    	hidePropertyObj();
        $("#divActionSet").show(1000);
        hideDivError();
    }

    function PopulateActionSet(result) {
        var resultArray = result.split(",");
        for (var i = 0; i < resultArray.length; i++) {
            if (i == 0) {
                var actionSetproperty = resultArray[i].split(":");
                $("#txtActionSetName").val(actionSetproperty[0]);
                $("#hdActivitySetName").val(actionSetproperty[0]);
                var txtName = actionSetproperty[0];
                $("#txtActionSetDescription").val(actionSetproperty[1]);
            }
            if (i == 1) {
                $('#ddlActionSetProperty').find('option').remove();
                var cell = document.getElementById("ddlActionSetProperty");
                var listValue = resultArray[i].split(":");
            	var listText = resultArray[i + 1].split(":");
                for (var j = 0; j < listValue.length; j++) {
                    var text = listText[j];
                    var value = listValue[j];;
                    $("#ddlActionSetProperty").append("<option value='" + value + "'>" + text + "</option>");
                }
            }
        }
    }

	$("#btnActionSetSave").click(function() {
       var validateValue= validateActionset();
        if($("#errortxtActionSetName").is(":visible"))
        {
            return false;
        }
            var flag = validateValue.indexOf(':');
            if (flag > 0)
            {
                PropertyError (validateValue,"divActionSetError");
                return false;
            }
            else
            {
                  $("#divActionSetError").html("");
                  clearValidation();
                  DisableActionSetElement();
                  EnableImage();
            }

        var actionSetName = $("#txtActionSetName").val();

        var actionSetDescription = $("#txtActionSetDescription").val();
        var idValues = '';
        var operation = $("[id$=ddlActionSetList] option:selected").val();
        if (operation == "000") {
            operation = "Insert";
        } else {
            operation = "Update";
        }
        $("[id$=ddlActionSetProperty] option").each(function() {
            idValues = idValues + $(this).val() + ":";
        });
        idValues = operation + ":" + actionSetName + ":" + actionSetDescription + ":" + idValues.substring(0, idValues.length - 1);

		$.ajax({
            type: "POST",
            url: "WorkflowConfiguration.aspx/MainFunction",
            data: "{'args':'SaveActionset','parameter':'" + idValues + "'}",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function(msg) {
                var textValue = $("[id$=ddlActionSetList] option:selected").val();
                DisableActionSetButton();
            	ChangeWorkflowPropertyName(actionSetName);
                //ChangeName(actionSetName);
                if (textValue != "000") {
                    $("[id$=ddlActionSetList] option:selected").text(actionSetName);
                } else {
                    $("[id$=ddlActionSetList] option:selected").remove();
                    $("[id$=ddlActionSetList]").append("<option value='" + msg.d + "' selected='selected'>" + actionSetName + "</option>");
                    $("[id$=ddlActionSetList]").append("<option value='000'>Insert Action Set Property</option>");
                }
            }
        });
    });

	 $("#btnremoveAction").click(function() {
	        $('#ddlActionSetProperty option:selected').remove()
	    });

    $("#btnAddAction").click(function() {
        var text = $("#ddlActionProperty option:selected").text();
        var value = $("#ddlActionProperty option:selected").val();
        if (value !== "00") {
            $("#ddlActionSetProperty").append("<option value='" + value + "'>" + text + "</option>");
        }
    });
    $("#btnUp").click(function() {
        var selectedOption = $('#ddlActionSetProperty  option:selected');
        var prevOption = $('#ddlActionSetProperty  option:selected').prev("option");
        if ($(prevOption).text() != "") {
            $(selectedOption).remove();
            $(prevOption).before($(selectedOption));
        }
    });

    $("#btnDown").click(function() {
        var selectedOption = $('#ddlActionSetProperty  option:selected');
        var prevOption = $('#ddlActionSetProperty  option:selected').next("option");
        if ($(prevOption).text() != "") {
            $(selectedOption).remove();
            $(prevOption).after($(selectedOption));
        }
    });

	function DisableActionSetElement()
{
    $("#txtActionSetName").attr("disabled", true);
    $("#txtActionSetDescription").attr("disabled", true);
    $('#ddlActionSetProperty').attr("disabled", true);
    $('#ddlActionProperty').attr("disabled", true);
}
	function EnableActionSetElement()
	{
	    $("#txtActionSetName").attr("disabled", false);
	    $("#txtActionSetDescription").attr("disabled", false);
	    $('#ddlActionSetProperty').attr("disabled", false);
	    $('#ddlActionProperty').attr("disabled", false);
	}

	$("#btnCancelEditActionSet").click(function(){
	 $('#ddlActionSetList').trigger('change');
	  EnableImage();
	  DisableActionSetElement();
	  DisableActionSetButton();
	  hideDivError();
	});

	$('#btnEditActionSet').click(function(){
		oldName =  $("#txtActionSetName").val();
	    EnableActionSetElement();
	    EnableImage();
	    EnableActionSetButton();
	    DisableImage();
	});

	function EnableActionSetButton()
	{
	    $("#btnActionSetSave").show();
	    $("#btnEditActionSet").hide();
	    $("#btnCancelEditActionSet").show();
	}
	function DisableActionSetButton()
	{
	    $("#btnActionSetSave").hide();
	    $("#btnEditActionSet").show();
	    $("#btnCancelEditActionSet").hide();
	}

	function GetValuePropertyActionSet()
	{
	    var  val ="";
	    var text="";
	 $("[id$=ddlActionProperty] option").remove();
		 $("[id$=ddlLoadProperty] option").each(function(){
		    val =$(this).val();
		    text=$(this).text();
		    $("[id$=ddlActionProperty]").append("<option value='"+ val +"'>"+text+"</option>");
		 });
	    $("[id$=ddlActionProperty] option:last").remove();
	}