﻿$(document).ready(function () {

});

function cleartext(control) {
    if (control.value != "") {
        var strOriginal = getOrignalData(control, $('#ctl00_cphBody_hdfStaticGuid').val());
        control.value = strOriginal;
    }
}

function encode(input) {
    var base64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef" +
   "ghijklmnopqrstuvwxyz0123456789+/=";

    var output = "";
    var ch1, ch2, ch3, enc1, enc2, enc3, enc4;
    var i = 0;

    do {
        ch1 = input.charCodeAt(i++);
        ch2 = input.charCodeAt(i++);
        ch3 = input.charCodeAt(i++);

        enc1 = ch1 >> 2;
        enc2 = ((ch1 & 3) << 4) | (ch2 >> 4);
        enc3 = ((ch2 & 15) << 2) | (ch3 >> 6);
        enc4 = ch3 & 63;

        if (isNaN(ch2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(ch3)) {
            enc4 = 64;
        }

        output = output + base64.charAt(enc1) + base64.charAt(enc2) +
          base64.charAt(enc3) + base64.charAt(enc4);
        ch1 = ch2 = ch3 = "";
        enc1 = enc2 = enc3 = enc4 = "";
    } while (i < input.length);

    return output;
}
function validateLength(sender, args) {
    args.Value = encode(args.Value);
    $('[id$=ctl00_cphBody_txtConPwd]').val(encode($('[id$=ctl00_cphBody_txtConPwd]').val()));
    if (args.Value == ($('[id$=ctl00_cphBody_txtNewPwd]').val())) {

        return args.IsValid = true;
    }
    else {
        $('[id$=ctl00_cphBody_txtConPwd]').val = "";
        return args.IsValid = false;
    }
}
function genratecode(vall) {
    if (vall.value.length > 0) {
        vall.value = Swaping(encode(vall.value));
    }
}


function Swaping(UserCode) {
    var len = (UserCode.length) / 3;
    var code1 = "";
    var i = 0;
    for (i = 0; i < UserCode.length ; i++) {
        code1 = code1 + UserCode.charAt(i + 1) + UserCode.charAt(i)
        i++
    }
    return code1;
}
function alphanumeric(obj) {
    var nu = 0;
    var alf = 0;
    var alphane = $('[id$=ctl00_cphBody_txtNewPwd]').val();

    var numaric = alphane;
    if (numaric.length > 7) {
        for (var j = 0; j < numaric.length; j++) {
            var alphaa = numaric.charAt(j);

            var hh = alphaa.charCodeAt(0);

            if (hh > 47 && hh < 58) {
                nu = 1;
            }
            if ((hh > 64 && hh < 91) || (hh > 96 && hh < 123)) {
                alf = 1;
            }

        }

        if (nu == 1 && alf == 1) {

            nu = 0;
            alf = 0;
            var aa = document.getElementById('lblalpha');
            if (aa != null) {
                aa.innerHTML = "";
            }
            document.getElementById("ctl00_cphBody_txtConPwd").focus();
            genratecode(obj);
            return true;

        }
        else {
            document.getElementById('ctl00_cphBody_txtNewPwd').value = '';
            document.getElementById("ctl00_cphBody_txtNewPwd").focus();
            nu = 0;
            alf = 0;
            var aa = document.getElementById('lblalpha');
            if (aa != null) {
                aa.innerHTML = "Password Should be alphanumeric";

            }
            return false;
        }

    }
    else {
        var aa = document.getElementById('lblalpha');
        if (aa != null) {
            aa.innerHTML = "Password length should be greater than 8";
        }
        return false;
    }
}


function getHashData(control) {
    control.value = genrateUserNameHash(control, $('#ctl00_cphBody_hdfStaticGuid').val());
}