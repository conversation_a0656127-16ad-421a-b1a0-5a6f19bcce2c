﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="AvgDataLag.ascx.cs" Inherits="CP.UI.Controls.AvgDataLag" %>
<link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
<asp:UpdatePanel runat="server" ID="updatepnl" UpdateMode="Conditional">
    <ContentTemplate>
        <div class="widget widget-heading-simple widget-body-white">

            <div class="widget-body">
                <div class="cioheader">
                    <div class="title">
                        <div class="text-left" style="width: 26%; display: inline-block">
                            Avg. Datalag Trend
                    <br />
                            <div class="subhead">
                                Past 7 Days
                            </div>
                        </div>
                        <div class="" style="width: 72%; display: inline-block">

                            <a href="#" data-toggle="dropdown" class="filter_icon_cio" title="Filter"></a>
                               
                            <ul class="dropdown-menu pull-right filterulcio notifyscroll" id="filterul" style="max-height: 175px;">

                                <asp:Repeater runat="server" ID="rptServices" OnItemCommand="rptServices_ItemCommand">
                                    <HeaderTemplate>
                                    </HeaderTemplate>
                                    <ItemTemplate>
                                        <li>
                                            <img src="../Images/CIO/cogwheel_icon.png" style="padding:0 2px 0px 5px" />
                                            <asp:LinkButton runat="server" ID="lnkbtnBusinessServiceName" Text='<%#Eval("Name") %>'>
                                            </asp:LinkButton>
                                            <asp:Label runat="server" ID="lblbusinessID" Text='<%#Eval("ID") %>' Visible="false"></asp:Label>
                                            <asp:Label runat="server" ID="lblrpo" Text='<%#Eval("ConfiguredRPO") %>' Visible="false"></asp:Label>
                                        </li>
                                    </ItemTemplate>

                                    <FooterTemplate>
                                    </FooterTemplate>
                                </asp:Repeater>

                            </ul>

                            <asp:ImageButton ID="imgBtnReport" runat="server" Visible="false" ImageUrl="../Images/CIO/report-icon.png" Style="float: right;" />
                        </div>
                    </div>
                </div>
                <div class="ciocontent">
                    <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 4px;">
                        <div class="col-md-6 padding-none-LR">
                            <div class="">
                                <asp:Label ID="lblcurrent" runat="server" CssClass="count" Text="0"></asp:Label>
                                <asp:Label ID="Label33" runat="server" CssClass="count" Text="Mins" Style="font-size: 14px;"></asp:Label>
                            </div>

                            <asp:Label ID="lblCurrentMth" runat="server" CssClass="subhead" Text="0"></asp:Label>
                            <span class="subhead">(Current Month)</span>
                        </div>
                    </div>
                    <div class="text-center lngraph">
                        <telerik:RadHtmlChart runat="server" ID="LineChartDataLag" Width="394px" Height="120px" Transitions="true" Skin="Silk">
                            <Appearance>
                                <FillStyle BackgroundColor="Transparent"></FillStyle>
                            </Appearance>

                            <Legend>
                                <Appearance BackgroundColor="Transparent" Position="Bottom">
                                </Appearance>
                            </Legend>
                            <PlotArea>
                                <Appearance>
                                    <FillStyle BackgroundColor="Transparent"></FillStyle>
                                </Appearance>
                                <XAxis AxisCrossingValue="0" Color="#878787" MajorTickType="None" MinorTickType="Outside" Width="1"
                                    Reversed="false">
                                    <MajorGridLines Visible="false" />
                                    <MinorGridLines Visible="false" />
                                    <LabelsAppearance DataFormatString="{0}" RotationAngle="20" Skip="0" Step="1">
                                    </LabelsAppearance>

                                </XAxis>
                                <YAxis AxisCrossingValue="0" Color="#878787" MajorTickSize="0" MajorTickType="None" Width="0"
                                     MinorTickSize="0" MinorTickType="None"  Reversed="false" 
                                   >
                                    <MinorGridLines Visible="false" />
                                    <LabelsAppearance DataFormatString="{0}min" RotationAngle="0" Skip="0" Step="1">
                                    </LabelsAppearance>

                                </YAxis>
                            </PlotArea>
                        </telerik:RadHtmlChart>
                    </div>
                </div>
                <div class="ciofooter">
                    <div class="col-md-9 padding-none-LR">
                        <img src="../Images/CIO/graph-leg.png" style="vertical-align: super;" />
                        <asp:Label CssClass="legtext tdword-wrap" runat="server" ID="lblavgservicename" Text="Avg. Datalag" style="margin-right: 6px; display: inline-block; vertical-align: top; width: 50%;"></asp:Label>
                        <img src="../Images/CIO/graph-leg-red.png" style="vertical-align: super;" />
                        <asp:Label CssClass="legtext" runat="server" ID="Label1" Text="Config. RPO" style="vertical-align: super;"></asp:Label>
                    </div>
                    <div class="col-md-3 padding-none-LR text-right">
                        <%--<asp:ImageButton ID="ImageButton10" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />--%>
                         <span class="ciopopover" data-toggle="popover" data-title="Avg. DataLag Trend" data-content="1) This is always Per Service/Per Function / Per InfraObject. (There is no overall datalag trend at installation level)<br />
2) We calculate DataLag either on per day/per week / per month / overall per service till YTD<br />
3) Max DataLag datapoint per hour is selected for 24 hr DataLag calculations<br />
4) Max DataLag datapoint out of 24hrs is selected for per day DataLag calculations<br />
5) Daily Avg DataLag(Max) is calculated as the average of only max datalag datapoints per hour.<br />
6) Daily Avg. DataLag(Overall)  is calculated as the average of all datalag datapoints of the day.<br />
7) Similar calculations from Point 1-6 are done for Functions / InfraObjects" data-placement="top" data-html="true"></span>
                    </div>
                </div>
            </div>

        </div>
    </ContentTemplate>
</asp:UpdatePanel>
<script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>

<script type="text/javascript">

    function pageLoad() {
        $(".notifyscroll").mCustomScrollbar({
            axis: "y"
        });
    }


    $(document).ready(function () {
        $(".notifyscroll").mCustomScrollbar({
            axis: "y"
        });
    });
</script>
