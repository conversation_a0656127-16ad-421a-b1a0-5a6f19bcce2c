﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "VsphereReplicationMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class VsphereReplicationMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }
        //[DataMember]
        //public string Createdate { get; set; }

        [DataMember]
        public string VsphereReplicationServerPR { get; set; }
        [DataMember]
        public string VsphereReplicationServerDR { get; set; }
        [DataMember]
        public string ReplicationHostnameServerPR { get; set; }
        [DataMember]
        public string ReplicationHostnameServerDR { get; set; }
        [DataMember]
        public string ReplicationServerVersionPR { get; set; }
        [DataMember]
        public string ReplicationServerVersionDR { get; set; }
        [DataMember]
        public string ReplicationServerStatusPR { get; set; }
        [DataMember]
        public string ReplicationServerStatusDR { get; set; }
        [DataMember]
        public string VMReplicationsSRMRecoveryPlanCountPR { get; set; }
        [DataMember]
        public string VMReplicationsSRMRecoveryPlanCountDR { get; set; }
        //[DataMember]
        //public string VMNamePR { get; set; }
        //[DataMember]
        //public string VMNameDR { get; set; }
        //[DataMember]
        //public string LastSyncTimePR { get; set; }
        //[DataMember]
        //public string LastSyncTimeDR { get; set; }
          [DataMember]
        public string VMNameLastSyncLagTimePR { get; set; }

        [DataMember]
        public string VMNameLastSyncLagTimeDR { get; set; }

        [DataMember]
        public string OverallLagTimePR { get; set; }
        [DataMember]
        public string OverallLagTimeDR { get; set; }





        #endregion Properties
    }
}