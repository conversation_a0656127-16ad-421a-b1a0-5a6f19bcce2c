﻿var clickedNodeId;
var Ip;
var globalJson;
var profileName;
var isStringEval = false;
var d3transform;
var url = 'data.json'
var r = 10;
var graph, layout, zoom, nodes, links, data;
var linkedByIndex = {};
var graphWidth, graphHeight;
var hdnIsSaveClicked;
var newTempDeleteLinkInfoList = [];
var newTempLinkInfoList = [];
var newTempAddedGrpNodesDetails = [];
var newTempremovedGrpNodesDetails = [];
var GlobalAddGroupNodeList = [];
var GlobalRemoveGroupNodeList = [];
var grpNodeMenuText = "";
var ishdnGroupChange = 0;
var grpList = [];
var objGrpIndex = [];

function dependoGraph(finalJsonString, hdnStr) {

    //finalJsonString = { "directed": true, "multigraph": false, "nodes": [{ "id": "**************", "data": { "OS": "MS Windows Server 2008 SP2", "allApps": "http,msrpc,netbios-ssn,microsoft-ds,ms-sql-s,device2,ms-olap4,ms-wbt-server" } }, { "id": "************", "data": { "OS": "Windows Server 2008 R2", "allApps": "msrpc,netbios-ssn,microsoft-ds,ms-wbt-server" } }, { "id": "**************", "data": { "OS": "MS Windows Server 2008 SP2", "allApps": "http,msrpc,netbios-ssn,microsoft-ds,ms-sql-s,device2,ms-olap4,ms-wbt-server" } }, { "id": "172.16.128.11", "data": { "OS": "MS Windows Vista SP1 - SP2", "allApps": "domain,http,kerberos-sec,msrpc,netbios-ssn,ldap,microsoft-ds,kpasswd5,http-rpc-epmap,ldapssl,NFS-or-IIS,LSA-or-nterm,ms-lsa,iad1,iad2,boinc,vfo,kyoceranetdev,jstel,compaqdiag,compaq-https,globalcatLDAP,globalcatLDAPssl,ms-wbt-server" } }, { "id": "172.16.128.138", "data": { "OS": "Linux 2.6.9 - 2.6.30", "allApps": "rpcbind,oracle" } }, { "id": "172.16.128.139", "data": { "OS": "MS Windows 7 SP0 - SP1", "allApps": "http,msrpc,netbios-ssn,microsoft-ds,mysql,ms-wbt-server" } }, ], "links": [{ "source": 0, "target": 1 }, { "source": 0, "target": 2 }, { "source": 0, "target": 3 }, { "source": 2, "target": 0 }, { "source": 2, "target": 1 }, ] };
    $(".graph svg").remove();
    hdnIsSaveClicked = hdnStr;
    data = globalJson;
    var tempData = data;

    if (jQuery.type(finalJsonString) === "string")
        tempData = eval('(' + finalJsonString + ')');

    grpList = [];
    tempData.nodes.forEach(function (n) {
        if (n.groupName != undefined) {
            if (grpList.indexOf(n.groupName) == -1)
                grpList.push(n.groupName);
        }
    });
    for (var k = 0; k < grpList.length; k++) {
        var gname = grpList[k];
        objGrpIndex[gname] = k + 1;
    }

    if (jQuery.type(finalJsonString) === "string")
        render(finalJsonString);
    else if (jQuery.type(finalJsonString) === "object")
        renderAfterTagging(data);

    ///Create Group accordion
    var grpHtml = "<div class='accordionGroup'>";
    for (var k = 0; k < grpList.length; k++) {
        grpHtml += "<h3>" + grpList[k] + "</h3><div><ol>";
        tempData.nodes.forEach(function (n) {
            if (n.groupName == grpList[k])
                if (n.isTagged == "True")
                    grpHtml += "<li>" + n.entityName + " (" + n.id + ")</li>";
                else
                    grpHtml += "<li>" + n.id + "</li>";
        });
        grpHtml += "</ol></div>";
    }
    grpHtml += "</div>";
    $('.lblSummary').html("Total Groups -" + grpList.length);
    $('.accordionGroup1').html(grpHtml);
    $('.group-node-details').css("display", "block");
    var $accordions = $(".accordionGroup").accordion({
        collapsible: true,
        active: false,
        animated: 'slide',
        autoHeight: false,
        event: 'click',


    }).on('click', function () {
        $accordions.not(this).accordion('activate', false);
    });   
    $(".accordionGroup .ui-accordion-content-active").css("height", "auto");

}
function formatClassName(prefix, object) {
    return prefix + '-' + object.id.replace(/(\.|\/)/gi, '-');
}

function findElementByNode(prefix, node) {
    var selector = '.' + formatClassName(prefix, node);
    return graph.select(selector);
}

function isConnected(a, b) {
    return linkedByIndex[a.index + "," + b.index] || linkedByIndex[b.index + "," + a.index] || a.index == b.index;
}

function fadeRelatedNodes(d, opacity, nodes, links) {


    $('path.link').removeAttr('data-show');

    nodes.style("stroke-opacity", function (o) {

        if (isConnected(d, o)) {
            thisOpacity = 1;
        } else {
            thisOpacity = opacity;
        }

        this.setAttribute('fill-opacity', thisOpacity);
        this.setAttribute('stroke-opacity', thisOpacity);

        //if (thisOpacity == 1) {
        //    this.classList.remove('dimmed');
        //} else {
        //    this.classList.add('dimmed');
        //}
        if (thisOpacity == 1) {
            // this.classList.remove('dimmed');
            this.className += " ";
        } else {
            //this.classList.add('dimmed');
            this.className += " dimmed";
        }

        return thisOpacity;
    });

    links.style("stroke-opacity", function (o) {

        if (o.source === d) {


            var elmNodes = graph.selectAll('.' + formatClassName('node', o.target));
            elmNodes.attr('fill-opacity', 1);
            elmNodes.attr('stroke-opacity', 1);

            elmNodes.classed('dimmed', false);


            var elmCurrentLink = $('path.link[data-source=' + o.source.index + ']');
            elmCurrentLink.attr('data-show', true);
            elmCurrentLink.attr('marker-end', 'url(#regular)');

            return 1;

        } else {

            var elmAllLinks = $('path.link:not([data-show])');

            if (opacity == 1) {
                elmAllLinks.attr('marker-end', 'url(#regular)');
            } else {
                //elmAllLinks.attr('marker-end', '');
                elmAllLinks.removeAttr('marker-end');
            }

            return opacity;
        }

    });
}

function render(finalJsonString) {

    zoom = d3.behavior.zoom();
    zoom.on("zoom", onZoomChanged);


    layout = d3.layout.force()
      .gravity(.05)
      .charge(-300)
      .linkDistance(100);


    graph = d3.select(".graph")
      .append("svg:svg")
      .attr("pointer-events", "all")
      .call(zoom)
      .append('svg:g')
        .attr('width', graphWidth)
        .attr('height', graphHeight);

    d3.select(window).on("resize", resize);


    // var graphData = window.getGraphData();

    var JsonObj = eval('(' + finalJsonString + ')');
    //isStringEval = true;
    var graphData = JsonObj;
    globalJson = JsonObj;
    //var graphData = finalJsonString;

    renderGraph(graphData);

    data = graphData;

    resize();

    centerGraph();
    $('.control-zoom ,.node-details').css("display", "block");

    $('.control-zoom a').on('click', onControlZoomClicked);
}

function resize() {
    graphWidth = $(".col-md-10").innerWidth(),
    graphHeight = $(".col-md-10").innerHeight();
    graph.attr("width", graphWidth)
         .attr("height", graphHeight);

    layout.size([graphWidth, graphHeight])
          .resume();
}

function centerGraph() {

    var centerTranslate = [
      (graphWidth / 2) - (graphWidth * 0.4 / 2),
      (graphHeight / 2) - (graphHeight * 0.4 / 2),
    ];

    zoom.translate(centerTranslate);


    graph.transition()
      .duration(500)
      .attr("transform", "translate(" + zoom.translate() + ")" + " scale(" + zoom.scale() + ")");
}

function renderGraph(data) {


    graph.append("svg:defs").selectAll("marker")
        .data(['regular'])
      .enter().append("svg:marker")
        .attr("id", String)
        .attr("viewBox", "0 -5 10 10")
        .attr("refX", 99.5)
        .attr("refY", -.4)
        .attr("markerWidth", 6)
        .attr("markerHeight", 6)
        .attr("orient", "auto")
      .append("svg:path")
        .attr("d", "M0,-5L10,0L0,5")
        .attr("fill", "#0f9def");


    links = graph.append('svg:g').selectAll("line")
      .data(data.links)
      .enter().append("svg:path")
      .attr('class', 'link')
      .attr("data-target", function (o) { return o.target.index })
      .attr("data-source", function (o) { return o.target.index })
      .attr("marker-end", function (d) { return "url(#regular)"; });


    nodes = graph.append('svg:g').selectAll("node")
      .data(data.nodes)
      .enter().append("svg:g")
      .attr("class", "node")
      .call(layout.drag)
      .on("mousedown", onNodeMouseDown)
      .on("click", function (n) {

          GetnodeProperties(n);
          $('[id$=modelbg]').css({ "display": "block" });
          $("#tabs-1,#tabs-2,#tabs-3").removeClass("show");
          $(".prop-tab li ").removeClass();
          $('#tabs-1').addClass("show");
          $('.testul li:first').addClass('active');
          $('[id$=pnlproperties]').css({ "display": "block" });
          var nodedetails = "<h3 class='margin-none' style='line-height:25px;'><img alt='Dependencies' src='../Images/business-process-icon.png'  class='vertical-baseline'><span class='lblSummary'> Dependencies</span></h3><hr class='margin-none' /><h5 class='margin-bottom-none text-primary text-bold' style='line-height:25px;'><img alt='Dependencies' src='../Images/Normal-icn.png' style='vertical-align: bottom;' width='20'>" + n.id + "</h5><ul><li><a> <img alt='Dependencies' src='../Images/icons/serverIP-icon.png' width='12px' height='14px' style='vertical-align: sub;' >",
          list = [];

          data.links.forEach(function (d) {
              if (d.source.id == n.id) {
                  list.push(d.target.id);
              }
          });
          if (list.length == 0) {
              list.push(" No Dependencies");
              $('.node-details').html(nodedetails + list.join("</a></li><li><a><img alt='Dependencies' src='../Images/icons/serverIP-icon.png' width='12px' height='14px' class='vertical-baseline'>") + "</a></li></ul>");
          }
          else if (list.length > 0) {
              if (list.length == 1) {
                  $('.node-details').html(nodedetails + list.join("</a></li><li><a><img alt='Dependencies' src='../Images/icons/serverIP-icon.png' width='12px' height='14px' class='vertical-baseline'>") + "</a></li></ul>");
              }
              else if (list.length > 1) {
                  sortedList = [];
                  sortedList = BubbleSortIP(list);
                  $('.node-details').html(nodedetails + sortedList.join("</a></li><li><a><img alt='Dependencies' src='../Images/icons/serverIP-icon.png' width='12px' height='14px' class='vertical-baseline'>") + "</a></li></ul>");
              }

          }
          $('.node-details ul').slimScroll({
              // height: 'auto',
              //alwaysVisible: true
          });
          //var slimscrolwidth = $(".slimScrollDiv").height();
          //$(".slimScrollDiv").css("height", (slimscrolwidth - 80) + "px");
          $(".slimScrollDiv").css("height", "270px");

      });

    nodes.attr("class", function (d) { return formatClassName('node', d) })

    nodes.append("svg:image")
            .attr("class", function (d) { return formatClassName('circle', d) })
            //.attr("xlink:href", "../Images/server-lightgreen.png")
            .attr("xlink:href", function (d) { return returnImageByCriticality(d) })
            .attr("x", "-7")
            .attr("y", "-0.5em")
            .attr("height", "24")
            .attr("width", "24")
      .on("mouseover", _.bind(onNodeMouseOver, this, nodes, links))
      .on("mouseout", _.bind(onNodeMouseOut, this, nodes, links))
     .on("contextmenu", function (d, nodes, links) {
         clickedNodeId = d.id;
         $('[id$="lblSaveMsg"]').hide();
         var menuText = "";


         var addDep = "Add Dependency";
         var editDep = "Edit Dependency";

         var groupNodeText = "";
         var createGroupNodeText = "Create Group Nodes";
         var editGroupNodeText = "Edit Group Nodes";

         var dependencyCount = 0;
         //list = [];

         data.links.forEach(function (n) {
             if (n.source.id == d.id) {
                 dependencyCount = dependencyCount + 1;
             }
         });

         if (dependencyCount == 0)
             menuText = addDep;
         else if (dependencyCount > 0)
             menuText = editDep;

         if (d.isGroupped == "True")
             grpNodeMenuText = editGroupNodeText;
         else
             grpNodeMenuText = createGroupNodeText;

         var linkToPage = "";
         var classStr = "";
         var classimp = "";
         if (d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "Yes") {
             classStr = "hide";
             linkToPage = "javascript:void(0);";
         }
         else if (d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "No") {
             classStr = "show";
             linkToPage = "../Group/InfraObjectsConfiguration.aspx";
         }
         else if (d.isIPAddressConfig == "No" && d.isIPaddressAssignedToInfra == "No") {
             classStr = "show";
             linkToPage = "../Component/ServerConfiguration.aspx";
         }
         if (d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "Yes") {
             classimp = "";
         }
         else {
             classimp = "hide";
         }
         context.attach(("svg image"), [
              {
                  text: 'Tag Port', href: "javascript:void(0);", action: function (e) {
                      GetServerport(d.id);

                  }
              },
         {
             text: 'Tag Entity', href: "javascript:void(0);", action: function (e) {
                 $('[id$=modelbg]').css({ "display": "block" });
                 $('[id$=pnlTagEntity]').css({ "display": "block" });
                 $("#spnIPAddress").text(d.id);

                 if (d.isTagged == "True") {

                     $("[id$=txtEntityName]").val(d.entityName);
                     $("[id$=txtDescription]").val(d.tagEntitydesc);
                     $("[id*='ddlCriticality'] option:contains(" + d.tagEntityCriticality + ")").attr('selected', 'selected');
                 }
                 else if (d.isTagged == "False") {
                     $("[id$=txtEntityName]").val('');
                     $("[id$=txtDescription]").val('');
                     $('[id$=ddlCriticality] option:eq(0)').attr("selected", "selected");
                 }
             }
         },
         {
             text: ' Impact', class: classimp, href: "javascript:void(0);", action: function (b) {
                 OpenWindow(clickedNodeId);
             }
         },
         {
             text: menuText, href: "javascript:void(0);", action: function (e) {
                 $("#btnchklist").val(" - Select IP - ");
                 $('[id$=modelbg]').css({ "display": "block" });
                 $('[id$=pnlMapDependancy]').css({ "display": "block" });
                 $("#spnTagName").text(d.id);
                 $("#lbladm").text(menuText);
                 $("ul#ulchklist").empty();
                 $('[id$="btnDepMapSave"]').attr("disabled", "disabled")


                 list = [];


                 data.links.forEach(function (n) {
                     if (n.source.id == d.id) {
                         list.push(n.target.id);
                     }
                 });
                 if (list.length == 0) {

                     listOfNodes1 = [];

                     data.nodes.forEach(function (n) {

                         if (n.id != d.id) {
                             listOfNodes1.push(n.id);
                         }
                     });
                     if (listOfNodes1.length > 0) {
                         listOfNodes2 = [];
                         listOfNodes2 = BubbleSortIP(listOfNodes1);

                         listOfNodes2.forEach(function (s) {

                             data.nodes.forEach(function (n) {
                                 if (s == n.id) {
                                     if (n.id != d.id) {
                                         if (n.isTagged == "True")
                                             $("ul#ulchklist").append('<li title="' + n.entityName + '"><input type="checkbox" value="' + n.id + '"><label>' + n.id + '</label></li>');
                                         else
                                             $("ul#ulchklist").append('<li><input type="checkbox" value="' + n.id + '"><label>' + n.id + '  </label></li>');
                                     }
                                 }
                             });
                         });
                     }
                 }
                 else {

                     listOfNodes = [];

                     data.nodes.forEach(function (n) {

                         if (n.id != d.id) {
                             listOfNodes.push(n.id);
                         }
                     });
                     if (listOfNodes.length > 0) {

                         finalListOfNodes = [];

                         finalListOfNodes = arr_diff(listOfNodes, list);

                         if (finalListOfNodes.length > 0) {

                             combineNodes = [];

                             list.forEach(function (c) {
                                 combineNodes.push(c);
                             });
                             finalListOfNodes.forEach(function (k) {
                                 combineNodes.push(k);
                             });

                             if (combineNodes.length > 0) {
                                 NewCombinedArrayWithSorting = [];
                                 NewCombinedArrayWithSorting = BubbleSortIP(combineNodes);

                                 NewCombinedArrayWithSorting.forEach(function (k) {

                                     data.nodes.forEach(function (n) {

                                         if (n.id == k) {
                                             if (n.isTagged == "True") {

                                                 var dependencyCount = 0;

                                                 if (list.length > 0) {
                                                     list.forEach(function (m) {
                                                         if (k == m)
                                                             dependencyCount = dependencyCount + 1;
                                                     });
                                                 }
                                                 if (dependencyCount > 0)
                                                     $("ul#ulchklist").append('<li title="' + n.entityName + '"><input type="checkbox" checked="checked" value="' + n.id + '"><label class="text-success">' + n.id + ' </label></li>');
                                                 else
                                                     $("ul#ulchklist").append('<li title="' + n.entityName + '"><input type="checkbox" value="' + n.id + '"><label>' + n.id + '  </label></li>');
                                             }
                                             else {
                                                 var dependencyCount = 0;

                                                 if (list.length > 0) {
                                                     list.forEach(function (m) {
                                                         if (k == m)
                                                             dependencyCount = dependencyCount + 1;
                                                     });
                                                 }
                                                 if (dependencyCount > 0)
                                                     $("ul#ulchklist").append('<li><input type="checkbox" checked="checked" value="' + n.id + '"><label class="text-success">' + n.id + '  </label></li>');
                                                 else
                                                     $("ul#ulchklist").append('<li><input type="checkbox" value="' + n.id + '"><label>' + n.id + '  </label></li>');
                                             }
                                         }
                                     });
                                 });
                             }
                         }
                         else if (finalListOfNodes.length == 0) {

                             listOfAllMappedNodes = [];

                             data.nodes.forEach(function (n) {
                                 if (n.id != d.id)
                                     listOfAllMappedNodes.push(n.id);
                             });
                             if (listOfAllMappedNodes.length > 0) {

                                 listOfAllMappedNodes1 = [];

                                 listOfAllMappedNodes1 = BubbleSortIP(listOfAllMappedNodes);

                                 listOfAllMappedNodes1.forEach(function (mn) {

                                     data.nodes.forEach(function (n) {
                                         if (n.id == mn) {
                                             if (n.isTagged == "True")
                                                 $("ul#ulchklist").append('<li title="' + n.entityName + '"><input type="checkbox" checked="checked" value="' + n.id + '"><label class="text-success">' + n.id + '</label></li>');
                                             else
                                                 $("ul#ulchklist").append('<li><input type="checkbox" checked="checked" value="' + n.id + '"><label class="text-success">' + n.id + '  </label></li>');
                                         }
                                     });
                                 });
                             }
                         }
                     }
                 }
                 $('input[type="checkbox"]').checkbox();

                 var selectedIp = "";
                 for (var e = 0; e < $("ul#ulchklist input[type=checkbox]").length; e++) {
                     var chkBox = $("ul#ulchklist input[type=checkbox]")[e];
                     if (chkBox.checked == true)
                         selectedIp += chkBox.value.split('.')[3] + ",";
                 }
                 if (selectedIp != "")
                     $("#btnchklist").val(selectedIp.substring(0, selectedIp.length - 1));
                 else
                     $("#btnchklist").val(" - Select IP - ");

             }
         },
{
    text: 'Configure', href: linkToPage, class: classStr, action: function (n) {

        if (localStorage.getItem("ADMIP") != null)
            localStorage.removeItem("ADMIP");
        localStorage.setItem("ADMIP", d.id);
    }
},
         {
             text: grpNodeMenuText, href: "javascript:void(0);", action: function (e) {
                 $('[id$=modelbg]').css({ "display": "block" });
                 $('[id$=Paneltaggroup]').css({ "display": "block" });
                 $("#lbladmgrp").text(d.id);
                 $("ul#ulgrpchklist").empty();
                 $("#lblError").attr("style", "display:none");
                 //$('[id$="btnSaveGroupNodes"]').attr("disabled", "disabled")



                 if (d.isGroupped == "True") {

                     $("[id$=txtgrpnode]").val(d.groupName);
                     $("[id$=txtgrpDescription]").val(d.groupDescription);
                     //$('[id$="btnSaveGroupNodes"]').removeAttr('disabled');
                     $('[id$="txtgrpnode"]').attr("disabled", "disabled");
                     $('[id$="txtgrpDescription"]').attr("disabled", "disabled");
                 }
                 else if (d.isGroupped == "False") {
                     $("[id$=txtgrpnode]").val('');
                     $("[id$=txtgrpDescription]").val('');
                     //$('[id$="btnSaveGroupNodes"]').removeAttr('disabled');
                     $('[id$="txtgrpnode"]').removeAttr('disabled');
                     $('[id$="txtgrpDescription"]').removeAttr('disabled');
                 }
                 list = [];
                 var temp = "";

                 //adddirect dependent nodes
                 data.links.forEach(function (n) {
                     if (n.source.id == d.id) {
                         list.push(n.target.id);
                     }
                 });

                 //Add all other nodes which are not dependents
                 if (list != null && list.length > 0) {
                     data.nodes.forEach(function (n) {
                         if (list.indexOf(n.id) == -1) {
                             list.push(n.id);
                         }
                     });
                 }
                 else {
                     data.nodes.forEach(function (n) {
                         list.push(n.id);
                     });
                 }

                 list.forEach(function (t) {
                     temp = "";
                     var n = jQuery.grep(data.nodes, function (obj) {
                         return obj.id === t;
                     })[0];
                     if (n.id != d.id) {
                         if (n.isGroupped != "True" && grpNodeMenuText == "Create Group Nodes") {
                             if (n.isTagged == "True")
                                 $("ul#ulgrpchklist").append('<li title="' + n.entityName + '"><input type="checkbox"  value="' + n.id + '"   ><label>' + n.id + '</label></li>');
                             else
                                 $("ul#ulgrpchklist").append('<li><input type="checkbox"   value="' + n.id + '"><label>' + n.id + '  </label></li>');
                         }
                         else {

                             if (n.isGroupped == "True")
                                 temp = 'checked="checked"';
                             if (grpNodeMenuText == "Edit Group Nodes" && n.groupName == d.groupName || n.isGroupped == "False") {
                                 if (n.isTagged == "True")
                                     $("ul#ulgrpchklist").append('<li title="' + n.entityName + '"><input type="checkbox" ' + temp + ' value="' + n.id + '"   ><label>' + n.id + '</label></li>');
                                 else
                                     $("ul#ulgrpchklist").append('<li><input type="checkbox"  ' + temp + ' value="' + n.id + '"><label>' + n.id + '  </label></li>');
                             }

                         }
                     }

                 });

                 $('input[type="checkbox"]').checkbox();
                 
                 var selectedIp = "";
                 for (var e = 0; e < $("ul#ulgrpchklist input[type=checkbox]").length; e++) {
                     var chkBox = $("ul#ulgrpchklist input[type=checkbox]")[e];
                     if (chkBox.checked == true)
                         selectedIp += chkBox.value.split('.')[3] + ",";
                 }
                 if (selectedIp != "")
                     $("#btngroupchklist").val(selectedIp.substring(0, selectedIp.length - 1));
                 else
                     $("#btngroupchklist").val(" - Select Nodes - ");
             }
         }
         ]);
     });

    nodes.append("svg:text")
        .attr("class", function (d) { return formatClassName('text', d) })
        .attr("x", -22)
        .attr("y", "-1.5em")
        .text(function (d) { return returnTaggedName(d); });


    nodes.append("svg:foreignObject")
    .attr("width", 20)
    .attr("height", 20)
    .attr("y", "2px")
    .attr("x", "10px")
  .append("xhtml:span")
   .text(function (d) { return getGroupIndex(d); })
        .on("mouseover", function (d) { return groupToolTip(d); })
         .on("mouseout", function (d) { return removeGrpToolTip(d); })
    .attr("class", function (d) { return getSpanClass(d) });

    data.links.forEach(function (d) {
        linkedByIndex[d.source.index + "," + d.target.index] = 1;
    });


    layout.nodes(data.nodes);
    layout.links(data.links);
    layout.on("tick", onTick);
    layout.start();

    var count = 0;

    data.nodes.forEach(function (n) {
        if (n.isTagged == "True")
            count = count + 1;
    });

    if (jQuery.type(data) === "object" && count == 0 && hdnIsSaveClicked == 0) {
        zoom.scale(0.4);


        graph.transition()
          .duration(500)
        .attr("transform", d3transform);
        //.attr("transform", "scale(" + zoom.scale() + ")");
    }
    else if (jQuery.type(data) === "object" && count > 0 && hdnIsSaveClicked == 1) {
        zoom.scale(0.4);


        graph.transition()
          .duration(500)
         .attr("transform", d3transform);
        //  .attr("transform", "scale(" + zoom.scale() + ")");
        //d3transform = $(".graph svg > g").attr("transform");
        //$(".graph svg > g").removeAttr("transform");

        //$(".graph svg > g").attr("transform", d3transform);
    }
    else if (jQuery.type(data) === "object" && count == 0 && hdnIsSaveClicked == 1) {
        zoom.scale(0.4);

        graph.transition()
          .duration(500)
         .attr("transform", d3transform);
        //.attr("transform", "scale(" + zoom.scale() + ")");
    }


}

function returnTaggedName(d) {
    if (d.isTagged == "True") {
        if (d.entityName.length > 5)
            return d.entityName.substr(0, 4) + ".." + "(" + d.id + ")";
        else
            return d.entityName + "(" + d.id + ")";
    }
    else {
        return d.id;
    }
}


function getSpanClass(d) {
    if (d.groupName != undefined)
        return "nodeSpan";
    else
        return "";
}

function getGroupIndex(d) {
    if (d.groupName != undefined) {
        for (var key in objGrpIndex) {
            if (key == d.groupName)
                return "G" + objGrpIndex[key];
        }
    }
    else
        return "";
}

function groupToolTip(d) {
    //debugger;
    $('.tooltip').empty();
    var html = "<div class=\"tip-text\">Group Name: <b>" + d.groupName + "</b></div>";
    $('.tooltip').css({ "left": (d3.event.pageX - 15), "top": (d3.event.pageY - 175), "border": "1px solid #ccc", "background-color": "#ccffff", "opacity": "1" }).append(html);
}

function removeGrpToolTip() {
    $('.tooltip').html("").css({ "border": "1px solid transparent", "background-color": "transparent", "opacity": "0" });

}

function returnImageByCriticality(d) {
    //var imageUrl = "";
    if (d.isTagged == "True") {
        if (d.tagEntityCriticality == "High" && d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "Yes") {
            return "../Images/High-protect-icn.png";
        }
        else if (d.tagEntityCriticality == "High" && d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "No") {
            return "../Images/High-Nprotect-icn.png";
        }
        else if (d.tagEntityCriticality == "High" && d.isIPAddressConfig == "No" && d.isIPaddressAssignedToInfra == "No") {
            return "../Images/High-Nprotect-icn.png";
        }
        else if (d.tagEntityCriticality == "Low" && d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "Yes") {
            return "../Images/Low-protect-icn.png";
        }
        else if (d.tagEntityCriticality == "Low" && d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "No") {
            imageUrl = "../Images/Low-Nprotect-icn.png";
        }
        else if (d.tagEntityCriticality == "Low" && d.isIPAddressConfig == "No" && d.isIPaddressAssignedToInfra == "No") {
            return "../Images/Low-Nprotect-icn.png";
        }
        else if (d.tagEntityCriticality == "Medium" && d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "Yes") {
            imageUrl = "../Images/Medium-protect-icn.png";
        }
        else if (d.tagEntityCriticality == "Medium" && d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "No") {
            return "../Images/Medium-Nprotect-icn.png";
        }
        else if (d.tagEntityCriticality == "Medium" && d.isIPAddressConfig == "No" && d.isIPaddressAssignedToInfra == "No") {
            return "../Images/Medium-Nprotect-icn.png";
        }
    }
    else if (d.isTagged == "False") {
        if (d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "Yes") {
            return "../Images/Normal-protect-icn.png";
        }
        else if (d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "No") {
            return "../Images/Normal-Nprotect-icn.png";
        }
        else if (d.isIPAddressConfig == "No" && d.isIPaddressAssignedToInfra == "No") {
            return "../Images/Normal-icn.png";
        }
    }

    //returnGroupImage(d, imageUrl)
}



function FormatCPConfigDetails(d) {

    var configInfo = "";
    if (d.configInformation != "None") {

        var bsbfStr = d.configInformation;

        var splitByHash = bsbfStr.split('|');

        if (splitByHash.length > 0) {
            splitByHash.forEach(function (n) {
                var splitByComma = n.split(',');
                if (splitByComma.length > 0) {
                    if (configInfo != "") {

                        var configStr = splitByComma[0] + "->" + splitByComma[1] + "->" + splitByComma[2] + "<br/>";
                        configInfo = configInfo + configStr;
                    }
                    else {
                        var configStr = splitByComma[0] + "->" + splitByComma[1] + "->" + splitByComma[2] + "<br/>";
                        configInfo = configStr;
                    }
                }
            });
        }
    }
    else
        configInfo = d.configInformation;
    return configInfo;
}

function onNodeMouseOver(nodes, links, d) {
    //debugger;
    var dependencyCount = 0;
    list = [];

    data.links.forEach(function (n) {
        if (n.source.id == d.id) {
            list.push(n.target.id);
        }
    });


    //var configAssignText;
    //if (d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "Yes")
    //    configAssignText = "<br/>Configured" + "<br/>" + "Protected";
    //else if (d.isIPAddressConfig == "Yes" && d.isIPaddressAssignedToInfra == "No")
    //    configAssignText = "<br/>Configured" + "<br/>" + "Not Protected";
    //else if (d.isIPAddressConfig == "No" && d.isIPaddressAssignedToInfra == "No")
    //    configAssignText = "<br/>Not Configured" + "<br/>" + "Not Protected";

    var configInfo = "";
    if (d.configInformation != "None") {

        var bsbfStr = d.configInformation;

        var splitByHash = bsbfStr.split('|');

        if (splitByHash.length > 0) {
            splitByHash.forEach(function (n) {
                var splitByComma = n.split(',');
                if (splitByComma.length > 0) {
                    if (configInfo != "") {

                        var configStr = splitByComma[0] + "->" + splitByComma[1] + "->" + splitByComma[2] + "<br/>";
                        configInfo = configInfo + configStr;
                    }
                    else {
                        var configStr = splitByComma[0] + "->" + splitByComma[1] + "->" + splitByComma[2] + "<br/>";
                        configInfo = configStr;
                    }
                }
            });
        }
    }
    else
        configInfo = d.configInformation;

    if (d.isTagged == "True" && d.isGroupped == "True") {

        var html = "<div class=\"tip-title text-left\">" + d.id + "</div>"
        + "<div class=\"tip-text\">Entity Name: <b>" + d.entityName + "</b></div>"
        + "<div class=\"tip-text\">Group Name: <b>" + d.groupName + "</b></div>"
        + "<div class=\"tip-text\">Group Description: <b>" + d.groupDescription + "</b></div>"
        + "<div class=\"tip-text\">Configured: <b>" + d.isIPAddressConfig + "</b></div>"
        + "<div class=\"tip-text\">CP Protected: <b>" + d.isIPaddressAssignedToInfra + "</b></div>"
        + "<div class=\"tip-text\">CP Configuration: <b>" + configInfo + "</b></div>"
        + "<div class=\"tip-text\">Dependencies: <b>" + list.length + "</b></div>"
        + "<div class=\"tip-text\">OS: <b>" + d.data.OS + "</b></div>"
        + "<div class=\"tip-text\">APPS: <b>" + d.data.allApps + "</b></div>";
        $('.tooltip').css({ "left": (d3.event.pageX - 15), "top": (d3.event.pageY - 225), "border": "1px solid #ccc", "background-color": "#ccffff", "opacity": "1" }).append(html);
    }
    else if (d.isTagged == "False" && d.isGroupped == "False") {
        var html = "<div class=\"tip-title text-left\">" + d.id + "</div>"
        + "<div class=\"tip-text\">Configured: <b>" + d.isIPAddressConfig + "</b></div>"
        + "<div class=\"tip-text\">CP Protected: <b>" + d.isIPaddressAssignedToInfra + "</b></div>"
        + "<div class=\"tip-text\">CP Configuration: <b>" + configInfo + "</b></div>"
        + "<div class=\"tip-text\">Dependencies: <b>" + list.length + "</b></div>"
        + "<div class=\"tip-text\">OS: <b>" + d.data.OS + "</b></div>"
        + "<div class=\"tip-text\">APPS: <b>" + d.data.allApps + "</b></div>";
        $('.tooltip').css({ "left": (d3.event.pageX - 15), "top": (d3.event.pageY - 225), "border": "1px solid #ccc", "background-color": "#ccffff", "opacity": "1" }).append(html);
    }
    else if (d.isTagged == "True" && d.isGroupped == "False") {

        var html = "<div class=\"tip-title text-left\">" + d.id + "</div>"
       + "<div class=\"tip-text\">Entity Name: <b>" + d.entityName + "</b></div>"
       + "<div class=\"tip-text\">Configured: <b>" + d.isIPAddressConfig + "</b></div>"
       + "<div class=\"tip-text\">CP Protected: <b>" + d.isIPaddressAssignedToInfra + "</b></div>"
       + "<div class=\"tip-text\">CP Configuration: <b>" + configInfo + "</b></div>"
       + "<div class=\"tip-text\">Dependencies: <b>" + list.length + "</b></div>"
       + "<div class=\"tip-text\">OS: <b>" + d.data.OS + "</b></div>"
       + "<div class=\"tip-text\">APPS: <b>" + d.data.allApps + "</b></div>";
        $('.tooltip').css({ "left": (d3.event.pageX - 15), "top": (d3.event.pageY - 225), "border": "1px solid #ccc", "background-color": "#ccffff", "opacity": "1" }).append(html);
    }
    else if (d.isTagged == "False" && d.isGroupped == "True") {

        var html = "<div class=\"tip-title text-left\">" + d.id + "</div>"
       + "<div class=\"tip-text\">Group Name: <b>" + d.groupName + "</b></div>"
       + "<div class=\"tip-text\">Group Description: <b>" + d.groupDescription + "</b></div>"
       + "<div class=\"tip-text\">Configured: <b>" + d.isIPAddressConfig + "</b></div>"
       + "<div class=\"tip-text\">CP Protected: <b>" + d.isIPaddressAssignedToInfra + "</b></div>"
       + "<div class=\"tip-text\">CP Configuration: <b>" + configInfo + "</b></div>"
       + "<div class=\"tip-text\">Dependencies: <b>" + list.length + "</b></div>"
       + "<div class=\"tip-text\">OS: <b>" + d.data.OS + "</b></div>"
       + "<div class=\"tip-text\">APPS: <b>" + d.data.allApps + "</b></div>";
        $('.tooltip').css({ "left": (d3.event.pageX - 15), "top": (d3.event.pageY - 225), "border": "1px solid #ccc", "background-color": "#ccffff", "opacity": "1" }).append(html);
    }

    var elm = findElementByNode('image', d);
    elm.style("fill", '#b94431');


    fadeRelatedNodes(d, .05, nodes, links);
}

function GetnodeProperties(d) {
    var Cpconfigdetails = FormatCPConfigDetails(d);
    // $('[id$=pnlproperties]').css({ "left": (d3.event.pageX - 15), "top": (d3.event.pageY - 225) });
    $('#tabs-1, #tabs-2,#tabs-3').html("");
    $("#spnip-prop").text(d.id);
    var iconspan;
    if ((d.data.OS).indexOf("Linux") != -1) {
        // alert(d.data);
        iconspan = "<span class='icon-linux'></span>";
        //  $('.tip-text:first-child').addClass('icon-linux');
    }
    else if ((d.data.OS).indexOf("Windows") != -1) {
        // alert('ss');
        iconspan = "<span class='icon-Windows'></span>";
    }
    else if ((d.data.OS).indexOf("HPUX") != -1) {
        // alert('ss');
        iconspan = "<span class='icon-HPUX'></span>";
    }
    else if ((d.data.OS).indexOf("AIX") != 1) {
        // alert('ss');
        iconspan = "<span class='icon-aix'></span>";
    }
    else if ((d.data.OS).indexOf("Solaris") != -1) {
        // alert('ss');
        iconspan = "<span class='icon-Solaris'></span>";
    } else if ((d.data.OS).indexOf("VMware") != -1) {
        iconspan = "<span class='vmware-icon'></span>";
    }
    else {
        iconspan = "<span class='os-icon'></span>";
    };
    Allapps = [];
    var splitallapps = "";
    Allapps = d.data.allApps.split(',');
    if (Allapps.length > 0) {
        Allapps.forEach(function (p) {
            if (splitallapps == "") {
                splitallapps = p;
            } else {
                splitallapps = splitallapps + ", " + p;
            }
        });
    }
    var CpConfigD = "";
    if (Cpconfigdetails == "None") {
        CpConfigD = "<span class='prop-value'> None</span>";
    }
    else {
        CpConfigD = "<div class='prop-value AddWidth'>" + Cpconfigdetails + "</div>";
    }
    var cpprop = " <div class=\"tip-text\">Configured: <span class='prop-value'>" + d.isIPAddressConfig + "</span></div>"
    + "<div class=\"tip-text\">CP Protected: <span class='prop-value'>" + d.isIPaddressAssignedToInfra + "</span></div>"
    + "<div class=\"tip-text\">CP Configuration:" + CpConfigD + "</div>";
    var osprop = "<div class=\"tip-text\">Operating System: <br/>" + iconspan + "   <span class='prop-value text-small'>" + d.data.OS + "</span></div>";
    var appsprop = "<div class=\"tip-text\">APPS: <div class='prop-value'>" + splitallapps + "</div></div>";
    $('#tabs-1').append(cpprop);
    $('#tabs-2').append(osprop);
    $('#tabs-3').append(appsprop);

    $("#tab-content").mCustomScrollbar({
        axis: "yx",
        autoDraggerLength: false,
        advanced: {
            updateOnContentResize: true,
            autoExpandHorizontalScroll: true
        },
        //scrollButtons: {
        //    enable: true
        //},
    });
    //var addWidth = $("#tabs-1 .mCSB_container").width();
    // alert(addWidth);
    //   $("#tabs-1 .AddWidth").css("padding-right", "10px");
}

function onNodeMouseOut(nodes, links, d) {
    $('.tooltip').html("").css({ "border": "1px solid transparent", "background-color": "transparent", "opacity": "0" });

    var elm = findElementByNode('image', d);
    elm.style("fill", '#ccc');


    fadeRelatedNodes(d, 1, nodes, links);

}

function onTick(e) {

    links.each(function () { this.parentNode.insertBefore(this, this); });

    links.attr("d", function (d) {
        var dx = d.target.x - d.source.x,
            dy = d.target.y - d.source.y,
            //dr = Math.sqrt(dx * dx + dy * dy);
             dr = 0;
        return "M" + d.source.x + "," + d.source.y + "A" + dr + "," + dr + " 0 0,1 " + d.target.x + "," + d.target.y;
    });

    nodes.attr("cx", function (d) { return d.x; })
     .attr("cy", function (d) { return d.y; })
     .attr("transform", function (d) { return "translate(" + d.x + "," + d.y + ")"; });
}

function onControlZoomClicked(e) {
    var elmTarget = $(this)
    var scaleProcentile = 0.20;


    var currentScale = zoom.scale();
    var newScale;
    if (elmTarget.hasClass('control-zoom-in')) {
        newScale = currentScale * (1 + scaleProcentile);
    } else {
        newScale = currentScale * (1 - scaleProcentile);
    }
    newScale = Math.max(newScale, 0);


    var centerTranslate = [
      (graphWidth / 2) - (graphWidth * newScale / 2),
      (graphHeight / 2) - (graphHeight * newScale / 2)
    ];


    zoom
      .translate(centerTranslate)
      .scale(newScale);


    graph.transition()
      .duration(500)
      .attr("transform", "translate(" + zoom.translate() + ")" + " scale(" + zoom.scale() + ")");

}

function onZoomChanged() {
    graph.attr("transform", "translate(" + d3.event.translate + ")" + " scale(" + d3.event.scale + ")");
}

function onNodeMouseDown(d) {
    d.fixed = true;
    d3.select(this).classed("sticky", true);
}

function isNumberKey(evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode
    if (charCode != 46 && charCode > 31
      && (charCode < 48 || charCode > 57))
        return false;
    return true;
}

function insertTagEntityDetailsInJson(globalJson, insertString) {
    //alert(globalJson);
    if (globalJson != null) {
        var splitInsertStr = insertString.split(',');
        list = [];

        globalJson.nodes.forEach(function (n) {
            if (n.id == splitInsertStr[0]) {
                list.push(n);
                var index = n.index;
                if (n.isGroupped == "True") {
                    var newObject = {
                        id: n.id,
                        isTagged: "True",
                        tagEntitydesc: splitInsertStr[2],
                        tagEntityCriticality: splitInsertStr[3],
                        entityName: splitInsertStr[1],
                        isGroupped: "True",
                        groupName: n.groupName,
                        groupDescription: n.groupDescription,
                        data: { OS: n.data.OS, allApps: n.data.allApps },
                        isIPAddressConfig: n.isIPAddressConfig,
                        isIPaddressAssignedToInfra: n.isIPaddressAssignedToInfra,
                        configInformation: n.configInformation,
                        index: n.index,
                        px: n.px,
                        py: n.py,
                        weight: n.weight,
                        x: n.x,
                        y: n.y
                    };
                    list.push(newObject);
                    $.extend(globalJson.nodes[index], newObject);
                    var jsonStr = convertObjectTOJsonFormat(globalJson);
                    var JsonObj = eval('(' + jsonStr + ')');
                    renderAfterTagging(JsonObj);

                    //renderAfterTagging(globalJson);
                }
                else if (n.isGroupped == "False") {
                    var newObject = {
                        id: n.id,
                        isTagged: "True",
                        tagEntitydesc: splitInsertStr[2],
                        tagEntityCriticality: splitInsertStr[3],
                        entityName: splitInsertStr[1],
                        isGroupped: "False",
                        data: { OS: n.data.OS, allApps: n.data.allApps },
                        isIPAddressConfig: n.isIPAddressConfig,
                        isIPaddressAssignedToInfra: n.isIPaddressAssignedToInfra,
                        configInformation: n.configInformation,
                        index: n.index,
                        px: n.px,
                        py: n.py,
                        weight: n.weight,
                        x: n.x,
                        y: n.y
                    };
                    list.push(newObject);
                    $.extend(globalJson.nodes[index], newObject);
                    var jsonStr = convertObjectTOJsonFormat(globalJson);
                    var JsonObj = eval('(' + jsonStr + ')');
                    renderAfterTagging(JsonObj);
                    //renderAfterTagging(globalJson);
                }
            }
        });
    }
}

//function insertDepMappingLinksInJsonNew(globalJson, srcTrgIPAddressList) {

//    if (globalJson != null) {

//        srcTrgIPAddressArray = [];

//        var newLinkObjectTobeInserted = "";

//        splitSrcTrgIPAddressArrayByPipeChar = srcTrgIPAddressList.split('|');

//        if (splitSrcTrgIPAddressArrayByPipeChar.length > 0) {

//            for (var i = 0; i < splitSrcTrgIPAddressArrayByPipeChar.length - 1; i++) {

//                var splitSrcTrgIPAddressArrayByComma = splitSrcTrgIPAddressArrayByPipeChar[i].split(',');

//                if (splitSrcTrgIPAddressArrayByComma.length > 0) {
//                    var trgNode = "";
//                    var sourceNode = "";


//                    data.nodes.forEach(function (L) {
//                        if (L.id == splitSrcTrgIPAddressArrayByComma[1])
//                            trgNode = L;
//                        if (L.id == splitSrcTrgIPAddressArrayByComma[0])
//                            sourceNode = L;
//                    });

//                    if (trgNode != "" && sourceNode != "") {

//                        if (trgNode.isTagged == "True" && sourceNode.isTagged == "True" && trgNode.isGroupped == "True" && sourceNode.isGroupped == "True") {
//                            newLinkObjectTobeInserted = {
//                                source: {
//                                    id: sourceNode.id,
//                                    isTagged: "True",
//                                    tagEntitydesc: sourceNode.tagEntitydesc,
//                                    tagEntityCriticality: sourceNode.tagEntityCriticality,
//                                    entityName: sourceNode.entityName,
//                                    isGroupped: "True",
//                                    groupName: sourceNode.groupName,
//                                    groupDescription: sourceNode.groupDescription,
//                                    data: { OS: sourceNode.data.OS, appApps: sourceNode.data.allApps },
//                                    index: sourceNode.index,
//                                    fixed: sourceNode.fixed,
//                                    isIPAddressConfig: sourceNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: sourceNode.isIPaddressAssignedToInfra,
//                                    configInformation: sourceNode.configInformation,
//                                    px: sourceNode.px,
//                                    py: sourceNode.py,
//                                    weight: sourceNode.weight,
//                                    x: sourceNode.x,
//                                    y: sourceNode.y
//                                },
//                                target: {
//                                    id: trgNode.id,
//                                    isTagged: "True",
//                                    tagEntitydesc: trgNode.tagEntitydesc,
//                                    tagEntityCriticality: trgNode.tagEntityCriticality,
//                                    entityName: trgNode.entityName,
//                                    isGroupped: "True",
//                                    groupName: trgNode.groupName,
//                                    groupDescription: trgNode.groupDescription,
//                                    data: { OS: trgNode.data.OS, appApps: trgNode.data.allApps },
//                                    index: trgNode.index,
//                                    fixed: trgNode.fixed,
//                                    isIPAddressConfig: trgNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: trgNode.isIPaddressAssignedToInfra,
//                                    configInformation: trgNode.configInformation,
//                                    px: trgNode.px,
//                                    py: trgNode.py,
//                                    weight: trgNode.weight,
//                                    x: trgNode.x,
//                                    y: trgNode.y
//                                }
//                            }
//                        }
//                        else if (trgNode.isTagged == "False" && sourceNode.isTagged == "False" && trgNode.isGroupped == "False" && sourceNode.isGroupped == "False") {
//                            newLinkObjectTobeInserted = {
//                                source: {
//                                    id: sourceNode.id,
//                                    isTagged: "False",
//                                    isGroupped: "False",
//                                    data: { OS: sourceNode.data.OS, appApps: sourceNode.data.allApps },
//                                    index: sourceNode.index,
//                                    fixed: sourceNode.fixed,
//                                    isIPAddressConfig: sourceNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: sourceNode.isIPaddressAssignedToInfra,
//                                    configInformation: sourceNode.configInformation,
//                                    px: sourceNode.px,
//                                    py: sourceNode.py,
//                                    weight: sourceNode.weight,
//                                    x: sourceNode.x,
//                                    y: sourceNode.y
//                                },
//                                target: {
//                                    id: trgNode.id,
//                                    isTagged: "False",
//                                    isGroupped: "False",
//                                    data: { OS: trgNode.data.OS, appApps: trgNode.data.allApps },
//                                    fixed: trgNode.fixed,
//                                    index: trgNode.index,
//                                    isIPAddressConfig: trgNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: trgNode.isIPaddressAssignedToInfra,
//                                    configInformation: trgNode.configInformation,
//                                    px: trgNode.px,
//                                    py: trgNode.py,
//                                    weight: trgNode.weight,
//                                    x: trgNode.x,
//                                    y: trgNode.y
//                                }
//                            }
//                        }
//                        else if (trgNode.isTagged == "True" && sourceNode.isTagged == "False" && trgNode.isGroupped == "True" && sourceNode.isGroupped == "False") {
//                            newLinkObjectTobeInserted = {
//                                source: {
//                                    id: sourceNode.id,
//                                    isTagged: "False",
//                                    isGroupped: "False",
//                                    data: { OS: sourceNode.data.OS, appApps: sourceNode.data.allApps },
//                                    index: sourceNode.index,
//                                    fixed: sourceNode.fixed,
//                                    isIPAddressConfig: sourceNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: sourceNode.isIPaddressAssignedToInfra,
//                                    configInformation: sourceNode.configInformation,
//                                    px: sourceNode.px,
//                                    py: sourceNode.py,
//                                    weight: sourceNode.weight,
//                                    x: sourceNode.x,
//                                    y: sourceNode.y
//                                },
//                                target: {
//                                    id: trgNode.id,
//                                    isTagged: "True",
//                                    tagEntitydesc: trgNode.tagEntitydesc,
//                                    tagEntityCriticality: trgNode.tagEntityCriticality,
//                                    entityName: trgNode.entityName,
//                                    isGroupped: "True",
//                                    groupName: trgNode.groupName,
//                                    groupDescription: trgNode.groupDescription,
//                                    data: { OS: trgNode.data.OS, appApps: trgNode.data.allApps },
//                                    index: trgNode.index,
//                                    fixed: trgNode.fixed,
//                                    isIPAddressConfig: trgNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: trgNode.isIPaddressAssignedToInfra,
//                                    configInformation: trgNode.configInformation,
//                                    px: trgNode.px,
//                                    py: trgNode.py,
//                                    weight: trgNode.weight,
//                                    x: trgNode.x,
//                                    y: trgNode.y
//                                }
//                            }
//                        }
//                        else if (trgNode.isTagged == "False" && sourceNode.isTagged == "True" && trgNode.isGroupped == "False" && sourceNode.isGroupped == "True") {
//                            newLinkObjectTobeInserted = {
//                                source: {
//                                    id: sourceNode.id,
//                                    isTagged: "True",
//                                    tagEntitydesc: sourceNode.tagEntitydesc,
//                                    tagEntityCriticality: sourceNode.tagEntityCriticality,
//                                    entityName: sourceNode.entityName,
//                                    isGroupped: "False",
//                                    data: { OS: sourceNode.data.OS, appApps: sourceNode.data.allApps },
//                                    index: sourceNode.index,
//                                    fixed: sourceNode.fixed,
//                                    isIPAddressConfig: sourceNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: sourceNode.isIPaddressAssignedToInfra,
//                                    configInformation: sourceNode.configInformation,
//                                    px: sourceNode.px,
//                                    py: sourceNode.py,
//                                    weight: sourceNode.weight,
//                                    x: sourceNode.x,
//                                    y: sourceNode.y
//                                },
//                                target: {
//                                    id: trgNode.id,
//                                    isTagged: "False",
//                                    isGroupped: "True",
//                                    groupName: trgNode.groupName,
//                                    groupDescription: trgNode.groupDescription,
//                                    data: { OS: trgNode.data.OS, appApps: trgNode.data.allApps },
//                                    fixed: trgNode.fixed,
//                                    index: trgNode.index,
//                                    isIPAddressConfig: trgNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: trgNode.isIPaddressAssignedToInfra,
//                                    configInformation: trgNode.configInformation,
//                                    px: trgNode.px,
//                                    py: trgNode.py,
//                                    weight: trgNode.weight,
//                                    x: trgNode.x,
//                                    y: trgNode.y
//                                }
//                            }
//                        }
//                        //Insert newLinkObjectTobeInserted into global Json.

//                        if (newLinkObjectTobeInserted != "") {

//                            var defaultIndex = -1;
//                            var linkIndex = 0;
//                            globalJson.links.forEach(function (link) {
//                                if ((link.source.id == newLinkObjectTobeInserted.source.id) && (link.target.id == newLinkObjectTobeInserted.target.id)) {
//                                    defaultIndex = linkIndex;
//                                }
//                                linkIndex = linkIndex + 1;
//                            });
//                            if (defaultIndex != -1)
//                                globalJson.links.splice(defaultIndex, 1);

//                            globalJson.links.push(newLinkObjectTobeInserted);
//                            newLinkObjectTobeInserted = "";
//                        }
//                    }
//                }
//            }
//        }
//    }
//}

//function deleteDepMappingLinksInJsonNew(globalJson, srcTrgIPAddressList) {

//    if (globalJson != null) {

//        splitSrcTrgIPAddressArrayByPipeChar = [];

//        var newLinkObjectTobeDeleted = "";

//        splitSrcTrgIPAddressArrayByPipeChar = srcTrgIPAddressList.split('|');

//        if (splitSrcTrgIPAddressArrayByPipeChar.length > 0) {

//            for (var i = 0; i < splitSrcTrgIPAddressArrayByPipeChar.length - 1; i++) {

//                var splitSrcTrgIPAddressArrayByComma = splitSrcTrgIPAddressArrayByPipeChar[i].split(',');

//                if (splitSrcTrgIPAddressArrayByComma.length > 0) {
//                    var trgNode = "";
//                    var sourceNode = "";


//                    data.nodes.forEach(function (L) {
//                        if (L.id == splitSrcTrgIPAddressArrayByComma[1])
//                            trgNode = L;
//                        if (L.id == splitSrcTrgIPAddressArrayByComma[0])
//                            sourceNode = L;
//                    });

//                    if (trgNode != "" && sourceNode != "") {

//                        if (trgNode.isTagged == "True" && sourceNode.isTagged == "True" && trgNode.isGroupped == "True" && sourceNode.isGroupped == "True") {
//                            newLinkObjectTobeDeleted = {
//                                source: {
//                                    id: sourceNode.id,
//                                    isTagged: "True",
//                                    tagEntitydesc: sourceNode.tagEntitydesc,
//                                    tagEntityCriticality: sourceNode.tagEntityCriticality,
//                                    entityName: sourceNode.entityName,
//                                    isGroupped: "True",
//                                    groupName: sourceNode.groupName,
//                                    groupDescription: sourceNode.groupDescription,
//                                    data: { OS: sourceNode.data.OS, appApps: sourceNode.data.allApps },
//                                    index: sourceNode.index,
//                                    fixed: sourceNode.fixed,
//                                    isIPAddressConfig: sourceNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: sourceNode.isIPaddressAssignedToInfra,
//                                    configInformation: sourceNode.configInformation,
//                                    px: sourceNode.px,
//                                    py: sourceNode.py,
//                                    weight: sourceNode.weight,
//                                    x: sourceNode.x,
//                                    y: sourceNode.y
//                                },
//                                target: {
//                                    id: trgNode.id,
//                                    isTagged: "True",
//                                    tagEntitydesc: trgNode.tagEntitydesc,
//                                    tagEntityCriticality: trgNode.tagEntityCriticality,
//                                    entityName: trgNode.entityName,
//                                    isGroupped: "True",
//                                    groupName: trgNode.groupName,
//                                    groupDescription: trgNode.groupDescription,
//                                    data: { OS: trgNode.data.OS, appApps: trgNode.data.allApps },
//                                    index: trgNode.index,
//                                    fixed: trgNode.fixed,
//                                    isIPAddressConfig: trgNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: trgNode.isIPaddressAssignedToInfra,
//                                    configInformation: trgNode.configInformation,
//                                    px: trgNode.px,
//                                    py: trgNode.py,
//                                    weight: trgNode.weight,
//                                    x: trgNode.x,
//                                    y: trgNode.y
//                                }
//                            }
//                        }
//                        else if (trgNode.isTagged == "False" && sourceNode.isTagged == "False" && trgNode.isGroupped == "False" && sourceNode.isGroupped == "False") {
//                            newLinkObjectTobeDeleted = {
//                                source: {
//                                    id: sourceNode.id,
//                                    isTagged: "False",
//                                    isGroupped: "False",
//                                    data: { OS: sourceNode.data.OS, appApps: sourceNode.data.allApps },
//                                    index: sourceNode.index,
//                                    fixed: sourceNode.fixed,
//                                    isIPAddressConfig: sourceNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: sourceNode.isIPaddressAssignedToInfra,
//                                    configInformation: sourceNode.configInformation,
//                                    px: sourceNode.px,
//                                    py: sourceNode.py,
//                                    weight: sourceNode.weight,
//                                    x: sourceNode.x,
//                                    y: sourceNode.y
//                                },
//                                target: {
//                                    id: trgNode.id,
//                                    isTagged: "False",
//                                    isGroupped: "False",
//                                    data: { OS: trgNode.data.OS, appApps: trgNode.data.allApps },
//                                    fixed: trgNode.fixed,
//                                    index: trgNode.index,
//                                    isIPAddressConfig: trgNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: trgNode.isIPaddressAssignedToInfra,
//                                    configInformation: trgNode.configInformation,
//                                    px: trgNode.px,
//                                    py: trgNode.py,
//                                    weight: trgNode.weight,
//                                    x: trgNode.x,
//                                    y: trgNode.y
//                                }
//                            }
//                        }
//                        else if (trgNode.isTagged == "True" && sourceNode.isTagged == "False" && trgNode.isGroupped == "True" && sourceNode.isGroupped == "False") {
//                            newLinkObjectTobeDeleted = {
//                                source: {
//                                    id: sourceNode.id,
//                                    isTagged: "False",
//                                    isGroupped: "False",
//                                    data: { OS: sourceNode.data.OS, appApps: sourceNode.data.allApps },
//                                    index: sourceNode.index,
//                                    fixed: sourceNode.fixed,
//                                    isIPAddressConfig: sourceNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: sourceNode.isIPaddressAssignedToInfra,
//                                    configInformation: sourceNode.configInformation,
//                                    px: sourceNode.px,
//                                    py: sourceNode.py,
//                                    weight: sourceNode.weight,
//                                    x: sourceNode.x,
//                                    y: sourceNode.y
//                                },
//                                target: {
//                                    id: trgNode.id,
//                                    isTagged: "True",
//                                    tagEntitydesc: trgNode.tagEntitydesc,
//                                    tagEntityCriticality: trgNode.tagEntityCriticality,
//                                    entityName: trgNode.entityName,
//                                    isGroupped: "True",
//                                    groupName: trgNode.groupName,
//                                    groupDescription: trgNode.groupDescription,
//                                    data: { OS: trgNode.data.OS, appApps: trgNode.data.allApps },
//                                    index: trgNode.index,
//                                    fixed: trgNode.fixed,
//                                    isIPAddressConfig: trgNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: trgNode.isIPaddressAssignedToInfra,
//                                    configInformation: trgNode.configInformation,
//                                    px: trgNode.px,
//                                    py: trgNode.py,
//                                    weight: trgNode.weight,
//                                    x: trgNode.x,
//                                    y: trgNode.y
//                                }
//                            }
//                        }
//                        else if (trgNode.isTagged == "False" && sourceNode.isTagged == "True" && trgNode.isGroupped == "False" && sourceNode.isGroupped == "True") {
//                            newLinkObjectTobeDeleted = {
//                                source: {
//                                    id: sourceNode.id,
//                                    isTagged: "True",
//                                    tagEntitydesc: sourceNode.tagEntitydesc,
//                                    tagEntityCriticality: sourceNode.tagEntityCriticality,
//                                    entityName: sourceNode.entityName,
//                                    isGroupped: "True",
//                                    groupName: sourceNode.groupName,
//                                    groupDescription: sourceNode.groupDescription,
//                                    data: { OS: sourceNode.data.OS, appApps: sourceNode.data.allApps },
//                                    index: sourceNode.index,
//                                    fixed: sourceNode.fixed,
//                                    isIPAddressConfig: sourceNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: sourceNode.isIPaddressAssignedToInfra,
//                                    configInformation: sourceNode.configInformation,
//                                    px: sourceNode.px,
//                                    py: sourceNode.py,
//                                    weight: sourceNode.weight,
//                                    x: sourceNode.x,
//                                    y: sourceNode.y
//                                },
//                                target: {
//                                    id: trgNode.id,
//                                    isTagged: "False",
//                                    isGroupped: "False",
//                                    data: { OS: trgNode.data.OS, appApps: trgNode.data.allApps },
//                                    fixed: trgNode.fixed,
//                                    index: trgNode.index,
//                                    isIPAddressConfig: trgNode.isIPAddressConfig,
//                                    isIPaddressAssignedToInfra: trgNode.isIPaddressAssignedToInfra,
//                                    configInformation: trgNode.configInformation,
//                                    px: trgNode.px,
//                                    py: trgNode.py,
//                                    weight: trgNode.weight,
//                                    x: trgNode.x,
//                                    y: trgNode.y
//                                }
//                            }
//                        }
//                        var defaultIndex = -1;
//                        var linkIndex = 0;
//                        globalJson.links.forEach(function (link) {
//                            if ((link.source.id == newLinkObjectTobeDeleted.source.id) && (link.target.id == newLinkObjectTobeDeleted.target.id)) {
//                                defaultIndex = linkIndex;
//                            }
//                            linkIndex = linkIndex + 1;
//                        });
//                        if (defaultIndex != -1)
//                            globalJson.links.splice(defaultIndex, 1);

//                        newLinkObjectTobeDeleted = "";
//                    }
//                }
//            }
//        }
//    }
//}

function renderAfterTagging(globalJson) {
    //alert(jQuery.type(globalJson));

    d3transform = $(".graph svg > g").attr("transform");
    $(".graph svg").remove();
    zoom = d3.behavior.zoom();
    zoom.on("zoom", onZoomChanged);


    layout = d3.layout.force()
      .gravity(.05)
      .charge(-300)
      .linkDistance(100);


    graph = d3.select(".graph")
      .append("svg:svg")
      .attr("pointer-events", "all")
      .call(zoom)
      .append('svg:g')
        .attr('width', graphWidth)
        .attr('height', graphHeight);

    d3.select(window).on("resize", resize);

    var graphData = globalJson;

    renderGraph(graphData);

    data = graphData;


    resize();



    $(".graph svg > g").removeAttr("transform");

    $(".graph svg > g").attr("transform", d3transform);

    $('.control-zoom ,.node-details').css("display", "block");


    $('.control-zoom a').on('click', onControlZoomClicked);
}

function AssignValue() {
    $("#ctl00_cphBody_hdnTagInfo").attr({ 'value': 'start|' });
    $("#ctl00_cphBody_hdnLinkInfo").attr({ 'value': 'start|' });
    $("#ctl00_cphBody_hdnDeleteLinkInfo").attr({ 'value': 'start|' });


    //$("#ctl00_cphBody_hdnAddedGrpNodesDetails").attr({ 'value': 'start|' });
    //$("#ctl00_cphBody_hdnRemovedGrpNodesDetails").attr({ 'value': 'start|' });

    $("#ctl00_cphBody_hdnAddedGrpNodesDetails").val("");
    $("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val("");

    $("#ctl00_cphBody_hdnMapList").val("");
    $("#ctl00_cphBody_hdnUnMapList").val("");


    newTempDeleteLinkInfoList = [];
    newTempLinkInfoList = [];
    // alert("cleared");


    GlobalAddGroupNodeList = [];
    GlobalRemoveGroupNodeList = [];

}

function union_arrays(x, y) {
    var obj = {};
    for (var i = x.length - 1; i >= 0; --i)
        obj[x[i]] = x[i];
    for (var i = y.length - 1; i >= 0; --i)
        obj[y[i]] = y[i];
    var res = []
    for (var k in obj) {
        if (obj.hasOwnProperty(k))  // <-- optional
            res.push(obj[k]);
    }
    return res;
}

function arr_diff(a1, a2) {
    var a = [], diff = [];
    for (var i = 0; i < a1.length; i++)
        a[a1[i]] = true;
    for (var i = 0; i < a2.length; i++)
        if (a[a2[i]]) delete a[a2[i]];
        else a[a2[i]] = true;
    for (var k in a)
        diff.push(k);
    return diff;
}

function convertObjectTOJsonFormat(JsonObject) {
    var returnStr = "";
    if (JsonObject != null) {
        var staticStr = "{" + jQuery.quoteString('directed') + ":" + JsonObject.directed + "," + jQuery.quoteString('multigraph') + ":" + JsonObject.multigraph + ",";

        var nodesStr = jQuery.quoteString('nodes') + ":" + "[";

        var linksStr = jQuery.quoteString('links') + ":" + "[";

        if (JsonObject.nodes.length > 0) {
            var i = 0;
            JsonObject.nodes.forEach(function (d) {
                if (d.isTagged == "False" && d.isGroupped == "False") {
                    var tempNode = "{" + jQuery.quoteString('isTagged') + ":" + '"' + d.isTagged + '"' + "," + jQuery.quoteString('isGroupped') + ":" + '"' + d.isGroupped + '"' + "," + jQuery.quoteString('id') + ":" + '"' + d.id + '"' + "," +
                        jQuery.quoteString('isIPAddressConfig') + ":" + '"' + d.isIPAddressConfig + '"' + "," + jQuery.quoteString('isIPaddressAssignedToInfra') + ":" +
                        '"' + d.isIPaddressAssignedToInfra + '"' + "," + jQuery.quoteString('configInformation') + ":" + '"' + d.configInformation + '"' + "," +
                        jQuery.quoteString('data') + ":" + "{" + jQuery.quoteString('OS') + ":" + '"' + d.data.OS + '"' + "," + jQuery.quoteString('allApps') + ":" +
                        '"' + d.data.allApps + '"' + "}" + "}";

                    if (i < JsonObject.nodes.length - 1) {
                        tempNode = tempNode + ",";
                    }
                    else if (i == JsonObject.nodes.length - 1) {
                        tempNode = tempNode + "]" + ",";
                    }
                    nodesStr = nodesStr + tempNode;
                    i = i + 1;
                }
                else if (d.isTagged == "True" && d.isGroupped == "True") {

                    var tempNode1 = "{" + jQuery.quoteString('isTagged') + ":" + '"' + d.isTagged + '"' + "," + jQuery.quoteString('isGroupped') + ":" + '"' + d.isGroupped + '"' + "," + jQuery.quoteString('id') + ":" + '"' + d.id + '"' + "," +
                        jQuery.quoteString('entityName') + ":" + '"' + d.entityName + '"' + "," + jQuery.quoteString('tagEntitydesc') + ":" + '"' + d.tagEntitydesc + '"' + "," + jQuery.quoteString('groupName') + ":" + '"' + d.groupName + '"' + "," + jQuery.quoteString('groupDescription') + ":" + '"' + d.groupDescription + '"' + "," +
                    jQuery.quoteString('tagEntityCriticality') + ":" + '"' + d.tagEntityCriticality + '"' + "," + jQuery.quoteString('isIPAddressConfig') + ":" + '"' +
                    d.isIPAddressConfig + '"' + "," + jQuery.quoteString('isIPaddressAssignedToInfra') + ":" + '"' + d.isIPaddressAssignedToInfra + '"' + "," +
                    jQuery.quoteString('configInformation') + ":" + '"' + d.configInformation + '"' + "," + jQuery.quoteString('data') + ":" + "{" + jQuery.quoteString('OS') + ":" + '"' + d.data.OS + '"' + "," + jQuery.quoteString('allApps') + ":" + '"' + d.data.allApps + '"' + "}" + "}";

                    if (i < JsonObject.nodes.length - 1) {
                        tempNode1 = tempNode1 + ",";
                    }
                    else if (i == JsonObject.nodes.length - 1) {
                        tempNode1 = tempNode1 + "]" + ",";
                    }
                    nodesStr = nodesStr + tempNode1;
                    i = i + 1;
                }
                else if (d.isTagged == "True" && d.isGroupped == "False") {

                    var tempNode1 =
                        "{" + jQuery.quoteString('isTagged') + ":" + '"' + d.isTagged + '"' + "," + jQuery.quoteString('isGroupped') + ":" + '"' + d.isGroupped + '"' + "," +
                        jQuery.quoteString('id') + ":" + '"' + d.id + '"' + "," + jQuery.quoteString('entityName') + ":" + '"' + d.entityName + '"' + "," +
                        jQuery.quoteString('tagEntitydesc') + ":" + '"' + d.tagEntitydesc + '"' + "," + jQuery.quoteString('tagEntityCriticality') + ":" + '"' + d.tagEntityCriticality + '"' + "," +
                        jQuery.quoteString('isIPAddressConfig') + ":" + '"' + d.isIPAddressConfig + '"' + "," + jQuery.quoteString('isIPaddressAssignedToInfra') + ":" + '"' + d.isIPaddressAssignedToInfra + '"' + "," +
                        jQuery.quoteString('configInformation') + ":" + '"' + d.configInformation + '"' + "," + jQuery.quoteString('data') + ":" + "{" + jQuery.quoteString('OS') + ":" + '"' + d.data.OS + '"' + "," + jQuery.quoteString('allApps') + ":" + '"' + d.data.allApps + '"' + "}" + "}";

                    if (i < JsonObject.nodes.length - 1) {
                        tempNode1 = tempNode1 + ",";
                    }
                    else if (i == JsonObject.nodes.length - 1) {
                        tempNode1 = tempNode1 + "]" + ",";
                    }
                    nodesStr = nodesStr + tempNode1;
                    i = i + 1;
                }
                else if (d.isTagged == "False" && d.isGroupped == "True") {

                    var tempNode1 = "{" + jQuery.quoteString('isTagged') + ":" + '"' + d.isTagged + '"' + "," + jQuery.quoteString('isGroupped') + ":" + '"' + d.isGroupped + '"' + "," + jQuery.quoteString('id') + ":" + '"' + d.id + '"' + "," +
                    jQuery.quoteString('groupName') + ":" + '"' + d.groupName + '"' + "," + jQuery.quoteString('groupDescription') + ":" + '"' + d.groupDescription + '"' + "," +
                    jQuery.quoteString('isIPAddressConfig') + ":" + '"' + d.isIPAddressConfig + '"' + "," + jQuery.quoteString('isIPaddressAssignedToInfra') + ":" + '"' + d.isIPaddressAssignedToInfra + '"' + "," +
                    jQuery.quoteString('configInformation') + ":" + '"' + d.configInformation + '"' + "," + jQuery.quoteString('data') + ":" + "{" + jQuery.quoteString('OS') + ":" + '"' + d.data.OS + '"' + "," + jQuery.quoteString('allApps') + ":" + '"' + d.data.allApps + '"' + "}" + "}";

                    if (i < JsonObject.nodes.length - 1) {
                        tempNode1 = tempNode1 + ",";
                    }
                    else if (i == JsonObject.nodes.length - 1) {
                        tempNode1 = tempNode1 + "]" + ",";
                    }
                    nodesStr = nodesStr + tempNode1;
                    i = i + 1;
                }
            });

            if (JsonObject.links.length > 0) {

                var j = 0;
                JsonObject.links.forEach(function (d) {
                    linksStr = linksStr + "{" + jQuery.quoteString('source') + ":" + d.source.index + "," + jQuery.quoteString('target') + ":" + d.target.index + "}";

                    if (j < JsonObject.links.length - 1) {
                        linksStr = linksStr + ",";
                    }
                    else if (j == JsonObject.links.length - 1) {
                        linksStr = linksStr + "]" + "}";
                    }

                    j = j + 1;
                });
                returnStr = staticStr + nodesStr + linksStr;
            }
            else {
                linksStr = jQuery.quoteString('links') + ":" + "[" + "]" + "}";
                returnStr = staticStr + nodesStr + linksStr;
            }
        }

    }

    return returnStr;
}

function sleep(milliseconds) {
    var start = new Date().getTime();
    for (var i = 0; i < 1e7; i++) {
        if ((new Date().getTime() - start) > milliseconds) {
            break;
        }
    }
}

function BubbleSortIP(listOfIPArray) {

    if (listOfIPArray.length > 0) {

        if (listOfIPArray.length == 1) {

        }
        else if (listOfIPArray.length > 1) {
            var length = listOfIPArray.length - 1;
            do {
                var swapped = false;

                for (var i = 0; i < length; ++i) {
                    var firstIp = listOfIPArray[i];

                    var secondIp = listOfIPArray[i + 1];

                    if (firstIp != secondIp) {

                        var splitFirstIp = firstIp.split('.');

                        var splitSecondIp = secondIp.split('.');

                        if (parseInt(splitFirstIp[0]) == parseInt(splitSecondIp[0])) {

                            if (parseInt(splitFirstIp[1]) == parseInt(splitSecondIp[1])) {

                                if (parseInt(splitFirstIp[2]) == parseInt(splitSecondIp[2])) {

                                    if (parseInt(splitFirstIp[3]) == parseInt(splitSecondIp[3])) {

                                    }
                                    else {
                                        if (parseInt(splitFirstIp[3]) > parseInt(splitSecondIp[3])) {

                                            var temp = firstIp;
                                            listOfIPArray[i] = secondIp;
                                            listOfIPArray[i + 1] = temp;
                                            swapped = true;
                                        }
                                    }
                                }
                                else {
                                    if (parseInt(splitFirstIp[2]) > parseInt(splitSecondIp[2])) {

                                        var temp = firstIp;
                                        listOfIPArray[i] = secondIp;
                                        listOfIPArray[i + 1] = temp;
                                        swapped = true;
                                    }
                                }
                            }
                            else {
                                if (parseInt(splitFirstIp[1]) > parseInt(splitSecondIp[1])) {

                                    var temp = firstIp;
                                    listOfIPArray[i] = secondIp;
                                    listOfIPArray[i + 1] = temp;
                                    swapped = true;
                                }
                            }
                        }
                        else {
                            if (parseInt(splitFirstIp[0]) > parseInt(splitSecondIp[0])) {

                                var temp = firstIp;
                                listOfIPArray[i] = secondIp;
                                listOfIPArray[i + 1] = temp;
                                swapped = true;
                            }
                        }
                    }
                }
            } while (swapped == true)
        }
    }
    return listOfIPArray;
}

$(".prop-tab li a").live("click", function (e) {
    var tabsactive = $(this).attr("href");
    $("#tabs-1,#tabs-2,#tabs-3").removeClass("show");
    $(tabsactive).addClass("show");
    $(".prop-tab li ").removeClass();
    $(this).parent().addClass("active");
    e.preventDefault();
});

$(document).ready(function () {


    $("a.close").live("click", function () {
        $('#tabs-1, #tabs-2,#tabs-3').html("");
        $('[id$=modelbg],[id$=pnlTagEntity],[id$=pnlTagPort1],[id$=pnlMapDependancy],[id$=Paneltaggroup],ul#ulchklist,ul#ulgrpchklist,#msgspnchklist,#msgspngroupchklist,[id$=pnlproperties]').css({ "display": "none" });
        $("[id$=txtEntityName]").val('').next('span').hide();
        $("[id$=txtDescription]").val('').next('span').hide();

        $("[id$=txtgrpnode]").val('').next('span').hide();
        $("[id$=txtgrpDescription]").val('').next('span').hide();


        $('[id$=ddlCriticality] option:eq(0)').attr("selected", "selected");
        $('[id$=ddlCriticality]').next('span').hide();
        $('.spnmessage').removeClass("show");
        $("ul#ulchklist").empty();
        $("ul#ulgrpchklist").empty();
        $("#ctl00_cphBody_hdnTempLinkInfo").val("")
        $("#ctl00_cphBody_hdnTempDeleteLinkInfo").val("")

    });

    $("ul#ulchklist, .nicescroll-rails").live("mouseleave", function () {

        $("ul#ulchklist").css({ "display": "none" });

    });

    $(".nicescroll-rails,ul#ulchklist").live("mouseover", function () {

        $("ul#ulchklist").css({ "display": "block" });

    });

    $("ul#ulgrpchklist, .nicescroll-rails").live("mouseleave", function () {

        $("ul#ulgrpchklist").css({ "display": "none" });

    });

    $(".nicescroll-rails,ul#ulgrpchklist").live("mouseover", function () {

        $("ul#ulgrpchklist").css({ "display": "block" });

    });

    context.init({ preventDoubleContext: false });

    $(document).on('mouseover', '.me-codesta', function () {
        $('.finale h1:first').css({ opacity: 0 });
        $('.finale h1:last').css({ opacity: 1 });
    });

    $(document).on('mouseout', '.me-codesta', function () {
        $('.finale h1:last').css({ opacity: 0 });
        $('.finale h1:first').css({ opacity: 1 });
    });

    //TagEntity OK button event.
    $("[id$=btnSave]").live("click", function (d) {
        var tagEntityStr, tagEntityDesStr, TagEntityCriticalityStr, SelectedText;

        if (($("[id$=txtEntityName]").val().trim().length != 0) && $("[id$=txtDescription]").val().trim().length != 0 && $("[id$=ddlCriticality]").val() > 0) {

            tagEntityStr = $("[id$=txtEntityName]").val();

            tagEntityDesStr = $("[id$=txtDescription]").val();

            TagEntityCriticalityStr = $("[id$=ddlCriticality]").val();

            if (TagEntityCriticalityStr > 0) {


                SelectedText = $("[id*='ddlCriticality'] :selected").text();
            }
            if (tagEntityStr != "undefined" && tagEntityDesStr != "undefined" && SelectedText != "undefined") {

                var finalDetails = clickedNodeId + "," + tagEntityStr + "," + tagEntityDesStr + "," + SelectedText + "|";

                var finalDetailsToPass = clickedNodeId + "," + tagEntityStr + "," + tagEntityDesStr + "," + SelectedText;

                insertTagEntityDetailsInJson(globalJson, finalDetailsToPass);

                var temp = $("#ctl00_cphBody_hdnTagInfo").val();  // Get

                $("#ctl00_cphBody_hdnTagInfo").val(temp + finalDetails);  // Set

                //localStorage.setItem(clickedNodeId, finalDetails);
                //for (var key in localStorage) {
                //    alert(localStorage.getItem(key));
                //}
            }
        }
    })

    $("[id$=btnDepMapSave]").live("click", function () {
        var tempLinkInfoFromHidden = "";
        var tempDeleteLinkInfoFromHidden = "";

        if (newTempLinkInfoList.length > 0) {
            newTempLinkInfoList.forEach(function (L) {
                if (tempLinkInfoFromHidden != "")
                    tempLinkInfoFromHidden = tempLinkInfoFromHidden + L;
                else
                    tempLinkInfoFromHidden = L;
            });
        }
        if (newTempDeleteLinkInfoList.length > 0) {
            newTempDeleteLinkInfoList.forEach(function (N) {
                if (tempDeleteLinkInfoFromHidden != "")
                    tempDeleteLinkInfoFromHidden = tempDeleteLinkInfoFromHidden + N;
                else
                    tempDeleteLinkInfoFromHidden = N;
            });
        }

        splitTempLinkInfoFromHiddenByPipeChar = [];
        splitTempLinkInfoFromHiddenByComma = [];

        splitTempDeleteLinkInfoFromHiddenByPipeChar = [];
        splitTempDeleteLinkInfoFromHiddenByComma = []

        maplinkList = [];
        ummaplinklist = [];

        if (tempLinkInfoFromHidden == "" && tempDeleteLinkInfoFromHidden == "") {

            $('[id$=modelbg]').css({ "display": "none" });
            $('[id$=pnlMapDependancy]').css({ "display": "none" });

            $('[id$="btnSaveDiscovery"]').attr('disabled', 'disabled');
            $('[id$="lblSaveMsg"]').hide();
        }
        else {
            if (tempLinkInfoFromHidden != "") {
                splitTempLinkInfoFromHiddenByPipeChar = tempLinkInfoFromHidden.split('|');

                if (splitTempLinkInfoFromHiddenByPipeChar.length > 0) {

                    for (var i = 0; i < splitTempLinkInfoFromHiddenByPipeChar.length - 1; i++) {

                        splitTempLinkInfoFromHiddenByComma = splitTempLinkInfoFromHiddenByPipeChar[i].split(',')

                        if (splitTempLinkInfoFromHiddenByComma.length > 0) {

                            var mapStr = splitTempLinkInfoFromHiddenByComma[1] + "," + splitTempLinkInfoFromHiddenByComma[2] + "|";

                            var index = maplinkList.indexOf(mapStr.trim());

                            if (index == -1) {
                                maplinkList.push(mapStr.trim());
                            }
                        }
                    }
                }
            }

            if (tempDeleteLinkInfoFromHidden != "") {

                splitTempDeleteLinkInfoFromHiddenByPipeChar = tempDeleteLinkInfoFromHidden.split('|');

                if (splitTempDeleteLinkInfoFromHiddenByPipeChar.length > 0) {
                    for (var j = 0; j < splitTempDeleteLinkInfoFromHiddenByPipeChar.length - 1 ; j++) {
                        splitTempDeleteLinkInfoFromHiddenByComma = splitTempDeleteLinkInfoFromHiddenByPipeChar[j].split(',');

                        var unmapStr = splitTempDeleteLinkInfoFromHiddenByComma[1] + "," + splitTempDeleteLinkInfoFromHiddenByComma[2] + "|";

                        var index = ummaplinklist.indexOf(unmapStr.trim());

                        if (index == -1) {
                            ummaplinklist.push(unmapStr.trim());
                        }
                    }
                }
            }
        }


        if (maplinkList.length > 0) {
            var maptrgIPAddressList = "";

            maplinkList.forEach(function (L) {

                if (maptrgIPAddressList != "") {
                    maptrgIPAddressList = maptrgIPAddressList + L;
                }
                else {
                    maptrgIPAddressList = L;
                }
            });
            if (maptrgIPAddressList != "") {
                // insertDepMappingLinksInJsonNew(globalJson, maptrgIPAddressList);
                UpdateJSONForAddRemoveLinks(globalJson, maptrgIPAddressList, "");
                $("#ctl00_cphBody_hdnTempLinkInfo").val("")
            }
        }


        if (ummaplinklist.length > 0) {

            var unmaptrgIPAddressList = "";

            ummaplinklist.forEach(function (L) {

                if (unmaptrgIPAddressList != "") {
                    unmaptrgIPAddressList = unmaptrgIPAddressList + L;
                }
                else {
                    unmaptrgIPAddressList = L;
                }
            });
            if (unmaptrgIPAddressList != "") {
                //deleteDepMappingLinksInJsonNew(globalJson, unmaptrgIPAddressList);
                UpdateJSONForAddRemoveLinks(globalJson, "", unmaptrgIPAddressList);
                $("#ctl00_cphBody_hdnTempDeleteLinkInfo").val("");
            }
        }

        passValueToSave();




        $('[id$=modelbg]').css({ "display": "none" });
        $('[id$=pnlMapDependancy]').css({ "display": "none" });


        //if (hdnIsSaveClicked == 1) {
        $('[id$="btnSaveDiscovery"]').removeAttr('disabled');
        $('[id$="lblSaveMsg"]').hide();

        // }
        data = globalJson;
        var Json2 = convertObjectTOJsonFormat(globalJson);
        var json3 = eval('(' + Json2 + ')');
        renderAfterTagging(json3);

        //dependoGraph(globalJson, "false");


        $("#ctl00_cphBody_hdnTempLinkInfo").val("")
        $("#ctl00_cphBody_hdnTempDeleteLinkInfo").val("");

    });

    $("[id$=txtEntityName], [id$=txtDescription]").live("blur", function () {
        RequireField($(this).attr("id"));
        $(this).next('span').addClass("pull-right");
    });

    $("[id$=ddlCriticality]").live("blur", function () {
        RequireDropDown($(this).attr("id"));
        $(this).next('span').addClass("pull-right");
    });

    $("[id$=txtgrpnode],[id$=txtgrpDescription]").live("blur", function () {
        RequireField($(this).attr("id"));
        $(this).next('span').addClass("pull-right")
    })

    //TagEntity OK button event.
    $("[id$=btnSave]").live("click", function () {
        var state = 'enabled';
        $("[id$=txtEntityName], [id$=txtDescription],[id$=ddlCriticality]").trigger("blur");

        if ($("[id$=txtEntityName]").val().length != 0 && $("[id$=txtDescription]").val().length != 0 && $("[id$=ddlCriticality]").val() > 0) {
            //$('.spnmessage').addClass("show");
            $('[id$=modelbg]').css({ "display": "none" });
            $('[id$=pnlTagEntity]').css({ "display": "none" });
            //if (hdnIsSaveClicked == 1) {
            $('[id$="btnSaveDiscovery"]').removeAttr('disabled');
            $('[id$="lblSaveMsg"]').hide();
            // }
        }
        else
            $('.spnmessage').addClass("hide");
    });

    $("[id$=btnSaveGroupNodes]").live("click", function () {
        var clickedNodeObj;
        var flag = false;
        $("#lblError").attr("style", "display:none");
        if (grpNodeMenuText == "Create Group Nodes") {
            var tem = $("ul#ulgrpchklist input[type=checkbox]:checked");
            if (tem != undefined && tem.length > 0) {
                flag = true;
            }
        }
        else
            flag = true;


        $("[id$=txtgrpnode], [id$=txtgrpDescription]").trigger("blur");
        if (flag == false) {
            $('[id$=msgspngroupchklist]').css({ "display": "block" });
        }


        data.nodes.forEach(function (n) {
            if (n.id == clickedNodeId)
                clickedNodeObj = n;
        });


        if ($("[id$=txtgrpnode]").val().length != 0 && $("[id$=txtgrpDescription]").val().length != 0 && flag == true) {

            ishdnGroupChange = 1;

            var nodestring = "";
            var Isnodefound = false;
            var updatedNodeString = "";

            if (newTempAddedGrpNodesDetails.length > 0) {

                newTempAddedGrpNodesDetails.forEach(function (L, index) {
                    nodestring += L + ",";
                });

                var tempVal = $("#ctl00_cphBody_hdnAddedGrpNodesDetails").val();


                if (tempVal != "") {

                    var splitArray = tempVal.split("|");

                    if (splitArray.length > 0) {

                        for (j = 0; j < splitArray.length - 1; j++) {
                            if (splitArray[j].split(",")[1] == clickedNodeObj.groupName) {

                                Isnodefound = true;

                                updatedNodeString += splitArray[j] + ',' + nodestring.substr(0, nodestring.length - 1) + "|";
                            }
                            else
                                updatedNodeString += splitArray[j] + "|";

                        }
                    }
                    if (!Isnodefound)
                        updatedNodeString = tempVal + clickedNodeId + "," + $("[id$=txtgrpnode]").val() + "," + $("[id$=txtgrpDescription]").val() + "," + $("#ctl00_cphBody_hdnDepProfileName").val() + "," + nodestring.substr(0, nodestring.length - 1) + "," + clickedNodeId + "|";

                    $("#ctl00_cphBody_hdnAddedGrpNodesDetails").val(updatedNodeString);
                }
                else
                    if (nodestring != "")
                        $("#ctl00_cphBody_hdnAddedGrpNodesDetails").val(clickedNodeId + "," + $("[id$=txtgrpnode]").val() + "," + $("[id$=txtgrpDescription]").val() + "," + $("#ctl00_cphBody_hdnDepProfileName").val() + "," + nodestring.substr(0, nodestring.length - 1) + "," + clickedNodeId + "|");

                newTempAddedGrpNodesDetails = [];//clear array.

            }
            $('[id$=modelbg]').css({ "display": "none" });
            $('[id$=Paneltaggroup]').css({ "display": "none" });


        } else
            $('.spnmessage').addClass("hide");


        // alert($("#ctl00_cphBody_hdnAddedGrpNodesDetails").val());

        UpdateCheckboxRemovenodelist(clickedNodeObj);

        if (ishdnGroupChange == 1)
            $('[id$="btnSaveDiscovery"]').removeAttr('disabled');

        UpdateJSONForAddRemoveGroup();

        //insertGroupNodeDetails(globalJson, passHdnValueToJson.substr(0, passHdnValueToJson.val().length - 1));



    });

    $("[id$=btnFindDependency]").live("click", function () {
        $("#ctl00_cphBody_hdnTagInfo").val("start|");
        $("#ctl00_cphBody_hdnLinkInfo").val("start|");
        $("#ctl00_cphBody_hdnDeleteLinkInfo").val("start|");
        $("#ctl00_cphBody_hdnAddedGrpNodesDetails").val("");
        $("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val("");

    });

    $("#btnchklist").live("click", function () {
        $("ul#ulchklist").toggle();


        if ($("ul#ulchklist li").length < 2) {
            $("ul#ulchklist").css("overflow-y", "hidden");
        } else {

            $("ul#ulchklist").niceScroll({ touchbehavior: true, cursordragontouch: true, cursorcolor: "#4a8bc2", cursoropacitymax: 1, scrollspeed: 5, mousescrollstep: 5, cursorwidth: 8, autohidemode: false, horizrailenabled: true });
        }

    });

    $("#btngroupchklist").live("click", function () {
        $("ul#ulgrpchklist").toggle();


        if ($("ul#ulgrpchklist li").length < 2) {
            $("ul#ulgrpchklist").css("overflow-y", "hidden");
        } else {

            $("ul#ulgrpchklist").niceScroll({ touchbehavior: true, cursordragontouch: true, cursorcolor: "#4a8bc2", cursoropacitymax: 1, scrollspeed: 5, mousescrollstep: 5, cursorwidth: 8, autohidemode: false, horizrailenabled: true });
        }

    });

    $("ul#ulchklist input[type=checkbox]").live("click", function () {
        if ($(this).is(':checked')) {
            $(this).parents("li").children("label").attr("class", "text-success");
            //sleep(500);

            var currentdate = new Date();
            var datetime = currentdate.getDate() + "-" + (currentdate.getMonth() + 1) + "-" + currentdate.getFullYear() + " " + currentdate.getHours() + ":" + currentdate.getMinutes() + ":" + currentdate.getSeconds() + "." + currentdate.getMilliseconds();

            var ipAddressList = "";

            splitTempLinkInfoFromHiddenByPipeChar1 = [];
            splitTempLinkInfoFromHiddenByComma1 = []

            if ($(this).val() != "undefined" || $(this).val() != "") {
                ipAddressList = $(this).val();
            }

            if (ipAddressList != "") {

                var linkDetail = datetime + "," + clickedNodeId + "," + ipAddressList + "|";

                var temp1 = $("#ctl00_cphBody_hdnTempLinkInfo").val();  // Get

                if (temp1 != "") {

                    splitTempDeleteLinkInfoFromHiddenByPipeChar1 = temp1.split('|');

                    if (splitTempDeleteLinkInfoFromHiddenByPipeChar1.length > 0) {

                        if (splitTempDeleteLinkInfoFromHiddenByPipeChar1.length > 1) {
                            for (var i = 0; i < splitTempDeleteLinkInfoFromHiddenByPipeChar1.length - 1; i++) {

                                splitTempDeleteLinkInfoFromHiddenByComma1 = splitTempDeleteLinkInfoFromHiddenByPipeChar1[i].split(',')

                                if (splitTempDeleteLinkInfoFromHiddenByComma1.length > 0) {

                                    if ((splitTempDeleteLinkInfoFromHiddenByComma1[1] == clickedNodeId) && (splitTempDeleteLinkInfoFromHiddenByComma1[2] == ipAddressList)) {

                                        if (newTempLinkInfoList.length > 0) {

                                            newTempLinkInfoList.forEach(function (L, index) {

                                                var splitL = L.split(',');
                                                if (splitL.length > 0) {
                                                    if ((splitL[2] == ipAddressList + "|") && (splitL[1] == clickedNodeId)) {
                                                        newTempLinkInfoList.splice(index, 1);
                                                        newTempLinkInfoList.push(linkDetail);
                                                    }
                                                    else if (index == newTempLinkInfoList.length - 1) {
                                                        newTempLinkInfoList.push(linkDetail);
                                                    }
                                                }
                                            });
                                        }
                                        else {
                                            newTempLinkInfoList.push(linkDetail);
                                        }
                                    }
                                    else {
                                        if (newTempLinkInfoList.length > 0) {



                                            newTempLinkInfoList.forEach(function (L, index) {

                                                var splitL = L.split(',');
                                                if (splitL.length > 0) {
                                                    if ((splitL[2] == ipAddressList + "|") && (splitL[1] == clickedNodeId)) {
                                                        newTempLinkInfoList.splice(index, 1);
                                                        newTempLinkInfoList.push(linkDetail);
                                                    }
                                                    else {
                                                        if (index == newTempLinkInfoList.length - 1) {
                                                            newTempLinkInfoList.push(linkDetail);
                                                        }
                                                    }

                                                }
                                            });
                                        }
                                        else {
                                            newTempLinkInfoList.push(linkDetail);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $("#ctl00_cphBody_hdnTempLinkInfo").val(temp1 + linkDetail);
                }
                else {
                    $("#ctl00_cphBody_hdnTempLinkInfo").val(linkDetail);

                    if (newTempLinkInfoList.length > 0) {
                        newTempLinkInfoList.forEach(function (L, index) {

                            var splitL = L.split(',');
                            if (splitL.length > 0) {
                                if ((splitL[2] == ipAddressList + "|") && (splitL[1] == clickedNodeId)) {
                                    newTempLinkInfoList.splice(index, 1);
                                    newTempLinkInfoList.push(linkDetail);
                                }
                                else {
                                    if (index == newTempLinkInfoList.length - 1) {
                                        newTempLinkInfoList.push(linkDetail);
                                    }
                                }

                            }
                        });
                    }
                    else {
                        newTempLinkInfoList.push(linkDetail);
                    }

                }

                var temp = $("#ctl00_cphBody_hdnLinkInfo").val();
                $("#ctl00_cphBody_hdnLinkInfo").val(temp + linkDetail);
            }


            if (newTempDeleteLinkInfoList.length > 0) {

                newTempDeleteLinkInfoList.forEach(function (L, index) {

                    var splitL = L.split(',');
                    if (splitL.length > 0) {
                        if ((splitL[2] == ipAddressList + "|") && (splitL[1] == clickedNodeId)) {
                            newTempDeleteLinkInfoList.splice(index, 1);
                        }
                    }
                });
            }
        }

        else {
            //sleep(500);
            $(this).parents("li").children("label").removeAttr("class");
            var currentdate = new Date();
            var datetime = currentdate.getDate() + "-" + (currentdate.getMonth() + 1) + "-" + currentdate.getFullYear() + " " + currentdate.getHours() + ":" + currentdate.getMinutes() + ":" + currentdate.getSeconds() + "." + currentdate.getMilliseconds()

            var ipAddressList = "";

            splitTempDeleteLinkInfoFromHiddenByPipeChar1 = [];
            splitTempDeleteLinkInfoFromHiddenByComma1 = []


            if ($(this).val() != "undefined" || $(this).val() != "") {
                ipAddressList = $(this).val();
            }

            if (ipAddressList != "") {

                var linkDetail = datetime + "," + clickedNodeId + "," + ipAddressList + "|";

                var temp1 = $("#ctl00_cphBody_hdnTempDeleteLinkInfo").val();  // Get

                if (temp1 != "") {

                    splitTempDeleteLinkInfoFromHiddenByPipeChar1 = temp1.split('|');

                    if (splitTempDeleteLinkInfoFromHiddenByPipeChar1.length > 0) {

                        if (splitTempDeleteLinkInfoFromHiddenByPipeChar1.length > 1) {
                            for (var i = 0; i < splitTempDeleteLinkInfoFromHiddenByPipeChar1.length - 1; i++) {

                                splitTempDeleteLinkInfoFromHiddenByComma1 = splitTempDeleteLinkInfoFromHiddenByPipeChar1[i].split(',')

                                if (splitTempDeleteLinkInfoFromHiddenByComma1.length > 0) {

                                    if ((splitTempDeleteLinkInfoFromHiddenByComma1[1] == clickedNodeId) && (splitTempDeleteLinkInfoFromHiddenByComma1[2] == ipAddressList)) {

                                        if (newTempDeleteLinkInfoList.length > 0) {

                                            newTempDeleteLinkInfoList.forEach(function (L, index) {

                                                var splitL = L.split(',');
                                                if (splitL.length > 0) {
                                                    if ((splitL[2] == ipAddressList + "|") && (splitL[1] == clickedNodeId)) {
                                                        newTempDeleteLinkInfoList.splice(index, 1);
                                                        newTempDeleteLinkInfoList.push(linkDetail);
                                                    }
                                                    else if (index == newTempDeleteLinkInfoList.length - 1) {
                                                        newTempDeleteLinkInfoList.push(linkDetail);
                                                    }
                                                }
                                            });
                                        }
                                        else {
                                            newTempDeleteLinkInfoList.push(linkDetail);
                                        }
                                    }
                                    else {
                                        if (newTempDeleteLinkInfoList.length > 0) {


                                            newTempDeleteLinkInfoList.forEach(function (L, index) {
                                                var splitL = L.split(',');
                                                if (splitL.length > 0) {
                                                    if ((splitL[2] == ipAddressList + "|") && (splitL[1] == clickedNodeId)) {
                                                        newTempDeleteLinkInfoList.splice(index, 1);
                                                        newTempDeleteLinkInfoList.push(linkDetail);
                                                    }
                                                    else {
                                                        if (index == newTempDeleteLinkInfoList.length - 1) {
                                                            newTempDeleteLinkInfoList.push(linkDetail);
                                                        }
                                                    }

                                                }
                                            });
                                        }
                                        else {
                                            newTempDeleteLinkInfoList.push(linkDetail);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $("#ctl00_cphBody_hdnTempDeleteLinkInfo").val(temp1 + linkDetail);
                }
                else {
                    $("#ctl00_cphBody_hdnTempDeleteLinkInfo").val(linkDetail);

                    if (newTempDeleteLinkInfoList.length > 0) {
                        newTempDeleteLinkInfoList.forEach(function (L, index) {

                            var splitL = L.split(',');
                            if (splitL.length > 0) {
                                if ((splitL[2] == ipAddressList + "|") && (splitL[1] == clickedNodeId)) {
                                    newTempDeleteLinkInfoList.splice(index, 1);
                                    newTempDeleteLinkInfoList.push(linkDetail);
                                }
                                else {
                                    if (index == newTempDeleteLinkInfoList.length - 1) {
                                        newTempDeleteLinkInfoList.push(linkDetail);
                                    }
                                }

                            }
                        });
                    }
                    else {
                        newTempDeleteLinkInfoList.push(linkDetail);
                    }
                }

                var temp = $("#ctl00_cphBody_hdnDeleteLinkInfo").val();  // Get

                $("#ctl00_cphBody_hdnDeleteLinkInfo").val(temp + linkDetail);  // Set
            }


            if (newTempLinkInfoList.length > 0) {

                newTempLinkInfoList.forEach(function (L, index) {

                    var splitL = L.split(',');
                    if (splitL.length > 0) {
                        if ((splitL[2] == ipAddressList + "|") && (splitL[1] == clickedNodeId)) {
                            newTempLinkInfoList.splice(index, 1);
                        }
                    }
                });
            }
        }
        var selectedIp = "";
        for (var e = 0; e < $("ul#ulchklist input[type=checkbox]").length; e++) {
            var chkBox = $("ul#ulchklist input[type=checkbox]")[e];
            if (chkBox.checked == true)
                selectedIp += chkBox.value.split('.')[3] + ",";
        }
        if (selectedIp != "")
            $("#btnchklist").val(selectedIp.substring(0, selectedIp.length - 1));
        else
            $("#btnchklist").val(" - Select IP - ");

        $('[id$="btnDepMapSave"]').removeAttr('disabled');
    });

    function passValueToSave() {

        if (newTempLinkInfoList.length > 0) {

            $("#ctl00_cphBody_hdnMapList").val("");

            newTempLinkInfoList.forEach(function (L) {

                var linkDetail = L;

                var temp1 = $("#ctl00_cphBody_hdnMapList").val();
                if (temp1 != "")
                    $("#ctl00_cphBody_hdnMapList").val(temp1 + linkDetail);
                else
                    $("#ctl00_cphBody_hdnMapList").val(linkDetail);
            });
        }

        if (newTempDeleteLinkInfoList.length > 0) {

            $("#ctl00_cphBody_hdnUnMapList").val("");

            newTempDeleteLinkInfoList.forEach(function (L) {

                var linkDetail = L;

                var temp1 = $("#ctl00_cphBody_hdnUnMapList").val();
                if (temp1 != "")
                    $("#ctl00_cphBody_hdnUnMapList").val(temp1 + linkDetail);
                else
                    $("#ctl00_cphBody_hdnUnMapList").val(linkDetail);
            });
        }
    }

    $("ul#ulgrpchklist input[type=checkbox]").live("click", function () {

        if ($(this).is(':checked')) {
            $(this).parents("li").children("label").attr("class", "text-success");
            $('[id$=msgspngroupchklist]').css({ "display": "none" });

            if ($(this).val() != "undefined" || $(this).val() != "") {

                var IpAdded = $(this).val();
                if (newTempAddedGrpNodesDetails.indexOf(IpAdded) == -1) {
                    newTempAddedGrpNodesDetails.push(IpAdded);
                }
                if (GlobalAddGroupNodeList.indexOf(IpAdded) == -1) {
                    GlobalAddGroupNodeList.push(IpAdded);
                }


                if (newTempremovedGrpNodesDetails.length > 0) {

                    newTempremovedGrpNodesDetails.forEach(function (L, index) {
                        if (L.trim() == IpAdded.trim()) {
                            newTempremovedGrpNodesDetails.splice(index, 1);
                        }
                    });

                    //newTempremovedGrpNodesDetails.splice(newTempremovedGrpNodesDetails.indexOf(IpAdded));
                }

                if (GlobalRemoveGroupNodeList.length > 0) {

                    GlobalRemoveGroupNodeList.forEach(function (L, index) {
                        if (L.trim() == IpAdded.trim()) {
                            GlobalRemoveGroupNodeList.splice(index, 1);
                        }
                    });

                }
            }
        }
        else {

            $(this).parents("li").children("label").removeAttr("class");

            var IpRemoved = $(this).val();

            if (newTempAddedGrpNodesDetails.indexOf(IpRemoved) != -1) {
                newTempAddedGrpNodesDetails.splice(newTempAddedGrpNodesDetails.indexOf(IpRemoved));
            }

            if (GlobalAddGroupNodeList.indexOf(IpRemoved) != -1) {
                GlobalAddGroupNodeList.splice(GlobalAddGroupNodeList.indexOf(IpRemoved));
            }

            if (newTempremovedGrpNodesDetails.indexOf(IpRemoved) == -1) {
                newTempremovedGrpNodesDetails.push(IpRemoved);
            }
            if (GlobalRemoveGroupNodeList.indexOf(IpRemoved) == -1) {
                GlobalRemoveGroupNodeList.push(IpRemoved);
            }

        }

        var selectedIp = "";
        for (var e = 0; e < $("ul#ulgrpchklist input[type=checkbox]").length; e++) {
            var chkBox = $("ul#ulgrpchklist input[type=checkbox]")[e];
            if (chkBox.checked == true)
                selectedIp += chkBox.value.split('.')[3] + ",";
        }
        if (selectedIp != "")
            $("#btngroupchklist").val(selectedIp.substring(0, selectedIp.length - 1));
        else
            $("#btngroupchklist").val(" - Select Nodes - ");
    })

});


function UpdateCheckboxRemovenodelist(clickedNodeObj) {
    var removenodestr = "";
    var nodestring = "";
    var Isnodefound = false;
    if (newTempremovedGrpNodesDetails.length > 0) {

        newTempremovedGrpNodesDetails.forEach(function (L, index) {
            nodestring += L + ",";
        });
    }
    var tempVal = $("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val();

    if (tempVal != "") {

        ishdnGroupChange = 1;

        var splitArray = tempVal.split("|");

        if (splitArray.length > 0) {

            for (j = 0; j < splitArray.length - 1; j++) {
                if (splitArray[j].split(",")[1] == clickedNodeObj.groupName) {

                    Isnodefound = true;

                    var newstring = clickedNodeId + "," + $("[id$=txtgrpnode]").val() + "," + $("[id$=txtgrpDescription]").val() + "," + $("#ctl00_cphBody_hdnDepProfileName").val() + "," + nodestring.substr(0, nodestring.length - 1);

                    removenodestr += newstring + "|";
                }
                else
                    removenodestr += splitArray[j] + "|";
            }
        }
        if (!Isnodefound) {
            removenodestr = tempVal + clickedNodeId + "," + $("[id$=txtgrpnode]").val() + "," + $("[id$=txtgrpDescription]").val() + "," + $("#ctl00_cphBody_hdnDepProfileName").val() + "," + nodestring.substr(0, nodestring.length - 1) + "|";
        }

        $("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val(removenodestr);
    }
    else
        if (nodestring != "")
            $("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val(clickedNodeId + "," + $("[id$=txtgrpnode]").val() + "," + $("[id$=txtgrpDescription]").val() + "," + $("#ctl00_cphBody_hdnDepProfileName").val() + "," + nodestring.substr(0, nodestring.length - 1) + "|");


    //alert($("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val());

    //var passHdnValueToJson = $("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val();
    //alert($("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val());

    newTempremovedGrpNodesDetails = [];//clear array.
}

function UpdateJSONForAddRemoveGroup() {
    var newaddnodes = $("#ctl00_cphBody_hdnAddedGrpNodesDetails").val();

    if (newaddnodes != "") {
        var splitArray = newaddnodes.split("|");
        for (var i = 0; i < splitArray.length - 1; i++) {
            var dependentNode = splitArray[i].split(',');//[3].split(',');

            for (var j = 4; j < dependentNode.length; j++) {
                globalJson.nodes.forEach(function (n) {
                    if (n.id == dependentNode[j]) {
                        n.isGroupped = "True";
                        n.groupName = splitArray[i].split(',')[1];
                        n.groupDescription = splitArray[i].split(',')[2];
                    }

                });
            }

        }
    }

    //update for Remove group

    var removenodegrp = $("#ctl00_cphBody_hdnRemovedGrpNodesDetails").val();

    if (removenodegrp != "") {
        var splitArray = removenodegrp.split("|");
        for (var i = 0; i < splitArray.length - 1; i++) {
            var dependentNode = splitArray[i].split(',');//[3].split(',');

            for (var j = 4; j < dependentNode.length; j++) {
                globalJson.nodes.forEach(function (n) {
                    if (n.id == dependentNode[j] && n.groupName != undefined && n.groupName == dependentNode[1]) {
                        n.isGroupped = "False";
                        delete n.groupName;
                        delete n.groupDescription;
                    }

                });
            }

        }

    }
    dependoGraph(globalJson, "False");

}

function UpdateJSONForAddRemoveLinks(globalJson, srcTrgAddedIPAddressList, srcTrgRemovedIPAddressList) {

    if (srcTrgAddedIPAddressList != "") {
        var splitArrayAdded = srcTrgAddedIPAddressList.split("|");

        //Add dependancy
        for (var j = 0; j < splitArrayAdded.length - 1; j++) {

            var trgNode = ""; var srcNode = " ";

            var addedLink = splitArrayAdded[j].split(',');

            if (addedLink.length > 0) {
                srcNode = addedLink[0];
                trgNode = addedLink[1];

                if (srcNode != "" && trgNode != "") {


                    var sourceNode;
                    var targteNode;

                    globalJson.nodes.forEach(function (node) {
                        if (node.id == srcNode) {
                            sourceNode = node;
                        }
                        if (node.id == trgNode) {
                            targteNode = node;
                        }

                    });
                    var linkFound = false;

                    globalJson.links.forEach(function (l) {
                        if (l.source.id == sourceNode.id && l.target.id == targteNode.id)
                            linkFound = true;

                    });
                    if (!linkFound)
                        globalJson.links.push({ "source": sourceNode, "target": targteNode });

                }
            }
        }

    }
    if (srcTrgRemovedIPAddressList != "") {

        var splitArrayRemove = srcTrgRemovedIPAddressList.split("|");

        //Remove dependancy
        for (var j = 0; j < splitArrayRemove.length - 1; j++) {

            var trgNode = ""; var srcNode = " ";

            var removedLink = splitArrayRemove[j].split(',');

            if (removedLink.length > 0) {
                srcNode = removedLink[0];
                trgNode = removedLink[1];

                if (srcNode != "" && trgNode != "") {

                    var sourceNode;
                    var targteNode;

                    globalJson.nodes.forEach(function (node) {
                        if (node.id == srcNode) {
                            sourceNode = node;
                        }
                        if (node.id == trgNode) {
                            targteNode = node;
                        }
                    });

                    var defaultIndex = 0;

                    globalJson.links.forEach(function (l) {
                        if (l.source.id == sourceNode.id && l.target.id == targteNode.id)
                            globalJson.links.splice(defaultIndex, 1);
                        defaultIndex++;

                    });

                }
            }
        }
    }
    //dependoGraph(convertObjectTOJsonFormat(data));

}



//// New Dev 17/01/2017 Niteen M. Tag Port
function GetServerport(ip) {
    $(".notifyscrolls").animate({
        scrollTop: 0
    });

    $("#spnIP").text(ip);
    Ip = ip;
    var allAppstr = "";
    $(".dynamicTable tr").remove();
    if (profileName == undefined)
        profileName = $('#ctl00_cphBody_ddlAppDepProfiles ').val();

    globalJson.nodes.forEach(function (n) {
        if (n.id == Ip) {
            allAppstr = n.data.allApps;
        }
    });

    if (allAppstr.d != "" && allAppstr != undefined) {
        var portIpArr = allAppstr.split(',');
        $(".dynamicTable").append("<tr><th style='width: 25%;'>Port No </th><th style='width: 65%;'>Port Name </th><th style='width: 5%;'> Action </th><th style='width: 5%;'> </th></tr>");
        for (var t = 0; t < portIpArr.length; t++) {
            if (portIpArr[t].indexOf('(') > -1) {
                var portName = portIpArr[t].split('(')[0];
                var portID = portIpArr[t].match(/\((.*)\)/);
                $(".dynamicTable tr:last").after("<tr> <td contenteditable='false' style='width: 25%; border-width: 0px ! important;' class='porticon_new'>" + portID[1] + "</td><td contenteditable='false' style='width: 65%; border-right-color: transparent; border-width: 0px ! important; position:relative' class='name_icon'> <span class='lblportn'> " + portName + "</span> </td><td contenteditable='false' class='dd' style='width: 5%; border-width: 0px;'><button class='editbtn'></button></td><td class='dd' style='width: 5%; border-width: 0px;' onclick=deleteport(this)> <span class='delete_icon'></span></td></tr>");
            }else
                $(".dynamicTable tr:last").after("<tr> <td contenteditable='false' style='width: 25%; border-width: 0px ! important;' class='porticon_new'>" + "NA" + "</td><td contenteditable='false' style='width: 65%; border-right-color: transparent; border-width: 0px ! important; position:relative' class='name_icon'> <span class='lblportn'> " + portIpArr[t] + "</span> </td><td contenteditable='false' class='dd' style='width: 5%; border-width: 0px;'><button class='editbtn'></button></td><td class='dd' style='width: 5%; border-width: 0px;' onclick=deleteport(this)> <span class='delete_icon'></span></td></tr>");

        }

        $('[id$=modelbg]').css({ "display": "block" });
        $('[id$=pnlTagPort1]').css({ "display": "block" });
    }

    var heightcheck = ($(".changetblstruc").height());
    if (heightcheck >= 126) {
        $('#ctl00_cphBody_ddlStandardsPort').css('left', '79px');
    }
    else {
        $('#ctl00_cphBody_ddlStandardsPort').css('left', '84px');
    }




}

// on Ok  maintain unique list for Apps
function UpdateAllAppData() {
    var allAppstr = "";
    var i = 0;
    $(".dynamicTable tr").each(function () {
        if (i != 0 && $(this).find('td.name_icon').text().trim() != "")
            allAppstr += $(this).find('td.name_icon').text().trim() + "(" + $(this).find('td.porticon_new').text().trim() + "),";
        i++;
    });

    if (allAppstr != "")
        allAppstr = allAppstr.substr(0, allAppstr.length - 1);

    globalJson.nodes.forEach(function (n) {
        if (n.id == Ip) {
            n.data.allApps = allAppstr;
        }
    });

    dependoGraph(convertObjectTOJsonFormat(globalJson), 1);


    $.ajax({
        type: "POST",
        url: "ViewApplicationDependencyMapping.aspx/UpdatemappingHost",
        data: "{'profileName':'" + $("#ctl00_cphBody_hdnDepProfileName").val() + "','ipAddress':'" + Ip + "','allApp':'" + allAppstr + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        success: function (data) {
            $('[id$=pnlTagPort1]').css({ "display": "none" });
        }, error: function () {
        }
    });
    $("#ctl00_cphBody_hdnAppdata").val(allAppstr);
    $('[id$="btnSaveDiscovery"]').removeAttr('disabled');
    $('[id$=modelbg],[id$=pnlTagPort1]').css({ "display": "none" });

}

//Group name validation for comma not allow
function checkForComma(event) {
    if (event.charCode == 44) {
        event.preventDefault();
        return false;
    } else {
        return true;
    }
}

//Goup name validation
function isGroupnameExist() {
    if (grpNodeMenuText == "Create Group Nodes") {
        var groupNamefound = "";
        $.ajax({
            type: "POST",
            url: "ViewApplicationDependencyMapping.aspx/ChechGroupNameExistByName",
            data: "{'profileName':'" + $("#ctl00_cphBody_hdnDepProfileName").val() + "','groupName':'" + $("[id$=txtgrpnode]").val() + "'}",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                if (data.d == "True") {
                    groupNamefound = data.d;
                }
            }, error: function () {
            }
        });

        if (groupNamefound == "True") {
            $("#lblError").removeAttr("style");
            $("[id$=txtgrpnode]").val("");
            return false;
        }
        else
            $("#lblError").attr("style", "display:none");
    }
}

//// End  Tag Port

function deleteport(row) {
    if (row != undefined) {
        (row.closest('tr')).remove()
    }
}

//add new row
function addPortRow() {
    if ($(".dynamicTable tr:last td span ")[0].innerHTML != "") {
        $(".dynamicTable tr:last").after("<tr> <td contenteditable='false' style='width: 25%; border-width: 0px ! important;' class='porticon_new'></td><td contenteditable='false' style='width: 65%; border-right-color: transparent; border-width: 0px ! important; position:relative' class='name_icon'> <span class='lblportn'></span> </td><td contenteditable='false' class='dd' style='width: 5%; border-width: 0px;'><button class='editbtn'></button></td><td class='dd' style='width: 5%; border-width: 0px;' onclick=deleteport(this)> <span class='delete_icon'></span></td></tr>");
        $(".divselect").show();
    }
    var hgt = $(".dynamicTable").height();
    $(".notifyscrolls").animate({
        scrollTop: hgt
    });
}

//Select port from standrad port
function Setselectedport(tr) {
    if (tr != undefined) {
        currentRow[0].innerHTML = tr.selectedOptions[0].value;
        currentRow[1].innerHTML = "<span class='lblportn'>" + tr.selectedOptions[0].text + "</span>";
        $(".divselect").hide();
    }
}


