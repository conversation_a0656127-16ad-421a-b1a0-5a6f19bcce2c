﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="EventListing.aspx.cs" Inherits="CP.UI.Admin.EventListing" Title="Continuity Patrol :: Event Listing" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        
        <h3>
            <img src="../Images/event-icon.png" />
            Event List</h3>
       
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                <div class="row">
                    <div class="col-md-12 form-horizontal uniformjs">
                        <div class="form-group">
                            <label class="col-md-3 control-label">
                                Infraobject
                            <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <asp:DropDownList ID="ddlgroup" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default"
                                    AutoPostBack="True" OnSelectedIndexChanged="ddlgroup_SelectedIndexChanged1">
                                </asp:DropDownList>
                            </div>
                        </div>
                        <div class="form-group">
                            <asp:Label ID="lblRecordMessage" CssClass="col-md-3 control-label error" runat="server"></asp:Label>
                        </div>
                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>

                                <asp:ListView ID="lsteventlist" runat="server" Visible="true">
                                    <LayoutTemplate>
                                        <table class="table table-striped table-bordered table-condensed" width="100%"
                                            runat="server" style="margin-bottom: 0;">
                                            <thead>
                                                <tr>
                                                    <th style="width: 25%;">Event ID
                                                    </th>
                                                    <th style="width: 25%;">Description
                                                    </th>
                                                    <th style="width: 25%;">Time
                                                    </th>
                                                    <th style="width: 25%;">Trigger
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="slim-scroll chat-items" data-scroll-height="190px" data-scroll-size="0">
                                            <table class="table table-striped table-bordered table-condensed" width="100%">
                                                <tbody>
                                                    <asp:PlaceHolder ID="itemPlaceholder" runat="server" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td style="width: 25%;">
                                                <asp:Label runat="server" Visible="True" Text='<%# Eval("EventId") %>'></asp:Label>
                                            </td>
                                            <td style="width: 25%;">
                                                <asp:Label ID="Label1" runat="server" Visible="True" Text='<%# Eval("Description") %>'></asp:Label>
                                            </td>
                                            <td style="width: 25%;">
                                                <asp:Label ID="Label2" runat="server" Visible="True" Text='<%# Eval("Time") %>'></asp:Label>
                                            </td>
                                            <td style="width: 25%;">
                                                <asp:Label ID="Label3" runat="server" Visible="True" Text='<%#Triggername(Eval("Trigger")) %>'></asp:Label>
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>