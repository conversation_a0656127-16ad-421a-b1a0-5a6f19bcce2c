﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="ParallelWorkflow.aspx.cs" Inherits="CP.UI.ParrallelWorkflow" Title="Continuity Patrol :: Parallel Workflow" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
    <script src="../Script/jquery-ui.js" type="text/javascript"></script>
    <script src="../Script/CollapseWidgetJS.js"></script>
    <script src="../Script/EncryptDecrypt.js"></script>
    <script type="text/javascript">



        function CancelClick() {
            return false;
        }
        $(document).ready(function () {
            //$("select.new-select").select2({
            //    //formatResult: format,gf
            //    //formatSelection: format,
            //});
            CollapseWidgetJS();
        });
        function pageLoad() {

            $(window).bind("capsOn", function (event) {
                if ($(".chk-caps").length > 0) {
                    $(".chk-caps:focus").nextAll(".caps-error").show();
                }
            });
            $(window).bind("capsOff capsUnknown", function (event) {
                $(".chk-caps:focus").nextAll(".caps-error").hide();
            });
            $(".chk-caps").bind("focusout", function (event) {
                $(".chk-caps:focus").nextAll(".caps-error").hide();
            });
            $(".chk-caps").bind("focusin", function (event) {
                if ($(window).capslockstate("state") === true) {
                    $(".chk-caps:focus").nextAll(".caps-error").show();
                }
            });
            $(window).capslockstate();


            //$("select.new-select").select2({
            //    //formatResult: format,
            //    //formatSelection: format,
            //});
            //  $("").chosen();
            $(".chosen-select, .chosen-select_drop,[id$=ddlLoadPorfile]").chosen({ search_contains: true });

            CollapseWidgetJS();

            if (sessionStorage.getItem("colapseIds") != null) {
                var idVal = sessionStorage.getItem("colapseIds");
                var idvalarr = idVal.split(',');
                for (var i = 0; i < idvalarr.length; i++) {
                    if (idvalarr[i].split('^')[1] == "true") {
                        $("#" + idvalarr[i].split('^')[0]).attr("data-collapse-closed", idvalarr[i].split('^')[1]).children(".widget-body").removeClass("in").addClass("collapse");
                    } else {
                        $("#" + idvalarr[i].split('^')[0]).attr("data-collapse-closed", idvalarr[i].split('^')[1]).children(".widget-body").removeClass("collapse").addClass("in");

                    }
                }
            }

        }
        $('span#ctl00_cphBody_lvProfiles_ctrl0_lvGroupNew_ctrl0_lblProgress').filter(function () { return $.text([this]) === 'Width:'; }).remove();
       
        function TimerFalse() {
            var newDate = new Date();
            $("[id$=txtDescription]").val($('[id$=ddlLoadPorfile] option:selected').text() + "_" + newDate.getFullYear() + ("0" + (newDate.getMonth() + 1)).slice(-2) + newDate.getDate() + "_" + newDate.getHours() + ":" + newDate.getMinutes() + ":" + newDate.getSeconds());
            var timer = $find("<%=Timerworkflow.ClientID%>")
            timer._stopTimer();
        }
      
        function clearText(control) {
            control.value = "";

        }

        function getHashData(control) {
            control.value = genrateUserNameHash(control, $('#ctl00_cphBody_hdfStaticGuid').val());
        }
    </script>
    <style type="text/css">
        .fontColor {
            color: black;
        }
        .small {
            max-height: 400px !important;
            min-height: 170px !important;
            overflow-y: scroll !important;
        }

        h5.heading {
            margin-bottom: 6px;
            padding-right: 0px !important;
            padding-left: 0px !important;
        }
         .alert_ic {
            background-image: url('../images/icons/alert.gif');
            background-color: transparent;
            border-width: 0px;
            height: 16px;
            width: 16px;
            padding: 0;
            /*border-radius: 50%;*/
            margin: 5px 10px;
            vertical-align: sub;
        }
           .small-element .popuptable th:first-child {
            background-color: #4a8bc2 !important;
        }

            progress-bar.fontColor {
            font-size: 10px;
            color: #000;
            line-height: 1.2em;
        }
            
        .clear {
            clear: both;
        }

    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="Updatepanel7" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <h3>
                    <img src="../Images/worflow_new.png">
                    Parallel Workflow</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">

                        <div>
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <div class="col-md-6">
                                        <label class="col-md-4 control-label">
                                            Configured Profiles:</label>
                                        <div class="col-md-8">
                                            <asp:DropDownList ID="ddlLoadPorfile" runat="server" CssClass="col-md-8"
                                                data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlLoadPorfile_SelectedIndexChanged">
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                    <asp:Panel ID="pnlUsers" runat="server" CssClass="col-md-6">
                                        <label class="col-md-4 control-label">
                                            Running Profiles(Users):</label>
                                        <div class="col-md-8">
                                            <asp:DropDownList ID="ddlUserList" runat="server" CssClass="selectpicker col-md-8"
                                                data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlUserList_SelectedIndexChanged">
                                            </asp:DropDownList>
                                        </div>

                                    </asp:Panel>
                                </div>


                                <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
                                    <Triggers>
                                        <asp:AsyncPostBackTrigger ControlID="Timerworkflow" EventName="Tick" />
                                    </Triggers>
                                    <ContentTemplate>


                                        <%-- <h3>
                                            <img src="../Images/worflow_new.png">
                                            Parallel Workflow</h3>--%>

                                        <%--<div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">--%>
                                        <div class="small-element">
                                            <div class="row">
                                                <div class="col-md-12 form-horizontal uniformjs">
                                                    <%--  <asp:Timer runat="server" ID="Timerworkflow" Interval="5000" OnTick="UpdateTimerTick"
                                        Enabled="false" />--%>
                                                    <%-- <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-3 control-label">
                                                Configured Profiles:</label>
                                            <div class="col-md-9">
                                                <asp:DropDownList ID="ddlLoadPorfile" runat="server" CssClass="selectpicker col-md-8"
                                                    data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlLoadPorfile_SelectedIndexChanged">
                                                </asp:DropDownList>
                                            </div>
                                        </div>
                                        <asp:Panel ID="pnlUsers" runat="server" CssClass="col-md-6">
                                            <label class="col-md-3 control-label">
                                                Running Profiles(Users):</label>
                                            <div class="col-md-9">
                                                <asp:DropDownList ID="ddlUserList" runat="server" CssClass="selectpicker col-md-8"
                                                    data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlUserList_SelectedIndexChanged">
                                                </asp:DropDownList>
                                            </div>

                                        </asp:Panel>
                                    </div>--%>
                                                    <hr />

                                                    <asp:Label ID="lblRunningPrfl" Visible="False" runat="server" Text="This Profile is already Running" />

                                                    <asp:ListView ID="lvGroup" runat="server" OnItemDeleting="lvGroup_ItemDeleting" OnItemCommand="lvGroup_ItemCommand"
                                                        OnItemDataBound="lvGroup_ItemDataBound">
                                                        <LayoutTemplate>
                                                            <table id="table1" runat="server" class="dynamicTable table table-bordered table-condensed table-striped"
                                                                style="margin-bottom: 0;" width="100%">
                                                                <thead>
                                                                    <tr>
                                                                        <th style="width: 2%">
                                                                            <asp:CheckBox ID="CheckAll" ToolTip="Select All" Checked="True" runat="server" OnCheckedChanged="CheckAllCheckedChanged"
                                                                                AutoPostBack="True" Enabled="false" />
                                                                        </th>
                                                                        <th style="width: 15%">InfraObject Name
                                                                        </th>
                                                                        <th style="width: 16%;">Workflow
                                                                        </th>
                                                                        <th style="width: 16%">Current Action
                                                                        </th>
                                                                        <th style="width: 7%;">Status
                                                                        </th>
                                                                        <th style="width: 10%">Progress
                                                                        </th>
                                                                        <th style="width: 10%">InfraObject State
                                                                        </th>
                                                                        <th style="width: 24%">Workflow Actions
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                            <div class="parallelScroll">
                                                                <table class="dynamicTable table table-bordered table-condensed table-striped" style="table-layout: fixed;"
                                                                    width="100%" id="lvGrouptbl">
                                                                    <tbody>
                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                            <hr />
                                                        </LayoutTemplate>
                                                        <ItemTemplate>
                                                            <tr>
                                                                <td style="width: 3.5%;">
                                                                    <div class="tdword-wrap">
                                                                        <asp:CheckBox ID="CheckBox_select" Checked="True" Enabled='<%# GetGroupCheck( Eval("GroupState")) %>'
                                                                            AutoPostBack="True" runat="server" OnCheckedChanged="CheckBoxItemCheckedChanged" />
                                                                </td>
                                                                <td style="width: 15%">
                                                                    <div class="tdword-wrap">
                                                                        <asp:Label ID="lblId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                                        <asp:Label ID="lblTable" Visible="False" runat="server" Text='<%# Eval("NameofTable") %>' />
                                                                        <asp:LinkButton ID="lbGroupName" Text='<%# Eval("InfraObjectName") %>' CommandName="GroupPopUp"
                                                                            runat="server" Enabled='<%# GroupNameEnable(Eval("Status")) %>' CssClass="bold"
                                                                            ForeColor="#000">Group Name</asp:LinkButton>
                                                                        <asp:Label ID="lblWorkflowType" Visible="False" runat="server" Text='<%# Eval("WorkflowType") %>' />
                                                                    </div>
                                                                </td>
                                                                <td style="width: 16%;">
                                                                    <div class="tdword-wrap">
                                                                        <asp:Label ID="Label2" runat="server" Text='<%# Eval("WorkflowName") %>' />
                                                                    </div>
                                                                </td>
                                                                <td style="width: 16%;">
                                                                    <div class="tdword-wrap">
                                                                        <asp:Label ID="Label1" runat="server" Text='<%# Eval("CurrentActionName") %>' />
                                                                    </div>
                                                                </td>
                                                                <td style="width: 7%">
                                                                    <asp:Label ID="GroupStatus" CssClass='<%# GetStatusCss( Eval("Status")) %>' runat="server"
                                                                        Text='<%# Eval("Status") %>' />
                                                                </td>
                                                                <td style="width: 10%" class="padding">
                                                                    <div>
                                                                        <div class="progress progress-success progress-mini col-md-8" style="padding-left: 0px; padding-right: 0px; height: 13px;">
                                                                            <asp:Label ID="lblProgress" CssClass="progress-bar" runat="server" Style='<%#ShowStatus( Eval("ProgressStatus"))%>'></asp:Label><%--style="width: 10%"--%>
                                                                        </div>
                                                                        <div class="col-md-4" style="margin-top: -6px;">
                                                                            <asp:Label ID="Label3" runat="server" Visible="true" Text='<%# Eval("ProgressStatus") %>' />
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td style="width: 10%;">
                                                                    <div class="tdword-wrap text-center">
                                                                        <asp:Image ID="imgGroupState" runat="server" ImageUrl='<%# GetGroupState( Eval("GroupState")) %>' />
                                                                    </div>
                                                                </td>
                                                                <td style="width: 24%">
                                                                    <asp:Button ID="BtnNext" CssClass="btn btn-primary col-xs-3" Width="17%" CommandName="Next" runat="server"
                                                                        Text="Next" Enabled='<%# ActiveProcess(Eval("Status"),Eval("ConditionalOperation"),Eval("ID")) %>' />
                                                                    <asp:Button ID="BtnRetry" CssClass="btn btn-primary col-xs-3" Width="19%" CommandName="Retry" runat="server"
                                                                        Text="Retry" Enabled='<%# ActiveProcess(Eval("Status"),Eval("ConditionalOperation"),Eval("ID")) %>' />
                                                                    <asp:Button ID="BtnAbortWorkflow" runat="server" CssClass="btn btn-primary col-xs-3" CommandName="Abort" Style="width: 19%"
                                                                        Text="Abort" Enabled='<%# ActiveProcess(Eval("Status"),Eval("ConditionalOperation"),Eval("ID")) %>' />
                                                                    <asp:Button ID="BtnReload" CssClass="btn btn-primary col-xs-3" Width="19%" CommandName="Reload" runat="server"
                                                                        Text="Reload" Enabled='<%# ActiveProcess(Eval("Status"),Eval("ConditionalOperation"),Eval("ID")) %>' />
                                                                   <asp:Button ID="BtnPause" CssClass="btn btn-primary col-xs-3" Width="18%" CommandName="Pause" runat="server" 
                                                                       Text='<%# ActiveProcessPauseText(Eval("ID"))%>' Enabled="false" />
                                                                    <asp:ImageButton ID="ImgDelete" runat="server" Visible='false' CommandName="Delete" Style="padding-left: 10px;"
                                                                        AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                                        ConfirmText='<%# "Are you sure want to delete " + Eval("InfraObjectName") + " ? " %>'
                                                                        OnClientCancel="CancelClick">
                                                                    </TK1:ConfirmButtonExtender>
                                                                    <TK1:ConfirmButtonExtender ID="ConfirmButtonAbort" runat="server" TargetControlID="BtnAbortWorkflow"
                                                                        ConfirmText='<%# "Are you sure want to abort execution of workflow " + Eval("InfraObjectName") + " ? " %>'
                                                                        OnClientCancel="CancelClick">
                                                                    </TK1:ConfirmButtonExtender>
                                                                </td>
                                                            </tr>
                                                        </ItemTemplate>
                                                        <EmptyDataTemplate>
                                                            <tr>
                                                                <td>
                                                                    <div class="message warning align-center bold no-bottom-margin">
                                                                        <asp:Label ID="pageResult" runat="server" Text="No Records"></asp:Label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </EmptyDataTemplate>
                                                    </asp:ListView>

                                                    <asp:Label ID="lblSameSOSB" Visible="true" ForeColor="Red" runat="server" Text="" />


                                                    <div class="form-actions row">
                                                        <div class="col-md-6">
                                                            <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                                                            <asp:Label ID="lblError" runat="server" ForeColor="Red"></asp:Label>
                                                        </div>
                                                        <div class="col-md-6 text-right">
                                                            <asp:Button ID="BtnStart" runat="server" Text="Start" Enabled="False" CssClass="btn btn-primary"
                                                                Width="16%" ValidationGroup="NewWorkflow" OnClick="BtnStartClick" OnClientClick="TimerFalse()" />
                                                            <%--  <asp:Button ID="BtnCompleted" runat="server" Text="Completed" Enabled="False" Visible="false"
                                    CssClass="btn btn-primary " Width="20%" OnClick="BtnCompletedClick" />--%>
                                                        </div>
                                                    </div>

                                                    <hr />
                                                </div>

                                                <asp:Timer runat="server" ID="Timerworkflow" Interval="5000" OnTick="UpdateTimerTick"/>   <%--Enabled="false"--%>

                                                <div id="divparallelProfile" class="small-element">
                                                    <asp:ListView ID="lvProfiles" runat="server" OnItemCommand="lvProfiles_ItemCommand"
                                                        OnItemDataBound="lvProfiles_ItemDataBound">
                                                        <ItemTemplate>
                                                            <div id='<%# Eval("Id") %>' class="widget" data-toggle="collapse-widget">
                                                                <div class="widget-head">
                                                                    <div class="">
                                                                        <h4 class="heading"><%# Eval("ProfileName") %></h4>
                                                                         <asp:Label ID="lblPName" Visible="False" runat="server" Text='<%# Eval("ProfileName") %>' />                                                                        
                                                                        <asp:Label ID="ProfileId" Visible="False" runat="server" Text='<%# Eval("ProfileId") %>' />
                                                                        <asp:Label ID="lblDrId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                                    </div>
                                                                    <%--<div class="col-md-6">--%>
                                                                    <span class="icon-sync"></span><span class="text-warning padding">Running <%# Eval("ActionsRunning") %> </span><span class="margin-right">|</span>
                                                                    <span class="InActive margin-left"></span><span class="text-danger padding margin-right">Errors <%# Eval("ActionsError") %></span><span class="margin-right">|</span>
                                                                    <span class="icon-skip"></span><span class="text-danger skip padding margin-right">Skipped <%# Eval("ActionsSkip") %></span><span class="margin-right">|</span>
                                                                    <span class="Active "></span><span class="  text-success padding">Success <%# Eval("ActionsSuccess") %></span>
                                                                      <asp:Button ID="btnShowAlert" runat="server" CssClass="alert_ic" CommandName="ShowAlerts" ToolTip="Wait Alert Action" Visible='<%# IsAlertPending(Eval("Id")) %>' />
                                                                    <%-- </div>--%>
                                                                    <%--<asp:LinkButton runat="server" ID="lnkcollapse" CssClass="collapse-toggle" ></asp:LinkButton>--%>
                                                                    <span class="collapse-toggle"></span>
                                                                </div>

                                                                <div class="widget-body">
                                                                    <%--  <asp:UpdatePanel ID="UpdatePanel6" runat="server" UpdateMode="Conditional">

                                                        <ContentTemplate>--%>
                                                                    <asp:ListView ID="lvGroupNew" runat="server" OnItemCommand="lvGroup_ItemCommand"
                                                                        OnItemDataBound="lvGroupNew_ItemDataBound">
                                                                        <LayoutTemplate>
                                                                            <table id="table1" runat="server" class="dynamicTable table table-bordered table-condensed table-striped"
                                                                                style="margin-bottom: 0;table-layout:fixed" width="100%">
                                                                                <thead>
                                                                                    <tr>
                                                                                        <th style="width: 3%">
                                                                                            <asp:CheckBox ID="CheckAll" ToolTip="Select All" Checked="True" runat="server" OnCheckedChanged="CheckAllCheckedChanged"
                                                                                                AutoPostBack="True" />
                                                                                        </th>
                                                                                        <th style="width: 15%">InfraObject Name
                                                                                        </th>
                                                                                        <th style="width: 16%;">Workflow
                                                                                        </th>
                                                                                        <th style="width: 14%">Current Action
                                                                                        </th>
                                                                                        <th style="width: 6%;">Status
                                                                                        </th>
                                                                                        <th style="width: 15%">Progress
                                                                                        </th>
                                                                                        <th style="width: 9%">InfraObject State
                                                                                        </th>
                                                                                        <th style="width: 22%">Workflow Actions
                                                                                        </th>
                                                                                    </tr>
                                                                                </thead>
                                                                            </table>
                                                                            <div class="parallelScroll">
                                                                                <table class="dynamicTable table table-bordered table-condensed table-striped" style="table-layout: fixed;" width="100%" id="lvGrouptbl">
                                                                                    <tbody>
                                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                                                                    </tbody>
                                                                                </table>
                                                                            </div>
                                                                        </LayoutTemplate>
                                                                        <ItemTemplate>
                                                                            <tr>
                                                                                <td style="width: 3%;">
                                                                                    <div class="tdword-wrap">
                                                                                        <asp:CheckBox ID="CheckBox_select" Checked="True" Enabled='<%# GetGroupCheck( Eval("GroupState")) %>'
                                                                                            AutoPostBack="True" runat="server" OnCheckedChanged="CheckBoxItemCheckedChanged" />
                                                                                </td>
                                                                                <td style="width: 15%">
                                                                                    <div class="tdword-wrap">
                                                                                        <asp:Label ID="lblDrOprId" Visible="False" runat="server" Text='<%# Eval("ParallelDROperationId") %>' />
                                                                                        <asp:Label ID="lblId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                                                        <asp:Label ID="lblTable" Visible="False" runat="server" Text='<%# Eval("NameofTable") %>' />
                                                                                        <asp:Label ID="lblActionId" Visible="False" runat="server" Text='<%# Eval("CurrentActionId") %>' />
                                                                                        <asp:Label ID="lblWorkflowId" Visible="False" runat="server" Text='<%# Eval("WorkflowId") %>' />
                                                                                        <asp:LinkButton ID="lbGroupName" Text='<%# Eval("InfraObjectName") %>' CommandName="GroupPopUp"
                                                                                            runat="server" Enabled='<%# GroupNameEnable(Eval("Status")) %>' CssClass="bold"
                                                                                            ForeColor="#000">Group Name</asp:LinkButton>
                                                                                    </div>
                                                                                </td>
                                                                                <td style="width: 16%;">
                                                                                    <div class="tdword-wrap">
                                                                                        <asp:Label ID="Label2" runat="server" Text='<%# Eval("WorkflowName") %>' />
                                                                                    </div>
                                                                                </td>
                                                                                <td style="width: 14%;">
                                                                                    <div class="tdword-wrap">
                                                                                        <asp:Label ID="Label1" runat="server" Text='<%# Eval("CurrentActionName") %>' />
                                                                                    </div>
                                                                                </td>
                                                                                <td style="width: 6%">
                                                                                    <asp:Label ID="GroupStatus" CssClass='<%# GetStatusCss( Eval("Status")) %>' runat="server"
                                                                                        Text='<%# Eval("Status") %>' />
                                                                                </td>
                                                                                <td style="width: 15%" class="padding">
                                                                                    <div>
                                                                                        <div class="progress progress-success progress-mini col-md-9" style="padding-left: 0px; padding-right: 0px; height: 15px; text-align: center; margin-top: 3px !important;">
                                                                                            <asp:Label ID="lblProgress" CssClass="progress-bar fontColor" runat="server" Text='<%#ShowStatus( Eval("ProgressStatus"))%>' Style='<%#ShowStatus( Eval("ProgressStatus"))%>'></asp:Label>
                                                                                            <%--style='<%#ShowStatus( Eval("ProgressStatus"))%>' --%>


                                                                                            <%--style="width: 10%"--%>
                                                                                        </div>
                                                                                        <div class="col-md-3" style="padding-right: 0; padding-left: 5px;">
                                                                                            <asp:Label ID="Label3" runat="server" Visible="true" Text='<%# Eval("ProgressStatus") %>' CssClass="col-md-3" Style="padding-left: 0px; padding-right: 5px;" />
                                                                                        </div>
                                                                                        <div class="clear"></div>
                                                                                        <asp:Label ID="lblActionComplTime1" runat="server" CssClass="col-md-12 padding-none"></asp:Label>

                                                                                    </div>
                                                                                </td>
                                                                                <td style="width: 9%;">
                                                                                    <div class="tdword-wrap text-center">
                                                                                        <asp:Image ID="imgGroupState" runat="server" ImageUrl='<%# GetGroupState( Eval("GroupState")) %>' />
                                                                                    </div>
                                                                                </td>
                                                                                <td style="width: 22%">
                                                                                    <asp:Button ID="BtnNext" CssClass="btn btn-primary col-xs-3" Width="15%" CommandName="Next" runat="server"
                                                                                        Text="Next" Enabled='<%# ActiveProcess(Eval("Status"),Eval("ConditionalOperation"),Eval("ID")) %>' />
                                                                                    <asp:Button ID="BtnRetry" CssClass="btn btn-primary col-xs-3" Width="15%" CommandName="Retry" runat="server"
                                                                                        Text="Retry" Enabled='<%# ActiveProcess1(Eval("Status"),Eval("ConditionalOperation"),Eval("ID")) %>' />
                                                                                    <asp:Button ID="BtnAbortWorkflow" runat="server" CssClass="btn btn-primary col-xs-3" Width="16%" CommandName="Abort"
                                                                                        Text="Abort" Enabled='<%# ActiveProcessAbort(Eval("Status"),Eval("ConditionalOperation"),Eval("ID")) %>' />
                                                                                    <asp:Button ID="BtnReload" CssClass="btn btn-primary col-xs-3" Width="18%" CommandName="Reload" runat="server"
                                                                                        Text="Reload" Enabled='<%# ActiveProcess(Eval("Status"),Eval("ConditionalOperation"),Eval("ID")) %>' />
                                                                                    <asp:Button ID="BtnPause" CssClass="btn btn-primary col-xs-3" Width="19%" CommandName="Pause" runat="server" 
                                                                                        Text='<%# ActiveProcessPauseText(Eval("ID"))%>' Enabled='<%# ActiveProcessPause(Eval("ID"))%>' />
                                                                                  
                                                                                       <asp:ImageButton ID="Imgpop" runat="server" Visible='<%# Active(Eval("ID")) %>' CommandName="FailedAction" Style="padding-left: 3px;padding-top: 5px;"
                                                                                        AlternateText="show" ToolTip="Show FailedAction" ImageUrl="../Images/icons/alert.gif" />

                                                                                    <asp:ImageButton ID="ImgDelete" runat="server" Visible='false' CommandName="Delete" Style="padding-left: 10px;"
                                                                                        AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                                                        ConfirmText='<%# "Are you sure want to delete " + Eval("InfraObjectName") + " ? " %>'
                                                                                        OnClientCancel="CancelClick">
                                                                                    </TK1:ConfirmButtonExtender>
                                                                                    <TK1:ConfirmButtonExtender ID="ConfirmButtonAbort" runat="server" TargetControlID="BtnAbortWorkflow"
                                                                                        ConfirmText='<%# "Are you sure want to abort execution of workflow " + Eval("InfraObjectName") + " ? " %>'
                                                                                        OnClientCancel="CancelClick">
                                                                                    </TK1:ConfirmButtonExtender>
                                                                                </td>
                                                                            </tr>
                                                                        </ItemTemplate>
                                                                        <EmptyDataTemplate>
                                                                            <tr>
                                                                                <td>
                                                                                    <div class="message warning align-center bold no-bottom-margin">
                                                                                        <asp:Label ID="pageResult" runat="server" Text="No Records"></asp:Label>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        </EmptyDataTemplate>
                                                                    </asp:ListView>

                                                                    <hr />

                                                                    <div class="text-right">
                                                                        <asp:Button ID="BtnCompleted" runat="server" Text="Completed"
                                                                            CssClass="btn btn-primary " CommandName="Completed" Enabled='<%# GetCompleteStatus(Eval("Id")) %>' />

                                                                    </div>
                                                                    <%--  </ContentTemplate>
                                                    </asp:UpdatePanel>--%>
                                                                </div>

                                                            </div>
                                                        </ItemTemplate>
                                                    </asp:ListView>
                                                </div>
                                                <asp:UpdatePanel ID="Updatepanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true"
                                                    EnableViewState="False">
                                                    <ContentTemplate>

                                                        <asp:Panel ID="PanelCheckMaintenanceStatus" runat="server" Style="display: none">

                                                            <div class="modal" style="display: block;" aria-hidden="false">
                                                                <div class="modal-dialog" style="width: 550px;">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <asp:LinkButton ID="LinkButton2" class="close" runat="server" OnClick="CloseMaintenanceClick">×</asp:LinkButton>
                                                                            <h3 class="modal-title">Confirmation</h3>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            <div class="row">
                                                                                <div class="col-md-3 text-center">

                                                                                    <%--<img src="../images/icons/Warning.png">--%>
                                                                                    <img src="../images/icons/danger-confirmation-icon.png" style="padding-top: 18px;">
                                                                                </div>
                                                                                <div class="col-md-9" style="border-left: 1px solid #dbdbdb;">
                                                                                    <asp:Label ID="lblStatusMessage" Visible="true" runat="server"></asp:Label>
                                                                                    <asp:HiddenField ID="gdnpopupval1" runat="server" />
                                                                                    <hr style="border-top-color: #4a8bc2; margin: 10px 0px;" />
                                                                                    <div class="row">
                                                                                        <div class="col-md-2" style="padding-top: 10px">Reason</div>
                                                                                        <div class="col-md-10">
                                                                                            <asp:TextBox ID="txtEmailMessage" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="1" Columns="1" Width="92%"></asp:TextBox>
                                                                                            <asp:HiddenField ID="gdnpopupval" runat="server" />
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <asp:Button ID="btnChangeMaintenance" CssClass="btn btn-primary" Width="20%" runat="server"
                                                                                Text="Ok" OnClick="BtnChangeMaintenanceClick" Style="padding: 5px 8px;" />
                                                                            <asp:Button ID="btnChangeMaintenanceCancel" CssClass="btn btn-default" Width="20%"
                                                                                runat="server" Text="Cancel" OnClick="BtnChangeMaintenanceCancelClick" />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </asp:Panel>
                                                    </ContentTemplate>
                                                    <%--  <Triggers>
                            <asp:AsyncPostBackTrigger ControlID="BtnStart" EventName="Click" />
                        </Triggers>--%>
                                                </asp:UpdatePanel>


                                                   <asp:Panel ID="PanelExceptionCondition" runat="server" Style="display: none">
                                                    <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                                        <ContentTemplate>
                                                            <div class="modal" style="display: block;" aria-hidden="false">
                                                                <div class="modal-dialog" style="width: 60%;">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <asp:LinkButton ID="LinkButton1" class="close" runat="server" OnClick="CloseAlertPopUpClick">×</asp:LinkButton>
                                                                           <%-- <h3 class="modal-title">Alert</h3>--%>
                                                                               <img src="../images/icons/normal_16.png" />
                                                                            <asp:Label ID="lblprofileName" Visible="True" runat="server"></asp:Label>
                                                                        </div>
                                                                        <div class="modal-body" style="padding-bottom: 0px">
                                                                               <%-- Strat hide Previous Code --%>
<%--                                                                            <div class="">
                                                                                <div class="form-group mb5">
                                                                                    <div class="col-xs-2 icondiv">
                                                                                        <span class="icon-state"></span>
                                                                                    </div>
                                                                                    <div class="col-xs-10">
                                                                                        <asp:Label ID="lblActionMessage" Visible="True" runat="server"></asp:Label>
                                                                                    </div>
                                                                                    <div class="clearfix"></div>
                                                                                </div>
                                                                                <div id="dvUserAuthentication" class="form-horizontal uniformjs" runat="server">
                                                                                    <div class="form-group">
                                                                                        <label class="col-xs-3 control-label lftdiv">Workflow Name :</label>
                                                                                        <div class="col-xs-9 control-label rghtdiv">
                                                                                            <asp:Label ID="lblWorkflowNameTime" runat="server" Style="word-break: break-all"></asp:Label>
                                                                                        </div>
                                                                                        <div class="clearfix"></div>
                                                                                    </div>
                                                                                    <div class="form-group">
                                                                                        <label class="col-xs-3 control-label lftdiv">Password : <span class="error">*</span>  </label>
                                                                                        <div class="col-xs-9 rghtdiv">
                                                                                            <asp:TextBox TextMode="Password" Style="width: 100%" CssClass="form-control" ID="txtAuthProfilePass" runat="server" EnableViewState="False"></asp:TextBox>
                                                                                            <asp:Label runat="server" ID="errormsg" Text="*" CssClass="error" Visible="false"></asp:Label>

                                                                                        </div>
                                                                                        <div class="clearfix"></div>
                                                                                       
                                                                                    </div>
                                                                                </div>
                                                                                <div class="form-group" id="dvActionNote" runat="server">
                                                                                    <label class="col-xs-3 control-label lftdiv">Note :   <span class="error">*</span></label>
                                                                                    <div class="col-xs-9 rghtdiv">
                                                                                        <asp:TextBox TextMode="MultiLine" Style="width: 100%" CssClass="form-control" ID="txtNote" runat="server"></asp:TextBox>
                                                                                    </div>
                                                                                    <div class="clearfix"></div>
                                                                                </div>


                                                                            </div>--%>
                                                                              <div class="row">
                                                                                
                                                                                <div class="col-xs-12">
                                                                                   
                                                                                    <asp:ListView ID="lvDisplayAlertAction" runat="server" OnItemCommand="lvDisplayAlertAction_ItemCommand" >
                                                                                        <LayoutTemplate>
                                                                                            <table id="table1" runat="server" class="table table-bordered popuptable table-condensed table-striped margin-bottom-none" width="100%" style="table-layout: fixed;">
                                                                                                <thead>
                                                                                                    <tr >
                                                                                                        <th style="width: 20%" >Action Name
                                                                                                        </th>
                                                                                                        <th style="width: 15%">Workflow Name
                                                                                                        </th>
                                                                                                        <th style="width: 15%">Message
                                                                                                        </th> 
                                                                                                        <th style="width: 20%" >Password
                                                                                                        </th>
                                                                                                        <th style="width: 20%" >Action Note
                                                                                                        </th>
                                                                                                          <th style="width:10%">Action 
                                                                                                        </th>
                                                                                                    </tr>
                                                                                                </thead>
                                                                                            </table>
                                                                                            <%-- <div class="slim-scroll">--%>
                                                                                            <table class="table table-bordered table-condensed table-striped " width="100%" style="table-layout: fixed;" id="lvFailedAction">
                                                                                                <tbody>
                                                                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                                                                                </tbody>
                                                                                            </table>
                                                                                            <%-- </div>--%>
                                                                                        </LayoutTemplate>
                                                                                        <ItemTemplate>
                                                                                            <tr>
                                                                                                <td style="width: 20%;" class="tdword-wrap">
                                                                                                    <asp:Label ID="lblId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                                                                    <asp:Label ID="lblGRPWFId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                                                                    <asp:Label ID="lblActionid" Visible="False" runat="server" Text='<%# Eval("CurrentActionId") %>' />
                                                                                                    <asp:Label ID="lblDROperationId" Visible="False" runat="server" Text='<%# Eval("ParallelDROperationId") %>' />
                                                                                                    <asp:Label ID="lblActionName" runat="server" ToolTip='<%# Eval("CurrentActionName") %>' Text='<%# Eval("CurrentActionName") %>' /> 
                                                                                                </td>
                                                                                                <td class="tdword-wrap" style="width: 15%">
                                                                                                    <asp:Label ID="lblWfName" runat="server" Text='<%#Eval("WorkflowName") %>' />
                                                                                                </td>
                                                                                                <td class="tdword-wrap" style="width: 15%">
                                                                                                    <asp:Label ID="lblAlertMessage" runat="server" ToolTip='<%#Eval("Message") %>' Text='<%#Eval("Message") %>' />
                                                                                                </td>
                                                                                                <td    class="tdword-wrap" style="width: 20%" >
                                                                                                      <asp:TextBox TextMode="Password" Style="width:100%;" CssClass="form-control" ID="txtAuthProfilePass"    Enabled='<%# Ispassword(Eval("CurrentActionId")) %>' runat="server" EnableViewState="False"></asp:TextBox>
                                                                                                      <asp:Label runat="server" ID="errormsg" Text="*" ToolTip="Invalid Password" CssClass="error" Visible="false"></asp:Label>
                                                                                                </td>

                                                                                                <td   class="tdword-wrap" style="width: 20%"  >
                                                                                                    <asp:TextBox TextMode="MultiLine" Style="width:100%;" CssClass="form-control" ID="txtNote"  Enabled='<%# IsNote(Eval("CurrentActionId")) %>' runat="server"></asp:TextBox>
                                                                                                </td>

                                                                                                <td style="width: 10%" class="small-element">
                                                                                                    <asp:Button ID="btnAlertOK" CssClass="btn btn-primary col-xs-3" Width="70%" CommandName="AlertOk" CausesValidation="false" runat="server" Text="Ok" />
                                                                                                </td>
                                                                                            </tr>
                                                                                        </ItemTemplate>
                                                                                        <EmptyDataTemplate>
                                                                                            <div class="row">
                                                                                                <asp:Label ID="pageResult" runat="server" CssClass="text-danger" Text="No Records"></asp:Label>
                                                                                            </div>
                                                                                        </EmptyDataTemplate>
                                                                                    </asp:ListView>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                        <div class="modal-footer">
                                                                            <%--<asp:Button ID="BtnOk" runat="server" Text="OK" CssClass="btn btn-primary" OnClick="BtnOkClick" />--%>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>


                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </asp:Panel>

                                              <%--  <asp:Panel ID="PanelExceptionCondition" runat="server" Style="display: none">
                                                    <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                                        <ContentTemplate>
                                                            <div class="modal" style="display: block;" aria-hidden="false">
                                                                <div class="modal-dialog" style="width: 370px;">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <asp:LinkButton ID="LinkButton1" class="close" runat="server" OnClick="CloseAlertPopUpClick">×</asp:LinkButton>
                                                                            <h3 class="modal-title">Alert</h3>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            <div class="row">
                                                                                <div class="col-xs-1">
                                                                                    <span class="icon-state"></span>
                                                                                </div>
                                                                                <div class="col-xs-11">
                                                                                    <asp:Label ID="lblActionMessage" Visible="True" runat="server"></asp:Label>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <asp:Button ID="BtnOk" runat="server" Text="OK" CssClass="btn btn-primary" OnClick="BtnOkClick" />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>


                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </asp:Panel>--%>

                                                <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenance" runat="server" TargetControlID="HiddenForMaintenance"
                                                    RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="PanelCheckMaintenanceStatus"
                                                    PopupDragHandleControlID="PanelCheckMaintenanceStatus" Drag="true" BackgroundCssClass="bg">
                                                </TK1:ModalPopupExtender>
                                                <asp:Button runat="server" ID="HiddenForModal" Style="display: none" />
                                                <asp:Button runat="server" ID="HiddenForMaintenance" Style="display: none" />
                                                <TK1:ModalPopupExtender ID="ModalPopupExtenderCustom" runat="server" TargetControlID="HiddenForModal"
                                                    RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelCustom"
                                                    PopupDragHandleControlID="panelCustom" Drag="true" BackgroundCssClass="bg">
                                                </TK1:ModalPopupExtender>
                                                <asp:Button runat="server" ID="HiddenAlert" Style="display: none" />
                                                <TK1:ModalPopupExtender ID="ModalPopupExtenderAlertCustom" runat="server" TargetControlID="HiddenAlert"
                                                    PopupControlID="PanelExceptionCondition" Drag="false" PopupDragHandleControlID="PanelExceptionCondition"
                                                    BackgroundCssClass="bg">
                                                </TK1:ModalPopupExtender>
                                                <asp:Button runat="server" ID="PopupExtenderForCredentialhiddenAlert" Style="display: none" />
                                                <TK1:ModalPopupExtender ID="ModalPopupExtenderForCredential" runat="server" TargetControlID="PopupExtenderForCredentialhiddenAlert"
                                                    PopupControlID="PanelWaitForCredential" Drag="false" PopupDragHandleControlID="PanelWaitForCredential"
                                                    BackgroundCssClass="bg">
                                                </TK1:ModalPopupExtender>
                                                <TK1:ModalPopupExtender ID="ModalPopupExtenderPassword" runat="server" TargetControlID="BtnStart"
                                                    RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="PanelAuthenticate"
                                                    PopupDragHandleControlID="PanelAuthenticate" Drag="true" BackgroundCssClass="bg">
                                                </TK1:ModalPopupExtender>
                                                <asp:Button runat="server" ID="BtnActionFailed" Style="display: none" />
                                                <TK1:ModalPopupExtender ID="ModalPopupExtenderActionFailed" runat="server" TargetControlID="BtnActionFailed"
                                                    RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="PanelFailedActions"
                                                    PopupDragHandleControlID="PanelFailedActions" Drag="true" BackgroundCssClass="bg">
                                                </TK1:ModalPopupExtender>

                                                <asp:UpdatePanel runat="server" ID="UpdatePanel9" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <asp:Panel ID="PanelCheckActiveStatus" runat="server" Style="display: none">
                                                            <asp:UpdatePanel ID="Updatepanel8" runat="server" UpdateMode="Always" ChildrenAsTriggers="true"
                                                                EnableViewState="False">
                                                                <ContentTemplate>
                                                                    <div class="modal" style="display: block;" aria-hidden="false">
                                                                        <div class="modal-dialog" style="width: 350px;">
                                                                            <div class="modal-content">
                                                                                <div class="modal-header">
                                                                                    <asp:LinkButton ID="LinkButton6" class="close" runat="server" OnClick="CloseActiveClick">×</asp:LinkButton>
                                                                                    <h3 class="modal-title">Confirmation</h3>
                                                                                </div>
                                                                                <div class="modal-body">
                                                                                    <div class="row">
                                                                                        <div class="col-md-2" style="padding-top: 5px;">

                                                                                            <img src="../images/icons/Warning.png">
                                                                                        </div>
                                                                                        <div class="col-md-10" style="padding-left: 0;">
                                                                                            <asp:Label ID="lblStatus" Visible="True" runat="server"></asp:Label>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="modal-footer">
                                                                                    <asp:Button ID="btnChangeActive" CssClass="btn btn-primary" Width="20%" runat="server"
                                                                                        Text="Ok" OnClick="BtnChangeActiveClick" />
                                                                                    <asp:Button ID="btnChangeActiveCancel" CssClass="btn btn-default" Width="20%"
                                                                                        runat="server" Text="Cancel" OnClick="BtnChangeActiveCancelClick" Style="padding: 4px 8px" />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>
                                                        </asp:Panel>
                                                        <asp:Button runat="server" ID="HiddenForActive" Style="display: none" />
                                                        <TK1:ModalPopupExtender ID="ModalPopupExtenderActive" runat="server" TargetControlID="HiddenForActive"
                                                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="PanelCheckActiveStatus"
                                                            PopupDragHandleControlID="PanelCheckActiveStatus" Drag="true" BackgroundCssClass="bg">
                                                        </TK1:ModalPopupExtender>
                                                    </ContentTemplate>
                                                    <Triggers>
                                                        <asp:AsyncPostBackTrigger ControlID="btnChangeActive" EventName="Click" />
                                                        <asp:AsyncPostBackTrigger ControlID="btnChangeActiveCancel" />
                                                    </Triggers>

                                                </asp:UpdatePanel>

                                                <asp:Panel ID="panelCustom" runat="server" Style="margin: auto; display: none">
                                                    <asp:UpdatePanel ID="Updatepanel3" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True"
                                                        EnableViewState="False" RenderMode="Inline">
                                                        <ContentTemplate>
                                                            <div class="modal" style="display: block;">
                                                                <div class="modal-dialog" style="width: 647px">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <asp:LinkButton ID="lnkbtnClose" runat="server" CssClass="close" OnClick="CloseGroupWorkflowClick"
                                                                                CommandName="Close"> ×</asp:LinkButton>
                                                                            <h3 class="modal-title">Timeline View</h3>
                                                                        </div>
                                                                        <%-- <Triggers>
                         <asp:AsyncPostBackTrigger ControlID="TimerDisplayStatus" EventName="Tick" />
                         </Triggers>--%>
                                                                        <%--<iframe id="iframeworkflow" src="CustomParallelWorkflow.aspx" frameborder="0"
                                                                            width="625" height="472" runat="server" scrolling="no" noresize="noresize" style="background-color: #FFFFFF ! important; overflow-y: hidden; overflow-x: hidden;"></iframe>--%>
                                                                        <asp:HtmlIframe id="iframeworkflow" src="CustomParallelWorkflow.aspx" frameborder="0"
                                                                            width="625" height="472" runat="server" scrolling="no" noresize="noresize" style="background-color: #FFFFFF ! important; overflow-y: hidden; overflow-x: hidden;">
                                                                        </asp:HtmlIframe>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </ContentTemplate>
                                                        <%--<Triggers>
                        <asp:AsyncPostBackTrigger ControlID="ImageButtonMsg" EventName="Click" />
                        </Triggers>--%>
                                                    </asp:UpdatePanel>
                                                </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                        <asp:Panel ID="PanelAuthenticate" runat="server" Style="display: none">
                            <asp:UpdatePanel ID="Updatepanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true"
                                EnableViewState="False">
                                <ContentTemplate>
                                    <div class="modal" style="display: block;" aria-hidden="false" id="">
                                        <div class="modal-dialog" style="padding-top: 170px; width: 414px;">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <asp:LinkButton ID="LinkButton5" class="close" runat="server" OnClick="CloseClick">×</asp:LinkButton>
                                                    <h3 class="modal-title">
                                                        <img src="../Images/icons/key_white.png" style="margin-right: 8px;" />
                                                        Authenticate</h3>
                                                </div>
                                                <div class="modal-body">
                                                    <div runat="server" id="divPsdErrMsg" class="message error" visible="false">
                                                        <asp:Label ID="lblPsdErrorMsg" Visible="False" runat="server" ForeColor="Red"></asp:Label>
                                                    </div>
                                                    <div class="row" style="margin-left: 18px;">
                                                        <div class="col-xs-12 form-horizontal uniformjs" style="padding-left: 0px">
                                                            <div class="form-group">
                                                                <label class="col-xs-3 control-label">
                                                                    <asp:Label ID="lblDescription" runat="server" Text="Description:"></asp:Label></label>
                                                                <div class="col-xs-9">
                                                                    <asp:TextBox ID="txtDescription" runat="server" Text="" CssClass="form-control" EnableViewState="False" Width="90%" TextMode="MultiLine"></asp:TextBox>
                                                                </div>
                                                            </div>
                                                            <div class="form-group">
                                                                <label class="col-xs-3 control-label">
                                                                    <asp:Label ID="lblpassword" runat="server">Password</asp:Label>
                                                                </label>
                                                                <div class="col-xs-9">
                                                                    <asp:TextBox ID="txtpassword" Text="" CssClass="form-control  chk-caps" autocomplete="off" TextMode="Password" Width="90%"
                                                                        runat="server" EnableViewState="False" onblur="getHashData(this)" onfocus="clearText(this)"></asp:TextBox>
                                                                    <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ErrorMessage="*" ControlToValidate="txtpassword" ValidationGroup="psd"></asp:RequiredFieldValidator>--%>
                                                                        <span class="caps-error">Caps Lock is ON.</span>
                                                                     </div>
                                                            </div>
                                                            <div class="form-group" style="margin-bottom: 0px;">
                                                                <div class="col-xs-12 text-center col-lg-push-3">
                                                                    <asp:RadioButtonList ID="RadioWorkflowExecution" runat="server" CellPadding="3" CellSpacing="2"
                                                                        RepeatDirection="Horizontal">
                                                                        <asp:ListItem Selected="True" Value="1">Auto</asp:ListItem>
                                                                        <asp:ListItem Value="2">Steps</asp:ListItem>
                                                                    </asp:RadioButtonList>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <asp:Button ID="btnPasswordOk" CssClass="btn btn-primary" Width="20%" runat="server"
                                                        Text="Ok" OnClick="BtnPasswordOkClick" />
                                                    <asp:Button ID="BtnAuthenticationCancel" CssClass="btn btn-default" Width="20%" runat="server"
                                                        Text="Cancel" OnClick="BtnCancelClick" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                        <asp:Panel ID="PanelWaitForCredential" runat="server" Style="display: none">
                            <asp:UpdatePanel ID="Updatepanel5" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                <ContentTemplate>
                                    <div class="modal" style="display: block;" aria-hidden="false">
                                        <div class="modal-dialog" style="width: 450px;">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <asp:LinkButton ID="LinkButton3" class="close" runat="server" OnClick="CloseWaitForCredentialPopUpClick">×</asp:LinkButton>
                                                    <%--<h3 class="modal-title">User Credential</h3>--%>
                                                    <asp:Label ID="lblIpAddress" runat="server"></asp:Label>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-xs-12 form-horizontal uniformjs">
                                                            <%-- <div class="form-group">
                                                <label class="col-xs-3 control-label">
                                                    <asp:Label ID="Label7" runat="server" Text="Enter Credential For :"></asp:Label></label>
                                                <div class="col-xs-9">
                                                    <asp:Label ID="lblIpAddress" runat="server"></asp:Label>
                                                </div>
                                            </div>--%>
                                                            <div class="form-group">
                                                                <label class="col-xs-3 control-label">
                                                                    <asp:Label ID="Label5" runat="server" Text="User Name:"></asp:Label></label>
                                                                <div class="col-xs-9">
                                                                    <asp:TextBox ID="txtUserNameForCredential" runat="server" Text="" CssClass="form-control" EnableViewState="False"></asp:TextBox>
                                                                    <%--  <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" CssClass="error" ErrorMessage="Enter User Name" Display="Dynamic" ControlToValidate="txtUserNameForCredential"></asp:RequiredFieldValidator>--%>
                                                                </div>
                                                            </div>
                                                            <div class="form-group">
                                                                <label class="col-xs-3 control-label">
                                                                    <asp:Label ID="Label6" runat="server">Password</asp:Label>
                                                                </label>
                                                                <div class="col-xs-9">
                                                                    <asp:TextBox ID="txtPasswordForCredential" Text="" CssClass="form-control" autocomplete="off" TextMode="Password"
                                                                        runat="server" EnableViewState="False"></asp:TextBox>
                                                                    <%-- <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" CssClass="error" ErrorMessage="Enter Password Name" Display="Dynamic" ControlToValidate="txtPasswordForCredential"></asp:RequiredFieldValidator>--%>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <asp:Button ID="btnWaitForCredential" runat="server" Text="OK" CssClass="btn btn-primary" OnClick="btnWaitForCredentialClick" />
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                        <asp:Panel ID="PanelFailedActions" runat="server" Style="display: none">
                            <asp:Timer runat="server" ID="TimerFailAction" Interval="5000" OnTick="FailTimerTick" />
                            <asp:UpdatePanel ID="Updatepanel6" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                 <Triggers>
                                    <asp:AsyncPostBackTrigger ControlID="TimerFailAction" EventName="Tick" />
                                </Triggers>
                                <ContentTemplate>
                                    <div class="modal" style="display: block;" aria-hidden="false">
                                        <div class="modal-dialog" style="width: 680px;">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <asp:LinkButton ID="LinkButton4" runat="server" class="close" OnClick="closePanelFailedActions_Click">x                              
                                                    </asp:LinkButton>
                                                    <h3 class="modal-title">Failed Actions</h3>
                                                    <asp:Label ID="Label8" runat="server"></asp:Label>
                                                </div>
                                                <div class="modal-body small">
                                                    <asp:ListView ID="lvFailedPRDr" runat="server">
                                                        <ItemTemplate>
                                                            <div class="widget-head">
                                                                <div class="npl">
                                                                    <h5 class="heading npl"><strong>Workflow Profile Name - <%# Eval("ProfileName") %> </strong></h5>
                                                                </div>
                                                                <div class="pull-right npr">
                                                                    <%--<h5 class="heading"><strong>Username - <%# Eval("username") %> </strong></h5>--%>
                                                                    <h5 class="heading npr"><strong>User:
                                                                        <asp:Label ID="lblUsername" runat="server" Text='<%# Eval("username") %>' Visible='<%# IsSuperAdmin %>'></asp:Label></strong> </h5>
                                                                </div>
                                                              
                                                              
                                                            </div>
                                                            <asp:ListView ID="lvFailedAction" runat="server" OnItemCommand="lvFailedAction_ItemCommand" OnInit="lvFailedAction_Init">
                                                                <LayoutTemplate>
                                                                    <table id="table1" runat="server" class="table table-bordered table-condensed table-striped margin-bottom-none" width="100%">
                                                                        <thead>
                                                                            <tr>
                                                                                <th style="width: 20%">Action Name
                                                                                </th>
                                                                                <th style="width: 20%">Workflow Name
                                                                                </th>
                                                                                <th style="width: 30%">Error Message
                                                                                </th>
                                                                                <th style="width: 30%">Workflow Actions
                                                                                </th>
                                                                            </tr>
                                                                        </thead>
                                                                    </table>
                                                                    <%-- <div class="slim-scroll">--%>
                                                                    <table class="table table-bordered table-condensed table-striped " width="100%" style="table-layout: fixed;" id="lvFailedAction">
                                                                        <tbody>
                                                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                                                        </tbody>
                                                                    </table>
                                                                    <%-- </div>--%>
                                                                </LayoutTemplate>
                                                                <ItemTemplate>
                                                                    <tr>
                                                                        <td style="width: 20%;" class="tdword-wrap">
                                                                            <asp:Label ID="lblId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                                                            <asp:Label ID="lblGrpid" Visible="False" runat="server"  />
                                                                            
                                                                            <asp:Label ID="Label1" runat="server" ToolTip='<%# Eval("WorkflowActionName") %>'
                                                                                Text='<%# Eval("WorkflowActionName") %>' />

                                                                        </td>
                                                                        <td class="tdword-wrap" style="width: 20%">
                                                                            <asp:Label ID="lblWfName" runat="server" Text='<%#Eval("WorkflowName") %>' />
                                                                        </td>
                                                                        <td class="tdword-wrap" style="width: 30%">
                                                                            <asp:Label ID="Label7" runat="server" ToolTip='<%#Eval("Message") %>' Text='<%#Eval("Message") %>' />
                                                                        </td>
                                                                        <td style="width: 30%" class="small-element">
                                                                            <asp:Button ID="BtnParallelNext" CssClass="btn btn-primary col-xs-3" Width="25%" CommandName="Next" runat="server"
                                                                                Enabled='<%# ActiveProcessForFailedAction(Eval("Status"),Eval("ConditionActionId"),Eval("Id")) %>' Text="Next" />
                                                                            <asp:Button ID="BtnParallelRetry" CssClass="btn btn-primary col-xs-3" Width="25%" CommandName="Retry" runat="server"
                                                                                Enabled='<%# ActiveProcessForFailedAction(Eval("Status"),Eval("ConditionActionId"),Eval("Id")) %>' Text="Retry" />
                                                                            <asp:Button ID="BtnReload" CssClass="btn btn-primary col-xs-3" Width="32%" CommandName="Reload" runat="server"
                                                                                Enabled='<%# ActiveProcessForFailedAction("Reload",Eval("IsReload"),Eval("Id")) %>'
                                                                                Text="Reload" />
                                                                        </td>
                                                                    </tr>
                                                                </ItemTemplate>
                                                                <EmptyDataTemplate>
                                                                    <div class="row">
                                                                        <asp:Label ID="pageResult" runat="server" CssClass="text-danger" Text="No Records"></asp:Label>
                                                                    </div>

                                                                </EmptyDataTemplate>
                                                            </asp:ListView>

                                                        </ItemTemplate>
                                                    </asp:ListView>
                                                </div>
                                            </div>
                                            <%--       <div class="modal-footer">
                            </div>--%>
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
