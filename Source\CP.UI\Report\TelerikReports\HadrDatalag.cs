namespace CP.UI.Report.TelerikReports
{
    using CP.Common.DatabaseEntity;
    using log4net;
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Data;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;

    /// <summary>
    /// Summary description for MssqlDatalagReport.
    /// </summary>
    public partial class HadrDataLag : Telerik.Reporting.Report
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(HadrDataLag));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();

        public HadrDataLag()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }
        private void showtable()
        {
            try
            {
                var dataTable = new DataTable();

                //string strDate = Convert.ToDateTime(this.ReportParameters["iStartDate"].Value).ToString("dd-MM-yyyy");
                //string endDate = Convert.ToDateTime(this.ReportParameters["iEndDate"].Value).ToString("dd-MM-yyyy");
                //int infraObjectId = Convert.ToInt32(this.ReportParameters["iInfraObjectId"].Value);

                string strDate = (this.ReportParameters["iStartDate"].Value).ToString();
                int infraObjectId = Convert.ToInt32(this.ReportParameters["iInfraObjectId"].Value);
                string endDate = (this.ReportParameters["iEndDate"].Value).ToString();

                string endDate2 = Convert.ToDateTime(endDate).ToString("yyyy-MM-dd");
                string strDate1 = Convert.ToDateTime(strDate).ToString("yyyy-MM-dd");
                IList<HADR> HADRList = new List<HADR>();
                HADRList = Facade.GetHadrMonitorByDate(infraObjectId, strDate1, endDate2);

                if (HADRList != null && HADRList.Count > 0)
                {
                    dataTable.Columns.Add("Sr.No.");
                    dataTable.Columns.Add("PR_LogFile");
                    dataTable.Columns.Add("DR_LogFile");

                    dataTable.Columns.Add("Datalag");
                    dataTable.Columns.Add("CreateDate");
                    
                    _logger.Info("Data Mapping Start For Report.");
                    int i = 1;
                    foreach (HADR vsmoni in HADRList)
                    {
                        DataRow dr = dataTable.NewRow();
                        dr["Sr.No."] = i.ToString();
                        dr["PR_LogFile"] = vsmoni.PRLogFile != null ? vsmoni.PRLogFile : "N/A";
                        dr["DR_LogFile"] = vsmoni.DRLogFile != null ? vsmoni.DRLogFile : "N/A";


                       

                        // InfraObject InfraObj = Facade.GetInfraObjectById(Convert.ToInt32(ddlGroup.SelectedValue));
                        InfraObject InfraObj = Facade.GetInfraObjectById(infraObjectId);
                        BusinessFunction businessFtn = Facade.GetBusinessFunctionById(InfraObj != null ? InfraObj.BusinessFunctionId : 0);
                        TimeSpan conDatalag = TimeSpan.FromSeconds(Convert.ToDouble(businessFtn.ConfiguredRPO));


                        dr["Datalag"] = vsmoni.Datalag != null ? vsmoni.Datalag : "N/A";
                        dr["CreateDate"] = vsmoni.CreateDate != null ? Convert.ToString(vsmoni.CreateDate) : "N/A";
                        i++;
                        dataTable.Rows.Add(dr);

                    }
                }
                this.DataSource = dataTable;
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method, InnerException Message " + ex.InnerException.Message);
            }
        }

        private void HadrDataLag_NeedDataSource(object sender, EventArgs e)
        {
            showtable();

        }

       

        //private void HadrDataLagReport_NeedDataSource(object sender, EventArgs e)
        //{
        //    showtable();

        //}

        
    }
}