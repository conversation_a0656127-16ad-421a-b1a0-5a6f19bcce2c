﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="SiteConfiguration.aspx.cs" Inherits="CP.UI.SiteConfiguration" Title="Continuity Patrol :: Site Configuration" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
     <script src="../Script/EncryptDecrypt.js"></script>
     <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    
    <script src="../Script/chosen.jquery.js"></script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
     <style>
        .chosen-select + .chosen-container {
            width: 48.5% !important;
            opacity: 1 !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

             
                <h3><span class="site-icon"></span>
                    Site Configuration</h3>
             
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-xs-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="txtName">
                                        Site Name <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:TextBox ID="txtName" runat="server" class="form-control" TabIndex="1" AutoPostBack="true" OnTextChanged="TxtNameTextChanged"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvName" CssClass="error" runat="server" ErrorMessage="Enter Site Name" Display="Dynamic"
                                            ControlToValidate="txtName"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="rfvtxtName" CssClass="error" ControlToValidate="txtName" Display="Dynamic"
                                            runat="server" ErrorMessage="Invalid Characters"
                                            ValidationExpression="(?=.*[a-zA-Z])^[a-zA-Z0-9 ,.'@#!%&*_-]+$"></asp:RegularExpressionValidator>
                                        <asp:Label ID="lblSiteName" runat="server" CssClass="error" Display="Dynamic"></asp:Label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="txtLocation">
                                        Location <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:TextBox ID="txtLocation" class="form-control" runat="server" TabIndex="2"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" CssClass="error" ErrorMessage="Enter Location" Display="Dynamic"
                                            ControlToValidate="txtLocation"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="regtxtLocation" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtLocation" ErrorMessage="Invalid Characters"
                                    ValidationExpression="(?=.*[a-zA-Z])^[a-zA-Z0-9 ,.'@#!%&*_-]+$"></asp:RegularExpressionValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="ddlCompanyName">
                                        Company Name<span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlCompanyName" runat="server" TabIndex="3" CssClass="chosen-select col-md-6"
                                            data-style="btn-default">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvddlCompanyName" runat="server" CssClass="error" ControlToValidate="ddlCompanyName"
                                           Display="Dynamic" ErrorMessage="Select Company Name" InitialValue="000" > </asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="ddlSiteStatus">
                                        Site Status <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlSiteStatus" runat="server" TabIndex="4" CssClass="chosen-select col-md-6"
                                            data-style="btn-default">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvddlSiteStatus" runat="server" CssClass="error" ControlToValidate="ddlSiteStatus"
                                            InitialValue="0" Display="Dynamic" ErrorMessage=" Select Site Status"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="ddlType">
                                      Site Type <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:DropDownList ID="ddlType" runat="server" TabIndex="5" CssClass="chosen-select col-md-6"
                                            data-style="btn-default">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvType" runat="server" CssClass="error" ControlToValidate="ddlType"
                                            InitialValue="0" ErrorMessage="Select Site Type" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <hr />
                                <h4>Information of Site In-charge</h4>
                                <hr />
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="txtSiteIncharge">
                                        Site In-charge<span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:TextBox ID="txtSiteIncharge" class="form-control" runat="server" TabIndex="6"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvSic" runat="server" CssClass="error" ErrorMessage="Enter Site Incharge" Display="Dynamic"
                                            ControlToValidate="txtSiteIncharge"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="rfString" runat="server" CssClass="error" ControlToValidate="txtSiteIncharge"
                                            Display="Dynamic" ErrorMessage="Name Should not allow Special Characters" SetFocusOnError="True"
                                            ValidationExpression="^[a-zA-Z0-9\s]{1,100}$"></asp:RegularExpressionValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="txtAddress">
                                        Address<span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:TextBox ID="txtAddress" class="form-control" runat="server" TextMode="MultiLine"
                                            TabIndex="7" />
                                        <asp:RequiredFieldValidator ID="rqdAddress" runat="server" CssClass="error" ErrorMessage="Enter Address" Display="Dynamic"
                                            ControlToValidate="txtAddress"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="txtEmail">
                                        Email <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:TextBox ID="txtEmail" class="form-control" runat="server" TabIndex="8" AutoCompleteType="Disabled"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvEmail" ControlToValidate="txtEmail" runat="server" CssClass="error"
                                            ErrorMessage="Enter Email Address" Display="Dynamic"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="revEmail" runat="server" CssClass="error" ControlToValidate="txtEmail"
                                            ErrorMessage="Valid email address is required." ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                                            Display="Dynamic"></asp:RegularExpressionValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label" for="txtMbCountryCode">
                                        Mobile <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                                                            
                                        <asp:TextBox ID="txtMbCountryCode" placeholder="+91" class="form-control" runat="server" MaxLength="6" AutoCompleteType="Disabled"
                                            TabIndex="9" Width="7%" ></asp:TextBox>
                                                                              

                                        <asp:TextBox ID="txtMobile" class="form-control" runat="server" MaxLength="10" TabIndex="10"
                                            AutoCompleteType="Disabled" Width="40.5%"></asp:TextBox>
                                         <asp:RequiredFieldValidator ID="rfvmobcntrycode" ControlToValidate="txtMbCountryCode" runat="server" CssClass="error" Display="Dynamic"
                                            ErrorMessage="Please Enter Country Code"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="regmobcntrycode" runat="server" ControlToValidate="txtMbCountryCode" CssClass="error" 
                                            ErrorMessage="Please Enter Valid Country Code" ValidationExpression="^\+\d+$"
                                            Display="Dynamic"></asp:RegularExpressionValidator>
                                        <asp:RequiredFieldValidator ID="rfvMobile" ControlToValidate="txtMobile" runat="server" CssClass="error"
                                            ErrorMessage="*" Text="Enter Mobile Number" Display="Dynamic"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="revMobile" runat="server" ControlToValidate="txtMobile" CssClass="error"
                                            ErrorMessage="Valid Mobile No is required." ValidationExpression="[0-9]{4,10}"
                                            Display="Dynamic"></asp:RegularExpressionValidator>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr class="separator" />
                        <div class="form-actions row">
                            <div class="col-xs-5">
                                <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
                                <asp:Label ID="lblProfileResult" runat="server" Text="&nbsp;"></asp:Label>
                            </div>
                            <div class="col-xs-7">
                                <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="15%" runat="server" Text="Save" Style="margin-left:10px;"
                                    OnClick="BtnSaveClick" TabIndex="11" />
                                <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server"
                                    Text="Cancel" OnClick="BtnCancelClick" TabIndex="12" CausesValidation="False" />
                            </div>
                        </div>
                    </div>
                </div>
              
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>