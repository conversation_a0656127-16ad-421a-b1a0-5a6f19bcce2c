﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="InfraMonitorApplication.aspx.cs" Inherits="CP.UI.Admin.InfraMonitorApplication"
    Title="Continuity Patrol :: Infra Object Application Monitoring" %>

<%@ Register Src="../Controls/ZFSStorageReplicationMonitoring.ascx" TagName="ZFSReplicationMonitor_UserControl" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/EMCSRDFStarMonitoring.ascx" TagPrefix="uc1" TagName="EMCSRDFStarMonitoring" %>
<%@ Register Src="~/Controls/EMCSRDFSGMonitoring.ascx" TagPrefix="uc1" TagName="EMCSRDFSGMonitoring" %>
<%@ Register Src="~/Controls/HACMPPowerHAClusterDetails.ascx" TagPrefix="uc1" TagName="HACMPPowerHAClusterDetails" %>
<%@ Register Src="~/Controls/EMCUnityMonitoring.ascx" TagPrefix="uc1" TagName="EMCUnityMonitoring" %>

<%@ Register Src="~/Controls/RecoveryPointMultiMonitoringDetails.ascx" TagPrefix="uc1" TagName="RecoveryPointMultiMonitoringDetails" %>
<%@ Register Src="~/Controls/RSyncMonitorDetails.ascx" TagPrefix="uc1" TagName="RSyncMonitorDetails" %>
<%@ Register Src="~/Controls/VeeamReplicationMonitoringDetails.ascx" TagPrefix="uc2" TagName="VeeamReplicationMonitoringDetail" %>
<%@ Register Src="../Controls/HuaweiReplicationASync.ascx" TagName="HuaweiReplicationAsync"
    TagPrefix="uc1" %>
<%@ Register Src="../Controls/HuaweiReplicationSync.ascx" TagName="HuaweiReplicationSync"
    TagPrefix="uc1" %>
<%@ Register Src="~/Controls/AzureSiteDetailsMonitoring.ascx" TagPrefix="uc1" TagName="AzureMonitoring" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style>
        /* #ctl00_cphBody_widget2 {
            display: none;
        }*/

        #ctl00_cphBody_EMCSRDFSGMonitoring_ddlreplicationEMCSRDFSG {
            font-weight: 100;
            padding: 3px 12px;
            margin-top: 3px;
            width: 56%;
            margin-left: 114px;
        }

            #ctl00_cphBody_EMCSRDFSGMonitoring_ddlreplicationEMCSRDFSG .caret {
                border-top-color: rgba(0, 0, 0, 0.5);
            }


        #ctl00_cphBody_EMCSRDFSGMonitoring_ddlreplicationEMCSRDFSG {
            text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
            /* color: rgba(0, 0, 0, 0.6); */
            text-shadow: 0 1px 0 #fff;
            background-color: #efefef;
            background-image: -moz-linear-gradient(top, #f4f4f4, #e7e7e7);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f4f4f4), to(#e7e7e7));
            background-image: -webkit-linear-gradient(top, #f4f4f4, #e7e7e7);
            background-image: -o-linear-gradient(top, #f4f4f4, #e7e7e7);
            background-image: linear-gradient(to bottom, #f4f4f4, #e7e7e7);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff4f4f4', endColorstr='#ffe7e7e7', GradientType=0);
            border: 1px solid #cecece;
        }

        .no-pad #contentmonitor .inner-2x {
            padding: 0px !important;
        }

        .workflow-icon {
            display: inline-block;
            background-image: url(../../images/icon-blue/workflow_icon_new_blue.png);
            background-repeat: no-repeat;
            width: 16px;
            height: 16px;
            vertical-align: middle;
        }

        #ctl00_cphBody_divAzureLogs {
            width: 100%;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR" id="dvContent2" runat="server">

        <h3>
            <img src="../Images/monitor.png">
            Infra Object Application Monitor</h3>

        <div class="widget" runat="server" id="widget4">

            <div class="widget-head">
                <asp:Label ID="lblApplicationName" class="heading" runat="server" Text="Label" Style="padding-left: 5px !important;"></asp:Label>
            </div>

            <div class="widget-body innerAll inner-2x" runat="server" id="widget4body">
                <div id="contentmonitor" class="tabs-content" runat="server">

                    <div class="widget widget-tabs changetabsstruc" id="widget5" runat="server" visible="false">

                        <div class="widget-head">
                            <ul>
                                <li id="Li1" runat="server" class="active">
                                    <a href="#ZFSReplication" data-toggle="tab"><i></i>Replication Details</a></li>

                                <li id="Li2" runat="server" visible="false">
                                    <a href="#ctl00_cphBody_HACMPPowerHA" data-toggle="tab"><i></i>Cluster Details</a></li>

                                <li id="Li3" runat="server">
                                    <a href="#HACMPPowerHA" data-toggle="tab"><i></i>Cluster Details</a></li>

                            </ul>
                        </div>

                        <asp:UpdatePanel ID="updDatabaseDetails" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <div class="widget-body">
                                    <div class="tab-content">
                                        <div id="ZFSReplication" class="tab-pane active">
                                            <uc1:ZFSReplicationMonitor_UserControl ID="ZFSReplicationMonitor_UserControl" runat="server" Visible="true" />
                                        </div>

                                        <div class="tab-pane" id="HACMPPowerHA" runat="server">
                                            <uc1:HACMPPowerHAClusterDetails runat="server" Visible="true" ID="HACMPPowerHAClusterDetails" />
                                        </div>

                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>

                    </div>

                    <div id="content-monitor1" class="tabs-content">
                        <asp:Label ID="lblServerDetails" runat="server" Visible="false" class="heading" Text="Label"></asp:Label>
                        <table id="tblCommonComponent" class="table table-bordered table-white" width="100%" runat="server">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th class="col-md-4">DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td><span id="spnPrHostIcon" runat="server"></span>
                                        <asp:Label ID="lblPRHost" runat="server" Text="Label"></asp:Label>
                                    </td>
                                    <td><span id="spnDrHostIcon" runat="server"></span>
                                        <asp:Label ID="lblDRHost" runat="server" Text="Label"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td><span id="spnApphealthPR" runat="server" class="health-up"></span>
                                        <asp:Label ID="lblPRIP" runat="server" Text="Label"></asp:Label>
                                    </td>
                                    <td><span id="spnApphealthDR" runat="server" class="health-up"></span>
                                        <asp:Label ID="lblDRIP" runat="server" Text="Label"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table id="tblSnapComponent" class="table table-bordered table-primary" runat="server">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Snap mirror
                                    </th>
                                    <th class="col-md-4">Production Server</th>
                                    <th>DR Server</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td><span id="spnsnapPR" runat="server"></span>
                                        <asp:Label ID="lblSnapPRServer" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="spnsnapDR" runat="server"></span>
                                        <asp:Label ID="lblSnapDRServer" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Server IP/HostName
                                    </td>
                                    <td>
                                        <span id="healthPR" runat="server"></span>
                                        <asp:Label ID="lblSnapPRIP" runat="server"></asp:Label>
                                    </td>
                                    <td>
                                        <span id="healthDR" runat="server"></span>
                                        <asp:Label ID="lblSnapDRIP" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Storage Id
                                    </td>
                                    <td>
                                        <span class="icon-storageDR"></span>
                                        <asp:Label ID="lblPRStorageId" runat="server"> </asp:Label>
                                    </td>
                                    <td>
                                        <span class="icon-storageDR"></span>
                                        <asp:Label ID="lblDRStorageId" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Volume
                                    </td>
                                    <td class="text-indent"><span class="icon-storageDR"></span>
                                        <asp:Label ID="lblPRVolume" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="icon-storageDR"></span>
                                        <asp:Label ID="lblDRVolume" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table id="tblEc2S3datasynccompMonitor" class="table table-bordered table-primary" runat="server">
                            <thead>
                                <tr>
                                    <th colspan="2">
                                        <img src="../Images/icons/Server1.png" alt="App Server" />
                                        Source App Server
                                    </th>
                                    <th colspan="2">
                                        <img src="../Images/icons/sync-cloud-white.png" width="20px" class="vertical-sub" alt="Cloud" />
                                        Target AWS Cloud
                                    </th>
                                </tr>
                            </thead>
                            <tr>
                                <td>Server Host Name
                                </td>
                                <td>
                                    <span class="host-icon">&nbsp;</span>
                                    <asp:Label ID="lblserverhostname" runat="server" Text=""></asp:Label>
                                </td>
                                <td>EC2 Instance ID </td>
                                <td>
                                    <span class="id-icon"></span>
                                    <asp:Label ID="lblinstanceId" runat="server" Text=""></asp:Label>

                                </td>

                            </tr>
                            <tr>
                                <td>IP Address/HostName
                                </td>
                                <td>
                                    <asp:Label ID="lblserverstatus" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblec2s3ipaddress" runat="server" Text=""></asp:Label>
                                </td>

                                <td>EC2 Instance Status </td>
                                <td>

                                    <asp:Label ID="statusIcon" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblinstancestatus" runat="server" Text=""></asp:Label>

                                </td>

                            </tr>
                            <tr>
                                <td>Operating System
                                </td>
                                <td>
                                    <asp:Label ID="OsIcon" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblops" runat="server" Text=""></asp:Label>
                                </td>
                                <td>EC2 Instance Type</td>
                                <td><span class="icon-disk-type">&nbsp;</span>
                                    <asp:Label ID="lblEc2InstanceType" runat="server" Text=""></asp:Label></td>

                            </tr>
                            <tr>
                                <td>Source Data Path(s)
                                </td>
                                <td><span class="icon-numbering"></span>
                                    <asp:Label ID="lblsrcdtpath" runat="server" Text=""></asp:Label>
                                </td>
                                <td>S3 Bucket Location 
                                </td>
                                <td>
                                    <span class="site-icon"></span>
                                    <asp:Label ID="lblbucketloc" runat="server" Text=""></asp:Label>

                                </td>

                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td>S3 Bucket Creation TimeStamp </td>
                                <td><span class="icon-Time"></span>
                                    <asp:Label ID="lbltmestamp" runat="server" Text=""></asp:Label></td>
                            </tr>
                        </table>
                        <table id="tblSRMVMwareComponent" runat="server" class="table table-bordered table-white" width="100%" style="display: none;">
                            <thead>
                                <tr>
                                    <th>Site Level Monitoring </th>
                                    <th class="col-md-4">Protected Site
                                    </th>
                                    <th class="col-md-4">Recovery Site
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Site Name
                                    </td>
                                    <td><span id="Span1" runat="server" class="site-icon">&nbsp;</span>
                                        <asp:Label ID="lblSitePR" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span2" runat="server" class="site-icon">&nbsp;</span>
                                        <asp:Label ID="lblSiteDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Site Status
                                    </td>
                                    <td><span id="Span5" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblSiteStatusPR" runat="server"></asp:Label>
                                    </td>
                                    <td><span id="Span6" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblSiteStatusDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr id="Tr4" runat="server">
                                    <td>vCenter Server - IP Address/HostName :Port
                                    </td>
                                    <td class="text-indent"><span id="Span7" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblvCenterServerPR" runat="server"></asp:Label>
                                        , 
                                    <asp:Label ID="lblvCenterPortPR" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span8" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblvCenterServerDR" runat="server"></asp:Label>
                                        , 
                                    <asp:Label ID="lblvCenterPortDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>SRM Server - IP Address/HostName : Port
                                    </td>
                                    <td class="text-indent"><span id="Span71" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblSRMServerPR" runat="server"></asp:Label>
                                        ,
                                    <asp:Label ID="lblSRMServerPortPR" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span72" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblSRMServerDR" runat="server"></asp:Label>
                                        ,
                                    <asp:Label ID="lblSRMServerPortDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr visible="false">
                                    <td>vCenter Version, Build
                                    </td>
                                    <td class="text-indent"><span id="Span73" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblvCrenterVersionPR" runat="server"></asp:Label>
                                        ,
                                    <asp:Label ID="lblvCrenterBuildPR" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span74" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblvCenterVersionDR" runat="server"></asp:Label>
                                        ,
                                    <asp:Label ID="lblvCrenterBuildDR" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>SRM Version
                                    </td>
                                    <td class="text-indent"><span id="Span75" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblSRMVersionPR" runat="server"></asp:Label>
                                        <asp:Label ID="lblSRMBuildPR" runat="server" Visible="false"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span76" runat="server" class="icon-log">&nbsp;</span>
                                        <asp:Label ID="lblSRMVersionDR" runat="server"></asp:Label>
                                        <asp:Label ID="lblSRMBuildDR" runat="server" Visible="false"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table id="tblBase24ApplicationDetail" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Server Name
                                    </td>
                                    <td class="text-indent"><span id="base24PR" runat="server" class="msexchangedag-icon"></span><span id="applicationImgPrServerId" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblApplicationPrServer" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="base24DR" runat="server" class="msexchangedag-icon"></span><span id="applicationImgDrServerId" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblApplicationDrServer" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblApplicationPrIpAddress" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblApplicationDrIpAddress" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr id="trmonitoringworkflow" runat="server">
                                    <td>MonitoringWorkflow
                                    </td>
                                    <td class="text-indent"><span class="workflow-icon"></span>
                                        <asp:Label ID="lblWorkflow" runat="server" CssClass="command"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span class="workflow-icon"></span>
                                        <asp:Label ID="lblDrWorkflowStatus" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr id="trapppath" runat="server" visible="false">
                                    <td>App Path
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblPrApplicationPath" runat="server" Text=""></asp:Label>
                                    </td>

                                    <td class="text-indent">
                                        <asp:Label ID="lblDrApplicationPath" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr id="trAppStatus" runat="server">
                                    <td>Application Status
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblIconPR" runat="server"></asp:Label>
                                        <asp:Label ID="lblPRAppStatus" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label ID="lblIconDR" runat="server"></asp:Label>
                                        <asp:Label ID="lblDRAppStatus" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table id="tblTPRCMonitoring" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Component Monitor
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>IP Address/HostName
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblTPIPPR" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblTPIPDR" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Name
                                    </td>
                                    <td class="text-indent"><span id="Span89" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRGroupName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span90" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRGroupName" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Type
                                    </td>
                                    <td class="text-indent"><span id="Span91" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRType" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span92" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRType" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>State
                                    </td>
                                    <td class="text-indent"><span id="Span93" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRState" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span94" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRState" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Status
                                    </td>
                                    <td class="text-indent"><span id="Span95" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRStatus" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span96" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRStatus" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Locations
                                    </td>
                                    <td class="text-indent"><span id="Span97" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRlocations" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span98" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRlocations" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Copy Sets
                                    </td>
                                    <td class="text-indent"><span id="Span99" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRCopySts" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span100" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRCopySts" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Copying 
                                    </td>
                                    <td class="text-indent"><span id="Span101" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRCopy" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span102" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRCopy" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Recoverable
                                    </td>
                                    <td class="text-indent"><span id="Span103" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRRecov" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span104" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRRecov" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Active Host
                                    </td>
                                    <td class="text-indent"><span id="Span105" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRhost12" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span106" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRhost12" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Error Count
                                    </td>
                                    <td class="text-indent"><span id="Span107" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRErrCount" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span108" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRErrCount" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Description 
                                    </td>
                                    <td class="text-indent"><span id="Span109" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRDes" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span110" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRDes" runat="server" Text="NA"></asp:Label>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                        <table id="tblSVCReplicationMonitoringSummaryLayout" runat="server" class="table table-bordered table-primary" style="display: none">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Replication Monitoring
                                    </th>
                                    <th class="col-md-4">Production Server
                                    </th>
                                    <th>DR Server
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Relationship Name 
                                    </td>
                                    <td class="text-indent"><span id="Span61" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCRMRelationshipName" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent"><span id="Span14" runat="server">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCRMRelationshipName" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Group Name
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCRMGroupName" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCRMGroupName" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Relationship Primary Value 
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCRMRelationshipPrimaryValue" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCRMRelationshipPrimaryValue" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Master Change Volume Name
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCRMMasterChangeVolumeName" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCRMMasterChangeVolumeName" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Auxiliary Change Volume Name 
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCRMAuxiliaryChangeVolumeName" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCRMAuxiliaryChangeVolumeName" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Relationship State 
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCRMRelationshipState" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCRMRelationshipState" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Relationship Progress 
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblPRSVCRMRelationshipProgress" runat="server"></asp:Label>
                                    </td>
                                    <td><span class="health-up">&nbsp;</span>
                                        <asp:Label ID="lblDRSVCRMRelationshipProgress" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div>
                            <div style="margin-bottom: 10px;">
                                <asp:Label ID="lblEmcISilonnm" runat="server" Visible="false" class="heading" Style="padding-left: 5px !important;"></asp:Label>
                            </div>
                            <div id="divEmcISilonMonitoring_scroll" runat="server" class="slim-scroll chat-items" data-scroll-max-height="450px" data-scroll-size="0" visible="false">
                                <div id="divEmcISilonMonitoring" runat="server" visible="false">
                                </div>
                            </div>

                        </div>
                    </div>

                </div>

                <div id="divAzureLogs" runat="server" style="display: none">
                    <uc1:AzureMonitoring ID="AzureMonitoring" runat="server" />
                </div>
            </div>

        </div>

        <div class="widget" runat="server" id="widget2">

            <div class="widget-head">
                <asp:Label ID="lblReplicationName" class="heading" runat="server" Style="padding-left: 5px !important;"></asp:Label>
            </div>

            <div class="widget-body innerAll inner-2x">
                <div id="repligmcontent" class="tabs-content ">

                    <table id="tblGlobalMirror" class="table table-bordered table-white" width="100%"
                        runat="server">

                        <tbody>
                            <tr>
                                <td class="col-md-4">ID
                                </td>
                                <td class="col-md-8"><span class="id-icon"></span>
                                    <asp:Label ID="lblGMRID" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Master Count
                                </td>
                                <td><span class="count-icon"></span>
                                    <asp:Label ID="lblMasterCount" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Master Session ID
                                </td>
                                <td><span class="id-icon"></span>
                                    <asp:Label ID="lblMasterSessionID" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Copy State
                                </td>
                                <td><span class="icon-disk-type"></span>
                                    <asp:Label ID="lblHealthIcon" runat="server"></asp:Label>
                                    <asp:Label ID="lblCopyState" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Fatal Reason
                                </td>
                                <td><span class="reason-icon"></span>
                                    <asp:Label ID="lblFatal" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>CG Interval Time (seconds)
                                </td>
                                <td><span class="icon-time"></span>
                                    <asp:Label ID="lblCGIntervalTime" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Coord. Time (milliseconds)
                                </td>
                                <td><span class="icon-time"></span>
                                    <asp:Label ID="lblCordTime" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Max CG Drain Time (seconds)
                                </td>
                                <td><span class="icon-time"></span>
                                    <asp:Label ID="lblMaxDrainTime" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Current Time
                                </td>
                                <td><span class="icon-time"></span>
                                    <asp:Label ID="lblCurrentTime" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>CG Time
                                </td>
                                <td><span class="icon-time"></span>
                                    <asp:Label ID="lblCGTime" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Successful CG Percentage
                                </td>
                                <td><span class="percentage-icon"></span>
                                    <asp:Label ID="lblCGPerc" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>FlashCopy Sequence Number
                                </td>
                                <td><span class="sequence-icon"></span>
                                    <asp:Label ID="lblseqNo" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Master ID
                                </td>
                                <td><span class="id-icon"></span>
                                    <asp:Label ID="lblMasterId" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table id="tblEMCSRDF" class="table table-bordered table-white" width="100%" runat="server" style="display: table;">
                        <thead>
                            <tr>
                                <th colspan="2">Replication Monitor
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Replication Type
                                </td>
                                <td class="col-md-8"><span class="replication-file-icon"></span>
                                    <asp:Label ID="lblReplicationType" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Device Group Name
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disks">&nbsp;</span>
                                    <asp:Label ID="lblDeviceGroupName" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 34%">Disk Groups Type
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disk-type">&nbsp;</span>
                                    <asp:Label ID="lblDeviceGroupType" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Disk Groups Symmetrix ID
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disk-symmetrix">&nbsp;</span>
                                    <asp:Label ID="lblDiskGroupsSymmetrixId" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Remote Symmetrix ID
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disk-remote">&nbsp;</span>
                                    <asp:Label ID="lblRemoteSymmtrix" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>RDF (RA) Group Number
                                </td>
                                <td class="text-indent">
                                    <span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblRdfGroupNumber" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Device State
                                </td>
                                <td>
                                    <span class="session-icon">&nbsp;</span>
                                    <asp:Label ID="lblDeviceState" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Pending Tracks
                                </td>
                                <td class="text-indent">

                                    <span class="icon-tracks">&nbsp;</span>
                                    <asp:Label ID="lblPendingTraks" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Lag
                                </td>
                                <td class="text-indent">

                                    <span class="icon-Time">&nbsp;</span>
                                    <asp:Label ID="lblAppDataLg" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table id="tblEMCSRDFCG" class="table table-bordered table-white" width="100%" runat="server" style="display: none;">
                        <thead>
                            <tr>
                                <th colspan="2">Replication Monitor
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Replication Type
                                </td>
                                <td class="col-md-8"><span class="replication-file-icon"></span>
                                    <asp:Label ID="lblReplicationTypeCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Device Group Name
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disks">&nbsp;</span>
                                    <asp:Label ID="lblDeviceGroupNameCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 34%">Disk Groups Type
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disk-type">&nbsp;</span>
                                    <asp:Label ID="lblDeviceGroupTypeCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Disk Groups Symmetrix ID
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disk-symmetrix">&nbsp;</span>
                                    <asp:Label ID="lblDiskGroupsSymmetrixIdCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Remote Symmetrix ID
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disk-remote">&nbsp;</span>
                                    <asp:Label ID="lblRemoteSymmtrixCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>RDF (RA) Group Number
                                </td>
                                <td class="text-indent">
                                    <span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblRdfGroupNumberCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Device State
                                </td>
                                <td>
                                    <span class="session-icon">&nbsp;</span>
                                    <asp:Label ID="lblDeviceStateCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Pending Tracks
                                </td>
                                <td class="text-indent">

                                    <span class="icon-tracks">&nbsp;</span>
                                    <asp:Label ID="lblPendingTraksCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Lag
                                </td>
                                <td class="text-indent">

                                    <span class="icon-Time">&nbsp;</span>
                                    <asp:Label ID="lblAppDataLgCG" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div runat="server" id="divPstatemonitor" visible="false">
                        <h5 style="font-size: 16px;" id="divPstatemonitorHead" runat="server">Replication State Monitoring</h5>

                        <table style="table-layout: fixed; width: 100%" id="tblPstateMoniter" runat="server" class="table table-bordered table-condensed table-white margin-bottom-none">
                            <thead>
                                <tr>
                                    <th style="width: 33.33%;">Group Name</th>
                                    <th style="width: 33.33%;">Component</th>
                                    <th style="width: 33.33%;"></th>

                                </tr>
                            </thead>
                        </table>

                        <div id="divRepeaterPstate" runat="server">
                            <table style="table-layout: fixed; width: 100%" class="table table-bordered table-condensed table-white">
                                <tbody>
                                    <asp:Repeater runat="server" ID="rptPStateMonitor">
                                        <HeaderTemplate></HeaderTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td style="width: 33.33%; vertical-align: middle;" rowspan="7">
                                                    <asp:Label runat="server" ID="lblComphead" Text='<%#Eval("GGroupName") %>'></asp:Label></td>
                                                <td style="width: 33.33%;" class="tdword-wrap">
                                                    <asp:Label runat="server" ID="cgGroup" Text='<%#Eval("GGroupName") %>'></asp:Label>
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStateMonitorGroupName" runat="server" Text='<%#Eval("GGroupName") %>'> </asp:Label>
                                                    <asp:Label ID="lblPStateMonitorInfraobject" runat="server" Text='<%#Eval("InfraObjectId") %>' Visible="false"> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Transfer-Source
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStateMonitorTransferSource" runat="server" Text='<%#Eval("TransferSource") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>
                                                <td style="width: 33.33%;" class="tdword-wrap">Copy
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStateMonitorCopy" runat="server" Text='<%#Eval("Copy") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Journal
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStateMonitorJournal" runat="server" Text='<%#Eval("Journal") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 35%;" class="tdword-wrap">Storage-Access
                                                </td>
                                                <td style="width: 35%;">
                                                    <asp:Label ID="lblPStateMonitorStorageAccess" runat="server" Text='<%#Eval("StorageAccess") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Link
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStateMonitorLink" runat="server" Text='<%#Eval("Link") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Data-Transfer Status
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStateMonitorDataTransferStatus" runat="server" Text='<%#Eval("DataTransferStatus") %>'> </asp:Label>
                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <FooterTemplate></FooterTemplate>
                                    </asp:Repeater>
                                </tbody>
                            </table>
                        </div>

                        <div runat="server" id="divPStatisticMonitor">
                            <h5 style="font-size: 16px;">Replication Statistic Monitoring</h5>
                            <table style="table-layout: fixed; width: 100%" class="table table-bordered table-condensed table-white margin-bottom-none">
                                <thead>
                                    <tr>
                                        <th style="width: 33.33%;">Group Name</th>
                                        <th style="width: 33.33%;">Component
                                        </th>
                                        <th style="width: 33.33%;"></th>
                                    </tr>
                                </thead>
                            </table>

                            <table style="table-layout: fixed; width: 100%" class="table  table-bordered table-condensed table-white">
                                <tbody>
                                    <asp:Repeater runat="server" ID="rptPStistictMonitor">
                                        <HeaderTemplate></HeaderTemplate>
                                        <ItemTemplate>

                                            <tr>
                                                <td style="width: 33.33%; vertical-align: middle;" rowspan="12">
                                                    <asp:Label ID="lblpcggroupname" runat="server" Text='<%#Eval("CGGroupName") %>'> </asp:Label></td>
                                                <td style="width: 33.33%;" class="tdword-wrap">GGroup Name
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticCGGroupName" runat="server" Text='<%#Eval("CGGroupName") %>'> </asp:Label>
                                                    <asp:Label ID="lblPStatisticInfraObjectId" runat="server" Visible="false" Text='<%#Eval("InfraObjectId") %>'> </asp:Label>
                                                </td>
                                            </tr>
                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Usage
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticUsage" runat="server" Text='<%#Eval("Usage") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>
                                                <td style="width: 33.33%;" class="tdword-wrap">Total
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticTotal" runat="server" Text='<%#Eval("Total") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Last Image
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticLastImage" runat="server" Text='<%#Eval("LastImage") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Current Image
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticCurrentImage" runat="server" Text='<%#Eval("CurrentImage") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Protection Window
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticProtectionWindow" runat="server" Text='<%#Eval("ProtectionWindow") %>'> </asp:Label>
                                                </td>
                                            </tr>

                                            <tr>

                                                <td style="width: 33.33%;" class="tdword-wrap">Journal Lag
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticJournalLag" runat="server" Text='<%#Eval("Journal_Lag") %>'> </asp:Label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 33.33%;" class="tdword-wrap">Status
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticStatus" runat="server" Text='<%#Eval("Status") %>'> </asp:Label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 33.33%;" class="tdword-wrap">Replication Lag Time 
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticReplcationLagtime" runat="server" Text='<%#Eval("ReplicationLagTime") %>'> </asp:Label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 33.33%;" class="tdword-wrap">Replication Lag Data 
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticReplcationLagData" runat="server" Text='<%#Eval("ReplicationLagData") %>'> </asp:Label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 33.33%;" class="tdword-wrap">Replication Lag Writes 
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticReplcationLagWriters" runat="server" Text='<%#Eval("ReplicationLagWriter") %>'> </asp:Label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="width: 33.33%;" class="tdword-wrap">Data Lag
                                                </td>
                                                <td style="width: 33.33%;">
                                                    <asp:Label ID="lblPStatisticLag" runat="server" Text='<%#Eval("DataLag") %>'> </asp:Label>
                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <FooterTemplate></FooterTemplate>
                                    </asp:Repeater>
                                </tbody>
                            </table>

                        </div>

                    </div>

                    <table id="tbleBDRReplicationDetailsMoni" class="table table-bordered table-white" width="100%" runat="server"
                        style="display: table;">
                        <thead>
                            <tr>
                                <th class="col-md-4">Replication Monitor
                                </th>
                                <th class="col-md-4">Source
                                </th>
                                <th class="col-md-4">Target
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Profile Name
                                </td>
                                <td class="col-md-8" colspan="2"><span class=""></span>
                                    <asp:Label ID="lblebdrProfileName" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Host
                                </td>
                                <td class="text-indent">
                                    <span class="">&nbsp;</span>
                                    <asp:Label ID="lblebdrsourceHost" runat="server"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span class="">&nbsp;</span>
                                    <asp:Label ID="lblebdrTargetHost" runat="server"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>Os Type
                                </td>
                                <td class="text-indent">
                                    <span class="">&nbsp;</span>
                                    <asp:Label ID="lblprosicoebdr" runat="server"></asp:Label>
                                    <asp:Label ID="lbleBDROsType" runat="server"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span class="">&nbsp;</span>
                                    <asp:Label ID="lbldrosicoebdr" runat="server"></asp:Label>
                                    <asp:Label ID="lbleBDROsTypeDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Machin Name
                                </td>
                                <td class="text-indent" colspan="2">
                                    <span class="">&nbsp;</span>
                                    <asp:Label ID="lbleBDRMachinName" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Status
                                </td>
                                <td class="text-indent" colspan="2">
                                    <span class="">&nbsp;</span>
                                    <asp:Label ID="lblstatusiconforebdr" runat="server"></asp:Label>
                                    <asp:Label ID="lblebDRStatus" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Health
                                </td>
                                <td class="text-indent" colspan="2">

                                    <span class="">&nbsp;</span>
                                    <asp:Label ID="lbleBDRHealthicon" runat="server"></asp:Label>
                                    <asp:Label ID="lblHealth" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table id="tblOracleDataGuard" class="table table-bordered table-white" width="100%"
                        runat="server" style="display: none;">
                        <thead>
                            <tr>
                                <th class="side1">Replication Monitor
                                </th>
                                <th>Production Server
                                </th>
                                <th>DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Replication Type
                                </td>
                                <td class="col-md-8"><span class="replication-file-icon"></span>
                                    <asp:Label ID="lblOracleDataguardRepType" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Replication Mode
                                </td>
                                <td class="text-indent"><span class="replication-file-icon"></span>
                                    <asp:Label ID="lbliconOracleModePR" runat="server">&nbsp;</asp:Label>
                                    <asp:Label ID="lblOracleDataguardRepModePR" runat="server"></asp:Label>
                                </td>
                                <td class="text-indent"><span class="replication-file-icon"></span>
                                    <asp:Label ID="lbliconOracleModeDR" runat="server">&nbsp;</asp:Label>
                                    <asp:Label ID="lblOracleDataguardRepModeDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Service Name
                                </td>
                                <td class="text-indent">
                                    <span class="icon-service">&nbsp;</span>
                                    <asp:Label ID="lblOracleDataguardRepServiceNamePR" runat="server"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span class="icon-service">&nbsp;</span>
                                    <asp:Label ID="lblOracleDataguardRepServiceNameDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Protection Mode
                                </td>
                                <td class="text-indent">
                                    <span class="icon-protection-mode">&nbsp;</span>
                                    <asp:Label ID="lblOracleDataguardRepProtectionModePR" runat="server"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span class="icon-protection-mode">&nbsp;</span>
                                    <asp:Label ID="lblOracleDataguardRepProtectionModeDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>DataGuard Status
                                </td>
                                <td class="text-indent">
                                    <span class="Replicating">&nbsp;</span>
                                    <asp:Label ID="lblOracleDataguardRepStatusPR" runat="server"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span class="icon-wait">&nbsp;</span>
                                    <asp:Label ID="lblOracleDataguardRepStatusDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table id="tblSnapMirriorReplication" class="table table-bordered table-primary"
                        width="100%" runat="server" style="display: none;">
                        <thead>
                            <tr>
                                <th colspan="2">Replication Monitor
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Replication Type
                                </td>
                                <td colspan="2">
                                    <span id="Span11" class="replication-file-icon" runat="server">&nbsp;</span>
                                    <span>
                                        <asp:Label ID="lblsnapmirrorReplicationtype" runat="server"></asp:Label></span>
                                </td>
                            </tr>
                            <tr>
                                <td>Source
                                </td>
                                <td colspan="2">
                                    <span id="Span15" class="icon-computer" runat="server">&nbsp;</span>
                                    <span>
                                        <asp:Label ID="lblsource" runat="server"></asp:Label>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>Destination
                                </td>
                                <td class="text-indent">
                                    <span id="Span16" class="icon-drcomputer" runat="server">&nbsp;</span>
                                    <span>
                                        <asp:Label ID="lblDestination" runat="server"></asp:Label>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>State
                                </td>
                                <td class="text-indent">
                                    <span id="Span17" class="icon-fastcopy" runat="server">&nbsp;</span>
                                    <span>
                                        <asp:Label ID="lblState" runat="server"></asp:Label>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>Lag
                                </td>
                                <td class="text-indent">
                                    <span id="Span18" class="icon-Time" runat="server">&nbsp;</span>
                                    <asp:Label ID="lblLag" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Status
                                </td>
                                <td class="text-indent">
                                    <span id="Span19" class="icon-idle" runat="server">&nbsp;</span>
                                    <span>
                                        <asp:Label ID="lblStatus" runat="server"></asp:Label>
                                    </span>
                                </td>
                            </tr>

                        </tbody>
                    </table>

                    <table id="tblEc2S3datasyncReplication" class="table table-bordered table-white" width="100%" runat="server"
                        style="display: none;">
                        <thead>
                            <tr>
                                <th colspan="2">Replication Monitor
                                </th>
                            </tr>
                        </thead>
                        <tbody>

                            <tr>
                                <td>Source Server Path
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disks">&nbsp;</span>
                                    <asp:Label ID="lblsrcpath" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Destination S3 Path
                                </td>
                                <td class="text-indent">
                                    <span class="icon-disks">&nbsp;</span>
                                    <asp:Label ID="lbldespath" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Replication Status
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="Iconreplistatus" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblrepstatus" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table id="tblMSSQLDoubletek" class="table table-striped table-condensed table-bordered table-responsive monitortable font" runat="server" style="display: table;">
                        <thead>
                            <tr>
                                <th style="border: 0px !important;">Replication Monitor 
                                </th>
                                <th style="border: 0px !important;"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Job Name</td>
                                <td><span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblJobName" runat="server" Text=""></asp:Label>
                                </td>

                            </tr>
                            <tr>
                                <td class="col-md-4">Replication Status</td>
                                <td><span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblReplicationStatus" runat="server" Text=""></asp:Label>
                                </td>

                            </tr>
                            <tr>
                                <td class="col-md-4">Source Server</td>
                                <td><span class="host-icon"></span>
                                    <asp:Label ID="lblSourceServer" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-md-4">Target Server</td>
                                <td><span class="host-icon"></span>
                                    <asp:Label ID="lblTargetserver" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-md-4">Job Type</td>
                                <td><span class="Type_Icon"></span>
                                    <asp:Label ID="lblJobType" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-md-4">Replication Queue (Bytes)</td>
                                <td><span class="icon-numbering pull-left"></span>
                                    <asp:Label ID="lblRepliQueue" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-md-4">Job Activity</td>
                                <td><span class="icon-sync"></span>
                                    <asp:Label ID="lblJobActivity" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-md-4">Mirror Status</td>
                                <td><span class="notification_icon_blue"></span>
                                    <asp:Label ID="lblMirrorStatus" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-md-4">Mirror Percent Complete</td>
                                <td><span class="percentage-icon"></span>
                                    <asp:Label ID="lblMirrorPerComplete" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-md-4">Recovery Point Latency</td>
                                <td><span class="icon-sync"></span>
                                    <asp:Label ID="lblRecoveryPoint" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div id="divHitachiURReplication" runat="server" visible="false">
                        <table id="tblHitachiURReplication" class="table table-striped table-condensed table-bordered table-responsive" runat="server">
                            <thead>
                                <tr>
                                    <th class="col-md-4">Replication Monitor </th>
                                    <th class="col-md-4">Production
                                    </th>
                                    <th>DR
                                    </th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td>Storage ID
                                    </td>
                                    <td class="text-indent">
                                        <span id="span3" class="icon-storagePR" runat="server">&nbsp;&nbsp;</span>
                                        <asp:Label ID="lblHitachiURPRStorage" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span id="span4" class="icon-storageDR" runat="server">&nbsp;&nbsp;</span>

                                        <asp:Label ID="lblHitachiURDRStorage" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>HORCOM Instance
                                    </td>
                                    <td class="text-indent">
                                        <span class="icon-numbering">&nbsp;</span>
                                        <asp:Label ID="lblHitachiURPRHORCOMInstance" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <span class="icon-numbering">&nbsp;</span>
                                        <asp:Label ID="lblHitachiURDRHORCOMInstance" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>HORCOM Status
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label class="Replicating" runat="server" ID="spanPrHORCOMStatus">&nbsp;&nbsp;</asp:Label>
                                        <asp:Label ID="lblPrHORCOMStatus" runat="server"></asp:Label>
                                    </td>
                                    <td class="text-indent">
                                        <asp:Label class="icon-async" runat="server" ID="spanDrHORCOMStatus">&nbsp;&nbsp;</asp:Label>
                                        <asp:Label ID="lblDrHORCOMStatus" runat="server"></asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Average Copy Pending Timer
                                    </td>
                                    <td colspan="2" class="text-indent"><span class="icon-Time">&nbsp;&nbsp;</span>
                                        <asp:Label ID="lblAverageCopyPendingTimer" runat="server"></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <asp:ListView ID="lstHitachiURDeviceMonitoring" runat="server" OnItemDataBound="LvHitachiDeviceItemDataBound">
                            <LayoutTemplate>
                                <table id="tblHitachiURDeviceMonitoring" class="table table-striped table-condensed table-bordered table-responsive">
                                    <thead>
                                        <tr>
                                            <th colspan="3">Device Group:
                                             <asp:Label ID="lblDeviceGroupName" runat="server"></asp:Label></th>
                                        </tr>
                                        <tr>
                                            <th class="col-md-4">P-VOL/ S-VOL </th>
                                            <th class="col-md-4">Lun ID
                                            </th>
                                            <th>Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td>

                                        <asp:Label ID="lblType" runat="server" Text='<%# Eval("Type") %>'></asp:Label>
                                    </td>
                                    <td>

                                        <asp:Label ID="lblLunsId" runat="server" Text='<%# Eval("LunsId") %>'></asp:Label>
                                    </td>
                                    <td class="text-indent">

                                        <asp:Label ID="Label38" runat="server" CssClass='<%#GetDeviceClass(Eval("VolumeStatus")) %>'>&nbsp;&nbsp;</asp:Label>
                                        <asp:Label ID="lblVolumeStatus" runat="server" Text='<%# Eval("VolumeStatus") %>'> </asp:Label>
                                        <asp:HiddenField ID="hdfDeviceName" runat="server" Value='<%# Eval("DeviceGroupName")%>'></asp:HiddenField>
                                    </td>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                    </div>

                    <table id="tblxivmirrormonitoring" runat="server" class="table table-bordered table-primary"
                        width="100%" style="display: none">
                        <%--<tr>
                            <td>
                                <div class="widget-head">
                                    <span style="padding-left: 5px !important;" class="heading" id="ctl00_cphBody_lblcggrName">XIV Mirror Monitoring</span>

                                </div>
                            </td>
                        </tr>--%>
                        <tr>
                            <td>CG  
                               <asp:DropDownList ID="ddlxivcg" runat="server" Style="margin-left: 20px; width: 200px" OnSelectedIndexChanged="ddlxivcg_SelectedIndexChanged" AutoPostBack="True">
                               </asp:DropDownList>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="widget-head">
                                    <span style="padding-left: 5px !important;" class="heading" id="Span62">CG Monitoring </span>

                                </div>
                                <table id="tblcgMonitor" runat="server" class="table table-bordered table-primary"
                                    width="100%">
                                    <thead>
                                        <tr>

                                            <th>Primary CG Name
                                            </th>
                                            <th>Secondory CG Name
                                            </th>
                                            <th>Active
                                            </th>
                                            <th>RPO Status
                                            </th>
                                            <th>Link Up
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>

                                            <td>
                                                <asp:Label ID="lblprcgname" runat="server" CssClass="health_up"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblseccgname" runat="server" CssClass="health_down"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcgactive" runat="server" CssClass="health_down"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcgrpostatus" runat="server" CssClass="health_down"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcglinkup" runat="server" CssClass="health_down"></asp:Label>
                                            </td>
                                        </tr>

                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="widget-head">
                                    <span style="padding-left: 5px !important;" class="heading" id="Span65">Volume Monitoring</span>

                                </div>
                                <asp:ListView ID="lvcgvolmoni" runat="server" Visible="true" OnItemDataBound="lvcgvolmoni_ItemDataBound">
                                    <LayoutTemplate>
                                        <table class="table table-bordered table-condensed margin-bottom-none" width="100%">
                                            <thead>

                                                <tr>

                                                    <th>Primary Volume
                                                    </th>
                                                    <th>Primary Storage
                                                    </th>
                                                    <th>Secondory Volume
                                                    </th>
                                                    <th>Secondory Storage
                                                    </th>
                                                    <th>Active
                                                    </th>
                                                    <th>RPO Status
                                                    </th>
                                                    <th>Link Up
                                                    </th>

                                                </tr>
                                            </thead>

                                            <tbody>
                                                <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />
                                            </tbody>
                                        </table>

                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr>

                                            <td>
                                                <asp:Label ID="lblcgprvolume" runat="server" CssClass="health_up" Text='<%#Eval("PRVolume")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcgprstorage" runat="server" CssClass="health_down" Text='<%#Eval("PRStorageName")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcgsecvolume" runat="server" CssClass="health_down" Text='<%#Eval("DRVolume")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcgsecstorage" runat="server" CssClass="health_down" Text='<%#Eval("DRStorageName")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcgvolactive" runat="server" CssClass="health_down" Text='<%#Eval("ActiveSatus")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcgvolrpostatus" runat="server" CssClass="health_down" Text='<%#Eval("RPOStatus")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblcgvollinkup" runat="server" CssClass="health_down" Text='<%#Eval("LinkUp")%>'></asp:Label>
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                    <EmptyDataTemplate>
                                        <div class="message warning align-center bold">
                                            <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>
                                </asp:ListView>


                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="widget-head">
                                    <span style="padding-left: 5px !important;" class="heading" id="Span66">XIV Mirror Statistics </span>

                                </div>

                                <asp:ListView ID="lvxivstat" runat="server" Visible="true">
                                    <LayoutTemplate>
                                        <table id="tblxivmirstat" class="table table-bordered table-primary"
                                            width="100%">
                                            <thead>
                                                <tr>

                                                    <th>Created
                                                    </th>
                                                    <th>Started
                                                    </th>
                                                    <th>Finished
                                                    </th>
                                                    <th>Job Size (MB)
                                                    </th>
                                                    <th>Job Duration (Sec)
                                                    </th>
                                                    <th>Average Sync Rate (MB/sec)
                                                    </th>

                                                </tr>
                                            </thead>
                                            <tbody>
                                                <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />
                                            </tbody>
                                        </table>

                                    </LayoutTemplate>

                                    <ItemTemplate>
                                        <tr>

                                            <td>
                                                <asp:Label ID="lblxivcreated" runat="server" CssClass="health_up" Text='<%#Eval("CGCreatedate")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblxivstarted" runat="server" CssClass="health_down" Text='<%#Eval("CGStartedDate")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblxivfinished" runat="server" CssClass="health_down" Text='<%#Eval("CGFinisheDate")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblxivjbsize" runat="server" CssClass="health_down" Text='<%#Eval("JobSize")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblxivjbdur" runat="server" CssClass="health_down" Text='<%#Eval("JobDuration")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblxivavgrate" runat="server" CssClass="health_down" Text='<%#Eval("AverageSyncRole")%>'></asp:Label>
                                            </td>

                                        </tr>



                                    </ItemTemplate>
                                    <EmptyDataTemplate>
                                        <div class="message warning align-center bold">
                                            <asp:Label ID="lblEmpty" Text="N/A" runat="server"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>
                                </asp:ListView>

                            </td>
                        </tr>
                        <tr>
                            <td>

                                <div class="widget-head">
                                    <span style="padding-left: 5px !important;" class="heading" id="Span67">Cluster Monitoring</span>

                                </div>

                                <div class="widget-head">
                                    <span style="padding-left: 5px !important;" class="heading" id="Span68">Cluster Status : </span>
                                    <asp:Label ID="lblcluststatus" runat="server" CssClass="health_down"></asp:Label>


                                </div>


                                <asp:ListView ID="lvclustmoni" runat="server" Visible="true" OnItemDataBound="lvcgvolmoni_ItemDataBound">
                                    <LayoutTemplate>

                                        <table id="tblxivclustmoni" class="table table-bordered table-primary" width="100%">
                                            <thead>

                                                <tr>
                                                    <th>Group Name
                                                    </th>
                                                    <th>Group State
                                                    </th>
                                                    <th>Node
                                                    </th>



                                                </tr>
                                            </thead>

                                            <tbody>
                                                <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />
                                            </tbody>
                                        </table>

                                    </LayoutTemplate>

                                    <ItemTemplate>
                                        <tr>

                                            <td>
                                                <asp:Label ID="lblclustgroupname" runat="server" CssClass="health_up" Text='<%#Eval("GroupName")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblclustgroupstate" runat="server" CssClass="health_down" Text='<%#Eval("GroupState")%>'></asp:Label>
                                            </td>
                                            <td>
                                                <asp:Label ID="lblclustnode" runat="server" CssClass="health_down" Text='<%#Eval("Node")%>'></asp:Label>
                                            </td>




                                        </tr>
                                    </ItemTemplate>
                                    <EmptyDataTemplate>
                                        <div class="message warning align-center bold">
                                            <asp:Label ID="lblEmpty" Text="N/A" runat="server"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>
                                </asp:ListView>

                            </td>
                        </tr>
                    </table>

                    <table id="tblSRMVMwareReplication" class="table table-striped table-condensed table-bordered table-responsive monitortable font" runat="server" style="display: none;">
                        <thead>
                            <tr>
                                <th class="col-md-4">Protection Group & Recovery Plan Monitoring</th>
                                <th>Protected Site</th>
                                <th>Recovery Site</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Protection Groups Name, Type, State, VM Count()</td>
                                <td><span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblPRotectionGrpNamePR" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblPGTypePR" runat="server" Visible="false" Text=""></asp:Label>
                                    <asp:Label ID="lblPGStatePR" runat="server" Visible="false" Text=""></asp:Label>
                                    <asp:Label ID="lblVMCountPR" runat="server" Visible="false" Text=""></asp:Label>
                                </td>
                                <td><span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblPRotectionGrpNameDR" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblPGTypeDR" runat="server" Visible="false" Text=""></asp:Label>
                                    <asp:Label ID="lblPGStateDR" runat="server" Visible="false" Text=""></asp:Label>
                                    <asp:Label ID="lblVMCountDR" runat="server" Visible="false" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-md-4">Recovery Plan Name, State</td>
                                <td><span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblRecoveryPlanNamePR" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblRecoveryPlanStatePR" runat="server" Visible="false" Text=""></asp:Label>
                                </td>
                                <td><span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblRecoveryPlanNameDR" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblRecoveryPlanStateDR" runat="server" Visible="false" Text=""></asp:Label>
                                </td>
                            </tr>
                            <tr id="Tr5" runat="server" visible="false">
                                <td class="col-md-4">Recovery Plan History Status [Plan Name, Last Run Date, State, TotalTime(sec)]</td>
                                <td>
                                    <asp:Label ID="lblHPNamePR" runat="server" Text=""></asp:Label>
                                    ,
                                    <asp:Label ID="lblLastRunDatePR" runat="server" Text=""></asp:Label>
                                    ,
                                    <asp:Label ID="lblHistoryStatePR" runat="server" Text=""></asp:Label>
                                    ,
                                    <asp:Label ID="lblTotalTimePR" runat="server" Text=""></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblHPNameDR" runat="server" Text=""></asp:Label>
                                    ,
                                    <asp:Label ID="lblLastRunDateDR" runat="server" Text=""></asp:Label>
                                    ,
                                    <asp:Label ID="lblHistoryStateDR" runat="server" Text=""></asp:Label>
                                    ,
                                    <asp:Label ID="lblTotalTimeDR" runat="server" Text=""></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table id="tblBase24Replication" class="table table-striped table-condensed table-bordered table-responsive monitortable font" runat="server" style="display: none;">
                        <thead>
                            <tr>
                                <th style="width: 30%">DRNet Replication Monitor</th>
                                <th>Production Server</th>
                                <th>DR Server</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Replication Type</td>
                                <td style="border-right: 0px !important"><span id="Span12Base24" class="icon-sync"></span>
                                    <asp:Label ID="lblPRBase24RepType" runat="server"></asp:Label>
                                </td>

                            </tr>
                            <tr>
                                <td class="col-md-4">Replication Process</td>
                                <td class="text-indent"><span class="icon-service">&nbsp;</span>
                                    <asp:Label ID="lblPRBase24RepProcess" runat="server"></asp:Label>
                                </td>
                                <td><span id="Span9" class="icon-service" runat="server">&nbsp;</span>
                                    <asp:Label ID="lblDRBase24RepProcess" runat="server"></asp:Label>
                                </td>
                            </tr>

                            <tr id="tr18">
                                <td class="col-md-4">Replication Mode</td>
                                <td class="text-indent"><span id="Span10" class="up-arrow-icon" runat="server"></span>
                                    <asp:Label ID="lblPRBase24RepMode" runat="server"></asp:Label>
                                </td>
                                <td><span id="Span54" class="up-arrow-icon" runat="server"></span>
                                    <asp:Label ID="lblDRBase24RepMode" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr id="tr19" runat="server">
                                <td class="col-md-4">Replication Status</td>
                                <td class="text-indent"><span class="Active">&nbsp;</span>
                                    <asp:Label ID="lblPRBase24RepStatus" runat="server"></asp:Label>
                                </td>
                                <td><span class="Active">&nbsp;</span>
                                    <asp:Label ID="lblDRBase24RepStatus" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr id="tr20" runat="server">
                                <td class="col-md-4">Replication Accessed</td>
                                <td class="text-indent"><span class="icon-Time">&nbsp;</span>
                                    <asp:Label ID="lblPRBase24RepAccessed" runat="server"></asp:Label>
                                </td>
                                <td><span id="Span12" class="icon-Time" runat="server">&nbsp;</span>
                                    <asp:Label ID="lblDRBase24RepAccessed" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr id="tr21" runat="server">
                                <td class="col-md-4">Replication Queue</td>
                                <td class="text-indent"><span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblPRBase24RepQueue" runat="server"></asp:Label>
                                </td>
                                <td><span class="icon-numbering">&nbsp;</span>
                                    <asp:Label ID="lblDRBase24RepQueue" runat="server"></asp:Label>
                                </td>
                            </tr>

                            <tr id="tr23" runat="server">
                                <td class="col-md-4">Audit File Name</td>
                                <td class="text-indent"><span class="icon-log">&nbsp;</span>
                                    <asp:Label ID="lblPRBase24AuditFileName" runat="server"></asp:Label>
                                </td>
                                <td><span class="icon-log">&nbsp;</span>
                                    <asp:Label ID="lblDRBase24AuditFileName" runat="server"></asp:Label>
                                </td>
                            </tr>

                            <tr id="tr22" runat="server">
                                <td class="col-md-4">Data lag</td>
                                <td style="border-right: 0px !important">
                                    <span id="Span13" class="icon-datalag-down" runat="server"></span>
                                    <asp:Label ID="lblBase24Datalag" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table id="tblTPRCRepliMonitoring" runat="server" class="table table-striped table-condensed table-bordered table-responsive" style="display: none;">
                        <thead>
                            <tr>
                                <th class="col-md-4">Replication Monitoring
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th>DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Pair Name
                                </td>
                                <td class="text-indent"><span id="Span111" runat="server">&nbsp;</span>
                                    <asp:Label ID="lblPRPairName" runat="server"></asp:Label>
                                </td>
                                <td class="text-indent"><span id="Span112" runat="server">&nbsp;</span>
                                    <asp:Label ID="lblDRPairName" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Error
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblPRError" runat="server"></asp:Label>
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblDRError" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>Copy Type
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblPRCopyType" runat="server"></asp:Label>
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblDRCopyType" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Progress
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblPRProgress" runat="server"></asp:Label>
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblDRProgress" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Error Volumes
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblPRerrorvol" runat="server"></asp:Label>
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblDRerrorvol" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Recoverable Pairs
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblPRrecoverPair" runat="server"></asp:Label>
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblDRrecoverPair" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Copying Pairs 
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblPRcopypairs" runat="server"></asp:Label>
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblDRcopypairs" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Total Pairs
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblPRtotalpairs" runat="server"></asp:Label>
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblDRtotalpairs" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Recovery Time
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblPRRecoveryTime" runat="server"></asp:Label>
                                </td>
                                <td><span class="health-up">&nbsp;</span>
                                    <asp:Label ID="lblDRRecoveryTime" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table id="tblVVR" runat="server" class="table table-striped table-condensed table-bordered table-responsive" width="100%" style="display: none;">
                        <thead>
                            <tr>
                                <th class="col-md-4">Monitoring Component
                                </th>
                                <th class="col-md-4">Production Server
                                </th>
                                <th class="col-md-4">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="3">
                                    <strong>Replicated Volume Group (RVG) Monitoring - Summary</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>RDS Name
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                    <asp:Label ID="lblPRRDSName" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                    <asp:Label ID="lblDRRDSName" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Hostname
                                </td>
                                <td>
                                    <asp:Label ID="Span63PR" runat="server"></asp:Label>
                                    <asp:Label ID="lblIPAddressPR" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Span64DR" runat="server"></asp:Label>
                                    <asp:Label ID="lblIPAddressDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>RVG Name
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                    <asp:Label ID="lblRVGNamePR" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                    <asp:Label ID="lblRVGNameDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Disk Group Name
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-symmetrix"></asp:Label>
                                    <asp:Label ID="lblDGNamePR" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-symmetrix"></asp:Label>
                                    <asp:Label ID="lblDGNameDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Data Volume count
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="count-icon"></asp:Label>
                                    <asp:Label ID="lblDVCPR" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="count-icon"></asp:Label>
                                    <asp:Label ID="lblDVCDR" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>SRL
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                    <asp:Label ID="lblPRSRL" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                    <asp:Label ID="lblDRSRL" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3">
                                    <strong>RLINKs Monitoring - Summary</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>RLINKs Name
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                                    <asp:Label ID="lblPRRlink" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                                    <asp:Label ID="lblDRRlink" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>RLINK State
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="Active"></asp:Label>
                                    <asp:Label ID="lblPRRlinkState" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="Active"></asp:Label>
                                    <asp:Label ID="lblDRRlinkState" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Synchronous Mode
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-async"></asp:Label>
                                    <asp:Label ID="lblPRSyncMode" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-async"></asp:Label>
                                    <asp:Label ID="lblDRSyncMode" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3">
                                    <strong>RLINKs Monitoring - Replication Performance</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>Last Checked Time
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                    <asp:Label ID="lblPRLCTime" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                    <asp:Label ID="lblDRLCTime" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3">
                                    <strong>Information Messages</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>No. of Messages Transmitted
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                    <asp:Label ID="lblPRMSGTrans" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                    <asp:Label ID="lblDRMSGTrans" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>No. of Blocks Transmitted
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                                    <asp:Label ID="lblPRBlockTrans" runat="server" Text="35933"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                                    <asp:Label ID="lblDRBlockTrans" runat="server" Text="0"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3">
                                    <strong>Error Information</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>Stream Errors
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-currentlsn"></asp:Label>
                                    <asp:Label ID="lblPRStreammirror" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-currentlsn"></asp:Label>
                                    <asp:Label ID="lblDRStreammirror" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Memory Errors
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-currentlsn"></asp:Label>
                                    <asp:Label ID="lblPRmemoryError" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-currentlsn"></asp:Label>
                                    <asp:Label ID="lblDRmemoryError" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3">
                                    <strong>RLINKs Monitoring - Secondary Update</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>Disk Group Name
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                    <asp:Label ID="lblDGNamePR1" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-disk-symmetrix"></asp:Label>
                                    <asp:Label ID="lblDGNameDR1" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>RLINKs Name
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                    <asp:Label ID="lblPRRlinkName1" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                                    <asp:Label ID="lblDRRlinkName1" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last Update (ID)
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="id-icon"></asp:Label>
                                    <asp:Label ID="lblLastupadtePR" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="id-icon"></asp:Label>
                                    <asp:Label ID="lblLastupadteDR" runat="server"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>Last Update Time
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                    <asp:Label ID="lblPRLUtime" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                    <asp:Label ID="lblDRLUtime" runat="server"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>DataLag (hrs,mins,secs)
                                </td>
                                <td colspan="2">
                                    <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                    <asp:Label ID="lblDatalag" runat="server"></asp:Label>
                                </td>
                            </tr>

                        </tbody>
                    </table>

                </div>
            </div>
        </div>

        <div runat="server" id="ADDiv" visible="false">
            <div class="widget" id="div9" runat="server">

                <div class="widget-body">

                    <div class="widget">

                        <div class="widget-head">
                            <asp:Label ID="Label45" class="heading" runat="server" Style="padding-left: 5px !important;" Text="Active Directory Monitoring"></asp:Label>
                        </div>

                        <div class="widget-body ">
                            <div class="tabs-content" width="100%">
                                <table id="tblActiveDirMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive">
                                    <thead>
                                        <tr>
                                            <th colspan="1" class="col-md-4">Replication Monitor
                                            </th>
                                            <th class="col-md-4">Production Server
                                            </th>
                                            <th class="col-md-4">DR Server
                                            </th>
                                        </tr>
                                    </thead>

                                    <tbody>

                                        <tr>
                                            <td>Domain Name
                                            </td>
                                            <td><span id="Span113" runat="server" class="site-icon">&nbsp;</span>
                                                <asp:Label ID="lblDomainNamePR" runat="server"></asp:Label>
                                            </td>
                                            <td><span id="Span114" runat="server" class="site-icon">&nbsp;</span>
                                                <asp:Label ID="lblDomainNameDR" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>IP Address/HostName,Domain Controller Name
                                            </td>
                                            <td><span id="Span115" runat="server" class="health-up">&nbsp;</span>
                                                <asp:Label ID="lblDomainControllerPR" runat="server"></asp:Label>
                                            </td>
                                            <td><span id="Span116" runat="server" class="health-up">&nbsp;</span>
                                                <asp:Label ID="lblDomainControllerDR" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr id="tr27" runat="server">
                                            <td>ADReplicationSite
                                            </td>
                                            <td class="text-indent">
                                                <span class="site-icon"></span>
                                                <asp:Label ID="lblADRepSitePR" runat="server" CssClass="command"></asp:Label>
                                            </td>
                                            <td class="text-indent">
                                                <span class="site-icon"></span>
                                                <asp:Label ID="lblADRepSiteDR" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr id="tr28" runat="server">
                                            <td>Partner Name
                                            </td>
                                            <td class="text-indent">
                                                <span class="icon-log"></span>
                                                <asp:Label ID="lblPartnerNamePR" runat="server" Text=""></asp:Label>
                                            </td>

                                            <td class="text-indent">
                                                <span class="icon-log"></span>
                                                <asp:Label ID="lblPartnerNameDR" runat="server" Text=""></asp:Label>
                                            </td>
                                        </tr>
                                        <tr id="tr6" runat="server">
                                            <td>Partner Type
                                            </td>
                                            <td class="text-indent">
                                                <span class="icon-log"></span>
                                                <asp:Label ID="lblPartnerTypePR" runat="server" Text=""></asp:Label>
                                            </td>

                                            <td class="text-indent">
                                                <span class="icon-log"></span>
                                                <asp:Label ID="lblPartnerTypeDR" runat="server" Text=""></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-md-4">Consecutive Replication Failures
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-log"></span>
                                                <asp:Label ID="lblReplicationFailuresPR" runat="server"></asp:Label>
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-log"></span>
                                                <asp:Label ID="lblReplicationFailuresDR" runat="server">-</asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-md-4">Last Replication Attempt
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-Time"></span>
                                                <asp:Label ID="lblLastReplicationAttemptPR" runat="server"></asp:Label>
                                            </td>
                                            <td class="col-md-4"><span class="icon-Time"></span>
                                                <asp:Label ID="lblLastReplicationAttemptDR" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-md-4">Last Replication Success
                                            </td>
                                            <td class="col-md-4"><span class="icon-Time"></span>
                                                <asp:Label ID="lblLastReplicationSuccessPR" runat="server"></asp:Label>
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-Time"></span>
                                                <asp:Label ID="lblLastReplicationSuccessDR" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-md-4">Last Replication Result
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-maxdbpage"></span>
                                                <asp:Label ID="lblLastReplicationResultPR" runat="server"></asp:Label>
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-maxdbpage"></span>
                                                <asp:Label ID="lblLastReplicationResultDR" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-md-4">Last Sync Message 
                                            </td>
                                            <td class="col-md-4"><span class="icon-log"></span>
                                                <asp:Label ID="lblLastSyncMessagePR" runat="server"></asp:Label>
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-log"></span>
                                                <asp:Label ID="lblLastSyncMessageDR" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-md-4">Replication Failure First Recorded Time
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-Time"></span>
                                                <asp:Label ID="lblFailureFRecTimePR" runat="server"></asp:Label>
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-Time"></span>
                                                <asp:Label ID="lblFailureFRecTimeDR" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-md-4">DataLag (Hours, Minutes, Seconds)
                                            </td>
                                            <td class="col-md-4">
                                                <span class="icon-Time"></span>
                                                <asp:Label ID="lblADDataLag" runat="server"></asp:Label>
                                            </td>

                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>

                </div>

            </div>
        </div>

        <div class="widget" id="divZerto" runat="server" visible="false">
            <div class="widget-head">
                <asp:Label ID="lbl_Zerto" runat="server" Text="Zerto Monitoring Summary" Style="margin-left: 10px;"></asp:Label>
            </div>
            <div class="widget-body innerAll inner-2x">
                <table id="tblZertoMonitor" class="table table-striped table-condensed table-bordered table-responsive" width="100%" runat="server">
                    <thead>
                        <tr>
                            <th class="col-md-4"></th>
                            <th class="col-md-4">Protected Site
                            </th>
                            <th class="col-md-4">Recovery Site
                            </th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>IP Address/HostName </td>
                            <td>
                                <span id="Span151" runat="server" class="health-up">&nbsp;</span>
                                <asp:Label ID="lblZetroPRIPAddress" runat="server"></asp:Label>
                            </td>
                            <td><span id="Span152" runat="server" class="health-up">&nbsp;</span>
                                <asp:Label ID="lblZetroDRIPAddress" runat="server"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>Protected/Recovery Site

                            </td>
                            <td class="text-indent"><span id="Span141" runat="server" class="icon-numbering">&nbsp;</span>
                                <asp:Label ID="lblPRRecovery" runat="server"></asp:Label>
                            </td>
                            <td class="text-indent"><span id="Span142" runat="server" class="icon-numbering">&nbsp;</span>
                                <asp:Label ID="lblDRRecovery" runat="server"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>Site Name
                            </td>
                            <td><span id="Span149" runat="server" class="site-icon">&nbsp;</span>
                                <asp:Label ID="lblPRSiteName" runat="server"></asp:Label>
                            </td>
                            <td><span id="Span150" runat="server" class="site-icon">&nbsp;</span>
                                <asp:Label ID="lblDRSiteName" runat="server"></asp:Label>
                            </td>
                        </tr>

                        <tr>
                            <td>VPG Name (#VMs)
                            </td>
                            <td><span id="Span153" runat="server" class="icon-computer">&nbsp;</span>
                                <asp:Label ID="lblPRVPG" runat="server"></asp:Label>
                            </td>
                            <td><span id="Span154" runat="server" class="icon-computer">&nbsp;</span>
                                <asp:Label ID="lblDRVPG" runat="server"></asp:Label>
                            </td>
                        </tr>

                        <tr>
                            <td>Site Type
                            </td>
                            <td><span id="Span155" runat="server" class="site-icon">&nbsp;</span>
                                <asp:Label ID="lblPRSiteType" runat="server"></asp:Label>
                            </td>
                            <td><span id="Span156" runat="server" class="site-icon">&nbsp;</span>
                                <asp:Label ID="lblDRSiteType" runat="server"></asp:Label>
                            </td>
                        </tr>

                        <tr>
                            <td>Protection Status
                            </td>
                            <td><span id="Span157" runat="server" class="archive-log-blue">&nbsp;</span>
                                <asp:Label ID="lblPRProtection" runat="server"></asp:Label>
                            </td>
                            <td><span id="Span158" runat="server" class="archive-log-blue">&nbsp;</span>
                                <asp:Label ID="lblDRProtection" runat="server"></asp:Label>
                            </td>
                        </tr>

                        <tr>
                            <td>VPG State
                            </td>
                            <td><span id="Span159" runat="server" class="pause">&nbsp;</span>
                                <asp:Label ID="lblPRVPGState" runat="server"></asp:Label>
                            </td>
                            <td><span id="Span160" runat="server" class="pause">&nbsp;</span>
                                <asp:Label ID="lblDRVPGState" runat="server"></asp:Label>
                            </td>
                        </tr>

                        <tr>
                            <td>Actual RPO (DataLag)
                            </td>
                            <td><span id="Span20" runat="server" class="icon-Time">&nbsp;</span>
                                <asp:Label ID="lblPRDatalag" runat="server"></asp:Label>
                            </td>

                        </tr>


                    </tbody>
                </table>
            </div>
        </div>

        <div class="widget" id="divFastCopy" runat="server" visible="false">

            <div class="widget-body">

                <div class="widget">

                    <div class="widget-head">
                        <asp:Label ID="lblFastCopy" class="heading" runat="server" Style="padding-left: 5px !important;"></asp:Label>
                    </div>

                    <div class="widget-body ">
                        <div class="tabs-content" width="100%">
                            <table id="tblFastCopy" class="table table-bordered table-white" width="100%" runat="server">

                                <thead>
                                    <tr>
                                        <th colspan="2">Replication Monitor
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>

                                     <tr>
                                        <td class="col-md-4">Server IP
                                        </td>
                                        <td class="col-md-8"><span class="icon-linux"></span>

                                            <asp:Label ID="lblserverip" runat="server"></asp:Label>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-4">Replication Type
                                        </td>
                                        <td class="col-md-8"><span class="replication-file-icon"></span>

                                            <asp:Label ID="lblFastcopyRepType" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Source Directory
                                        </td>
                                        <td class="text-indent">
                                            <span class="icon-storageDR">&nbsp;</span>
                                            <asp:Label ID="lblFastcopySourceDirectory" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Target Directory
                                        </td>
                                        <td class="text-indent">
                                            <span class="icon-storageDR">&nbsp;</span>
                                            <asp:Label ID="lblFastcopyTargetDirectory" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Total Replication Files
                                        </td>
                                        <td colspan="2"><span class="icon-disks ">&nbsp;</span>
                                            <asp:Label ID="lbltotreplicationJobPairs" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Last Replicated file Name
                                        </td>
                                        <td class="text-indent">
                                            <span class="icon-storageDR">&nbsp;</span>
                                            <asp:Label ID="lblFastcopyLastFileName" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Last Replicated file Size
                                        </td>
                                        <td class="active vertical-align">
                                            <span class="icon-storageDR">&nbsp;</span>
                                            <asp:Label ID="lblFastcopyLastFileSize" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="widget" id="divHP3PARStorage" runat="server" visible="false">
            <div class="widget-body">

                <div class="widget">

                    <div class="widget-head">
                        <asp:Label ID="lblreplicationNm" class="heading" runat="server" Style="padding-left: 5px !important;"></asp:Label>
                    </div>

                    <div class="widget-body ">
                        <div class="tabs-content" width="100%">
                            <table id="tblHP3PARStorage" class="table table-striped table-condensed table-bordered table-responsive" width="100%" runat="server">
                                <thead>
                                    <tr>
                                        <th class="col-md-4">Replication Monitoring
                                        </th>
                                        <th class="col-md-4">Production Server
                                        </th>
                                        <th class="col-md-4">DR Server
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>3PAR Storage IP Address/HostName
                                        </td>
                                        <td>
                                            <asp:Label ID="prspan" runat="server" CssClass="health-up"></asp:Label>
                                            <asp:Label ID="lblHP3PARPRIP" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="drspan" runat="server" CssClass="health-up"></asp:Label>
                                            <asp:Label ID="lblHP3PARDRIP" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>3PAR Storage Name
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-database"></asp:Label>
                                            <asp:Label ID="lblPR3PARStorageName" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-database"></asp:Label>
                                            <asp:Label ID="lblDR3PARStorageName" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Remote Copy Group Name
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                            <asp:Label ID="lblPRRemoteCopyGrpName" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                            <asp:Label ID="lblDRRemoteCopyGrpName" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Role
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-computer"></asp:Label>
                                            <asp:Label ID="lblPR3PARRole" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-drcomputer"></asp:Label>
                                            <asp:Label ID="lblDR3PARRole" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Replication Mode
                                        </td>
                                        <td>
                                            <asp:Label runat="server" class="replication-file-icon"></asp:Label>
                                            <asp:Label ID="lblPR3PARRepliMode" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" class="replication-file-icon"></asp:Label>
                                            <asp:Label ID="lblDR3PARRepliMode" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>DR State
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-enable"></asp:Label>
                                            <asp:Label ID="lblPR3PARState" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-enable"></asp:Label>
                                            <asp:Label ID="lblDR3PARState" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Group State
                                        </td>
                                        <td>
                                            <asp:Label CssClass="icon-on" runat="server"></asp:Label>
                                            <asp:Label ID="lblPR3PARGrpState" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label CssClass="icon-on" runat="server"></asp:Label>
                                            <asp:Label ID="lblDR3PARGrpState" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Last Sync Time
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                            <asp:Label ID="lblPR3PARSyncTime" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                            <asp:Label ID="lblDR3PARSyncTime" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>DataLag (HH:MM:SS)
                                        </td>
                                        <td colspan="2">
                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                            <asp:Label ID="lblHP3PARDatalag" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="widget" id="divHP3PARVirtualMonitoring" runat="server" visible="false">
            <div class="widget-body">
                <div class="widget">

                    <div class="widget-head">
                        <asp:Label ID="Label160" class="heading" runat="server" Text="Virtual Volume Monitoring (from Secondary Group System)"></asp:Label>
                    </div>

                    <div class="widget-body ">
                        <div class="tabs-content" width="100%">
                            <table id="Table13" runat="server" class="table table-bordered table-white" width="100%">
                                <thead>
                                    <tr>
                                        <th class="col-md-4">Component
                                        </th>
                                        <th class="col-md-4">Virtual Volume Monitoring Details
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Local VV Name 
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-computer"></asp:Label>
                                            <asp:Label ID="lblvvname" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Remote VV Name
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-drcomputer"></asp:Label>
                                            <asp:Label ID="lblremotevvname" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Sync State
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-sync"></asp:Label>
                                            <asp:Label ID="lblsyncstate" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Last Sync Time
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                            <asp:Label ID="lbllstsynstate" runat="server"></asp:Label>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="widget" runat="server" id="Div7" visible="false">

            <div class="widget-head">
                <asp:Label ID="lblRoboCopyNm" class="heading" runat="server" Style="padding-left: 5px !important;" Text="Robocopy Detailed Monitoring "></asp:Label>
            </div>
            <div class="widget-body innerAll inner-2x" id="divrobocopydeatail" runat="server">
                <div id="robocopydeatail" class="tabs-content">
                    <table id="tblrobocpymonitor" class="table table-striped table-bordered table-responsive" width="100%" runat="server">
                        <thead>
                            <tr>
                                <th class="col-md-4">RoboCopy Details
                                </th>
                                <th class="col-md-4">Source Server 
                                </th>
                                <th class="col-md-4">Target Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>IP Address/HostName
                                </td>
                                <td>
                                    <asp:Label ID="spnLabel41" runat="server"></asp:Label>
                                    <asp:Label ID="Label41" runat="server"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="spnLabel42" runat="server"></asp:Label>
                                    <asp:Label ID="Label42" runat="server"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>Hostname
                                </td>
                                <td><span class="host-icon"></span>
                                    <asp:Label ID="Label43" runat="server"></asp:Label>
                                </td>
                                <td><span class="host-icon"></span>
                                    <asp:Label ID="Label44" runat="server"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>No. Of Jobs
                                </td>
                                <td colspan="2">
                                    <asp:Label ID="lblNoRoboJobs" runat="server"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3">
                                    <asp:Repeater ID="rptRoboMonitorStatus" runat="server" OnItemDataBound="rptRoboMonitorStatus_ItemDataBound">
                                        <HeaderTemplate>
                                            <table class="table table-striped table-bordered table-condensed table-white">
                                                <tr>
                                                    <th colspan="3" style="background-color: transparent !important; border-width: 0px !important; color: #8a7f7f !important;">RoboCopy Monitoring Status</th>
                                                </tr>
                                        </HeaderTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <th colspan="3" style="background-color: #4a8bc2 !important;">Job: <%# Eval("RowIndex") %></th>
                                            </tr>
                                            <tr>
                                                <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>
                                                <asp:Label ID="lblRoboCopyJobId" runat="server" Text='<%# Eval("RoboCopyJobId") %>' Visible="false"></asp:Label>
                                                <td>Drive / Directory for Copy
                                                </td>
                                                <td><span class="folder-blue_new"></span>
                                                    <asp:Label ID="lblspath" runat="server" Text='<%# Eval("SourceDirectory") %>'></asp:Label>
                                                </td>
                                                <td><span class="folder-blue_new"></span>
                                                    <asp:Label ID="lbldpath" runat="server" Text='<%# Eval("DestinationDirectory") %>'></asp:Label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Selected Options
                                                </td>
                                                <td colspan="2"><span class="icon filter-blue-16" style="background-position: -1px -1px;"></span>
                                                    <asp:Label ID="lblRoboSelectedOptions" runat="server" Text='<%# Eval("SelectedOptions") %>'></asp:Label>
                                                </td>
                                            </tr>
                                            <%-- <tr>
                                            <td colspan="3"><b>RoboCopy Monitoring Status</b> </td>
                                        </tr>--%>
                                            <tr>
                                                <td>Started Date, Time
                                                </td>
                                                <td colspan="2"><span class="icon-Time"></span>
                                                    <asp:Label ID="lblrepstarttime" runat="server" Text='<%# Eval("RepStartTime") %>'></asp:Label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>End Date, Time
                                                </td>
                                                <td colspan="2"><span class="icon-Time"></span>
                                                    <asp:Label ID="lblrependtime" runat="server" Text='<%# Eval("RepEndTime") %>'></asp:Label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="2" class="no-padding">
                                                    <table style="width: 100%;" class="noborder">
                                                        <tr>
                                                            <td style="width: 17%">
                                                                <b>Total</b>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <b>Copied</b>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <b>Skipped</b>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <b>Mismatch</b>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <b>FAILED</b>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <b>Extras</b>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Dirs
                                                </td>
                                                <td colspan="2" class="no-padding">
                                                    <table style="width: 100%;" class="noborder">
                                                        <tr>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lbltotaldir" runat="server" Text='<%# Eval("TotalDirCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblcopieddir" runat="server" Text='<%# Eval("TotalDirCopiedCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblskippeddir" runat="server" Text='<%# Eval("TotalSkippedDirCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblmismatcheddir" runat="server" Text='<%# Eval("TotalMisMatchedDirCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <asp:Label ID="lblfaileddir" runat="server" Text='<%# Eval("TotalFailedDirCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <asp:Label ID="lblextrasdir" runat="server" Text='<%# Eval("TotalExtrasDirCount") %>'></asp:Label>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Files
                                                </td>
                                                <td colspan="2" class="no-padding">
                                                    <table style="width: 100%;" class="noborder">
                                                        <tr>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lbltotalfiles" runat="server" Text='<%# Eval("TotalFilesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblcopiedfiles" runat="server" Text='<%# Eval("TotalFilesCopiedCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblskippedfiles" runat="server" Text='<%# Eval("TotalSkippedFilesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblmismatchedfiles" runat="server" Text='<%# Eval("TotalMisMatchedFilesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <asp:Label ID="lblfailedfiles" runat="server" Text='<%# Eval("TotalFailedFilesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <asp:Label ID="lblextrasfiles" runat="server" Text='<%# Eval("TotalExtrasFilesCount") %>'></asp:Label>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Bytes
                                                </td>
                                                <td colspan="2" class="no-padding">
                                                    <table style="width: 100%;" class="noborder">
                                                        <tr>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lbltotalbytes" runat="server" Text='<%# Eval("TotalBytesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblcopiedbytes" runat="server" Text='<%# Eval("TotalBytesCopiedCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblskippedbytes" runat="server" Text='<%# Eval("TotalSkippedBytesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblmismatchedbytes" runat="server" Text='<%# Eval("TotalMisMatchedBytesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <asp:Label ID="lblfailedbytes" runat="server" Text='<%# Eval("TotalFailedBytesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <asp:Label ID="lblextrasbytes" runat="server" Text='<%# Eval("TotalExtrasBytesCount") %>'></asp:Label>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Times
                                                </td>
                                                <td colspan="2" class="no-padding">
                                                    <table style="width: 100%;" class="noborder">
                                                        <tr>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lbltotaltimes" runat="server" Text='<%# Eval("TotalTimesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblcopiedtimes" runat="server" Text='<%# Eval("TotalTimesCopiedCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblskippedtimes" runat="server" Text='<%# Eval("TotalSkippedTimesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 17%">
                                                                <asp:Label ID="lblmismatchedtimes" runat="server" Text='<%# Eval("TotalMisMatchedTimesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <asp:Label ID="lblfailedtimes" runat="server" Text='<%# Eval("TotalFailedTimesCount") %>'></asp:Label>
                                                            </td>
                                                            <td style="width: 16%">
                                                                <asp:Label ID="lblextrastimes" runat="server" Text='<%# Eval("TotalExtrasTimesCount") %>'></asp:Label>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Speed (Bytes/Sec)
                                                </td>
                                                <td colspan="2" class="no-padding">
                                                    <table style="width: 100%;" class="noborder">
                                                        <tr>
                                                            <td colspan="5">
                                                                <asp:Label ID="speedbytepersec" runat="server" Text='<%# Eval("SpeedBytesPerSeconds") %>'></asp:Label>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Speed (MegaBytes/min)
                                                </td>
                                                <td colspan="2" class="no-padding">
                                                    <table style="width: 100%;" class="noborder">
                                                        <tr>
                                                            <td colspan="5">
                                                                <asp:Label ID="speedmegabytepermin" runat="server" Text='<%# Eval("SpeedMBPerMinute") %>'></asp:Label>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <FooterTemplate>
                                            </table>
                                        </FooterTemplate>
                                    </asp:Repeater>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>

        <div runat="server" id="divMimix" visible="false">
            <div>
                <div id="Div3" class="tabs-content ">

                    <div class="widget" runat="server" id="dvMMXManager">
                        <div class="widget-head">
                            <asp:Label ID="lblMMXManager" runat="server" class="heading" Text="Mimix Manager" Style="padding-left: 5px !important;"></asp:Label>
                        </div>
                        <div id="dvMimixManager" class="widget-body" runat="server">
                            <asp:ListView ID="lsvMimixManager" runat="server" Visible="true">
                                <LayoutTemplate>
                                    <table class="table table-bordered table-white" width="100%" style="margin-bottom: 0px;">
                                        <thead>
                                            <tr>
                                                <th style="width: 5%;">Sr. No.
                                                </th>
                                                <th style="width: 20%;">SystemDefinition
                                                </th>
                                                <th style="width: 16%;">Type
                                                </th>
                                                <th style="width: 19%;">System
                                                </th>
                                                <th style="width: 20%;">Journal
                                                </th>
                                                <th style="width: 20%;">ClusterServices
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                    <div class="slim-scroll chat-items" data-scroll-height="110px" data-scroll-size="0">
                                        <table class="table table-bordered table-white" width="100%">
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                            </tbody>
                                        </table>
                                    </div>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td class="text-center" style="width: 5%;">
                                            <%# Container.DataItemIndex + 1 %>
                                        </td>
                                        <td style="width: 20%;">
                                            <asp:Label ID="lblMMXSystemDefinition" runat="server" Text='<%# Eval("SystemDefinition") %> '> </asp:Label>
                                        </td>
                                        <td style="width: 16%">
                                            <asp:Label ID="lblMMXType" runat="server" Text='<%# Eval("Type") %>'></asp:Label>
                                        </td>
                                        <td style="width: 19%;">
                                            <asp:Label ID="lblMMXSystem" runat="server" Text='<%# Eval("System") %> '> </asp:Label>
                                        </td>
                                        <td style="width: 20%;">
                                            <asp:Label ID="lblMMXJournal" runat="server" Text='<%# Eval("Journal") %> '> </asp:Label>
                                        </td>
                                        <td style="width: 20%;">
                                            <asp:Label ID="lblMMXClusterServices" runat="server" Text='<%# Eval("ClusterServices") %> '> </asp:Label>
                                        </td>
                                    </tr>
                                </ItemTemplate>
                            </asp:ListView>
                        </div>
                    </div>

                    <div class="widget" runat="server" id="dvMMXAvail">

                        <div class="widget-head">
                            <asp:Label ID="Label3" runat="server" class="heading" Text="Mimix Availability" Style="padding-left: 5px !important;"></asp:Label>
                        </div>

                        <div id="dvMimixAvail" class="widget-body" runat="server">
                            <asp:ListView ID="lsvMimixAvail" runat="server" Visible="true">
                                <LayoutTemplate>
                                    <table class="table table-bordered table-white" width="100%" style="margin-bottom: 0px;">
                                        <thead>
                                            <tr>
                                                <th style="width: 5%;">Sr. No.
                                                </th>
                                                <th style="width: 40%;">Activity
                                                </th>
                                                <th style="width: 55%;">Status
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                    <div class="slim-scroll chat-items" data-scroll-height="110px" data-scroll-size="0">
                                        <table class="table table-bordered table-white" width="100%">
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                            </tbody>
                                        </table>
                                    </div>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td class="text-center" style="width: 5%;">
                                            <%# Container.DataItemIndex + 1 %>
                                        </td>
                                        <td style="width: 40%;">
                                            <asp:Label ID="lblMMXActivity" runat="server" Text='<%# Eval("Activity") %> '> </asp:Label>
                                        </td>
                                        <td style="width: 55%">
                                            <asp:Label ID="lblMMXStatus" runat="server" Text='<%# Eval("Status") %>'></asp:Label>
                                        </td>
                                    </tr>
                                </ItemTemplate>
                            </asp:ListView>
                        </div>
                    </div>

                    <div class="widget" runat="server" id="Div4">

                        <div class="widget-head">
                            <asp:Label ID="lblMaximAlertsMonitor" runat="server" class="heading" Text="MIMIX Alerts" Style="padding-left: 5px !important;"></asp:Label>
                        </div>
                        <div id="Div5" class="widget-body" runat="server">

                            <table id="tblMimixAlertsMonitor" runat="server" class="table table-striped  table-bordered table-responsive" style="display: table;">
                                <thead>
                                    <tr>
                                        <th class="col-md-4"></th>
                                        <th class="col-md-4">Production Server
                                        </th>
                                        <th class="col-md-4"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr id="tr1">
                                        <td>Audit Count
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblAuditCountPR" runat="server"></asp:Label>
                                        </td>
                                        <td class="text-indent">
                                            <%-- <asp:Label ID="lblAuditCountDR" runat="server"></asp:Label>--%>
                                        </td>
                                    </tr>
                                    <tr id="tr2" runat="server">
                                        <td>Recovery Count
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblRecoveryCountPR" runat="server"></asp:Label>
                                        </td>
                                        <td class="text-indent">
                                            <%-- <asp:Label ID="lblRecoveryCountDR" runat="server"></asp:Label>--%>
                                        </td>
                                    </tr>
                                    <tr id="tr3" runat="server">
                                        <td>Notification Count
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblNotificationCountPR" runat="server"></asp:Label>
                                        </td>
                                        <td class="text-indent">
                                            <%-- <asp:Label ID="lblNotificationCountDR" runat="server"></asp:Label>--%>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <%--   <table id="tblMimixHealthReplication" class="table table-striped table-condensed table-bordered table-responsive monitortable font" runat="server" style="display: table;">
                                                            <thead>
                                                                <tr>
                                                                    <th class="col-md-4">MIMIX Health</th>
                                                                    <th class="col-md-4">Production Server
                                                                    </th>
                                                                    <th class="col-md-4">DR Server
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                 <tr>
                                                                    <td class="col-md-4">DataGroup</td>
                                                                    <td>
                                                                        <asp:Label ID="lblMHDataGroupPR" runat="server"></asp:Label>
                                                                    </td>
                                                                    <td><span id="Span31" runat="server">&nbsp;</span>
                                                                        <asp:Label ID="lblMHDataGroupDR" runat="server"></asp:Label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="col-md-4">Source System</td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMHSourceSysPR" runat="server"></asp:Label>
                                                                    </td>
                                                                    <td><span id="Span30" runat="server">&nbsp;</span>
                                                                        <asp:Label ID="lblMHSourceSysDR" runat="server"></asp:Label>
                                                                    </td>
                                                                </tr>
                                                               
                                                                <tr id="tr13">
                                                                    <td class="col-md-4">Source DB</td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMHSourceDBPR" runat="server"></asp:Label>
                                                                    </td>
                                                                    <td><span id="Span32" runat="server">&nbsp;</span>
                                                                        <asp:Label ID="lblMHSourceDBDR" runat="server"></asp:Label>
                                                                    </td>
                                                                </tr>
                                                                <tr id="tr14" runat="server">
                                                                    <td class="col-md-4">Source Object </td>
                                                                    <td class="text-indent">
                                                                        <asp:Label ID="lblMHSourceObjPR" runat="server"></asp:Label>
                                                                    </td>
                                                                    <td><span id="Span33" runat="server">&nbsp;</span>
                                                                        <asp:Label ID="lblMHSourceObjDR" runat="server"></asp:Label>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>  --%>
                    <div class="widget" runat="server" id="dvMMXHealth">

                        <div class="widget-head">
                            <asp:Label ID="lblMimixHealth" runat="server" class="heading" Text="Mimix Health" Style="padding-left: 5px !important;"></asp:Label>
                        </div>
                        <div id="dvMimixHealth" class="widget-body" runat="server">
                            <asp:ListView ID="lsvMimixHealth" runat="server" Visible="true">
                                <LayoutTemplate>
                                    <table class="table table-bordered table-white" width="100%" style="margin-bottom: 0px;">
                                        <thead>
                                            <tr>
                                                <th style="width: 5%;">Sr. No.
                                                </th>
                                                <th style="width: 8.63%;">DataGroup
                                                </th>
                                                <th style="width: 8.63%;">Source System
                                                </th>
                                                <th style="width: 8.63%;">Source Mgr
                                                </th>
                                                <th style="width: 8.63%;">Source DB
                                                </th>
                                                <th style="width: 8.63%;">Source Object
                                                </th>
                                                <th style="width: 8.63%;">Target System
                                                </th>
                                                <th style="width: 8.63%;">Target Mgr
                                                </th>
                                                <th style="width: 8.63%;">Target DB
                                                </th>
                                                <th style="width: 8.63%;">Target Obj
                                                </th>
                                                <th style="width: 8.63%;">Error DB
                                                </th>
                                                <th style="width: 8.63%;">Error Obj
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                    <div class="slim-scroll chat-items" data-scroll-height="110px" data-scroll-size="0">
                                        <table class="table table-bordered table-white" width="100%">
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                            </tbody>
                                        </table>
                                    </div>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td class="text-center" style="width: 5%;">
                                            <%# Container.DataItemIndex + 1 %>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXDataGroup" runat="server" Text='<%# Eval("DataGroup") %> '> </asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXSourceSystem" runat="server" Text='<%# Eval("SourceSystem") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXSourceMgr" runat="server" Text='<%# Eval("SourceDB") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXSourceDB" runat="server" Text='<%# Eval("SourceDB") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXSourceObject" runat="server" Text='<%# Eval("SourceObject") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXTargetSystem" runat="server" Text='<%# Eval("TargetSystem") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXTargetMgr" runat="server" Text='<%# Eval("TargetMgr") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXTargetDB" runat="server" Text='<%# Eval("TargetDB") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXTargetObj" runat="server" Text='<%# Eval("TargetObject") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXErrorDB" runat="server" Text='<%# Eval("ErrorDB") %>'></asp:Label>
                                        </td>
                                        <td style="width: 8.63%;">
                                            <asp:Label ID="lblMMXErrorObj" runat="server" Text='<%# Eval("ErrorObj") %>'></asp:Label>
                                        </td>
                                    </tr>
                                </ItemTemplate>
                            </asp:ListView>
                        </div>
                    </div>
                    <%--  <div class="widget" runat="server" id="Div6">
                      
                        <div class="widget-head">
                            <asp:Label ID="lblMimixDatalagMoniotr" runat="server" class="heading" Text="MIMIX DataLag" Style="padding-left: 5px !important;"></asp:Label>
                        </div>
                      <div id="Div7" class="widget-body" runat="server">
                            <table id="tblMimixDatalagMonitor" runat="server" class="table table-striped  table-bordered table-responsive" style="display: none; table-layout: fixed;">
                                <thead>
                                    <tr>
                                        <th class="col-md-4"></th>
                                        <th class="col-md-4">Production Server
                                        </th>
                                        <th class="col-md-4">DR Server
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Server Name
                                        </td>
                                        <td><span id="Span24" runat="server">&nbsp;</span>
                                            <asp:Label ID="lblMServerPR" runat="server"></asp:Label>
                                        </td>
                                        <td><span id="Span25" runat="server">&nbsp;</span>
                                            <asp:Label ID="lblMServerDR" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>IP Address
                                        </td>
                                        <td class="text-indent"><span id="Span28" runat="server">&nbsp;</span>
                                            <asp:Label ID="lblMIPPR" runat="server"></asp:Label>
                                        </td>
                                        <td class="text-indent"><span id="Span29" runat="server">&nbsp;</span>
                                            <asp:Label ID="lblMIPDR" runat="server"></asp:Label>
                                        </td>
                                    </tr>

                                    <tr id="tr10">
                                        <td>DataGroup
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblMDataGroupPR" runat="server"></asp:Label>
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblMDataGroupDR" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr id="tr11" runat="server">
                                        <td>DB Sequence
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblMDBSourceSeqPR" runat="server"></asp:Label>
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblMDBTargetSeqDR" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr id="tr9" runat="server">
                                        <td>DB Time
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblMDBSourceTime" runat="server"></asp:Label>
                                        </td>
                                        <td class="text-indent">
                                            <asp:Label ID="lblMDBTargetTime" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr id="tr12" runat="server">
                                        <td>Curent Datalag</td>
                                        <td class="text-indent" colspan="2">
                                            <asp:Label ID="lblMDBCurrentDatalag" runat="server" Text=""></asp:Label></td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>--%>

                    <div class="widget" runat="server" id="Div2">

                        <div class="widget-head">
                            <asp:Label ID="lblmmxdatalag" runat="server" class="heading" Text="MIMIX DataLag" Style="padding-left: 5px !important;"></asp:Label>
                        </div>
                        <div id="Div8" class="widget-body" runat="server">
                            <div class="row">
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4 control-label">DataGroup :</label>
                                            <div class="col-md-8">
                                                <asp:Label ID="lblDataGroup" runat="server" Text=""></asp:Label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="col-md-4 control-label">DatabaseErrors :</label>
                                            <div class="col-md-8">
                                                <asp:Label ID="lblDatabaseErrors" runat="server" Text=""></asp:Label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4 control-label">Elapsed Time :</label>
                                            <div class="col-md-8">
                                                <asp:Label ID="lblElapsedTime" runat="server" Text=""></asp:Label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="col-md-4 control-label">ObjectInErrorAndActive :</label>
                                            <div class="col-md-8">
                                                <asp:Label ID="lblObjectInErrorAndActive" runat="server" Text=""></asp:Label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-md-6">
                                            <label class="col-md-4 control-label">Transfer Def :</label>
                                            <div class="col-md-8">
                                                <asp:Label ID="lblTransferDef" runat="server" Text=""></asp:Label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="col-md-4 control-label">State :</label>
                                            <div class="col-md-8">
                                                <asp:Label ID="lblDataLagState" runat="server" Text=""></asp:Label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <table class="table table-bordered table-responsive" style="width: 100%">
                                <tbody>
                                    <tr>
                                        <td class="text-center" style="width: 10%; vertical-align: middle;">Database
                                            <asp:Label ID="lblDataLagDatabase" runat="server" Text=""></asp:Label>
                                        </td>

                                        <td style="width: 90%">
                                            <table class="table table-striped  table-bordered table-responsive" style="width: 100%; margin-bottom: 0px;">
                                                <thead>
                                                    <tr>
                                                        <th style="background-color: transparent !important; border: 1px solid #ccc !important; width: 20%"></th>
                                                        <th style="width: 20%">Receiver</th>
                                                        <th style="width: 20%">Sequence</th>
                                                        <th style="width: 20%">Date/Time</th>
                                                        <th style="width: 20%">TransPer/Hour</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>Source Journal
                                        
                                                        </td>
                                                        <td>
                                                            <asp:Label ID="lblDBSourceReceiver" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblDBSourceSequnce" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblDBSourceDataTime" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblDBSourceTransPerHour" runat="server" Text=""></asp:Label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>RJ Target Journal                                        
                                                        </td>
                                                        <td>
                                                            <asp:Label ID="lblDBTargetReceiver" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblDBTargetSequnce" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblDBTargetDataTime" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblDBTargetTransPerHour" runat="server" Text=""></asp:Label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Last Read
                                                        </td>
                                                        <td>
                                                            <asp:Label ID="lblDBLastReadReceiver" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblDBLastReadSequence" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblDBLastReadDataTime" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblLastReadTrans" runat="server" Text=""></asp:Label></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>

                                    </tr>
                                    <tr>
                                        <td class="text-center" style="width: 10%; vertical-align: middle;">Object
                                            <asp:Label ID="lblDataLagObject" runat="server" Text=""></asp:Label>
                                        </td>
                                        <td style="width: 90%;">
                                            <table class="table table-striped  table-bordered table-responsive" style="width: 100%; margin-bottom: 0px;">
                                                <thead>
                                                    <tr>
                                                        <th style="background-color: transparent !important; border: 1px solid #ccc !important; width: 20%"></th>
                                                        <th style="width: 20%">Receiver</th>
                                                        <th style="width: 20%">Sequence</th>
                                                        <th style="width: 20%">Date/Time</th>
                                                        <th style="width: 20%">TransPer/Hour</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>Current</td>
                                                        <td>
                                                            <asp:Label ID="lblobjCurrentReceiver" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblobjCurrentSeq" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblobjCurrentDatatime" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblobjCurrentTrans" runat="server" Text=""></asp:Label></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Last</td>
                                                        <td>
                                                            <asp:Label ID="lblobjLastReceiver" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblobjLastSequence" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblobjLastDateTime" runat="server" Text=""></asp:Label></td>
                                                        <td>
                                                            <asp:Label ID="lblobjLastTrans" runat="server" Text=""></asp:Label></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                        </div>
                    </div>

                </div>
            </div>
        </div>

        <div class="innerLR" id="emcMirrorDiv" runat="server" visible="false">
            <%--  <h3>
            <img src="../Images/monitor.png" style="vertical-align: text-top">
            EMC Mirror View</h3>--%>
            <div class="widget">
                <div class="widget-head">
                    <asp:Label ID="lblEmcMirrorView" runat="server" Text="" CssClass="heading" Style="padding-left: 5px !important;"></asp:Label>
                </div>

                <div class="widget-body ">
                    <div id="dvEmcMirrorViewReplicationDetails" runat="server">
                    </div>
                </div>
            </div>

            <div class="widget">
                <div class="widget-head">
                    <asp:Label ID="lblEmcmvmirrornm" runat="server" Visible="false" CssClass="heading" Style="padding-left: 5px !important;"></asp:Label>
                </div>
                <div class="widget-body ">
                    <div id="divEmcmvmirrorMonitoring_scroll" runat="server">

                        <div id="divEmcmvmirrorMonitoring" runat="server">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="widget" runat="server" id="widget3" visible="false">

            <div class="widget-head" id="dvappserviceheader" runat="Server">
                <asp:Label ID="Label1" runat="server" class="heading" Text="Application Services Status" Style="padding-left: 5px !important;"></asp:Label>
            </div>

            <div class="widget-body innerAll inner-2x" id="servicestatus" runat="Server">
                <div id="servicemonitor" class="tabs-content">
                    <asp:ListView ID="lvAppdetails" runat="server" Visible="true">
                        <LayoutTemplate>
                            <table class="table table-bordered table-white" width="100%" style="margin-bottom: 0px;">
                                <thead>
                                    <tr>
                                        <th style="width: 4%;">No.
                                        </th>
                                        <th style="width: 60%;">Service Name
                                        </th>
                                        <th style="width: 36%;">State
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                            <div class="slim-scroll chat-items" data-scroll-height="190px" data-scroll-size="0">
                                <table class="table table-bordered table-white" width="100%">
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                    </tbody>
                                </table>
                            </div>
                        </LayoutTemplate>
                        <ItemTemplate>
                            <tr>
                                <td style="width: 4%;">
                                    <%# Container.DataItemIndex + 1 %>
                                </td>
                                <td style="width: 60%;">
                                    <asp:Label ID="lblServiceName" runat="server" Text='<%# Eval("ServiceName") %> '> </asp:Label>
                                </td>
                                <td style="width: 36%">
                                    <asp:Label ID="lblIconState" runat="server" CssClass='<%# GetStatusIcons(Eval("Status")) %>'></asp:Label>&nbsp;
                                    <asp:Label ID="lblState" runat="server" Text='<%# GetStatus(Eval("Status")) %>'></asp:Label>
                                </td>
                            </tr>
                        </ItemTemplate>
                        <EmptyDataTemplate>
                            <div class="message">
                                No Group present in this Application
                            </div>
                        </EmptyDataTemplate>
                    </asp:ListView>
                </div>
            </div>
        </div>

        <div runat="server" id="dvEmcUnity" visible="false" class="widget">
            <div class="innerLR">
                <uc1:EMCUnityMonitoring runat="server" ID="EMCUnityMonitoring" />
            </div>
        </div>

        <uc1:HuaweiReplicationAsync ID="huaweiReplicationAsync" runat="server" Visible="false" />
        <uc1:HuaweiReplicationSync ID="huaweiReplicationSync" runat="server" Visible="false" />

        <div class="innerLR" id="divRecoverPointInner" runat="server" visible="false">
            <h3>
                <img src="../Images/monitor.png" style="vertical-align: text-top">
                Recover Point Monitor</h3>

            <div class="widget" id="Div6" runat="server">

                <div class="widget-head">
                    <asp:Label runat="server" CssClass="interval-icon" Style="margin-left: 10px;"></asp:Label>
                    <asp:Label ID="lblRecoverPointHead" CssClass="heading" runat="server" Style="float: none; padding-left: 0;" Text=""></asp:Label>
                </div>

                <div class="widget-body " id="">
                    <div id="divRecoverPointReplica" class="tabs-content">
                        <table id="Table9" class="table table-bordered table-condensed table-striped" runat="server">
                            <thead>
                                <tr>
                                    <th class="col-md-3">Component</th>
                                    <th class="col-md-3">Site1</th>
                                    <th class="col-md-3">Site2</th>
                                    <th class="col-md-3" id="hideSite3th" runat="server">Site3</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="col-md-3" rowspan="2" style="vertical-align: middle;">RP Appliance Version</td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="site-icon"></asp:Label>
                                        <asp:Label ID="Label119" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="site-icon"></asp:Label>
                                        <asp:Label ID="Label120" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" id="hideSite3td" runat="server">
                                        <asp:Label runat="server" CssClass="site-icon"></asp:Label>
                                        <asp:Label ID="Label121" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                        <asp:Label ID="Label122" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                        <asp:Label ID="Label123" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" id="hideVersion3td" runat="server">
                                        <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                        <asp:Label ID="Label124" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="Table4" class="table table-bordered table-condensed table-striped" runat="server">
                            <tbody>

                                <tr>
                                    <td class="col-md-3">Latest Image
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label87" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label88" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideLatestImage3td">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label90" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-3">Current Image
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label91" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label94" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideCurrentImage3td">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label97" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-3">Journal Lag
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label1820" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label5" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideJournalLag">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label6" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-3">Protection Window
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label7" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label8" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideProtectionWindows">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label9" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="Table5" class="table table-bordered table-condensed table-striped" runat="server">

                            <tbody>
                                <tr>
                                    <td class="col-md-3">Replication Lag</td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="lag-blue-icon vertical-Middle"></asp:Label>
                                        <asp:Label ID="Label98" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="lag-blue-icon vertical-Middle"></asp:Label>
                                        <asp:Label ID="Label99" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideReplicationLag3td">
                                        <asp:Label runat="server" CssClass="lag-blue-icon vertical-Middle"></asp:Label>
                                        <asp:Label ID="Label100" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-3">Replication Lag Time
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label101" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label102" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideReplicationLagTime3td">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label103" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-3">Replication Lag Data
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-disks"></asp:Label>
                                        <asp:Label ID="Label104" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-disks"></asp:Label>
                                        <asp:Label ID="Label105" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideReplicationLagData3td">
                                        <asp:Label runat="server" CssClass="icon-disks"></asp:Label>
                                        <asp:Label ID="Label106" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-3">Replication Lag Write
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                        <asp:Label ID="Label107" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                        <asp:Label ID="Label108" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideReplicationLagwrite3td">
                                        <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                        <asp:Label ID="Label109" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="Table6" class="table table-bordered table-condensed table-striped" runat="server">

                            <tbody>
                                <tr>
                                    <td class="col-md-3">Storage Access State</td>
                                    <td class="col-md-3">
                                        <asp:Label ID="iconLabel110" runat="server" CssClass="asnych_icon-blue vertical-Middle" Style="background-position: 0 4px;"></asp:Label>
                                        <asp:Label ID="Label110" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" ID="lbliconStorrageAccessState" CssClass=""></asp:Label>
                                        <asp:Label ID="Label111" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideStorageAccessState3td">
                                        <asp:Label ID="iconLabel112" runat="server" CssClass="icon-disable"></asp:Label>
                                        <asp:Label ID="Label112" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="Table7" class="table table-bordered table-condensed table-striped" runat="server">

                            <tbody>
                                <tr>
                                    <td class="col-md-3">Data Transfer Status</td>
                                    <td class="col-md-3">
                                        <asp:Label ID="iconLabel113" runat="server" CssClass="icon-NA"></asp:Label>
                                        <asp:Label ID="Label113" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" ID="lbliconDataTransferStatus" CssClass=""></asp:Label>
                                        <asp:Label ID="Label114" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideDataTransferStatus3td">
                                        <asp:Label ID="iconLabel115" runat="server" CssClass="icon-enable"></asp:Label>
                                        <asp:Label ID="Label115" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table id="Table8" class="table table-bordered table-condensed table-striped" runat="server">

                            <tbody>
                                <tr>
                                    <td class="col-md-3">Data Lag</td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                        <asp:Label ID="Label116" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label117" runat="server" Text=""></asp:Label>
                                    </td>
                                    <td class="col-md-3" runat="server" id="hideDataLag3td">
                                        <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                        <asp:Label ID="Label118" runat="server" Text=""></asp:Label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div id="divEmcsrdfSGLogs" runat="server" visible="false" class="widget">
            <div class="innerLR">
                <uc1:EMCSRDFSGMonitoring ID="EMCSRDFSGMonitoring" runat="server" Visible="true" />
            </div>

        </div>

        <div runat="server" id="divEmcsrdfStar" visible="false" class="widget">
            <div class="innerLR">
                <uc1:EMCSRDFStarMonitoring runat="server" ID="EMCSRDFStarMonitoring" Visible="true" />
            </div>
        </div>

        <div id="divRecoveryPointMulti" runat="server" visible="false">
            <div class="innerLR">
                <uc1:RecoveryPointMultiMonitoringDetails runat="server" ID="RecoveryPointMultiMonitoringDetails" Visible="true" />
            </div>

        </div>

        <div id="VeeamReplicationDiv" runat="server" visible="false">
            <uc2:VeeamReplicationMonitoringDetail runat="server" ID="VeeamReplicationDetail" Visible="true" />
        </div>

        <div id="dvRSync" runat="server" visible="false">
            <div class="innerLR" style="padding: 0;">
                <uc1:RSyncMonitorDetails runat="server" ID="RSyncMonitorDetails" Visible="true" />
            </div>
        </div>

        <div class="widget" runat="server" id="Test" visible="false">

            <div class="widget-head">
                <asp:Label ID="Label2" class="heading" runat="server" Text="Service/Process/Workflow" Style="padding-left: 5px !important;"></asp:Label>
            </div>

            <div class="widget-body" runat="server" id="dvservice">
                <div id="Div1" class="tabs-content" runat="server">
                    <asp:Panel ID="Panel_listview" runat="server">
                        <asp:ListView ID="lvMonitoringService" runat="server" OnItemDataBound="MonitorService_OnItemDataBound" Visible="true">
                            <LayoutTemplate>
                                <table class="table table-bordered table-condensed margin-bottom-none" width="100%">
                                    <thead>
                                        <tr>
                                            <th class="col-md-4">Service Name
                                            </th>
                                            <th class="col-md-4">Server IP/HostName
                                            </th>
                                            <th>Status
                                            </th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />
                                    </tbody>
                                </table>

                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td class="col-md-4">

                                        <asp:Label ID="ServiceId" runat="server" Text='<%# Eval("ServiceId") %>' Visible="false" />
                                        <asp:Label ID="WorkflowactionId" runat="server" Text='<%# Eval("Type") %>' Visible="false" />
                                        <asp:Label ID="lblServiceName" runat="server" Text='' />
                                    </td>
                                    <td class="col-md-4">


                                        <asp:Label ID="lblIpAddress" runat="server" Text='' />
                                    </td>
                                    <td>
                                        <asp:Label ID="Label15" runat="server" CssClass='<%#Eval("Status").ToString()=="2"? "":Eval("Status").ToString()=="1" ? "Replicating" : "InActive" %>'></asp:Label>
                                        <asp:Label ID="lblStatus" runat="server" CssClass='<%#Eval("Status").ToString()=="2"? "": Eval("Status").ToString()=="1" ? "text-success" : "text-danger" %>' Text='<%#Eval("Status").ToString()=="1" ? "Running" : Eval("Status").ToString()=="0" ? "Stopped" : "NA"  %>' />
                                    </td>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                    </asp:Panel>
                </div>
            </div>
        </div>

        <div class="widget" id="divVVR" runat="server" visible="false">
            <div class="widget-body">

                <div class="widget">

                    <div class="widget-head">
                        <asp:Label ID="Label156" class="heading" runat="server" Text="Veritas Volume Replicator (VVR) Replication Component"></asp:Label>
                    </div>

                    <div class="widget-body ">
                        <div class="tabs-content" width="100%">
                            <table id="Table11" runat="server" class="table table-bordered table-white" width="100%">
                                <thead>
                                    <tr>
                                        <th class="col-md-4">Replication Component
                                        </th>
                                        <th class="col-md-4">Production Server
                                        </th>
                                        <th class="col-md-4">DR Server
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>VVR Server - IP Address/HostName
                                        </td>
                                        <td>
                                            <asp:Label ID="spanvvrpr" runat="server"></asp:Label>
                                            <asp:Label ID="lblPRIpAddressPRR" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="spanvvrdr" runat="server"></asp:Label>
                                            <asp:Label ID="lblDRIpAddressDRR" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Disk Group Name
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-disk-symmetrix"></asp:Label>
                                            <asp:Label ID="lblPRDGName" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-disk-symmetrix"></asp:Label>
                                            <asp:Label ID="lblDRDGName" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Replicated Volume Group Name
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                            <asp:Label ID="lblPRRVGName" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="icon-disk-type"></asp:Label>
                                            <asp:Label ID="lblDRRVGName" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>RLINK Name
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                                            <asp:Label ID="lblPRRlinkName" runat="server"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label runat="server" CssClass="replication-file-icon"></asp:Label>
                                            <asp:Label ID="lblDRRlinkName" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</asp:Content>
