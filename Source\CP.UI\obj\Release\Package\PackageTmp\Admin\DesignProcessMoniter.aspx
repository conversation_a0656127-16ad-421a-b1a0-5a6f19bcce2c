﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="DesignProcessMoniter.aspx.cs" Inherits="CP.UI.Admin.DesignProcessMoniter" MasterPageFile="~/Master/BcmsDefault.Master" %>

<%@ Register Assembly="Telerik.Web.UI" Namespace="Telerik.Web.UI" TagPrefix="telerik" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajax" %>

<%@ Register Src="DashboardContols/DataCenter.ascx" TagName="DataCenter" TagPrefix="uc2" %>
<%@ Register Src="DashboardContols/ITInfraSummary.ascx" TagName="ITInfraSummary" TagPrefix="uc3" %>
<%@ Register Src="DashboardContols/EventSummary.ascx" TagName="EventSummary" TagPrefix="uc4" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/DashBoard.js"></script>
    <script src="../Script/context.js"></script>
    <script src="../Script/CollapsibleLists.js"></script>
    <script src="../Script/bootstrap-multiselect.js"></script>

    <link href="../App_Themes/CPTheme/bootstrap-multiselect.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <style type="text/css">
        body {
            font-family: Arial, sans-serif;
            background-size: cover;
            height: 100vh;
        }

        h1 {
            text-align: center;
            font-family: Tahoma, Arial, sans-serif;
            color: #06D85F;
            margin: 80px 0;
        }

        .pdbt0 {
            padding-bottom: 0px !important;
        }

        .box {
            width: 40%;
            margin: 0 auto;
            background: rgba(255,255,255,0.2);
            padding: 35px;
            border: 2px solid #fff;
            border-radius: 20px/50px;
            background-clip: padding-box;
            text-align: center;
        }

        .button {
            font-size: 1em;
            padding: 10px;
            color: #fff;
            border: 2px solid #06D85F;
            border-radius: 20px/50px;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease-out;
        }

            .button:hover {
                background: #06D85F;
            }

        .overlay {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            transition: opacity 500ms;
            visibility: hidden;
            opacity: 0;
        }

            .overlay:target {
                visibility: visible;
                opacity: 1;
            }

        .popup {
            margin: 70px auto;
            padding: 20px;
            background: #fff;
            border-radius: 5px;
            width: 30%;
            position: relative;
            transition: all 5s ease-in-out;
        }

            .popup h2 {
                margin-top: 0;
                color: #333;
                font-family: Tahoma, Arial, sans-serif;
            }

        .wd95 {
            width: 95%;
        }

        .popup .close {
            position: absolute;
            top: 20px;
            right: 30px;
            transition: all 200ms;
            font-size: 30px;
            font-weight: bold;
            text-decoration: none;
            color: #333;
        }

            .popup .close:hover {
                color: #06D85F;
            }

        .popup .content {
            max-height: 30%;
            overflow: auto;
        }

        .mr23 {
            margin-right: 23px !important;
        }
        /*.modal#loadService {
            z-index: 1111;
        }*/

        .rht-btn {
            z-index: 1;
            position: relative;
        }

            .rht-btn button {
                font-size: 12px;
                margin: 10px 10px;
            }

        .multiselect.dropdown-toggle.btn.btn-default {
            width: 100%;
        }

        .multiselect-container.dropdown-menu.pull-right {
            width: 100%;
        }

        .hide-native-select .multiselect .multiselect-selected-text {
            float: left;
        }

        .hide-native-select .multiselect .caret {
            float: right;
            margin-top: 9px;
        }

        .chkbx {
            padding-top: 6px;
        }

            .chkbx label {
                vertical-align: sub;
            }

        #divservicesDetailsummary .widget .widget-head {
            padding: 0;
        }

        #divservicesDetailsummary .widget.widget-tabs > .widget-head ul li.active {
            background: #4a8bc2 none repeat scroll 0 0;
            color: #fff;
            height: 34px;
        }

            #divservicesDetailsummary .widget.widget-tabs > .widget-head ul li.active a {
                color: #fff;
            }

                #divservicesDetailsummary .widget.widget-tabs > .widget-head ul li.active a i::before {
                    color: #fff;
                }

        #divservicesDetailsummary .glyphicons a i {
            width: 23px;
            display: inline-block;
        }

        #divservicesDetailsummary .widget .widget-head ul .col-md-6 {
            width: 100% !important;
        }

        .RadWindow_Metro table.rwTitlebarControls em {
            text-transform: none !important;
        }

        #divservicesDetailsummary .widget.widget-tabs > .widget-head ul li a i::before {
            text-shadow: none;
        }

        #divservicesDetailsummary .widget .widget-head > .glyphicons i::before, #divservicesDetailsummary .widget .widget-head ul .glyphicons i::before {
            font-size: 20px;
            line-height: 27px;
        }

        .treeView li.clicked {
            font-weight: bold;
            color: #4a8bc2;
        }


        #tab-ImapctRule {
            padding: 10px;
        }

        @media screen and (max-width: 700px) {
            .box {
                width: 70%;
            }

            .popup {
                width: 70%;
            }
        }
    </style>

    <style>
        .RadComboBoxDropDown .rcbList li {
            font-size: 12px;
        }

            .RadComboBoxDropDown .rcbList li:nth-child(even) {
                background-color: rgba(0,0,3,0.1);
            }

        .RadComboBox table {
            background-color: #efefef !important;
            background-image: linear-gradient(to bottom, #f4f4f4, #e7e7e7) !important;
            background-repeat: repeat-x !important;
            border: 1px solid #cecece !important;
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .RadComboBox .rcbEmptyMessage {
            font-style: normal !important;
        }

        .RadComboBoxDropDown {
            border: 1px solid #cecece !important;
        }

        .importexportbtn {
            margin-top: 5px;
            margin-right: 10px;
        }

        #ctl00_cphBody_RadComboBox1 table, #ctl00_cphBody_RadComboBox2 table, #ctl00_cphBody_RadComboBox3 table {
            width: 346px !important;
        }



        .Blink {
            fill: none;
            stroke: #4a8bc2;
            stroke-width: 1.8px;
        }

        .Rlink {
            fill: none;
            stroke: #ff0307;
            stroke-width: 1.8px;
        }

        .Ylink {
            fill: none;
            stroke: #eac200;
            stroke-width: 1.8px;
        }

        .Olink {
            fill: none;
            stroke: #ed6800;
            stroke-width: 1.8px;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function () {
            $("#popClose").click(function (e) {
                //  $(".modal").css("display", "none");
                //  $(".bg").css("display", "none");
            });
            // $('#ddlAttachProfile').multiselect();

            //$('a[data-toggle="tab"]').on('show.bs.tab', function (e) {
            //    localStorage.setItem('activeTab', $(e.target).attr('href'));

            //});

            //var activeTab = localStorage.getItem('activeTab');


            //if (activeTab) {

            //    $('#myTab a[href="' + activeTab + '"]').tab('show');

            //}

        });
        function displayUser(id) {
            if (id == 'Profile') {
                document.getElementById('divProfile').style.display = 'block';
                document.getElementById('divUser').style.display = 'none';

            }
            if (id == 'User') {
                document.getElementById('divProfile').style.display = 'none';
                document.getElementById('divUser').style.display = 'block';

            }

        }

        function OnChildWindowClosed(sender, eventArgs) {
            // document.location.reload(); // there may be a cleaner way to do the refresh

            <%= Page.ClientScript.GetPostBackEventReference(imgRefreshBSToBS, String.Empty) %>;

            window["<%= upnlBStoBSlst.ClientID %>"].submit;



        }

        function OnChildWindowClosed1(sender, eventArgs) {
            // document.location.reload(); // there may be a cleaner way to do the refresh

            <%= Page.ClientScript.GetPostBackEventReference(imgRefreshInfraToBF, String.Empty) %>;

            window["<%= upnl.ClientID %>"].submit;



        }

    </script>
    <style type="text/css">
        .treeView {
            -moz-user-select: none;
            position: relative;
            padding-left: 0px;
            left: 15px;
        }

            .treeView ul {
                margin: 0 0 0 -1.5em;
                padding: 0 0 0 1.5em;
            }

                .treeView ul ul {
                    background: url('../Images/icons/tree-list-item-contents.png') repeat-y left;
                    background-position: 20px 0px;
                }

        /*.treeView li.lastChild > ul*/ #ctl00_cphBody_newList li ul li:last-child ul {
            background-image: none;
        }

        .treeView li {
            margin: 0;
            padding: 0;
            background: url('../Images/icons/tree-list-item-root.png') no-repeat top left;
            list-style-position: inside;
            color: #515151 !important;
            cursor: pointer;
        }

            .treeView li.collapsibleListOpen {
                list-style-image: url('../Images/ServiceLogo/tree-button-open.png');
                cursor: pointer;
                padding-top: 3px;
                background-image: url('../Images/icons/tree-list-item-tp.png');
                background-position: 20px 0px;
            }

            .treeView li.collapsibleListClosed {
                list-style-image: url('../Images/ServiceLogo/tree-button-closed.png');
                cursor: pointer;
                padding-top: 3px;
                background-image: url('../Images/icons/tree-list-item-tp.png');
                background-position: 20px 0px;
            }

            .treeView li li {
                background-image: url('../Images/icons/tree-list-item.png');
                padding-left: 1.5em;
            }

            /*.treeView li.lastChild*/ .treeView li ul li ul li:last-child {
                background-image: url('../Images/icons/tree-list-item-last.png');
            }

            /*.treeView li.collapsibleListOpen {
                    background-image: url('../Images/icons/tree-list-item-open.png');
                }*/

            /*.treeView li.collapsibleListOpen.lastChild*/ .treeView li ul li.collapsibleListOpen:last-child, .treeView li ul li.collapsibleListClosed:last-child {
                /*background-image: url('../Images/icons/tree-list-item-last-open.png');*/
                padding-left: 19px;
            }

        #ctl00_cphBody_newList li ul li ul li ul, #userList li ul li ul li ul li ul li ul {
            padding-left: 37px;
        }

        #userList li ul li ul li ul {
            background-image: none;
        }

            #userList li ul li ul li ul li ul {
                background-image: url("../Images/icons/tree-list-item-contents.png");
            }

            #userList li ul li ul li ul li:last-child ul {
                background-image: none;
            }

            #userList li ul li ul li ul li ul li ul {
                background-image: none;
            }

        #btnSaveProfile {
            font-size: 12px;
            margin-bottom: 5px;
            margin-top: -5px;
            padding: 5px 10px;
            margin-right: 10px;
        }

        .toprow {
            border-bottom: 1px solid #ddd;
            padding: 10px 0px 0px 15px;
            margin-bottom: 0px;
        }

        .lefttree {
            width: 20% !important;
            border-right: 1px solid #ddd;
            min-height: 473px;
        }

        .rightdiag {
            width: 80% !important;
        }

        .treeTitle {
            background-color: #ebebeb;
            padding: 6px 15px 7px;
        }

        .form-horizontal .form-group {
            margin-left: 0px !important;
            margin-right: 0px !important;
        }

        label, .font13 {
            font-size: 13px !important;
        }

        .font12 {
            font-size: 12px !important;
        }

        .hide-native-select .btn-group {
            width: 95%;
        }
    </style>



</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <telerik:RadCodeBlock ID="RadCodeBlock1" runat="server">
    </telerik:RadCodeBlock>
    <telerik:RadWindowManager ID="RadWindowManager1" runat="server" IconUrl="~/Images/icons/alerts/sign_add.png">
        <Windows>
            <telerik:RadWindow ID="TelRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false" Title="Business Service Impact Analysis"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="520"
                Width="1200" Skin="Metro" OnClientClose="OnChildWindowClosed" />
        </Windows>
        <Windows>
            <telerik:RadWindow ID="TelRadWindow2" AutoSize="false" runat="server" VisibleStatusbar="false" Title="Business Functions Impact Analysis"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="520"
                Width="1200" Skin="Metro" OnClientClose="OnChildWindowClosed1" />
        </Windows>
        <%--  <Windows>
            <telerik:RadWindow ID="TelImpRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="620" ShowContentDuringLoad="false"
                Width="1260" Skin="Metro" />
        </Windows>
        <Windows>
            <telerik:RadWindow ID="TelImpBrtoBfRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="520" ShowContentDuringLoad="false" OnClientClose="OnChildWindowClosed1"
                Width="1200" Skin="Metro" />
        </Windows>--%>
    </telerik:RadWindowManager>

    <div class="innerLR">
        <h3 style="line-height: 40px">
            <img src="../Images/ServiceLogo/process-designer_icon.png">
            Process Designer</h3>

        <div class="widget widget-heading-simple widget-body-white ">
            <div class="widget-body padding-none">

                <div class="col-md-12 form-horizontal uniformjs padding-none-LR">


                    <div id="divservicesDetailsummary">

                        <div id="divtopline" runat="server">

                            <%--  <asp:UpdatePanel ID="udpgraph" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="false">
                                <ContentTemplate>--%>
                            <div class="widget widget-tabs widget-tabs-icons-only-2 widget-activity margin-none">
                                <!-- Tabs Heading -->
                                <div class="widget-head">
                                    <ul class="col-md-12" id="myTab">

                                        <li class="col-md-6 active glyphicons cargo" runat="server">
                                            <a data-toggle="tab" href="#tab-ServiceDesign"><i></i>Business Service Design</a>

                                        </li>
                                        <%--<li class="col-md-6 glyphicons impact-icon">
                                            <a data-toggle="tab" href="#tab-ImapctRule"><i></i>Impact Rule</a>

                                        </li>--%>
                                    </ul>
                                </div>
                                <div class="tab-content">
                                    <!-- Filter Users Tab -->
                                    <div class="tab-pane active" id="tab-ServiceDesign">

                                        <asp:UpdatePanel ID="udpServiceDesign" runat="server" UpdateMode="Conditional">
                                            <ContentTemplate>

                                                <div class="form-group toprow">
                                                    <div class="text-left" style="display: inline-block; width: 50%; float: left;">
                                                        <asp:RadioButton ID="btnProfile" runat="server" TextAlign="Right" Text="By Business Service Profile" Checked="true" onclick="return displayUser('Profile')" />
                                                        <asp:RadioButton ID="btnUser" runat="server" TextAlign="Right" Text="By User" Checked="false" onclick="return displayUser('User')" />
                                                    </div>
                                                    <div class="text-right" style="display: inline-block; width: 50%;">
                                                        <button id="btnSaveProfile" class="btn btn-primary" onclick=" return saveServicePopup();">Save Business Service Group</button>
                                                    </div>
                                                </div>
                                                <div class="row" style="margin-left: 0px !important; margin-right: 0px !important;">
                                                    <div class="col-md-3 lefttree padding-none-LR" style="">

                                                        <div id="divProfile" style="display: block">
                                                            <div class="treeTitle">
                                                                <img src="../Images/ServiceLogo/profile-header-blue_icon.png" />
                                                                <span class="font13">By Business Service Profile</span>
                                                            </div>
                                                            <ul class="treeView" id="newList" runat="server">
                                                            </ul>
                                                        </div>
                                                        <div id="divUser" style="display: none">
                                                            <div class="treeTitle">
                                                                <img src="../Images/ServiceLogo/user-header-blue_icon.png" />
                                                                <label>By User</label>
                                                            </div>
                                                            <ul class="treeView" id="userList">
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-9 rightdiag" id="dvMainDiagram" style="height: 500px; overflow: hidden;">
                                                        <div id="bsbody3">
                                                        </div>
                                                    </div>
                                                </div>
                                            </ContentTemplate>
                                        </asp:UpdatePanel>
                                    </div>

                                    <div class="tab-pane" id="tab-ImapctRule">

                                        <asp:UpdatePanel ID="udpImapctRule" runat="server" UpdateMode="Conditional">
                                            <ContentTemplate>

                                                <telerik:RadMultiPage runat="server" ID="RadMultiPage3" SelectedIndex="0">

                                                    <telerik:RadPageView ID="RadPageView8" runat="server" Selected="true">


                                                        <telerik:RadTabStrip runat="server" ID="RadTabStrip1" MultiPageID="RadMultiPage2" SelectedIndex="0" Skin="Silk" Width="1296" OnTabClick="RadTabStrip1_TabClick">
                                                            <Tabs>
                                                                <telerik:RadTab Value="BstoBsTab" Text="Business Service to Business Service" Width="300px"></telerik:RadTab>
                                                                <telerik:RadTab Value="InfraToBsTab" Text="Business Service to InfraObject" Width="300px"></telerik:RadTab>

                                                            </Tabs>
                                                        </telerik:RadTabStrip>



                                                        <telerik:RadMultiPage runat="server" ID="RadMultiPage2" SelectedIndex="0" CssClass="outerMultiPage">

                                                            <telerik:RadPageView runat="server" ID="RadPageView4">
                                                                <telerik:RadSplitter ID="RadSplitter3" runat="server" Height="450" Width="1296" Orientation="Horizontal">
                                                                    <telerik:RadPane ID="RadPane5" runat="server" Height="400" MinHeight="350" MaxHeight="750" Width="1296">


                                                                        <asp:UpdatePanel ID="upBStoBS" runat="server" UpdateMode="Conditional" class="col-md-12 form-horizontal uniformjs">
                                                                            <ContentTemplate>



                                                                                <telerik:RadComboBox ID="RadComboBox3" runat="server" Width="350" Enabled="true" CssClass="margin-top" DropDownCssClass="widget border"
                                                                                    DropDownWidth="700" EmptyMessage="Search" HighlightTemplatedItems="true"
                                                                                    EnableLoadOnDemand="true" AllowCustomText="true" MarkFirstMatch="true" Filter="Contains"
                                                                                    Label="Select Business Service to view Rules" LabelCssClass="control-label" Skin="" EnableEmbeddedSkins="false" OnSelectedIndexChanged="RadComboBox3_SelectedIndexChanged" AutoPostBack="true">
                                                                                    <HeaderTemplate>
                                                                                        <div class="widget-head">
                                                                                            <div class="form-group text-bold">
                                                                                                <div class="col-md-6">
                                                                                                 Business Service Group
                                                                                                </div>
                                                                                                <div class="col-md-6">
                                                                                                    Profile
                                                                                                </div>

                                                                                            </div>
                                                                                        </div>
                                                                                    </HeaderTemplate>
                                                                                    <ItemTemplate>
                                                                                        <div class="row">
                                                                                            <div class="form-group">
                                                                                                <div class="col-md-5">

                                                                                                    <%# Eval("ServiceName")%>
                                                                                                </div>
                                                                                                <div class="col-md-5">


                                                                                                    <%# Eval("ProfileName")%>
                                                                                                </div>

                                                                                                <div class="col-md-2" style="display: none">

                                                                                                    <%# Eval("ServiceId")%>
                                                                                                </div>
                                                                                                <div class="col-md-2" style="display: none">

                                                                                                    <%# Eval("ProfileId")%>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </ItemTemplate>

                                                                                </telerik:RadComboBox>
                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                        <hr class="margin-bottom-none" />

                                                                        <asp:UpdatePanel ID="upnlBStoBSlst" runat="server" UpdateMode="Conditional">
                                                                            <ContentTemplate>
                                                                                <div id="divemptyFunctionPopupBstoBs"></div>
                                                                                <div id="divbsViewBStoBS" style="display: none;">
                                                                                    <div id="bsbodyBstoBs" runat="server">
                                                                                    </div>
                                                                                    <div class="col-md-4">
                                                                                        <a id="btnReloadBstoBS" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                                                                        <a id="btnResetbstobs" class="reset-icon margin-top" title="Back to initial zoom position">&nbsp;</a>
                                                                                    </div>
                                                                                    <div class="col-md-8 text-right">
                                                                                        <img src="../Images/icons/yellow-dot.png" />
                                                                                        <label class="margin-right">Partial Impact</label>
                                                                                        <img src="../Images/icons/orange-dot.png" />
                                                                                        <label class="margin-right">Major Impact</label>
                                                                                        <img src="../Images/icons/red-dot.png" />
                                                                                        <label class="margin-right">Total Impact</label>
                                                                                    </div>
                                                                                </div>

                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                    </telerik:RadPane>
                                                                    <telerik:RadSplitBar ID="Radsplitbar3" runat="server">
                                                                    </telerik:RadSplitBar>
                                                                    <telerik:RadPane ID="RadPane6" runat="server" MaxHeight="250" Height="150">


                                                                        <div class="col-md-12">

                                                                            <div class="widget-head">
                                                                                <h4 class=" heading">Impact Relationship View</h4>
                                                                                <asp:UpdatePanel ID="UpdatePanel6" runat="server" UpdateMode="Conditional" class="text-right">
                                                                                    <ContentTemplate>
                                                                                        <asp:ImageButton runat="server" ID="imgRefreshBSToBS" OnClick="imgRefreshBSToBS_Click" AlternateText=" " Width="0" Height="0" Visible="true" />

                                                                                        <asp:ImageButton runat="server" ID="imgBtnBstoBS" OnClick="imgBtnBstoBS_Click" Visible="false" Enabled="false" ImageUrl="../Images/plus-icon.png" />

                                                                                    </ContentTemplate>
                                                                                </asp:UpdatePanel>

                                                                            </div>
                                                                            <div class="widget-body padding-none">
                                                                                <asp:UpdatePanel ID="upBStoBSRuleList" runat="server" UpdateMode="Conditional">
                                                                                    <ContentTemplate>
                                                                                        <asp:ListView ID="lvBStoBSlst" runat="server" OnItemDataBound="lvBStoBSlst_ItemDataBound" OnItemEditing="lvBStoBSlst_ItemEditing" OnItemCommand="lvBStoBSlst_ItemCommand" OnItemDeleting="lvBStoBSlst_ItemDeleting">
                                                                                            <LayoutTemplate>
                                                                                                <table id="tblebia3" class="table table-bordered table-condensed EntityImptTable margin-bottom-none" style="table-layout:fixed;">
                                                                                                    <thead>

                                                                                                        <tr>
                                                                                                            <th style="width: 5%;" class="text-center">#</th>
                                                                                                            <%--<th>Rule</th>--%>
                                                                                                            <th style="width: 12%;">Rule Code</th>
                                                                                                            <th style="width: 25%;">If Business Service</th>

                                                                                                            <th style="width: 15%;">Impact</th>
                                                                                                            <th style="width: 20%;">Business Service</th>
                                                                                                            <%--<th style="width: 15%;">Impact</th>--%>
                                                                                                            <th style="width: 15%;">Effective Date</th>
                                                                                                            <th style="width: 8%;" class="text-center">Action</th>
                                                                                                        </tr>
                                                                                                    </thead>
                                                                                                </table>
                                                                                                <div class=" ">
                                                                                                    <table class="table table-bordered table-condensed EntityImptTable " style="table-layout:fixed;">
                                                                                                        <tbody>
                                                                                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                                                        </tbody>

                                                                                                    </table>
                                                                                                </div>
                                                                                            </LayoutTemplate>

                                                                                            <ItemTemplate>
                                                                                                <tr>
                                                                                                    <td style="width: 5%;"  class="text-center">
                                                                                                        <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %></td>
                                                                                                    <td style="width: 12%;">
                                                                                                        <asp:Label ID="Label1" runat="server" Text='<%# Eval("RuleCode") %>'></asp:Label>
                                                                                                    </td>
                                                                                                    <td style="width: 25%;">
                                                                                                        <asp:Label ID="lblParentBSID" runat="server" class="text-bold " Text='<%# Eval("ParentBSId") %>'></asp:Label>
                                                                                                        &nbsp;&nbsp;  is 
                                                                                                    </td>
                                                                                                    <td style="width: 15%;">
                                                                                                        <asp:Label ID="lblParentBSImpactID" runat="server" class="label label-red " Text='<%# Eval("ParentBSImpactID") %>'></asp:Label>
                                                                                                        &nbsp;&nbsp;  By  
                                                                                                    </td>
                                                                                                    <td style="width: 20%;">
                                                                                                        <asp:Label ID="lblChildBSID" runat="server" class="text-bold" Text='<%# Eval("ChildBSId") %>'></asp:Label>
                                                                                                        <%-- <label class="label label-primary ">BS Object1</label>--%>
                                                                                                    </td>
                                                                                                    <%-- <td style="width: 15%;">
                                                                            <asp:Label ID="lblChildBSImpactID" runat="server" class="label label-orange " Text='<%# Eval("ChildBSImpactID") %>'></asp:Label>
                                                                            <%-- <label class="label label-warning ">Partially</label>
                                                                        </td>--%>
                                                                                                    <td style="width: 15%;">
                                                                                                        <asp:Label ID="lblEffectiveDateFrom_InfraToBF" runat="server" Text='<%# Eval("EffectiveDateFrom") %>'></asp:Label><%--25-02-2015 16:26:10--%>
                                                                                                    </td>

                                                                                                    <td style="width: 8%;" class="text-center">
                                                                                                        <asp:ImageButton ID="btnBSSaveProfile" runat="server" ImageUrl="../Images/icons/pencil.png" CommandName="Edit" ToolTip="Edit" Enabled="<%# IsSuperAdmin %>"></asp:ImageButton>
                                                                                                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                                                                        <asp:ImageButton ID="btnBSShowDiagram" ImageUrl="../images/report-icon.png" runat="server" CommandName="View" ToolTip="View"></asp:ImageButton></td>
                                                                                                    <ajax:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ImgDelete" ConfirmText='<%# "Please confirm if you want to Delete " + Eval("RuleCode") +" " + " Rule?" %>'
                                                                                                        OnClientCancel="CancelClick">
                                                                                                    </ajax:ConfirmButtonExtender>
                                                                                                </tr>
                                                                                            </ItemTemplate>
                                                                                        </asp:ListView>
                                                                                        <div class="row">
                                                                                            <div class="col-xs-6">
                                                                                                <asp:DataPager ID="dataPager5" runat="server" PagedControlID="lvBStoBSlst">
                                                                                                    <Fields>
                                                                                                        <asp:TemplatePagerField>
                                                                                                            <PagerTemplate>
                                                                                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                                                                <br />
                                                                                                            </PagerTemplate>
                                                                                                        </asp:TemplatePagerField>
                                                                                                    </Fields>
                                                                                                </asp:DataPager>
                                                                                            </div>
                                                                                            <div class="col-xs-6 text-right">
                                                                                                <asp:DataPager ID="dataPager6" runat="server" PagedControlID="lvBStoBSlst" PageSize="100">
                                                                                                    <Fields>
                                                                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                                                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                                                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                                                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                                                                            NumericButtonCssClass="btn-pagination" />
                                                                                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                                                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                                                                    </Fields>
                                                                                                </asp:DataPager>
                                                                                            </div>
                                                                                        </div>
                                                                                    </ContentTemplate>
                                                                                </asp:UpdatePanel>
                                                                            </div>
                                                                        </div>

                                                                        <%--   </div>--%>
                                                                    </telerik:RadPane>
                                                                </telerik:RadSplitter>
                                                            </telerik:RadPageView>

                                                            <telerik:RadPageView runat="server" ID="RadPageView2">
                                                                <telerik:RadMultiPage runat="server" ID="RadMultiPage1" SelectedIndex="0">
                                                                    <telerik:RadPageView runat="server" ID="RadPageView1">
                                                                        <telerik:RadSplitter ID="RadSplitter2" runat="server" Height="450" Width="1296" Orientation="Horizontal">
                                                                            <telerik:RadPane ID="RadPane3" runat="server" Height="400" MinHeight="350" MaxHeight="750" Width="1296">
                                                                                <asp:UpdatePanel ID="UpFilter" runat="server" UpdateMode="Conditional" class="col-md-12 form-horizontal uniformjs">
                                                                                    <ContentTemplate>

                                                                                        <telerik:RadComboBox ID="RadComboBox1" runat="server" Width="350" CssClass="margin-top" DropDownCssClass="widget border"
                                                                                            DropDownWidth="350" EmptyMessage="Search" HighlightTemplatedItems="true"
                                                                                            EnableLoadOnDemand="true" AllowCustomText="true" MarkFirstMatch="true" Filter="Contains"
                                                                                            Label="Select Business Service to view Rules" LabelCssClass="control-label" Skin="" EnableEmbeddedSkins="false" OnSelectedIndexChanged="RadComboBox1_SelectedIndexChanged" AutoPostBack="true">
                                                                                            <%--  <HeaderTemplate>
                                                                                                <div class="widget-head">
                                                                                                    <div class="form-group text-bold">
                                                                                                        <div class="col-md-3">
                                                                                                            Business Service 
                                                                                                        </div>
                                                                                                       <div class="col-md-4">
                                                                                                            Infraobject
                                                                                                        </div>
                                                                                                        <div class="col-md-5">
                                                                                                            Infraobject Compenent
                                                                                                        </div>

                                                                                                    </div>
                                                                                                </div>
                                                                                            </HeaderTemplate>--%>
                                                                                            <ItemTemplate>
                                                                                                <div class="col-md-12 padding-none">
                                                                                                    <%# Eval("businesService")%>
                                                                                                </div>
                                                                                                <div class="col-md-3" style="display: none">
                                                                                                    <%# Eval("bsid")%>
                                                                                                </div>

                                                                                                <%--<div class="col-md-4">
                                                                                                            <%# Eval("Infraobject")%>
                                                                                                        </div>--%>
                                                                                                <%--<div class="col-md-5">
                                                                                                            <%# Eval("Infracomponent")%>
                                                                                                        </div>--%>
                                                                                            </ItemTemplate>

                                                                                        </telerik:RadComboBox>

                                                                                    </ContentTemplate>
                                                                                </asp:UpdatePanel>
                                                                                <hr class="margin-bottom-none" />

                                                                                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                                                                    <ContentTemplate>
                                                                                        <div id="divemptyFunctionPopup"></div>
                                                                                        <div id="divbsView" style="display: none;">
                                                                                            <div id="bsbody" runat="server">
                                                                                            </div>
                                                                                            <div id="bsbody2" runat="server">
                                                                                            </div>
                                                                                            <div class="col-md-4">
                                                                                                <a id="btnReload" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                                                                                <a id="btnReset" class="reset-icon margin-top" title="Back to initial zoom position">&nbsp;</a>
                                                                                            </div>
                                                                                            <div class="col-md-8 text-right">
                                                                                                <img src="../Images/icons/yellow-dot.png" />
                                                                                                <label class="margin-right">Partial Impact</label>
                                                                                                <img src="../Images/icons/orange-dot.png" />
                                                                                                <label class="margin-right">Major Impact</label>
                                                                                                <img src="../Images/icons/red-dot.png" />
                                                                                                <label class="margin-right">Total Impact</label>
                                                                                            </div>
                                                                                        </div>

                                                                                    </ContentTemplate>
                                                                                </asp:UpdatePanel>

                                                                            </telerik:RadPane>
                                                                            <telerik:RadSplitBar ID="Radsplitbar2" runat="server">
                                                                            </telerik:RadSplitBar>
                                                                            <telerik:RadPane ID="RadPane4" runat="server" MaxHeight="250" Height="150">

                                                                                <%--                                        <div class="row">--%>
                                                                                <div class="col-md-12">

                                                                                    <div class="widget-head">
                                                                                        <h4 class="heading padding-none-LR">Impact Relationship View</h4>
                                                                                        <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional" class="text-right">
                                                                                            <ContentTemplate>
                                                                                                <asp:ImageButton runat="server" ID="imgRefreshInfraToBF" OnClick="imgRefreshInfraToBF_Click" AlternateText=" " Width="0" Height="0" Visible="true" />


                                                                                            </ContentTemplate>
                                                                                        </asp:UpdatePanel>

                                                                                    </div>
                                                                                    <div class="widget-body padding-none">
                                                                                        <asp:UpdatePanel ID="upnl" runat="server" UpdateMode="Conditional">
                                                                                            <ContentTemplate>
                                                                                                <asp:ListView ID="lvInfratoBSlst" runat="server" OnItemDataBound="lvInfratoBSlst_ItemDataBound" OnItemEditing="lvInfratoBSlst_ItemEditing" OnItemCommand="lvInfratoBSlst_ItemCommand" OnItemDeleting="lvInfratoBSlst_ItemDeleting">
                                                                                                    <LayoutTemplate>
                                                                                                        <table id="tblebia3" class="table table-bordered table-condensed EntityImptTable margin-bottom-none" style="table-layout:fixed;">
                                                                                                            <thead>

                                                                                                                <tr>
                                                                                                                    <th style="width: 3%;">#</th>
                                                                                                                    <%--<th>Rule</th>--%>
                                                                                                                    <th style="width: 10%;">Rule Code</th>
                                                                                                                    <th style="width: 20%;">If Infraobject Component</th>
                                                                                                                    <th style="width: 15%;">Business Function</th>
                                                                                                                    <th style="width: 10%;">Impact</th>
                                                                                                                    <th style="width: 14%;">Business Service</th>
                                                                                                                    <th style="width: 10%;">Impact</th>
                                                                                                                    <th style="width: 12%;">Effective Date</th>
                                                                                                                    <th style="width: 6%;" class="text-center">Action</th>
                                                                                                                </tr>
                                                                                                            </thead>
                                                                                                        </table>
                                                                                                        <div class=" ">
                                                                                                            <table class="table table-bordered table-condensed EntityImptTable " style="table-layout:fixed;">
                                                                                                                <tbody>
                                                                                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                                                                </tbody>

                                                                                                            </table>
                                                                                                        </div>
                                                                                                    </LayoutTemplate>

                                                                                                    <ItemTemplate>
                                                                                                        <tr>
                                                                                                            <td style="width: 3%;">
                                                                                                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %></td>
                                                                                                            <td style="width: 10%">
                                                                                                                <asp:Label ID="Label1" runat="server" Text='<%# Eval("RuleCode") %>'></asp:Label></td>
                                                                                                            <td style="width: 20%;" class="tdword-wrap"><%--IF Infraobject Component --%>
                                                                                                                <asp:Label ID="lblInfraComponentId" runat="server" class="label label-red " Text='<%# Eval("InfraComponentId") %>'></asp:Label>
                                                                                                                <asp:Label ID="lblComponentID" Visible="false" runat="server" class="label label-red " Text='<%# Eval("InfraComponentId") %>'></asp:Label>
                                                                                                                <asp:Label ID="lblInfraComponentType" runat="server" class="label label-primary " Text='<%# Eval("InfraComponentType") %>' Visible="false"></asp:Label>
                                                                                                                <asp:Label ID="lblInfraID" runat="server" class="label label-primary " Text='<%# Eval("InfraID") %>' Visible="false"></asp:Label>
                                                                                                                is not available then 
                                                                                                            </td>
                                                                                                            <td style="width: 15%;" class="tdword-wrap">
                                                                                                                <asp:Label ID="lblBFID" runat="server" class="text-bold " Text='<%# Eval("BFID") %>'></asp:Label>
                                                                                                                will be   
                                                                                                            </td>
                                                                                                            <td style="width: 10%;" class="tdword-wrap">
                                                                                                                <asp:Label ID="lblBFImpactID" runat="server" CssClass="label label-yellow" Text='<%# Eval("BFImpactID") %>'></asp:Label>
                                                                                                                and  
                                                                                                            </td>
                                                                                                            <td style="width: 14%;" class="tdword-wrap">
                                                                                                                <asp:Label ID="lblBSID" runat="server" class="text-bold " Text='<%# Eval("BSId") %>' Visible="false"></asp:Label>
                                                                                                                <asp:Label ID="lblBSName" runat="server" class="text-bold " ToolTip='<%# Eval("BSName") + "  will be" %> '  Text='<%# Eval("BSName") %>'></asp:Label>
                                                                                                                <%-- <label class="label label-primary ">BS Object1</label>--%>
                                                                                will be   
                                                                                                            </td>
                                                                                                            <td style="width: 10%;">
                                                                                                                <asp:Label ID="lblBSImpactId" runat="server" class="label label-yellow " Text='<%# Eval("BSImpactID") %>'></asp:Label>
                                                                                                            </td>
                                                                                                            <td style="width: 12%;">
                                                                                                                <asp:Label ID="lblEffectiveDateFrom_InfraToBF" runat="server" Text='<%# Eval("EffectiveDateFrom") %>'></asp:Label>
                                                                                                            </td>

                                                                                                            <td style="width: 6%;" class="text-center">
                                                                                                                <asp:ImageButton ID="btnSaveProfile1" runat="server" ImageUrl="../Images/icons/pencil.png" CommandName="Edit" ToolTip="Edit" Enabled="<%# IsSuperAdmin %>"></asp:ImageButton>
                                                                                                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                                                                                <asp:ImageButton ID="btnShowDiagram" ImageUrl="../images/report-icon.png" runat="server" CommandName="View" ToolTip="View"></asp:ImageButton>
                                                                                                                <ajax:ConfirmButtonExtender ID="ConfirmButtonExtender2" runat="server" TargetControlID="ImgDelete" ConfirmText='<%# "Please confirm if you want to Delete " + Eval("RuleCode") +" " + " Rule?" %>'
                                                                                                                    OnClientCancel="CancelClick">
                                                                                                                </ajax:ConfirmButtonExtender>

                                                                                                            </td>
                                                                                                        </tr>
                                                                                                    </ItemTemplate>
                                                                                                </asp:ListView>
                                                                                                <div class="row">
                                                                                                    <div class="col-xs-6">
                                                                                                        <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvInfratoBSlst">
                                                                                                            <Fields>
                                                                                                                <asp:TemplatePagerField>
                                                                                                                    <PagerTemplate>
                                                                                                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                                                                        Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                                                                        <br />
                                                                                                                    </PagerTemplate>
                                                                                                                </asp:TemplatePagerField>
                                                                                                            </Fields>
                                                                                                        </asp:DataPager>
                                                                                                    </div>
                                                                                                    <div class="col-xs-6 text-right">
                                                                                                        <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvInfratoBSlst" PageSize="100">
                                                                                                            <Fields>
                                                                                                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                                                                                    ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                                                                                <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                                                                                    NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                                                                                    NumericButtonCssClass="btn-pagination" />
                                                                                                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                                                                                    ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                                                                            </Fields>
                                                                                                        </asp:DataPager>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </ContentTemplate>
                                                                                        </asp:UpdatePanel>
                                                                                    </div>
                                                                                </div>

                                                                                <%--   </div>--%>
                                                                            </telerik:RadPane>
                                                                        </telerik:RadSplitter>
                                                                    </telerik:RadPageView>
                                                                </telerik:RadMultiPage>
                                                            </telerik:RadPageView>

                                                            <telerik:RadPageView runat="server" ID="RadPageView3">


                                                                <telerik:RadSplitter ID="RadSplitter1" runat="server" Height="450" Width="1296" Orientation="Horizontal">
                                                                    <telerik:RadPane ID="RadPane2" runat="server" Height="400" MinHeight="350" MaxHeight="750" Width="1296">


                                                                        <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional" class="col-md-12 form-horizontal uniformjs">
                                                                            <ContentTemplate>
                                                                                <telerik:RadComboBox ID="RadComboBox2" runat="server" Width="350" CssClass="margin-top" DropDownCssClass="widget border"
                                                                                    DropDownWidth="700" EmptyMessage="Search" HighlightTemplatedItems="true"
                                                                                    EnableLoadOnDemand="true" AllowCustomText="true" MarkFirstMatch="true" Filter="Contains"
                                                                                    Label="Select Business Service to view Rules" LabelCssClass="col-md-5 control-label" Skin="" EnableEmbeddedSkins="false" OnSelectedIndexChanged="RadComboBox2_SelectedIndexChanged" AutoPostBack="true">
                                                                                    <HeaderTemplate>
                                                                                        <div class="widget-head">
                                                                                            <div class="form-group text-bold">
                                                                                                <div class="col-md-3">
                                                                                                    Business Service 
                                                                                                </div>
                                                                                                <div class="col-md-4">
                                                                                                    Infraobject
                                                                                                </div>
                                                                                                <div class="col-md-5">
                                                                                                    Infraobject Compenent
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </HeaderTemplate>
                                                                                    <ItemTemplate>
                                                                                        <div class="row">
                                                                                            <div class="form-group">
                                                                                                <div class="col-md-3">
                                                                                                    <%--<%# DataBinder.Eval(Container, "Attributes['Infracomponent']")%>--%>
                                                                                                    <%# Eval("businesService")%>
                                                                                                </div>
                                                                                                <div class="col-md-4">

                                                                                                    <%-- <%# DataBinder.Eval(Container, "Attributes['Infraobject']")%>--%>
                                                                                                    <%# Eval("Infraobject")%>
                                                                                                </div>
                                                                                                <div class="col-md-5">
                                                                                                    <%-- <%# DataBinder.Eval(Container, "Text")%>--%>

                                                                                                    <%# Eval("Infracomponent")%>
                                                                                                </div>
                                                                                                <div class="col-md-3" style="display: none">
                                                                                                    <%-- <%# DataBinder.Eval(Container, "Text")%>--%>
                                                                                                    <%# Eval("bsid")%>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </ItemTemplate>

                                                                                </telerik:RadComboBox>

                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>
                                                                        <hr class="margin-bottom-none" />

                                                                        <asp:UpdatePanel ID="udpgraphBftoBf" runat="server" UpdateMode="Conditional">
                                                                            <ContentTemplate>
                                                                                <div id="divemptyFunctionPopupBftoBf"></div>
                                                                                <div id="divbsViewBFtoBF" style="display: none;">
                                                                                    <div id="bsbodyBftoBf" runat="server">
                                                                                    </div>

                                                                                    <div class="col-md-4">
                                                                                        <a id="btnReloadBftoBF" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                                                                        <a id="btnResetbftobf" class="reset-icon margin-top" title="Back to initial zoom position">&nbsp;</a>
                                                                                    </div>
                                                                                    <div class="col-md-8 text-right">
                                                                                        <img src="../Images/icons/yellow-dot.png" />
                                                                                        <label class="margin-right">Partial Impact</label>
                                                                                        <img src="../Images/icons/orange-dot.png" />
                                                                                        <label class="margin-right">Major Impact</label>
                                                                                        <img src="../Images/icons/red-dot.png" />
                                                                                        <label class="margin-right">Total Impact</label>
                                                                                    </div>
                                                                                </div>

                                                                            </ContentTemplate>
                                                                        </asp:UpdatePanel>

                                                                    </telerik:RadPane>
                                                                    <telerik:RadSplitBar ID="Radsplitbar1" runat="server">
                                                                    </telerik:RadSplitBar>
                                                                    <telerik:RadPane ID="RadPane1" runat="server" MaxHeight="250" Height="150">

                                                                        <div class="col-md-12">

                                                                            <div class="widget-head">
                                                                                <h4 class=" heading padding-none-LR">Impact Relationship View</h4>
                                                                                <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional" class="text-right">
                                                                                    <ContentTemplate>
                                                                                    </ContentTemplate>
                                                                                </asp:UpdatePanel>
                                                                            </div>
                                                                            <div class="widget-body padding-none">
                                                                            </div>

                                                                        </div>
                                                                        <%--</div>--%>
                                                                    </telerik:RadPane>
                                                                </telerik:RadSplitter>
                                                            </telerik:RadPageView>


                                                        </telerik:RadMultiPage>

                                                        <%--</div>--%>
                                                    </telerik:RadPageView>

                                                </telerik:RadMultiPage>

                                                <asp:UpdateProgress ID="UpdateProgress1" AssociatedUpdatePanelID="udpImapctRule" runat="server">
                                                    <ProgressTemplate>
                                                        <div id="imgLoading" class="loading-mask">
                                                            <span>Loading...</span>
                                                        </div>
                                                    </ProgressTemplate>
                                                </asp:UpdateProgress>
                                            </ContentTemplate>
                                        </asp:UpdatePanel>
                                    </div>
                                </div>
                            </div>
                            <%-- </ContentTemplate>
                            </asp:UpdatePanel>--%>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <%--Select service to attach Popup--%>
    <div id="servicp" style="display: none">
        <div id="popup1">
            <div class="bg" style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;" id="bg">
            </div>
            <div class="modal" id="dvAddService">
                <div class="modal-dialog" style="width: 460px;">
                    <div class="modal-content  widget-body-white" style="box-shadow: 0 0px 0 0 #dbdbdb">
                        <div class="modal-header">
                            <h3 class="modal-title">Add Business Service </h3>
                            <a id="popClose" title="Close Window" class="close" style="color: #fff;">x</a>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group margin-bottom-none">
                                        <label class="col-md-5 control-label" style="padding-top: 3px;" id="lblService">Select Business Service</label>
                                        <div class="col-md-7">
                                            <select id="ddlAppList" style="width: 160px"></select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer" style="padding: 5px;">
                            <div class="text-right">
                                <input type="button" id="btnLoadService" class="btn btn-primary" value="Load" onclick="attachService();" style="width: 20%; margin-right: 18px;" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--end popup--%>
    <div id="popup_service" style="display: none;">
        <asp:Panel ID="modelbg" runat="server" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
        </asp:Panel>
        <asp:Panel ID="pnlSaveService" runat="server" Width="100%" Style="display: block;">
            <div class="modal" style="display: block;">
                <div class="modal-dialog" style="width: 590px;">
                    <div class="modal-content  widget-body-white">
                        <div class="modal-header">
                            <h3 class="modal-title">Save Service Group</h3>
                            <a id="btnclose" title="Close Window" class="close" style="color: #fff;">x</a>
                        </div>
                        <div class="modal-body">
                            <asp:Label ID="lblsmtpsavemessage" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>

                            <div class="row">
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="form-group">
                                        <label class="col-md-4 control-label ">
                                            <span>Service Group Name</span>
                                            <span class="inactive">*</span>
                                        </label>
                                        <div class="col-md-8 padding-none">
                                            <asp:TextBox ID="txtServiceName" runat="server" CssClass="form-control wd95"></asp:TextBox>
                                            <label id="errServiceName" style="display: none; color: red;">*</label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label "></label>
                                        <div class="col-md-8 chkbx padding-none">
                                            <input type="checkbox" id="chkNewProfile" onclick="hideShowControl(this);"><label>New Profile</label>
                                        </div>
                                    </div>
                                    <div class="form-group" id="dvAttchedProfile">
                                        <label class="col-md-4 control-label ">
                                            Attach Profile
                                        </label>
                                        <div class="col-md-8 padding-none">
                                            <select id="ddlAttachProfile" class="col-md-12" data-style="btn-default" multiple="multiple">
                                            </select>
                                            <label id="errAttachProfile" style="display: none; color: red;">*</label>
                                        </div>

                                    </div>
                                    <div class="form-group" id="dvProfileName">
                                        <label class="col-md-4 control-label ">
                                            <span>Profile Name</span>
                                            <span class="inactive">*</span>
                                        </label>
                                        <div class="col-md-8 padding-none">
                                            <asp:TextBox ID="txtProfileName" runat="server" CssClass="form-control wd95"></asp:TextBox>
                                            <label id="errProfileName" style="display: none; color: red;">*</label>
                                        </div>
                                    </div>


                                    <div class="form-group" id="dvAttachRole">
                                        <label class="col-md-4 control-label ">
                                            Attach Role
                                        </label>
                                        <div class="col-md-8 padding-none">
                                            <select id="ddlAttachRole" class="selectpicker col-md-12" data-style="btn-default"></select>
                                            <label id="errAttachRole" style="display: none; color: red;">*</label>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="modal-footer pdbt0">
                                <div class="row">
                                    <div class="col-xs-6 text-left">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                Fields</span>
                                        <label id="errDuplicateName" style="display: none; color: red;">Service Profile already exist.</label>
                                    </div>
                                    <div class="col-xs-6">
                                        <input type="button" id="btnSaveService" onclick="saveService();" class="btn btn-primary" value="Save" />
                                        <input type="button" id="btnCancel" value="Cancel" class="btn btn-default mr23" />

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>
    </div>
    <script src="../Script/d3.min.js"></script>
    <script src="../Script/ServiceTreeDesignProcessMoniter.js"></script>
    <script src="../Script/EntityRelationshipForAllBS.js"></script>
    <script src="../Script/EntityRelationshipList.js"></script>
    <script src="../Script/ImpactDiagramBStoBS.js"></script>
    




    <script type="text/javascript">
        //=====================Delete GroupService Context menu=========================

        $(function () {


            $('ul').on('contextmenu', 'li', function (e) {
                e.preventDefault();
                $("#" + this.id).addClass("CurrentActive");

            });
        });

        $(document).ready(function () {
            context.init({ preventDoubleContext: true });
            context.attach(('.treeView li .Treeli'), [
                        {

                            // $('.treeView li').children("span.Treeli")
                            text: "Delete Service Group", action: function (e) {
                                var ServiceId = $("li.CurrentActive").attr("id");
                                $("li.CurrentActive").removeClass("CurrentActive");
                                DeleteGroupServiceByProfile(ServiceId);
                            }
                        }

            ]);

            $(('.treeView li .Treeli')).on("contextmenu", true);


        });

        //$(function () {


        //    $('ul').on('contextmenu', 'li', function (e) {
        //        e.preventDefault();
        //        $("#" + this.id).addClass("PActive");

        //    });
        //});

        //    $(document).ready(function () {
        //    context.init({ preventDoubleContext: true });
        //    context.attach(('.treeView li .Profileli'), [
        //                {

        //                    text: "Delete Service Profile", action: function (e)
        //                    {
        //                      alert(ServiceProfileId);

        //                    }
        //                }

        //    ]);

        //    $(('.treeView li .Profileli')).on("contextmenu", true);


        //});




        //================== New development=====================
        $(document).ready(function () {
            CallAjaxForTree();
            CallAjaxForTreeForUser();
            context.init({ preventDoubleContext: true });
            context.attach(("#dvMainDiagram"), [
                        {
                            text: "New Service Group", action: function (e) {
                                newServiceProfile();
                            }

                        }
                         ,
                        {
                            text: "Clear", href: "javascript:void(0);", action: function (e) {
                                clearAll();
                            }
                        }
            ]);

            $('[id$=bsbody3]').on("contextmenu", true);
            $('#ctl00_cphBody_pnlSaveService .bootstrap-select .filter-option').text('- Select Profile -');
            $('.treeView li').live('click', function (event) {
                event.stopPropagation();
                var attr = $(this).attr('id');
                if (typeof attr !== typeof undefined && attr !== false) {
                    $('.clicked').removeClass('clicked');
                    $(this).addClass('clicked');
                }
                else {
                    return false;
                }
            });
        });


        function renderRuleCombobox(GServiceId) {
            var combo = $find("<%= RadComboBox3.ClientID %>");
            var itm = combo.findItemByValue(GServiceId);
            //itm.select();
            combo.disable();

            renderAjaxDataBSToBSForDesignService1(GServiceId);
        }


        $("#popClose").click(function () {
            $("#servicp").css("display", "none");
            $("#dvAddService").css("display", "none");
            $("#bg").css("display", "none");
        });

        //===== Open Popup for save service with profile attach ==============


        //$('#btnSaveProfile').on('click', function () {
        //                      
        //    saveServicePopup();

        //});

        //$("#btnSaveProfile").click(function () {

        //    saveServicePopup();
        //});

        $("#btnCancel").click(function () {
            $("#popup_service").css("display", "none");
        });
        $("#btnclose").click(function () {
            $("#popup_service").css("display", "none");
        });

        //============Save Service  with profile attach===================
        //$("#btnSaveService").click(function () {
        //                      
        //    saveService();
        //});
        //function pageload() {
        //    $('#ddlAttachProfile').multiselect();
        //}

        //=============rad windows telerik pop up=========================

        function openRadWindow(Url) {
            window.radopen(Url, "TelRadWindow");
        }

        function openInfratoBSRadWindow(Url) {
            window.radopen(Url, "TelRadWindow2");
        }

        function CancelClick() {
            return false;
        }





    </script>


</asp:Content>


