﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ADMImpact.aspx.cs" Inherits="CP.UI.Admin.ADMImpact" %>


<!DOCTYPE html>
<html>
<head id="Head1" runat="server">
    <title>Business Functions Impact Analysis </title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <script src="../Script/jquery-3.5.1.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <style type="text/css">
        #addtable th, #addtable td {
            font-size: 11px !important;
            padding: 2px;
        }
    </style>
</head>

<body>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="sp" runat="server"></asp:ScriptManager>
        <div class="innerLR">
            <asp:UpdatePanel ID="udpmain" runat="server" UpdateMode="Conditional">
                <ContentTemplate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="widget ">
                                <div class="widget-head">
                                    <h4 class=" heading">Business Service View</h4>
                                    <div class="pull-right">
                                        <a id="btnReload" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                        <a id="btnReset" class="reset-icon margin-top" title="Back to initial zoom position">&nbsp;</a>
                                    </div>
                                </div>
                                <div class="widget-body">

                                    <asp:ListView ID="rptImpactDetails" runat="server" Visible="false">
                                        <LayoutTemplate>
                                            <table class="table table-bordered table-striped table-white margin-bottom-none">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 41.6667%">Business Function Name </th>
                                                        <th style="width: 33.3333%">Impact</th>
                                                        <th>RTO</th>
                                                    </tr>
                                                </thead>
                                            </table>
                                            <div style="overflow: auto; height: 125px;">
                                                <table class="table table-bordered table-striped table-white">
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tbody>
                                                </table>
                                            </div>

                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td style="width: 41.6667%">
                                                    <asp:Label ID="Label1" runat="server"
                                                        Text='<%#Eval("ApplicationName").ToString()%>'></asp:Label></td>
                                                <td style="width: 33.3333%">
                                                    <asp:Label ID="Label2" runat="server" CssClass='<%#SetImpactColor(Eval("ImpactSummary"))%>'
                                                        Text='<%#Eval("ImpactSummary").ToString() %>'></asp:Label></td>
                                                <td>
                                                    <span class="icon-Time"></span>
                                                    <asp:Label ID="Label3" runat="server"
                                                        Text='<%#Eval("RTO").ToString() %>'></asp:Label></td>

                                            </tr>
                                        </ItemTemplate>
                                    </asp:ListView>
                                    <asp:UpdatePanel ID="udpgraph" runat="server" UpdateMode="Conditional" ViewStateMode="Disabled" ChildrenAsTriggers="false">
                                        <ContentTemplate>
                                            <div id="body">
                                            </div>
                                            <div class="row padding">
                                                <div class="col-md-4 d3-legend">
                                                    <span class="label label-danger">&nbsp;</span>
                                                    <span class="text-legend">Business Service having more than 30% InfraObjects affected</span>
                                                </div>
                                                <div class="col-md-4 d3-legend">
                                                    <span class="label label-warning">&nbsp;</span>
                                                    <span class="text-legend">Business Service having less than 30% InfraObjects affected</span>
                                                </div>
                                                <div class="col-md-4 d3-legend">
                                                    <span class="label label-path-link">&nbsp;</span>
                                                    <span class="text-legend">Business service having none of InfraObjects affected Or Business Service having InfraObjects with low priorities are affected</span>
                                                </div>
                                            </div>

                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </div>
                            </div>

                        </div>
                        <asp:UpdatePanel ID="UpdImpact" runat="server" UpdateMode="Conditional" ViewStateMode="Disabled" ChildrenAsTriggers="false">
                            <ContentTemplate>
                                <div class="col-md-3 padding-none-LR">
                                    <div class="widget ">
                                        <div class="widget-head">
                                            <h4 class=" heading">Financial Impact Summary </h4>
                                        </div>
                                        <div class="widget-body">
                                            <div class="text-center text-primary text-small">
                                                Downtime :  
                                                <asp:Label ID="lblDowntime" runat="server" CssClass="inactive strong text-larger text-bold" Text="2"></asp:Label>
                                                hrs
                                            </div>
                                            <div class="widget-body">
                                                <div class="text-center">
                                                    <span class="fa fa-dollar text-primary text-large"></span>
                                                    <asp:Label ID="lblTotalfinancialCost" runat="server" CssClass="text-large inactive"></asp:Label>
                                                    <a id="AncViewDetails" runat="server" visible="false" class="text-primary text-small" href="javascript:void(0)">( View Details...)</a>
                                                </div>

                                                <asp:Panel ID="PnlFinacialPieChart" runat="server" Width="320px" Height="300px">
                                                    <telerik:RadHtmlChart runat="server" ID="RadChartFinancialImpDistribution" Width="320px" Height="300px"
                                                        Transitions="false" PlotArea-Appearance-TextStyle-Margin="-35" PlotArea-YAxis-Visible="false" PlotArea-XAxis-Step="1" PlotArea-YAxis-Step="20">
                                                        <Appearance>
                                                            <FillStyle BackgroundColor="Transparent"></FillStyle>
                                                        </Appearance>
                                                        <ChartTitle>
                                                            <Appearance Align="Center" BackgroundColor="White" Position="Top">
                                                            </Appearance>
                                                        </ChartTitle>
                                                        <Legend>
                                                            <Appearance BackgroundColor="Transparent" Position="Bottom" Visible="true">
                                                                <TextStyle FontSize="11" Padding="45" FontFamily="segoe_uiregular,sans-serif" />
                                                            </Appearance>
                                                        </Legend>
                                                        <PlotArea>
                                                            <CommonTooltipsAppearance Visible="false"></CommonTooltipsAppearance>
                                                            <Appearance>
                                                                <FillStyle BackgroundColor="Transparent"></FillStyle>
                                                            </Appearance>
                                                            <Series>
                                                                <telerik:PieSeries>
                                                                    <SeriesItems>
                                                                    </SeriesItems>
                                                                </telerik:PieSeries>
                                                            </Series>
                                                        </PlotArea>
                                                    </telerik:RadHtmlChart>
                                                </asp:Panel>
                                                <div id="addtable" class="text-primary" runat="server">
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                        <asp:UpdatePanel ID="udpTreeView" runat="server" UpdateMode="Conditional" ViewStateMode="Disabled" ChildrenAsTriggers="false">
                            <ContentTemplate>
                                <div class="col-md-3">
                                    <div class="widget ">
                                        <div class="widget-head">
                                            <h4 class=" heading">Business Impact View</h4>
                                        </div>
                                        <div class="widget-body impactscroll">
                                            <asp:TreeView ID="tvImpact" runat="server" ShowLines="true"></asp:TreeView>
                                            <asp:Label ID="lblNoImpact" runat="server" Visible="false" Text="No Impact" CssClass="label label-success"></asp:Label>
                                        </div>
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>
        <asp:HiddenField ID="hdnBusinessId" runat="server" Value="" />
    </form>

    <script src="../Script/jquery.modal.js" type="text/javascript"></script>

    <script src="../Script/bootstrap.min.js"></script>

    <script src="../Script/modernizr.js"></script>

    <script src="../Script/jquery.slimscroll.min.js"></script>

    <script src="../Script/jquery.cookie.js"></script>

    <script src="../Script/bootstrap-select.js"></script>

    <script src="../Script/bootstrap-select.init.js"></script>

    <script src="../Script/core.init.js"></script>

    <script src="../Script/d3.min.js"></script>

    <script src="../Script/AppDepMapJs.js"></script>

    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>


    <script type="text/javascript">
        $(document).ready(function () {
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();

            var businessid = $('[id$=hdnBusinessId]').val();
            if (businessid != null)
                renderAjaxData(businessid);


            $("#lblImpactBF").css("font-size", "17px");

            $("#udpmain .widget-body").css("height", "460px");

            $(".impactscroll").mCustomScrollbar({
                axis: "yx",
                setHeight: "460px",
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                },

            });

        });

        function pageLoad() {
            $(".impactscroll").mCustomScrollbar({
                axis: "yx",
                setHeight: "460px",
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                },

            });
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
        }

    </script>

</body>
</html>
