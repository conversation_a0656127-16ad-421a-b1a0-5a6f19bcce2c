﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="MSSQL2000DatasyncMonitiring.aspx.cs" Inherits="CP.UI.Admin.MSSQL2000DatasyncMonitiring" Title="Continuity Patrol :: MSSQL Datasyn Monitoring" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style type="text/css">
       
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">

        <h3>
            <img src="../Images/health.png" />
            MSSQL Datasync Monitoring</h3>

        <div class="widget" data-toggle="collapse-widget" data-collapse-closed="true">
            <div class="widget-head" id="exhealth">
                <asp:Label ID="Label15" runat="server" CssClass="heading" Style="padding-left: 5px !important;">SQL DataSync Health</asp:Label>
                <span class="pull-right" style="color: #555;padding-top: 6px;">Last Refreshed At:
                    <asp:Label ID="txtlastrestime" runat="server"></asp:Label>
                </span>
            </div>
            <div id="exhealth-content">
                <div class="widget-body">
                    <table class="table table-bordered table-white" width="100%">
                        <thead>
                            <tr>
                                <th style="width: 40%">MSSQL Health
                                </th>
                                <th runat="server" id="idProdserver">Production
                                </th>
                                <th runat="server" id="idDrodserver">DR
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>MSSQL Server Instance
                                </td>
                                <td>
                                    <span class="icon-storageDR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblPRServer" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span class="icon-storageDR float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblDRServer" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Server IP Address/HostName
                                </td>
                                <td>
                                    <span id="spnPRcomp" runat="server">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblPRIPAddress" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span id="spnDRcomp" runat="server">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblDRIPAddress" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Database
                                </td>
                                <td>
                                    <span class="icon-database float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblprdatabase" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span class="icon-database float-left">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lbldrdatabase" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Database State
                                </td>
                                <td>
                                    <span id="spnPRState" class="Replicating float-left" runat="server">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lblprDBState" CssClass="active" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span id="spnDRState" class="InActive float-left" runat="server">&nbsp;</span>&nbsp;&nbsp;
                                    <asp:Label ID="lbldrDBState" runat="server" CssClass="inactive" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Database Recovery Model
                                </td>
                                <td>
                                    <asp:Label ID="Label24" CssClass="icon-dbrecover" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lblprDBRecovery" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label26" CssClass="icon-dbrecover" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lbldrDBRecovery" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>Database Access Restrict Status
                                </td>
                                <td>
                                    <asp:Label ID="Label29" CssClass="icon-dbrestrict" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lblAccessrestrictPR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label30" CssClass="icon-dbrestrict" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lblAccessrestrictDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Updatability
                                </td>
                                <td>
                                    <asp:Label ID="Label25" CssClass="icon-dbupdate" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lblUpdateabilitypPR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label27" CssClass="icon-dbupdate" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lblUpdateabilitypDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>MSSQL Server Edition
                                </td>
                                <td>
                                    <asp:Label ID="Label28" CssClass="icon-edition" runat="server"></asp:Label>
                                    <asp:Label ID="lblVersionPR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label11" CssClass="icon-edition" runat="server"></asp:Label>
                                    <asp:Label ID="lblVersionDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>MSSQL Server Release
                                </td>
                                <td>
                                    <asp:Label ID="Label10" CssClass="icon-release" runat="server"></asp:Label>
                                    <asp:Label ID="lblReleasePR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label12" CssClass="icon-release" runat="server"></asp:Label>
                                    <asp:Label ID="lblReleaseDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>Database Size (in MB)
                                </td>
                                <td>
                                    <asp:Label ID="Label13" CssClass="icon-size" runat="server"></asp:Label>
                                    <asp:Label ID="lblSizePR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label14" CssClass="icon-size" runat="server"></asp:Label>
                                    <asp:Label ID="lblSizeDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="widget" data-toggle="collapse-widget" data-collapse-closed="true">
            <div class="widget-head" id="monitor">


                <asp:Label ID="lblAppName" runat="server" CssClass="heading" Style="padding-left: 5px !important;">SQL DataSync status</asp:Label>

            </div>
            <div class="widget-body">
                <div id="monitor-content">
                    <table class="table table-bordered table-white" width="100%">
                        <thead>
                            <tr>
                                <th colspan="2" height="30px">
                                    <%--<span class="pull-right"> Last Monitoring Cycle : <asp:Label ID="txtlastrestime" runat="server"></asp:Label> </span>--%>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="width: 40%">Last backup file Name
                                </td>
                                <td>
                                    <asp:Label ID="Label1" CssClass="icon-backup" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lbllastbackup" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>LSN of last backup file
                                </td>
                                <td>
                                    <asp:Label ID="Label2" CssClass="icon-currentlsn" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lbllastbackuplsn" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last Backup Date & Time
                                </td>
                                <td>
                                    <asp:Label ID="Label5" CssClass="icon-Time" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lablbackupdate" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last copied file Name
                                </td>
                                <td>
                                    <asp:Label ID="Label8" CssClass="icon-copy" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lbllastcopy" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>LSN of last copied file
                                </td>
                                <td>
                                    <asp:Label ID="Label3" CssClass="icon-currentlsn" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lbllastcopylsn" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last Copied Date & Time
                                </td>
                                <td>
                                    <asp:Label ID="Label6" CssClass="icon-Time" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lbllastcopydate" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Last restored file Name
                                </td>
                                <td>
                                    <asp:Label ID="Label9" CssClass="icon-backup" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lbllastrestore" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>LSN of last restored file
                                </td>
                                <td>
                                    <asp:Label ID="Label4" CssClass="icon-currentlsn" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lbllastrestorelsn" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>

                            <tr>
                                <td>Last Restored Date & Time
                                </td>
                                <td>
                                    <asp:Label ID="Label7" CssClass="icon-Time" runat="server" Text=""></asp:Label>&nbsp;
                                    <asp:Label ID="lbllastrestoredate" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="widget" runat="server" id="divServices" visible="false">

            <div class="widget-head">

                <asp:Label ID="Label16" CssClass="heading" runat="server" Text="Services" Style="padding-left: 5px !important;"></asp:Label>
            </div>

            <div class="widget-body ">
                <div id="Div1" class="tabs-content ">
                    <asp:Panel ID="Panel_listview" runat="server">
                        <asp:ListView ID="lvMonitoringService" runat="server" OnItemDataBound="MonitorService_OnItemDataBound" Visible="true">
                            <LayoutTemplate>
                                <table class="table table-bordered table-condensed margin-bottom-none" width="100%">
                                    <thead>
                                        <tr>
                                            <th class="col-md-4">Service Name
                                            </th>
                                            <th class="col-md-4">Server IP
                                            </th>
                                            <th>Status
                                            </th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />
                                    </tbody>
                                </table>

                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td class="col-md-4">

                                        <asp:Label ID="ServiceId" runat="server" Text='<%# Eval("ServiceId") %>' Visible="false" />
                                        <asp:Label ID="lblServiceName" runat="server" Text='' />
                                    </td>
                                    <td class="col-md-4">


                                        <asp:Label ID="lblIpAddress" runat="server" Text='' />
                                    </td>
                                    <td>
                                        <asp:Label ID="Label15" runat="server" CssClass='<%#Eval("Status").ToString()=="1" ? "Replicating" : "InActive" %>'></asp:Label>

                                        <asp:Label ID="lblStatus" runat="server" CssClass='<%#Eval("Status").ToString()=="1" ? "text-success" : "text-danger" %>' Text='<%#Eval("Status").ToString()=="1" ? "Running" : "Stopped" %>' />
                                    </td>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                        <asp:Label ID="lblEmpty" Text="" runat="server"></asp:Label>
                    </asp:Panel>
                </div>
            </div>
        </div>

        <%--  <div id="divService" class="widget" data-toggle="collapse-widget" data-collapse-closed="true" runat="server">

            <div class="widget-head" id="srstatus">

                <asp:Label ID="Label17" runat="server" CssClass="heading" Style="padding-left: 5px !important;">MSSQL DataSync Services Status</asp:Label>
            </div>

            <div class="widget-body">
                <table class="table table-bordered table-white" width="100%" id="tblservice" runat="server">
                    <thead>
                        <tr>
                            <th style="width: 40%; text-align: left">Server
                            </th>
                            <th colspan="2" style="text-align: left">Production
                           &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:Label ID="lblprimIPAddress" runat="server" Text="***************"></asp:Label>
                            </th>

                            <th colspan="2" style="text-align: left">Secondary
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:Label ID="lblsecIPAddress" runat="server" Text="***************"></asp:Label>
                            </th>
                        </tr>
                        <tr>
                            <td style="width: 40%;"><strong>Service Name</strong>
                            </td>
                            <td><strong>Status</strong>
                            </td>

                            <td><strong>Start Mode</strong>
                            </td>
                            <td><strong>Status</strong>
                            </td>

                            <td><strong>Start Mode</strong>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>MSSQL SERVER
                            </td>
                            <td>
                                <asp:Label ID="lblsqlserverPR" CssClass="Replicating float-left"
                                    runat="server"></asp:Label>&nbsp;
                                <asp:Label ID="lblPRsqlserstatus" runat="server" Text="--"></asp:Label>
                            </td>

                            <td>

                                <asp:Label ID="lblPRStartMode" runat="server" Text="--"></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblsqlserverDR" CssClass="Replicating float-left"
                                    runat="server"></asp:Label>&nbsp;
                                <asp:Label ID="lblDRsqlserstatus" runat="server" Text="--"></asp:Label>
                            </td>

                            <td>

                                <asp:Label ID="lblDRStartMode" runat="server" Text="--"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>MSSQL Server ADHelper
                            </td>
                            <td>
                                <asp:Label ID="lbladhelperPr" CssClass="Replicating float-left" runat="server"></asp:Label>&nbsp;
                                <asp:Label ID="lblPRADHelperStatus" runat="server" Text="--"></asp:Label>
                            </td>

                            <td>

                                <asp:Label ID="lblPRADHelperMode" runat="server" Text="--"></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lbladhelperDr" CssClass="Replicating float-left" runat="server"></asp:Label>&nbsp;
                                <asp:Label ID="lblDRADHelperStatus" runat="server" Text="--"></asp:Label>
                            </td>

                            <td>

                                <asp:Label ID="lblDRADHelperMode" runat="server" Text="--"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>SQL SERVER AGENT
                            </td>
                            <td>
                                <asp:Label ID="lblsqlagentPR" CssClass="InActive float-left" runat="server"></asp:Label>&nbsp;
                                <asp:Label ID="lblPRSERVERAGENTStatus" runat="server" Text="--"></asp:Label>
                            </td>

                            <td>

                                <asp:Label ID="lblPRSERVERAGENTMode" runat="server" Text="--"></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblsqlagentDR" CssClass="InActive float-left" runat="server"></asp:Label>&nbsp;
                                <asp:Label ID="lblDRSERVERAGENTStatus" runat="server" Text="--"></asp:Label>
                            </td>

                            <td>

                                <asp:Label ID="lblDRSERVERAGENTMode" runat="server" Text="--"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>Windows Firewall/Internet Connection Sharing (ICS)
                            </td>
                            <td>
                                <asp:Label ID="lblfirewallPR" CssClass="Replicating float-left"
                                    runat="server"></asp:Label>&nbsp;
                                <asp:Label ID="lblPRFirewallstatus" runat="server" Text="--"></asp:Label>
                            </td>

                            <td>

                                <asp:Label ID="lblPRFirewallMode" runat="server" Text="--"></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblfirewallDR" CssClass="Replicating float-left"
                                    runat="server"></asp:Label>&nbsp;
                                <asp:Label ID="lblDRFirewallstatus" runat="server" Text="--"></asp:Label>
                            </td>

                            <td>

                                <asp:Label ID="lblDRFirewallMode" runat="server" Text="--"></asp:Label>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>--%>
    </div>
</asp:Content>
