﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "NTNXLeapRPReplMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class NTNXLeapRPReplMonitor : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string PRSiteName { get; set; }

        [DataMember]
        public string DRSiteName { get; set; }

        [DataMember]
        public string PRLeapRecoveryPlanName { get; set; }

        [DataMember]
        public string DRLeapRecoveryPlanName { get; set; }

        [DataMember]
        public string PRLocalAvailabilityZoneName { get; set; }

        [DataMember]
        public string DRLocalAvailabilityZoneName { get; set; }

        [DataMember]
        public string PRAvailabilityZoneSequence { get; set; }

        [DataMember]
        public string DRAvailabilityZoneSequence { get; set; }

        [DataMember]
        public string PRNumberOfRecoverableEntities { get; set; }

        [DataMember]
        public string DRNumberOfRecoverableEntities { get; set; }

        [DataMember]
        public string PRProtectionPolicyName { get; set; }

        [DataMember]
        public string DRProtectionPolicyName { get; set; }

        [DataMember]
        public string PRConfiguredRPO { get; set; }

        [DataMember]
        public string DRConfiguredRPO { get; set; }

        [DataMember]
        public DateTime CreateDate { get; set; }

        [DataMember]
        public DateTime UpdateDate { get; set; }


        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }
        #endregion Properties
    }
}
