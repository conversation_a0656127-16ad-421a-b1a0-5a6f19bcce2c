using System.Diagnostics;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.DataAccess.AppDependencyMapSettings;
using CP.DataAccess.AppDepMapLinks;
using CP.DataAccess.AppDepMapProfileDetails;
using CP.DataAccess.Base;
using CP.DataAccess.BusinessServiceAvailability;
using CP.DataAccess.BusinessServiceRPO;
using CP.DataAccess.BusinessServiceRTO;
using CP.DataAccess.SQLNative2008;
using CP.DataAccess.EC2S3DataSyncRep;
using CP.DataAccess.BIATimeInterval;
using CP.DataAccess.BIAProfile;
using CP.DataAccess.BusinessProfileTimeInterval;
using CP.DataAccess.BusinessProfileImpactType;
using CP.DataAccess.OracleEmcSrdfVMAXMonitor;
using CP.DataAccess.OracleEmcSrdfDMXMonitor;
using CP.DataAccess.DatabaseSYBase;
using CP.DataAccess.SyBasesMonitor;
using CP.DataAccess.AccessManagerCustomRole;
using CP.DataAccess.CPSL;
using CP.DataAccess.CPSLScheduled;
using CP.DataAccess.MSSQLDBMirrorsReplication;
using CP.DataAccess.MSSQLDBMirrorRepliMonitor;
using CP.DataAccess.VmWarepathDetail;
using CP.DataAccess.InfraobjectCGNameDetails;
using CP.DataAccess.IBMXIVReplication;
using CP.DataAccess.IBMXIVReplimonitor;
using CP.DataAccess.InfraobjectVolumeNameDetails;

using CP.DataAccess.HyperVReplication;
using CP.DataAccess.ClusterNodeMonitor;
using CP.DataAccess.ClusterSummaryMonitor;
using CP.DataAccess.DataBaseSyBaseWithSrs;
using CP.DataAccess.SybaseWithSRS;
using CP.DataAccess.SybaseWithSRSMonitoring;
using CP.DataAccess.TPRCRepli;
using CP.DataAccess.TPRCMonitoring;
using CP.DataAccess.MSSQLAlwaysOnRepli;
using CP.DataAccess.MSSQL2014ServerMonitor;
using CP.DataAccess.AppDepGroupNode;
using CP.DataAccess.ReplicatedGrpMonitor;
using CP.DataAccess.RLinkMonitorSecandry;
using CP.DataAccess.RLinkMonitorRepliPerformm;
using CP.DataAccess.EMCSRDFStarMonitor;
using CP.DataAccess.ActiveODGNonODG;
using CP.DataAccess.EmcISilonRepliMonitor;
using CP.DataAccess.EMCISilonReplicationPolicy;
using CP.DataAccess.EMCISilonReplication;
using CP.DataAccess.EmcMirrorViewReplication;
using CP.DataAccess.EmcMirrorViewRepliMonitoring;
using CP.DataAccess.Emc_MV_MirrorMonitoring;
using CP.DataAccess.CustomSubRoleTypess;
//using CP.DataAccess.DataBaseSyBaseWithRsHadr;
using CP.DataAccess.SybaseWithRSHADR;
using CP.DataAccess.SybaseWithRSHADR_DBMonitoring;
using CP.DataAccess.SybaseWithRSHADR_Repli_Monitoring;
using CP.DataAccess.SybaseWithRSHADR_Repli_MonitoringNew;
using CP.DataAccess.EmcUnityReplication;
using CP.DataAccess.EmcUnityRepliMonitoring;
using CP.DataAccess.DataBaseSyBaseWithRsHadr;
using CP.DataAccess.DatabaseHanaDb;
using CP.DataAccess.HANADBMonitoring;
using CP.DataAccess.HanaDBService;
using CP.DataAccess.HanaDBReplicationMode;
using CP.DataAccess.HanaDBSystemOperationMode;
using CP.DataAccess.GoldenGateReplication;
using CP.DataAccess.GoldenGateDBMonitors;
using CP.DataAccess.GoldenGateRepliMonitor;
using CP.DataAccess.GoldenGateGroupDetail;
using CP.DataAccess.RSyncJobs;
using CP.DataAccess.RSync;
using CP.DataAccess.RSyncMonitoring;
using CP.DataAccess.VeeamMonitor;
using CP.DataAccess.VeeamRepli;
using CP.DataAccess.VeeamVMJobs;
using CP.DataAccess.ServiceDiagramDetails;
using CP.DataAccess.NodeSubstituteAuthenticate;
using CP.DataAccess.DatabaseSqlplus;
using CP.DataAccess.DatabaseSubstituteAuthenticate;
using CP.DataAccess.CloudantReplicationMonitoring;
using CP.DataAccess.CloudantLBMonitoring;
using CP.DataAccess.HuaweiStorages;
using CP.DataAccess.NutLeapRPReplMonitor;
using CP.DataAccess.NutLeapRcblEntReplMonitor;
using CP.DataAccess.ActivDirectory;
using CP.DataAccess.ActivDirectoryMonitor;
using CP.DataAccess.DatabaseMSSQLMonitor;
using CP.DataAccess.DatabaseMaria;
using CP.DataAccess.MariaDBMonitor;
using CP.DataAccess.AzureMonitor;
using CP.DataAccess.AzureSiteRecoveryRep;
using CP.DataAccess.ZertoSiteRecovery;
using CP.DataAccess.ZertoMonitor;
using CP.DataAccess.EMCSRDFCGMonitor;
using CP.DataAccess.PostgreSqlClusterMonitor;
using CP.DataAccess.VmwareVsphereReplication;
using CP.DataAccess.VmwareVsphereMonitoring;
using CP.DataAccess.LoadMaster;
using CP.DataAccess.AzureGatewayView;
using System;
using CP.DataAccess.AzureGatewayListener;
using CP.DataAccess.AzureGatewayHealth;
using CP.DataAccess.OracleCloudComputeReplication;
using CP.DataAccess.HomeRegionDetails;
using CP.DataAccess.OracleCloudComponentMonitor;
using CP.DataAccess.OracleCloudComponentInstanceMonitor;
using CP.DataAccess.OracleCloudInstanceLevelMonitor;
using CP.DataAccess.RP4VM;
using CP.DataAccess.RPForVMMonitoring;
using CP.DataAccess.EMCMTree;
using CP.DataAccess.EMCMonitor;
using CP.DataAccess.AvamarBackup;
using CP.DataAccess.AvamarRestore;
using CP.DataAccess.AvamarReplicationMonitor;
using CP.DataAccess.AvamarActivity;
using CP.DataAccess.AvamarDataLag;
using CP.DataAccess.RackwareRepli;
using CP.DataAccess.RackwareReplicationMonitor;
using CP.DataAccess.RackwareSourceReplicationMonitor;
using CP.DataAccess.EMCDellCyberRecoverVault;
using CP.DataAccess.DellEMCCyberRecoveryVaultMonitor;
using CP.DataAccess.EMCProtectionPolicy;
using CP.DataAccess.DiffModule;
using CP.DataAccess.DiffModuleMonitoring;
using CP.DataAccess.PPDMREPORT;
using CP.DataAccess.RPVMMonitoring;
using CP.DataAccess.RPVMActivityMonitoring;
using CP.DataAccess.DatabaseOracleCloudDb;
using CP.DataAccess.OracleCloudComponentInstanceMonitor;
using CP.DataAccess.OracleCloudDataGuardWorkRequestStatusLatest;
using CP.DataAccess.RedHatVirtulizationMonitoring;
using CP.DataAccess.RedHatVirtualizationRepli;
using CP.DataAccess.OpenshiftReplication;
using CP.DataAccess.OpenShiftMonitor;
using CP.DataAccess.OCExaDBMonitor;
using CP.DataAccess.OCExaDBConfig;



namespace CP.DataAccess
{
    public sealed class DataAccessFactory : BaseDataAccessFactory
    {
        #region Constructer

        public DataAccessFactory(Context context)
            : base(context)
        {
        }

        #endregion Constructer

        #region ContinuityPatrol Factory Methods

        #region AvamarReplication

        [DebuggerStepThrough()]
        public override IAvamarReplicationDataAccess CreateAvamarReplicationDataAccess()
        {
            string type = typeof(AvamarReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AvamarReplicationDataAccess(CurrentContext);
            }

            return (IAvamarReplicationDataAccess)CurrentContext[type];
        }

        #endregion


        #region HP3PARStorage

        [DebuggerStepThrough]
        public override IHP3PARStorageDataAccess CreateHP3PARStorageDataAccess()
        {
            string type = typeof(CP.Common.DatabaseEntity.HP3PARStorage).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HP3PARStorageDataAccess(CurrentContext);
            }

            return (IHP3PARStorageDataAccess)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IVirtualMonitoringDataAccess CreateVirtualmonitoringDataAccess()
        {
            string type = typeof(VirtualMonitoringDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VirtualMonitoringDataAccess(CurrentContext);
            }

            return (IVirtualMonitoringDataAccess)CurrentContext[type];
        }
        #endregion HP3PARStorage


        #region AzureKubernete

        [DebuggerStepThrough()]
        public override IAzureKuberneteDataAccess CreateAzureKuberneteDataAccess()
        {
            string type = typeof(AzureKubernetesDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureKubernetesDataAccess(CurrentContext);
            }

            return (IAzureKuberneteDataAccess)CurrentContext[type];
        }

        #endregion

        #region ReplicationAzureCosmosDB

        [DebuggerStepThrough()]
        public override IReplicationAzureCosmosDBDataAccess CreateReplicationAzureCosmosDBDataAccess()
        {
            string type = typeof(ReplicationAzureCosmosDBDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ReplicationAzureCosmosDBDataAccess(CurrentContext);
            }

            return (IReplicationAzureCosmosDBDataAccess)CurrentContext[type];
        }

        #endregion

        #region Archive

        [DebuggerStepThrough]
        public override IArchiveDataAccess CreateArchiveDataAccess()
        {
            string type = typeof(ArchiveDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ArchiveDataAccess(CurrentContext);
            }

            return (IArchiveDataAccess)CurrentContext[type];
        }

        #endregion Archive

        #region ActionSet

        [DebuggerStepThrough]
        public override IActionSetDataAccess CreateActionSetDataAccess()
        {
            string type = typeof(ActionSetDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ActionSetDataAccess(CurrentContext);
            }

            return (IActionSetDataAccess)CurrentContext[type];
        }

        #endregion ActionSet

        #region  ActionHumanIntervention

        [DebuggerStepThrough]
        public override IActionHumanInterventionDataAccess CreateActionHumanInterventionDataAccess()
        {
            string type = typeof(ActionHumanInterventionDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ActionHumanInterventionDataAccess(CurrentContext);
            }

            return (IActionHumanInterventionDataAccess)CurrentContext[type];
        }

        #endregion

        #region Alert

        [DebuggerStepThrough]
        public override IAlertDataAccess CreateAlertDataAccess()
        {
            string type = typeof(AlertDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AlertDataAccess(CurrentContext);
            }

            return (IAlertDataAccess)CurrentContext[type];
        }

        #endregion Alert

        #region AlertManager

        [DebuggerStepThrough]
        public override IAlertManagerDataAccess CreateAlertManagerDataAccess()
        {
            string type = typeof(AlertReceiverDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AlertManagerDataAccess(CurrentContext);
            }

            return (IAlertManagerDataAccess)CurrentContext[type];
        }

        #endregion AlertManager

        #region AlertReceiver

        [DebuggerStepThrough]
        public override IAlertReceiverDataAccess CreateAlertReceiverDataAccess()
        {
            string type = typeof(AlertReceiverDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AlertReceiverDataAccess(CurrentContext);
            }

            return (IAlertReceiverDataAccess)CurrentContext[type];
        }

        #endregion AlertReceiver

        #region AlertNotification

        [DebuggerStepThrough]
        public override IAlertNotificationDataAccess CreateAlertNotificationDataAccess()
        {
            string type = typeof(AlertNotificationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AlertNotificationDataAccess(CurrentContext);
            }

            return (IAlertNotificationDataAccess)CurrentContext[type];
        }

        #endregion AlertNotification

        #region ApplicationGroup

        [DebuggerStepThrough]
        public override IApplicationGroupDataAccess CreateApplicationGroupDataAccess()
        {
            string type = typeof(ApplicationGroupDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ApplicationGroupDataAccess(CurrentContext);
            }

            return (IApplicationGroupDataAccess)CurrentContext[type];
        }

        #endregion ApplicationGroup

        #region ApplicationGroupInfo

        [DebuggerStepThrough]
        public override IApplicationGroupInfoDataAccess CreateApplicationGroupInfoDataAccess()
        {
            string type = typeof(ApplicationGroupInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ApplicationGroupInfoDataAccess(CurrentContext);
            }

            return (IApplicationGroupInfoDataAccess)CurrentContext[type];
        }

        #endregion ApplicationGroupInfo

        #region ApplicationDiscovery

        [DebuggerStepThrough]
        public override IApplicationDiscoveryDataAccess CreateApplicationDiscoveryDataAccess()
        {
            string type = typeof(ApplicationDiscoveryDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ApplicationDiscoveryDataAccess(CurrentContext);
            }

            return (IApplicationDiscoveryDataAccess)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IApplicationDiscoveryProfileDetailsDataAccess CreateApplicationDiscoveryProfileDetailsDataAccess()
        {
            string type = typeof(ApplicationDiscoveryProfileDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ApplicationDiscoveryProfileDetailsDataAccess(CurrentContext);
            }

            return (IApplicationDiscoveryProfileDetailsDataAccess)CurrentContext[type];
        }
        #endregion

        #region Application dependency group nodes
        [DebuggerStepThrough]
        public override IApplicationDepGroupNodes CreateAppDepGroupNodes()
        {
            string type = typeof(AppDependencyGroupNodesDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AppDependencyGroupNodesDataAccess(CurrentContext);
            }

            return (IApplicationDepGroupNodes)CurrentContext[type];
        }

        #endregion

        #region Analytics

        [DebuggerStepThrough]
        public override IAnalyticsDataAccess CreateAnalyticsDataAccess()
        {
            string type = typeof(AnalyticsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AnalyticsDataAccess(CurrentContext);
            }

            return (IAnalyticsDataAccess)CurrentContext[type];
        }

        #endregion

        #region ActionAnalytic

        [DebuggerStepThrough]
        public override IActionAnalyticDataAccess CreateActionAnalyticDataAccess()
        {
            string type = typeof(IActionAnalyticDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ActionAnalyticDataAccess(CurrentContext);
            }
            return (IActionAnalyticDataAccess)CurrentContext[type];
        }

        #endregion

        #region AzureKubernetDBMonito
        public override IAzureKubernetDBMonitorDataAccess CreateAzurekubernetesMonitorDataAccess()
        {
            string type = typeof(AzureKubernetDBMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureKubernetDBMonitorDataAccess(CurrentContext);
            }

            return (IAzureKubernetDBMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region AzureCosmosDBMonitor

        [DebuggerStepThrough()]
        public override IAzureCosmosDBMonitorDataAccess CreateAzureCosmosDBMonitorDataAccess()
        {
            string type = typeof(AzureCosmosDBMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureCosmosDBMonitorDataAccess(CurrentContext);
            }

            return (IAzureCosmosDBMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region ASMGird
        public override IASMGirdDataAccess CreateActionASMGirdDataAccess()
        {
            string type = typeof(ASMGridDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ASMGridDataAccess(CurrentContext);
            }

            return (IASMGirdDataAccess)CurrentContext[type];
        }
        #endregion

        #region BIAHuminterventionsAction

        [DebuggerStepThrough]
        public override IBIAHuminterventionsActionDataAccess CreateBIAHuminterventionsActionDataAccess()
        {
            string type = typeof(IBIAHuminterventionsActionDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAHuminterventionsActionDataAccess(CurrentContext);
            }

            return (IBIAHuminterventionsActionDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAFailureActinBySolType

        [DebuggerStepThrough]
        public override IBIAFailureActinBySolTypeDataAccess CreateIBIAFailureActinBySolTypeDataAccess()
        {
            string type = typeof(IBIAFailureActinBySolTypeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAFailureActinBySolTypeDataAccess(CurrentContext);
            }

            return (IBIAFailureActinBySolTypeDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAFailureActinBySOSB

        [DebuggerStepThrough]
        public override IBIAFailureActinBySOSBDataAccess CreateBIAFailureActinBySOSBDataAccess()
        {
            string type = typeof(IBIAFailureActinBySOSBDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAFailureActinBySOSBDataAccess(CurrentContext);
            }

            return (IBIAFailureActinBySOSBDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAActionCountCompletedOutOfRTOBySol

        [DebuggerStepThrough]
        public override IBIAActionCountCompletedOutOfRTOBySolDataAccess CreateBIAActionCountCompletedOutOfRTOBySolDataAccess()
        {
            string type = typeof(IBIAActionCountCompletedOutOfRTOBySolDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAActionCountCompletedOutOfRTOBySolDataAccess(CurrentContext);
            }

            return (IBIAActionCountCompletedOutOfRTOBySolDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAWorkflowAnalyatic

        [DebuggerStepThrough]
        public override IBIAWorkflowAnalyaticDataAccess CreateBIAWorkflowAnalyaticDataAccess()
        {
            string type = typeof(IBIAWorkflowAnalyaticDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAWorkflowAnalyaticDataAccess(CurrentContext);
            }

            return (IBIAWorkflowAnalyaticDataAccess)CurrentContext[type];
        }

        #endregion

        #region CreateIBIAFailedWorkflowDataAccess

        [DebuggerStepThrough]
        public override IBIAFailedWorkflowDataAccess CreateIBIAFailedWorkflowDataAccess()
        {
            string type = typeof(CP.DataAccess.BIAFailedWorkflowDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CP.DataAccess.BIAFailedWorkflowDataAccess(CurrentContext);
            }

            return (IBIAFailedWorkflowDataAccess)CurrentContext[type];
        }

        #endregion BIAFailedWorkflowByWorkflowType

        #region BIAFailedWorkflowByWorkflowType
        [DebuggerStepThrough]
        public override IBIAFailedWorkflowByWorkflowType CreateIBIAFailedWorkflowByWorkflowType()
        {
            string type = typeof(BIAFailedWorkflowByWorkflowTypeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAFailedWorkflowByWorkflowTypeDataAccess(CurrentContext);
            }

            return (IBIAFailedWorkflowByWorkflowType)CurrentContext[type];
        }
        #endregion CreateIBIAFailedWorkflowByWorkflowType

        #region BIAFailedAction

        [DebuggerStepThrough]
        public override IBIAFailedActionDataAccess CreateBIAFailedActionDataAccess()
        {
            string type = typeof(IBIAFailedActionDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAFailedActionDataAccess(CurrentContext);
            }
            return (IBIAFailedActionDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIASuccessWFvsHI

        [DebuggerStepThrough]
        public override IBIASuccessWFvsHIDataAccess CreateBIASuccessWFvsHIDataAccess()
        {
            string type = typeof(IBIASuccessWFvsHIDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIASuccessWFvsHIDataAccess(CurrentContext);
            }
            return (IBIASuccessWFvsHIDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIA Overall Workflow Statistics
        [DebuggerStepThrough]
        public override IBIAOvervallWorkflowStatisticsDatatAccess CreateBIAOvervallWorkflowStatisticsDataAccess()
        {
            string type = typeof(IBIAOvervallWorkflowStatisticsDatatAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAOvervallWorkflowStatisticsDatatAccess(CurrentContext);
            }
            return (IBIAOvervallWorkflowStatisticsDatatAccess)CurrentContext[type];
        }
        #endregion

        #region BIAActionEffiAutoMode

        [DebuggerStepThrough]
        public override IBIAActionEffiAutoModeDataAccess CreateIIBIAActionEffiAutoModeDataAccess()
        {
            string type = typeof(IBIAActionEffiAutoModeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAActionEffiAutoModeDataAccess(CurrentContext);
            }
            return (IBIAActionEffiAutoModeDataAccess)CurrentContext[type];
        }


        public override IBIAActionEffiAutoModeDataAccess CreateBIAActionEffiAutoModeDataAccess()
        {
            string type = typeof(BIAActionEffiAutoModeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAActionEffiAutoModeDataAccess(CurrentContext);
            }

            return (BIAActionEffiAutoModeDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAWorkflowfailureprofileTrend

        [DebuggerStepThrough]
        public override IBIAWorkflowfailureprofileTrendDataAccess CreateBIAWorkflowfailureprofileTrendDataAccess()
        {
            string type = typeof(BIAWorkflowfailureprofileTrendDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAWorkflowfailureprofileTrendDataAccess(CurrentContext);
            }

            return (IBIAWorkflowfailureprofileTrendDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAWorkflowFailureWorkflowType

        [DebuggerStepThrough]
        public override IBIAWorkflowFailureWorkflowTypeTrendDataAccess CreateBIAWorkflowFailureWorkflowTypeTrendDataAccess()
        {
            string type = typeof(IBIAWorkflowFailureWorkflowTypeTrendDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAWorkflowFailureWorkflowTypeDataAccess(CurrentContext);
            }
            return (IBIAWorkflowFailureWorkflowTypeTrendDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAWorkflowTrendsAll

        [DebuggerStepThrough]
        public override IBIAWorkflowTrendsAllDataAccess CreateBIAWorkflowTrendsAllDataAccess()
        {
            string type = typeof(BIAWorkflowTrendsAllDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAWorkflowTrendsAllDataAccess(CurrentContext);
            }

            return (IBIAWorkflowTrendsAllDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAWorkflowProfilesHumanInterventionsTrend

        [DebuggerStepThrough]
        public override IBIAWorkflowProfilesHumanInterventionsTrendDataAccess CreateBIAWorkflowProfilesHumanInterventionsTrendDataAccess()
        {
            string type = typeof(BIAWorkflowProfilesHumanInterventionsTrendDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAWorkflowProfilesHumanInterventionsTrendDataAccess(CurrentContext);
            }

            return (IBIAWorkflowProfilesHumanInterventionsTrendDataAccess)CurrentContext[type];
        }

        #endregion

        #region ProfileDetailsWorkflowEfficiency

        [DebuggerStepThrough]
        public override IBIAWorkflowefficiencyTrendDataAccess CreateBIAWorkflowefficiencyTrendDataAccess()
        {
            string type = typeof(IBIAWorkflowefficiencyTrendDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAWorkflowefficiencyTrendDataAccess(CurrentContext);
            }
            return (IBIAWorkflowefficiencyTrendDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAImpactCount

        [DebuggerStepThrough]
        public override IBIAImpactCountDataAccess CreateBIAImpactCountDataAccess()
        {
            string type = typeof(IBIAImpactCountDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAImpactCountDataAccess(CurrentContext);
            }
            return (IBIAImpactCountDataAccess)CurrentContext[type];
        }

        #endregion

        #region BiaActiontrendComponent

        [DebuggerStepThrough]
        public override IBIAActiontrendComponentDataAccess CreateBIAActiontrendComponentDataAccess()
        {
            string type = typeof(IBIAActiontrendComponentDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BiaActiontrendComponentDataAccess(CurrentContext);
            }
            return (IBIAActiontrendComponentDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAActionFailureHumanInterventionTrend

        [DebuggerStepThrough]
        public override IBIAActionFailureHumanInterventionTrendDataAccess CreateIBIAActionFailureHumanInterventionTrendDataAccess()
        {
            string type = typeof(IBIAActionFailureHumanInterventionTrendDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAActionFailureHumanInterventionTrendDataAccess(CurrentContext);
            }
            return (IBIAActionFailureHumanInterventionTrendDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAFailureACtionByWorkflowType

        [DebuggerStepThrough]
        public override IBIAFailureActionByWorkflowTypeDataAccess CreateBIAFailureActionByWorkflowTypeDataAccess()
        {
            string type = typeof(BIAFailureActionByWorkflowTypeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAFailureActionByWorkflowTypeDataAccess(CurrentContext);
            }

            return (IBIAFailureActionByWorkflowTypeDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAProfile Details action efficiency Trend

        [DebuggerStepThrough]
        public override IBIAProfileDetailsActionEfficiencyTrendDataAccess CreateBIAProfileDetailsActionEfficiencyTrendDataAccess()
        {
            string type = typeof(IBIAProfileDetailsActionEfficiencyTrendDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAProfileDetailsActionEfficiencyTrendDataAccess(CurrentContext);
            }
            return (IBIAProfileDetailsActionEfficiencyTrendDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAWorkflowCompleted with RTO

        [DebuggerStepThrough]
        public override IBIAWorkflowCompletedWithinRTODataAccess CreateBIAWorkflowCompletedWithinRTODataAccess()
        {
            string type = typeof(BIAWorkflowCompletedWithinRTODataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAWorkflowCompletedWithinRTODataAccess(CurrentContext);
            }

            return (IBIAWorkflowCompletedWithinRTODataAccess)CurrentContext[type];
        }

        #endregion

        #region Alert Page

        #region BIAAlertCountBusinessServiceWise

        public override IBIAAlertCountBusinessServiceWiseDataAccess CreateBIAAlertCountBusinessServiceWiseDataAccess()
        {
            string type = typeof(BIAAlertCountBusinessServiceWiseDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAAlertCountBusinessServiceWiseDataAccess(CurrentContext);
            }

            return (IBIAAlertCountBusinessServiceWiseDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAGetAlertDetails

        public override IBIAGetAlertDetailsDataAccess CreateBIAGetAlertDetailsDataAccess()
        {
            string type = typeof(BIAGetAlertDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAGetAlertDetailsDataAccess(CurrentContext);
            }

            return (IBIAGetAlertDetailsDataAccess)CurrentContext[type];
        }

        #endregion   BIAAlertsTrendBusinessService

        #region BIAAlertsTrendBusinessService

        public override IBIAAlertsTrendBusinessServiceDataAccess CreateBIAAlertsTrendBusinessServiceDataAccess()
        {
            string type = typeof(BIAAlertsTrendBusinessServiceDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAAlertsTrendBusinessServiceDataAccess(CurrentContext);
            }

            return (IBIAAlertsTrendBusinessServiceDataAccess)CurrentContext[type];
        }

        #endregion

        #region BIAOverallAlertStatistics

        public override IBIAOverallAlertStatisticsDataAccess CreateBIAOverallAlertStatisticsDataAccess()
        {
            string type = typeof(BIAOverallAlertStatisticsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAOverallAlertStatisticsDataAccess(CurrentContext);
            }

            return (IBIAOverallAlertStatisticsDataAccess)CurrentContext[type];
        }

        #endregion

        #endregion

        #region BIAActionEfficiency

        public override IBIAActionEfficiencyDataAccess CreateBIAActionEfficiencyDataAccess()
        {
            string type = typeof(BIAActionEfficiencyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAActionEfficiencyDataAccess(CurrentContext);
            }

            return (IBIAActionEfficiencyDataAccess)CurrentContext[type];
        }

        #endregion

        #region Schedule Discovery

        [DebuggerStepThrough]
        public override IScheduleDiscProfDetailsDataAccess CreateScheduleDiscoveryProfileDetailsDataAccess()
        {
            string type = typeof(ScheduleDiscoveryProfDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ScheduleDiscoveryProfDetailsDataAccess(CurrentContext);
            }

            return (IScheduleDiscProfDetailsDataAccess)CurrentContext[type];
        }

        #endregion

        #region BusinessFunction

        [DebuggerStepThrough]
        public override IBusinessFunctionDataAccess CreateBusinessFunctionDataAccess()
        {
            string type = typeof(BusinessFunctionDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessFunctionDataAccess(CurrentContext);
            }

            return (IBusinessFunctionDataAccess)CurrentContext[type];
        }

        #endregion BusinessFunction


        #region ActiveAodgodgLogRepliDetails

        public override IActiveODGReplicationNonOdgDataAccess CreateAodgNonODGLogRepliDetailsDataAccess()
        {
            string type = typeof(ActiveODGReplicationNonOdgDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ActiveODGReplicationNonOdgDataAccess(CurrentContext);
            }
            return (ActiveODGReplicationNonOdgDataAccess)CurrentContext[type];
        }

        #endregion ActiveAodgodgLogRepliDetails

        #region BusinessFunctionActivityBIA

        [DebuggerStepThrough()]
        public override IBusinessFunctionBIAActivityDataAccess CreateBusinessFunctionActivityBIADataAccess()
        {
            string type = typeof(BusinessFunctionBIAActivityDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessFunctionBIAActivityDataAccess(CurrentContext);
            }

            return (IBusinessFunctionBIAActivityDataAccess)CurrentContext[type];
        }

        #endregion

        #region BusinessFunctionBIADetails

        [DebuggerStepThrough()]
        public override IBusinessFunctionBIADetailsDataAccess CreateBusinessFunctionBIADetailsDataAccess()
        {
            string type = typeof(BusinessFunctionBIADetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessFunctionBIADetailsDataAccess(CurrentContext);
            }

            return (IBusinessFunctionBIADetailsDataAccess)CurrentContext[type];
        }

        #endregion

        #region BusinessFunctionBIA

        [DebuggerStepThrough()]
        public override IBusinessFunctionBIADataAccess CreateBusinessFunctionBIADataAccess()
        {
            string type = typeof(BusinessFunctionBIADataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessFunctionBIADataAccess(CurrentContext);
            }

            return (IBusinessFunctionBIADataAccess)CurrentContext[type];
        }

        #endregion

        #region BusinessFunctionBIASeverity

        [DebuggerStepThrough()]
        public override IBusinessFunctionBIASeverityDataAccess CreateBusinessFunctionBIASeverityDataAccess()
        {
            string type = typeof(BusinessFunctionBIASeverityDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessFunctionBIASeverityDataAccess(CurrentContext);
            }

            return (IBusinessFunctionBIASeverityDataAccess)CurrentContext[type];
        }

        #endregion

        #region BusinessFunctionBIARelation

        [DebuggerStepThrough()]
        public override IBusinessFunctionBIARelationDataAccess CreateBusinessFunctionBIARelationDataAccess()
        {
            string type = typeof(BusinessFunctionBIARelationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessFunctionBIARelationDataAccess(CurrentContext);
            }

            return (IBusinessFunctionBIARelationDataAccess)CurrentContext[type];
        }

        #endregion

        #region BusinessService

        [DebuggerStepThrough]
        public override IBusinessServiceDataAccess CreateBusinessServiceDataAccess()
        {
            string type = typeof(BusinessServiceDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessServiceDataAccess(CurrentContext);
            }

            return (IBusinessServiceDataAccess)CurrentContext[type];
        }

        #endregion BusinessService

        #region BusinessImpactDataAccess
        public override IBusinessImpactDataAccess CreateBusinessImpactDataAccess()
        {
            string type = typeof(BusinessImpactDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessImpactDataAccess(CurrentContext);
            }

            return (IBusinessImpactDataAccess)CurrentContext[type];
        }
        #endregion

        #region BusinessTimeIntervalDataAccess
        public override IBusinessTimeInterval CreateIBusinessTimeIntervalDataAccess()
        {
            string type = typeof(BusinessTimeIntervalDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessTimeIntervalDataAccess(CurrentContext);
            }

            return (IBusinessTimeInterval)CurrentContext[type];
        }
        #endregion

        #region BusinessTimeIntervalDataAccess
        public override IBusinessProfile CreateBusinessProfileDataAccess()
        {
            string type = typeof(BusinessProfileDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessProfileDataAccess(CurrentContext);
            }

            return (IBusinessProfile)CurrentContext[type];
        }
        #endregion

        #region BusinessTimeIntervalDataAccess
        public override IBIAProfileTimeInterval CreateBIAProfileTimeIntervalDataAccess()
        {
            string type = typeof(BIAProfileTimeIntervalsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAProfileTimeIntervalsDataAccess(CurrentContext);
            }

            return (IBIAProfileTimeInterval)CurrentContext[type];
        }
        #endregion

        #region BusinessProfileImpactTypeDataAccess
        public override IBIAProfileImpactTypes CreateBIAProfileImpactTypesDataAccess()
        {
            string type = typeof(BIAProfileImpactTypes).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BIAProfileImpactTypesDataAccess(CurrentContext);
            }

            return (IBIAProfileImpactTypes)CurrentContext[type];
        }
        #endregion

        #region BusinessServiceAvailability

        [DebuggerStepThrough]
        public override IBusinessServiceAvailabilityInfoDataAccess CreateBusinessServiceAvailabilityInfoDataAccess()
        {
            string type = typeof(BusinessServiceAvailabilityDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessServiceAvailabilityDataAccess(CurrentContext);
            }

            return (IBusinessServiceAvailabilityInfoDataAccess)CurrentContext[type];
        }

        #endregion BusinessServiceAvailability

        #region BusinessServiceRPO

        [DebuggerStepThrough]
        public override IBusinessServiceRPOInfoDataAccess CreateBusinessServiceRPOInfoDataAccess()
        {
            string type = typeof(BusinessServiceRPOInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessServiceRPOInfoDataAccess(CurrentContext);
            }

            return (IBusinessServiceRPOInfoDataAccess)CurrentContext[type];
        }

        #endregion BusinessServiceRPO

        #region BusinessServiceRTO

        [DebuggerStepThrough]
        public override IBusinessServiceRTOInfoDataAccess CreateBusinessServiceRTOInfoDataAccess()
        {
            string type = typeof(BusinessServiceRTOInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessServiceRTOInfoDataAccess(CurrentContext);
            }

            return (IBusinessServiceRTOInfoDataAccess)CurrentContext[type];
        }

        #endregion BusinessServiceRTO

        #region ApplicationDependency

        [DebuggerStepThrough]
        public override IApplicationDependencyDataAccess CreateApplicationDependencyDataAccess()
        {
            string type = typeof(ApplicationDependencyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ApplicationDependencyDataAccess(CurrentContext);
            }

            return (IApplicationDependencyDataAccess)CurrentContext[type];
        }

        #endregion ApplicationDependency

        #region Application Dependency Mapping

        [DebuggerStepThrough]
        public override ITagEntityDetailsDataAccess CreateTagEntityDetailsDataAccess()
        {
            string type = typeof(TagEntityDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new TagEntityDetailsDataAccess(CurrentContext);
            }

            return (ITagEntityDetailsDataAccess)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IAppDepMappingHosts CreateAppDepMappingHosts()
        {
            string type = typeof(AppDepMappingHostsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AppDepMappingHostsDataAccess(CurrentContext);
            }

            return (IAppDepMappingHosts)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IAppDepMappingLinks CreateAppDepMappingLinks()
        {
            string type = typeof(AppDepMappingLinksDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AppDepMappingLinksDataAccess(CurrentContext);
            }

            return (IAppDepMappingLinks)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IAppDepMappingProfileDetails CreateAppDepMappingProfileDetails()
        {
            string type = typeof(AppDepMappingProfileDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AppDepMappingProfileDetailsDataAccess(CurrentContext);
            }

            return (IAppDepMappingProfileDetails)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IAppDepMapSettings CreateApplicationDependencyMappingSettingsDataAccess()
        {
            string type = typeof(AppDepMapSettingsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AppDepMapSettingsDataAccess(CurrentContext);
            }

            return (IAppDepMapSettings)CurrentContext[type];
        }

        #endregion

        #region ApplicationMonitor

        [DebuggerStepThrough]
        public override IApplicationMonitorDataAccess CreateApplicationDetailDataAccess()
        {
            string type = typeof(ApplicationMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ApplicationMonitorDataAccess(CurrentContext);
            }

            return (IApplicationMonitorDataAccess)CurrentContext[type];
        }

        #endregion ApplicationMonitor

        #region ApplicationService

        [DebuggerStepThrough]
        public override IApplicationServiceDataAccess CreateApplicationServiceDataAccess()
        {
            string type = typeof(ApplicationServiceDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ApplicationServiceDataAccess(CurrentContext);
            }

            return (IApplicationServiceDataAccess)CurrentContext[type];
        }

        #endregion ApplicationService

        #region Audit

        [DebuggerStepThrough]
        public override IAuditDataAccess CreateAuditDataAccess()
        {
            string type = typeof(AuditDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AuditDataAccess(CurrentContext);
            }

            return (IAuditDataAccess)CurrentContext[type];
        }

        #endregion Audit

        #region BPAutomation

        [DebuggerStepThrough]
        public override IBPAutomationDataAccess CreateBPAutomationDataAccess()
        {
            string type = typeof(BPAutomationdDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BPAutomationdDataAccess(CurrentContext);
            }

            return (IBPAutomationDataAccess)CurrentContext[type];
        }

        #endregion BPAutomation

        #region BusinessInfo

        [DebuggerStepThrough]
        public override IBusinessInfoDataAccess CreateBusinessInfoDataAccess()
        {
            string type = typeof(BusinessInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessInfoDataAccess(CurrentContext);
            }

            return (IBusinessInfoDataAccess)CurrentContext[type];
        }

        #endregion BusinessInfo

        #region BusinessUserFunction

        [DebuggerStepThrough]
        public override IBusinessUserFunctionDataAccess CreateBusinessUserFunctionDataAccess()
        {
            string type = typeof(BusinessUserFunctionDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessUserFunctionDataAccess(CurrentContext);
            }

            return (IBusinessUserFunctionDataAccess)CurrentContext[type];
        }

        #endregion BusinessUserFunction

        #region BusinessUserFunctionRPO

        [DebuggerStepThrough]
        public override IBusinessFunctionRPODataAccess CreateBusinessUserFunctionRPODataAccess()
        {
            string type = typeof(BusinessFunctionRPODataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BusinessFunctionRPODataAccess(CurrentContext);
            }

            return (IBusinessFunctionRPODataAccess)CurrentContext[type];
        }

        #endregion BusinessUserFunctionRPO

        #region CompanyInfo

        [DebuggerStepThrough]
        public override ICompanyInfoDataAccess CreateCompanyInfoDataAccess()
        {
            string type = typeof(CompanyInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CompanyInfoDataAccess(CurrentContext);
            }

            return (ICompanyInfoDataAccess)CurrentContext[type];
        }

        #endregion CompanyInfo

        #region CompanyProfile

        [DebuggerStepThrough]
        public override ICompanyProfileDataAccess CreateCompanyProfileDataAccess()
        {
            string type = typeof(CompanyProfileDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CompanyProfileDataAccess(CurrentContext);
            }

            return (ICompanyProfileDataAccess)CurrentContext[type];
        }

        #endregion CompanyProfile

        #region CPSLSCript

        [DebuggerStepThrough]
        public override ICPSLScriptDataAccess CreateCPSLScriptDataAccess()
        {
            string type = typeof(CPSLScriptDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CPSLScriptDataAccess(CurrentContext);
            }

            return (ICPSLScriptDataAccess)CurrentContext[type];
        }

        #endregion CPSLSCript

        #region CPSLSCheduled

        [DebuggerStepThrough]
        public override ICPSLScheduleDataAccess CreateCPSLScheduleDataAccess()
        {
            string type = typeof(CPSLScheduledDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CPSLScheduledDataAccess(CurrentContext);
            }

            return (ICPSLScheduleDataAccess)CurrentContext[type];
        }

        #endregion CPSLSCheduled

        #region CustomException

        [DebuggerStepThrough]
        public override ICustomExceptionDataAccess CreateCustomExceptionDataAccess()
        {
            string type = typeof(AlertTypeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AlertTypeDataAccess(CurrentContext);
            }

            return (ICustomExceptionDataAccess)CurrentContext[type];
        }

        #endregion CustomException

        #region CustomSubRoleTypes

        [DebuggerStepThrough]
        public override ICustomSubRoleTypeDataAccess CreateCustomSubRoleTypeDataAccess()
        {
            string type = typeof(CustomSubRoleTypeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CustomSubRoleTypeDataAccess(CurrentContext);
            }

            return (ICustomSubRoleTypeDataAccess)CurrentContext[type];
        }

        #endregion BusinessFunction

        #region BaseDatabase

        [DebuggerStepThrough]
        public override IDatabaseBaseDataAccess CreateDatabaseBaseDataAccess()
        {
            string type = typeof(DatabaseBaseDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseBaseDataAccess(CurrentContext);
            }

            return (IDatabaseBaseDataAccess)CurrentContext[type];
        }

        #endregion BaseDatabase

        #region DomainDetails

        [DebuggerStepThrough]
        public override IDomainDetailsDataAccess CreateDomainDetailsDataAccess()
        {
            string type = typeof(DomainDetailsDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DomainDetailsDataAccess(CurrentContext);
            }
            return (IDomainDetailsDataAccess)CurrentContext[type];
        }

        #endregion DomainDetails

        #region DatabaseBackupInfo

        [DebuggerStepThrough]
        public override IDatabaseBackupInfoDataAccess CreateDatabaseBackupInfoDataAccess()
        {
            string type = typeof(DatabaseBackupInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseBackupInfoDataAccess(CurrentContext);
            }

            return (IDatabaseBackupInfoDataAccess)CurrentContext[type];
        }

        #endregion DatabaseBackupInfo

        #region DatabaseBackupOperation

        [DebuggerStepThrough]
        public override IDatabaseBackupOperationDataAccess CreateDatabaseBackupOperationDataAccess()
        {
            string type = typeof(DatabaseBackupOperationBuilder).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseBackupOperationDataAccess(CurrentContext);
            }

            return (IDatabaseBackupOperationDataAccess)CurrentContext[type];
        }

        #endregion DatabaseBackupOperation

        #region DatabaseDb2

        [DebuggerStepThrough]
        public override IDatabaseDb2DataAccess CreateDatabaseDb2DataAccess()
        {
            string type = typeof(DatabaseDb2DataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseDb2DataAccess(CurrentContext);
            }

            return (IDatabaseDb2DataAccess)CurrentContext[type];
        }

        #endregion DatabaseDb2

        #region DatabaseExchange

        [DebuggerStepThrough]
        public override IDatabaseExchangeDataAccess CreateDatabaseExchangeDataAccess()
        {
            string type = typeof(DatabaseExchangeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseExchangeDataAccess(CurrentContext);
            }

            return (IDatabaseExchangeDataAccess)CurrentContext[type];
        }

        #endregion DatabaseExchange

        #region DatabaseExchangeDAG

        [DebuggerStepThrough]
        public override IDatabaseExchangeDAGDataAccess CreateDataBaseExchangeDAGDataAccess()
        {
            string type = typeof(DatabaseExchangeDAGDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseExchangeDAGDataAccess(CurrentContext);
            }

            return (IDatabaseExchangeDAGDataAccess)CurrentContext[type];
        }

        #endregion DatabaseExchangeDAG
        #region ExchangeDAGSumry

        [DebuggerStepThrough]
        public override IExChangeSumryDataAccess CreateExchangeSummaryDataAccess()
        {
            string type = typeof(DagSummaryDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DagSummaryDataAccess(CurrentContext);
            }

            return (IExChangeSumryDataAccess)CurrentContext[type];
        }

        #endregion ExchangeDAGSumry


        #region ExchangeHealthSumry

        [DebuggerStepThrough]
        public override IExChangeHealthSumryDataAccess CreateExchangeHealthSumryDataAccess()
        {
            string type = typeof(ExchngHealtSumryDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchngHealtSumryDataAccess(CurrentContext);
            }

            return (IExChangeHealthSumryDataAccess)CurrentContext[type];
        }

        #endregion ExchangeHealthSumry

        #region ExchangeMontrStats

        [DebuggerStepThrough]
        public override IExchngMntrStatusDataAccess CreateExchangeMntrStatusDataAccess()
        {
            string type = typeof(ExchangMntrStatsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangMntrStatsDataAccess(CurrentContext);
            }

            return (IExchngMntrStatusDataAccess)CurrentContext[type];
        }

        #endregion ExchangeMontrStats



        #region DatabaseMaxDB

        [DebuggerStepThrough]
        public override IDatabaseMaxDBDataAccess CreateDatabaseMaxDBDataAccess()
        {
            string type = typeof(DatabaseMaxDBDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseMaxDBDataAccess(CurrentContext);
            }

            return (IDatabaseMaxDBDataAccess)CurrentContext[type];
        }

        #endregion DatabaseMaxDB

        #region DatabaseNodes

        [DebuggerStepThrough]
        public override IDatabaseNodesDataAccess CreateDatabaseNodesDataAccess()
        {
            string type = typeof(DatabaseNodesDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseNodesDataAccess(CurrentContext);
            }

            return (IDatabaseNodesDataAccess)CurrentContext[type];
        }

        #endregion DatabaseNodes

        #region DatabaseOracle

        [DebuggerStepThrough]
        public override IDatabaseOracleDataAccess CreateDatabaseOracleDataAccess()
        {
            string type = typeof(DatabaseOracleDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseOracleDataAccess(CurrentContext);
            }

            return (IDatabaseOracleDataAccess)CurrentContext[type];
        }

        #endregion DatabaseOracle

        #region DatabaseOracleRac

        [DebuggerStepThrough]
        public override IDatabaseOracleRacDataAccess CreateDatabaseOracleRacDataAccess()
        {
            string type = typeof(DatabaseOracleRacDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseOracleRacDataAccess(CurrentContext);
            }

            return (IDatabaseOracleRacDataAccess)CurrentContext[type];
        }

        #endregion DatabaseOracleRac

        #region DatabaseSql

        [DebuggerStepThrough]
        public override IDatabaseSqlDataAccess CreateDatabaseSqlDataAccess()
        {
            string type = typeof(DatabaseSqlDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseSqlDataAccess(CurrentContext);
            }

            return (IDatabaseSqlDataAccess)CurrentContext[type];
        }

        #endregion DatabaseSql

        #region DatabaseSybase

        [DebuggerStepThrough]
        public override IDatabaseSyBaseDataAccess CreateDatabaseSybaseDataAccess()
        {
            string type = typeof(DataBaseSyBaseDBDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DataBaseSyBaseDBDataAccess(CurrentContext);
            }

            return (IDatabaseSyBaseDataAccess)CurrentContext[type];
        }

        #endregion DatabaseSybase

        #region DatabaseSybaseWithSrs

        [DebuggerStepThrough]
        public override IDatabaseSyBaseWithSrsDataAccess CreateDatabaseSybaseWithSrsDataAccess()
        {
            string type = typeof(DataBaseSybaseWithSrsDBDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DataBaseSybaseWithSrsDBDataAccess(CurrentContext);
            }

            return (IDatabaseSyBaseWithSrsDataAccess)CurrentContext[type];
        }

        #endregion DatabaseSybase

        #region DatabasePostgreSql

        [DebuggerStepThrough]
        public override IDatabasePostgreSqlDataAccess CreateDatabasePostgreSqlDataAccess()
        {
            string type = typeof(DatabaseSqlDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabasepostgreSqlDataAccess(CurrentContext);
            }

            return (IDatabasePostgreSqlDataAccess)CurrentContext[type];
        }

        #endregion DatabasePostgreSql

        #region DatabaseDatabasePostgre9x

        //[DebuggerStepThrough]
        //public override IDatabasePostgre9xDataAccess CreateDatabasePostgre9xDataAccess()
        //{
        //    string type = typeof(DatabaseSqlDataAccess).ToString();

        //    if (!CurrentContext.Contains(type))
        //    {
        //        CurrentContext[type] = new DatabaseDatabasePostgre9xDataAccess(CurrentContext);
        //    }

        //    return (IDatabasePostgre9xDataAccess)CurrentContext[type];
        //}

        [DebuggerStepThrough]
        public override IDatabasePostgre9xDataAccess CreateDatabasePostgre9xDataAccess()
        {
            string type = typeof(DatabaseDatabasePostgre9xDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseDatabasePostgre9xDataAccess(CurrentContext);
            }

            return (IDatabasePostgre9xDataAccess)CurrentContext[type];
        }

        #endregion DatabasePostgre9x

        #region DatabaseMS_SQL

        [DebuggerStepThrough]
        public override IDataBaseMSSqlDataAccess CreateDatabaseMSSqlDataAccess()
        {
            string type = typeof(IDataBaseMSSqlDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                //CurrentContext[type] = new DatabaseDatabasePostgre9xDataAccess(CurrentContext);
                CurrentContext[type] = new DataBaseMSSqlDataAccess(CurrentContext);
            }

            return (DataBaseMSSqlDataAccess)CurrentContext[type];
        }

        #endregion DatabaseMS_SQL

        #region DatabaseVersion

        [DebuggerStepThrough]
        public override IDatabaseVersionDataAccess CreateDatabaseVersionDataAccess()
        {
            string type = typeof(DatabaseVersionDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseVersionDataAccess(CurrentContext);
            }

            return (IDatabaseVersionDataAccess)CurrentContext[type];
        }

        #endregion DatabaseVersion

        #region DataGuard

        [DebuggerStepThrough]
        public override IDataGuardDataAccess CreateDataGuardDataAccess()
        {
            string type = typeof(DataGuardDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DataGuardDataAccess(CurrentContext);
            }

            return (IDataGuardDataAccess)CurrentContext[type];
        }

        #endregion DataGuard

        #region DataGuardMonitor

        [DebuggerStepThrough]
        public override IDataGuardMonitorDataAccess CreateDataGuardMonitorDataAccess()
        {
            string type = typeof(DataGuardMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DataGuardMonitorDataAccess(CurrentContext);
            }

            return (IDataGuardMonitorDataAccess)CurrentContext[type];
        }

        #endregion DataGuardMonitor

        #region DataSyncProperties

        [DebuggerStepThrough]
        public override IDataSyncPropertiesDataAccess CreateDataSyncPropertiesDataAccess()
        {
            string type = typeof(DataSyncPropertiesDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DataSyncPropertiesDataAccess(CurrentContext);
            }

            return (IDataSyncPropertiesDataAccess)CurrentContext[type];
        }

        #endregion DataSyncProperties

        #region DiscoverScan

        [DebuggerStepThrough]
        public override IDiscoverScanDataAccess CreateDiscoverScanDataAccess()
        {
            string type = typeof(DiscoverScanDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DiscoverScanDataAccess(CurrentContext);
            }

            return (IDiscoverScanDataAccess)CurrentContext[type];
        }

        #endregion DiscoverScan

        #region DiscoveryConfiguration

        [DebuggerStepThrough]
        public override IDiscoveryConfigurationDataAccess CreateDiscoveryConfigurationDataAccess()
        {
            string type = typeof(DiscoveryConfigurationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DiscoveryConfigurationDataAccess(CurrentContext);
            }

            return (IDiscoveryConfigurationDataAccess)CurrentContext[type];
        }

        #endregion DiscoveryConfiguration

        #region DiscoveryHostLogs

        [DebuggerStepThrough]
        public override IDiscoveryHostLogsDataAccess CreateDiscoveryHostLogsDataAccess()
        {
            string type = typeof(DiscoveryHostLogsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DiscoveryHostLogsDataAccess(CurrentContext);
            }

            return (IDiscoveryHostLogsDataAccess)CurrentContext[type];
        }

        #endregion DiscoveryHostLogs

        #region DnsServerDetails

        [DebuggerStepThrough]
        public override IDnsServerDetailsDataAccess CreateDnsServerDetailsDataAccess()
        {
            string type = typeof(DnsServerDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DnsServerDetailsDataAccess(CurrentContext);
            }

            return (IDnsServerDetailsDataAccess)CurrentContext[type];
        }

        #endregion DnsServerDetails

        #region DROperation

        [DebuggerStepThrough]
        public override IDROperationDataAccess CreateDROperationDataAccess()
        {
            string type = typeof(DROperationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DROperationDataAccess(CurrentContext);
            }

            return (IDROperationDataAccess)CurrentContext[type];
        }

        #endregion DROperation

        #region DROperationResult

        [DebuggerStepThrough]
        public override IDROperationResultDataAccess CreateDROperationResultDataAccess()
        {
            string type = typeof(DROperationResultDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DROperationResultDataAccess(CurrentContext);
            }

            return (IDROperationResultDataAccess)CurrentContext[type];
        }

        #endregion DROperationResult

        # region DB2DataSync

        public override IDB2DataSyncMonitorDataAccess CreateDB2DataSyncDataAccess()
        {
            string type = typeof(DB2DataSyncMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DB2DataSyncMonitorDataAccess(CurrentContext);
            }

            return (IDB2DataSyncMonitorDataAccess)CurrentContext[type];
        }

        #endregion ContinuityPatrol Factory Methods

        #region DatabaseMySql

        [DebuggerStepThrough]
        public override IDatabaseMySqlDataAccess CreateDatabaseMySqlDataAccess()
        {
            string type = typeof(CP.DataAccess.DataBaseMySql.DatabaseMySqlDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CP.DataAccess.DataBaseMySql.DatabaseMySqlDataAccess(CurrentContext);
            }

            return (IDatabaseMySqlDataAccess)CurrentContext[type];
        }

        #endregion DatabaseMySql

        #region EmcDevicesDetails

        [DebuggerStepThrough]
        public override IEMCDeviceDetailsDataAccess CreateEMCDeviceDetailsDataAccess()
        {
            string type = typeof(EMCDeviceDetailsDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCDeviceDetailsDataAccess(CurrentContext);
            }
            return (IEMCDeviceDetailsDataAccess)CurrentContext[type];
        }

        #endregion EmcDevicesDetails

        #region Ec2S3datasyncDetails

        [DebuggerStepThrough]
        public override IEc2S3DataSyncMonitorDetailsDataAccess CreateEc2S3DataSyncMonitorDetailsDataAccess()
        {
            string type = typeof(Ec2S3DataSyncMonitorDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Ec2S3DataSyncMonitorDataAccess(CurrentContext);
            }
            return (IEc2S3DataSyncMonitorDetailsDataAccess)CurrentContext[type];
        }

        #endregion Ec2S3datasyncDetails

        #region Ec2S3datasyncReplicationMonitor

        [DebuggerStepThrough]
        public override IEc2S3DataSyncReplicationMonitorDataAccess CreateEc2S3DataSyncRepliMonitorDataAccess()
        {
            string type = typeof(Ec2S3DataSyncReplicationMonitorDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Ec2S3DataSyncReplicationMonitorDataAccess(CurrentContext);
            }
            return (IEc2S3DataSyncReplicationMonitorDataAccess)CurrentContext[type];
        }

        #endregion Ec2S3datasyncReplicationMonitor

        #region EMCSRDF

        [DebuggerStepThrough]
        public override IEMCSRDFDataAccess CreateEMCSRDFDataAccess()
        {
            string type = typeof(EmcSrdfDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcSrdfDataAccess(CurrentContext);
            }

            return (IEMCSRDFDataAccess)CurrentContext[type];
        }

        #endregion EMCSRDF

        #region RecoveryPoint

        public override IRecoveryPointDataAccess CreateRecoveryPointDataAccess()
        {

            string type = typeof(RecoveryPointDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RecoveryPointDataAccess(CurrentContext);
            }

            return (IRecoveryPointDataAccess)CurrentContext[type];
        }

        #endregion

        #region RecoverPStateMonitor

        public override IRecoverPStateMonitorDataAccess CreateRecoverPStateMonitorDataAccess()
        {
            string type = typeof(RecoveryPStateMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RecoveryPStateMonitorDataAccess(CurrentContext);
            }

            return (IRecoverPStateMonitorDataAccess)CurrentContext[type];
        }
        #endregion

        #region RecoverPStatisticMonitor
        public override IRecoverPStatisticMonitorDataAccess CreateRecoverPStatisticMonitorDataAccess()
        {
            string type = typeof(RecoverPStatisticMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RecoverPStatisticMonitorDataAccess(CurrentContext);
            }

            return (IRecoverPStatisticMonitorDataAccess)CurrentContext[type];
        }
        #endregion

        #region ExchangeSCRStatus

        [DebuggerStepThrough]
        public override IExchangeSCRStatusDataAccess CreateExchangeSCRStatusDataAccess()
        {
            string type = typeof(ExchangeSCRStatusDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangeSCRStatusDataAccess(CurrentContext);
            }

            return (IExchangeSCRStatusDataAccess)CurrentContext[type];
        }

        #endregion ExchangeSCRStatus

        #region ExchangeService

        [DebuggerStepThrough]
        public override IExchangeServiceDataAccess CreateExchangeServiceDataAccess()
        {
            string type = typeof(ExchangeServiceDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangeServiceDataAccess(CurrentContext);
            }

            return (IExchangeServiceDataAccess)CurrentContext[type];
        }

        #endregion ExchangeService

        #region ExchangeHealth

        [DebuggerStepThrough]
        public override IExchangeHealthDataAccess CreateExchangeHealthDataAccess()
        {
            string type = typeof(ExchangeHealthDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangeHealthDataAccess(CurrentContext);
            }

            return (IExchangeHealthDataAccess)CurrentContext[type];
        }

        #endregion ExchangeHealth

        #region ExchageDAGRep

        [DebuggerStepThrough]
        public override IExchageDagReplicationDataAccess CreateExchangeReplicationDataAccess()
        {
            string type = typeof(ExchangedagReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangedagReplicationDataAccess(CurrentContext);
            }

            return (IExchageDagReplicationDataAccess)CurrentContext[type];
        }

        #endregion ExchageDAGRep

        #region ExchangeDAGCompMonitorDataAccess

        [DebuggerStepThrough]
        public override IExchangeDAGCompMonitorDataAccess CreateExchangeDAGCompMonitorDataAccess()
        {
            string type = typeof(ExchangeDAGCompMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangeDAGCompMonitorDataAccess(CurrentContext);
            }

            return (IExchangeDAGCompMonitorDataAccess)CurrentContext[type];
        }

        #endregion ExchangeDAGCompMonitorDataAccess

        #region ExchangeDAGMonitor

        [DebuggerStepThrough]
        public override IExchangeDAGMonitoringDataAccess CreateExchangeDAGMonitoringDataAccess()
        {
            string type = typeof(IExchangeDAGMonitoringDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangeDAGMonitoringDataAccess(CurrentContext);
            }

            return (IExchangeDAGMonitoringDataAccess)CurrentContext[type];
        }

        #endregion ExchangeDAGMonitor

        #region ExchangeDAGReplHealthStatus

        [DebuggerStepThrough]
        public override IExchangeDAGReplHealthStatusDataAccess CreateExchangeDAGReplHealthStatusDataAccess()
        {
            string type = typeof(IExchangeDAGReplHealthStatusDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangeDAGReplHealthStatusDataAccess(CurrentContext);
            }

            return (IExchangeDAGReplHealthStatusDataAccess)CurrentContext[type];
        }

        #endregion ExchangeDAGReplHealthStatus

        #region ExchangeDAGServiceMonitoring

        [DebuggerStepThrough]
        public override IExchangeDAGServiceMonitoringDataAccess CreateExchangeDAGServiceMonitoringDataAccess()
        {
            string type = typeof(IExchangeDAGServiceMonitoringDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ExchangeDAGServiceMonitoringDataAccess(CurrentContext);
            }

            return (IExchangeDAGServiceMonitoringDataAccess)CurrentContext[type];
        }

        #endregion ExchangeDAGServiceMonitoring

        #region EventManagement

        [DebuggerStepThrough]
        public override IEventManagementDataAccess CreateEventManagementDataAccess()
        {
            string type = typeof(EventManagementDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EventManagementDataAccess(CurrentContext);
            }

            return (IEventManagementDataAccess)CurrentContext[type];
        }

        #endregion EventManagement

        #region EventManagementList

        [DebuggerStepThrough]
        public override IEventManagementListDataAccess CreateEventManagementListDataAccess()
        {
            string type = typeof(EventManagementList).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EventManagementListDataAccess(CurrentContext);
            }

            return (IEventManagementListDataAccess)CurrentContext[type];
        }

        #endregion EventManagementList

        #region FastCopy

        [DebuggerStepThrough]
        public override IFastCopyDataAccess CreateFastCopyDataAccess()
        {
            string type = typeof(FastCopyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new FastCopyDataAccess(CurrentContext);
            }

            return (IFastCopyDataAccess)CurrentContext[type];
        }

        #endregion FastCopy

        #region FastCopyJob

        [DebuggerStepThrough]
        public override IFastCopyJobDataAccess CreateFastCopyJobDataAccess()
        {
            string type = typeof(FastCopyJobDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new FastCopyJobDataAccess(CurrentContext);
            }

            return (IFastCopyJobDataAccess)CurrentContext[type];
        }

        #endregion FastCopyJob

        #region FastCopyReplication

        [DebuggerStepThrough]
        public override IFastCopyMonitorDataAccess CreateFastCopyReplicationDataAccess()
        {
            string type = typeof(FastCopyMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new FastCopyMonitorDataAccess(CurrentContext);
            }

            return (IFastCopyMonitorDataAccess)CurrentContext[type];
        }

        #endregion FastCopyReplication

        #region Group

        [DebuggerStepThrough]
        public override IGroupDataAccess CreateGroupDataAccess()
        {
            string type = typeof(GroupDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GroupDataAccess(CurrentContext);
            }

            return (IGroupDataAccess)CurrentContext[type];
        }

        #endregion Group

        #region InfraObjectJob

        [DebuggerStepThrough]
        public override IInfraObjectJobDataAccess CreateInfraObjectJobDataAccess()
        {
            string type = typeof(InfraObjectJobDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraObjectJobDataAccess(CurrentContext);
            }

            return (IInfraObjectJobDataAccess)CurrentContext[type];
        }

        #endregion InfraObjectJob

        #region InfraobjectCGName

        public override IInfraobjectCGDetailsDataAccess CreateInfraobjectCGDetailsDataAccess()
        {
            string type = typeof(InfraobjectCGNameDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraobjectCGNameDataAccess(CurrentContext);
            }

            return (IInfraobjectCGDetailsDataAccess)CurrentContext[type];

        }

        #endregion

        #region IncidentManagementSummary

        public override IIncidentManagementSummaryDataAccess CreateIncidentManagementSummaryDataAccess()
        {
            string type = typeof(IncidentManagementSummaryDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new IncidentManagementSummaryDataAccess(CurrentContext);
            }

            return (IncidentManagementSummaryDataAccess)CurrentContext[type];
        }

        #endregion IncidentManagementSummary

        #region IncidentManagementBIASummary

        public override IIncidentManagementBIASummaryDataAccess CreateIncidentManagementBIASummaryDataAccess()
        {
            string type = typeof(IncidentManagementBIASummaryDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new IncidentManagementBIASummaryDataAccess(CurrentContext);
            }

            return (IncidentManagementBIASummaryDataAccess)CurrentContext[type];
        }

        #endregion IncidentManagementBIASummary

        #region GroupDatabaseNodes

        //[DebuggerStepThrough]
        public override IGroupDatabaseNodesDataAccess CreateGroupDatabaseNodesDataAccess()
        {
            string type = typeof(GroupDatabaseNodes).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GroupDatabaseNodesDataAccess(CurrentContext);
            }

            return (IGroupDatabaseNodesDataAccess)CurrentContext[type];
        }

        #endregion GroupDatabaseNodes

        #region JobTypeReplicationType

        //[DebuggerStepThrough]
        public override IJobTypeReplicationTypeDataAccess CreateJobTypeReplicationTypeDataAccess()
        {
            string type = typeof(JobTypeReplicationType).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new JobTypeRepTypeDataAccess(CurrentContext);
            }

            return (IJobTypeReplicationTypeDataAccess)CurrentContext[type];
        }

        #endregion JobTypeReplicationType

        //#region InfraObjects

        //[DebuggerStepThrough]
        //public override IInfraObjectsDataAccess CreateInfraObjectsDataAccess()
        //{
        //    string type = typeof(InfraObjectsDataAccess).ToString();

        //    if (!CurrentContext.Contains(type))
        //    {
        //        CurrentContext[type] = new InfraObjectsDataAccess(CurrentContext);
        //    }

        //    return (IInfraObjectsDataAccess)CurrentContext[type];
        //}

        //#endregion

        #region InfraobjectDiskMonitor

        [DebuggerStepThrough]
        public override IInfraobjectDiskMonitorDataAccess CreateInfraobjectDiskMonitorDataAccess()
        {
            string type = typeof(InfraobjectDiskMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraobjectDiskMonitorDataAccess(CurrentContext);
            }

            return (IInfraobjectDiskMonitorDataAccess)CurrentContext[type];
        }

        #endregion InfraobjectDiskMonitor

        #region InfraObjectsLuns

        [DebuggerStepThrough]
        public override IInfraObjectsLunsDataAccess CreateInfraObjectsLunsDataAccess()
        {
            string type = typeof(InfraObjectsLunsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraObjectsLunsDataAccess(CurrentContext);
            }

            return (IInfraObjectsLunsDataAccess)CurrentContext[type];
        }

        #endregion InfraObjectsLuns

        #region Heatmap

        [DebuggerStepThrough()]
        public override IHeatmapDataAccess CreateHeatmapDataAccess()
        {
            string type = typeof(HeatmapDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HeatmapDataAccess(CurrentContext);
            }

            return (IHeatmapDataAccess)CurrentContext[type];
        }

        #endregion Heatmap

        #region ImpactAnalysis

        [DebuggerStepThrough()]
        public override IImpactAnalysisDataAccess CreateImpactAnalysisDataAccess()
        {
            string type = typeof(ImpactAnalysisDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ImpactAnalysisDataAccess(CurrentContext);
            }

            return (IImpactAnalysisDataAccess)CurrentContext[type];
        }

        #endregion ImpactAnalysis

        #region ImpactTypeMaster

        [DebuggerStepThrough()]
        public override IImpactTypeMasterDataAccess CreateImpactTypeMasterDataAccess()
        {
            string type = typeof(ImpactTypeMasterDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ImpactTypeMasterDataAccess(CurrentContext);
            }

            return (IImpactTypeMasterDataAccess)CurrentContext[type];
        }

        #endregion

        #region ImpactMaster

        [DebuggerStepThrough()]
        public override IImpactMasterDataAccess CreateImpactMasterDataAccess()
        {
            string type = typeof(ImpactMasterDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ImpactMasterDataAccess(CurrentContext);
            }

            return (IImpactMasterDataAccess)CurrentContext[type];
        }

        #endregion

        #region ImpactRelType

        [DebuggerStepThrough()]
        public override IImpactRelTypeDataAccess CreateImpactRelTypeDataAccess()
        {
            string type = typeof(ImpactRelTypeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ImpactRelTypeDataAccess(CurrentContext);
            }

            return (IImpactRelTypeDataAccess)CurrentContext[type];
        }

        #endregion

        #region Incident

        [DebuggerStepThrough]
        public override IIncidentDataAccess CreateIncidentDataAccess()
        {
            string type = typeof(IncidentDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new IncidentDataAccess(CurrentContext);
            }

            return (IIncidentDataAccess)CurrentContext[type];
        }

        #endregion Incident

        #region Infrastructure

        [DebuggerStepThrough]
        public override IInfrastructureDataAccess CreateInfrastructureDataAccess()
        {
            string type = typeof(InfrastructureDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfrastructureDataAccess(CurrentContext);
            }

            return (IInfrastructureDataAccess)CurrentContext[type];
        }

        #endregion Infrastructure

        #region Incident Managment

        [DebuggerStepThrough]
        public override IIncidentManagementDataAccess CreateIncidentManagementDataAccess()
        {
            string type = typeof(IncidentManagmentDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new IncidentManagmentDataAccess(CurrentContext);
            }

            return (IIncidentManagementDataAccess)CurrentContext[type];
        }

        #endregion Incident Managment

        #region Incident Managment New

        [DebuggerStepThrough]
        public override IIncidentManagementNewDataAccess CreateIncidentManagementNewDataAccess()
        {
            string type = typeof(IncidentManagementNewDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new IncidentManagementNewDataAccess(CurrentContext);
            }

            return (IIncidentManagementNewDataAccess)CurrentContext[type];
        }

        #endregion Incident Managment New

        #region InfraObject

        [DebuggerStepThrough]
        public override IInfraObjectDataAccess CreateInfraObjectDataAccess()
        {
            string type = typeof(InfraObjectDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraObjectDataAccess(CurrentContext);
            }

            return (IInfraObjectDataAccess)CurrentContext[type];
        }

        #endregion InfraObject

        #region JobName

        public override IJobDataAccess CreateJobDataAccess()
        {
            string type = typeof(JobDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new JobDataAccess(CurrentContext);
            }

            return (IJobDataAccess)CurrentContext[type];
        }

        #endregion JobName

        #region GroupLuns

        [DebuggerStepThrough]
        public override IGroupLunsDataAccess CreateGroupLunsDataAccess()
        {
            string type = typeof(GroupLunsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GroupLunsDataAccess(CurrentContext);
            }

            return (IGroupLunsDataAccess)CurrentContext[type];
        }

        #endregion GroupLuns

        #region Group_Luns

        [DebuggerStepThrough]
        public override IGroup_LunsDataAccess CreateGroup_LunsDataAccess()
        {
            string type = typeof(Group_LunsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Group_LunsDataAccess(CurrentContext);
            }

            return (IGroup_LunsDataAccess)CurrentContext[type];
        }

        #endregion Group_Luns


        #region GroupWorkflow

        [DebuggerStepThrough]
        public override IGroupWorkflowDataAccess CreateGroupWorkflowDataAccess()
        {
            string type = typeof(GroupWorkflowDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GroupWorkflowDataAccess(CurrentContext);
            }

            return (IGroupWorkflowDataAccess)CurrentContext[type];
        }

        #endregion GroupWorkflow

        #region GlobalMirror

        [DebuggerStepThrough]
        public override IGlobalMirrorDataAccess CreateGlobalMirrorDataAccess()
        {
            string type = typeof(GlobalMirrorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GlobalMirrorDataAccess(CurrentContext);
            }

            return (IGlobalMirrorDataAccess)CurrentContext[type];
        }

        #endregion GlobalMirror

        #region GlobalMirrorLuns

        [DebuggerStepThrough]
        public override IGlobalMirrorLunsDataAccess CreateGlobalMirrorLunsDataAccess()
        {
            string type = typeof(GlobalMirrorLunsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GlobalMirrorLunsDataAccess(CurrentContext);
            }

            return (IGlobalMirrorLunsDataAccess)CurrentContext[type];
        }

        #endregion GlobalMirrorLuns

        #region GlobalMirrorReplication

        [DebuggerStepThrough]
        public override IGlobalMirrorMonitorDataAccess CreateGlobalMirrorReplicationDataAccess()
        {
            string type = typeof(GlobalMirrorMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GlobalMirrorMonitorDataAccess(CurrentContext);
            }

            return (IGlobalMirrorMonitorDataAccess)CurrentContext[type];
        }

        #endregion GlobalMirrorReplication

        #region LogVolume

        [DebuggerStepThrough]
        public override ILogVolumeDataAccess CreateLogVolumeDataAccess()
        {
            string type = typeof(LogVolumeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new LogVolumeDataAccess(CurrentContext);
            }

            return (ILogVolumeDataAccess)CurrentContext[type];
        }

        #endregion LogVolume

        #region LicenseKey

        [DebuggerStepThrough]
        public override ILicencekeyDataAccess CreateLicencekeyDataAccess()
        {
            string type = typeof(LicencekeyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new LicencekeyDataAccess(CurrentContext);
            }

            return (ILicencekeyDataAccess)CurrentContext[type];
        }

        #endregion LicenseKey

        #region Maintenance

        [DebuggerStepThrough]
        public override IMaintenanceDataAccess CreateMaintenanceDataAccess()
        {
            string type = typeof(MaintenanceDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MaintenanceDataAccess(CurrentContext);
            }

            return (IMaintenanceDataAccess)CurrentContext[type];
        }

        #endregion Maintenance

        #region MaxDBMonitor

        [DebuggerStepThrough]
        public override IMaxDBMonitorDataAccess CreateMaxDBMonitorDataAccess()
        {
            string type = typeof(MaxDBMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MaxDBMonitorDataAccess(CurrentContext);
            }

            return (IMaxDBMonitorDataAccess)CurrentContext[type];
        }

        #endregion MaxDBMonitor

        #region MaxDBReplication

        [DebuggerStepThrough]
        public override IMaxDBReplicationDataAccess CreateMaxDBReplicationDataAccess()
        {
            string type = typeof(MaxDBReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MaxDBReplicationDataAccess(CurrentContext);
            }

            return (IMaxDBReplicationDataAccess)CurrentContext[type];
        }

        #endregion MaxDBReplication

        #region MountPoint

        [DebuggerStepThrough]
        public override IMountPointDataAccess CreateMountPointDataAccess()
        {
            string type = typeof(MountPointDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MountPointDataAccess(CurrentContext);
            }

            return (IMountPointDataAccess)CurrentContext[type];
        }

        #endregion MountPoint

        #region MonitorServices

        [DebuggerStepThrough]
        public override IMonitorServicesDataAccess CreateMonitorServicesDataAccess()
        {
            string type = typeof(MonitorServicesDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MonitorServicesDataAccess(CurrentContext);
            }

            return (IMonitorServicesDataAccess)CurrentContext[type];
        }

        #endregion MonitorServices

        #region MonitorQueue
        public override IMonitorQueueDataAccess CreateMonitorQueueDataAccess()
        {
            string type = typeof(MonitorQueueDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MonitorQueueDataAccess(CurrentContext);
            }

            return (MonitorQueueDataAccess)CurrentContext[type];

        }
        #endregion

        #region QueueMoniter
        public override IQueueMoniterDataAccess CreateQueueMoniterDataAccess()
        {

            string type = typeof(QueueMoniterDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new QueueMoniterDataAccess(CurrentContext);
            }

            return (QueueMoniterDataAccess)CurrentContext[type];

        }

        #endregion

        #region MonitorServiceStatusLogs

        [DebuggerStepThrough]
        public override IMonitorServiceStatusLogsDataAccess CreateMonitorServiceStatusLogsDataAccess()
        {
            string type = typeof(MonitorServiceStatusLogsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MonitorServiceStatusLogsDataAccess(CurrentContext);
            }

            return (IMonitorServiceStatusLogsDataAccess)CurrentContext[type];
        }

        #endregion MonitorServiceStatusLogs

        #region DatabaseMirrorMonitor

        [DebuggerStepThrough]
        public override IDatabaseMirrorMonitorDataAccess CreateDatabaseMirrorMonitorDataAccess()
        {
            string type = typeof(DatabaseMirrorMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseMirrorMonitorDataAccess(CurrentContext);
            }

            return (IDatabaseMirrorMonitorDataAccess)CurrentContext[type];
        }

        #endregion DatabaseMirrorMonitor

        #region MySqlGlobalMirror

        [DebuggerStepThrough]
        public override IMySQLGlobalMirrorMonitorDataAccess CreateMysqlGlobalMirrorMonitorDataAccess()
        {
            string type = typeof(CP.DataAccess.MySqlGlobalMirrorMonitor.MySqlGlobalMirrorMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CP.DataAccess.MySqlGlobalMirrorMonitor.MySqlGlobalMirrorMonitorDataAccess(CurrentContext);
            }

            return (IMySQLGlobalMirrorMonitorDataAccess)CurrentContext[type];
        }

        #endregion MySqlGlobalMirror

        #region MySqlNativeMonitor

        [DebuggerStepThrough]
        public override IMySqlNativeMonitorDataAccess CreateMySqlNativeMonitorDataAccess()
        {
            string type = typeof(CP.DataAccess.MySqlNativeMonitor.MySqlNativeMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CP.DataAccess.MySqlNativeMonitor.MySqlNativeMonitorDataAccess(CurrentContext);
            }

            return (IMySqlNativeMonitorDataAccess)CurrentContext[type];
        }

        #endregion MySqlNativeMonitor

        #region MssqlEmcsrdfMonitor

        [DebuggerStepThrough]
        public override IMssqlEmcSrdfMonitorDataAccess CreateMssqlEmcSrdfMonitorDataAccess()
        {
            string type = typeof(MssqlEmcSrdfMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MssqlEmcSrdfMonitorDataAccess(CurrentContext);
            }

            return (IMssqlEmcSrdfMonitorDataAccess)CurrentContext[type];
        }

        #endregion MssqlEmcsrdfMonitor

        #region Nodes

        [DebuggerStepThrough]
        public override INodesDataAccess CreateNodesDataAccess()
        {
            string type = typeof(NodesDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NodesDataAccess(CurrentContext);
            }

            return (INodesDataAccess)CurrentContext[type];
        }

        #endregion Nodes

        #region NetworkIP

        [DebuggerStepThrough]
        public override INetworkIPDataAcess CreateNetworkIPDataAccess()
        {
            string type = typeof(NetworkIPDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NetworkIPDataAccess(CurrentContext);
            }

            return (INetworkIPDataAcess)CurrentContext[type];
        }

        #endregion NetworkIP

        #region PostgreMonitor

        [DebuggerStepThrough]
        public override IPostgredbMonitoringDataAccess CreatePostgredbMonitoringDataAccess()
        {
            string type = typeof(PostgredbMonitoring).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new PostgredbMonitoringDataAccess(CurrentContext);
            }

            return (IPostgredbMonitoringDataAccess)CurrentContext[type];
        }

        #endregion

        #region Postgredbstatus

        [DebuggerStepThrough]
        public override IPostgreReplicationDataAccess CreatePostgreReplicationDataAccess()
        {
            string type = typeof(PostgreReplication).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new PostgreReplicationDataAccess(CurrentContext);
            }

            return (IPostgreReplicationDataAccess)CurrentContext[type];
        }

        #endregion

        #region PrallelDROperation

        [DebuggerStepThrough]
        public override IParallelDROperationDataAccess CreateParallelDROperationDataAccess()
        {
            string type = typeof(ParallelDROperationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ParallelDROperationDataAccess(CurrentContext);
            }

            return (IParallelDROperationDataAccess)CurrentContext[type];
        }

        #endregion PrallelDROperation

        #region ParallelGroupWorkflow

        [DebuggerStepThrough]
        public override IParallelGroupWorkflowDataAccess CreateParallelGroupWorkflowDataAccess()
        {
            string type = typeof(ParallelGroupWorkflowDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ParallelGroupWorkflowDataAccess(CurrentContext);
            }

            return (IParallelGroupWorkflowDataAccess)CurrentContext[type];
        }

        #endregion ParallelGroupWorkflow

        #region ParallelWorkflowActionResult

        [DebuggerStepThrough]
        public override IParallelWorkflowActionResultDataAccess CreateParallelWorkflowActionResultDataAccess()
        {
            string type = typeof(ParallelWorkflowActionResultDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ParallelWorkflowActionResultDataAccess(CurrentContext);
            }

            return (IParallelWorkflowActionResultDataAccess)CurrentContext[type];
        }

        #endregion ParallelWorkflowActionResult

        #region ParallelProfile

        public override IParallelProfileDataAccess CreateParallelProfileDataAccess()
        {
            string type = typeof(ParallelProfileDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ParallelProfileDataAccess(CurrentContext);
            }

            return (IParallelProfileDataAccess)CurrentContext[type];
        }

        #endregion ParallelProfile

        #region ParallelWorkflowProfile

        [DebuggerStepThrough]
        public override IParallelWorkflowProfileDataAccess CreateParallelWorkflowProfileDataAccess()
        {
            string type = typeof(ParallelWorkflowProfileDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ParallelWorkflowProfileDataAccess(CurrentContext);
            }

            return (IParallelWorkflowProfileDataAccess)CurrentContext[type];
        }

        #endregion ParallelWorkflowProfile

        #region ParallelServer

        public override IParallelServerDataAccess CreateParallelServerDataAccess()
        {
            string type = typeof(ParallelServerDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ParallelServerDataAccess(CurrentContext);
            }

            return (IParallelServerDataAccess)CurrentContext[type];
        }

        #endregion ParallelServer

        #region ReplicationBase

        [DebuggerStepThrough]
        public override IReplicationBaseDataAccess CreateReplicationBaseDataAccess()
        {
            string type = typeof(ReplicationBaseDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ReplicationBaseDataAccess(CurrentContext);
            }

            return (IReplicationBaseDataAccess)CurrentContext[type];
        }

        #endregion ReplicationBase

        #region ReportSchedule

        [DebuggerStepThrough]
        public override IReportScheduleDataAccess CreateReportScheduleDataAccess()
        {
            string type = typeof(ReportScheduleDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ReportScheduleDataAccess(CurrentContext);
            }

            return (IReportScheduleDataAccess)CurrentContext[type];
        }

        #endregion ReportSchedule

        #region RTOMTRConfiguration

        [DebuggerStepThrough]
        public override IRTOMTRConfigurationDataAccess CreateRTOMTRConfigurationDataAccess()
        {
            string type = typeof(RTOMTRConfigurationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RTOMTRConfigurationDataAccess(CurrentContext);
            }

            return (IRTOMTRConfigurationDataAccess)CurrentContext[type];
        }


        #endregion

        #region OracleEmcSrdf

        [DebuggerStepThrough]
        public override IOracleEmcSrdfVMAXDataAccess CreateOracleEmcSrdfVMAXDataAccess()
        {
            string type = typeof(OracleEmcSrdfFullVMAXDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OracleEmcSrdfFullVMAXDataAccess(CurrentContext);
            }

            return (IOracleEmcSrdfVMAXDataAccess)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IOracleEmcSrdfDMXDataAccess CreateOracleEmcSrdfDMXDataAccess()
        {
            string type = typeof(OracleEmcSrdfFullDMXDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OracleEmcSrdfFullDMXDataAccess(CurrentContext);
            }

            return (IOracleEmcSrdfDMXDataAccess)CurrentContext[type];
        }

        #endregion OracleEmcSrdf

        #region OracleLog

        [DebuggerStepThrough]
        public override IOraleLogDataAccess CreateOracleLogDataAccess()
        {
            string type = typeof(OracleLogDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OracleLogDataAccess(CurrentContext);
            }

            return (IOraleLogDataAccess)CurrentContext[type];
        }

        #endregion OracleLog

        #region ScheduleWorkflow

        [DebuggerStepThrough]
        public override IScheduleWorkflowDataAccess CreateWorkflowScheduleDataAccess()
        {
            string type = typeof(ScheduleWorkflowDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ScheduleWorkflowDataAccess(CurrentContext);
            }

            return (IScheduleWorkflowDataAccess)CurrentContext[type];
        }

        #endregion ScheduleWorkflow

        #region SCR

        [DebuggerStepThrough]
        public override ISCRDataAccess CreateSCRDataAccess()
        {
            string type = typeof(SCRDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SCRDataAccess(CurrentContext);
            }

            return (ISCRDataAccess)CurrentContext[type];
        }

        #endregion SCR

        #region Server

        [DebuggerStepThrough]
        public override IServerDataAccess CreateServerDataAccess()
        {
            string type = typeof(ServerDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ServerDataAccess(CurrentContext);
            }

            return (IServerDataAccess)CurrentContext[type];
        }

        #endregion Server

        #region SingleSignOn

        [DebuggerStepThrough]
        public override ISSOConfigurationDataAccess CreateSingleSignOnDataAccess()
        {
            string type = typeof(SingleSignOnDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SingleSignOnDataAccess(CurrentContext);
            }

            return (ISSOConfigurationDataAccess)CurrentContext[type];
        }
        #endregion

        #region Setting

        [DebuggerStepThrough]
        public override ISettingDataAccess CreateSettingDataAccess()
        {
            string type = typeof(SettingDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SettingDataAccess(CurrentContext);
            }

            return (ISettingDataAccess)CurrentContext[type];
        }

        #endregion Site

        #region Site

        [DebuggerStepThrough]
        public override ISiteDataAccess CreateSiteDataAccess()
        {
            string type = typeof(SiteDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SiteDataAccess(CurrentContext);
            }

            return (ISiteDataAccess)CurrentContext[type];
        }

        #endregion Site

        #region SiteInchargeInfo

        [DebuggerStepThrough]
        public override ISiteInchargeInfoDataAccess CreateSiteInchargeInfoDataAccess()
        {
            string type = typeof(SiteInchargeInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SiteInchargeInfoDataAccess(CurrentContext);
            }

            return (ISiteInchargeInfoDataAccess)CurrentContext[type];
        }

        #endregion SiteInchargeInfo

        #region SqlNative

        [DebuggerStepThrough]
        public override ISqlNativeDataAccess CreateSqlNativeDataAccess()
        {
            string type = typeof(SqlNativeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SqlNativeDataAccess(CurrentContext);
            }

            return (ISqlNativeDataAccess)CurrentContext[type];
        }

        #endregion SqlNative

        #region SqlNativeMonitor

        [DebuggerStepThrough]
        public override ISqlNativeMonitorDataAccess CreateSqlNativeMonitorDataAccess()
        {
            string type = typeof(SqlNativeMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SqlNativeMonitorDataAccess(CurrentContext);
            }

            return (ISqlNativeMonitorDataAccess)CurrentContext[type];
        }

        #endregion SqlNativeMonitor

        #region SQLNative2008Monitor

        [DebuggerStepThrough]
        public override ISQLNative2008MonitorDataAccess CreateSQLNative2008MonitorDataAccess()
        {
            string type = typeof(SQLNative2008MonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SQLNative2008MonitorDataAccess(CurrentContext);
            }

            return (ISQLNative2008MonitorDataAccess)CurrentContext[type];
        }

        #endregion SQLNative2008Monitor

        #region SqlNativeHealth

        [DebuggerStepThrough]
        public override ISqlNativeHealthDataAccess CreateSqlNativeHealthDataAccess()
        {
            string type = typeof(SqlNativeHealth).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SqlnativeHealthDataAccess(CurrentContext);
            }

            return (ISqlNativeHealthDataAccess)CurrentContext[type];
        }

        #endregion SqlNativeHealth

        #region SqlNativeHealthPara

        [DebuggerStepThrough]
        public override ISQLNativeHeathParaDataAccess CreateSqlNativeHealthParaDataAccess()
        {
            string type = typeof(SqlNativeHealthParameter).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SqlNativeHealthParaDataAccess(CurrentContext);
            }

            return (ISQLNativeHeathParaDataAccess)CurrentContext[type];
        }

        #endregion SqlNativeHealthPara

        #region SqlNativeServices

        [DebuggerStepThrough]
        public override ISqlNativeServicesDataAccess CreateSqlNativeServicesDataAccess()
        {
            string type = typeof(SqlNativeServices).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SqlNativeServicesDataAccess(CurrentContext);
            }

            return (ISqlNativeServicesDataAccess)CurrentContext[type];
        }

        #endregion SqlNativeServices

        #region SqlNative2008Replication

        [DebuggerStepThrough]
        public override ISQLNative2008ReplicationDataAcess CreateSQLNative2008ReplicationDataAcess()
        {
            string type = typeof(SQLNative2008DataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SQLNative2008DataAccess(CurrentContext);
            }

            return (ISQLNative2008ReplicationDataAcess)CurrentContext[type];
        }

        #endregion SqlNative2008Replication

        #region SnapMirror

        [DebuggerStepThrough]
        public override ISnapMirrorDataAccess CreateSnapMirrorDataAccess()
        {
            string type = typeof(SnapMirrorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SnapMirrorDataAccess(CurrentContext);
            }

            return (ISnapMirrorDataAccess)CurrentContext[type];
        }

        #endregion SnapMirror

        #region SnapMirrorMonitor

        [DebuggerStepThrough]
        public override ISnapMirrorMonitorDataAccess CreateSnapMirrorMonitorDataAccess()
        {
            string type = typeof(SnapMirrorMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SnapMirrorMonitorDataAccess(CurrentContext);
            }

            return (ISnapMirrorMonitorDataAccess)CurrentContext[type];
        }

        #endregion SnapMirrorMonitor

        #region SMSConfiguration

        [DebuggerStepThrough]
        public override ISMSConfigurationDataAccess CreateSMSconfigurationDataAccess()
        {
            string type = typeof(SMSConfigurationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SMSConfigurationDataAccess(CurrentContext);
            }

            return (ISMSConfigurationDataAccess)CurrentContext[type];
        }

        #endregion SMSConfiguration

        #region SmtpConfiguration

        [DebuggerStepThrough]
        public override ISmtpConfigurationDataAccess CreateSmtpconfigurationDataAccess()
        {
            string type = typeof(SmtpConfigurationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SmtpConfigurationDataAccess(CurrentContext);
            }

            return (ISmtpConfigurationDataAccess)CurrentContext[type];
        }

        #endregion SmtpConfiguration

        #region User

        [DebuggerStepThrough]
        public override IUserDataAccess CreateUserDataAccess()
        {
            string type = typeof(UserDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new UserDataAccess(CurrentContext);
            }

            return (IUserDataAccess)CurrentContext[type];
        }

        #endregion User

        #region UserActivity

        [DebuggerStepThrough]
        public override IUserActivityDataAccess CreateUserActivityDataAccess()
        {
            string type = typeof(UserActivityDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new UserActivityDataAccess(CurrentContext);
            }

            return (IUserActivityDataAccess)CurrentContext[type];
        }

        #endregion UserActivity

        #region SMSReport

        [DebuggerStepThrough]
        public override ISMSReportDataAccess CreateSMSReportDataAccess()
        {
            string type = typeof(SMSReportDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SMSReportDataAccess(CurrentContext);
            }

            return (ISMSReportDataAccess)CurrentContext[type];
        }

        #endregion SMSReport

        #region EmailReport

        [DebuggerStepThrough]
        public override IEmailReportDataAccess CreateEmailReportDataAccess()
        {
            string type = typeof(EmailReportDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmailReportDataAccess(CurrentContext);
            }

            return (IEmailReportDataAccess)CurrentContext[type];
        }

        #endregion EmailReport

        #region UserGroup

        [DebuggerStepThrough]
        public override IUserGroupDataAccess CreateUserGroupDataAccess()
        {
            string type = typeof(UserGroupDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new UserGroupDataAccess(CurrentContext);
            }
            return (IUserGroupDataAccess)CurrentContext[type];
        }

        #endregion UserGroup

        #region UserInfraObject

        [DebuggerStepThrough]
        public override IUserInfraObjectDataAccess CreateUserInfraObjectDataAccess()
        {
            string type = typeof(UserInfraObjectDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new UserInfraObjectDataAccess(CurrentContext);
            }
            return (IUserInfraObjectDataAccess)CurrentContext[type];
        }

        #endregion UserInfraObject

        #region UserInfo

        [DebuggerStepThrough]
        public override IUserInfoDataAccess CreateUserInfoDataAccess()
        {
            string type = typeof(UserInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new UserInfoDataAccess(CurrentContext);
            }

            return (IUserInfoDataAccess)CurrentContext[type];
        }

        #endregion UserInfo

        #region VmwareMonitor

        [DebuggerStepThrough]
        public override IVmwareMonitorDataAccess CreateVmwareMonitorDataAccess()
        {
            string type = typeof(VmwareMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VmwareMonitorDataAccess(CurrentContext);
            }

            return (IVmwareMonitorDataAccess)CurrentContext[type];
        }

        #endregion VmwareMonitor

        #region Workflow

        [DebuggerStepThrough]
        public override IWorkflowDataAccess CreateWorkflowDataAccess()
        {
            string type = typeof(WorkflowDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new WorkflowDataAccess(CurrentContext);
            }

            return (IWorkflowDataAccess)CurrentContext[type];
        }

        #endregion Workflow

        #region WorkflowAction

        public override IWorkflowActionDataAccess CreateWorkflowActionDataAccess()
        {
            string type = typeof(WorkflowActionBuilder).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new WorkflowActionDataAccess(CurrentContext);
            }
            return (IWorkflowActionDataAccess)CurrentContext[type];
        }

        #endregion WorkflowAction

        #region Sql200Log

        [DebuggerStepThrough]
        public override ISql2000LogDataAccess CreateSqlLog2000DataAccess()
        {
            string type = typeof(Sql2000LogDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Sql2000LogDataAccess(CurrentContext);
            }

            return (ISql2000LogDataAccess)CurrentContext[type];
        }

        #endregion Sql200Log

        #region Sql2000Health

        [DebuggerStepThrough]
        public override ISql2000HealthDataAccess CreateSql2000HealthDataAccess()
        {
            string type = typeof(Sql2000HealthDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Sql2000HealthDataAccess(CurrentContext);
            }

            return (ISql2000HealthDataAccess)CurrentContext[type];
        }

        #endregion Sql2000Health

        #region Sql2000Service

        [DebuggerStepThrough]
        public override ISql2000ServiceDataAccess CreateSqlService2000DataAccess()
        {
            string type = typeof(Sql2000ServiceDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Sql2000ServiceDataAccess(CurrentContext);
            }

            return (Sql2000ServiceDataAccess)CurrentContext[type];
        }

        #endregion Sql2000Service

        #region HitachiUr

        [DebuggerStepThrough]
        public override IHitachiUrReplicationDataAccess CreateHitachiUrReplicationDataAccess()
        {
            string type = typeof(HitachiUrDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HitachiUrDataAccess(CurrentContext);
            }

            return (IHitachiUrReplicationDataAccess)CurrentContext[type];
        }

        #endregion HitachiUr

        #region HitachiUrMonitoring

        [DebuggerStepThrough]
        public override IHitachiURMonitoringDataAccess CreateHitachiUrMonitoringDataAccess()
        {
            string type = typeof(HitachiURMonitoringStatusDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HitachiURMonitoringStatusDataAccess(CurrentContext);
            }

            return (IHitachiURMonitoringDataAccess)CurrentContext[type];
        }

        #endregion HitachiUrMonitoring

        #region HitachiUrDeviceMonitoring

        [DebuggerStepThrough]
        public override IHitachiURDeviceMonitoringDataAccess CreateHitachiUrDeviceMonitoringDataAccess()
        {
            string type = typeof(HitachiURDeviceMonitoringStatusDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HitachiURDeviceMonitoringStatusDataAccess(CurrentContext);
            }

            return (IHitachiURDeviceMonitoringDataAccess)CurrentContext[type];
        }

        #endregion HitachiUrDeviceMonitoring

        # region HitachiUrLuns

        [DebuggerStepThrough]
        public override IHitachiUrLunsDataAccess CreateHitachiUrLunsDataAccess()
        {
            string type = typeof(HitachiUrLunsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HitachiUrLunsDataAccess(CurrentContext);
            }

            return (HitachiUrLunsDataAccess)CurrentContext[type];
        }

        #endregion

        public override IHADRReplicationDataAccess CreateHADRReplicationDataAccess()
        {
            string type = typeof(HADRReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HADRReplicationDataAccess(CurrentContext);
            }

            return (IHADRReplicationDataAccess)CurrentContext[type];
        }

        public override IHADRDataAccess CreateHadrDataAccess()
        {
            string type = typeof(HADRDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HADRDataAccess(CurrentContext);
            }

            return (IHADRDataAccess)CurrentContext[type];
        }

        #region Postgre9xComponentMonitor
        [DebuggerStepThrough]
        public override IPostgre9xComponentMonitorDataAccess CreatePostgre9xComponentMonitorDataAccess()
        {
            string type = typeof(Postgre9xComponentMonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Postgre9xComponentMonitorDataAccess(CurrentContext);
            }

            return (IPostgre9xComponentMonitorDataAccess)CurrentContext[type];
        }
        #endregion

        #region Postgre9xMonitorStatus
        [DebuggerStepThrough]
        public override IPostgre9xMonitorStatusDataAccess CreatePostgre9xMonitorStatusDataAccess()
        {
            string type = typeof(Postgre9xMonitorStatus).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Postgre9xMonitorStatusDataAccess(CurrentContext);
            }

            return (IPostgre9xMonitorStatusDataAccess)CurrentContext[type];
        }
        #endregion

        #region EC2S3DataSyncReplication

        [DebuggerStepThrough]
        public override IEC2S3DataSyncDataAccess CreateEC2S3DataSyncReplicationDataAcess()
        {
            string type = typeof(EC2S3DataSyncReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EC2S3DataSyncReplicationDataAccess(CurrentContext);
            }

            return (IEC2S3DataSyncDataAccess)CurrentContext[type];
        }

        #endregion EC2S3Replication

        #region VmwareNetSnapMirrorMonitor

        [DebuggerStepThrough]
        public override IVMWareNetSnapMirrorDataAccess CreateVMWareNetSnapMirrorMonitorDataAccess()
        {
            string type = typeof(VMWareNetsnapMirrorMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VMWareNetsnapMirrorMonitorDataAccess(CurrentContext);
            }

            return (IVMWareNetSnapMirrorDataAccess)CurrentContext[type];
        }

        #endregion VmwareNetSnapMirrorMonitor

        #region InfraObjectScheduleWF

        [DebuggerStepThrough]
        public override IInfraobjectScheduleworkFlowDataAccess CreateInfraobjectScheduleworkFlowDataAccess()
        {
            string type = typeof(IInfraobjectScheduleworkFlowDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CP.DataAccess.InfraobjectScheduledWF.InfraObjectScheduleWFDataAccess(CurrentContext);
            }

            return (IInfraobjectScheduleworkFlowDataAccess)CurrentContext[type];
        }

        #endregion InfraObjectScheduleWF

        #region MSSqlDoubletek monitor

        [DebuggerStepThrough]
        public override IMSSqlDoubletekRepliMonitorDataAccess CreateMSSqlDoubletekRepliMoniDataAccess()
        {
            string type = typeof(MSSqlDoubleteRepliMonitorkDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MSSqlDoubleteRepliMonitorkDataAccess(CurrentContext);
            }

            return (IMSSqlDoubletekRepliMonitorDataAccess)CurrentContext[type];
        }

        #endregion MSSqlDoubletek monitor

        #region MSSqlDoubletek Replication

        [DebuggerStepThrough]
        public override IMSSqlDoubleTakeRepliDataAccess CreateMSSqlDoubleTakeRepliDataAccess()
        {
            string type = typeof(MSSqlDoubleTakeRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MSSqlDoubleTakeRepliDataAccess(CurrentContext);
            }

            return (IMSSqlDoubleTakeRepliDataAccess)CurrentContext[type];
        }

        #endregion MSSqlDoubletek Replication

        #region BFBIAMatrix

        [DebuggerStepThrough]
        public override IBFBIAMatrixDataAccess CreateBFBIAMatrixDataAccess()
        {
            string type = typeof(BFBIAMatrixDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BFBIAMatrixDataAccess(CurrentContext);
            }

            return (IBFBIAMatrixDataAccess)CurrentContext[type];
        }

        #endregion BFBIAMatrix

        #region BFBIAMatrixDetails

        [DebuggerStepThrough]
        public override IBFBIAMatrixDetailsDataAccess CreateBFBIAMatrixDetailsDataAccess()
        {
            string type = typeof(BFBIAMatrixDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BFBIAMatrixDetailsDataAccess(CurrentContext);
            }

            return (IBFBIAMatrixDetailsDataAccess)CurrentContext[type];
        }

        #endregion BFBIAMatrixDetails

        #region MssqlDMXEmcsrdfMonitor

        [DebuggerStepThrough]
        public override IMssqlDMXEmcSrdfMonitorDataAccess CreateMssqlDMXEmcSrdfMonitorDataAccess()
        {
            string type = typeof(MssqlDMXEmcSrdfMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MssqlDMXEmcSrdfMonitorDataAccess(CurrentContext);
            }

            return (IMssqlDMXEmcSrdfMonitorDataAccess)CurrentContext[type];
        }

        #endregion MssqlEmcsrdfMonitor

        #region MssqlVMAXEmcsrdfMonitor

        [DebuggerStepThrough]
        public override IMssqlVMAXEmcSrdfMonitorDataAccess CreateMssqlVMAXEmcSrdfMonitorDataAccess()
        {
            string type = typeof(MssqlVMAXEmcSrdfMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MssqlVMAXEmcSrdfMonitorDataAccess(CurrentContext);
            }

            return (IMssqlVMAXEmcSrdfMonitorDataAccess)CurrentContext[type];
        }

        #endregion MssqlEmcsrdfMonitor

        #region SybaseMonitor

        [DebuggerStepThrough]
        public override ISybaseDataAccess CreateSybaseMonitorDataAccess()
        {
            string type = typeof(SyBaseMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SyBaseMonitorDataAccess(CurrentContext);
            }

            return (ISybaseDataAccess)CurrentContext[type];
        }

        #endregion SybaseMonitor

        #region Accessmanager

        [DebuggerStepThrough]
        public override IAccessManagerDataAccess CreateAccessManagerDataAccess()
        {
            string type = typeof(AccessManagerCustomDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AccessManagerCustomDataAccess(CurrentContext);
            }

            return (IAccessManagerDataAccess)CurrentContext[type];
        }

        #endregion Accessmanager

        #region MimixReplication
        [DebuggerStepThrough]
        public override IMimixRepliDataAccess CreateMimixReplicationDataAccess()
        {
            string type = typeof(MimixRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MimixRepliDataAccess(CurrentContext);
            }

            return (IMimixRepliDataAccess)CurrentContext[type];
        }
        #endregion MimixReplication

        #region MimixDatalag
        [DebuggerStepThrough]
        public override IMimixDataLagDataAccess CreateMimixDatalgMonitorDataAccess()
        {
            string type = typeof(MimixDatalagDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MimixDatalagDataAccess(CurrentContext);
            }

            return (IMimixDataLagDataAccess)CurrentContext[type];
        }
        #endregion MimixDatalag

        #region MimixHealth
        [DebuggerStepThrough]
        public override IMimixHealthDataAccess CreateMimixHealthReplicationDataAccess()
        {
            string type = typeof(MimixHealthDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MimixHealthDataAccess(CurrentContext);
            }

            return (IMimixHealthDataAccess)CurrentContext[type];
        }
        #endregion MimixHealth

        #region MimixAlerts
        [DebuggerStepThrough]
        public override IMimixAlertsDataAccess CreateMimixAlertsMonitorDataAccess()
        {
            string type = typeof(MimixAlertsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MimixAlertsDataAccess(CurrentContext);
            }

            return (IMimixAlertsDataAccess)CurrentContext[type];
        }
        #endregion MimixAlerts

        #region MimixManager
        [DebuggerStepThrough]
        public override IMimixManagerDataAccess CreateMimixManagerMonitorDataAccess()
        {
            string type = typeof(MimixManagerDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MimixManagerDataAccess(CurrentContext);
            }

            return (IMimixManagerDataAccess)CurrentContext[type];
        }
        #endregion MimixManager

        #region MimixAvilability
        [DebuggerStepThrough]
        public override IMimixAvilabilityDataAccess CreateMimixAvilabilityMonitorDataAccess()
        {
            string type = typeof(MimixAvilabilityDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MimixAvilabilityDataAccess(CurrentContext);
            }

            return (IMimixAvilabilityDataAccess)CurrentContext[type];
        }
        #endregion MimixAvilability

        #region HyperVRepli

        [DebuggerStepThrough]
        public override IHyperVDataAccess CreateHyperVMonitorDataAccess()
        {
            string type = typeof(HyperVRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HyperVRepliDataAccess(CurrentContext);
            }

            return (IHyperVDataAccess)CurrentContext[type];
        }

        #endregion

        #region HyperVMonitor

        [DebuggerStepThrough]
        public override IHyperVMonitorDataAccess CreateHyperVComponentMonitorDataAccess()
        {
            string type = typeof(HyperVMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HyperVMonitorDataAccess(CurrentContext);
            }

            return (IHyperVMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region HyperVClusterSummaryMonitor

        [DebuggerStepThrough]
        public override IClusterSummaryDataAccess CreateClusterSummaryDataAccess()
        {
            string type = typeof(ClusterSummaryDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ClusterSummaryDataAccess(CurrentContext);
            }

            return (IClusterSummaryDataAccess)CurrentContext[type];
        }

        #endregion

        #region HyperVClusterNodeMonitor

        [DebuggerStepThrough]
        public override IClusterNodeDataAccess CreateClusterNodeDataAccess()
        {
            string type = typeof(ClusterNodeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ClusterNodeDataAccess(CurrentContext);
            }

            return (IClusterNodeDataAccess)CurrentContext[type];
        }

        #endregion

        #region MySqlRepli

        [DebuggerStepThrough]
        public override IMySqlRepliDataAccess CreateMySqlRepliDataAccess()
        {
            string type = typeof(MySqlRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MySqlRepliDataAccess(CurrentContext);
            }

            return (IMySqlRepliDataAccess)CurrentContext[type];
        }

        #endregion

        #region MySqlMonitor

        [DebuggerStepThrough]
        public override IMySqlMonitorDataAccess CreateMySqlMonitorDataAccess()
        {
            string type = typeof(MySqlMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MySqlMonitorDataAccess(CurrentContext);
            }

            return (IMySqlMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region SVCGlobalMirrorORMetroMirror

        [DebuggerStepThrough]
        public override ISVCGlobalMirrorORMetroConfigDataAccess CreateSVCGlobalMirrorOrMetroDataAccess()
        {
            string type = typeof(SVCGlobalMirrorORMetroConfigDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SVCGlobalMirrorORMetroConfigDataAccess(CurrentContext);
            }

            return (ISVCGlobalMirrorORMetroConfigDataAccess)CurrentContext[type];
        }

        #endregion SVCGlobalMirrorORMetroMirror

        #region SVCGlobalMirror

        [DebuggerStepThrough]
        public override ISVCGlobalMirrorMonitorDataAccess CreateSVCGlobalMirrorDataAccess()
        {
            string type = typeof(SVCGlobalMirrorMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SVCGlobalMirrorMonitorDataAccess(CurrentContext);
            }

            return (ISVCGlobalMirrorMonitorDataAccess)CurrentContext[type];
        }

        #endregion SVCGlobalMirror

        #region SVCControllerMonitor

        [DebuggerStepThrough]
        public override ISVCcontrollerMonitorDataAccess CreateSVCControllerMonitorDataAccess()
        {
            string type = typeof(SVCcontrollerMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SVCcontrollerMonitorDataAccess(CurrentContext);
            }

            return (ISVCcontrollerMonitorDataAccess)CurrentContext[type];
        }

        #endregion SVCControllerMonitor

        #region SVC Node Deatiled Monitor

        [DebuggerStepThrough]
        public override ISVCNodeDetailedDataAccess CreateSVCNodeDetailedrMonitorDataAccess()
        {
            string type = typeof(SVCNodeDetailedDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SVCNodeDetailedDataAccess(CurrentContext);
            }

            return (ISVCNodeDetailedDataAccess)CurrentContext[type];
        }

        #endregion SVC Node Deatiled Monitor

        #region SVCReplication

        [DebuggerStepThrough]
        public override ISVCGMReplicationMDataAccess CreateDatabaseSVCGMReplicationMDataAccess()
        {
            string type = typeof(CP.DataAccess.SVCGMReplication.SVCGMReplicationMsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CP.DataAccess.SVCGMReplication.SVCGMReplicationMsDataAccess(CurrentContext);
            }

            return (ISVCGMReplicationMDataAccess)CurrentContext[type];
        }

        #endregion

        #region InfraobjectGlobalMirrorLuns

        public override IInfraobjectGlobalMirrorLunsDataAccess CreateInfraobjectGlobalMirrorlunsDataAccess()
        {
            string type = typeof(InfraobjGlobalMirrorlunsDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraobjGlobalMirrorlunsDataAccess(CurrentContext);
            }

            return (InfraobjGlobalMirrorlunsDataAccess)CurrentContext[type];

        }

        #endregion

        #region InfraObjectGlobalMirrorLunsDetails

        public override IInfraobjectGlobaliMirrorlunsDetailsDataAccess CreateInfraobjectGlobalMirrorlunsDetailsDataAccess()
        {

            string type = typeof(InfraobjGlobalmirrorDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraobjGlobalmirrorDetailsDataAccess(CurrentContext);
            }

            return (InfraobjGlobalmirrorDetailsDataAccess)CurrentContext[type];

        }

        #endregion

        #region Logs
        public override IInfraobjectSchedularLogs CreateInfraobjectSchedularLogsDetailsDataAccess()
        {

            string type = typeof(InfrScheLogsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfrScheLogsDataAccess(CurrentContext);
            }

            return (InfrScheLogsDataAccess)CurrentContext[type];

        }

        #endregion

        #region Status
        public override IInfraobjectSchedularStatus CreateInfraobjectSchedularStatusDetailsDataAccess()
        {

            string type = typeof(InfrScheStatusDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfrScheStatusDataAccess(CurrentContext);
            }

            return (InfrScheStatusDataAccess)CurrentContext[type];

        }

        #endregion

        #region WFDetailsStatus


        public override IInfrascheduleWfDataAccess CreateInfraobjectSchedularWorkflowDetailsDataAccess()
        {

            string type = typeof(InfraScheWFDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraScheWFDataAccess(CurrentContext);
            }

            return (InfraScheWFDataAccess)CurrentContext[type];

        }

        #endregion

        #region MSSQLDBMirrorReplication

        [DebuggerStepThrough]
        public override IMSSQLDBMirrorDataAccess CreateMSSQLDBMirrorDataAccess()
        {
            string type = typeof(MSSqlDBMirrorReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MSSqlDBMirrorReplicationDataAccess(CurrentContext);
            }

            return (IMSSQLDBMirrorDataAccess)CurrentContext[type];
        }

        #endregion MSSQLDBMirrorReplication

        #region MSSQLDBMirrorReplicationMonitor

        [DebuggerStepThrough]
        public override IMSSQLDBMirrorReplicationDataAccess CreateMSSQLDBMirrorReplicationDataAccess()
        {
            string type = typeof(MSSQLDBMirrorReplMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MSSQLDBMirrorReplMonitorDataAccess(CurrentContext);
            }

            return (IMSSQLDBMirrorReplicationDataAccess)CurrentContext[type];
        }

        #endregion MSSQLDBMirrorReplicationMonitor

        #region VmWarePathDetails

        public override IVmWarePathDetailDataAccess CreateVmWarePathDetailsDataAccess()
        {
            string type = typeof(VmWarePathDetailDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VmWarePathDetailDataAccess(CurrentContext);
            }
            return (VmWarePathDetailDataAccess)CurrentContext[type];
        }

        #endregion

        #region XIVMirrorMonitoring

        [DebuggerStepThrough]
        public override IXIVMirrorStatisticsDataAccess CreateXIVMirrorStatisticsDataAccess()
        {
            string type = typeof(XIVMIrrorStatisticsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new XIVMIrrorStatisticsDataAccess(CurrentContext);
            }

            return (IXIVMirrorStatisticsDataAccess)CurrentContext[type];
        }

        #endregion XIVMirrorMonitoring

        #region IBMXIVMirror

        [DebuggerStepThrough]
        public override IIBMXIVConfigurationDataAccess CreateIBMXIVConfigurationDataAccess()
        {
            string type = typeof(IBMXIVMirrorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new IBMXIVMirrorDataAccess(CurrentContext);
            }

            return (IIBMXIVConfigurationDataAccess)CurrentContext[type];
        }

        #endregion IBMXIVMirror

        #region CGMonitoring

        [DebuggerStepThrough]
        public override ICGMonitoringDataAccess CreateCGMonitoringDataAccess()
        {
            string type = typeof(CGMonitoringDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CGMonitoringDataAccess(CurrentContext);
            }

            return (ICGMonitoringDataAccess)CurrentContext[type];
        }

        #endregion CGMonitoring

        #region ClustoreMonitoring

        [DebuggerStepThrough]
        public override IClustorMonitoringDataAccess CreateClustorMonitoringDataAccess()
        {
            string type = typeof(ClustorMonitoringDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ClustorMonitoringDataAccess(CurrentContext);
            }

            return (IClustorMonitoringDataAccess)CurrentContext[type];
        }

        #endregion ClustoreMonitoring

        #region CGVolumeMonitoring

        [DebuggerStepThrough]
        public override ICGVolumeMontoringDataAccess CreateCGVolumeMontoringDataAccess()
        {
            string type = typeof(CGVolumeMonitoringDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CGVolumeMonitoringDataAccess(CurrentContext);
            }

            return (ICGVolumeMontoringDataAccess)CurrentContext[type];
        }

        #endregion CGVolumeMonitoring

        #region IBMXIVReplicationMonitor

        [DebuggerStepThrough]
        public override IXIVReplicationMonitorDataAccess CreateXIVReplicationMonitorDataAccess()
        {
            string type = typeof(IBMXIVRepliMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new IBMXIVRepliMonitorDataAccess(CurrentContext);
            }

            return (IXIVReplicationMonitorDataAccess)CurrentContext[type];
        }

        #endregion IBMXIVReplicationMonitor

        #region XIVMirror

        public override IXIVMirrorDataAccess CreateXIVMirrorDataAccess()
        {
            string type = typeof(XIVMirrorDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new XIVMirrorDataAccess(CurrentContext);
            }

            return (IXIVMirrorDataAccess)CurrentContext[type];

        }

        #endregion

        #region InfraobjectVolumeName

        public override IInfraobjectVolumeDataAccess CreateInfraobjectVolumeDetailsDataAccess()
        {
            string type = typeof(InfraobjectVolumeNameDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new InfraobjectVolumeNameDataAccess(CurrentContext);
            }

            return (IInfraobjectVolumeDataAccess)CurrentContext[type];

        }

        #endregion

        #region SRMVmwareMonitor

        [DebuggerStepThrough]
        public override ISRMVmwareMonitorDataAccess CreateSRMVmwareMonitorDataAccess()
        {
            string type = typeof(SRMVmwareMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SRMVmwareMonitorDataAccess(CurrentContext);
            }

            return (ISRMVmwareMonitorDataAccess)CurrentContext[type];
        }

        #endregion SRMVmwareMonitor

        #region ODGMonitor
        public override IActiveODGMonitorDataAccess CreateODGMonitorDataAccess()
        {
            string type = typeof(ActiveODGMonitorDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ActiveODGMonitorDataAccess(CurrentContext);
            }
            return (ActiveODGMonitorDataAccess)CurrentContext[type];
        }
        #endregion ODGMonitor

        #region VCenterMonitorProfile

        public override IVCenterMonitorProfileDataAccess CreateVCenterMonitorProfileDataAccess()
        {
            string type = typeof(VCenterMonitorProfileDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VCenterMonitorProfileDataAccess(CurrentContext);
            }

            return (VCenterMonitorProfileDataAccess)CurrentContext[type];
        }

        #endregion  VCenterMonitorProfile

        #region ImportCMDB

        public override IImportCMDBDataAccess CreateImportCMDBDataAccess()
        {
            string type = typeof(ImportCMDBDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ImportCMDBDataAccess(CurrentContext);
            }

            return (ImportCMDBDataAccess)CurrentContext[type];
        }

        #endregion

        #region DefaultMoniService

        public override IDefaultMonitorServices CreateDefaultMonitorServicesDataAccess()
        {
            string type = typeof(DefaultMoniServiceDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DefaultMoniServiceDataAccess(CurrentContext);
            }

            return (DefaultMoniServiceDataAccess)CurrentContext[type];
        }

        #endregion

        #region SybaseWithSRS

        [DebuggerStepThrough]
        public override ISybaseWithSRSRepliDataAccess CreateSybaseWithSRSDataAccess()
        {
            string type = typeof(SybaseWithSRSDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SybaseWithSRSDataAccess(CurrentContext);
            }

            return (ISybaseWithSRSRepliDataAccess)CurrentContext[type];
        }

        #endregion DataGuard

        #region SybaseWithSRSMonitor

        [DebuggerStepThrough]
        public override ISybaseWithSRSDataAccess CreateSybaseWithSRSMonitorDataAccess()
        {
            string type = typeof(SybaseWithSRSMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SybaseWithSRSMonitorDataAccess(CurrentContext);
            }

            return (ISybaseWithSRSDataAccess)CurrentContext[type];
        }

        #endregion SybaseMonitor

        #region mysqlFullDBEmcsrdf
        [DebuggerStepThrough]
        public override IMysqlFullDBMonitorDataAccess CreateMyslFullDbDataAccess()
        {
            string type = typeof(MysqlFullDBDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MysqlFullDBDataAccess(CurrentContext);
            }

            return (IMysqlFullDBMonitorDataAccess)CurrentContext[type];
        }
        #endregion

        #region eBDRProfile

        public override IeBDRProfileDataAccess CreateeBDRProfileDataAccess()
        {
            string type = typeof(EBDRReplicationDataAcess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EBDRReplicationDataAcess(CurrentContext);
            }

            return (EBDRReplicationDataAcess)CurrentContext[type];
        }
        #endregion eBDRProfile

        #region eBDRProfile

        public override IeBDRProfileReplication CreateeBDRProfileReplicationDataAccess()
        {
            string type = typeof(eBDRProfileRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new eBDRProfileRepliDataAccess(CurrentContext);
            }

            return (eBDRProfileRepliDataAccess)CurrentContext[type];
        }

        #endregion eBDRProfile

        #region eBDRProfile

        public override IeBDRProfileRepli CreateeBDRProfileRepliDataAccess()
        {
            string type = typeof(eBDRProfileRepliNewDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new eBDRProfileRepliNewDataAccess(CurrentContext);
            }

            return (eBDRProfileRepliNewDataAccess)CurrentContext[type];
        }

        #endregion eBDRProfile

        #region Base24Replication

        [DebuggerStepThrough]
        public override IBase24RepliDataAccess CreateBase24ReplicationDataAccess()
        {
            string type = typeof(IBase24RepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Base24RepliDataAccess(CurrentContext);
            }

            return (IBase24RepliDataAccess)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IBase24RepliMonitorDataAccess CreateBase24RepliMonitorDataAccess()
        {
            string type = typeof(IBase24RepliMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Base24RepliMonitorDataAccess(CurrentContext);
            }

            return (IBase24RepliMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region TPRCReplication

        [DebuggerStepThrough]
        public override ITPRCReplicationDataAccess CreateTPRCReplicationDataAccess()
        {
            string type = typeof(TPRCReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new TPRCReplicationDataAccess(CurrentContext);
            }

            return (ITPRCReplicationDataAccess)CurrentContext[type];
        }

        #endregion TPRCReplication

        #region TPRCMonitor

        [DebuggerStepThrough]
        public override ITPRCMonitorDataAccess CreateTPRCMonitorDataAccess()
        {
            string type = typeof(TPRCMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new TPRCMonitorDataAccess(CurrentContext);
            }

            return (ITPRCMonitorDataAccess)CurrentContext[type];
        }

        #endregion SnapMirrorMonitor

        #region ComponentFailureDaily

        [DebuggerStepThrough]
        public override IComponentFailureDailyDataAccess CreateComponentFailureDailyDataAccess()
        {
            string type = typeof(ComponentFailureDailyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ComponentFailureDailyDataAccess(CurrentContext);
            }

            return (IComponentFailureDailyDataAccess)CurrentContext[type];
        }

        #endregion

        #region ServiceRTODaily
        [DebuggerStepThrough]
        public override IServiceRTODailyDataAccess CreateServiceRTODailyDataAccess()
        {
            string type = typeof(ServiceRTODailyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ServiceRTODailyDataAccess(CurrentContext);
            }

            return (IServiceRTODailyDataAccess)CurrentContext[type];
        }

        #endregion

        #region BSDRReadyDaily

        [DebuggerStepThrough]
        public override IBSDRReadyDaily CreateBSDRReadyDailyDataAccess()
        {
            string type = typeof(BSDRReadyDailyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new BSDRReadyDailyDataAccess(CurrentContext);
            }

            return (IBSDRReadyDaily)CurrentContext[type];
        }
        #endregion

        #region SubstituteAuthentication
        [DebuggerStepThrough]
        public override ISubstituteAuthenticateDataAccess CreateSubstituteAuthenticateDataAccess()
        {
            string type = typeof(Substitute_Authentication).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SubstituteAuthenticateDataAccess(CurrentContext);
            }

            return (ISubstituteAuthenticateDataAccess)CurrentContext[type];
        }
        #endregion

        #region MSSQLAlwaysOnReplication

        [DebuggerStepThrough]
        public override IMSSQLAlwaysOnReplicationDataAcess CreateMSSQLAlwaysOnReplicationDataAcess()
        {
            string type = typeof(MSSQLAlwaysOnDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MSSQLAlwaysOnDataAccess(CurrentContext);
            }

            return (IMSSQLAlwaysOnReplicationDataAcess)CurrentContext[type];
        }

        #endregion MSSQLAlwaysOnReplication

        #region MSSQLAlwaysOnMonitor

        [DebuggerStepThrough]
        public override IMSSQL2014ServerDataAccess CreateMSSQL2014ServerMonitorDataAccess()
        {
            string type = typeof(MSSQLServer2014MonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MSSQLServer2014MonitorDataAccess(CurrentContext);
            }

            return (MSSQLServer2014MonitorDataAccess)CurrentContext[type];
        }
        #endregion

        #region ServiceProfile
        public override IServiceProfile CreateServiceProfileDataAccess()
        {

            string type = typeof(ServiceProfileDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ServiceProfileDataAccess(CurrentContext);
            }

            return (ServiceProfileDataAccess)CurrentContext[type];

        }
        public override IUserServiceDataAccess CreateUserServiceProfileDataAccess()
        {

            string type = typeof(UserServiceProfileDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new UserServiceProfileDataAccess(CurrentContext);
            }

            return (UserServiceProfileDataAccess)CurrentContext[type];

        }
        #endregion ServiceProfile

        #region HP3PARMonitor

        [DebuggerStepThrough]
        public override IHP3PAR_MonitorDataAccess CreateHP3PARMonitorDataAccess()
        {
            string type = typeof(HP3PARMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HP3PARMonitorDataAccess(CurrentContext);
            }

            return (IHP3PAR_MonitorDataAccess)CurrentContext[type];
        }
        #endregion HP3PARMonitor

        #region AodgLogRepliDetails
        public override IAodgLogRepliDetailsDataAccess CreateAodgLogRepliDetailsDataAccess()
        {
            string type = typeof(AodgRepliLogDetailsDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AodgRepliLogDetailsDataAccess(CurrentContext);
            }
            return (AodgRepliLogDetailsDataAccess)CurrentContext[type];
        }
        #endregion ODGMonitor

        #region VVRReplication

        [DebuggerStepThrough]
        public override IVVRReplicationDataAccess CreateVVRReplicationDataAccess()
        {
            string type = typeof(VVRReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VVRReplicationDataAccess(CurrentContext);
            }

            return (IVVRReplicationDataAccess)CurrentContext[type];
        }

        #endregion VVRReplication

        #region RVG Monitoring Summary

        [DebuggerStepThrough]
        public override IReplicatedGroupNameDataAccess CreateReplicatedGroupNameDataAccess()
        {
            string type = typeof(ReplicatedGroupMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ReplicatedGroupMonitorDataAccess(CurrentContext);
            }

            return (IReplicatedGroupNameDataAccess)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IRlinkMonitorSecUpdateDataAccess CreateRlinkMonitorSecUpdateDataAccess()
        {
            string type = typeof(RLinkMonitorSecUpdateDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RLinkMonitorSecUpdateDataAccess(CurrentContext);
            }

            return (IRlinkMonitorSecUpdateDataAccess)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override IRlinkMonitorRepliPerformDataAccess CreateRlinkMonitorRepliPerformDataAccess()
        {
            string type = typeof(RLinkMonitorRepliPerDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RLinkMonitorRepliPerDataAccess(CurrentContext);
            }

            return (IRlinkMonitorRepliPerformDataAccess)CurrentContext[type];
        }

        //[DebuggerStepThrough]
        //public override IRlinkMonitorSummaryDataAccess CreateRlinkMonitorSummaryDataAccess()
        //{
        //    string type = typeof(RLinkMonitorSumaryDataAccess).ToString();

        //    if (!CurrentContext.Contains(type))
        //    {
        //        CurrentContext[type] = new RLinkMonitorSumaryDataAccess(CurrentContext);
        //    }

        //    return (IRlinkMonitorSummaryDataAccess)CurrentContext[type];
        //}

        #endregion

        #region VeritasCluster

        public override IVeritasClusterDataAccess CreateActionVeritasClusterDataAccess()
        {
            string type = typeof(VeritasClusterDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VeritasClusterDataAccess(CurrentContext);
            }

            return (IVeritasClusterDataAccess)CurrentContext[type];
        }




        #region VeritasClusterMonitoring

        public override IVeritasClusterMonitoringDataAccess CreateActionVeritasClusterMonitorDataAccess()
        {
            string type = typeof(VeritasClusterMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VeritasClusterMonitorDataAccess(CurrentContext);
            }

            return (IVeritasClusterMonitoringDataAccess)CurrentContext[type];
        }
        #endregion

        #region EMCSRDFSG

        [DebuggerStepThrough]
        public override IEMCSRDFSGDataAccess CreateEMCSRDFSGDataAccess()
        {
            string type = typeof(EMCSRDFSGDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCSRDFSGDataAccess(CurrentContext);
            }

            return (IEMCSRDFSGDataAccess)CurrentContext[type];
        }

        #endregion









        #endregion

        #region EMCSRDFStar

        [DebuggerStepThrough]
        public override IEMCSRDFStarDataAccess CreateEMCSRDFStarDataAccess()
        {
            string type = typeof(EMCSRDFStarDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCSRDFStarDataAccess(CurrentContext);
            }

            return (IEMCSRDFStarDataAccess)CurrentContext[type];
        }

        #endregion

        #region EmcsrdfStarMonitor

        [DebuggerStepThrough]
        public override IEMCSRDFStarMonitorDataAccess CreateEMCSRDFStarMonitorDataAccess()
        {
            string type = typeof(EMCSRDFStarMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCSRDFStarMonitorDataAccess(CurrentContext);
            }

            return (IEMCSRDFStarMonitorDataAccess)CurrentContext[type];
        }

        #endregion EmcsrdfStar

        #region EmcsrdfSGLogs

        [DebuggerStepThrough]
        public override IEmcsrdfSGLogsDataAccess CreateEmcsrdfSGLogsDataAccess()
        {
            string type = typeof(EmcsrdfSGLogsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcsrdfSGLogsDataAccess(CurrentContext);

            }

            return (IEmcsrdfSGLogsDataAccess)CurrentContext[type];
        }

        #endregion

        #region MongoDB

        [DebuggerStepThrough]
        public override IMongoDataBaseDataAccess CreateMongoDatabaseDataAccess()
        {
            string type = typeof(MongoDataBaseDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MongoDataBaseDataAccess(CurrentContext);
            }

            return (IMongoDataBaseDataAccess)CurrentContext[type];
        }

        #endregion MongoDB

        #region ZFSReplication

        [DebuggerStepThrough]
        public override IZFSStorageReplicationDataAccess CreateZFSReplicationDataAccess()
        {
            string type = typeof(ZFSStorageReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ZFSStorageReplicationDataAccess(CurrentContext);
            }

            return (IZFSStorageReplicationDataAccess)CurrentContext[type];
        }

        #endregion ZFSReplication

        #region ZFSReplicationMonitorLogs

        [DebuggerStepThrough]
        public override IZFSReplicationMonitorLogsDataAccess CreateZFSReplicationMonitorLogsDataAccess()
        {
            string type = typeof(ZFSReplicationMonitorLogsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ZFSReplicationMonitorLogsDataAccess(CurrentContext);

            }

            return (IZFSReplicationMonitorLogsDataAccess)CurrentContext[type];
        }
        #endregion

        #region MaxEmcSrdfMonitor

        [DebuggerStepThrough]
        public override IMaxFullDBEmcSrdfMonitorDataAccess CreateMaxFullDBEmcSrdfMonitorDataAccess()
        {
            string type = typeof(MaxFullDBEmcSrdfMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MaxFullDBEmcSrdfMonitorDataAccess(CurrentContext);
            }

            return (IMaxFullDBEmcSrdfMonitorDataAccess)CurrentContext[type];
        }

        #endregion MaxEmcSrdfMonitor

        #region EmcISilonReplication

        [DebuggerStepThrough]
        public override IEmcISilonReplicationDataAccess CreateEmcISilonReplicationBaseDataAccess()
        {
            string type = typeof(EmcIsilonReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcIsilonReplicationDataAccess(CurrentContext);
            }

            return (IEmcISilonReplicationDataAccess)CurrentContext[type];
        }

        #endregion EmcISilonReplication

        #region EmcISilonReplicationPolicy

        [DebuggerStepThrough]
        public override IEmcISilonRepliPolicyDataAccess CreateEmcISilonReplicationPolicyDataAccess()
        {
            string type = typeof(EmcIsilonRepliPolicyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcIsilonRepliPolicyDataAccess(CurrentContext);
            }

            return (IEmcISilonRepliPolicyDataAccess)CurrentContext[type];
        }

        #endregion EmcISilonReplicationPolicy

        #region EmcISilonMonitor

        [DebuggerStepThrough]
        public override IEmcISilon_MonitorDataAccess CreateEmcISilonMonitorDataAccess()
        {
            string type = typeof(EmcISilonDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcISilonDataAccess(CurrentContext);
            }

            return (IEmcISilon_MonitorDataAccess)CurrentContext[type];
        }
        #endregion EmcISilonMonitor

        #region EmcMirrorViewReplication
        [DebuggerStepThrough]
        public override IEmcMirrorViewRepliDataAccess CreateEmcMirrorViewReplicationDataAccess()
        {
            string type = typeof(EmcMirrorViewRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcMirrorViewRepliDataAccess(CurrentContext);
            }

            return (IEmcMirrorViewRepliDataAccess)CurrentContext[type];
        }
        #endregion EmcMirrorViewReplication

        #region EmcMirrorViewRepliMonitoring
        [DebuggerStepThrough]
        public override IEmcMirrorViewRepliMonitorDataAccess CreateEmcMirrorViewRepliMonitorDataAccess()
        {
            string type = typeof(EmcMirrorViewRepliMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcMirrorViewRepliMonitorDataAccess(CurrentContext);
            }

            return (IEmcMirrorViewRepliMonitorDataAccess)CurrentContext[type];
        }
        #endregion EmcMirrorViewRepliMonitoring

        #region Emc_MV_MirrorViewRepliMonitoring
        [DebuggerStepThrough]
        public override IEmc_MV_Mirror_MonitorDataAccess CreateEmc_MV_Mirror_MonitorDataAccess()
        {
            string type = typeof(Emc_MV_MirrorMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new Emc_MV_MirrorMonitorDataAccess(CurrentContext);
            }

            return (IEmc_MV_Mirror_MonitorDataAccess)CurrentContext[type];
        }
        #endregion Emc_MV_MirrorViewRepliMonitoring

        #region HACMPCluster

        [DebuggerStepThrough]
        public override IHACMPClusterDataAccess CreateHACMPClusterDataAccess()
        {
            string type = typeof(HACMPClusterDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HACMPClusterDataAccess(CurrentContext);
            }

            return (IHACMPClusterDataAccess)CurrentContext[type];
        }

        #endregion HACMPCluster

        #region HACMPClusterDetailsMonitor

        [DebuggerStepThrough]
        public override IHACMPClusterDetailsMonitorDataAccess CreateHACMPClusterDetailsDataAccess()
        {
            string type = typeof(HACMPClusterDetailsMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HACMPClusterDetailsMonitorDataAccess(CurrentContext);

            }

            return (IHACMPClusterDetailsMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region HACMPResourceGroupsMonitor

        [DebuggerStepThrough]
        public override IHACMPResourceGroupsMonitorDataAccess CreateHACMPResourceGroupsMonitorDataAccess()
        {
            string type = typeof(HACMPResourceGroupsMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HACMPResourceGroupsMonitorDataAccess(CurrentContext);

            }

            return (IHACMPResourceGroupsMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region RoboCopy

        [DebuggerStepThrough]
        public override IRoboCopyDataAccess CreateRoboCopyDataAccess()
        {
            string type = typeof(RoboCopyDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RoboCopyDataAccess(CurrentContext);

            }

            return (IRoboCopyDataAccess)CurrentContext[type];
        }

        #endregion

        #region RoboCopyJob

        [DebuggerStepThrough]
        public override IRoboCopyJobDataAccess CreateRoboCopyJobDataAccess()
        {
            string type = typeof(RoboCopyJobDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RoboCopyJobDataAccess(CurrentContext);

            }

            return (IRoboCopyJobDataAccess)CurrentContext[type];
        }

        #endregion

        #region RoboCopyOptions

        [DebuggerStepThrough]
        public override IRoboCopyOptionsDataAccess CreateRoboCopyOptionsDataAccess()
        {
            string type = typeof(RoboCopyOptionsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RoboCopyOptionsDataAccess(CurrentContext);

            }

            return (IRoboCopyOptionsDataAccess)CurrentContext[type];
        }

        #endregion

        #region RoboCopyLogs

        [DebuggerStepThrough]
        public override IRoboCopyLogsDataAccess CreateRoboCopyLogsDataAccess()
        {
            string type = typeof(RoboCopyLogsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RoboCopyLogsDataAccess(CurrentContext);

            }

            return (IRoboCopyLogsDataAccess)CurrentContext[type];
        }

        #endregion

        #region RecoveryPointMulti

        [DebuggerStepThrough]
        public override IRecoveryPointMultiDataAccess CreateRecoveryPointMultiDataAccess()
        {
            string type = typeof(RecoveryPointMultiDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RecoveryPointMultiDataAccess(CurrentContext);
            }

            return (IRecoveryPointMultiDataAccess)CurrentContext[type];
        }

        #endregion RecoveryPointMulti

        #region DatabaseSybaseWithRsHADR

        public override IDatabaseSyBaseWithRsHadrDataAccess CreateDatabaseSybaseWithRsHadrDataAccess()
        {
            string type = typeof(DataBaseSybaseWithRsHadrDBDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DataBaseSybaseWithRsHadrDBDataAccess(CurrentContext);
            }

            return (IDatabaseSyBaseWithRsHadrDataAccess)CurrentContext[type];
        }

        #endregion

        #region SybaseWithRSHADRReplication

        [DebuggerStepThrough]
        public override ISybaseWithRSHADRDataAccess CreateSybaseWithRSHADRDataAccess()
        {
            string type = typeof(SybaseWithRSHADRDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SybaseWithRSHADRDataAccess(CurrentContext);
            }

            return (ISybaseWithRSHADRDataAccess)CurrentContext[type];
        }

        #endregion SybaseWithRSHADRReplication

        #region SybaseWithRSHADRDBMonitor

        [DebuggerStepThrough]
        public override ISybaseWithRSHADR_DBMonitorDataAccess CreateSybaseRsHadrDBMonitorDataAccess()
        {
            string type = typeof(SybaseWithRSHadrDBMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SybaseWithRSHadrDBMonitorDataAccess(CurrentContext);
            }

            return (ISybaseWithRSHADR_DBMonitorDataAccess)CurrentContext[type];
        }

        #endregion SybaseWithRSHADRDBMonitor

        #region SybaseWithRSHADRRepliMonitor

        [DebuggerStepThrough]
        public override ISybaseWithRSHADR_RepliMonitorDataAccess CreateSybaseRsHadrRepliMonitorDataAccess()
        {
            string type = typeof(SybaseWithRSHadrRepliMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SybaseWithRSHadrRepliMonitorDataAccess(CurrentContext);
            }

            return (ISybaseWithRSHADR_RepliMonitorDataAccess)CurrentContext[type];
        }

        #endregion SybaseWithRSHADRRepliMonitor

        #region SybaseWithRSHADRRepliMonitorNew

        [DebuggerStepThrough]
        public override ISybaseWithRSHADR_RepliMonitorDataAccessNew CreateSybaseRsHadrRepliMonitorDataAccessNew()
        {
            string type = typeof(SybaseWithRSHadrRepliMonitorDataAccessNew).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new SybaseWithRSHadrRepliMonitorDataAccessNew(CurrentContext);
            }

            return (ISybaseWithRSHADR_RepliMonitorDataAccessNew)CurrentContext[type];
        }

        #endregion SybaseWithRSHADRRepliMonitorNew

        #region EmcUnityReplication

        [DebuggerStepThrough]
        public override IEmcUnityRepliDataAccess CreateEmcUnityReplicationDataAccess()
        {
            string type = typeof(EmcUnityRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcUnityRepliDataAccess(CurrentContext);
            }

            return (IEmcUnityRepliDataAccess)CurrentContext[type];
        }
        #endregion EmcUnityReplication

        #region EmcUnityRepliMonitoring
        [DebuggerStepThrough]
        public override IEmcUnityRepli_MonitorDataAccess CreateEmcUnityRepliMonitorDataAccess()
        {
            string type = typeof(EmcUnityRepliMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EmcUnityRepliMonitorDataAccess(CurrentContext);
            }

            return (IEmcUnityRepli_MonitorDataAccess)CurrentContext[type];
        }
        #endregion EmcUnityRepliMonitoring

        #region MongoDBDMonitorStatus

        [DebuggerStepThrough]
        public override IMongoDBDMonitorStatusDataAccess CreateMongoDBDMonitorStatusDataAccess()
        {
            string type = typeof(MongoDBDMonitorStatusDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MongoDBDMonitorStatusDataAccess(CurrentContext);

            }

            return (IMongoDBDMonitorStatusDataAccess)CurrentContext[type];
        }
        #endregion

        #region DatabaseHanaDb

        [DebuggerStepThrough]
        public override IDatabaseHanaDbDataAccess CreateDatabaseHanaDBDataAccess()
        {
            string type = typeof(DatabaseHanaDbDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseHanaDbDataAccess(CurrentContext);
            }

            return (IDatabaseHanaDbDataAccess)CurrentContext[type];
        }

        #endregion DatabaseHanaDb

        #region HanaDbMonitor

        [DebuggerStepThrough]
        public override IHanaDbMonitorDataAccess CreateHanaDbMonitorDataAccess()
        {
            string type = typeof(HanaDbMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HanaDbMonitorDataAccess(CurrentContext);
            }

            return (IHanaDbMonitorDataAccess)CurrentContext[type];
        }

        #endregion HanaDbMonitor

        #region HanaDbService

        [DebuggerStepThrough]
        public override IHanaDbDatabaseServiceDataAccess CreateHanaDbServiceDataAccess()
        {
            string type = typeof(HanaDbDatabaseServiceDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HanaDbDatabaseServiceDataAccess(CurrentContext);
            }

            return (IHanaDbDatabaseServiceDataAccess)CurrentContext[type];
        }

        #endregion HanaDbService

        #region HanaDbReplicationMode

        [DebuggerStepThrough]
        public override IHanaDbReplicationModeDataAccess CreateHanaDbRepliModeDataAccess()
        {
            string type = typeof(HanaDbReplicationModeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HanaDbReplicationModeDataAccess(CurrentContext);
            }

            return (IHanaDbReplicationModeDataAccess)CurrentContext[type];
        }

        #endregion HanaDbReplicationMode

        #region HanaDbSystemOperationMode

        [DebuggerStepThrough]
        public override IHanaDbSystemOperationModeDataAccess CreateHanaDbOperationModeDataAccess()
        {
            string type = typeof(HanaDbSystemOperationModeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HanaDbSystemOperationModeDataAccess(CurrentContext);
            }

            return (IHanaDbSystemOperationModeDataAccess)CurrentContext[type];
        }

        #endregion HanaDbSystemOperationMode

        #region GoldenGateReplication

        [DebuggerStepThrough]
        public override IGoldenGateRepliDataAccess CreateGoldenGateReplicationDataAccess()
        {
            string type = typeof(GoldenGateRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GoldenGateRepliDataAccess(CurrentContext);
            }

            return (IGoldenGateRepliDataAccess)CurrentContext[type];
        }
        #endregion GoldenGateReplication

        #region GoldenGateDBMonitor

        [DebuggerStepThrough]
        public override IGoldenGateDBMonitorDataAccess CreateGoldenGateDBMonitorDataAccess()
        {
            string type = typeof(GoldenGateMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GoldenGateMonitorDataAccess(CurrentContext);
            }

            return (IGoldenGateDBMonitorDataAccess)CurrentContext[type];
        }
        #endregion GoldenGateDBMonitor

        #region GoldenGateRepliMonitor

        [DebuggerStepThrough]
        public override IGoldenGateRepliMonitorDataAccesscs CreateGoldenGateRepliMonitorDataAccess()
        {
            string type = typeof(GoldenGateRepliMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GoldenGateRepliMonitorDataAccess(CurrentContext);
            }

            return (IGoldenGateRepliMonitorDataAccesscs)CurrentContext[type];
        }
        #endregion GoldenGateRepliMonitor

        #region GoldenGateGroupDet

        [DebuggerStepThrough]
        public override IGoldenGateGroupDetailsDataAccesscs CreateGoldenGateGroupDetMonitorDataAccess()
        {
            string type = typeof(GoldenGateGroupDetDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new GoldenGateGroupDetDataAccess(CurrentContext);
            }

            return (IGoldenGateGroupDetailsDataAccesscs)CurrentContext[type];
        }
        #endregion GoldenGateGroupDet

        //#region RSync

        //[DebuggerStepThrough]
        //public override IRSyncDataAccess CreateRSyncDataAccess()
        //{
        //    string type = typeof(RSyncDataAccess).ToString();

        //    if (!CurrentContext.Contains(type))
        //    {
        //        CurrentContext[type] = new RSyncDataAccess(CurrentContext);

        //    }

        //    return (IRSyncDataAccess)CurrentContext[type];
        //}

        //#endregion

        //#region RSyncJob

        //[DebuggerStepThrough]
        //public override IRSyncJobDataAccess CreateRSyncJobDataAccess()
        //{
        //    string type = typeof(RSyncJobDataAccess).ToString();

        //    if (!CurrentContext.Contains(type))
        //    {
        //        CurrentContext[type] = new RSyncJobDataAccess(CurrentContext);

        //    }

        //    return (IRSyncJobDataAccess)CurrentContext[type];
        //}

        //#endregion

        //#region RSyncOptions

        //[DebuggerStepThrough]
        //public override IRSyncOptionsDataAccess CreateRSyncOptionsDataAccess()
        //{
        //    string type = typeof(RSyncOptionsDataAccess).ToString();

        //    if (!CurrentContext.Contains(type))
        //    {
        //        CurrentContext[type] = new RSyncOptionsDataAccess(CurrentContext);

        //    }

        //    return (IRSyncOptionsDataAccess)CurrentContext[type];
        //}

        //#endregion

        //#region RSyncMonitor

        //[DebuggerStepThrough]
        //public override IRSyncMonitorDataAccess CreateRSyncMonitorDataAccess()
        //{
        //    string type = typeof(RSyncMonitorDataAccess).ToString();

        //    if (!CurrentContext.Contains(type))
        //    {
        //        CurrentContext[type] = new RSyncMonitorDataAccess(CurrentContext);

        //    }

        //    return (IRSyncMonitorDataAccess)CurrentContext[type];
        //}

        //#endregion

        #region VeeamReplication

        [DebuggerStepThrough]
        public override IVeeamRepliDataAccess CreateVeeamReplicationDataAccess()
        {
            string type = typeof(VeeamRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VeeamRepliDataAccess(CurrentContext);
            }

            return (IVeeamRepliDataAccess)CurrentContext[type];
        }
        #endregion VeeamReplication

        #region VeeamMonitor

        [DebuggerStepThrough]
        public override IVeeamMonitorDataAccess CreateVeeamMonitorDataAccess()
        {
            string type = typeof(VeeamMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VeeamMonitorDataAccess(CurrentContext);
            }

            return (IVeeamMonitorDataAccess)CurrentContext[type];
        }
        #endregion VeeamMonitor

        #region VeeamVMJobs

        [DebuggerStepThrough]
        public override IVeeamVMJobDataAccess CreateVeeamVMJobDataAccess()
        {
            string type = typeof(VeeamVMJobDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VeeamVMJobDataAccess(CurrentContext);
            }

            return (IVeeamVMJobDataAccess)CurrentContext[type];
        }
        #endregion VeeamVMJobs

        #region RSync

        [DebuggerStepThrough]
        public override IRSyncDataAccess CreateRSyncDataAccess()
        {
            string type = typeof(RSyncDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RSyncDataAccess(CurrentContext);

            }

            return (IRSyncDataAccess)CurrentContext[type];
        }

        #endregion

        #region RSyncJob

        [DebuggerStepThrough]
        public override IRSyncJobDataAccess CreateRSyncJobDataAccess()
        {
            string type = typeof(RSyncJobDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RSyncJobDataAccess(CurrentContext);

            }

            return (IRSyncJobDataAccess)CurrentContext[type];
        }

        #endregion

        #region RSyncOptions

        [DebuggerStepThrough]
        public override IRSyncOptionsDataAccess CreateRSyncOptionsDataAccess()
        {
            string type = typeof(RSyncOptionsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RSyncOptionsDataAccess(CurrentContext);

            }

            return (IRSyncOptionsDataAccess)CurrentContext[type];
        }

        #endregion

        #region RSyncMonitor

        [DebuggerStepThrough]
        public override IRSyncMonitorDataAccess CreateRSyncMonitorDataAccess()
        {
            string type = typeof(RSyncMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RSyncMonitorDataAccess(CurrentContext);

            }

            return (IRSyncMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region DatabaseSubstituteAuthentication

        [DebuggerStepThrough]
        public override IDatabaseAuthenticateDataAccess CreateDatabaseSubstituteAuthenticateDataAccess()
        {
            string type = typeof(SubstituteAuthentication).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseSubstituteAuthenticateDataAccess(CurrentContext);
            }

            return (DatabaseSubstituteAuthenticateDataAccess)CurrentContext[type];
        }

        #endregion

        #region DatabaseSqlPlus

        [DebuggerStepThrough]
        public override IDatabaseSqlplusDataAccess CreateDatabaseSqlplusDataAccess()
        {
            string type = typeof(DatabaseSqlPlusDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseSqlPlusDataAccess(CurrentContext);
            }

            return (IDatabaseSqlplusDataAccess)CurrentContext[type];
        }

        #endregion DatabaseSqlPlus

        #region DatabaseRole

        [DebuggerStepThrough]
        public override IDatabaseRoleDataAccess CreateDatabasRoleDataAccess()
        {
            string type = typeof(DatabaseRoleDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseRoleDataAccess(CurrentContext);
            }

            return (IDatabaseRoleDataAccess)CurrentContext[type];
        }

        #endregion DatabaseRole

        #region NodeSubstituteAuthentication

        [DebuggerStepThrough]
        public override INodeAuthenticateDataAccess CreateNodeSubstituteAuthenticateDataAccess()
        {
            string type = typeof(SubstituteNodeAuthentication).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NodeSubstituteAuthenticateDataAccess(CurrentContext);
            }

            return (NodeSubstituteAuthenticateDataAccess)CurrentContext[type];
        }

        #endregion

        #region ServiceDRProtection

        [DebuggerStepThrough]
        public override IServiceDRProtectionDataAccess CreateServiceDRProtectionDataAccess()
        {
            string type = typeof(ServiceDRProtectionDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ServiceDRProtectionDataAccess(CurrentContext);
            }
            return (IServiceDRProtectionDataAccess)CurrentContext[type];
        }

        #endregion ServiceDRProtection


        #region ServiceDiagram

        [DebuggerStepThrough]
        public override IServiceDiagramDataAccess CreateServiceDiagramDataAccess()
        {
            string type = typeof(ServiceDiagramDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ServiceDiagramDataAccess(CurrentContext);

            }

            return (IServiceDiagramDataAccess)CurrentContext[type];
        }

        #endregion

        #region DatabaseCloudantNoSQL

        [DebuggerStepThrough]
        public override IDatabaseCloudantNoSQLDataAccess CreateDatabaseCloudantNoSQLDataAccess()
        {
            string type = typeof(CP.DataAccess.DataBaseCloudantNoSQL.DatabaseCloudantNoSQlDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CP.DataAccess.DataBaseCloudantNoSQL.DatabaseCloudantNoSQlDataAccess(CurrentContext);
            }
            return (IDatabaseCloudantNoSQLDataAccess)CurrentContext[type];
        }

        #endregion DatabaseCloudantNoSQL

        #region CloudantDbReplication

        [DebuggerStepThrough]
        public override ICloudantDBReplicationDataAccess CreateCloudantDBReplicationBaseDataAccess()
        {
            string type = typeof(CloudantDBReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CloudantDBReplicationDataAccess(CurrentContext);
            }

            return (ICloudantDBReplicationDataAccess)CurrentContext[type];
        }

        #endregion CloudantDbReplication



        #region CloudantDBReplicationJob

        [DebuggerStepThrough]
        public override ICloudantDBReplicationJobDataAccess CreateCloudantDBReplicationJobDataAccess()
        {
            string type = typeof(CloudantDBReplicationJobDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CloudantDBReplicationJobDataAccess(CurrentContext);
            }

            return (ICloudantDBReplicationJobDataAccess)CurrentContext[type];
        }

        #endregion CloudantDBReplicationJob


        #region CloudantDBMonitor

        [DebuggerStepThrough]
        public override ICloudantDBMonitorDataAccess CreateCloudantDBMonitorDataAccess()
        {
            string type = typeof(CloudantDBMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CloudantDBMonitorDataAccess(CurrentContext);
            }

            return (ICloudantDBMonitorDataAccess)CurrentContext[type];
        }

        [DebuggerStepThrough]
        public override ICloudant_Repli_MonitorDataAccess CreateCloudantREpliMonitorDataAccess()
        {
            string type = typeof(CloudantRepliMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CloudantRepliMonitorDataAccess(CurrentContext);
            }

            return (ICloudant_Repli_MonitorDataAccess)CurrentContext[type];
        }


        [DebuggerStepThrough]
        public override ICloudantLBMonitorDataAccess CreateCloudantLBMonitorDataAccess()
        {
            string type = typeof(CloudantLBMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CloudantLBMonitorDataAccess(CurrentContext);
            }

            return (ICloudantLBMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region LogViewer

        [DebuggerStepThrough]
        public override ILogViewerDataAccess CreateLogViewerDataAccess()
        {
            string type = typeof(LogViewerDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new LogViewerDataAccess(CurrentContext);

            }
            return (ILogViewerDataAccess)CurrentContext[type];
        }

        public override ILogFileDetailsDataAccess CreateLogFileDetailsDataAccess()
        {
            string type = typeof(LogFileDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new LogFileDetailsDataAccess(CurrentContext);

            }
            return (ILogFileDetailsDataAccess)CurrentContext[type];
        }

        #endregion LogViewer

        #region CPNodeMaster

        public override ICPNodeMasterDataCcess CreateCPNodeMasterDataAccess()
        {
            string type = typeof(CPNodeMasterDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CPNodeMasterDataAccess(CurrentContext);
            }

            return (CPNodeMasterDataAccess)CurrentContext[type];
        }
        #endregion CPNodeMaster

        #region EMCSRDF

        [DebuggerStepThrough]
        public override IHuaweiStorageDataAccess CreateHuaweiStorageDataAccess()
        {
            string type = typeof(HuaweiStorageDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HuaweiStorageDataAccess(CurrentContext);
            }

            return (IHuaweiStorageDataAccess)CurrentContext[type];
        }

        #endregion EMCSRDF

        #region Huwaistoragemonitor

        [DebuggerStepThrough]
        public override IHuwaiStorageMonitorStatslogsDataAccess CreateHuwaiStorageMonitorStatslogsDataAccess()
        {
            string type = typeof(HuwaiStorageMonitorStatslogsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HuwaiStorageMonitorStatslogsDataAccess(CurrentContext);
            }

            return (IHuwaiStorageMonitorStatslogsDataAccess)CurrentContext[type];
        }

        #endregion Huwaistoragemonitor

        #region Nutanix Leap

        [DebuggerStepThrough]
        public override INutanixLeapRepliDataAccess CreateLeapNutanixRepliDataAccess()
        {
            string type = typeof(NutanixLeapReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NutanixLeapReplicationDataAccess(CurrentContext);

            }

            return (NutanixLeapReplicationDataAccess)CurrentContext[type];
        }

        #endregion

        #region NutanixLeapRepli

        [DebuggerStepThrough]
        public override INTNXLeapRPReplMonitorDataAccess CreateNTNXLeapRepliMoniotorDataAccess()
        {
            string type = typeof(NutLeapRPReplMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NutLeapRPReplMonitorDataAccess(CurrentContext);

            }

            return (NutLeapRPReplMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region NutanixLeapRcbEnt

        [DebuggerStepThrough]
        public override INTNXLeapRcblEntReplMonitorDataAccess CreateNTNXLeapRcblEntMoniotorDataAccess()
        {
            string type = typeof(NutLeapRcblEntReplMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NutLeapRcblEntReplMonitorDataAccess(CurrentContext);

            }

            return (NutLeapRcblEntReplMonitorDataAccess)CurrentContext[type];
        }

        #endregion


        #region ActiveDirectory

        [DebuggerStepThrough]
        public override IActiveDirectoryDataAccess CreateActiveDirectoryDataAccess()
        {
            string type = typeof(ActiveDirectoryDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ActiveDirectoryDataAccess(CurrentContext);

            }

            return (IActiveDirectoryDataAccess)CurrentContext[type];
        }

        #endregion
        #region ActiveDirectoryMonitor

        [DebuggerStepThrough]
        public override IActiveDirectoryMonitorDataAccess CreateActiveDirectoryMonitorDataAccess()
        {
            string type = typeof(ActiveDirectoryMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ActiveDirectoryMonitorDataAccess(CurrentContext);

            }

            return (IActiveDirectoryMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region DatabaseOceanstorMssql
        [DebuggerStepThrough]
        public override IDatabaseOceanMssqlDataAccess CreateDatabaseOceanMSSqlDataAccess()
        {
            string type = typeof(DatabaseOceanstorMssqlDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseOceanstorMssqlDataAccess(CurrentContext);
            }

            return (IDatabaseOceanMssqlDataAccess)CurrentContext[type];
        }

        #endregion

        #region MSSQLMonitor

        [DebuggerStepThrough]
        public override IDatabaseMSSQLMonDataAccess CreateMSSqlMonitorDataAccess()
        {
            string type = typeof(DataBaseMSSqlMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DataBaseMSSqlMonitorDataAccess(CurrentContext);
            }

            return (IDatabaseMSSQLMonDataAccess)CurrentContext[type];
        }
        #endregion MSSQLMonitor

        #region NutanixClusterDetailsMonitoring
        [DebuggerStepThrough]
        public override INutanixClusterDetailDataAccess CreateNutanixClusterDetailsDataAccess()
        {
            string type = typeof(NutanixClusterDetailDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NutanixClusterDetailDataAccess(CurrentContext);
            }

            return (INutanixClusterDetailDataAccess)CurrentContext[type];
        }
        #endregion NutanixClusterDetailsMonitoring

        #region NutanixProtectionDomainMonitoring
        [DebuggerStepThrough]
        public override INutanixProtectionDomainDataAccess CreateNutanixProtectionDomainDataAccess()
        {
            string type = typeof(NutanixProtectionDomainDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NutanixProtectionDomainDataAccess(CurrentContext);
            }

            return (INutanixProtectionDomainDataAccess)CurrentContext[type];
        }
        #endregion NutanixProtectionDomainMonitoring

        #region NutanixProtectionDomainRepliMonitoring
        [DebuggerStepThrough]
        public override INutanixProtectionDomainRepliDataAccess CreateNutanixProtectionDomainRepliDataAccess()
        {
            string type = typeof(NutanixProtectionDomainRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NutanixProtectionDomainRepliDataAccess(CurrentContext);
            }

            return (INutanixProtectionDomainRepliDataAccess)CurrentContext[type];
        }
        #endregion NutanixProtectionDomainRepliMonitoring

        #region Nutanix

        [DebuggerStepThrough]
        public override INutanixRepliDataAccess CreateNutanixRepliDataAccess()
        {
            string type = typeof(NutanixRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new NutanixRepliDataAccess(CurrentContext);

            }

            return (NutanixRepliDataAccess)CurrentContext[type];
        }

        #endregion

        #region MariaDB

        [DebuggerStepThrough]
        public override IDatabaseMariaDataAccess CreateDatabaseMariaDataAccess()
        {
            string type = typeof(IDatabaseMariaDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseMariaDataAccess(CurrentContext);
            }

            return (IDatabaseMariaDataAccess)CurrentContext[type];
        }

        #endregion
        #region MariaDB

        [DebuggerStepThrough]
        public override IMariaDBMonitorDataAccess CreateMariaDBMonitorDataAccess()
        {
            string type = typeof(IMariaDBMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new MariaDBMonitorDataAccess(CurrentContext);
            }

            return (IMariaDBMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region AzureMonitor

        [DebuggerStepThrough]
        public override IAzure_MonitoringDataAccess CreateAzureMonitorDataAccess()
        {
            string type = typeof(AzureMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureMonitorDataAccess(CurrentContext);
            }

            return (IAzure_MonitoringDataAccess)CurrentContext[type];
        }

        #endregion AzureMonitor
        #region AzureRecSite

        [DebuggerStepThrough]
        public override IAzureRecSiteDataAccess CreateAzureRecSiteDataAccess()
        {
            string type = typeof(AzureRecSiteDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureRecSiteDataAccess(CurrentContext);
            }

            return (IAzureRecSiteDataAccess)CurrentContext[type];
        }

        #endregion AzureRecSite

        #region Zertomonitor

        [DebuggerStepThrough]
        public override IZerto_MonitoringDataAccess CreateZertoMonitorDataAccess()
        {
            string type = typeof(ZertoMonitorDataaccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ZertoMonitorDataaccess(CurrentContext);
            }

            return (IZerto_MonitoringDataAccess)CurrentContext[type];
        }

        #endregion Zertomonitor

        #region ZetroRecSite

        [DebuggerStepThrough]
        public override IZertoRecSiteDataAccess CreateZertoRecSiteDataAccess()
        {
            string type = typeof(ZertoSiteDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new ZertoSiteDataAccess(CurrentContext);
            }

            return (IZertoRecSiteDataAccess)CurrentContext[type];
        }

        #endregion ZetroRecSite

        #region EMCSRDFCG

        [DebuggerStepThrough]
        public override IEMCSRDFCGDataAccess CreateEMCSRDFCGDataAccess()
        {
            string type = typeof(EMCSRDFCGDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCSRDFCGDataAccess(CurrentContext);
            }

            return (IEMCSRDFCGDataAccess)CurrentContext[type];
        }

        #endregion

        #region EMCSRDFCGMonitor

        [DebuggerStepThrough]
        public override IEMCSRDFCGMonitorDataAccess CreateEMCSRDFCGMonitorDataAccess()
        {
            string type = typeof(EMCSRDFCGMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCSRDFCGMonitorDataAccess(CurrentContext);
            }

            return (IEMCSRDFCGMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region PostgresSQLMonitor

        [DebuggerStepThrough]
        public override IPostgresSqlClusterMonitorDataAccess CreatePostgreSQLComponentMonitorDataAccess()
        {
            string type = typeof(PGSqlClusterMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new PGSqlClusterMonitorDataAccess(CurrentContext);
            }

            return (IPostgresSqlClusterMonitorDataAccess)CurrentContext[type];
        }
        #endregion PostgresSQLMonitor

        #region VmwareVsphere

        [DebuggerStepThrough]
        public override IVmwareVsphereRepliDataAccess CreateVmwareVsphereDataAccess()
        {
            string type = typeof(VmwareVsphereRepli).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VmwareVsphereRepliDataAccess(CurrentContext);
            }

            return (VmwareVsphereRepliDataAccess)CurrentContext[type];
        }

        #endregion VmwareVsphere

        #region VmwareVsphereMonitor
        [DebuggerStepThrough]
        public override IVmwareVsphereMonitorDataAccess CreateVmwareVsphereMonitorDataAccess()
        {
            string type = typeof(VmwareVsphereMonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VmwareVsphereMonitorDataAccess(CurrentContext);
            }

            return (VmwareVsphereMonitorDataAccess)CurrentContext[type];
        }
        #endregion VmwareVsphereMonitor


        #region CPLoadMaster

        public override ICPLoadMasterDataAccess CreateCPLoadMasterDataAccess()
        {
            string type = typeof(CPLoadMasterDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CPLoadMasterDataAccess(CurrentContext);
            }

            return (CPLoadMasterDataAccess)CurrentContext[type];
        }
        #endregion CPLoadMaster

        #region AzureGateway
        public override IAzureGateWayViewDataAccess CreateAzureGatewayViewDataAccess()
        {
            string type = typeof(AzureGatewayViewDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureGatewayViewDataAccess(CurrentContext);
            }

            return (IAzureGateWayViewDataAccess)CurrentContext[type];
        }

        public override IAzureGatewayListenerViewDataAccess CreateAzureGatewayListenerViewDataAccess()
        {
            string type = typeof(AzureGatewayListenerViewDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureGatewayListenerViewDataAccess(CurrentContext);
            }

            return (IAzureGatewayListenerViewDataAccess)CurrentContext[type];
        }

        public override IAzureGatewayHealthViewDataAccess CreateAzureGatewayHealthViewDataAccess()
        {
            string type = typeof(AzureGatewayHealthViewDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureGatewayHealthViewDataAccess(CurrentContext);
            }

            return (IAzureGatewayHealthViewDataAccess)CurrentContext[type];
        }

        public override IAzureGatewayHealthViewDataAccess AzureGateWay_GETBYDATE()
        {
            string type = typeof(AzureGatewayHealthViewDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureGatewayHealthViewDataAccess(CurrentContext);
            }

            return (IAzureGatewayHealthViewDataAccess)CurrentContext[type];
        }

        #endregion AzureGateway

        #region AzureGatewayReplication

        [DebuggerStepThrough()]
        public override IAzureGatewayReplicationDataAccess CreateAzureGatewayReplicationDataAccess()
        {
            string type = typeof(AzureGatewayReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AzureGatewayReplicationDataAccess(CurrentContext);
            }

            return (IAzureGatewayReplicationDataAccess)CurrentContext[type];
        }

        #endregion

        #region OracleCloudReplication

        [DebuggerStepThrough]
        public override IOracleCloudReplicationDataAcess CreateOracleCloudReplicationDataAccess()
        {
            string type = typeof(OracleCloudReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OracleCloudReplicationDataAccess(CurrentContext);
            }

            return (IOracleCloudReplicationDataAcess)CurrentContext[type];
        }

        #endregion OracleCloudReplication

        #region RegionDetails

        [DebuggerStepThrough]
        public override IRegionDetailsDataAccess CreateRegionDetailsDataAccess()
        {
            string type = typeof(HomeRegionDetailDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new HomeRegionDetailDataAccess(CurrentContext);
            }

            return (IRegionDetailsDataAccess)CurrentContext[type];
        }

        #endregion OracleCloudReplication


        [DebuggerStepThrough]
        public override IOracleCloudComponentInfoDataAccess CreateOracleCloudComponentInfoDataAccess()
        {
            string type = typeof(OracleCloudComponentInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OracleCloudComponentInfoDataAccess(CurrentContext);
            }

            return (IOracleCloudComponentInfoDataAccess)CurrentContext[type];
        }


        [DebuggerStepThrough]
        public override IOracleCloudComponentInstanceInfoDataAccess CreateOracleCloudComponentInstanceInfoDataAccess()
        {
            string type = typeof(OracleCloudComponentInstanceInfoDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OracleCloudComponentInstanceInfoDataAccess(CurrentContext);
            }

            return (IOracleCloudComponentInstanceInfoDataAccess)CurrentContext[type];
        }


        [DebuggerStepThrough]
        public override IOracleCloudInstanceLevelMonitoringDataAccess CreateOracleCloudInstanceLevelMonitoringDataAccess()
        {
            string type = typeof(OracleCloudInstanceLevelMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OracleCloudInstanceLevelMonitorDataAccess(CurrentContext);
            }

            return (IOracleCloudInstanceLevelMonitoringDataAccess)CurrentContext[type];
        }

        #region RP4VM
        [DebuggerStepThrough]
        public override Irpforvm_replicationDataAccess Createrpforvm_replicationDataAccess()
        {
            string type = typeof(rpforvm_replication).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new rpforvm_replicationDataAccess(CurrentContext);
            }

            return (rpforvm_replicationDataAccess)CurrentContext[type];
        }
        #endregion RP4VM

        #region RP4VMMonitor

        public override IRPForVMMonitorDataAccess CreateRPForVMMonitorDataAccess()
        {
            string type = typeof(RPForVMMonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RPForVMMonitorDataAccess(CurrentContext);
            }

            return (RPForVMMonitorDataAccess)CurrentContext[type];
        }

        #endregion RP4VMMonitor

        #region EMC MTree

        public override IEMCDataDomainMTreeDataAccess CreateEMCDataDomainMTreeDataAccess()
        {
            string type = typeof(EMCDataDomainMTreeDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCDataDomainMTreeDataAccess(CurrentContext);
            }

            return (IEMCDataDomainMTreeDataAccess)CurrentContext[type];
        }

        #endregion EMC MTree

        #region EMC Monitor

        public override IEMCMTree_MonitorDataAccess CreateEMCMTree_MonitorDataAccess()
        {
            string type = typeof(EMCMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCMonitorDataAccess(CurrentContext);
            }

            return (IEMCMTree_MonitorDataAccess)CurrentContext[type];
        }

        #endregion EMC Monitor





        public override IAvamarBackupDataAccess CreateAvamarBackupDataAccess()
        {
            string type = typeof(AvamarBackupDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AvamarBackupDataAccess(CurrentContext);
            }

            return (IAvamarBackupDataAccess)CurrentContext[type];
        }

        public override IAvamarRestoreDataAccess CreateAvamarRestoreDataAccess()
        {
            string type = typeof(AvamarRestoreDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AvamarRestoreDataAccess(CurrentContext);
            }

            return (IAvamarRestoreDataAccess)CurrentContext[type];
        }

        public override IAvamarReplicationMonitor CreateAvamarReplicationMonitorDataAccess()
        {
            string type = typeof(AvamarReplicationMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AvamarReplicationMonitorDataAccess(CurrentContext);
            }

            return (IAvamarReplicationMonitor)CurrentContext[type];
        }

        public override IAvamarActivity CreateAvamarActivityDataAccess()
        {
            string type = typeof(AvamarActivityDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AvamarActivityDataAccess(CurrentContext);
            }

            return (IAvamarActivity)CurrentContext[type];
        }

        public override IAvamarDataLagDataAccess CreateAvamarDataLagDataAccess()
        {
            string type = typeof(AvamarDataLagDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new AvamarDataLagDataAccess(CurrentContext);
            }

            return (IAvamarDataLagDataAccess)CurrentContext[type];
        }


        #region RackwareReplication

        [DebuggerStepThrough]
        public override IRackwareRepliDataAccess CreateRackwareDataAccess()
        {
            string type = typeof(RackwareReplication).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RackwareRepliDataAccess(CurrentContext);
            }

            return (RackwareRepliDataAccess)CurrentContext[type];
        }

        #endregion RackwareReplication


        #region RackwareMonitorReplication

        [DebuggerStepThrough]
        public override IRackwareDataAccess CreateRackwareMonitorDataAccess()
        {
            string type = typeof(RackwareMonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RackwareMonitorDataAccess(CurrentContext);
            }

            return (RackwareMonitorDataAccess)CurrentContext[type];
        }

        #endregion RackwareReplication

        #region RackwareSourceMonitorReplication

        [DebuggerStepThrough]
        public override ISourceRackwareDataAccess CreateRackwareSourceMonitorDataAccess()
        {
            string type = typeof(RackwareSourceMonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RackwareSourceMonitorDataAccess(CurrentContext);
            }

            return (RackwareSourceMonitorDataAccess)CurrentContext[type];
        }

        #endregion RackwareSourceReplication

        #region CyberRecoverVaultReplication

        [DebuggerStepThrough]
        public override ICyberRecoveryVault CreateCyberRecoveryVaultDataAccess()
        {
            string type = typeof(CyberRecoveryVault).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CyberRecoverVaultDataAccess(CurrentContext);
            }

            return (CyberRecoverVaultDataAccess)CurrentContext[type];
        }

        #endregion CyberRecoverVaultReplication


        #region CyberRecoverVaultMonitor

        [DebuggerStepThrough]
        public override ICyberRecoveryVaultMonitor CreateCyberRecoveryVaultMonitorDataAccess()
        {
            string type = typeof(ICyberRecoveryVaultMonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CyberrecoveryVaultMonitorDataAccess(CurrentContext);
            }

            return (CyberrecoveryVaultMonitorDataAccess)CurrentContext[type];
        }

        #endregion CyberRecoverVaultMonitor

        #region EmcProtectionPolicy

        [DebuggerStepThrough]
        public override IEMCProtectionPolicyDataAccess CreateEMCProtectionpolicyDataAccess()
        {
            string type = typeof(EMCProtectionPolicyDataAccess).ToString();
            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new EMCProtectionPolicyDataAccess(CurrentContext);
            }
            return (IEMCProtectionPolicyDataAccess)CurrentContext[type];
        }

        #endregion EmcProtectionPolicy


        #region DiffModule

        public override IDiffModule CreateDiffModuleDataAccess()
        {
            string type = typeof(IDiffModule).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DiffModuleITOPSDataAccess(CurrentContext);
            }

            return (DiffModuleITOPSDataAccess)CurrentContext[type];
        }
        #endregion DiffModule

        #region DiffModuleMonitoring
        public override IDiffModuleMonitorDataAccess CreateDiffModuleMonitorDataAccess()
        {
            string type = typeof(DiffModuleMonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DiffModuleMonitorDataAccess(CurrentContext);
            }

            return (DiffModuleMonitorDataAccess)CurrentContext[type];
        }
        #endregion

        #region PPDMReport

        [DebuggerStepThrough()]
        public override IPPDMreport CreatePPDMMonitortMonitorDataAccess()
        {
            string type = typeof(PPDMDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new PPDMDataAccess(CurrentContext);
            }

            return (IPPDMreport)CurrentContext[type];
        }

        #endregion



        #region RPVMnew
        [DebuggerStepThrough]
        public override IRPVMMonitorDataAccess CreateRPVMMonitorDataAccess()
        {
            string type = typeof(RPVMReplicationmonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RPVMMonitorDataAccess(CurrentContext);
            }

            return (RPVMMonitorDataAccess)CurrentContext[type];
        }

        public override IRPVMActivityMonitorDataAccess CreateRPVMActivityMonitorDataAccess()
        {
            string type = typeof(RPVMActivitymonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RPVMActivityMonitorDataAccess(CurrentContext);
            }

            return (RPVMActivityMonitorDataAccess)CurrentContext[type];
        }
        #endregion RPVMnew



        #region ORACLECLOUDE
        public override IDatabaseOracleCloudDbDataAccess CreateOracleCloudDataAccess()
        {
            string type = typeof(OracleCloudDb).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new DatabaseOracleCloudDbDataAccess(CurrentContext);
            }

            return (DatabaseOracleCloudDbDataAccess)CurrentContext[type];
        }



        public override IOracleCloudDataGuard_DBSystemDetailsDataAccess CreateOracleCloudDataGuard_DBSystemDetailsDataAccess()
        {
            string type = typeof(CP.DataAccess.OracleCloudDataGuardDBSystemDetails.OracleCloudDataGuard_DBSystemDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new CP.DataAccess.OracleCloudDataGuardDBSystemDetails.OracleCloudDataGuard_DBSystemDetailsDataAccess(CurrentContext);
            }

            return (CP.DataAccess.OracleCloudDataGuardDBSystemDetails.OracleCloudDataGuard_DBSystemDetailsDataAccess)CurrentContext[type];
        }

        public override IOracleCloudDataGuard_WorkRequestStatusLatestDataAccess CreateOracleCloudDataGuard_WorkRequestStatusLatestDataAccess()
        {
            string type = typeof(OracleCloudDataGuard_WorkRequestStatusLatestDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OracleCloudDataGuard_WorkRequestStatusLatestDataAccess(CurrentContext);
            }

            return (OracleCloudDataGuard_WorkRequestStatusLatestDataAccess)CurrentContext[type];
        }
        #endregion ORACLECLOUDE


        public override IRedHatVirtulizationMonitoringDetailsDataAccess CreateRedHatVirtualizationDetailsDataAccess()
        {
            string type = typeof(RedHatVirtulizationMonitoringDetailsDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RedHatVirtulizationMonitoringDetailsDataAccess(CurrentContext);
            }

            return (IRedHatVirtulizationMonitoringDetailsDataAccess)CurrentContext[type];
        }

        public override IRedHatVirtualizationRepliDataAccess CreateRedHatVirtualizationDataAccess()
        {
            string type = typeof(RedHatVirtualizationRepliDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new RedHatVirtualizationRepliDataAccess(CurrentContext);
            }
            return (IRedHatVirtualizationRepliDataAccess)CurrentContext[type];

        }

        #region openshiftrepli
        [DebuggerStepThrough]
        public override IOpenshiftReplicationDataAccess CreateOpenShiftDataAccess()
        {
            string type = typeof(OpenshiftReplicationDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OpenshiftReplicationDataAccess(CurrentContext);

            }

            return (IOpenshiftReplicationDataAccess)CurrentContext[type];
        }

        #endregion

        #region openshiftmonitring



        [DebuggerStepThrough]
        public override IOpenShiftMonitorDataAccess CreateOpenShiftMonitorDataAccess()
        {
            string type = typeof(OpenShiftMonitoringDetails).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OpenShiftMonitorDataAccess(CurrentContext);
            }

            return (OpenShiftMonitorDataAccess)CurrentContext[type];
        }

        #endregion

        #region VSphere
        [DebuggerStepThrough]
        public override IVSphereRepliDataAccess CreateVSphereRepliDataAccess()
        {
            string type = typeof(VsphereReplication).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VSphereRepliDataAccess(CurrentContext);
            }

            return (VSphereRepliDataAccess)CurrentContext[type];
        }
        #endregion VSphere

        #region VSphereMonitor
        [DebuggerStepThrough]
        public override IvSphereMonitorDataAccess CreateVSphereRepliMonitorDataAccess()
        {
            string type = typeof(VsphereReplicationMonitor).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VSphereMonitorDataAccess(CurrentContext);
            }

            return (VSphereMonitorDataAccess)CurrentContext[type];
        }

        public override IvSphereMonitorReportDataAccess CreateVSphereRepliMonitorReportDataAccess()
        {
            string type = typeof(VsphereMonitorReport).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new VSphereMonitorReportDataAccess(CurrentContext);
            }

            return (VSphereMonitorReportDataAccess)CurrentContext[type];
        }
        #endregion VSphereMonitor

        #endregion

        


        

        public override IOCExaDBMonitorDataAccess CreateOCExaDBMonitorDataAccess()
        {
            string type = typeof(OCExaDBMonitorDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OCExaDBMonitorDataAccess(CurrentContext);
            }

            return (OCExaDBMonitorDataAccess)CurrentContext[type];
        }

        public override IOCExaDBConfigDataAccess CreateOCExaDBConfigDataAccess()
        {
            string type = typeof(OCExaDBConfigDataAccess).ToString();

            if (!CurrentContext.Contains(type))
            {
                CurrentContext[type] = new OCExaDBConfigDataAccess(CurrentContext);
            }

            return (OCExaDBConfigDataAccess)CurrentContext[type];
        }

        
    }
}